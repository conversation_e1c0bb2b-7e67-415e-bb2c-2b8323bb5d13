﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.AdvTree;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Controls;
using ns18;
using ns20;
using ns26;
using ns4;
using TEx;

namespace ns7
{
	// Token: 0x02000207 RID: 519
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class Control1 : UserControl
	{
		// Token: 0x06001520 RID: 5408 RVA: 0x0000880A File Offset: 0x00006A0A
		public Control1()
		{
			this.method_31();
		}

		// Token: 0x06001521 RID: 5409 RVA: 0x0008ED80 File Offset: 0x0008CF80
		public void method_0()
		{
			Base.UI.ChartThemeChanged += this.method_1;
			BaoDianMgr.action_0 = (Action<BaoDian>)Delegate.Combine(BaoDianMgr.action_0, new Action<BaoDian>(this.method_8));
			BaoDianMgr.action_1 = (Action<Node>)Delegate.Combine(BaoDianMgr.action_1, new Action<Node>(this.method_9));
			this.panel_0.ClientSizeChanged += this.panel_0_ClientSizeChanged;
			this.panel_2.ClientSizeChanged += this.panel_2_ClientSizeChanged;
			this.textBoxX_0.TextChanged += this.textBoxX_0_TextChanged;
			this.textBoxX_0.Leave += this.textBoxX_0_Leave;
			this.textBoxX_0.Enter += this.textBoxX_0_Enter;
			this.advTree_0.HideBorder = true;
			this.advTree_0.PathSeparator = Class521.smethod_0(50747);
			this.advTree_0.MouseDown += this.advTree_0_MouseDown;
			this.advTree_0.CellEditEnding += this.advTree_0_CellEditEnding;
			this.advTree_0.AfterCellEditComplete += this.advTree_0_AfterCellEditComplete;
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(0);
			this.tabControl_0.Padding = new System.Windows.Forms.Padding(0);
			this.panel_3.Padding = new System.Windows.Forms.Padding(0);
			this.panel_3.Margin = new System.Windows.Forms.Padding(0);
			this.panel_3.BorderStyle = BorderStyle.None;
			this.pictureBox_0.Padding = new System.Windows.Forms.Padding(0);
			this.pictureBox_0.BorderStyle = BorderStyle.None;
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font(Class521.smethod_0(7183), (float)(11.25 / TApp.DpiScale));
				this.tabControl_0.Font = font;
				this.tabControl_0.SelectedTabFont = font;
				this.tabControl_1.Font = font;
				this.tabControl_1.SelectedTabFont = font;
				this.textBoxX_0.Font = font;
			}
			this.method_10();
			this.method_2();
		}

		// Token: 0x06001522 RID: 5410 RVA: 0x0000881A File Offset: 0x00006A1A
		private void method_1(object sender, EventArgs e)
		{
			this.method_2();
		}

		// Token: 0x06001523 RID: 5411 RVA: 0x0008EF98 File Offset: 0x0008D198
		private void method_2()
		{
			Base.UI.smethod_74(this.expandableSplitter_0);
			Color backColor = Base.UI.smethod_34();
			Color color = Base.UI.smethod_35();
			this.pictureBox_0.BackColor = backColor;
			this.panel_3.BackColor = backColor;
			this.advTree_0.BackColor = backColor;
			this.advTree_0.ForeColor = color;
			Color backColor2;
			Color textColor;
			Color backColor3;
			Color color2;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				color = Class181.color_9;
				backColor = Class181.color_3;
				backColor2 = Class181.color_2;
				textColor = color;
				backColor3 = Class181.color_4;
				color2 = Color.FromArgb(69, 69, 69);
				this.BackColor = Class181.color_3;
				this.panel_0.BackColor = Class181.color_3;
			}
			else
			{
				color = Class181.color_3;
				backColor = Color.White;
				backColor2 = Class181.color_9;
				textColor = color;
				backColor3 = Class181.color_12;
				Color white = Color.White;
				color2 = Color.FromArgb(255, 250, 240);
				this.BackColor = Class181.color_9;
				this.panel_0.BackColor = Class181.color_9;
			}
			this.advTree_0.ColumnsBackgroundStyle = new ElementStyle();
			this.advTree_0.ColumnsBackgroundStyle.BackColor = backColor2;
			this.advTree_0.ColumnsBackgroundStyle.BackColor2 = color2;
			this.advTree_0.ColumnsBackgroundStyle.Border = eStyleBorderType.Solid;
			this.advTree_0.ColumnsBackgroundStyle.BorderBottomColor = color2;
			this.advTree_0.ColumnsBackgroundStyle.BorderColor = color2;
			this.advTree_0.ColumnStyleNormal = new ElementStyle();
			this.advTree_0.ColumnStyleNormal.TextColor = textColor;
			this.advTree_0.ColumnStyleNormal.PaddingTop = 4;
			this.advTree_0.ColumnStyleNormal.PaddingBottom = 4;
			this.advTree_0.NodeStyleSelected = new ElementStyle();
			this.advTree_0.NodeStyleSelected.BackColor = backColor3;
			this.advTree_0.CellStyleDefault = new ElementStyle();
			this.advTree_0.CellStyleDefault.PaddingTop = 3;
			this.advTree_0.CellStyleDefault.PaddingBottom = 3;
			this.advTree_0.CellStyleDefault.TextColor = color;
			this.advTree_0.ExpandBorderColor = color2;
			this.advTree_0.GridColumnLines = false;
			this.method_3();
		}

		// Token: 0x06001524 RID: 5412 RVA: 0x0008F1C0 File Offset: 0x0008D3C0
		private void method_3()
		{
			TabColorScheme colorScheme = Base.UI.smethod_72();
			this.tabControl_0.ColorScheme = colorScheme;
			this.tabControl_1.ColorScheme = colorScheme;
		}

		// Token: 0x06001525 RID: 5413 RVA: 0x0008F1F0 File Offset: 0x0008D3F0
		private void advTree_0_CellEditEnding(object sender, CellEditEventArgs e)
		{
			Node parent = e.Cell.Parent;
			NodeCollection nodes = this.advTree_0.Nodes;
			if (parent.Parent != null)
			{
				nodes = parent.Parent.Nodes;
			}
			List<Node> list = new List<Node>();
			for (int i = 0; i < nodes.Count; i++)
			{
				Node node = nodes[i];
				if (node != parent)
				{
					list.Add(node);
				}
			}
			if (this.method_23(e.NewText, list))
			{
				MessageBox.Show(Class521.smethod_0(50752), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				e.Cancel = true;
			}
		}

		// Token: 0x06001526 RID: 5414 RVA: 0x0008F28C File Offset: 0x0008D48C
		private void advTree_0_AfterCellEditComplete(object sender, CellEditEventArgs e)
		{
			this.bool_1 = false;
			bool flag = false;
			Node parent = e.Cell.Parent;
			BaoDian baoDian = parent.Tag as BaoDian;
			if (baoDian == null)
			{
				using (IEnumerator enumerator = parent.Nodes.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						object obj = enumerator.Current;
						Node node = (Node)obj;
						BaoDian baoDian2 = node.Tag as BaoDian;
						if (baoDian2 != null)
						{
							if (baoDian2.Group != parent.FullPath)
							{
								flag = true;
								baoDian2.Group = parent.FullPath;
							}
						}
						else
						{
							foreach (object obj2 in node.Nodes)
							{
								BaoDian baoDian3 = ((Node)obj2).Tag as BaoDian;
								if (baoDian3 != null && baoDian3.Group != node.FullPath)
								{
									flag = true;
									baoDian3.Group = node.FullPath;
								}
							}
						}
					}
					goto IL_134;
				}
			}
			if (baoDian.Name != e.NewText)
			{
				flag = true;
				baoDian.Name = e.NewText;
			}
			IL_134:
			if (flag)
			{
				BaoDianMgr.smethod_8();
			}
		}

		// Token: 0x06001527 RID: 5415 RVA: 0x0008F3F4 File Offset: 0x0008D5F4
		private void panel_0_ClientSizeChanged(object sender, EventArgs e)
		{
			int num = 0;
			try
			{
				foreach (object obj in this.advTree_0.Columns)
				{
					DevComponents.AdvTree.ColumnHeader columnHeader = (DevComponents.AdvTree.ColumnHeader)obj;
					int num2 = columnHeader.MinimumWidth;
					if (columnHeader.Width.AutoSize && columnHeader.Width.AutoSizeWidth > 0)
					{
						num2 = columnHeader.Width.AutoSizeWidth;
					}
					num += num2;
				}
				int num3 = 35;
				if (this.advTree_0.VerticalScroll.Visible)
				{
					num3 = 50;
				}
				this.panel_1.Width = num + num3;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001528 RID: 5416 RVA: 0x0008F4C4 File Offset: 0x0008D6C4
		private void panel_2_ClientSizeChanged(object sender, EventArgs e)
		{
			try
			{
				Size clientSize = this.panel_2.ClientSize;
				if ((double)(clientSize.Width / clientSize.Height) > 1.5)
				{
					this.splitContainer_0.Orientation = Orientation.Vertical;
					this.splitContainer_0.SplitterDistance = Convert.ToInt32(Math.Round((double)clientSize.Width * 0.6));
				}
				else
				{
					this.splitContainer_0.Orientation = Orientation.Horizontal;
					this.splitContainer_0.SplitterDistance = Convert.ToInt32(Math.Round((double)clientSize.Height * 0.7));
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001529 RID: 5417 RVA: 0x0008F57C File Offset: 0x0008D77C
		private void advTree_0_MouseDown(object sender, MouseEventArgs e)
		{
			this.advTree_0.ContextMenuStrip = null;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			if (e.Button == MouseButtons.Right)
			{
				ToolStripMenuItem value = this.method_4(null);
				contextMenuStrip.Items.Add(value);
				if (Base.UI.smethod_37() != null)
				{
					Node node = this.advTree_0.Nodes[0];
					if (this.advTree_0.SelectedNode != node)
					{
						foreach (object obj in this.advTree_0.Nodes)
						{
							Node node2 = (Node)obj;
							if (this.advTree_0.SelectedNode == node2)
							{
								node = node2;
							}
						}
					}
					ToolStripMenuItem value2 = this.method_5(node);
					contextMenuStrip.Items.Add(value2);
				}
			}
			Base.UI.smethod_73(contextMenuStrip);
			this.advTree_0.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x0600152A RID: 5418 RVA: 0x0008F678 File Offset: 0x0008D878
		private ToolStripMenuItem method_4(Node node_1)
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = Class521.smethod_0(50805);
			toolStripMenuItem.Tag = node_1;
			toolStripMenuItem.Image = Class375.NewFolder;
			toolStripMenuItem.Click += this.method_19;
			return toolStripMenuItem;
		}

		// Token: 0x0600152B RID: 5419 RVA: 0x0008F6C4 File Offset: 0x0008D8C4
		private ToolStripMenuItem method_5(Node node_1)
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = Class521.smethod_0(50822);
			toolStripMenuItem.Tag = node_1;
			toolStripMenuItem.Image = Class375.NewBook;
			toolStripMenuItem.Click += this.method_24;
			return toolStripMenuItem;
		}

		// Token: 0x0600152C RID: 5420 RVA: 0x00008824 File Offset: 0x00006A24
		private void textBoxX_0_Enter(object sender, EventArgs e)
		{
			this.bool_2 = true;
			this.string_0 = this.textBoxX_0.Text;
		}

		// Token: 0x0600152D RID: 5421 RVA: 0x0008F710 File Offset: 0x0008D910
		private void textBoxX_0_Leave(object sender, EventArgs e)
		{
			if (this.bool_0 && !this.string_0.Equals(this.textBoxX_0.Text))
			{
				if (this.pictureBox_0.Tag == null)
				{
					return;
				}
				if (MessageBox.Show(Class521.smethod_0(50839), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					(this.pictureBox_0.Tag as BaoDian).Note = this.textBoxX_0.Text;
					BaoDianMgr.smethod_8();
				}
				this.bool_0 = false;
			}
			this.bool_2 = false;
		}

		// Token: 0x0600152E RID: 5422 RVA: 0x00008840 File Offset: 0x00006A40
		private void textBoxX_0_TextChanged(object sender, EventArgs e)
		{
			this.bool_0 = true;
		}

		// Token: 0x0600152F RID: 5423 RVA: 0x0000884B File Offset: 0x00006A4B
		public void method_6(BaoDian baoDian_0)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				MessageBox.Show(Class521.smethod_0(50872), Class521.smethod_0(7587), MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
			else
			{
				this.method_7(baoDian_0);
			}
		}

		// Token: 0x06001530 RID: 5424 RVA: 0x0008F7A0 File Offset: 0x0008D9A0
		private void method_7(BaoDian baoDian_0)
		{
			StkSymbol stkSymbol = Base.Data.UsrStkSymbols[baoDian_0.SymbolID];
			StkSymbol stkSymbol2 = Base.Data.CurrSelectedSymbol;
			if (stkSymbol2 == null && Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null)
			{
				stkSymbol2 = Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol;
			}
			DateTime symbolTime = baoDian_0.SymbolTime;
			if (stkSymbol2 != null)
			{
				if (stkSymbol2.ID == stkSymbol.ID)
				{
					if (Base.UI.smethod_122(symbolTime, Class521.smethod_0(50941)))
					{
						return;
					}
					if (!Base.UI.smethod_123())
					{
						return;
					}
					if (!Base.Data.smethod_129(symbolTime, DateTime.Now))
					{
						return;
					}
					Base.UI.smethod_176(Class521.smethod_0(51079));
					Base.Data.smethod_128(symbolTime, null);
				}
				else
				{
					Base.UI.smethod_176(Class521.smethod_0(51079));
					Base.Data.smethod_66(baoDian_0.SymbolID, baoDian_0.SymbolTime, false, false);
				}
			}
			else
			{
				Base.UI.smethod_176(Class521.smethod_0(51079));
				Base.Data.smethod_66(baoDian_0.SymbolID, baoDian_0.SymbolTime, false, false);
			}
			Base.UI.smethod_27(baoDian_0.PeriodType, baoDian_0.PeriodUnit);
			Base.UI.smethod_178();
		}

		// Token: 0x06001531 RID: 5425 RVA: 0x0008F8B8 File Offset: 0x0008DAB8
		private void method_8(BaoDian baoDian_0)
		{
			Node node = this.method_12(baoDian_0.Group);
			Node node2 = BaoDianMgr.smethod_4(baoDian_0);
			this.advTree_0.BeginUpdate();
			node.Nodes.Add(node2);
			node.Expand();
			this.method_13(node2);
			this.method_16(this.method_17());
			this.advTree_0.EndUpdate();
			this.panel_0_ClientSizeChanged(null, null);
			try
			{
				this.method_30(baoDian_0);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001532 RID: 5426 RVA: 0x0008F940 File Offset: 0x0008DB40
		private void method_9(Node node_1)
		{
			BaoDian baoDian = node_1.Tag as BaoDian;
			if (node_1.FullPath != baoDian.Group)
			{
				this.advTree_0.BeginUpdate();
				try
				{
					Node node = this.method_12(baoDian.Group);
					node_1.Parent.Nodes.Remove(node_1);
					node.Nodes.Add(node_1);
					this.method_16(true);
					this.advTree_0.EndUpdate();
					this.panel_0_ClientSizeChanged(null, null);
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
			this.method_30(baoDian);
		}

		// Token: 0x06001533 RID: 5427 RVA: 0x0008F9DC File Offset: 0x0008DBDC
		private void method_10()
		{
			this.advTree_0.Nodes.Clear();
			this.advTree_0.BeginUpdate();
			BaoDian baoDian = null;
			foreach (Node node in BaoDianMgr.smethod_0())
			{
				this.advTree_0.Nodes.Add(node);
			}
			Node node2 = this.method_11();
			if (node2 != null)
			{
				baoDian = (node2.Tag as BaoDian);
			}
			this.advTree_0.NodeDoubleClick += this.advTree_0_NodeDoubleClick;
			this.advTree_0.NodeMouseDown += this.advTree_0_NodeMouseDown;
			this.advTree_0.NodeMouseUp += this.advTree_0_NodeMouseUp;
			this.method_16(baoDian != null);
			this.advTree_0.ExpandAll();
			this.method_13(node2);
			this.advTree_0.EndUpdate();
			this.panel_0_ClientSizeChanged(null, null);
			this.method_30(baoDian);
		}

		// Token: 0x06001534 RID: 5428 RVA: 0x0008FAEC File Offset: 0x0008DCEC
		private Node method_11()
		{
			Node result;
			foreach (object obj in this.advTree_0.Nodes)
			{
				foreach (object obj2 in ((Node)obj).Nodes)
				{
					Node node = (Node)obj2;
					if (node.Tag is BaoDian)
					{
						result = node;
						goto IL_E2;
					}
					foreach (object obj3 in node.Nodes)
					{
						Node node2 = (Node)obj3;
						if (node2.Tag is BaoDian)
						{
							result = node2;
							goto IL_E2;
						}
					}
				}
			}
			return null;
			IL_E2:
			return result;
		}

		// Token: 0x06001535 RID: 5429 RVA: 0x0008FC0C File Offset: 0x0008DE0C
		private Node method_12(string string_1)
		{
			Node result;
			foreach (object obj in this.advTree_0.Nodes)
			{
				Node node = (Node)obj;
				if (!(node.Tag is BaoDian))
				{
					if (node.FullPath == string_1)
					{
						result = node;
						goto IL_C4;
					}
					foreach (object obj2 in node.Nodes)
					{
						Node node2 = (Node)obj2;
						if (!(node2.Tag is BaoDian) && node2.FullPath == string_1)
						{
							result = node2;
							goto IL_C4;
						}
					}
				}
			}
			return null;
			IL_C4:
			return result;
		}

		// Token: 0x06001536 RID: 5430 RVA: 0x0008FD00 File Offset: 0x0008DF00
		private void method_13(Node node_1)
		{
			if (node_1 != null)
			{
				BaoDian baoDian_ = node_1.Tag as BaoDian;
				Node selectedNode = this.advTree_0.SelectedNode;
				if (selectedNode != null)
				{
					if (selectedNode != node_1)
					{
						this.method_30(baoDian_);
					}
				}
				else
				{
					this.method_30(baoDian_);
				}
				this.method_14();
				node_1.Image = Class375.Book_openHS;
				this.advTree_0.SelectedNode = node_1;
			}
		}

		// Token: 0x06001537 RID: 5431 RVA: 0x0008FD60 File Offset: 0x0008DF60
		private void method_14()
		{
			foreach (object obj in this.advTree_0.Nodes)
			{
				foreach (object obj2 in ((Node)obj).Nodes)
				{
					Node node = (Node)obj2;
					if (node.Tag is BaoDian)
					{
						node.Image = Class375.Book_angleHS;
					}
					else
					{
						foreach (object obj3 in node.Nodes)
						{
							((Node)obj3).Image = Class375.Book_angleHS;
						}
					}
				}
			}
		}

		// Token: 0x06001538 RID: 5432 RVA: 0x0008FE70 File Offset: 0x0008E070
		private void advTree_0_NodeMouseUp(object sender, TreeNodeMouseEventArgs e)
		{
			BaoDian baoDian = e.Node.Tag as BaoDian;
			if (e.Button == MouseButtons.Left && baoDian == null)
			{
				this.method_15();
			}
		}

		// Token: 0x06001539 RID: 5433 RVA: 0x00008881 File Offset: 0x00006A81
		private void method_15()
		{
			this.advTree_0.BeginUpdate();
			this.method_16(this.method_17());
			this.advTree_0.EndUpdate();
			this.panel_0_ClientSizeChanged(null, null);
		}

		// Token: 0x0600153A RID: 5434 RVA: 0x0008FEA8 File Offset: 0x0008E0A8
		private void method_16(bool bool_3)
		{
			foreach (object obj in this.advTree_0.Columns)
			{
				DevComponents.AdvTree.ColumnHeader columnHeader = (DevComponents.AdvTree.ColumnHeader)obj;
				if (columnHeader.Width.AutoSize != bool_3)
				{
					columnHeader.Width.AutoSize = bool_3;
				}
			}
		}

		// Token: 0x0600153B RID: 5435 RVA: 0x0008FF1C File Offset: 0x0008E11C
		private bool method_17()
		{
			bool result = false;
			foreach (object obj in this.advTree_0.Nodes)
			{
				Node node = (Node)obj;
				foreach (object obj2 in node.Nodes)
				{
					Node node2 = (Node)obj2;
					if (!(node2.Tag is BaoDian))
					{
						if (node2.Expanded && node2.Nodes.Count > 0)
						{
							result = true;
							break;
						}
					}
					else if (node.Expanded)
					{
						result = true;
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x0600153C RID: 5436 RVA: 0x00090004 File Offset: 0x0008E204
		private void advTree_0_NodeMouseDown(object sender, TreeNodeMouseEventArgs e)
		{
			Node node = e.Node;
			Node selectedNode = this.advTree_0.SelectedNode;
			if (node.Tag != null)
			{
				this.method_13(node);
			}
			if (e.Button == MouseButtons.Right)
			{
				BaoDian baoDian = node.Tag as BaoDian;
				this.advTree_0.ContextMenuStrip = null;
				ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
				if (baoDian == null)
				{
					if (!node.FullPath.Contains(this.advTree_0.PathSeparator))
					{
						ToolStripMenuItem value = this.method_4(node);
						contextMenuStrip.Items.Add(value);
					}
					if (Base.UI.smethod_37() != null)
					{
						ToolStripMenuItem value2 = this.method_5(node);
						contextMenuStrip.Items.Add(value2);
					}
					contextMenuStrip.Items.Add(new ToolStripSeparator());
				}
				else
				{
					ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
					toolStripMenuItem.Text = Class521.smethod_0(51116);
					toolStripMenuItem.Tag = baoDian;
					toolStripMenuItem.Image = Class375.flash_16x16;
					toolStripMenuItem.Click += this.method_28;
					if (Base.UI.IsInCreateNewPageState)
					{
						toolStripMenuItem.Enabled = false;
					}
					contextMenuStrip.Items.Add(toolStripMenuItem);
					contextMenuStrip.Items.Add(new ToolStripSeparator());
					ToolStripMenuItem toolStripMenuItem2 = new ToolStripMenuItem();
					toolStripMenuItem2.Text = Class521.smethod_0(22174);
					toolStripMenuItem2.Tag = node;
					toolStripMenuItem2.Image = Class375.EditHS;
					toolStripMenuItem2.Click += this.method_25;
					contextMenuStrip.Items.Add(toolStripMenuItem2);
				}
				ToolStripMenuItem toolStripMenuItem3 = new ToolStripMenuItem();
				toolStripMenuItem3.Text = Class521.smethod_0(51125);
				toolStripMenuItem3.Tag = node;
				toolStripMenuItem3.Image = Class375.RenameHS;
				toolStripMenuItem3.Click += this.method_18;
				contextMenuStrip.Items.Add(toolStripMenuItem3);
				bool flag = false;
				if (this.advTree_0.Nodes.Count == 1 && this.advTree_0.Nodes[0] == node)
				{
					flag = true;
				}
				if (!flag)
				{
					ToolStripMenuItem toolStripMenuItem4 = new ToolStripMenuItem();
					toolStripMenuItem4.Text = Class521.smethod_0(22208);
					toolStripMenuItem4.Tag = node;
					toolStripMenuItem4.Image = Class375.DeleteHS;
					toolStripMenuItem4.Click += this.method_29;
					contextMenuStrip.Items.Add(toolStripMenuItem4);
				}
				this.advTree_0.ContextMenuStrip = contextMenuStrip;
			}
		}

		// Token: 0x0600153D RID: 5437 RVA: 0x00090254 File Offset: 0x0008E454
		private void advTree_0_NodeDoubleClick(object sender, TreeNodeMouseEventArgs e)
		{
			Node node = e.Node;
			BaoDian baoDian = node.Tag as BaoDian;
			if (baoDian != null)
			{
				this.method_13(node);
				if (!Base.UI.IsInCreateNewPageState)
				{
					this.method_6(baoDian);
				}
			}
		}

		// Token: 0x0600153E RID: 5438 RVA: 0x00090290 File Offset: 0x0008E490
		private void method_18(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			if (node != null)
			{
				node.BeginEdit();
				this.bool_1 = true;
			}
		}

		// Token: 0x0600153F RID: 5439 RVA: 0x000902C0 File Offset: 0x0008E4C0
		private void method_19(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			this.advTree_0.BeginUpdate();
			Node node2;
			if (node == null)
			{
				node2 = this.method_20(null, null);
				this.advTree_0.Nodes.Add(node2);
			}
			else
			{
				node2 = this.method_20(node.Nodes, null);
				node.Nodes.Add(node2);
			}
			this.advTree_0.EndUpdate();
			this.advTree_0.SelectedNode = node2;
			node2.BeginEdit();
		}

		// Token: 0x06001540 RID: 5440 RVA: 0x00090344 File Offset: 0x0008E544
		public Node method_20(NodeCollection nodeCollection_0 = null, string string_1 = null)
		{
			if (string.IsNullOrEmpty(string_1))
			{
				string_1 = this.method_21(nodeCollection_0);
			}
			return BaoDianMgr.smethod_2(string_1);
		}

		// Token: 0x06001541 RID: 5441 RVA: 0x0009036C File Offset: 0x0008E56C
		private string method_21(NodeCollection nodeCollection_0 = null)
		{
			if (nodeCollection_0 == null)
			{
				nodeCollection_0 = this.advTree_0.Nodes;
			}
			string text = Class521.smethod_0(50805);
			if (this.method_22(text, nodeCollection_0))
			{
				for (int i = 1; i < 2147483647; i++)
				{
					string text2 = text + i.ToString();
					if (!this.method_22(text2, nodeCollection_0))
					{
						return text2;
					}
				}
			}
			return text;
		}

		// Token: 0x06001542 RID: 5442 RVA: 0x000903D4 File Offset: 0x0008E5D4
		private bool method_22(string string_1, NodeCollection nodeCollection_0)
		{
			bool result;
			foreach (object obj in nodeCollection_0)
			{
				Node node = (Node)obj;
				if (!node.IsEditing && node.Text == string_1)
				{
					result = true;
					goto IL_50;
				}
			}
			return false;
			IL_50:
			return result;
		}

		// Token: 0x06001543 RID: 5443 RVA: 0x00090448 File Offset: 0x0008E648
		private bool method_23(string string_1, IEnumerable<Node> ienumerable_0)
		{
			bool result;
			foreach (Node node in ienumerable_0)
			{
				if (!node.IsEditing && node.Text == string_1)
				{
					result = true;
					goto IL_43;
				}
			}
			return false;
			IL_43:
			return result;
		}

		// Token: 0x06001544 RID: 5444 RVA: 0x000904B0 File Offset: 0x0008E6B0
		private void method_24(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			ChtCtrl chtCtrl = Base.UI.smethod_37();
			if (chtCtrl != null)
			{
				BaoDianMgr.smethod_6(chtCtrl, node.FullPath);
			}
		}

		// Token: 0x06001545 RID: 5445 RVA: 0x000904E8 File Offset: 0x0008E6E8
		private void method_25(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			if (node != null && node.Tag is BaoDian)
			{
				BaoDianMgr.smethod_7(node);
			}
		}

		// Token: 0x06001546 RID: 5446 RVA: 0x00090520 File Offset: 0x0008E720
		public List<string> method_26()
		{
			List<string> list = new List<string>();
			foreach (object obj in this.advTree_0.Nodes)
			{
				Node node = (Node)obj;
				if (node.Tag == null)
				{
					list.Add(node.FullPath);
				}
				foreach (object obj2 in node.Nodes)
				{
					Node node2 = (Node)obj2;
					if (node2.Tag == null)
					{
						list.Add(node2.FullPath);
					}
				}
			}
			return list;
		}

		// Token: 0x06001547 RID: 5447 RVA: 0x000905F8 File Offset: 0x0008E7F8
		public AdvTree method_27()
		{
			return this.advTree_0;
		}

		// Token: 0x06001548 RID: 5448 RVA: 0x00090610 File Offset: 0x0008E810
		private void method_28(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				BaoDian baoDian = toolStripMenuItem.Tag as BaoDian;
				if (baoDian != null)
				{
					this.method_6(baoDian);
				}
			}
		}

		// Token: 0x06001549 RID: 5449 RVA: 0x00090644 File Offset: 0x0008E844
		private void method_29(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			BaoDian baoDian = node.Tag as BaoDian;
			string str = (baoDian == null) ? Class521.smethod_0(51143) : Class521.smethod_0(51138);
			string text = Class521.smethod_0(51148) + str + Class521.smethod_0(7721);
			if (baoDian == null && node.Nodes.Count > 0)
			{
				text += Class521.smethod_0(51181);
			}
			if (MessageBox.Show(text, Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				node.Tag = null;
				node.Remove();
				BaoDianMgr.smethod_8();
				Node node2 = this.method_11();
				if (node2 != null)
				{
					this.method_13(node2);
				}
				else
				{
					this.method_15();
					this.method_30(null);
				}
			}
		}

		// Token: 0x0600154A RID: 5450 RVA: 0x00090710 File Offset: 0x0008E910
		private void method_30(BaoDian baoDian_0)
		{
			if (baoDian_0 == null)
			{
				if (this.pictureBox_0 != null && this.pictureBox_0.Image != null)
				{
					this.pictureBox_0.Image = null;
				}
				this.textBoxX_0.WatermarkText = Class521.smethod_0(51246);
			}
			else
			{
				try
				{
					this.pictureBox_0.Image = baoDian_0.ScreenShot;
					this.textBoxX_0.Text = baoDian_0.Note;
					if (string.IsNullOrEmpty(baoDian_0.Note))
					{
						this.textBoxX_0.WatermarkText = Class521.smethod_0(51392);
					}
				}
				catch (Exception exception_)
				{
					Class48.smethod_4(exception_, true, null);
				}
			}
			if (this.pictureBox_0 != null)
			{
				this.pictureBox_0.Tag = baoDian_0;
			}
			this.bool_0 = false;
		}

		// Token: 0x17000381 RID: 897
		// (get) Token: 0x0600154B RID: 5451 RVA: 0x000907D8 File Offset: 0x0008E9D8
		public bool IsInInputState
		{
			get
			{
				bool result;
				if (!this.bool_1)
				{
					result = this.bool_2;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000382 RID: 898
		// (get) Token: 0x0600154C RID: 5452 RVA: 0x000907FC File Offset: 0x0008E9FC
		public bool IsScrollableCtrlFocused
		{
			get
			{
				bool result;
				if (!this.advTree_0.Focused)
				{
					result = (this.advTree_0.MouseOverNode != null);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x0600154D RID: 5453 RVA: 0x000088AF File Offset: 0x00006AAF
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600154E RID: 5454 RVA: 0x0009082C File Offset: 0x0008EA2C
		private void method_31()
		{
			this.icontainer_0 = new Container();
			this.panel_0 = new Panel();
			this.expandableSplitter_0 = new ExpandableSplitter();
			this.panel_1 = new Panel();
			this.advTree_0 = new AdvTree();
			this.columnHeader_0 = new DevComponents.AdvTree.ColumnHeader();
			this.columnHeader_1 = new DevComponents.AdvTree.ColumnHeader();
			this.columnHeader_2 = new DevComponents.AdvTree.ColumnHeader();
			this.node_0 = new Node();
			this.nodeConnector_0 = new NodeConnector();
			this.elementStyle_0 = new ElementStyle();
			this.panel_2 = new Panel();
			this.splitContainer_0 = new SplitContainer();
			this.tabControl_0 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_0 = new TabControlPanel();
			this.panel_3 = new Panel();
			this.pictureBox_0 = new PictureBox();
			this.tabItem_0 = new TabItem(this.icontainer_0);
			this.tabControl_1 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_1 = new TabControlPanel();
			this.textBoxX_0 = new TextBoxX();
			this.tabItem_1 = new TabItem(this.icontainer_0);
			this.panel_0.SuspendLayout();
			this.panel_1.SuspendLayout();
			((ISupportInitialize)this.advTree_0).BeginInit();
			this.panel_2.SuspendLayout();
			this.splitContainer_0.Panel1.SuspendLayout();
			this.splitContainer_0.Panel2.SuspendLayout();
			this.splitContainer_0.SuspendLayout();
			((ISupportInitialize)this.tabControl_0).BeginInit();
			this.tabControl_0.SuspendLayout();
			this.tabControlPanel_0.SuspendLayout();
			this.panel_3.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			((ISupportInitialize)this.tabControl_1).BeginInit();
			this.tabControl_1.SuspendLayout();
			this.tabControlPanel_1.SuspendLayout();
			base.SuspendLayout();
			this.panel_0.Controls.Add(this.expandableSplitter_0);
			this.panel_0.Controls.Add(this.panel_2);
			this.panel_0.Controls.Add(this.panel_1);
			this.panel_0.Dock = DockStyle.Fill;
			this.panel_0.Location = new Point(0, 0);
			this.panel_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_0.Name = Class521.smethod_0(51417);
			this.panel_0.Size = new Size(1214, 462);
			this.panel_0.TabIndex = 4;
			this.expandableSplitter_0.BackColor = SystemColors.ControlLight;
			this.expandableSplitter_0.BackColor2 = Color.Empty;
			this.expandableSplitter_0.BackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.BackColorSchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.ExpandableControl = this.panel_1;
			this.expandableSplitter_0.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expandableSplitter_0.HotBackColor2 = Color.Empty;
			this.expandableSplitter_0.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expandableSplitter_0.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.Location = new Point(400, 0);
			this.expandableSplitter_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.expandableSplitter_0.Name = Class521.smethod_0(51438);
			this.expandableSplitter_0.Size = new Size(4, 462);
			this.expandableSplitter_0.Style = eSplitterStyle.Mozilla;
			this.expandableSplitter_0.TabIndex = 4;
			this.expandableSplitter_0.TabStop = false;
			this.panel_1.Controls.Add(this.advTree_0);
			this.panel_1.Dock = DockStyle.Left;
			this.panel_1.Location = new Point(0, 0);
			this.panel_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_1.Name = Class521.smethod_0(51455);
			this.panel_1.Size = new Size(400, 462);
			this.panel_1.TabIndex = 2;
			this.advTree_0.AccessibleRole = AccessibleRole.Outline;
			this.advTree_0.AllowDrop = true;
			this.advTree_0.BackColor = SystemColors.Window;
			this.advTree_0.BackgroundStyle.Class = Class521.smethod_0(51468);
			this.advTree_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.advTree_0.BackgroundStyle.Border = eStyleBorderType.None;
			this.advTree_0.Columns.Add(this.columnHeader_0);
			this.advTree_0.Columns.Add(this.columnHeader_1);
			this.advTree_0.Columns.Add(this.columnHeader_2);
			this.advTree_0.Dock = DockStyle.Fill;
			this.advTree_0.Location = new Point(0, 0);
			this.advTree_0.Name = Class521.smethod_0(51489);
			this.advTree_0.Nodes.AddRange(new Node[]
			{
				this.node_0
			});
			this.advTree_0.NodesConnector = this.nodeConnector_0;
			this.advTree_0.NodeStyle = this.elementStyle_0;
			this.advTree_0.PathSeparator = Class521.smethod_0(51510);
			this.advTree_0.Size = new Size(400, 462);
			this.advTree_0.Styles.Add(this.elementStyle_0);
			this.advTree_0.TabIndex = 0;
			this.advTree_0.Text = Class521.smethod_0(51489);
			this.columnHeader_0.MinimumWidth = 200;
			this.columnHeader_0.Name = Class521.smethod_0(51515);
			this.columnHeader_0.Text = Class521.smethod_0(1472);
			this.columnHeader_0.Width.Absolute = 200;
			this.columnHeader_1.MinimumWidth = 90;
			this.columnHeader_1.Name = Class521.smethod_0(51544);
			this.columnHeader_1.Text = Class521.smethod_0(51577);
			this.columnHeader_1.Width.Absolute = 90;
			this.columnHeader_2.MinimumWidth = 90;
			this.columnHeader_2.Name = Class521.smethod_0(51586);
			this.columnHeader_2.Text = Class521.smethod_0(51615);
			this.columnHeader_2.Width.Absolute = 90;
			this.node_0.Expanded = true;
			this.node_0.Name = Class521.smethod_0(51624);
			this.node_0.Text = Class521.smethod_0(51624);
			this.nodeConnector_0.LineColor = SystemColors.ControlText;
			this.elementStyle_0.CornerType = eCornerType.Square;
			this.elementStyle_0.Name = Class521.smethod_0(51633);
			this.elementStyle_0.TextColor = SystemColors.ControlText;
			this.panel_2.Controls.Add(this.splitContainer_0);
			this.panel_2.Dock = DockStyle.Fill;
			this.panel_2.Location = new Point(400, 0);
			this.panel_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_2.Name = Class521.smethod_0(51654);
			this.panel_2.Size = new Size(814, 462);
			this.panel_2.TabIndex = 3;
			this.splitContainer_0.Dock = DockStyle.Fill;
			this.splitContainer_0.Location = new Point(0, 0);
			this.splitContainer_0.Name = Class521.smethod_0(51699);
			this.splitContainer_0.Orientation = Orientation.Horizontal;
			this.splitContainer_0.Panel1.Controls.Add(this.tabControl_0);
			this.splitContainer_0.Panel2.Controls.Add(this.tabControl_1);
			this.splitContainer_0.Size = new Size(814, 462);
			this.splitContainer_0.SplitterDistance = 335;
			this.splitContainer_0.SplitterWidth = 1;
			this.splitContainer_0.TabIndex = 0;
			this.tabControl_0.BackColor = Color.Transparent;
			this.tabControl_0.CanReorderTabs = true;
			this.tabControl_0.Controls.Add(this.tabControlPanel_0);
			this.tabControl_0.Dock = DockStyle.Fill;
			this.tabControl_0.Location = new Point(0, 0);
			this.tabControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_0.Name = Class521.smethod_0(51740);
			this.tabControl_0.SelectedTabFont = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold);
			this.tabControl_0.SelectedTabIndex = 0;
			this.tabControl_0.Size = new Size(814, 335);
			this.tabControl_0.Style = eTabStripStyle.Flat;
			this.tabControl_0.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_0.TabIndex = 1;
			this.tabControl_0.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_0.Tabs.Add(this.tabItem_0);
			this.tabControl_0.Text = Class521.smethod_0(20184);
			this.tabControlPanel_0.Controls.Add(this.panel_3);
			this.tabControlPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_0.Location = new Point(0, 0);
			this.tabControlPanel_0.Name = Class521.smethod_0(51765);
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_0.Size = new Size(814, 307);
			this.tabControlPanel_0.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_0.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_0.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_0.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_0.Style.GradientAngle = -90;
			this.tabControlPanel_0.TabIndex = 1;
			this.tabControlPanel_0.TabItem = this.tabItem_0;
			this.panel_3.Controls.Add(this.pictureBox_0);
			this.panel_3.Dock = DockStyle.Fill;
			this.panel_3.Location = new Point(1, 1);
			this.panel_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_3.Name = Class521.smethod_0(51794);
			this.panel_3.Size = new Size(812, 305);
			this.panel_3.TabIndex = 21;
			this.pictureBox_0.BackColor = Color.Transparent;
			this.pictureBox_0.Dock = DockStyle.Fill;
			this.pictureBox_0.Location = new Point(0, 0);
			this.pictureBox_0.Margin = new System.Windows.Forms.Padding(4);
			this.pictureBox_0.Name = Class521.smethod_0(51823);
			this.pictureBox_0.Size = new Size(812, 305);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.Zoom;
			this.pictureBox_0.TabIndex = 0;
			this.pictureBox_0.TabStop = false;
			this.tabItem_0.AttachedControl = this.tabControlPanel_0;
			this.tabItem_0.Name = Class521.smethod_0(51860);
			this.tabItem_0.Text = Class521.smethod_0(51881);
			this.tabControl_1.BackColor = Color.Transparent;
			this.tabControl_1.CanReorderTabs = true;
			this.tabControl_1.Controls.Add(this.tabControlPanel_1);
			this.tabControl_1.Dock = DockStyle.Fill;
			this.tabControl_1.Location = new Point(0, 0);
			this.tabControl_1.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_1.Name = Class521.smethod_0(51890);
			this.tabControl_1.SelectedTabFont = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold);
			this.tabControl_1.SelectedTabIndex = 0;
			this.tabControl_1.Size = new Size(814, 126);
			this.tabControl_1.Style = eTabStripStyle.Flat;
			this.tabControl_1.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_1.TabIndex = 2;
			this.tabControl_1.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_1.Tabs.Add(this.tabItem_1);
			this.tabControl_1.Text = Class521.smethod_0(20184);
			this.tabControlPanel_1.Controls.Add(this.textBoxX_0);
			this.tabControlPanel_1.Dock = DockStyle.Fill;
			this.tabControlPanel_1.Location = new Point(0, 0);
			this.tabControlPanel_1.Name = Class521.smethod_0(51915);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_1.Size = new Size(814, 98);
			this.tabControlPanel_1.Style.Border = eBorderType.None;
			this.tabControlPanel_1.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_1.Style.GradientAngle = -90;
			this.tabControlPanel_1.TabIndex = 1;
			this.tabControlPanel_1.TabItem = this.tabItem_1;
			this.textBoxX_0.Border.Border = eStyleBorderType.None;
			this.textBoxX_0.Dock = DockStyle.Fill;
			this.textBoxX_0.Location = new Point(1, 1);
			this.textBoxX_0.Multiline = true;
			this.textBoxX_0.Name = Class521.smethod_0(51944);
			this.textBoxX_0.Size = new Size(812, 96);
			this.textBoxX_0.TabIndex = 0;
			this.tabItem_1.AttachedControl = this.tabControlPanel_1;
			this.tabItem_1.Name = Class521.smethod_0(51973);
			this.tabItem_1.Text = Class521.smethod_0(51994);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.panel_0);
			base.Name = Class521.smethod_0(52003);
			base.Size = new Size(1214, 462);
			this.panel_0.ResumeLayout(false);
			this.panel_1.ResumeLayout(false);
			((ISupportInitialize)this.advTree_0).EndInit();
			this.panel_2.ResumeLayout(false);
			this.splitContainer_0.Panel1.ResumeLayout(false);
			this.splitContainer_0.Panel2.ResumeLayout(false);
			this.splitContainer_0.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_0).EndInit();
			this.tabControl_0.ResumeLayout(false);
			this.tabControlPanel_0.ResumeLayout(false);
			this.panel_3.ResumeLayout(false);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			((ISupportInitialize)this.tabControl_1).EndInit();
			this.tabControl_1.ResumeLayout(false);
			this.tabControlPanel_1.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000AE8 RID: 2792
		private bool bool_0;

		// Token: 0x04000AE9 RID: 2793
		private bool bool_1;

		// Token: 0x04000AEA RID: 2794
		private bool bool_2;

		// Token: 0x04000AEB RID: 2795
		private string string_0;

		// Token: 0x04000AEC RID: 2796
		private IContainer icontainer_0;

		// Token: 0x04000AED RID: 2797
		private Panel panel_0;

		// Token: 0x04000AEE RID: 2798
		private ExpandableSplitter expandableSplitter_0;

		// Token: 0x04000AEF RID: 2799
		private Panel panel_1;

		// Token: 0x04000AF0 RID: 2800
		private AdvTree advTree_0;

		// Token: 0x04000AF1 RID: 2801
		private DevComponents.AdvTree.ColumnHeader columnHeader_0;

		// Token: 0x04000AF2 RID: 2802
		private DevComponents.AdvTree.ColumnHeader columnHeader_1;

		// Token: 0x04000AF3 RID: 2803
		private DevComponents.AdvTree.ColumnHeader columnHeader_2;

		// Token: 0x04000AF4 RID: 2804
		private Node node_0;

		// Token: 0x04000AF5 RID: 2805
		private NodeConnector nodeConnector_0;

		// Token: 0x04000AF6 RID: 2806
		private ElementStyle elementStyle_0;

		// Token: 0x04000AF7 RID: 2807
		private Panel panel_2;

		// Token: 0x04000AF8 RID: 2808
		private SplitContainer splitContainer_0;

		// Token: 0x04000AF9 RID: 2809
		private Panel panel_3;

		// Token: 0x04000AFA RID: 2810
		private PictureBox pictureBox_0;

		// Token: 0x04000AFB RID: 2811
		private TextBoxX textBoxX_0;

		// Token: 0x04000AFC RID: 2812
		private DevComponents.DotNetBar.TabControl tabControl_0;

		// Token: 0x04000AFD RID: 2813
		private TabControlPanel tabControlPanel_0;

		// Token: 0x04000AFE RID: 2814
		private TabItem tabItem_0;

		// Token: 0x04000AFF RID: 2815
		private DevComponents.DotNetBar.TabControl tabControl_1;

		// Token: 0x04000B00 RID: 2816
		private TabControlPanel tabControlPanel_1;

		// Token: 0x04000B01 RID: 2817
		private TabItem tabItem_1;
	}
}
