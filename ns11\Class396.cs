﻿using System;
using System.Drawing;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns11
{
	// Token: 0x020002F8 RID: 760
	internal sealed class Class396 : ShapeCurve
	{
		// Token: 0x0600212E RID: 8494 RVA: 0x000EBAB4 File Offset: 0x000E9CB4
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			if (base.IndData.SingleData.Contains(Class521.smethod_0(98553)))
			{
				this.string_0 = (string)base.IndData.SingleData[Class521.smethod_0(98553)];
			}
			Ind_TextItem ind_TextItem = zedGraphControl_0.GraphPane.AddTextItem(base.IndData.Name, base.DataView, SymbolType.None, this.string_0);
			ind_TextItem.Symbol.Border.Color = color_0;
			ind_TextItem.Tag = string_1 + Class521.smethod_0(2712) + base.IndData.Name;
			this.curveItem_0 = ind_TextItem;
			base.method_3(string_1, ind_TextItem);
		}

		// Token: 0x0600212F RID: 8495 RVA: 0x0000D66A File Offset: 0x0000B86A
		public Class396(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002130 RID: 8496 RVA: 0x000EB5D0 File Offset: 0x000E97D0
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_0);
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(97541)));
				int_0 = dataArray_1.Data.Length - 1;
			}
			double y = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList.Count != 1)
			{
				throw new Exception(Class521.smethod_0(98360));
			}
			PointPair result;
			if ((int)dataArray_1.OtherDataArrayList[0].Data[int_0] == 1)
			{
				result = new PointPair(new XDate(dateTime), y, 1.0);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x04001031 RID: 4145
		private string string_0 = Class521.smethod_0(1449);
	}
}
