﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns1;
using ns13;
using ns18;
using ns21;
using ns22;
using ns24;
using ns26;
using ns4;
using TEx.Chart;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200018A RID: 394
	internal abstract class ChartBase : IDisposable, Interface1
	{
		// Token: 0x06000EEB RID: 3819 RVA: 0x000616C0 File Offset: 0x0005F8C0
		public ChartBase(ChtCtrl dChtCtrl, SplitterPanel panel)
		{
			ChartBase.Class227 @class = new ChartBase.Class227();
			@class.splitterPanel_0 = panel;
			base..ctor();
			@class.chartBase_0 = this;
			this.chtCtrl_0 = dChtCtrl;
			this.vmethod_0();
			if (@class.splitterPanel_0.InvokeRequired)
			{
				@class.splitterPanel_0.Invoke(new MethodInvoker(@class.method_0));
			}
			else
			{
				@class.splitterPanel_0.Controls.Clear();
				@class.splitterPanel_0.Controls.Add(this.ZedGraphControl);
			}
			this.splitterPanel_0 = @class.splitterPanel_0;
		}

		// Token: 0x06000EEC RID: 3820 RVA: 0x00061750 File Offset: 0x0005F950
		protected virtual void vmethod_0()
		{
			this.zedGraphControl_0 = new ZedGraphControl();
			this.ZedGraphControl.Visible = false;
			this.GraphPane.Y2Axis.IsVisible = false;
			this.zedGraphControl_0.Dock = DockStyle.Fill;
			this.zedGraphControl_0.BorderStyle = BorderStyle.None;
			this.zedGraphControl_0.GraphPane.Border.Color = Color.Transparent;
			this.zedGraphControl_0.GraphPane.Border.Width = 0f;
			GraphPane graphPane = this.zedGraphControl_0.GraphPane;
			graphPane.Title.IsVisible = false;
			graphPane.Legend.IsVisible = false;
			graphPane.IsFontsScaled = false;
			graphPane.XAxis.Type = AxisType.DateAsOrdinal;
			graphPane.XAxis.Title.IsVisible = false;
			graphPane.XAxis.Scale.FontSpec.Size = 11f;
			graphPane.YAxis.Title.IsVisible = false;
			graphPane.YAxis.Scale.FontSpec.Size = 11f;
			graphPane.YAxis.Scale.Align = AlignP.Inside;
			graphPane.YAxis.Scale.MagAuto = false;
			graphPane.XAxis.Scale.LabelGap = 0.15f;
			graphPane.Chart.IsRectAuto = false;
			graphPane.YAxis.Scale.IsSkipFirstLabel = true;
			graphPane.YAxis.Scale.IsSkipLastLabel = true;
			graphPane.YAxis.MinorTic.Size = 0f;
			graphPane.YAxis.MajorTic.Size = 0f;
			graphPane.YAxis.MajorGrid.IsVisible = true;
			graphPane.XAxis.MajorTic.Size = 0f;
			this.zedGraphControl_0.MouseMoveEvent += this.zedGraphControl_0_MouseMoveEvent;
			this.zedGraphControl_0.MouseLeave += this.zedGraphControl_0_MouseLeave;
			this.zedGraphControl_0.DoubleClick += this.zedGraphControl_0_DoubleClick;
			this.method_47();
			this.textObj_0 = this.method_46();
			this.zedGraphControl_0.GraphPane.GraphObjList.Add(this.textObj_0);
		}

		// Token: 0x06000EED RID: 3821 RVA: 0x00061988 File Offset: 0x0005FB88
		public CurveItem method_0(string string_9)
		{
			GraphPane graphPane = this.GraphPane;
			int num = graphPane.CurveList.IndexOfTag(string_9);
			CurveItem result;
			if (num < 0)
			{
				result = null;
			}
			else
			{
				CurveItem curveItem = graphPane.CurveList[num];
				result = curveItem;
			}
			return result;
		}

		// Token: 0x06000EEE RID: 3822 RVA: 0x000619C4 File Offset: 0x0005FBC4
		public TextObj method_1(string string_9)
		{
			GraphPane graphPane = this.GraphPane;
			int num = graphPane.GraphObjList.IndexOfTag(string_9);
			TextObj result;
			if (num < 0)
			{
				result = null;
			}
			else
			{
				TextObj textObj = graphPane.GraphObjList[num] as TextObj;
				result = textObj;
			}
			return result;
		}

		// Token: 0x06000EEF RID: 3823 RVA: 0x00061A04 File Offset: 0x0005FC04
		protected DateTime method_2(HisData hisData_0)
		{
			ChartBase.Class228 @class = new ChartBase.Class228();
			@class.hisData_0 = hisData_0;
			DateTime result;
			if (this.HisDataPeriodSet.IsPeriod1m)
			{
				result = @class.hisData_0.Date;
			}
			else
			{
				result = this.HisDataPeriodSet.PeriodHisDataList.First(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Key;
			}
			return result;
		}

		// Token: 0x06000EF0 RID: 3824 RVA: 0x00061A64 File Offset: 0x0005FC64
		public DateTime method_3(int int_0)
		{
			DateTime result;
			if (this.HisDataPeriodSet.PeriodHisDataList.Count > int_0)
			{
				result = this.HisDataPeriodSet.PeriodHisDataList.Keys[int_0];
			}
			else
			{
				result = this.HisDataPeriodSet.PeriodHisDataList.Keys.Last<DateTime>();
			}
			return result;
		}

		// Token: 0x06000EF1 RID: 3825 RVA: 0x000067BC File Offset: 0x000049BC
		public virtual void vmethod_1(int int_0)
		{
			this.vmethod_2();
			this.vmethod_3();
			this.ApplyTheme(Base.UI.Form.ChartTheme);
		}

		// Token: 0x06000EF2 RID: 3826 RVA: 0x00061AB8 File Offset: 0x0005FCB8
		public virtual void vmethod_2()
		{
			GraphPane graphPane = this.GraphPane;
			for (int i = 0; i < graphPane.CurveList.Count; i++)
			{
				graphPane.CurveList[i] = null;
			}
			if (graphPane.CurveList.Count > 0)
			{
				graphPane.CurveList.Clear();
			}
		}

		// Token: 0x06000EF3 RID: 3827 RVA: 0x00061B0C File Offset: 0x0005FD0C
		public virtual void vmethod_3()
		{
			this.ZedGraphControl.IsEnableZoom = false;
			this.GraphPane.Legend.IsVisible = false;
			GraphPane graphPane = this.GraphPane;
			graphPane.XAxis.Scale.Min = 0.0;
			graphPane.XAxis.Scale.Format = Class521.smethod_0(48020);
			if (this.IsXAxisVisible)
			{
				graphPane.XAxis.MinorTic.Size = 0f;
				graphPane.XAxis.MajorTic.Size = 1f;
			}
		}

		// Token: 0x06000EF4 RID: 3828 RVA: 0x00061BA4 File Offset: 0x0005FDA4
		public static double smethod_0(double double_0, double double_1, int int_0)
		{
			if (int_0 < 2)
			{
				throw new Exception(Class521.smethod_0(71864));
			}
			return (Scale.SafeLog(double_0) - Scale.SafeLog(double_1)) / (double)(int_0 - 1);
		}

		// Token: 0x06000EF5 RID: 3829 RVA: 0x00061BDC File Offset: 0x0005FDDC
		public virtual void ApplyTheme(ChartTheme theme)
		{
			Color color;
			if (theme == ChartTheme.Classic)
			{
				color = Class181.color_9;
				Color darkRed = Color.DarkRed;
				Color black = Color.Black;
				this.GraphPane.Chart.Fill = new Fill(black);
				this.GraphPane.Fill = new Fill(black);
				this.GraphPane.Chart.Border.Color = darkRed;
				this.GraphPane.YAxis.MajorGrid.Color = darkRed;
				this.textObj_1.FontSpec.FontColor = color;
				this.textObj_1.FontSpec.Border.Color = darkRed;
				this.textObj_1.FontSpec.Fill.Color = darkRed;
				this.textObj_2.FontSpec.FontColor = color;
				this.textObj_2.FontSpec.Border.Color = darkRed;
				this.textObj_2.FontSpec.Fill.Color = darkRed;
				if (this.RevInfoBox != null)
				{
					this.RevInfoBox.Border.Color = darkRed;
					this.RevInfoBox.Fill = new Fill(black);
					this.method_68(color);
				}
			}
			else
			{
				color = Class181.color_2;
				Color color2 = Color.FromArgb(255, 255, 236);
				if (theme == ChartTheme.Modern)
				{
					this.GraphPane.Chart.Fill = new Fill(Color.White, color2, 45f);
				}
				else if (theme == ChartTheme.Yellow)
				{
					this.GraphPane.Chart.Fill = new Fill(color2);
				}
				this.GraphPane.Chart.Border.Color = Color.DarkGray;
				this.GraphPane.YAxis.MajorGrid.Color = Color.LightGray;
				Color color_ = Class181.color_16;
				this.textObj_1.FontSpec.FontColor = Color.White;
				this.textObj_1.FontSpec.Border.Color = color_;
				this.textObj_1.FontSpec.Fill.Color = color_;
				this.textObj_2.FontSpec.FontColor = Color.White;
				this.textObj_2.FontSpec.Border.Color = color_;
				this.textObj_2.FontSpec.Fill.Color = color_;
				if (this.RevInfoBox != null)
				{
					if (theme == ChartTheme.Yellow)
					{
						this.RevInfoBox.Fill = new Fill(color2);
					}
					else
					{
						this.RevInfoBox.Fill = new Fill(Color.White, Color.FromArgb(240, 240, 255));
					}
					this.RevInfoBox.Border.Color = Color.Gray;
					this.method_68(color);
				}
			}
			this.GraphPane.YAxis.Scale.FontSpec.FontColor = color;
			this.textObj_0.FontSpec.FontColor = color;
			if (this.IsXAxisVisible)
			{
				this.GraphPane.XAxis.Scale.FontSpec.FontColor = color;
				this.GraphPane.XAxis.MajorTic.Color = color;
			}
			this.ZedGraphControl.Refresh();
		}

		// Token: 0x06000EF6 RID: 3830 RVA: 0x000067DC File Offset: 0x000049DC
		public virtual void vmethod_4(int int_0)
		{
			if (!this.ZedGraphControl.Visible)
			{
				this.ZedGraphControl.Visible = true;
			}
			this.vmethod_19();
		}

		// Token: 0x06000EF7 RID: 3831 RVA: 0x00061EFC File Offset: 0x000600FC
		protected int method_4(int int_0)
		{
			int result = 0;
			int maxSticksPerChart = this.ChtCtrl.MaxSticksPerChart;
			if (int_0 >= maxSticksPerChart)
			{
				result = int_0 - maxSticksPerChart;
			}
			return result;
		}

		// Token: 0x06000EF8 RID: 3832 RVA: 0x000067FF File Offset: 0x000049FF
		public virtual void vmethod_5(HisData hisData_0)
		{
			this.vmethod_19();
		}

		// Token: 0x06000EF9 RID: 3833 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_6(HDTick hdtick_0)
		{
		}

		// Token: 0x06000EFA RID: 3834 RVA: 0x000067FF File Offset: 0x000049FF
		public virtual void vmethod_7(int int_0, HisData hisData_0, bool bool_3)
		{
			this.vmethod_19();
		}

		// Token: 0x06000EFB RID: 3835 RVA: 0x00006809 File Offset: 0x00004A09
		public virtual void vmethod_8(int int_0, HisData hisData_0)
		{
			this.vmethod_7(int_0, hisData_0, false);
		}

		// Token: 0x06000EFC RID: 3836 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_9(int int_0, HDTick hdtick_0, bool bool_3)
		{
		}

		// Token: 0x06000EFD RID: 3837 RVA: 0x00006816 File Offset: 0x00004A16
		public virtual void vmethod_10(int int_0, HDTick hdtick_0)
		{
			this.vmethod_9(int_0, hdtick_0, false);
		}

		// Token: 0x06000EFE RID: 3838 RVA: 0x00006823 File Offset: 0x00004A23
		public virtual void vmethod_11(HisData hisData_0)
		{
			this.vmethod_18();
			this.ZedGraphControl.Refresh();
		}

		// Token: 0x06000EFF RID: 3839 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_12()
		{
		}

		// Token: 0x06000F00 RID: 3840 RVA: 0x00061F24 File Offset: 0x00060124
		public virtual void vmethod_13(string string_9)
		{
			ChartBase.Class229 @class = new ChartBase.Class229();
			@class.string_0 = Class521.smethod_0(71893) + string_9;
			if (this.MasterPane != null && !this.MasterPane.GraphObjList.Exists(new Predicate<GraphObj>(@class.method_0)))
			{
				TextObj textObj = new TextObj(string_9, 0.5, 0.5);
				textObj.Location.CoordinateFrame = CoordType.PaneFraction;
				textObj.FontSpec.FontColor = Color.FromArgb(70, 255, 100, 100);
				textObj.FontSpec.IsBold = true;
				textObj.FontSpec.Size = 100f;
				textObj.FontSpec.Border.IsVisible = false;
				textObj.FontSpec.Fill.IsVisible = false;
				textObj.Location.AlignH = AlignH.Center;
				textObj.Location.AlignV = AlignV.Center;
				textObj.ZOrder = ZOrder.B_BehindLegend;
				textObj.Tag = @class.string_0;
				this.MasterPane.GraphObjList.Add(textObj);
			}
		}

		// Token: 0x06000F01 RID: 3841 RVA: 0x00006838 File Offset: 0x00004A38
		public void method_5()
		{
			this.method_6(string.Empty);
		}

		// Token: 0x06000F02 RID: 3842 RVA: 0x00062038 File Offset: 0x00060238
		public void method_6(string string_9)
		{
			ChartBase.Class230 @class = new ChartBase.Class230();
			@class.string_0 = string_9;
			if (this.MasterPane != null)
			{
				this.MasterPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(@class.method_0));
			}
		}

		// Token: 0x06000F03 RID: 3843 RVA: 0x00006847 File Offset: 0x00004A47
		public void method_7()
		{
			SortableBindingList<ShownOpenTrans> currOpenTransList = Base.Trading.CurrOpenTransList;
		}

		// Token: 0x06000F04 RID: 3844 RVA: 0x0006207C File Offset: 0x0006027C
		private void method_8(Transaction transaction_0)
		{
			double num = Convert.ToDouble(transaction_0.Price);
			Enum17 transType = (Enum17)transaction_0.TransType;
			GraphPane graphPane = this.GraphPane;
			if (graphPane.YAxis.Scale.Min < num && graphPane.YAxis.Scale.Max > num)
			{
				Color color = Color.Silver;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					color = Color.DarkRed;
				}
				LineObj lineObj = new LineObj(color, graphPane.XAxis.Scale.Min, num, graphPane.XAxis.Scale.Max, num);
				lineObj.IsClippedToChartRect = false;
				lineObj.Line.Style = DashStyle.Solid;
				lineObj.Tag = ChartBase.string_7;
				lineObj.ZOrder = ZOrder.E_BehindCurves;
				graphPane.GraphObjList.Add(lineObj);
				AlignV alignV = AlignV.Top;
				if (num > (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) / 2.0 + graphPane.YAxis.Scale.Min)
				{
					alignV = AlignV.Bottom;
				}
				string[] array = new string[7];
				array[0] = num.ToString();
				array[1] = Class521.smethod_0(3636);
				array[2] = ((transType == Enum17.const_1) ? Class521.smethod_0(18676) : Class521.smethod_0(18671));
				array[3] = transaction_0.Units.ToString();
				array[4] = Class521.smethod_0(11739);
				int num2 = 5;
				decimal? profit = transaction_0.Profit;
				decimal d = 0m;
				array[num2] = ((profit.GetValueOrDefault() >= d & profit != null) ? Class521.smethod_0(71915) : Class521.smethod_0(71910));
				array[6] = transaction_0.Profit.ToString();
				string.Concat(array);
				TextObj textObj = new TextObj(Class521.smethod_0(1449), 0.0, num, CoordType.XChartFractionYScale, AlignH.Left, alignV);
				textObj.ZOrder = ZOrder.A_InFront;
				textObj.Tag = lineObj.Tag + ChartBase.string_8;
				this.method_9(textObj);
				this.GraphPane.GraphObjList.Add(textObj);
			}
		}

		// Token: 0x06000F05 RID: 3845 RVA: 0x000622A4 File Offset: 0x000604A4
		private void method_9(TextObj textObj_3)
		{
			textObj_3.FontSpec.FontColor = Color.Gray;
			textObj_3.FontSpec.Border.IsVisible = true;
			textObj_3.FontSpec.Border.Color = Color.Gray;
			textObj_3.FontSpec.StringAlignment = StringAlignment.Near;
			textObj_3.FontSpec.Fill.IsVisible = false;
			textObj_3.FontSpec.Size = 11f;
			textObj_3.FontSpec.IsBold = false;
			textObj_3.IsClippedToChartRect = false;
		}

		// Token: 0x06000F06 RID: 3846 RVA: 0x0006232C File Offset: 0x0006052C
		protected void method_10(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(71920);
			toolStripMenuItem.ToolTipText = Class521.smethod_0(71933);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			string str = Class521.smethod_0(3636) + Base.UI.Form.TradingUnits.ToString() + (this.Symbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734));
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(26376) + str;
			toolStripMenuItem2.Click += this.method_41;
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_0);
				if (keys != Keys.None)
				{
					toolStripMenuItem2.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(26410) + str;
			toolStripMenuItem3.Click += this.method_42;
			try
			{
				Keys keys2 = Class210.smethod_3(Enum3.const_1);
				if (keys2 != Keys.None)
				{
					toolStripMenuItem3.ShortcutKeys = keys2;
				}
			}
			catch
			{
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
			if (this.ChtCtrl.SymbDataSet.CurrSymblSupportsShort)
			{
				ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
				toolStripMenuItem4.Text = Class521.smethod_0(26444) + str;
				toolStripMenuItem4.Click += this.method_43;
				try
				{
					Keys keys3 = Class210.smethod_3(Enum3.const_2);
					if (keys3 != Keys.None)
					{
						toolStripMenuItem4.ShortcutKeys = keys3;
					}
				}
				catch
				{
				}
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem4);
				ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
				toolStripMenuItem5.Text = Class521.smethod_0(26478) + str;
				toolStripMenuItem5.Click += this.method_44;
				try
				{
					Keys keys4 = Class210.smethod_3(Enum3.const_3);
					if (keys4 != Keys.None)
					{
						toolStripMenuItem5.ShortcutKeys = keys4;
					}
				}
				catch
				{
				}
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem5);
			}
		}

		// Token: 0x06000F07 RID: 3847 RVA: 0x00062568 File Offset: 0x00060768
		protected void method_11(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(72002);
			toolStripMenuItem.Click += this.method_39;
			if (this.ChtCtrl.IsInCrossReviewMode)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000F08 RID: 3848 RVA: 0x000625CC File Offset: 0x000607CC
		protected void method_12(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(27304);
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_24);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Image = Class375.saveHS;
			toolStripMenuItem.Click += this.method_40;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000F09 RID: 3849 RVA: 0x00062648 File Offset: 0x00060848
		public void method_13(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(69248);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(64362);
			toolStripMenuItem2.Click += this.method_28;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(64349);
			toolStripMenuItem3.Click += this.method_29;
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(64375);
			toolStripMenuItem4.Click += this.method_30;
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
			if (this is Class226)
			{
				toolStripMenuItem2.Checked = true;
			}
			if (this is ChartKLine)
			{
				toolStripMenuItem3.Checked = true;
			}
			if (!Base.UI.IsInCreateNewPageState && Base.UI.IsTransTabsVisible)
			{
				toolStripMenuItem4.Enabled = false;
			}
		}

		// Token: 0x06000F0A RID: 3850 RVA: 0x00062744 File Offset: 0x00060944
		public void method_14(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(72027);
			toolStripMenuItem.DropDownOpening += this.method_21;
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000F0B RID: 3851 RVA: 0x0006279C File Offset: 0x0006099C
		public void method_15(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(72044);
			toolStripMenuItem.DropDownOpening += this.method_16;
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000F0C RID: 3852 RVA: 0x000627F4 File Offset: 0x000609F4
		private void method_16(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			toolStripMenuItem.DropDownItems.Clear();
			this.method_17(toolStripMenuItem);
		}

		// Token: 0x06000F0D RID: 3853 RVA: 0x0006281C File Offset: 0x00060A1C
		private void method_17(ToolStripMenuItem toolStripMenuItem_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_77(Class521.smethod_0(39117));
			toolStripMenuItem.Click += this.method_18;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_77(Class521.smethod_0(39134));
			toolStripMenuItem2.Click += this.method_19;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_77(Class521.smethod_0(39151));
			toolStripMenuItem3.Click += this.method_20;
			toolStripMenuItem_0.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem,
				toolStripMenuItem2,
				toolStripMenuItem3
			});
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				toolStripMenuItem.Checked = true;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				toolStripMenuItem2.Checked = true;
			}
			else
			{
				toolStripMenuItem3.Checked = true;
			}
		}

		// Token: 0x06000F0E RID: 3854 RVA: 0x00006851 File Offset: 0x00004A51
		private void method_18(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Classic);
		}

		// Token: 0x06000F0F RID: 3855 RVA: 0x0000685B File Offset: 0x00004A5B
		private void method_19(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Yellow);
		}

		// Token: 0x06000F10 RID: 3856 RVA: 0x00006865 File Offset: 0x00004A65
		private void method_20(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Modern);
		}

		// Token: 0x06000F11 RID: 3857 RVA: 0x000628E0 File Offset: 0x00060AE0
		private void method_21(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem_ = sender as ToolStripMenuItem;
			this.method_22(toolStripMenuItem_);
		}

		// Token: 0x06000F12 RID: 3858 RVA: 0x00062900 File Offset: 0x00060B00
		private void method_22(ToolStripMenuItem toolStripMenuItem_0)
		{
			toolStripMenuItem_0.DropDownItems.Clear();
			if (Base.Data.CurrExchangeList.Count > 1)
			{
				if (TApp.IsStIncluded)
				{
					List<StkSymbol> source = Base.Data.smethod_98();
					List<StkSymbol> source2 = Base.Data.smethod_100(false);
					List<StkSymbol> source3 = Base.Data.smethod_101(false);
					ExchgHouse exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(ChartBase.<>c.<>9.method_0));
					if (exchgHouse != null)
					{
						ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
						toolStripMenuItem.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable = source.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_1));
						if (enumerable.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
							toolStripMenuItem2.Text = Class521.smethod_0(23271);
							this.method_24(toolStripMenuItem2, enumerable);
							toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
						}
						IEnumerable<StkSymbol> enumerable2 = source2.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_2));
						if (enumerable2.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
							toolStripMenuItem3.Text = Class521.smethod_0(23245);
							this.method_24(toolStripMenuItem3, enumerable2);
							toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
						}
						IEnumerable<StkSymbol> enumerable3 = source3.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_3));
						if (enumerable3.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
							toolStripMenuItem4.Text = Class521.smethod_0(23215);
							this.method_24(toolStripMenuItem4, enumerable3);
							toolStripMenuItem.DropDownItems.Add(toolStripMenuItem4);
						}
						List<StkSymbol> list = Base.Data.smethod_106(false);
						if (list.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
							toolStripMenuItem5.Text = Class521.smethod_0(23093);
							this.method_24(toolStripMenuItem5, list);
							toolStripMenuItem.DropDownItems.Add(toolStripMenuItem5);
						}
						List<StkSymbol> list2 = Base.Data.smethod_107(new int?(5), false);
						if (list2.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem6 = Base.UI.smethod_76();
							toolStripMenuItem6.Text = Class521.smethod_0(23063);
							this.method_24(toolStripMenuItem6, list2);
							toolStripMenuItem.DropDownItems.Add(toolStripMenuItem6);
						}
						toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem);
					}
					exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(ChartBase.<>c.<>9.method_4));
					if (exchgHouse != null)
					{
						ToolStripMenuItem toolStripMenuItem7 = Base.UI.smethod_76();
						toolStripMenuItem7.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable4 = source.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_5));
						if (enumerable4.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem8 = Base.UI.smethod_76();
							toolStripMenuItem8.Text = Class521.smethod_0(23271);
							this.method_24(toolStripMenuItem8, enumerable4);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem8);
						}
						IEnumerable<StkSymbol> enumerable5 = source2.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_6));
						if (enumerable5.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem9 = Base.UI.smethod_76();
							toolStripMenuItem9.Text = Class521.smethod_0(23245);
							this.method_24(toolStripMenuItem9, enumerable5);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem9);
						}
						IEnumerable<StkSymbol> enumerable6 = source3.Where(new Func<StkSymbol, bool>(ChartBase.<>c.<>9.method_7));
						if (enumerable6.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem10 = Base.UI.smethod_76();
							toolStripMenuItem10.Text = Class521.smethod_0(23215);
							this.method_24(toolStripMenuItem10, enumerable6);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem10);
						}
						List<StkSymbol> list3 = Base.Data.smethod_104(false);
						if (list3.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem11 = Base.UI.smethod_76();
							toolStripMenuItem11.Text = Class521.smethod_0(23135);
							this.method_24(toolStripMenuItem11, list3);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem11);
						}
						List<StkSymbol> list4 = Base.Data.smethod_105(false);
						if (list4.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem12 = Base.UI.smethod_76();
							toolStripMenuItem12.Text = Class521.smethod_0(23177);
							this.method_24(toolStripMenuItem12, list4);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem12);
						}
						List<StkSymbol> list5 = Base.Data.smethod_107(new int?(6), false);
						if (list5.Any<StkSymbol>())
						{
							ToolStripMenuItem toolStripMenuItem13 = Base.UI.smethod_76();
							toolStripMenuItem13.Text = Class521.smethod_0(23063);
							this.method_24(toolStripMenuItem13, list5);
							toolStripMenuItem7.DropDownItems.Add(toolStripMenuItem13);
						}
						toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem7);
					}
				}
				if (TApp.IsFtIncluded)
				{
					int[] array = new int[]
					{
						1,
						2,
						4,
						3,
						0,
						-1
					};
					for (int i = 0; i < array.Length; i++)
					{
						ChartBase.Class231 @class = new ChartBase.Class231();
						@class.int_0 = array[i];
						ExchgHouse exchgHouse2 = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(@class.method_0));
						if (exchgHouse2 != null)
						{
							ToolStripMenuItem toolStripMenuItem14 = Base.UI.smethod_76();
							toolStripMenuItem14.Text = exchgHouse2.AbbrName_CN;
							IEnumerable<StkSymbol> ienumerable_ = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1));
							if (this.method_23(toolStripMenuItem14, ienumerable_))
							{
								toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem14);
							}
						}
					}
				}
			}
			else
			{
				this.method_24(toolStripMenuItem_0, Base.Data.UsrStkSymbols.Values);
			}
			if (Base.UI.TransTabs != null)
			{
				List<StkSymbol> zixuanStkSymbList = Base.UI.TransTabs.ZixuanStkSymbList;
				if (zixuanStkSymbList != null)
				{
					ToolStripMenuItem toolStripMenuItem15 = Base.UI.smethod_76();
					toolStripMenuItem15.Text = Class521.smethod_0(22944);
					this.method_24(toolStripMenuItem15, zixuanStkSymbList);
					toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem15);
				}
			}
		}

		// Token: 0x06000F13 RID: 3859 RVA: 0x00062E98 File Offset: 0x00061098
		private bool method_23(ToolStripMenuItem toolStripMenuItem_0, IEnumerable<StkSymbol> ienumerable_0)
		{
			bool result = false;
			foreach (string text in ienumerable_0.Select(new Func<StkSymbol, string>(ChartBase.<>c.<>9.method_8)).Distinct<string>())
			{
				ChartBase.Class232 @class = new ChartBase.Class232();
				ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
				toolStripMenuItem.Text = text;
				@class.string_0 = text.Substring(0, text.IndexOf(Class521.smethod_0(24872)));
				IEnumerable<StkSymbol> enumerable = ienumerable_0.Where(new Func<StkSymbol, bool>(@class.method_0));
				if (enumerable.Any<StkSymbol>())
				{
					this.method_24(toolStripMenuItem, enumerable);
					toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem);
					result = true;
				}
			}
			return result;
		}

		// Token: 0x06000F14 RID: 3860 RVA: 0x00062F78 File Offset: 0x00061178
		private void method_24(ToolStripMenuItem toolStripMenuItem_0, IEnumerable<StkSymbol> ienumerable_0)
		{
			toolStripMenuItem_0.DropDownItems.Clear();
			foreach (StkSymbol stkSymbol in ienumerable_0)
			{
				ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
				toolStripMenuItem.Text = stkSymbol.CNName + Class521.smethod_0(24872) + stkSymbol.Code + Class521.smethod_0(5046);
				toolStripMenuItem.Click += this.method_25;
				toolStripMenuItem.Tag = stkSymbol.ID;
				if (stkSymbol.Code == Base.Data.CurrSelectedSymbol.Code)
				{
					toolStripMenuItem.Checked = true;
				}
				toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem);
			}
		}

		// Token: 0x06000F15 RID: 3861 RVA: 0x0006304C File Offset: 0x0006124C
		private void method_25(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = (ToolStripMenuItem)sender;
			if (!toolStripMenuItem.Checked)
			{
				int num = Convert.ToInt32(toolStripMenuItem.Tag);
				StkSymbol stkSymbol_ = SymbMgr.smethod_3(num);
				if (Base.UI.IsInCreateNewPageState)
				{
					SplitterPanel containerSpltPanel = this.ChtCtrl.ContainerSpltPanel;
					int? num2 = null;
					if (this.ChtCtrl.IfNoSync)
					{
						num2 = new int?(num);
					}
					else
					{
						if (MessageBox.Show(Class521.smethod_0(72061), Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) != DialogResult.OK)
						{
							return;
						}
						this.ChtCtrl.IfNoSync = true;
						num2 = new int?(num);
						this.ChtCtrl.LinkedSymblId = num2;
					}
					Base.UI.smethod_176(Class521.smethod_0(4654));
					ChtCtrl chtCtrl;
					if (this.ChtCtrl is ChtCtrl_KLine)
					{
						chtCtrl = Base.UI.smethod_143(num2, new PeriodType?(this.ChtCtrl.PeriodType), this.ChtCtrl.PeriodUnits);
					}
					else
					{
						chtCtrl = Base.UI.smethod_145(num2, new PeriodType?(this.ChtCtrl.PeriodType), this.ChtCtrl.PeriodUnits);
					}
					containerSpltPanel.Controls.Clear();
					containerSpltPanel.Controls.Add(chtCtrl);
					chtCtrl.ContainerSpltPanel = containerSpltPanel;
				}
				else
				{
					Base.UI.smethod_176(Class521.smethod_0(4654));
					Base.UI.smethod_175(stkSymbol_);
				}
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06000F16 RID: 3862 RVA: 0x0006319C File Offset: 0x0006139C
		public void method_26(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(72170);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			if (!this.ChtCtrl.IfNoSync)
			{
				toolStripMenuItem.Checked = true;
			}
			toolStripMenuItem.Click += this.method_27;
		}

		// Token: 0x06000F17 RID: 3863 RVA: 0x000631F4 File Offset: 0x000613F4
		private void method_27(object sender, EventArgs e)
		{
			if ((sender as ToolStripMenuItem).Checked)
			{
				this.ChtCtrl.IfNoSync = true;
				this.ChtCtrl.LinkedSymblId = new int?(this.ChtCtrl.Symbol.ID);
			}
			else
			{
				this.ChtCtrl.IfNoSync = false;
				this.ChtCtrl.LinkedSymblId = null;
			}
			Base.UI.smethod_104();
		}

		// Token: 0x06000F18 RID: 3864 RVA: 0x0000686F File Offset: 0x00004A6F
		private void method_28(object sender, EventArgs e)
		{
			if (!((ToolStripMenuItem)sender).Checked)
			{
				Base.UI.smethod_149(this.ChtCtrl);
			}
		}

		// Token: 0x06000F19 RID: 3865 RVA: 0x0000688C File Offset: 0x00004A8C
		private void method_29(object sender, EventArgs e)
		{
			if (!((ToolStripMenuItem)sender).Checked)
			{
				Base.UI.smethod_151(this.ChtCtrl);
			}
		}

		// Token: 0x06000F1A RID: 3866 RVA: 0x000068A9 File Offset: 0x00004AA9
		private void method_30(object sender, EventArgs e)
		{
			Base.UI.smethod_153(this.ChtCtrl);
		}

		// Token: 0x06000F1B RID: 3867 RVA: 0x00063264 File Offset: 0x00061464
		protected void method_31(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(72187);
			toolStripMenuItem.Checked = this.ChtCtrl.IsReverse;
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_18);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.Click += this.method_35;
		}

		// Token: 0x06000F1C RID: 3868 RVA: 0x000632E4 File Offset: 0x000614E4
		protected void method_32(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(27236);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_22);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Click += this.method_37;
		}

		// Token: 0x06000F1D RID: 3869 RVA: 0x00063354 File Offset: 0x00061554
		protected void method_33(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(27274);
			toolStripMenuItem.ShortcutKeys = Class210.smethod_3(Enum3.const_23);
			toolStripMenuItem.Image = Class375.Book_angleHS;
			toolStripMenuItem.Click += this.method_34;
			try
			{
				contextMenuStrip_0.Items.Add(toolStripMenuItem);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06000F1E RID: 3870 RVA: 0x000068B8 File Offset: 0x00004AB8
		private void method_34(object sender, EventArgs e)
		{
			BaoDianMgr.smethod_6(this.ChtCtrl, null);
		}

		// Token: 0x06000F1F RID: 3871 RVA: 0x000068C8 File Offset: 0x00004AC8
		private void method_35(object sender, EventArgs e)
		{
			this.ChtCtrl.IsReverse = !this.ChtCtrl.IsReverse;
		}

		// Token: 0x06000F20 RID: 3872 RVA: 0x000068E5 File Offset: 0x00004AE5
		private void method_36(object sender, EventArgs e)
		{
			this.ChtCtrl.IfShowTickPanel = !this.ChtCtrl.IfShowTickPanel;
		}

		// Token: 0x06000F21 RID: 3873 RVA: 0x00006902 File Offset: 0x00004B02
		private void method_37(object sender, EventArgs e)
		{
			Base.UI.smethod_179(this.Symbol);
		}

		// Token: 0x06000F22 RID: 3874 RVA: 0x00006911 File Offset: 0x00004B11
		protected void method_38(ToolStripDropDown toolStripDropDown_0)
		{
			toolStripDropDown_0.Items.Add(new ToolStripSeparator());
		}

		// Token: 0x06000F23 RID: 3875 RVA: 0x00006926 File Offset: 0x00004B26
		private void method_39(object sender, EventArgs e)
		{
			this.method_49();
		}

		// Token: 0x06000F24 RID: 3876 RVA: 0x00006930 File Offset: 0x00004B30
		private void method_40(object sender, EventArgs e)
		{
			Base.UI.smethod_81();
		}

		// Token: 0x06000F25 RID: 3877 RVA: 0x000633CC File Offset: 0x000615CC
		private void method_41(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(this.Symbol.ID, OrderType.Order_OpenLong, 0m))
			{
				Base.Trading.smethod_198(this.Symbol.ID, OrderType.Order_OpenLong, new decimal?(0m));
			}
		}

		// Token: 0x06000F26 RID: 3878 RVA: 0x00063414 File Offset: 0x00061614
		private void method_42(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(this.Symbol.ID, OrderType.Order_CloseLong, 0m))
			{
				Base.Trading.smethod_198(this.Symbol.ID, OrderType.Order_CloseLong, new decimal?(0m));
			}
		}

		// Token: 0x06000F27 RID: 3879 RVA: 0x0006345C File Offset: 0x0006165C
		private void method_43(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(this.Symbol.ID, OrderType.Order_OpenShort, 0m))
			{
				Base.Trading.smethod_198(this.Symbol.ID, OrderType.Order_OpenShort, new decimal?(0m));
			}
		}

		// Token: 0x06000F28 RID: 3880 RVA: 0x000634A4 File Offset: 0x000616A4
		private void method_44(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(this.Symbol.ID, OrderType.Order_CloseShort, 0m))
			{
				Base.Trading.smethod_198(this.Symbol.ID, OrderType.Order_CloseShort, new decimal?(0m));
			}
		}

		// Token: 0x06000F29 RID: 3881 RVA: 0x000634EC File Offset: 0x000616EC
		protected void method_45(TextObj textObj_3)
		{
			textObj_3.FontSpec.FontColor = Color.Silver;
			textObj_3.FontSpec.Border.IsVisible = false;
			textObj_3.FontSpec.StringAlignment = StringAlignment.Near;
			textObj_3.FontSpec.Fill.IsVisible = false;
			textObj_3.FontSpec.Size = 11f;
			textObj_3.FontSpec.IsBold = false;
			textObj_3.IsClippedToChartRect = true;
		}

		// Token: 0x06000F2A RID: 3882 RVA: 0x0006355C File Offset: 0x0006175C
		public TextObj method_46()
		{
			TextObj textObj = new TextObj(Class521.smethod_0(1449), 0.015, 0.016, CoordType.ChartFraction, AlignH.Left, AlignV.Top);
			this.method_45(textObj);
			return textObj;
		}

		// Token: 0x06000F2B RID: 3883 RVA: 0x0006359C File Offset: 0x0006179C
		private void method_47()
		{
			this.textObj_1 = new TextObj(Class521.smethod_0(1449), 30.0, 1.0, CoordType.XScaleYChartFraction, AlignH.Left, AlignV.Top);
			this.textObj_2 = new TextObj(Class521.smethod_0(1449), 0.0, 25000.0, CoordType.XChartFractionYScale, AlignH.Right, AlignV.Top);
			this.textObj_1.ZOrder = ZOrder.A_InFront;
			this.textObj_2.ZOrder = ZOrder.A_InFront;
			this.textObj_1.Tag = Class521.smethod_0(72204);
			this.textObj_2.Tag = Class521.smethod_0(72213);
			this.method_48(this.textObj_1);
			this.method_48(this.textObj_2);
			this.GraphPane.GraphObjList.Add(this.textObj_1);
			this.GraphPane.GraphObjList.Add(this.textObj_2);
		}

		// Token: 0x06000F2C RID: 3884 RVA: 0x00063688 File Offset: 0x00061888
		private void method_48(TextObj textObj_3)
		{
			textObj_3.FontSpec.FontColor = Color.Gray;
			textObj_3.FontSpec.Border.IsVisible = true;
			textObj_3.FontSpec.Border.Color = Color.Gray;
			textObj_3.FontSpec.StringAlignment = StringAlignment.Near;
			textObj_3.FontSpec.Fill.IsVisible = true;
			textObj_3.FontSpec.Fill.Color = Color.AliceBlue;
			textObj_3.FontSpec.Size = 11f;
			textObj_3.FontSpec.IsBold = false;
			textObj_3.IsClippedToChartRect = false;
		}

		// Token: 0x06000F2D RID: 3885 RVA: 0x00063724 File Offset: 0x00061924
		protected virtual void zedGraphControl_0_MouseLeave(object sender, EventArgs e)
		{
			if (this.textObj_2.IsVisible)
			{
				this.textObj_2.IsVisible = false;
			}
			if (this.textObj_1.IsVisible)
			{
				this.textObj_1.IsVisible = false;
			}
			this.zedGraphControl_0.Refresh();
			if (!this.IsXAxisVisible)
			{
				ChartBase chartWithXAxisShown = this.ChtCtrl.ChartWithXAxisShown;
				if (chartWithXAxisShown != null)
				{
					if (chartWithXAxisShown.PtDateTxtObj.IsVisible)
					{
						chartWithXAxisShown.PtDateTxtObj.IsVisible = false;
					}
					chartWithXAxisShown.ZedGraphControl.Refresh();
				}
			}
			this.RevCrossYVal = null;
			if (this.ChtCtrl.IsInCrossReviewMode && !this.ChtCtrl.IsMouseEntered)
			{
				this.ChtCtrl.method_26();
				this.ChtCtrl.vmethod_22();
				this.ChtCtrl.RevCrossXVal = null;
				this.ChtCtrl.Refresh();
			}
		}

		// Token: 0x06000F2E RID: 3886 RVA: 0x00006926 File Offset: 0x00004B26
		private void zedGraphControl_0_DoubleClick(object sender, EventArgs e)
		{
			this.method_49();
		}

		// Token: 0x06000F2F RID: 3887 RVA: 0x00006939 File Offset: 0x00004B39
		private void method_49()
		{
			if (this.ChtCtrl.IsInCrossReviewMode)
			{
				this.ChtCtrl.IsInCrossReviewMode = false;
			}
			else if (Base.UI.DrawMode == DrawMode.Off)
			{
				this.ChtCtrl.IsInCrossReviewMode = true;
			}
			this.ChtCtrl.method_15();
		}

		// Token: 0x06000F30 RID: 3888 RVA: 0x0006380C File Offset: 0x00061A0C
		protected virtual bool zedGraphControl_0_MouseMoveEvent(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			PointF pointF = new PointF((float)mouseEventArgs_0.X, (float)mouseEventArgs_0.Y);
			if (this.LastMousePtF == null)
			{
				this.LastMousePtF = new PointF?(pointF);
			}
			else
			{
				if (pointF == this.LastMousePtF.Value)
				{
					return false;
				}
				this.LastMousePtF = new PointF?(pointF);
			}
			Point point_ = new Point(mouseEventArgs_0.X, mouseEventArgs_0.Y);
			this.method_51(point_);
			return true;
		}

		// Token: 0x06000F31 RID: 3889 RVA: 0x00063894 File Offset: 0x00061A94
		protected PointD method_50(int int_0, int int_1)
		{
			Point p = new Point(int_0, int_1);
			GraphPane graphPane = this.MasterPane.FindChartRect(p);
			PointD result;
			if (graphPane != null)
			{
				double x;
				double y;
				graphPane.ReverseTransform(p, out x, out y);
				result = new PointD(x, y);
			}
			else
			{
				result = default(PointD);
			}
			return result;
		}

		// Token: 0x06000F32 RID: 3890 RVA: 0x000638EC File Offset: 0x00061AEC
		private void method_51(Point point_0)
		{
			GraphPane graphPane = this.MasterPane.FindChartRect(point_0);
			if (graphPane != null && graphPane.CurveList.Any<CurveItem>())
			{
				CurveItem curveItem = graphPane.CurveList.Aggregate(new Func<CurveItem, CurveItem, CurveItem>(ChartBase.<>c.<>9.method_9));
				double num;
				double num2;
				graphPane.ReverseTransform(point_0, out num, out num2);
				int num3 = Convert.ToInt32(Math.Round(num)) - 1;
				if (num3 >= curveItem.NPts)
				{
					num3 = curveItem.NPts - 1;
				}
				if (num3 < 0)
				{
					num3 = 0;
				}
				string text;
				if (num3 == curveItem.NPts - 1 && this.ChtCtrl.IsLastItemShown)
				{
					try
					{
						text = this.SymbDataSet.CurrHisDataSet.CurrHisData.Date.ToString(Class521.smethod_0(47367));
						goto IL_FB;
					}
					catch
					{
						text = Class521.smethod_0(1449);
						goto IL_FB;
					}
				}
				text = XDate.ToString(this.method_53(num3), Class521.smethod_0(47367));
				IL_FB:
				if (text.Contains(Class521.smethod_0(3672)))
				{
					text = Class521.smethod_0(1449);
				}
				else
				{
					text = this.method_52(text);
				}
				double double_ = (double)(this.ChtCtrl.IsInCrossReviewMode ? 1 : 0);
				this.method_56(double_, num2);
				if (!this.textObj_2.IsVisible)
				{
					this.textObj_2.IsVisible = true;
				}
				if (this.IsXAxisVisible)
				{
					this.method_58(this, text, point_0);
				}
				else
				{
					ChartBase chartWithXAxisShown = this.ChtCtrl.ChartWithXAxisShown;
					if (chartWithXAxisShown != null)
					{
						this.method_58(chartWithXAxisShown, text, point_0);
						chartWithXAxisShown.ZedGraphControl.Refresh();
					}
				}
				if (this.ChtCtrl.IsInCrossReviewMode)
				{
					this.RevCrossYVal = new double?(num2);
					this.ChtCtrl.RevCrossXVal = new double?(num);
					this.ChtCtrl.method_15();
				}
				else
				{
					this.zedGraphControl_0.Refresh();
				}
			}
		}

		// Token: 0x06000F33 RID: 3891 RVA: 0x00063AF0 File Offset: 0x00061CF0
		protected string method_52(string string_9)
		{
			string result;
			if (Base.UI.Form.IsInBlindTestMode && !string.IsNullOrEmpty(string_9))
			{
				int num = string_9.IndexOf(Class521.smethod_0(3636));
				int num2;
				if (string_9.Substring(0, 1) != Class521.smethod_0(47686))
				{
					num2 = 0;
				}
				else
				{
					num2 = 1;
				}
				string oldValue = string_9.Substring(num2, num - num2);
				result = string_9.Replace(oldValue, Class521.smethod_0(54048));
			}
			else
			{
				result = string_9;
			}
			return result;
		}

		// Token: 0x06000F34 RID: 3892 RVA: 0x00063B68 File Offset: 0x00061D68
		protected double method_53(int int_0)
		{
			CurveItem curveItem = this.GraphPane.CurveList.Aggregate(new Func<CurveItem, CurveItem, CurveItem>(ChartBase.<>c.<>9.method_10));
			ValueHandler valueHandler = new ValueHandler(this.GraphPane, false);
			double result;
			if ((curveItem is BarItem || curveItem is ErrorBarItem || curveItem is HiLowBarItem) && this.GraphPane.BarSettings.Base != BarBase.X)
			{
				double num;
				double num2;
				valueHandler.GetValues(curveItem, int_0, out num, out num2, out result);
			}
			else
			{
				double num;
				double num2;
				valueHandler.GetValues(curveItem, int_0, out result, out num2, out num);
			}
			return result;
		}

		// Token: 0x06000F35 RID: 3893 RVA: 0x00063C04 File Offset: 0x00061E04
		protected string method_54(double double_0)
		{
			int digits = this.vmethod_14();
			return Math.Round(double_0, digits).ToString(Class521.smethod_0(5338) + digits.ToString());
		}

		// Token: 0x06000F36 RID: 3894 RVA: 0x00063C44 File Offset: 0x00061E44
		protected virtual int vmethod_14()
		{
			return this.Symbol.DigitNb;
		}

		// Token: 0x06000F37 RID: 3895 RVA: 0x00006978 File Offset: 0x00004B78
		protected void method_55(double double_0)
		{
			this.method_56(0.0, double_0);
		}

		// Token: 0x06000F38 RID: 3896 RVA: 0x00063C60 File Offset: 0x00061E60
		protected void method_56(double double_0, double double_1)
		{
			this.textObj_2.Text = this.method_54(double_1);
			this.textObj_2.Location.X = double_0;
			this.textObj_2.Location.Y = double_1;
			if (!this.textObj_2.IsVisible)
			{
				this.textObj_2.IsVisible = true;
			}
		}

		// Token: 0x06000F39 RID: 3897 RVA: 0x0000698C File Offset: 0x00004B8C
		protected void method_57(string string_9, double double_0)
		{
			this.method_59(this, string_9, double_0);
		}

		// Token: 0x06000F3A RID: 3898 RVA: 0x00063CBC File Offset: 0x00061EBC
		protected void method_58(ChartBase chartBase_0, string string_9, PointF pointF_0)
		{
			double double_;
			double num;
			chartBase_0.GraphPane.ReverseTransform(pointF_0, out double_, out num);
			this.method_59(chartBase_0, string_9, double_);
		}

		// Token: 0x06000F3B RID: 3899 RVA: 0x00063CE4 File Offset: 0x00061EE4
		protected void method_59(ChartBase chartBase_0, string string_9, double double_0)
		{
			TextObj ptDateTxtObj = chartBase_0.PtDateTxtObj;
			ptDateTxtObj.Text = string_9;
			RectangleF rectF = ptDateTxtObj.RectF;
			if (chartBase_0.GraphPane.XAxis.Scale.Transform(double_0) + rectF.Width > chartBase_0.GraphPane.Rect.Width)
			{
				PointF ptF = new PointF(chartBase_0.GraphPane.Rect.Width - rectF.Width - 5f, 0f);
				double num;
				double num2;
				chartBase_0.GraphPane.ReverseTransform(ptF, out num, out num2);
				double_0 = num;
			}
			ptDateTxtObj.Location.X = double_0;
			if (!ptDateTxtObj.IsVisible)
			{
				ptDateTxtObj.IsVisible = true;
			}
		}

		// Token: 0x06000F3C RID: 3900 RVA: 0x00063D9C File Offset: 0x00061F9C
		public virtual void vmethod_15()
		{
			float size = this.vmethod_16();
			foreach (TextObj textObj in this.TextObjList)
			{
				textObj.FontSpec.Size = size;
			}
			if (this.GraphPane != null)
			{
				this.GraphPane.XAxis.Scale.FontSpec.Size = size;
				this.GraphPane.YAxis.Scale.FontSpec.Size = size;
				this.GraphPane.Y2Axis.Scale.FontSpec.Size = size;
				if (this.RevInfoBox != null)
				{
					double num = this.vmethod_20();
					this.RevInfoBox.Location.X = -num;
					this.RevInfoBox.Location.Width = num;
					foreach (TextObj textObj2 in this.RevInfoLabelList)
					{
						textObj2.Location.X = -num / 2.0;
					}
				}
			}
		}

		// Token: 0x06000F3D RID: 3901 RVA: 0x00063EDC File Offset: 0x000620DC
		public virtual float vmethod_16()
		{
			float dpiScaleMulti = TApp.DpiScaleMulti;
			float num = 12.5f * dpiScaleMulti;
			float num2 = 17f * dpiScaleMulti;
			float num3 = ((float)(10 * this.ChtCtrl.Width / 1250) + 8f) * dpiScaleMulti;
			if (num3 < num)
			{
				num3 = num;
			}
			else if (num3 > num2)
			{
				num3 = num2;
			}
			return num3;
		}

		// Token: 0x06000F3E RID: 3902 RVA: 0x00063F34 File Offset: 0x00062134
		public void method_60(TextObj textObj_3)
		{
			if (textObj_3 != null)
			{
				float size = this.vmethod_16();
				textObj_3.FontSpec.Size = size;
			}
		}

		// Token: 0x06000F3F RID: 3903 RVA: 0x00063F5C File Offset: 0x0006215C
		protected virtual void vmethod_17()
		{
			if (this.GraphPane != null && this.GraphPane.GraphObjList != null)
			{
				this.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(ChartBase.<>c.<>9.method_11));
			}
			this.RevInfoBox = null;
		}

		// Token: 0x06000F40 RID: 3904 RVA: 0x00063FB8 File Offset: 0x000621B8
		protected virtual void vmethod_18()
		{
			GraphPane graphPane = this.GraphPane;
			if (graphPane == null)
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(48589)));
			}
			else
			{
				foreach (GraphObj item in graphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartBase.<>c.<>9.method_12)).ToList<GraphObj>())
				{
					graphPane.GraphObjList.Remove(item);
				}
				if (this.ChtCtrl.IsInCrossReviewMode)
				{
					Color color = Color.Gray;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						color = Color.Silver;
					}
					double? num = null;
					if (this.ChtCtrl.RevCrossXVal != null)
					{
						num = this.ChtCtrl.RevCrossXVal;
					}
					else if (this.ChtCtrl.IsMouseEntered)
					{
						this.ChtCtrl.RevCrossXVal = new double?((double)(this.ChtCtrl.IndexOfLastItemShownInScr + 1));
						num = this.ChtCtrl.RevCrossXVal;
					}
					if (num != null)
					{
						LineObj lineObj = new LineObj(color, num.Value, graphPane.YAxis.Scale.Min, num.Value, graphPane.YAxis.Scale.Max);
						lineObj.Tag = ChartBase.string_1;
						graphPane.GraphObjList.Add(lineObj);
						double? num2 = null;
						if (this.RevCrossYVal != null)
						{
							num2 = this.RevCrossYVal;
						}
						if (num2 != null)
						{
							LineObj lineObj2 = new LineObj(color, graphPane.XAxis.Scale.Min, num2.Value, graphPane.XAxis.Scale.Max, num2.Value);
							lineObj2.Tag = ChartBase.string_1;
							graphPane.GraphObjList.Add(lineObj2);
						}
						this.vmethod_19();
					}
				}
			}
		}

		// Token: 0x06000F41 RID: 3905 RVA: 0x000641CC File Offset: 0x000623CC
		public void method_61()
		{
			GraphPane graphPane = this.GraphPane;
			foreach (GraphObj item in graphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartBase.<>c.<>9.method_13)).ToList<GraphObj>())
			{
				graphPane.GraphObjList.Remove(item);
			}
		}

		// Token: 0x06000F42 RID: 3906 RVA: 0x00006999 File Offset: 0x00004B99
		protected virtual void vmethod_19()
		{
			if (!this.HeaderTextObj.IsVisible)
			{
				this.HeaderTextObj.IsVisible = true;
			}
		}

		// Token: 0x06000F43 RID: 3907 RVA: 0x00064258 File Offset: 0x00062458
		protected TextObj method_62(string string_9, double double_0, double double_1)
		{
			return new TextObj(string_9, double_0, double_1, CoordType.ChartFraction, AlignH.Center, AlignV.Top)
			{
				Tag = ChartBase.string_4
			};
		}

		// Token: 0x06000F44 RID: 3908 RVA: 0x00064280 File Offset: 0x00062480
		protected TextObj method_63(string string_9, double double_0, double double_1)
		{
			return new TextObj(string_9, double_0, double_1, CoordType.ChartFraction, AlignH.Right, AlignV.Top)
			{
				Tag = ChartBase.string_5
			};
		}

		// Token: 0x06000F45 RID: 3909 RVA: 0x000642A8 File Offset: 0x000624A8
		protected BoxObj method_64(double double_0, double double_1)
		{
			Color borderColor = Color.Red;
			Color fillColor = Color.Black;
			Color fillColor2 = Color.Black;
			if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				borderColor = Color.Gray;
				fillColor = Color.FromArgb(255, 255, 236);
				fillColor2 = Color.FromArgb(255, 255, 236);
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Modern)
			{
				borderColor = Color.Gray;
				fillColor = Color.White;
				fillColor2 = Color.FromArgb(240, 240, 255);
			}
			return new BoxObj(-double_0, 0.0, double_0, double_1, borderColor, fillColor, fillColor2)
			{
				Tag = ChartBase.string_3,
				ZOrder = ZOrder.A_InFront,
				Location = 
				{
					CoordinateFrame = CoordType.ChartFraction
				}
			};
		}

		// Token: 0x06000F46 RID: 3910 RVA: 0x000069B6 File Offset: 0x00004BB6
		protected void method_65(TextObj textObj_3, Color color_0)
		{
			textObj_3.ZOrder = ZOrder.A_InFront;
			textObj_3.FontSpec.FontColor = color_0;
			textObj_3.FontSpec.Border.IsVisible = false;
			textObj_3.FontSpec.Fill.IsVisible = false;
		}

		// Token: 0x06000F47 RID: 3911 RVA: 0x0006436C File Offset: 0x0006256C
		public void method_66(bool bool_3)
		{
			if (this.GraphPane != null && this.GraphPane.GraphObjList != null)
			{
				foreach (GraphObj graphObj in this.GraphPane.GraphObjList)
				{
					if (graphObj != null && graphObj.Tag != null && graphObj.Tag.ToString().StartsWith(ChartBase.string_2))
					{
						graphObj.IsVisible = bool_3;
					}
				}
			}
		}

		// Token: 0x06000F48 RID: 3912 RVA: 0x00064400 File Offset: 0x00062600
		public void method_67()
		{
			string text = Class521.smethod_0(72226);
			foreach (TextObj textObj in this.RevInfoTextList)
			{
				textObj.Text = text;
			}
		}

		// Token: 0x06000F49 RID: 3913 RVA: 0x00064460 File Offset: 0x00062660
		public void method_68(Color color_0)
		{
			foreach (TextObj textObj in this.RevInfoLabelList)
			{
				textObj.FontSpec.FontColor = color_0;
			}
		}

		// Token: 0x06000F4A RID: 3914 RVA: 0x000644BC File Offset: 0x000626BC
		protected virtual double vmethod_20()
		{
			return (double)this.ChtCtrl.ChartRect_LeftMargin / ((double)this.GraphPane.Rect.Width - (double)this.ChtCtrl.ChartRect_LeftMargin * 0.95);
		}

		// Token: 0x06000F4B RID: 3915 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_21(bool bool_3)
		{
		}

		// Token: 0x06000F4C RID: 3916 RVA: 0x00064508 File Offset: 0x00062708
		protected bool method_69(int int_0)
		{
			bool result = false;
			try
			{
				if (int_0 >= 0 && int_0 < this.PeriodHisDataList.Count)
				{
					result = this.SymbDataSet.method_36(this.PeriodHisDataList.Values[int_0], true);
				}
				else
				{
					Class184.smethod_0(new Exception(Class521.smethod_0(72235)));
				}
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x06000F4D RID: 3917 RVA: 0x00064578 File Offset: 0x00062778
		protected string method_70(double double_0)
		{
			return Utility.GetValidStringFromDoubleVal(double_0, this.Symbol.DigitNb, Class521.smethod_0(1449));
		}

		// Token: 0x06000F4E RID: 3918 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_22(HisData hisData_0)
		{
		}

		// Token: 0x06000F4F RID: 3919 RVA: 0x000069EF File Offset: 0x00004BEF
		public void Dispose()
		{
			this.vmethod_23(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x06000F50 RID: 3920 RVA: 0x000645A4 File Offset: 0x000627A4
		~ChartBase()
		{
			this.vmethod_23(false);
		}

		// Token: 0x06000F51 RID: 3921 RVA: 0x00006A00 File Offset: 0x00004C00
		protected virtual void vmethod_23(bool bool_3)
		{
			if (!this.bool_1)
			{
				if (bool_3 && this.zedGraphControl_0 != null)
				{
					this.zedGraphControl_0.Dispose();
				}
				this.bool_1 = true;
			}
		}

		// Token: 0x1700024B RID: 587
		// (get) Token: 0x06000F52 RID: 3922 RVA: 0x000645D4 File Offset: 0x000627D4
		public PaneBase Pane
		{
			get
			{
				return this.zedGraphControl_0.GraphPane;
			}
		}

		// Token: 0x1700024C RID: 588
		// (get) Token: 0x06000F53 RID: 3923 RVA: 0x000645F0 File Offset: 0x000627F0
		public MasterPane MasterPane
		{
			get
			{
				return this.zedGraphControl_0.MasterPane;
			}
		}

		// Token: 0x1700024D RID: 589
		// (get) Token: 0x06000F54 RID: 3924 RVA: 0x0006460C File Offset: 0x0006280C
		public GraphPane GraphPane
		{
			get
			{
				return this.zedGraphControl_0.GraphPane;
			}
		}

		// Token: 0x1700024E RID: 590
		// (get) Token: 0x06000F55 RID: 3925 RVA: 0x00064628 File Offset: 0x00062828
		public ZedGraphControl ZedGraphControl
		{
			get
			{
				return this.zedGraphControl_0;
			}
		}

		// Token: 0x1700024F RID: 591
		// (get) Token: 0x06000F56 RID: 3926 RVA: 0x00064640 File Offset: 0x00062840
		public SortedList<DateTime, HisData> HisDataList
		{
			get
			{
				return this.chtCtrl_0.HisDataList;
			}
		}

		// Token: 0x17000250 RID: 592
		// (get) Token: 0x06000F57 RID: 3927 RVA: 0x0006465C File Offset: 0x0006285C
		public HisDataPeriodSet HisDataPeriodSet
		{
			get
			{
				return this.chtCtrl_0.HisDataPeriodSet;
			}
		}

		// Token: 0x17000251 RID: 593
		// (get) Token: 0x06000F58 RID: 3928 RVA: 0x00064678 File Offset: 0x00062878
		public ChtCtrl ChtCtrl
		{
			get
			{
				return this.chtCtrl_0;
			}
		}

		// Token: 0x17000252 RID: 594
		// (get) Token: 0x06000F59 RID: 3929 RVA: 0x00064690 File Offset: 0x00062890
		// (set) Token: 0x06000F5A RID: 3930 RVA: 0x00006A2B File Offset: 0x00004C2B
		public ChartType ChartType
		{
			get
			{
				return this.chartType_0;
			}
			protected set
			{
				this.chartType_0 = value;
			}
		}

		// Token: 0x17000253 RID: 595
		// (get) Token: 0x06000F5B RID: 3931 RVA: 0x000646A8 File Offset: 0x000628A8
		// (set) Token: 0x06000F5C RID: 3932 RVA: 0x00006A36 File Offset: 0x00004C36
		public string Tag
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x17000254 RID: 596
		// (get) Token: 0x06000F5D RID: 3933 RVA: 0x000646C0 File Offset: 0x000628C0
		// (set) Token: 0x06000F5E RID: 3934 RVA: 0x00006A41 File Offset: 0x00004C41
		public bool IsXAxisVisible
		{
			get
			{
				bool result = false;
				if (this.GraphPane != null && this.GraphPane.XAxis != null)
				{
					result = this.GraphPane.XAxis.IsVisible;
				}
				return result;
			}
			set
			{
				if (this.GraphPane != null && this.GraphPane.XAxis != null)
				{
					this.GraphPane.XAxis.IsVisible = value;
				}
			}
		}

		// Token: 0x17000255 RID: 597
		// (get) Token: 0x06000F5F RID: 3935 RVA: 0x000646FC File Offset: 0x000628FC
		// (set) Token: 0x06000F60 RID: 3936 RVA: 0x00006A6B File Offset: 0x00004C6B
		public bool IfShowPointValue
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x17000256 RID: 598
		// (get) Token: 0x06000F61 RID: 3937 RVA: 0x00064714 File Offset: 0x00062914
		// (set) Token: 0x06000F62 RID: 3938 RVA: 0x00006A76 File Offset: 0x00004C76
		public TextObj PtDateTxtObj
		{
			get
			{
				return this.textObj_1;
			}
			set
			{
				this.textObj_1 = value;
			}
		}

		// Token: 0x17000257 RID: 599
		// (get) Token: 0x06000F63 RID: 3939 RVA: 0x0006472C File Offset: 0x0006292C
		// (set) Token: 0x06000F64 RID: 3940 RVA: 0x00006A81 File Offset: 0x00004C81
		public TextObj HeaderTextObj
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x17000258 RID: 600
		// (get) Token: 0x06000F65 RID: 3941 RVA: 0x00064744 File Offset: 0x00062944
		public List<TextObj> TextObjList
		{
			get
			{
				List<TextObj> result;
				try
				{
					result = this.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartBase.<>c.<>9.method_14)).Cast<TextObj>().ToList<TextObj>();
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					result = new List<TextObj>();
				}
				return result;
			}
		}

		// Token: 0x17000259 RID: 601
		// (get) Token: 0x06000F66 RID: 3942 RVA: 0x000647B0 File Offset: 0x000629B0
		// (set) Token: 0x06000F67 RID: 3943 RVA: 0x00006A8C File Offset: 0x00004C8C
		public double? RevCrossYVal
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x1700025A RID: 602
		// (get) Token: 0x06000F68 RID: 3944 RVA: 0x000647C8 File Offset: 0x000629C8
		// (set) Token: 0x06000F69 RID: 3945 RVA: 0x00006A97 File Offset: 0x00004C97
		public BoxObj RevInfoBox
		{
			get
			{
				return this.boxObj_0;
			}
			set
			{
				this.boxObj_0 = value;
			}
		}

		// Token: 0x1700025B RID: 603
		// (get) Token: 0x06000F6A RID: 3946 RVA: 0x000647E0 File Offset: 0x000629E0
		public List<TextObj> RevInfoLabelList
		{
			get
			{
				return this.TextObjList.Where(new Func<TextObj, bool>(ChartBase.<>c.<>9.method_15)).ToList<TextObj>();
			}
		}

		// Token: 0x1700025C RID: 604
		// (get) Token: 0x06000F6B RID: 3947 RVA: 0x00064820 File Offset: 0x00062A20
		public List<TextObj> RevInfoTextList
		{
			get
			{
				return this.TextObjList.Where(new Func<TextObj, bool>(ChartBase.<>c.<>9.method_16)).ToList<TextObj>();
			}
		}

		// Token: 0x1700025D RID: 605
		// (get) Token: 0x06000F6C RID: 3948 RVA: 0x00064860 File Offset: 0x00062A60
		public SortedList<DateTime, HisData> PeriodHisDataList
		{
			get
			{
				return this.HisDataPeriodSet.PeriodHisDataList;
			}
		}

		// Token: 0x1700025E RID: 606
		// (get) Token: 0x06000F6D RID: 3949 RVA: 0x0006487C File Offset: 0x00062A7C
		public int MaxSticksPerChart
		{
			get
			{
				return this.ChtCtrl.MaxSticksPerChart;
			}
		}

		// Token: 0x1700025F RID: 607
		// (get) Token: 0x06000F6E RID: 3950 RVA: 0x00064898 File Offset: 0x00062A98
		// (set) Token: 0x06000F6F RID: 3951 RVA: 0x00006AA2 File Offset: 0x00004CA2
		public SplitterPanel ParentPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
			}
		}

		// Token: 0x17000260 RID: 608
		// (get) Token: 0x06000F70 RID: 3952 RVA: 0x000648B0 File Offset: 0x00062AB0
		// (set) Token: 0x06000F71 RID: 3953 RVA: 0x00006AAD File Offset: 0x00004CAD
		public PointF? LastMousePtF { get; set; }

		// Token: 0x17000261 RID: 609
		// (get) Token: 0x06000F72 RID: 3954 RVA: 0x000648C8 File Offset: 0x00062AC8
		public double WidthHeightAdjRatio
		{
			get
			{
				return this.method_71();
			}
		}

		// Token: 0x06000F73 RID: 3955 RVA: 0x000648E0 File Offset: 0x00062AE0
		private double method_71()
		{
			double num = this.GraphPane.YAxis.Scale.Max - this.GraphPane.YAxis.Scale.Min;
			double num2 = this.GraphPane.XAxis.Scale.Max - this.GraphPane.XAxis.Scale.Min;
			float width = this.GraphPane.Chart.Rect.Width;
			float height = this.GraphPane.Chart.Rect.Height;
			return num / (double)height / (num2 / (double)width);
		}

		// Token: 0x06000F74 RID: 3956 RVA: 0x00064984 File Offset: 0x00062B84
		public SortedList<DateTime, HisData> method_72(DateTime dateTime_0, ref bool bool_3)
		{
			return this.ChtCtrl.method_40(dateTime_0, ref bool_3);
		}

		// Token: 0x17000262 RID: 610
		// (get) Token: 0x06000F75 RID: 3957 RVA: 0x000649A4 File Offset: 0x00062BA4
		public StkSymbol Symbol
		{
			get
			{
				return this.ChtCtrl.Symbol;
			}
		}

		// Token: 0x17000263 RID: 611
		// (get) Token: 0x06000F76 RID: 3958 RVA: 0x000649C0 File Offset: 0x00062BC0
		public SymbDataSet SymbDataSet
		{
			get
			{
				return this.ChtCtrl.SymbDataSet;
			}
		}

		// Token: 0x17000264 RID: 612
		// (get) Token: 0x06000F77 RID: 3959 RVA: 0x000649DC File Offset: 0x00062BDC
		// (set) Token: 0x06000F78 RID: 3960 RVA: 0x00006AB8 File Offset: 0x00004CB8
		public bool SupportHighLowMark { get; set; }

		// Token: 0x04000797 RID: 1943
		private ZedGraphControl zedGraphControl_0;

		// Token: 0x04000798 RID: 1944
		private ChtCtrl chtCtrl_0;

		// Token: 0x04000799 RID: 1945
		private ChartType chartType_0;

		// Token: 0x0400079A RID: 1946
		private string string_0;

		// Token: 0x0400079B RID: 1947
		private bool bool_0;

		// Token: 0x0400079C RID: 1948
		private TextObj textObj_0;

		// Token: 0x0400079D RID: 1949
		private TextObj textObj_1;

		// Token: 0x0400079E RID: 1950
		private TextObj textObj_2;

		// Token: 0x0400079F RID: 1951
		private BoxObj boxObj_0;

		// Token: 0x040007A0 RID: 1952
		private SplitterPanel splitterPanel_0;

		// Token: 0x040007A1 RID: 1953
		protected static readonly string string_1 = Class521.smethod_0(46972);

		// Token: 0x040007A2 RID: 1954
		protected static readonly string string_2 = Class521.smethod_0(72264);

		// Token: 0x040007A3 RID: 1955
		protected static readonly string string_3 = ChartBase.string_2 + Class521.smethod_0(72277);

		// Token: 0x040007A4 RID: 1956
		protected static readonly string string_4 = ChartBase.string_2 + Class521.smethod_0(72286);

		// Token: 0x040007A5 RID: 1957
		protected static readonly string string_5 = ChartBase.string_2 + Class521.smethod_0(72295);

		// Token: 0x040007A6 RID: 1958
		protected static readonly string string_6 = Class521.smethod_0(72304);

		// Token: 0x040007A7 RID: 1959
		protected static readonly string string_7 = ChartBase.string_6 + Class521.smethod_0(18005);

		// Token: 0x040007A8 RID: 1960
		protected static readonly string string_8 = Class521.smethod_0(72317);

		// Token: 0x040007A9 RID: 1961
		private bool bool_1;

		// Token: 0x040007AA RID: 1962
		private double? nullable_0;

		// Token: 0x040007AB RID: 1963
		[CompilerGenerated]
		private PointF? nullable_1;

		// Token: 0x040007AC RID: 1964
		[CompilerGenerated]
		private bool bool_2;

		// Token: 0x0200018B RID: 395
		[CompilerGenerated]
		private sealed class Class227
		{
			// Token: 0x06000F7B RID: 3963 RVA: 0x00006AC3 File Offset: 0x00004CC3
			internal void method_0()
			{
				this.splitterPanel_0.Controls.Clear();
				this.splitterPanel_0.Controls.Add(this.chartBase_0.ZedGraphControl);
			}

			// Token: 0x040007AD RID: 1965
			public SplitterPanel splitterPanel_0;

			// Token: 0x040007AE RID: 1966
			public ChartBase chartBase_0;
		}

		// Token: 0x0200018C RID: 396
		[CompilerGenerated]
		private sealed class Class228
		{
			// Token: 0x06000F7D RID: 3965 RVA: 0x00064AA4 File Offset: 0x00062CA4
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key >= this.hisData_0.Date;
			}

			// Token: 0x040007AF RID: 1967
			public HisData hisData_0;
		}

		// Token: 0x0200018D RID: 397
		[CompilerGenerated]
		private sealed class Class229
		{
			// Token: 0x06000F7F RID: 3967 RVA: 0x00064ACC File Offset: 0x00062CCC
			internal bool method_0(GraphObj graphObj_0)
			{
				bool result;
				if (graphObj_0 is TextObj && graphObj_0.Tag != null)
				{
					result = (graphObj_0.Tag.ToString() == this.string_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040007B0 RID: 1968
			public string string_0;
		}

		// Token: 0x0200018E RID: 398
		[CompilerGenerated]
		private sealed class Class230
		{
			// Token: 0x06000F81 RID: 3969 RVA: 0x00064B08 File Offset: 0x00062D08
			internal bool method_0(GraphObj graphObj_0)
			{
				bool result;
				if (graphObj_0.Tag != null)
				{
					result = graphObj_0.Tag.ToString().StartsWith(Class521.smethod_0(71893) + this.string_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040007B1 RID: 1969
			public string string_0;
		}

		// Token: 0x0200018F RID: 399
		[CompilerGenerated]
		private sealed class Class231
		{
			// Token: 0x06000F83 RID: 3971 RVA: 0x00064B4C File Offset: 0x00062D4C
			internal bool method_0(ExchgHouse exchgHouse_0)
			{
				return exchgHouse_0.ID == this.int_0;
			}

			// Token: 0x06000F84 RID: 3972 RVA: 0x00064B6C File Offset: 0x00062D6C
			internal bool method_1(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ExchangeID == this.int_0;
			}

			// Token: 0x040007B2 RID: 1970
			public int int_0;
		}

		// Token: 0x02000191 RID: 401
		[CompilerGenerated]
		private sealed class Class232
		{
			// Token: 0x06000F99 RID: 3993 RVA: 0x00064D74 File Offset: 0x00062F74
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.MstSymbol.CNName == this.string_0;
			}

			// Token: 0x040007C5 RID: 1989
			public string string_0;
		}
	}
}
