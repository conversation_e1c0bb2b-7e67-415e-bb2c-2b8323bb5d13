﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;
using TEx.Inds;
using TEx.SIndicator;

namespace ns7
{
	// Token: 0x020002CE RID: 718
	internal sealed partial class Form21 : Form
	{
		// Token: 0x0600202C RID: 8236 RVA: 0x000E47B8 File Offset: 0x000E29B8
		public Form21()
		{
			this.method_2();
			Base.UI.smethod_54(this);
			this.dataGridView_0.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridView_0.BackgroundColor = Color.White;
			this.dataGridView_0.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView_0.ColumnHeadersVisible = false;
			this.dataGridView_0.RowHeadersVisible = false;
			this.dataGridView_0.DataSource = this.bindingList_0;
		}

		// Token: 0x0600202D RID: 8237 RVA: 0x000E4844 File Offset: 0x000E2A44
		public void method_0(List<IndEx> list_1)
		{
			if (list_1 != null)
			{
				this.bindingList_0.Clear();
				this.list_0 = list_1;
				foreach (IndEx indEx in this.list_0)
				{
					this.bindingList_0.Add(new NameScript(indEx.EnName, indEx.UDInd.UDS.Script));
				}
			}
		}

		// Token: 0x0600202E RID: 8238 RVA: 0x000E48D0 File Offset: 0x000E2AD0
		private IndEx method_1(int int_0)
		{
			IndEx result;
			if (int_0 < this.list_0.Count)
			{
				result = this.list_0[int_0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600202F RID: 8239 RVA: 0x000E4900 File Offset: 0x000E2B00
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.dataGridView_0.CurrentRow != null)
			{
				int index = this.dataGridView_0.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				if (indEx != null)
				{
					indEx.RemoveFromChart();
					this.list_0.Remove(indEx);
					this.bindingList_0.RemoveAt(index);
				}
			}
		}

		// Token: 0x06002030 RID: 8240 RVA: 0x0000D15F File Offset: 0x0000B35F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002031 RID: 8241 RVA: 0x000E4958 File Offset: 0x000E2B58
		private void method_2()
		{
			this.groupBox_0 = new GroupBox();
			this.dataGridView_0 = new DataGridView();
			this.button_0 = new Button();
			this.groupBox_0.SuspendLayout();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			base.SuspendLayout();
			this.groupBox_0.Controls.Add(this.dataGridView_0);
			this.groupBox_0.Location = new Point(34, 12);
			this.groupBox_0.Name = Class521.smethod_0(10647);
			this.groupBox_0.Size = new Size(319, 188);
			this.groupBox_0.TabIndex = 4;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(95268);
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.Location = new Point(6, 24);
			this.dataGridView_0.Name = Class521.smethod_0(95285);
			this.dataGridView_0.RowTemplate.Height = 27;
			this.dataGridView_0.Size = new Size(307, 152);
			this.dataGridView_0.TabIndex = 0;
			this.button_0.Location = new Point(141, 215);
			this.button_0.Margin = new Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(95306);
			this.button_0.Size = new Size(93, 30);
			this.button_0.TabIndex = 5;
			this.button_0.Text = Class521.smethod_0(95327);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(391, 259);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.groupBox_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(95344);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(48525);
			this.groupBox_0.ResumeLayout(false);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000FC0 RID: 4032
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FC1 RID: 4033
		private List<IndEx> list_0 = new List<IndEx>();

		// Token: 0x04000FC2 RID: 4034
		private IContainer icontainer_0;

		// Token: 0x04000FC3 RID: 4035
		private GroupBox groupBox_0;

		// Token: 0x04000FC4 RID: 4036
		private DataGridView dataGridView_0;

		// Token: 0x04000FC5 RID: 4037
		private Button button_0;
	}
}
