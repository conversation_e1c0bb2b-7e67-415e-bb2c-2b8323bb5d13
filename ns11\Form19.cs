﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns11
{
	// Token: 0x020001FF RID: 511
	internal sealed partial class Form19 : Form
	{
		// Token: 0x0600149B RID: 5275 RVA: 0x000083E3 File Offset: 0x000065E3
		public Form19()
		{
			this.method_0();
			base.Load += this.Form19_Load;
		}

		// Token: 0x0600149C RID: 5276 RVA: 0x0008AD68 File Offset: 0x00088F68
		private void Form19_Load(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfROpenFixedAmt)
			{
				this.radioButton_0.Checked = true;
				this.numericUpDown_0.Enabled = false;
			}
			else
			{
				this.radioButton_1.Checked = true;
				this.textBox_0.Enabled = false;
			}
			if (Base.UI.Form.ROpenRatio != null)
			{
				this.numericUpDown_0.Value = Base.UI.Form.ROpenRatio.Value;
			}
			else
			{
				this.numericUpDown_0.Value = 3m;
			}
			if (Base.UI.Form.ROpenFixedAmt != null)
			{
				this.textBox_0.Text = Base.UI.Form.ROpenFixedAmt.Value.ToString();
			}
			else
			{
				this.textBox_0.Text = Base.Trading.smethod_213().ToString();
			}
			if (Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.checkBox_0.Checked = true;
			}
			this.textBox_0.KeyPress += this.textBox_0_KeyPress;
			this.radioButton_1.CheckedChanged += this.radioButton_1_CheckedChanged;
			this.button_1.Click += this.button_1_Click;
		}

		// Token: 0x0600149D RID: 5277 RVA: 0x0008AEAC File Offset: 0x000890AC
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.radioButton_0.Checked)
			{
				Base.UI.Form.IfROpenFixedAmt = true;
				bool flag = false;
				string value = this.textBox_0.Text.Trim();
				try
				{
					if (string.IsNullOrEmpty(value))
					{
						flag = true;
					}
					else if (Convert.ToInt32(value) < 1)
					{
						flag = true;
					}
				}
				catch
				{
					flag = true;
				}
				if (flag)
				{
					MessageBox.Show(Class521.smethod_0(38115), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.textBox_0.Focus();
					return;
				}
				Base.UI.Form.ROpenFixedAmt = new int?(Convert.ToInt32(value));
			}
			else
			{
				Base.UI.Form.IfROpenFixedAmt = false;
				Base.UI.Form.ROpenRatio = new decimal?(this.numericUpDown_0.Value);
			}
			Base.UI.Form.IfROpenNoShowCnfmDlg = this.checkBox_0.Checked;
			base.DialogResult = DialogResult.OK;
			base.Close();
		}

		// Token: 0x0600149E RID: 5278 RVA: 0x00007104 File Offset: 0x00005304
		private void textBox_0_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600149F RID: 5279 RVA: 0x0008AFA4 File Offset: 0x000891A4
		private void radioButton_1_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_1.Checked)
			{
				this.numericUpDown_0.Enabled = true;
				this.textBox_0.Enabled = false;
			}
			else
			{
				this.numericUpDown_0.Enabled = false;
				this.textBox_0.Enabled = true;
			}
		}

		// Token: 0x060014A0 RID: 5280 RVA: 0x00008405 File Offset: 0x00006605
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060014A1 RID: 5281 RVA: 0x0008AFF4 File Offset: 0x000891F4
		private void method_0()
		{
			this.groupBox_0 = new GroupBox();
			this.label_0 = new Label();
			this.textBox_0 = new TextBox();
			this.label_1 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.checkBox_0 = new CheckBox();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.groupBox_0.SuspendLayout();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			base.SuspendLayout();
			this.groupBox_0.Controls.Add(this.label_0);
			this.groupBox_0.Controls.Add(this.textBox_0);
			this.groupBox_0.Controls.Add(this.label_1);
			this.groupBox_0.Controls.Add(this.numericUpDown_0);
			this.groupBox_0.Controls.Add(this.radioButton_0);
			this.groupBox_0.Controls.Add(this.radioButton_1);
			this.groupBox_0.Location = new Point(47, 22);
			this.groupBox_0.Name = Class521.smethod_0(41410);
			this.groupBox_0.Size = new Size(277, 100);
			this.groupBox_0.TabIndex = 5;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(50327);
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(243, 63);
			this.label_0.Name = Class521.smethod_0(5915);
			this.label_0.Size = new Size(22, 15);
			this.label_0.TabIndex = 20;
			this.label_0.Text = Class521.smethod_0(3554);
			this.textBox_0.Location = new Point(170, 58);
			this.textBox_0.Name = Class521.smethod_0(41440);
			this.textBox_0.Size = new Size(69, 25);
			this.textBox_0.TabIndex = 19;
			this.textBox_0.Text = Class521.smethod_0(41465);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(246, 32);
			this.label_1.Name = Class521.smethod_0(41474);
			this.label_1.Size = new Size(15, 15);
			this.label_1.TabIndex = 18;
			this.label_1.Text = Class521.smethod_0(5356);
			this.numericUpDown_0.DecimalPlaces = 1;
			this.numericUpDown_0.Increment = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numericUpDown_0.Location = new Point(170, 28);
			this.numericUpDown_0.Minimum = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numericUpDown_0.Name = Class521.smethod_0(41495);
			this.numericUpDown_0.Size = new Size(69, 25);
			this.numericUpDown_0.TabIndex = 17;
			this.numericUpDown_0.Value = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(26, 149);
			this.checkBox_0.Name = Class521.smethod_0(41524);
			this.checkBox_0.Size = new Size(119, 19);
			this.checkBox_0.TabIndex = 16;
			this.checkBox_0.Text = Class521.smethod_0(50340);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Location = new Point(20, 60);
			this.radioButton_0.Name = Class521.smethod_0(41598);
			this.radioButton_0.Size = new Size(148, 19);
			this.radioButton_0.TabIndex = 1;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(41627);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(20, 30);
			this.radioButton_1.Name = Class521.smethod_0(41660);
			this.radioButton_1.Size = new Size(148, 19);
			this.radioButton_1.TabIndex = 0;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(41689);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(256, 142);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(92, 30);
			this.button_0.TabIndex = 18;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(154, 142);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(92, 30);
			this.button_1.TabIndex = 17;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(371, 180);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.checkBox_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(50365);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(23849);
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000AA7 RID: 2727
		private IContainer icontainer_0;

		// Token: 0x04000AA8 RID: 2728
		private GroupBox groupBox_0;

		// Token: 0x04000AA9 RID: 2729
		private Label label_0;

		// Token: 0x04000AAA RID: 2730
		private TextBox textBox_0;

		// Token: 0x04000AAB RID: 2731
		private Label label_1;

		// Token: 0x04000AAC RID: 2732
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000AAD RID: 2733
		private CheckBox checkBox_0;

		// Token: 0x04000AAE RID: 2734
		private RadioButton radioButton_0;

		// Token: 0x04000AAF RID: 2735
		private RadioButton radioButton_1;

		// Token: 0x04000AB0 RID: 2736
		private Button button_0;

		// Token: 0x04000AB1 RID: 2737
		private Button button_1;
	}
}
