﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns12;
using ns16;
using ns18;
using ns5;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x0200033A RID: 826
	public sealed class UserDefineInd : SIndicatorBase
	{
		// Token: 0x060022CE RID: 8910 RVA: 0x0000DC8E File Offset: 0x0000BE8E
		public UserDefineInd(DataProvider dp, UserDefineIndScript uds) : base(dp)
		{
			this.userDefineIndScript_0 = uds;
		}

		// Token: 0x060022CF RID: 8911 RVA: 0x0000DCA0 File Offset: 0x0000BEA0
		public UserDefineInd(DataProvider dp) : base(dp)
		{
			this.userDefineIndScript_0 = new UserDefineIndScript();
		}

		// Token: 0x060022D0 RID: 8912 RVA: 0x0000DCB6 File Offset: 0x0000BEB6
		public void method_0(UserDefineIndScript userDefineIndScript_1)
		{
			this.UDS = userDefineIndScript_1;
		}

		// Token: 0x060022D1 RID: 8913 RVA: 0x000F5724 File Offset: 0x000F3924
		public bool method_1(DataProvider dataProvider_0)
		{
			base.DataProvider = dataProvider_0;
			return this.method_2();
		}

		// Token: 0x060022D2 RID: 8914 RVA: 0x000F5744 File Offset: 0x000F3944
		protected bool method_2()
		{
			this.list_0 = new List<DataArray>();
			DataArray[] array = this.method_3();
			bool result;
			if (array != null)
			{
				this.list_0 = array.ToList<DataArray>();
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060022D3 RID: 8915 RVA: 0x000F577C File Offset: 0x000F397C
		private static void smethod_0(ref SortedList<DateTime, HisData> sortedList_0)
		{
			for (int i = 0; i < 10; i++)
			{
				DateTime key = DateTime.Now.AddDays((double)i);
				HisData hisData = new HisData();
				hisData.Close = (double)(i + 3);
				hisData.Open = (double)(i + 2);
				hisData.High = (double)(i + 5);
				hisData.Low = (double)(i + 1);
				sortedList_0.Add(key, hisData);
			}
		}

		// Token: 0x060022D4 RID: 8916 RVA: 0x000F57E0 File Offset: 0x000F39E0
		public static bool smethod_1(UserDefineIndScript userDefineIndScript_1, TradingSymbol tradingSymbol_0)
		{
			bool result;
			try
			{
				SortedList<DateTime, HisData> datalist = new SortedList<DateTime, HisData>();
				UserDefineInd.smethod_0(ref datalist);
				result = (new UserDefineInd(new DataProvider(datalist, tradingSymbol_0), userDefineIndScript_1).method_3() != null);
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, Class521.smethod_0(104036), MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = false;
			}
			return result;
		}

		// Token: 0x060022D5 RID: 8917 RVA: 0x000F5844 File Offset: 0x000F3A44
		public DataArray[] method_3()
		{
			DataArray[] result;
			try
			{
				result = this.method_4();
			}
			catch (Exception ex)
			{
				string text = Class521.smethod_0(104053);
				if (!ex.Message.StartsWith(Class521.smethod_0(104082)))
				{
					text = ex.Message;
				}
				if (ex.InnerException != null)
				{
					text += ex.InnerException.Message;
				}
				MessageBox.Show(text, Class521.smethod_0(104103), MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = null;
			}
			return result;
		}

		// Token: 0x060022D6 RID: 8918 RVA: 0x000F58CC File Offset: 0x000F3ACC
		private DataArray[] method_4()
		{
			this.parserEnvironment_0 = new ParserEnvironment(this.UDS.UserDefineParams, this);
			Tokenes tokenes = new Class440(this.parserEnvironment_0).method_1(this.CodeText);
			double num = ParserEnvironment.smethod_1(tokenes.method_0());
			this.UDS.Ver = num;
			if (num > TApp.double_0)
			{
				throw new Exception(string.Concat(new object[]
				{
					Class521.smethod_0(104120),
					this.Name,
					Class521.smethod_0(104129),
					num,
					Class521.smethod_0(104146)
				}));
			}
			this.UDS.Ver = num;
			Class444 @class = new Class444(this.parserEnvironment_0);
			this.class411_0 = @class.method_0(tokenes);
			object obj = this.class411_0.vmethod_1(this.parserEnvironment_0);
			this.list_0 = (List<DataArray>)obj;
			this.HasLast = this.list_0.Any(new Func<DataArray, bool>(UserDefineInd.<>c.<>9.method_0));
			return this.list_0.ToArray();
		}

		// Token: 0x060022D7 RID: 8919 RVA: 0x000F59F8 File Offset: 0x000F3BF8
		public DataArray[] method_5()
		{
			DataArray[] result;
			try
			{
				object obj = this.class411_0.vmethod_1(this.parserEnvironment_0);
				this.list_0 = (List<DataArray>)obj;
				result = this.list_0.ToArray();
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = null;
			}
			return result;
		}

		// Token: 0x170005FA RID: 1530
		// (get) Token: 0x060022D8 RID: 8920 RVA: 0x000F5A64 File Offset: 0x000F3C64
		// (set) Token: 0x060022D9 RID: 8921 RVA: 0x0000DCC1 File Offset: 0x0000BEC1
		public string CodeText
		{
			get
			{
				return this.userDefineIndScript_0.Code;
			}
			set
			{
				this.userDefineIndScript_0.Code = value;
			}
		}

		// Token: 0x170005FB RID: 1531
		// (get) Token: 0x060022DA RID: 8922 RVA: 0x000F5A80 File Offset: 0x000F3C80
		// (set) Token: 0x060022DB RID: 8923 RVA: 0x0000DCD1 File Offset: 0x0000BED1
		public bool HasLast { get; private set; }

		// Token: 0x170005FC RID: 1532
		// (get) Token: 0x060022DC RID: 8924 RVA: 0x000F5A98 File Offset: 0x000F3C98
		public List<DataArray> DataList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x170005FD RID: 1533
		// (get) Token: 0x060022DD RID: 8925 RVA: 0x000F5AB0 File Offset: 0x000F3CB0
		// (set) Token: 0x060022DE RID: 8926 RVA: 0x0000DCDC File Offset: 0x0000BEDC
		public UserDefineIndScript UDS
		{
			get
			{
				return this.userDefineIndScript_0;
			}
			private set
			{
				this.userDefineIndScript_0 = value;
			}
		}

		// Token: 0x170005FE RID: 1534
		// (get) Token: 0x060022DF RID: 8927 RVA: 0x000F5AC8 File Offset: 0x000F3CC8
		public string Name
		{
			get
			{
				return this.userDefineIndScript_0.Name;
			}
		}

		// Token: 0x170005FF RID: 1535
		// (get) Token: 0x060022E0 RID: 8928 RVA: 0x000F5AE4 File Offset: 0x000F3CE4
		// (set) Token: 0x060022E1 RID: 8929 RVA: 0x0000DCE7 File Offset: 0x0000BEE7
		public Class411 ProgrameTree
		{
			get
			{
				return this.class411_0;
			}
			set
			{
				this.class411_0 = value;
			}
		}

		// Token: 0x17000600 RID: 1536
		// (get) Token: 0x060022E2 RID: 8930 RVA: 0x000F5AFC File Offset: 0x000F3CFC
		// (set) Token: 0x060022E3 RID: 8931 RVA: 0x0000DCF2 File Offset: 0x0000BEF2
		public ParserEnvironment PE
		{
			get
			{
				return this.parserEnvironment_0;
			}
			set
			{
				this.parserEnvironment_0 = value;
			}
		}

		// Token: 0x040010E3 RID: 4323
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040010E4 RID: 4324
		private List<DataArray> list_0;

		// Token: 0x040010E5 RID: 4325
		private UserDefineIndScript userDefineIndScript_0;

		// Token: 0x040010E6 RID: 4326
		private Class411 class411_0;

		// Token: 0x040010E7 RID: 4327
		private ParserEnvironment parserEnvironment_0;
	}
}
