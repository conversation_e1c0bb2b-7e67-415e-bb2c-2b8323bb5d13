﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006F RID: 111
	[Serializable]
	internal sealed class DrawGannLines : DrawObj, ISerializable
	{
		// Token: 0x06000407 RID: 1031 RVA: 0x00003742 File Offset: 0x00001942
		public DrawGannLines()
		{
		}

		// Token: 0x06000408 RID: 1032 RVA: 0x00003B5D File Offset: 0x00001D5D
		public DrawGannLines(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5200);
			base.CanChgColor = true;
			base.IsOneClickLoc = true;
		}

		// Token: 0x06000409 RID: 1033 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawGannLines(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600040A RID: 1034 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600040B RID: 1035 RVA: 0x00022E58 File Offset: 0x00021058
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_1, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			TextObj textObj = base.method_27(chartCS_1, double_1, min, Class521.smethod_0(4933), null, string_5);
			textObj.Location.AlignV = AlignV.Bottom;
			textObj.Location.AlignH = AlignH.Left;
			list.Add(textObj);
			foreach (double num in new List<double>(new double[]
			{
				5.0,
				9.0,
				11.0,
				14.0,
				17.0,
				21.0,
				23.0,
				25.0,
				32.0,
				37.0,
				41.0,
				45.0,
				50.0,
				57.0,
				59.0,
				61.0,
				65.0,
				68.0,
				71.0,
				73.0,
				77.0,
				81.0
			}))
			{
				double num2 = double_1 - 1.0 + num;
				if (num2 >= max || num2 <= 0.0)
				{
					break;
				}
				LineObj item2 = base.method_22(chartCS_1, num2, string_5);
				list.Add(item2);
				TextObj textObj2 = base.method_27(chartCS_1, num2, min, num.ToString(), null, string_5);
				textObj2.Location.AlignV = AlignV.Bottom;
				textObj2.Location.AlignH = AlignH.Left;
				list.Add(textObj2);
			}
			return list;
		}

		// Token: 0x0600040C RID: 1036 RVA: 0x00022FB0 File Offset: 0x000211B0
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				5.0,
				9.0,
				11.0,
				14.0,
				17.0,
				21.0,
				23.0,
				25.0,
				32.0,
				37.0,
				41.0,
				45.0,
				50.0,
				57.0,
				59.0,
				61.0,
				65.0,
				68.0,
				71.0,
				73.0,
				77.0,
				81.0
			});
			return base.method_28(list_, 1.0, 5000.0, 0);
		}
	}
}
