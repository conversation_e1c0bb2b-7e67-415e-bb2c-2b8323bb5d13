﻿using System;
using System.Runtime.CompilerServices;
using ns15;
using ns18;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000227 RID: 551
	public sealed class FilterCond
	{
		// Token: 0x060016DB RID: 5851 RVA: 0x000095E7 File Offset: 0x000077E7
		public FilterCond(string tblKey, string name, ValueUnitType type = ValueUnitType.None, ComparisonOpt opt = ComparisonOpt.Bigger, string hint = null, double? val = null)
		{
			this.TableKey = tblKey;
			this.Name = name;
			this.UnitType = type;
			this.Opt = opt;
			this.Hint = hint;
			this.Value = val;
		}

		// Token: 0x170003B3 RID: 947
		// (get) Token: 0x060016DC RID: 5852 RVA: 0x0009FD34 File Offset: 0x0009DF34
		// (set) Token: 0x060016DD RID: 5853 RVA: 0x0000961E File Offset: 0x0000781E
		public string TableKey { get; set; }

		// Token: 0x170003B4 RID: 948
		// (get) Token: 0x060016DE RID: 5854 RVA: 0x0009FD4C File Offset: 0x0009DF4C
		// (set) Token: 0x060016DF RID: 5855 RVA: 0x00009629 File Offset: 0x00007829
		public string Name { get; set; }

		// Token: 0x170003B5 RID: 949
		// (get) Token: 0x060016E0 RID: 5856 RVA: 0x0009FD64 File Offset: 0x0009DF64
		// (set) Token: 0x060016E1 RID: 5857 RVA: 0x00009634 File Offset: 0x00007834
		public ValueUnitType UnitType { get; set; }

		// Token: 0x170003B6 RID: 950
		// (get) Token: 0x060016E2 RID: 5858 RVA: 0x0009FD7C File Offset: 0x0009DF7C
		// (set) Token: 0x060016E3 RID: 5859 RVA: 0x0000963F File Offset: 0x0000783F
		public ComparisonOpt Opt { get; set; }

		// Token: 0x170003B7 RID: 951
		// (get) Token: 0x060016E4 RID: 5860 RVA: 0x0009FD94 File Offset: 0x0009DF94
		// (set) Token: 0x060016E5 RID: 5861 RVA: 0x0000964A File Offset: 0x0000784A
		public string Hint { get; set; }

		// Token: 0x170003B8 RID: 952
		// (get) Token: 0x060016E6 RID: 5862 RVA: 0x0009FDAC File Offset: 0x0009DFAC
		// (set) Token: 0x060016E7 RID: 5863 RVA: 0x00009655 File Offset: 0x00007855
		public double? Value { get; set; }

		// Token: 0x060016E8 RID: 5864 RVA: 0x0009FDC4 File Offset: 0x0009DFC4
		public string method_0(DateTime dateTime_0)
		{
			string result = null;
			if (this.Value != null)
			{
				string text = Base.Data.smethod_130(this.Opt);
				double num = this.Value.Value;
				if (this.UnitType == ValueUnitType.Yi)
				{
					num *= 100000000.0;
				}
				else if (this.UnitType == ValueUnitType.Wan)
				{
					num *= 10000.0;
				}
				string text2 = this.method_1(dateTime_0, Class521.smethod_0(1449));
				result = string.Concat(new string[]
				{
					this.TableKey,
					Class521.smethod_0(4736),
					text,
					Class521.smethod_0(4736),
					num.ToString(),
					Class521.smethod_0(4736),
					text2
				});
			}
			return result;
		}

		// Token: 0x060016E9 RID: 5865 RVA: 0x0009FE98 File Offset: 0x0009E098
		private string method_1(DateTime dateTime_0, string string_3 = "")
		{
			bool flag = false;
			if (this.TableKey.StartsWith(Class521.smethod_0(59187)))
			{
				flag = true;
			}
			DateTime dateTime;
			if (flag)
			{
				dateTime = Base.Trading.smethod_22(dateTime_0);
			}
			else
			{
				dateTime = Utility.GetQuarterEndDate(dateTime_0, -1);
			}
			return dateTime.ToString(string.Concat(new string[]
			{
				Class521.smethod_0(54946),
				string_3,
				Class521.smethod_0(59204),
				string_3,
				Class521.smethod_0(59209)
			}));
		}

		// Token: 0x04000BAB RID: 2987
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000BAC RID: 2988
		[CompilerGenerated]
		private string string_1;

		// Token: 0x04000BAD RID: 2989
		[CompilerGenerated]
		private Enum20 enum20_0;

		// Token: 0x04000BAE RID: 2990
		[CompilerGenerated]
		private ComparisonOpt comparisonOpt_0;

		// Token: 0x04000BAF RID: 2991
		[CompilerGenerated]
		private string string_2;

		// Token: 0x04000BB0 RID: 2992
		[CompilerGenerated]
		private double? nullable_0;
	}
}
