﻿using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns19;
using ns25;
using ns26;
using ns31;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200020F RID: 527
	internal sealed class DataGridViewHisTrans : Class292
	{
		// Token: 0x1400007F RID: 127
		// (add) Token: 0x06001592 RID: 5522 RVA: 0x0009309C File Offset: 0x0009129C
		// (remove) Token: 0x06001593 RID: 5523 RVA: 0x000930D4 File Offset: 0x000912D4
		public event Delegate22 ChangeToHisTransDTRequested
		{
			[CompilerGenerated]
			add
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001594 RID: 5524 RVA: 0x0009310C File Offset: 0x0009130C
		protected void method_5(ShownHisTrans shownHisTrans_0)
		{
			EventArgs16 e = new EventArgs16(shownHisTrans_0);
			Delegate22 @delegate = this.delegate22_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x06001595 RID: 5525 RVA: 0x00008A5A File Offset: 0x00006C5A
		public DataGridViewHisTrans()
		{
			base.MouseDoubleClick += this.DataGridViewHisTrans_MouseDoubleClick;
			base.Resize += this.DataGridViewHisTrans_Resize;
		}

		// Token: 0x06001596 RID: 5526 RVA: 0x00008A88 File Offset: 0x00006C88
		public DataGridViewHisTrans(SortableBindingList<ShownHisTrans> hisLst) : this()
		{
			this.sortableBindingList_0 = hisLst;
		}

		// Token: 0x06001597 RID: 5527 RVA: 0x00008A99 File Offset: 0x00006C99
		protected override void vmethod_1()
		{
			this.sortableBindingList_0 = Base.Trading.CurrHisTransList;
			base.DataSource = this.sortableBindingList_0;
			this.method_12();
		}

		// Token: 0x06001598 RID: 5528 RVA: 0x00008ABA File Offset: 0x00006CBA
		public void method_6()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x06001599 RID: 5529 RVA: 0x00008ACB File Offset: 0x00006CCB
		public void method_7(SortableBindingList<ShownHisTrans> sortableBindingList_1)
		{
			this.sortableBindingList_0 = sortableBindingList_1;
			this.method_6();
		}

		// Token: 0x0600159A RID: 5530 RVA: 0x00093134 File Offset: 0x00091334
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Name = Class521.smethod_0(52059);
			toolStripMenuItem.Text = Class521.smethod_0(52076);
			toolStripMenuItem.Click += this.method_8;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Opening += this.method_9;
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x0600159B RID: 5531 RVA: 0x000931B0 File Offset: 0x000913B0
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			base.vmethod_3(dataGridViewCellFormattingEventArgs_0);
			string dataPropertyName = base.Columns[dataGridViewCellFormattingEventArgs_0.ColumnIndex].DataPropertyName;
			ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
			if (dataPropertyName == Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})))
			{
				DataGridViewCell dataGridViewCell_ = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex].Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
				base.method_3(dataGridViewCell_);
			}
		}

		// Token: 0x0600159C RID: 5532 RVA: 0x00008ADC File Offset: 0x00006CDC
		private void method_8(object sender, EventArgs e)
		{
			this.method_10(base.DataSource as SortableBindingList<ShownHisTrans>);
		}

		// Token: 0x0600159D RID: 5533 RVA: 0x00093260 File Offset: 0x00091460
		private void method_9(object sender, CancelEventArgs e)
		{
			if (base.DataSource != null && (base.DataSource as SortableBindingList<ShownHisTrans>).Count >= 1)
			{
				this.ContextMenuStrip.Items[0].Enabled = true;
			}
			else
			{
				this.ContextMenuStrip.Items[0].Enabled = false;
			}
		}

		// Token: 0x0600159E RID: 5534 RVA: 0x000932BC File Offset: 0x000914BC
		public void method_10(SortableBindingList<ShownHisTrans> sortableBindingList_1)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = Class521.smethod_0(52102);
			saveFileDialog.Filter = Class521.smethod_0(52127);
			saveFileDialog.FileName = Class521.smethod_0(52148);
			saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			try
			{
				if (saveFileDialog.ShowDialog() == DialogResult.OK)
				{
					string fileName = saveFileDialog.FileName;
					this.method_11(sortableBindingList_1, saveFileDialog.FileName);
					MessageBox.Show(Class521.smethod_0(52165), Class521.smethod_0(19216), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x0600159F RID: 5535 RVA: 0x00093360 File Offset: 0x00091560
		private void method_11(SortableBindingList<ShownHisTrans> sortableBindingList_1, string string_0)
		{
			try
			{
				using (StreamWriter streamWriter = new StreamWriter(string_0, false, Encoding.GetEncoding(Class521.smethod_0(17987))))
				{
					streamWriter.WriteLine(Class521.smethod_0(52198));
					foreach (ShownHisTrans shownHisTrans in sortableBindingList_1)
					{
						string value = string.Concat(new object[]
						{
							shownHisTrans.SymblCode,
							Class521.smethod_0(4736),
							shownHisTrans.TransTypeDesc.Substring(0, 1),
							Class521.smethod_0(4736),
							shownHisTrans.TransTypeDesc.Substring(1, 1),
							Class521.smethod_0(4736),
							shownHisTrans.CreateTime,
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(new decimal?(shownHisTrans.Price)),
							Class521.smethod_0(4736),
							shownHisTrans.Units,
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(shownHisTrans.Profit),
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(shownHisTrans.Fee),
							Class521.smethod_0(4736),
							shownHisTrans.Notes
						});
						streamWriter.WriteLine(value);
					}
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x060015A0 RID: 5536 RVA: 0x00093524 File Offset: 0x00091724
		private void DataGridViewHisTrans_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			int columnIndex = dataGridView.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = dataGridView.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex >= 0 && rowIndex >= 0)
			{
				ShownHisTrans shownHisTrans = dataGridView.Rows[rowIndex].DataBoundItem as ShownHisTrans;
				if (Base.Data.UsrStkSymbols.ContainsKey(shownHisTrans.SymbolID))
				{
					if ((shownHisTrans.SymbolID != Base.Data.CurrSelectedSymbol.ID || !(shownHisTrans.CreateTime == Base.Data.CurrDate)) && !Base.UI.Form.IsInBlindTestMode && MessageBox.Show(Class521.smethod_0(52295), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_5(shownHisTrans);
					}
				}
				else
				{
					MessageBox.Show(Class521.smethod_0(52364), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060015A1 RID: 5537 RVA: 0x00093618 File Offset: 0x00091818
		private void method_12()
		{
			if (base.Columns.Count > 0)
			{
				DataGridViewColumnCollection columns = base.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].HeaderText = Class521.smethod_0(52457);
				DataGridViewColumnCollection columns2 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns2[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_OpenUnits())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Visible = false;
				DataGridViewColumnCollection columns3 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns3[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].HeaderText = Class521.smethod_0(52474);
				DataGridViewColumnCollection columns4 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns4[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						DataGridViewColumnCollection columns5 = base.Columns;
						parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
						columns5[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_SymblCode())), new ParameterExpression[]
						{
							parameterExpression
						}))].Visible = false;
					}
					DataGridViewColumnCollection columns6 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns6[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = false;
				}
				else
				{
					DataGridViewColumnCollection columns7 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns7[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_SymblCode())), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = true;
					DataGridViewColumnCollection columns8 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns8[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = true;
				}
				this.Refresh();
			}
		}

		// Token: 0x060015A2 RID: 5538 RVA: 0x000041B9 File Offset: 0x000023B9
		private void DataGridViewHisTrans_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015A3 RID: 5539 RVA: 0x0009396C File Offset: 0x00091B6C
		private void method_13(DataGridView dataGridView_0)
		{
			int num = 0;
			VScrollBar vscrollBar = dataGridView_0.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = dataGridView_0.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 669m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					DataGridViewColumnCollection columns = dataGridView_0.Columns;
					ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_SymblCode())), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
					DataGridViewColumnCollection columns2 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns2[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_TransTypeDesc())), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(44 * num2 / d)) : 44);
					DataGridViewColumnCollection columns3 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns3[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Units())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					DataGridViewColumnCollection columns4 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns4[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					DataGridViewColumnCollection columns5 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns5[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Fee())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					DataGridViewColumnCollection columns6 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns6[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
					DataGridViewColumnCollection columns7 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns7[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(120 * num2 / d)) : 120);
					DataGridViewColumnCollection columns8 = dataGridView_0.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
					columns8[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(Transaction.get_Notes())), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(140 * num2 / d)) : 140);
					return;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 489m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 609m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				DataGridViewColumnCollection columns9 = dataGridView_0.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns9[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_SymblCode())), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
			}
			try
			{
				DataGridViewColumnCollection columns10 = dataGridView_0.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns10[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(ShownHisTrans.get_TransTypeDesc())), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(44 * num2 / d)) : 44);
				DataGridViewColumnCollection columns11 = dataGridView_0.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns11[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Units())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				DataGridViewColumnCollection columns12 = dataGridView_0.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns12[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				DataGridViewColumnCollection columns13 = dataGridView_0.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns13[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Fee())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				DataGridViewColumnCollection columns14 = dataGridView_0.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns14[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
				DataGridViewColumnCollection columns15 = dataGridView_0.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownHisTrans), Class521.smethod_0(52097));
				columns15[Utility.GetPropertyName<ShownHisTrans>(Expression.Lambda<Func<ShownHisTrans, object>>(Expression.Property(parameterExpression, methodof(Transaction.get_Notes())), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(140 * num2 / d)) : 140);
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
		}

		// Token: 0x04000B0E RID: 2830
		private SortableBindingList<ShownHisTrans> sortableBindingList_0;

		// Token: 0x04000B0F RID: 2831
		[CompilerGenerated]
		private Delegate22 delegate22_0;
	}
}
