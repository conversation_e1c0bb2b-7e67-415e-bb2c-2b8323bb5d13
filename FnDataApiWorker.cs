﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Timers;
using ns0;
using ns18;
using ns20;
using ns26;
using ns3;
using ns4;
using ns5;
using ns8;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000024 RID: 36
	internal sealed class FnDataApiWorker : Class10
	{
		// Token: 0x060000C9 RID: 201 RVA: 0x00002D92 File Offset: 0x00000F92
		public FnDataApiWorker()
		{
			this.dictionary_0 = new Dictionary<string, string>();
			this.keyValuePair_0 = default(KeyValuePair<string, DateTime>);
			base.ResultCompressed = true;
		}

		// Token: 0x060000CA RID: 202 RVA: 0x00013188 File Offset: 0x00011388
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && reqDict.ContainsKey(Class521.smethod_0(1594)))
			{
				DataGridViewMkt dataGridViewMkt = reqDict[Class521.smethod_0(1594)] as DataGridViewMkt;
				string string_ = rslt.data as string;
				if (dataGridViewMkt.MktDgvType == Enum1.const_0)
				{
					this.method_6(string_, reqDict);
				}
				else if (dataGridViewMkt.MktDgvType == Enum1.const_4)
				{
					this.method_8(string_, reqDict);
				}
			}
		}

		// Token: 0x060000CB RID: 203 RVA: 0x000131F4 File Offset: 0x000113F4
		public void method_1(DataGridViewMkt dataGridViewMkt_0, List<ShowMktSymb> list_0)
		{
			string text = this.method_3(dataGridViewMkt_0);
			if (!string.IsNullOrEmpty(text))
			{
				if (!this.method_5(dataGridViewMkt_0))
				{
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					dictionary[Class521.smethod_0(1603)] = text;
					if (dataGridViewMkt_0.MktDgvType == Enum1.const_0)
					{
						string text2 = Class521.smethod_0(1612);
						dictionary[Class521.smethod_0(1621)] = text2;
						if (dataGridViewMkt_0.SourceMktSymbLst != null)
						{
							string[] array = dataGridViewMkt_0.method_14();
							if (array != null)
							{
								Class0<string, string, int, Class3<string, string[], string>> value = new Class0<string, string, int, Class3<string, string[], string>>(Class521.smethod_0(1634), TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class3<string, string[], string>(text2, array, text));
								dictionary[Class521.smethod_0(1376)] = value;
							}
						}
						else if (dataGridViewMkt_0.IdxClassAry != null)
						{
							Class0<string, string, int, Class4<string, string[], string>> value2 = new Class0<string, string, int, Class4<string, string[], string>>(Class521.smethod_0(1647), TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class4<string, string[], string>(text2, dataGridViewMkt_0.IdxClassAry, text));
							dictionary[Class521.smethod_0(1376)] = value2;
							dictionary[Class521.smethod_0(1664)] = list_0;
						}
					}
					else if (dataGridViewMkt_0.MktDgvType == Enum1.const_4)
					{
						string[] array2 = dataGridViewMkt_0.method_14();
						if (array2 != null)
						{
							Class0<string, string, int, Class5<string[], string>> value3 = new Class0<string, string, int, Class5<string[], string>>(Class521.smethod_0(1685), TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class5<string[], string>(array2, text));
							dictionary[Class521.smethod_0(1376)] = value3;
						}
					}
					if (!dictionary.ContainsKey(Class521.smethod_0(1376)))
					{
						return;
					}
					dictionary[Class521.smethod_0(1594)] = dataGridViewMkt_0;
					try
					{
						base.GetHttpResponseInBackground(dictionary);
						this.dictionary_0[dataGridViewMkt_0.Name] = text;
						this.keyValuePair_0 = new KeyValuePair<string, DateTime>(dataGridViewMkt_0.Name, DateTime.Now);
						return;
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
						return;
					}
				}
				this.keyValuePair_1 = new KeyValuePair<DataGridViewMkt, List<ShowMktSymb>>(dataGridViewMkt_0, list_0);
				this.method_2();
			}
		}

		// Token: 0x060000CC RID: 204 RVA: 0x000133E4 File Offset: 0x000115E4
		private void method_2()
		{
			if (this.timer_0 == null)
			{
				this.timer_0 = new Timer(2000.0);
				this.timer_0.AutoReset = false;
				this.timer_0.Elapsed += this.timer_0_Elapsed;
			}
			if (!this.timer_0.Enabled)
			{
				this.timer_0.Start();
			}
		}

		// Token: 0x060000CD RID: 205 RVA: 0x00002DBA File Offset: 0x00000FBA
		private void timer_0_Elapsed(object sender, ElapsedEventArgs e)
		{
			this.method_1(this.keyValuePair_1.Key, this.keyValuePair_1.Value);
		}

		// Token: 0x060000CE RID: 206 RVA: 0x0001344C File Offset: 0x0001164C
		private string method_3(DataGridViewMkt dataGridViewMkt_0)
		{
			string text = null;
			if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrDate != null)
			{
				text = Base.Data.CurrSymbDataSet.CurrDate.Value.AddHours(4.0).ToString(Class521.smethod_0(1702));
				if (this.method_4(text, dataGridViewMkt_0))
				{
					return null;
				}
			}
			return text;
		}

		// Token: 0x060000CF RID: 207 RVA: 0x000134C0 File Offset: 0x000116C0
		private bool method_4(string string_0, DataGridViewMkt dataGridViewMkt_0)
		{
			if (this.dictionary_0.ContainsKey(dataGridViewMkt_0.Name))
			{
				string b = this.dictionary_0[dataGridViewMkt_0.Name];
				if (string_0 == b)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x060000D0 RID: 208 RVA: 0x00013504 File Offset: 0x00011704
		public bool method_5(DataGridViewMkt dataGridViewMkt_0)
		{
			bool result;
			if (this.keyValuePair_0.Key == dataGridViewMkt_0.Name && (DateTime.Now - this.keyValuePair_0.Value).TotalMilliseconds < 2000.0)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060000D1 RID: 209 RVA: 0x0001355C File Offset: 0x0001175C
		private void method_6(string string_0, Dictionary<string, object> dictionary_1)
		{
			if (string_0 != null)
			{
				dictionary_1[Class521.smethod_0(1621)] as string == Class521.smethod_0(1719);
				List<ShowMktSymb> list = this.method_7(string_0);
				if (list.Count > 0)
				{
					List<ShowMktSymb> list2 = null;
					DataGridViewMkt dataGridViewMkt = dictionary_1[Class521.smethod_0(1594)] as DataGridViewMkt;
					if (dataGridViewMkt.SourceMktSymbLst != null)
					{
						list2 = dataGridViewMkt.SourceMktSymbLst;
					}
					else if (dictionary_1.ContainsKey(Class521.smethod_0(1664)))
					{
						list2 = (dictionary_1[Class521.smethod_0(1664)] as List<ShowMktSymb>);
					}
					if (list2 != null)
					{
						SortableBindingList<ShowMktSymb> value = new SortableBindingList<ShowMktSymb>(list2.Join(list, new Func<ShowMktSymb, <>f__AnonymousType6<int, string>>(FnDataApiWorker.<>c.<>9.method_0), new Func<ShowMktSymb, <>f__AnonymousType6<int, string>>(FnDataApiWorker.<>c.<>9.method_1), new Func<ShowMktSymb, ShowMktSymb, ShowMktSymb>(FnDataApiWorker.<>c.<>9.method_2)).ToList<ShowMktSymb>());
						dictionary_1[Class521.smethod_0(1724)] = value;
					}
				}
			}
		}

		// Token: 0x060000D2 RID: 210 RVA: 0x00013684 File Offset: 0x00011884
		private List<ShowMktSymb> method_7(string string_0)
		{
			string[] array = string_0.Split(new string[]
			{
				Environment.NewLine
			}, StringSplitOptions.None);
			List<ShowMktSymb> list = new List<ShowMktSymb>();
			foreach (string text in array)
			{
				if (!string.IsNullOrEmpty(text))
				{
					string[] array3 = text.Split(new char[]
					{
						','
					});
					ShowMktSymb showMktSymb = new ShowMktSymb();
					string text2 = array3[0].Trim();
					int num = text2.IndexOf(Class521.smethod_0(1733));
					showMktSymb.StkCode = text2.Substring(0, num);
					showMktSymb.ExchgId = ShowMktSymb.smethod_0(text2.Substring(num + 1));
					showMktSymb.close = new double?(Convert.ToDouble(array3[1]));
					string value = array3[2];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.turnover_rate = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[3];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.turnover_rate_f = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[4];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.volume_ratio = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[5];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.pe = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[6];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.pe_ttm = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[7];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.pb = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[8];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.ps = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[9];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.dv_ratio = new double?(Math.Round(Convert.ToDouble(value), 2));
					}
					value = array3[10];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.total_share = new double?(Math.Round(Convert.ToDouble(value), 0));
					}
					value = array3[11];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.total_mv = new double?(Math.Round(Convert.ToDouble(value) / 10000.0, 2));
					}
					value = array3[12];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.float_share = new double?(Math.Round(Convert.ToDouble(value), 0));
					}
					value = array3[13];
					if (!string.IsNullOrEmpty(value))
					{
						showMktSymb.circ_mv = new double?(Math.Round(Convert.ToDouble(value) / 10000.0, 2));
					}
					list.Add(showMktSymb);
				}
			}
			return list;
		}

		// Token: 0x060000D3 RID: 211 RVA: 0x00013950 File Offset: 0x00011B50
		private void method_8(string string_0, Dictionary<string, object> dictionary_1)
		{
			if (string_0 != null)
			{
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				List<ShowMktSymb> list = new List<ShowMktSymb>();
				foreach (string text in array)
				{
					if (!string.IsNullOrEmpty(text))
					{
						string[] array3 = text.Split(new char[]
						{
							','
						});
						ShowMktSymb showMktSymb = new ShowMktSymb();
						string text2 = array3[0].Trim();
						int num = text2.IndexOf(Class521.smethod_0(1733));
						showMktSymb.StkCode = text2.Substring(0, num);
						showMktSymb.ExchgId = ShowMktSymb.smethod_0(text2.Substring(num + 1));
						string text3 = array3[4];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.cb_stk_code = base.method_0(text3);
						}
						text3 = array3[5];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.cb_stk_name = text3;
						}
						text3 = array3[6];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.maturity = new double?(Math.Round(Convert.ToDouble(text3), 0));
						}
						text3 = array3[7];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.par = new double?(Math.Round(Convert.ToDouble(text3), 0));
						}
						text3 = array3[9];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.issue_size = new double?(Math.Round(Convert.ToDouble(text3) / 10000.0, 0));
						}
						text3 = array3[11];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.value_date = text3;
						}
						text3 = array3[12];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.maturity_date = text3;
						}
						text3 = array3[17];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.list_date = text3;
						}
						text3 = array3[18];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.delist_date = text3;
						}
						text3 = array3[22];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.first_conv_price = new double?(Math.Round(Convert.ToDouble(text3), 2));
						}
						text3 = array3[23];
						if (!string.IsNullOrEmpty(text3))
						{
							showMktSymb.conv_price = new double?(Math.Round(Convert.ToDouble(text3), 2));
						}
						text3 = array3[26];
						if (!string.IsNullOrEmpty(text3))
						{
							try
							{
								showMktSymb.maturity_put_price = new double?(Math.Round(Convert.ToDouble(text3), 2));
								goto IL_232;
							}
							catch (Exception)
							{
								goto IL_232;
							}
							goto IL_229;
						}
						IL_232:
						list.Add(showMktSymb);
					}
					IL_229:;
				}
				if (list.Count > 0)
				{
					SortableBindingList<ShowMktSymb> value = new SortableBindingList<ShowMktSymb>((dictionary_1[Class521.smethod_0(1594)] as DataGridViewMkt).SourceMktSymbLst.Join(list, new Func<ShowMktSymb, <>f__AnonymousType6<int, string>>(FnDataApiWorker.<>c.<>9.method_3), new Func<ShowMktSymb, <>f__AnonymousType6<int, string>>(FnDataApiWorker.<>c.<>9.method_4), new Func<ShowMktSymb, ShowMktSymb, ShowMktSymb>(FnDataApiWorker.<>c.<>9.method_5)).ToList<ShowMktSymb>());
					dictionary_1[Class521.smethod_0(1724)] = value;
				}
			}
		}

		// Token: 0x1700003E RID: 62
		// (get) Token: 0x060000D4 RID: 212 RVA: 0x00013C54 File Offset: 0x00011E54
		// (set) Token: 0x060000D5 RID: 213 RVA: 0x00002DDA File Offset: 0x00000FDA
		public bool CachedReqTriggered { get; private set; }

		// Token: 0x0400004C RID: 76
		private const int int_1 = 2000;

		// Token: 0x0400004D RID: 77
		private Timer timer_0;

		// Token: 0x0400004E RID: 78
		private Dictionary<string, string> dictionary_0;

		// Token: 0x0400004F RID: 79
		private KeyValuePair<string, DateTime> keyValuePair_0;

		// Token: 0x04000050 RID: 80
		private KeyValuePair<DataGridViewMkt, List<ShowMktSymb>> keyValuePair_1;

		// Token: 0x04000051 RID: 81
		[CompilerGenerated]
		private bool bool_0;
	}
}
