﻿using System;

namespace ns11
{
	// Token: 0x020003FA RID: 1018
	internal sealed class EventArgs34 : EventArgs
	{
		// Token: 0x170006CF RID: 1743
		// (get) Token: 0x060027AB RID: 10155 RVA: 0x0000F41D File Offset: 0x0000D61D
		public Exception FatalException
		{
			get
			{
				return this.exception_0;
			}
		}

		// Token: 0x060027AC RID: 10156 RVA: 0x0000F425 File Offset: 0x0000D625
		internal EventArgs34(Exception exception_1)
		{
			this.exception_0 = exception_1;
		}

		// Token: 0x040013B9 RID: 5049
		private Exception exception_0;
	}
}
