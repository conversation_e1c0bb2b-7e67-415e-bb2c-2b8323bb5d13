﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx;

namespace ns1
{
	// Token: 0x02000229 RID: 553
	internal sealed class Class303
	{
		// Token: 0x170003BD RID: 957
		// (get) Token: 0x060016F8 RID: 5880 RVA: 0x000A00A0 File Offset: 0x0009E2A0
		// (set) Token: 0x060016F9 RID: 5881 RVA: 0x0000968C File Offset: 0x0000788C
		public string Name { get; set; }

		// Token: 0x170003BE RID: 958
		// (get) Token: 0x060016FA RID: 5882 RVA: 0x000A00B8 File Offset: 0x0009E2B8
		// (set) Token: 0x060016FB RID: 5883 RVA: 0x00009697 File Offset: 0x00007897
		public List<FilterCond> FilterConds { get; set; }

		// Token: 0x04000BB5 RID: 2997
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000BB6 RID: 2998
		[CompilerGenerated]
		private List<FilterCond> list_0;
	}
}
