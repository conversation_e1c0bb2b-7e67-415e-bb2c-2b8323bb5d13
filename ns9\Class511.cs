﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace ns9
{
	// Token: 0x020003B9 RID: 953
	[CompilerGenerated]
	internal sealed class Class511
	{
		// Token: 0x060026D0 RID: 9936 RVA: 0x00105F4C File Offset: 0x0010414C
		internal static uint smethod_0(string string_0)
		{
			uint num;
			if (string_0 != null)
			{
				num = 2166136261U;
				for (int i = 0; i < string_0.Length; i++)
				{
					num = ((uint)string_0[i] ^ num) * 16777619U;
				}
			}
			return num;
		}

		// Token: 0x040012B0 RID: 4784 RVA: 0x00002050 File Offset: 0x00000250
		internal static readonly Class511.Struct13 struct13_0;

		// Token: 0x040012B1 RID: 4785 RVA: 0x00002078 File Offset: 0x00000278
		internal static readonly Class511.Struct11 struct11_0;

		// Token: 0x040012B2 RID: 4786 RVA: 0x00002090 File Offset: 0x00000290
		internal static readonly Class511.Struct17 struct17_0;

		// Token: 0x040012B3 RID: 4787 RVA: 0x00002140 File Offset: 0x00000340
		internal static readonly Class511.Struct11 struct11_1;

		// Token: 0x040012B4 RID: 4788 RVA: 0x00002158 File Offset: 0x00000358
		internal static readonly Class511.Struct10 struct10_0;

		// Token: 0x040012B5 RID: 4789 RVA: 0x00002168 File Offset: 0x00000368
		internal static readonly Class511.Struct12 struct12_0;

		// Token: 0x040012B6 RID: 4790 RVA: 0x00002188 File Offset: 0x00000388
		internal static readonly Class511.Struct16 struct16_0;

		// Token: 0x040012B7 RID: 4791 RVA: 0x000021D8 File Offset: 0x000003D8
		internal static readonly Class511.Struct12 struct12_1;

		// Token: 0x040012B8 RID: 4792 RVA: 0x000021F8 File Offset: 0x000003F8
		internal static readonly Class511.Struct15 struct15_0;

		// Token: 0x040012B9 RID: 4793 RVA: 0x00002240 File Offset: 0x00000440
		internal static readonly Class511.Struct11 struct11_2;

		// Token: 0x040012BA RID: 4794 RVA: 0x00002258 File Offset: 0x00000458
		internal static readonly Class511.Struct14 struct14_0;

		// Token: 0x040012BB RID: 4795 RVA: 0x00002280 File Offset: 0x00000480
		internal static readonly Class511.Struct9 struct9_0;

		// Token: 0x040012BC RID: 4796 RVA: 0x00002288 File Offset: 0x00000488
		internal static readonly Class511.Struct12 struct12_2;

		// Token: 0x040012BD RID: 4797 RVA: 0x000022A8 File Offset: 0x000004A8
		internal static readonly Class511.Struct14 struct14_1;

		// Token: 0x020003BA RID: 954
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 6)]
		private struct Struct9
		{
		}

		// Token: 0x020003BB RID: 955
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 16)]
		private struct Struct10
		{
		}

		// Token: 0x020003BC RID: 956
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 24)]
		private struct Struct11
		{
		}

		// Token: 0x020003BD RID: 957
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 32)]
		private struct Struct12
		{
		}

		// Token: 0x020003BE RID: 958
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 34)]
		private struct Struct13
		{
		}

		// Token: 0x020003BF RID: 959
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 40)]
		private struct Struct14
		{
		}

		// Token: 0x020003C0 RID: 960
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 72)]
		private struct Struct15
		{
		}

		// Token: 0x020003C1 RID: 961
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 80)]
		private struct Struct16
		{
		}

		// Token: 0x020003C2 RID: 962
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 176)]
		private struct Struct17
		{
		}
	}
}
