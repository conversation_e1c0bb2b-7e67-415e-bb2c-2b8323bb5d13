﻿using System;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000076 RID: 118
	[Serializable]
	internal sealed class DrawLineHExt : DrawLineH, ISerializable
	{
		// Token: 0x0600043C RID: 1084 RVA: 0x00003D26 File Offset: 0x00001F26
		public DrawLineHExt()
		{
		}

		// Token: 0x0600043D RID: 1085 RVA: 0x00003D2E File Offset: 0x00001F2E
		public DrawLineHExt(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y1, false)
		{
		}

		// Token: 0x0600043E RID: 1086 RVA: 0x00003D3D File Offset: 0x00001F3D
		public DrawLineHExt(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc) : base(chart, x1, y1, x2, y1, disableRstPrc)
		{
			base.Name = Class521.smethod_0(5272);
		}

		// Token: 0x0600043F RID: 1087 RVA: 0x00003D5F File Offset: 0x00001F5F
		protected DrawLineHExt(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000440 RID: 1088 RVA: 0x00003D70 File Offset: 0x00001F70
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000441 RID: 1089 RVA: 0x000238F0 File Offset: 0x00021AF0
		protected override LineObj vmethod_24(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			return base.method_23(double_1, double_4, max, double_4, base.Tag);
		}
	}
}
