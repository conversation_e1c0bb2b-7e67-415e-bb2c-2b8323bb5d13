﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000065 RID: 101
	[Serializable]
	internal sealed class DrawDegree45Dn : DrawDegree45Up, ISerializable
	{
		// Token: 0x06000357 RID: 855 RVA: 0x000036FC File Offset: 0x000018FC
		public DrawDegree45Dn()
		{
		}

		// Token: 0x06000358 RID: 856 RVA: 0x00003704 File Offset: 0x00001904
		public DrawDegree45Dn(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5068);
		}

		// Token: 0x06000359 RID: 857 RVA: 0x00003725 File Offset: 0x00001925
		protected DrawDegree45Dn(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600035A RID: 858 RVA: 0x00003736 File Offset: 0x00001936
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600035B RID: 859 RVA: 0x00020288 File Offset: 0x0001E488
		protected override List<LineObj> vmethod_24(ChartCS chartCS_1, double double_1, double double_2, string string_5)
		{
			return base.method_39(chartCS_1, double_1, double_2, string_5, false);
		}
	}
}
