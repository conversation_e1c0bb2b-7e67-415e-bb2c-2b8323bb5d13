﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx;
using TEx.Comn;

namespace ns9
{
	// Token: 0x0200028B RID: 651
	internal sealed class Class339
	{
		// Token: 0x06001D15 RID: 7445 RVA: 0x000CDE98 File Offset: 0x000CC098
		public static int smethod_0(SortedList<DateTime, HisData> sortedList_0, DateTime dateTime_0)
		{
			int result = -1;
			if (sortedList_0.Count > 0)
			{
				try
				{
					result = sortedList_0.IndexOfKey(dateTime_0);
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x06001D16 RID: 7446 RVA: 0x000CDED4 File Offset: 0x000CC0D4
		public static HisData smethod_1(StkSymbol stkSymbol_0, DateTime dateTime_0)
		{
			SymbDataSet symbDataSet = Base.Data.smethod_48(stkSymbol_0);
			HisData result;
			if (symbDataSet != null && symbDataSet.CurrHisDataSet != null)
			{
				SortedList<DateTime, HisData> sortedList_;
				if ((Base.UI.Form.IsSpanMoveNext || Base.UI.Form.IsSpanMovePrev) && Base.UI.Form.SpanMoveChtCtrl != null && Base.UI.Form.SpanMoveChtCtrl.HisDataPeriodSet.IsPeriodLong && symbDataSet.Curr1hPeriodHisData != null)
				{
					sortedList_ = symbDataSet.Curr1hPeriodHisData.PeriodHisDataList;
				}
				else
				{
					sortedList_ = symbDataSet.CurrHisDataSet.FetchedHisDataList;
				}
				result = Class339.smethod_2(sortedList_, dateTime_0);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001D17 RID: 7447 RVA: 0x000CDF60 File Offset: 0x000CC160
		public static HisData smethod_2(SortedList<DateTime, HisData> sortedList_0, DateTime dateTime_0)
		{
			Class339.Class340 @class = new Class339.Class340();
			@class.dateTime_0 = dateTime_0;
			HisData hisData = null;
			if (sortedList_0 != null && sortedList_0.Count > 0)
			{
				HisData result;
				try
				{
					if (sortedList_0.TryGetValue(@class.dateTime_0, out hisData))
					{
						goto IL_CF;
					}
					DateTime t = sortedList_0.Keys.First<DateTime>();
					DateTime t2 = sortedList_0.Keys.Last<DateTime>();
					if (@class.dateTime_0 > t && @class.dateTime_0 < t2)
					{
						hisData = Class339.smethod_3(sortedList_0, @class.dateTime_0);
					}
					else if (@class.dateTime_0 > t2)
					{
						hisData = sortedList_0.LastOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Value;
					}
					else
					{
						hisData = sortedList_0.FirstOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).Value;
					}
					result = hisData;
				}
				catch
				{
					goto IL_CF;
				}
				return result;
			}
			IL_CF:
			return hisData;
		}

		// Token: 0x06001D18 RID: 7448 RVA: 0x000CE054 File Offset: 0x000CC254
		public static HisData smethod_3(SortedList<DateTime, HisData> sortedList_0, DateTime dateTime_0)
		{
			Class339.Class341 @class = new Class339.Class341();
			@class.dateTime_0 = dateTime_0;
			HisData hisData = sortedList_0.LastOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Value;
			if (hisData != null)
			{
				hisData = Class339.smethod_4(hisData, @class.dateTime_0);
			}
			else
			{
				hisData = sortedList_0.FirstOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).Value;
				if (hisData != null)
				{
					hisData = Class339.smethod_4(hisData, @class.dateTime_0);
				}
			}
			return hisData;
		}

		// Token: 0x06001D19 RID: 7449 RVA: 0x000CE0D0 File Offset: 0x000CC2D0
		public static HisData smethod_4(HisData hisData_0, DateTime dateTime_0)
		{
			return new HisData
			{
				Close = hisData_0.Close,
				Open = hisData_0.Close,
				High = hisData_0.Close,
				Low = hisData_0.Close,
				Volume = new double?(0.0),
				Amount = hisData_0.Amount,
				Date = dateTime_0
			};
		}

		// Token: 0x0200028C RID: 652
		[CompilerGenerated]
		private sealed class Class340
		{
			// Token: 0x06001D1C RID: 7452 RVA: 0x000CE140 File Offset: 0x000CC340
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key <= this.dateTime_0;
			}

			// Token: 0x06001D1D RID: 7453 RVA: 0x000CE164 File Offset: 0x000CC364
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key >= this.dateTime_0;
			}

			// Token: 0x04000E6A RID: 3690
			public DateTime dateTime_0;
		}

		// Token: 0x0200028D RID: 653
		[CompilerGenerated]
		private sealed class Class341
		{
			// Token: 0x06001D1F RID: 7455 RVA: 0x000CE188 File Offset: 0x000CC388
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key <= this.dateTime_0;
			}

			// Token: 0x06001D20 RID: 7456 RVA: 0x000CE1AC File Offset: 0x000CC3AC
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key >= this.dateTime_0;
			}

			// Token: 0x04000E6B RID: 3691
			public DateTime dateTime_0;
		}
	}
}
