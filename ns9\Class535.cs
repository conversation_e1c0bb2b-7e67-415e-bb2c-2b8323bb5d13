﻿using System;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using ns13;
using ns17;
using ns18;
using ns20;
using ns26;
using ns28;
using ns30;
using ns4;

namespace ns9
{
	// Token: 0x020003F5 RID: 1013
	internal class Class535
	{
		// Token: 0x06002795 RID: 10133 RVA: 0x0000F32D File Offset: 0x0000D52D
		public void method_0(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x06002796 RID: 10134 RVA: 0x0010C7A8 File Offset: 0x0010A9A8
		internal bool method_1(byte[] byte_0, Class535.Class539 class539_0)
		{
			byte[] byte_;
			bool result;
			try
			{
				byte_ = Class522.smethod_4(byte_0);
				goto IL_1E;
			}
			catch (Exception)
			{
				this.method_3(Enum35.const_0, Class522.string_0);
				result = false;
			}
			return result;
			IL_1E:
			byte[] array = Class534.smethod_0(byte_, Class521.smethod_0(118345));
			if (array == null)
			{
				this.method_3(Enum35.const_0, Class534.string_0);
				return false;
			}
			this.method_4(Enum35.const_1);
			Class546 @class = new Class546(Class521.smethod_0(118671));
			if (this.iwebProxy_0 != null)
			{
				@class.method_0(this.iwebProxy_0);
			}
			Class535.Class537 class2 = new Class535.Class537(this, array, @class, class539_0);
			@class.method_1(new Delegate39(class2.method_0));
			return class2.bool_0;
		}

		// Token: 0x140000C0 RID: 192
		// (add) Token: 0x06002797 RID: 10135 RVA: 0x0010C854 File Offset: 0x0010AA54
		// (remove) Token: 0x06002798 RID: 10136 RVA: 0x0010C88C File Offset: 0x0010AA8C
		public event Delegate38 SendingReportFeedback
		{
			[CompilerGenerated]
			add
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002799 RID: 10137 RVA: 0x0010C8C4 File Offset: 0x0010AAC4
		protected void method_2(Enum35 enum35_0, string string_3, string string_4)
		{
			Delegate38 @delegate = this.delegate38_0;
			if (@delegate != null)
			{
				@delegate(this, new EventArgs37(enum35_0, string_3, string_4));
			}
		}

		// Token: 0x0600279A RID: 10138 RVA: 0x0000F336 File Offset: 0x0000D536
		protected void method_3(Enum35 enum35_0, string string_3)
		{
			this.method_2(enum35_0, string_3, string.Empty);
		}

		// Token: 0x0600279B RID: 10139 RVA: 0x0000F345 File Offset: 0x0000D545
		protected void method_4(Enum35 enum35_0)
		{
			this.method_3(enum35_0, string.Empty);
		}

		// Token: 0x040013A6 RID: 5030
		protected const string string_0 = "{100fd8cd-4fe2-410e-8c33-ae1af08ef31d}";

		// Token: 0x040013A7 RID: 5031
		private const string string_1 = "{be78a0c5-c47c-4127-a428-52bdc580a02f}";

		// Token: 0x040013A8 RID: 5032
		private const string string_2 = "{bf13b64c-b3d2-4165-b3f5-7f852d4744cf}";

		// Token: 0x040013A9 RID: 5033
		private IWebProxy iwebProxy_0;

		// Token: 0x040013AA RID: 5034
		[CompilerGenerated]
		private Delegate38 delegate38_0;

		// Token: 0x020003F6 RID: 1014
		private sealed class Class537
		{
			// Token: 0x0600279D RID: 10141 RVA: 0x0000F353 File Offset: 0x0000D553
			public Class537(Class535 class535_1, byte[] byte_1, Class546 class546_1, Class535.Class539 class539_1)
			{
				this.class535_0 = class535_1;
				this.class539_0 = class539_1;
				this.class546_0 = class546_1;
				this.byte_0 = byte_1;
			}

			// Token: 0x0600279E RID: 10142 RVA: 0x0010C8EC File Offset: 0x0010AAEC
			public void method_0(string string_0)
			{
				if (string_0 == Class521.smethod_0(119454))
				{
					this.class535_0.method_4(Enum35.const_2);
					byte[] bytes = Encoding.UTF8.GetBytes(Class521.smethod_0(117441));
					byte[] destinationArray = new byte[bytes.Length + this.byte_0.Length];
					Array.Copy(bytes, destinationArray, bytes.Length);
					Array.Copy(this.byte_0, 0, destinationArray, bytes.Length, this.byte_0.Length);
					Class535.Class538 @class = new Class535.Class538(this.class535_0);
					this.class546_0.method_2(destinationArray, this.class539_0.EmailAddress, this.class539_0.AppFriendlyName, this.class539_0.BuildFriendlyNumber, new Delegate39(@class.method_0));
					this.bool_0 = @class.bool_0;
					return;
				}
				if (this.class535_0.delegate38_0 != null)
				{
					this.class535_0.delegate38_0(this, new EventArgs37(Enum35.const_1, string_0));
				}
				this.bool_0 = false;
			}

			// Token: 0x040013AB RID: 5035
			private readonly Class535 class535_0;

			// Token: 0x040013AC RID: 5036
			private readonly byte[] byte_0;

			// Token: 0x040013AD RID: 5037
			private readonly Class546 class546_0;

			// Token: 0x040013AE RID: 5038
			private readonly Class535.Class539 class539_0;

			// Token: 0x040013AF RID: 5039
			public bool bool_0 = true;
		}

		// Token: 0x020003F7 RID: 1015
		private sealed class Class538
		{
			// Token: 0x0600279F RID: 10143 RVA: 0x0000F37F File Offset: 0x0000D57F
			public Class538(Class535 class535_1)
			{
				this.class535_0 = class535_1;
			}

			// Token: 0x060027A0 RID: 10144 RVA: 0x0010C9E4 File Offset: 0x0010ABE4
			public void method_0(string string_0)
			{
				if (string_0.StartsWith(Class521.smethod_0(119449)))
				{
					this.class535_0.method_3(Enum35.const_2, string_0);
					this.bool_0 = false;
					return;
				}
				this.class535_0.method_2(Enum35.const_3, string.Empty, string_0);
				this.bool_0 = true;
			}

			// Token: 0x040013B0 RID: 5040
			private readonly Class535 class535_0;

			// Token: 0x040013B1 RID: 5041
			public bool bool_0;
		}

		// Token: 0x020003F8 RID: 1016
		internal sealed class Class539
		{
			// Token: 0x060027A1 RID: 10145 RVA: 0x0000F38E File Offset: 0x0000D58E
			public Class539(string string_3, string string_4, string string_5)
			{
				this.string_0 = string_3;
				this.string_2 = string_5;
				this.string_1 = string_4;
			}

			// Token: 0x170006CB RID: 1739
			// (get) Token: 0x060027A2 RID: 10146 RVA: 0x0000F3AB File Offset: 0x0000D5AB
			public string BuildFriendlyNumber
			{
				get
				{
					return this.string_2;
				}
			}

			// Token: 0x170006CC RID: 1740
			// (get) Token: 0x060027A3 RID: 10147 RVA: 0x0000F3B3 File Offset: 0x0000D5B3
			public string AppFriendlyName
			{
				get
				{
					return this.string_1;
				}
			}

			// Token: 0x170006CD RID: 1741
			// (get) Token: 0x060027A4 RID: 10148 RVA: 0x0000F3BB File Offset: 0x0000D5BB
			public string EmailAddress
			{
				get
				{
					return this.string_0;
				}
			}

			// Token: 0x040013B2 RID: 5042
			public static Class535.Class539 class539_0 = new Class535.Class539(null, null, null);

			// Token: 0x040013B3 RID: 5043
			private readonly string string_0;

			// Token: 0x040013B4 RID: 5044
			private readonly string string_1;

			// Token: 0x040013B5 RID: 5045
			private readonly string string_2;
		}
	}
}
