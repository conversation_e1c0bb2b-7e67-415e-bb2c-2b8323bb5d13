﻿using System;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Sources;

namespace ns21
{
	// Token: 0x02000130 RID: 304
	internal sealed class Class185
	{
		// Token: 0x06000C8E RID: 3214 RVA: 0x0004AB34 File Offset: 0x00048D34
		public static int smethod_0(IUpdateSource iupdateSource_0)
		{
			UpdateManager instance = UpdateManager.Instance;
			int num = 0;
			int result;
			if (instance.State != UpdateManager.UpdateProcessState.NotChecked)
			{
				result = 0;
			}
			else
			{
				try
				{
					instance.CheckForUpdates(iupdateSource_0);
					num = instance.UpdatesAvailable;
				}
				catch (Exception ex)
				{
					NAppUpdateException ex2 = ex as NAppUpdateException;
					throw;
				}
				result = num;
			}
			return result;
		}
	}
}
