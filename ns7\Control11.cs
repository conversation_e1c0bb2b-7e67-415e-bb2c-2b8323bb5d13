﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;

namespace ns7
{
	// Token: 0x02000252 RID: 594
	internal sealed class Control11 : DevComponents.DotNetBar.TabControl
	{
		// Token: 0x0600196E RID: 6510 RVA: 0x0000A9D9 File Offset: 0x00008BD9
		public Control11()
		{
			base.Style = eTabStripStyle.Flat;
			base.TabAlignment = eTabStripAlignment.Bottom;
			this.Dock = DockStyle.Fill;
			this.BackColor = Color.FromArgb(194, 217, 247);
		}

		// Token: 0x0600196F RID: 6511 RVA: 0x000B0D5C File Offset: 0x000AEF5C
		public void method_0(string string_0)
		{
			Control11.Class320 @class = new Control11.Class320();
			@class.string_0 = string_0;
			TabItem tabItem = base.Tabs.Cast<TabItem>().ToList<TabItem>().SingleOrDefault(new Func<TabItem, bool>(@class.method_0));
			if (tabItem != null)
			{
				base.SelectedTab = tabItem;
			}
		}

		// Token: 0x02000253 RID: 595
		[CompilerGenerated]
		private sealed class Class320
		{
			// Token: 0x06001971 RID: 6513 RVA: 0x000B0DA4 File Offset: 0x000AEFA4
			internal bool method_0(TabItem tabItem_0)
			{
				return tabItem_0.Text.Contains(this.string_0);
			}

			// Token: 0x04000CC8 RID: 3272
			public string string_0;
		}
	}
}
