﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns12;
using ns18;
using ns26;
using ns28;
using ns7;
using ns9;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000312 RID: 786
	public sealed class TreeFunction : Class415
	{
		// Token: 0x060021E8 RID: 8680 RVA: 0x0000D993 File Offset: 0x0000BB93
		public TreeFunction(HToken token, Class411 left, Class411 right) : base(token, left, right)
		{
		}

		// Token: 0x060021E9 RID: 8681 RVA: 0x000F085C File Offset: 0x000EEA5C
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_1)
			{
				throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(101741)));
			}
			HToken htoken = tokenes_0.Current;
			Class412 @class = new Class412(tokenes_0.Current);
			tokenes_0.method_1();
			if (!(tokenes_0.Current.Symbol.Name != Class521.smethod_0(24872)))
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30)
				{
					if (tokenes_0.method_1().Symbol.HSymbolType == Enum26.const_30)
					{
						throw new Exception(htoken.method_0(Class521.smethod_0(101807)));
					}
					Class411 right = Class418.smethod_0(tokenes_0, htoken);
					if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30 | tokenes_0.Current.Symbol.Name != Class521.smethod_0(5046))
					{
						throw new Exception(htoken.method_0(Class521.smethod_0(101836)));
					}
					return new TreeFunction(@class.Token, @class, right);
				}
			}
			throw new Exception(htoken.method_0(Class521.smethod_0(101770)));
		}

		// Token: 0x060021EA RID: 8682 RVA: 0x000F0994 File Offset: 0x000EEB94
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			TreeFunction.Class430 @class = new TreeFunction.Class430();
			HToken token = this.Left.Token;
			if (this.Left.Token.Symbol.HSymbolType != Enum26.const_1)
			{
				throw new Exception(this.Left.Token.method_0(Class521.smethod_0(101741)));
			}
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			@class.string_0 = (string)obj;
			List<object> list = (List<object>)this.Right.vmethod_1(parserEnvironment_0);
			bool bool_ = list.Where(new Func<object, bool>(TreeFunction.<>c.<>9.method_0)).Any(new Func<object, bool>(TreeFunction.<>c.<>9.method_1));
			List<MethodInfo> list2 = parserEnvironment_0.Functions.Where(new Func<MethodInfo, bool>(@class.method_0)).ToList<MethodInfo>();
			bool flag = list2.Any<MethodInfo>();
			object result;
			foreach (MethodInfo methodInfo in list2)
			{
				ParameterInfo[] parameters = methodInfo.GetParameters();
				if (parameters.Length == list.Count)
				{
					bool flag2 = true;
					int i = 0;
					while (i < parameters.Length)
					{
						Type parameterType = parameters[i].ParameterType;
						Type type = list[i].GetType();
						if (parameterType != type)
						{
							flag2 = false;
							IL_157:
							if (!flag2)
							{
								goto IL_15B;
							}
							object object_ = methodInfo.Invoke(parserEnvironment_0.UserDefineIns, list.ToArray());
							result = this.method_2(object_, bool_);
							goto IL_2F4;
						}
						else
						{
							i++;
						}
					}
					goto IL_157;
				}
				IL_15B:
				if (parameters.Length == 1 && parameters[0].ParameterType == typeof(DataArray[]) && list.Count > 0)
				{
					Type elementType = parameters[0].ParameterType.GetElementType();
					if (list[0].GetType() == elementType)
					{
						List<DataArray> list3 = new List<DataArray>();
						for (int j = 0; j < list.Count; j++)
						{
							if (list[j].GetType() == typeof(DataArray))
							{
								list3.Add(list[j] as DataArray);
							}
						}
						object object_ = methodInfo.Invoke(parserEnvironment_0.UserDefineIns, new object[]
						{
							list3.ToArray()
						});
						result = this.method_2(object_, bool_);
						goto IL_2F4;
					}
				}
			}
			if (flag)
			{
				foreach (MethodInfo methodInfo2 in list2)
				{
					List<object> list4 = this.method_3(methodInfo2, list);
					if (list4 != null)
					{
						try
						{
							object object_ = methodInfo2.Invoke(parserEnvironment_0.UserDefineIns, list4.ToArray());
							result = this.method_2(object_, bool_);
							goto IL_2F4;
						}
						catch (Exception ex)
						{
							throw ex;
						}
						break;
					}
				}
				throw new Exception(token.method_0(Class521.smethod_0(101869)));
			}
			throw new Exception(token.method_0(Class521.smethod_0(101902)));
			IL_2F4:
			return result;
		}

		// Token: 0x060021EB RID: 8683 RVA: 0x000F0CEC File Offset: 0x000EEEEC
		private object method_2(object object_0, bool bool_0)
		{
			object result;
			if (object_0.GetType() == typeof(DataArray))
			{
				(object_0 as DataArray).HasLast = bool_0;
				result = object_0;
			}
			else
			{
				result = object_0;
			}
			return result;
		}

		// Token: 0x060021EC RID: 8684 RVA: 0x000F0D20 File Offset: 0x000EEF20
		public List<object> method_3(MethodInfo methodInfo_0, List<object> list_0)
		{
			List<object> list = new List<object>();
			ParameterInfo[] parameters = methodInfo_0.GetParameters();
			List<object> result;
			if (parameters.Length == list_0.Count)
			{
				for (int i = 0; i < parameters.Length; i++)
				{
					Type parameterType = parameters[i].ParameterType;
					Type type = list_0[i].GetType();
					if (parameterType == type)
					{
						list.Add(list_0[i]);
					}
					else if (parameterType == typeof(DataArray) && type == typeof(double))
					{
						DataArray item = (double)list_0[i];
						list.Add(item);
					}
					else if (parameterType == typeof(DataArray) && type == typeof(int))
					{
						DataArray item2 = (double)((int)list_0[i]);
						list.Add(item2);
					}
					else if (parameterType == typeof(double) && type == typeof(DataArray))
					{
						double num = (list_0[i] as DataArray).Data.Last<double>();
						list.Add(num);
					}
					else
					{
						if (parameterType != typeof(string) || type != typeof(double))
						{
							return null;
						}
						list.Add(((double)list_0[i]).ToString());
					}
				}
				result = list;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060021ED RID: 8685 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x02000313 RID: 787
		[CompilerGenerated]
		private sealed class Class430
		{
			// Token: 0x060021EF RID: 8687 RVA: 0x000F0EA0 File Offset: 0x000EF0A0
			internal bool method_0(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.string_0;
			}

			// Token: 0x04001067 RID: 4199
			public string string_0;
		}
	}
}
