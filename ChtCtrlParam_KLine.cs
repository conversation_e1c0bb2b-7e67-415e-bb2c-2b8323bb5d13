﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Linq;
using ns18;
using TEx.Inds;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x020001CF RID: 463
	[Serializable]
	internal sealed class ChtCtrlParam_KLine : ChtCtrlParam
	{
		// Token: 0x06001239 RID: 4665 RVA: 0x00082708 File Offset: 0x00080908
		public override XElement vmethod_0()
		{
			XElement xelement = base.vmethod_0();
			xelement.SetAttributeValue(Class521.smethod_0(2053), Class521.smethod_0(4933));
			if (this.SpCParam_VolMACD != null)
			{
				XElement xelement2 = this.SpCParam_VolMACD.GetXElement(Class521.smethod_0(2346));
				xelement.Add(xelement2);
			}
			if (this.SpCParam_VolMACD_Sub != null)
			{
				XElement xelement3 = this.SpCParam_VolMACD_Sub.GetXElement(Class521.smethod_0(2371));
				xelement.Add(xelement3);
			}
			if (this.ChartParamList != null && this.ChartParamList.Any<ChartParam>())
			{
				XElement xelement4 = new XElement(Class521.smethod_0(2426));
				foreach (ChartParam chartParam in this.ChartParamList)
				{
					XElement xelement5 = new XElement(Class521.smethod_0(2447));
					xelement5.SetAttributeValue(Class521.smethod_0(2464), Convert.ToInt32(chartParam.ChartType));
					if (chartParam.ChartType == ChartType.CandleStick)
					{
						xelement5.SetAttributeValue(Class521.smethod_0(2477), chartParam.IsYAxisLogType);
					}
					if (chartParam.UDSList != null && chartParam.UDSList.Any<UserDefineIndScript>())
					{
						XElement xelement6 = new XElement(Class521.smethod_0(2498));
						foreach (UserDefineIndScript userDefineIndScript in chartParam.UDSList)
						{
							XElement content = UserDefineFileMgr.smethod_26(new UserDefineIndParams(userDefineIndScript.Name, userDefineIndScript.UserDefineParams));
							xelement6.Add(content);
						}
						xelement5.Add(xelement6);
					}
					if (chartParam.IndList != null && chartParam.IndList.Any<Indicator>())
					{
						XElement xelement7 = new XElement(Class521.smethod_0(2400));
						foreach (Indicator indicator in chartParam.IndList)
						{
							XElement xelement8 = new XElement(Class521.smethod_0(2413));
							Type type = indicator.GetType();
							xelement8.SetAttributeValue(Class521.smethod_0(2690), type.ToString());
							XElement xelement9 = new XElement(Class521.smethod_0(48831));
							SerializationInfo serializationInfo = new SerializationInfo(type, new FormatterConverter());
							indicator.GetObjectData(serializationInfo, new StreamingContext(StreamingContextStates.All));
							SerializationInfoEnumerator enumerator4 = serializationInfo.GetEnumerator();
							for (int i = 0; i < serializationInfo.MemberCount; i++)
							{
								enumerator4.MoveNext();
								XElement xelement10 = new XElement(Class521.smethod_0(48848));
								xelement10.SetAttributeValue(Class521.smethod_0(1858), enumerator4.Name);
								if (enumerator4.ObjectType == typeof(int[]))
								{
									int[] array = enumerator4.Value as int[];
									string text = string.Empty;
									foreach (int num in array)
									{
										text = text + num.ToString() + Class521.smethod_0(3636);
									}
									text = text.Trim();
									xelement10.SetAttributeValue(Class521.smethod_0(48861), text);
								}
								else if (enumerator4.ObjectType == typeof(Color))
								{
									xelement10.SetAttributeValue(Class521.smethod_0(48861), ((Color)enumerator4.Value).ToArgb());
								}
								else if (enumerator4.ObjectType.IsEnum)
								{
									xelement10.SetAttributeValue(Class521.smethod_0(48861), Convert.ToInt32(enumerator4.Value));
								}
								else
								{
									xelement10.SetAttributeValue(Class521.smethod_0(48861), enumerator4.Value);
								}
								xelement10.SetAttributeValue(Class521.smethod_0(2690), enumerator4.ObjectType.FullName);
								xelement10.SetAttributeValue(Class521.smethod_0(48870), enumerator4.ObjectType.AssemblyQualifiedName);
								xelement9.Add(xelement10);
							}
							if (xelement9.HasElements)
							{
								xelement8.Add(xelement9);
							}
							xelement7.Add(xelement8);
						}
						xelement5.Add(xelement7);
					}
					xelement4.Add(xelement5);
				}
				xelement.Add(xelement4);
			}
			return xelement;
		}

		// Token: 0x170002B0 RID: 688
		// (get) Token: 0x0600123A RID: 4666 RVA: 0x00082C14 File Offset: 0x00080E14
		// (set) Token: 0x0600123B RID: 4667 RVA: 0x000079C4 File Offset: 0x00005BC4
		public SplitContainerParam SpCParam_VolMACD
		{
			get
			{
				return this._SpCParam_VolMACD;
			}
			set
			{
				this._SpCParam_VolMACD = value;
			}
		}

		// Token: 0x170002B1 RID: 689
		// (get) Token: 0x0600123C RID: 4668 RVA: 0x00082C2C File Offset: 0x00080E2C
		// (set) Token: 0x0600123D RID: 4669 RVA: 0x000079CF File Offset: 0x00005BCF
		public SplitContainerParam SpCParam_VolMACD_Sub
		{
			get
			{
				return this._SpCParam_VolMACD_Sub;
			}
			set
			{
				this._SpCParam_VolMACD_Sub = value;
			}
		}

		// Token: 0x170002B2 RID: 690
		// (get) Token: 0x0600123E RID: 4670 RVA: 0x00082C44 File Offset: 0x00080E44
		// (set) Token: 0x0600123F RID: 4671 RVA: 0x000079DA File Offset: 0x00005BDA
		public List<ChartParam> ChartParamList
		{
			get
			{
				return this._ChartParamList;
			}
			set
			{
				this._ChartParamList = value;
			}
		}

		// Token: 0x04000989 RID: 2441
		private SplitContainerParam _SpCParam_VolMACD;

		// Token: 0x0400098A RID: 2442
		private SplitContainerParam _SpCParam_VolMACD_Sub;

		// Token: 0x0400098B RID: 2443
		private List<ChartParam> _ChartParamList;
	}
}
