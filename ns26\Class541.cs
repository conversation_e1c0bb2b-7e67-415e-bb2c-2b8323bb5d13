﻿using System;
using System.Drawing;
using System.IO;
using System.Reflection;
using ns18;

namespace ns26
{
	// Token: 0x020003FE RID: 1022
	internal sealed class Class541
	{
		// Token: 0x060027C4 RID: 10180 RVA: 0x0010CA34 File Offset: 0x0010AC34
		public static Bitmap smethod_0(string string_0)
		{
			Bitmap result;
			try
			{
				Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream(Class521.smethod_0(118720) + string_0 + Class521.smethod_0(98317));
				result = ((manifestResourceStream == null) ? null : new Bitmap(manifestResourceStream));
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060027C5 RID: 10181 RVA: 0x0010CA8C File Offset: 0x0010AC8C
		public static Icon smethod_1(string string_0)
		{
			Icon result;
			try
			{
				Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream(Class521.smethod_0(118720) + string_0 + Class521.smethod_0(118781));
				result = ((manifestResourceStream == null) ? null : new Icon(manifestResourceStream));
			}
			catch
			{
				result = null;
			}
			return result;
		}
	}
}
