﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000070 RID: 112
	[Serializable]
	internal sealed class DrawGoldenRatio : DrawRatio, ISerializable
	{
		// Token: 0x0600040D RID: 1037 RVA: 0x00003B8C File Offset: 0x00001D8C
		public DrawGoldenRatio()
		{
		}

		// Token: 0x0600040E RID: 1038 RVA: 0x00003B94 File Offset: 0x00001D94
		public DrawGoldenRatio(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5225);
			base.IfShowTopDashLine = true;
		}

		// Token: 0x0600040F RID: 1039 RVA: 0x00003BBC File Offset: 0x00001DBC
		protected DrawGoldenRatio(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
			base.IfShowTopDashLine = true;
		}

		// Token: 0x06000410 RID: 1040 RVA: 0x00003BD4 File Offset: 0x00001DD4
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000411 RID: 1041 RVA: 0x00022FF4 File Offset: 0x000211F4
		protected override void vmethod_24(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<DrawSublineParam> sublineParamList = base.SublineParamList;
			for (int i = 0; i < sublineParamList.Count; i++)
			{
				DrawSublineParam drawSublineParam = sublineParamList[i];
				bool bool_ = false;
				if (drawSublineParam.Enabled)
				{
					if (i == 5)
					{
						bool_ = true;
					}
					base.method_42(list_2, chartCS_1, double_1, double_2, double_3, double_4, drawSublineParam.Value, string_6, bool_);
				}
			}
		}

		// Token: 0x06000412 RID: 1042 RVA: 0x0002304C File Offset: 0x0002124C
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				0.236,
				0.382,
				0.5,
				0.618,
				0.786,
				1.0,
				1.382,
				1.618,
				2.618
			});
			return base.method_28(list_, 0.001, 500.0, 3);
		}
	}
}
