﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml.Linq;
using ns18;
using ns20;
using ns21;
using ns30;
using TEx;

namespace ns22
{
	// Token: 0x02000174 RID: 372
	internal sealed class Class210
	{
		// Token: 0x06000E19 RID: 3609 RVA: 0x0005C544 File Offset: 0x0005A744
		public static List<Class280> smethod_0()
		{
			return new List<Class280>
			{
				new Class280(200, Class521.smethod_0(26155), Class521.smethod_0(26188), KeyModifiers.None, Keys.F1),
				new Class280(201, Class521.smethod_0(26205), Class521.smethod_0(26238), KeyModifiers.None, Keys.F2),
				new Class280(202, Class521.smethod_0(26255), Class521.smethod_0(26288), KeyModifiers.None, Keys.F3),
				new Class280(203, Class521.smethod_0(26305), Class521.smethod_0(26338), KeyModifiers.None, Keys.F4),
				new Class280(204, Class521.smethod_0(26355), Class521.smethod_0(26376), KeyModifiers.Ctrl, Keys.D1),
				new Class280(205, Class521.smethod_0(26385), Class521.smethod_0(26410), KeyModifiers.Ctrl, Keys.D2),
				new Class280(206, Class521.smethod_0(26419), Class521.smethod_0(26444), KeyModifiers.Ctrl, Keys.D3),
				new Class280(207, Class521.smethod_0(26453), Class521.smethod_0(26478), KeyModifiers.Ctrl, Keys.D4),
				new Class280(208, Class521.smethod_0(26487), Class521.smethod_0(26500), KeyModifiers.Alt, Keys.A),
				new Class280(209, Class521.smethod_0(26517), Class521.smethod_0(26542), KeyModifiers.Alt, Keys.W),
				new Class280(300, Class521.smethod_0(26559), Class521.smethod_0(26576), KeyModifiers.None, Keys.Space),
				new Class280(301, Class521.smethod_0(26613), Class521.smethod_0(26630), KeyModifiers.None, Keys.OemOpenBrackets),
				new Class280(302, Class521.smethod_0(26655), Class521.smethod_0(26676), KeyModifiers.None, Keys.OemCloseBrackets),
				new Class280(303, Class521.smethod_0(26701), Class521.smethod_0(26734), KeyModifiers.None, Keys.F5),
				new Class280(306, Class521.smethod_0(26763), Class521.smethod_0(26784), KeyModifiers.Ctrl, Keys.Right),
				new Class280(307, Class521.smethod_0(26817), Class521.smethod_0(26846), KeyModifiers.Ctrl, Keys.Left),
				new Class280(308, Class521.smethod_0(26879), Class521.smethod_0(26904), KeyModifiers.None, Keys.Next),
				new Class280(309, Class521.smethod_0(26929), Class521.smethod_0(26958), KeyModifiers.None, Keys.Prior),
				new Class280(310, Class521.smethod_0(26983), Class521.smethod_0(27004), KeyModifiers.Ctrl, Keys.R),
				new Class280(311, Class521.smethod_0(27029), Class521.smethod_0(27054), KeyModifiers.Ctrl, Keys.D),
				new Class280(312, Class521.smethod_0(27079), Class521.smethod_0(27116), KeyModifiers.Ctrl, Keys.X),
				new Class280(313, Class521.smethod_0(27149), Class521.smethod_0(27182), KeyModifiers.None, Keys.F9),
				new Class280(330, Class521.smethod_0(27215), Class521.smethod_0(27236), KeyModifiers.Ctrl, Keys.Z),
				new Class280(340, Class521.smethod_0(27253), Class521.smethod_0(27274), KeyModifiers.Ctrl, Keys.B),
				new Class280(350, Class521.smethod_0(27291), Class521.smethod_0(27304), KeyModifiers.Ctrl, Keys.S)
			};
		}

		// Token: 0x06000E1A RID: 3610 RVA: 0x0005C930 File Offset: 0x0005AB30
		public static List<Class280> smethod_1()
		{
			List<Class280> list = Class210.smethod_2(Base.UI.smethod_53());
			if (list == null)
			{
				list = Class210.smethod_0();
			}
			else
			{
				using (List<Class280>.Enumerator enumerator = Class210.DefaultUsrHotKeyList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Class210.Class211 @class = new Class210.Class211();
						@class.class280_0 = enumerator.Current;
						if (!list.Exists(new Predicate<Class280>(@class.method_0)))
						{
							list.Add(@class.class280_0);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000E1B RID: 3611 RVA: 0x0005C9C4 File Offset: 0x0005ABC4
		public static List<Class280> smethod_2(XDocument xdocument_0)
		{
			List<Class280> list = null;
			if (xdocument_0 != null)
			{
				try
				{
					list = new List<Class280>();
					foreach (XElement xelement in xdocument_0.Element(Class521.smethod_0(12376)).Element(Class521.smethod_0(27321)).Elements(Class521.smethod_0(27334)))
					{
						Class280 @class = new Class280();
						@class.Id = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(12411)).Value);
						@class.KeyModifier = (KeyModifiers)Convert.ToInt32(xelement.Attribute(Class521.smethod_0(27343)).Value);
						@class.Key = (Keys)Convert.ToInt32(xelement.Attribute(Class521.smethod_0(27356)).Value);
						Class280 class2 = Class210.smethod_7(@class.Id);
						@class.EnName = class2.EnName;
						@class.CnName = class2.CnName;
						@class.IfDispInQuickWnd = class2.IfDispInQuickWnd;
						list.Add(@class);
					}
				}
				catch
				{
					throw;
				}
			}
			return list;
		}

		// Token: 0x06000E1C RID: 3612 RVA: 0x0005CB38 File Offset: 0x0005AD38
		public static Keys smethod_3(Enum3 enum3_0)
		{
			return Class210.smethod_4((int)enum3_0);
		}

		// Token: 0x06000E1D RID: 3613 RVA: 0x0005CB50 File Offset: 0x0005AD50
		public static Keys smethod_4(int int_0)
		{
			Keys keyCode;
			try
			{
				Class280 @class = Class210.smethod_6(int_0);
				if (@class != null)
				{
					keyCode = @class.KeyCode;
					goto IL_48;
				}
			}
			catch
			{
				Class48.smethod_4(new Exception(Class521.smethod_0(27361) + int_0.ToString() + Class521.smethod_0(27390)), true, null);
			}
			return Keys.None;
			IL_48:
			return keyCode;
		}

		// Token: 0x06000E1E RID: 3614 RVA: 0x0005CBBC File Offset: 0x0005ADBC
		public static Class280 smethod_5(Enum3 enum3_0)
		{
			return Class210.smethod_6((int)enum3_0);
		}

		// Token: 0x06000E1F RID: 3615 RVA: 0x0005CBD4 File Offset: 0x0005ADD4
		public static Class280 smethod_6(int int_0)
		{
			Class210.Class212 @class = new Class210.Class212();
			@class.int_0 = int_0;
			return Class210.UsrHotKeyList.Find(new Predicate<Class280>(@class.method_0));
		}

		// Token: 0x06000E20 RID: 3616 RVA: 0x0005CC08 File Offset: 0x0005AE08
		private static Class280 smethod_7(int int_0)
		{
			Class210.Class213 @class = new Class210.Class213();
			@class.int_0 = int_0;
			return Class210.DefaultUsrHotKeyList.Find(new Predicate<Class280>(@class.method_0));
		}

		// Token: 0x17000234 RID: 564
		// (get) Token: 0x06000E21 RID: 3617 RVA: 0x0005CC3C File Offset: 0x0005AE3C
		public static List<Class280> DefaultUsrHotKeyList
		{
			get
			{
				if (Class210.list_0 == null || Class210.list_0.Count == 0)
				{
					Class210.list_0 = Class210.smethod_0();
				}
				return Class210.list_0;
			}
		}

		// Token: 0x17000235 RID: 565
		// (get) Token: 0x06000E22 RID: 3618 RVA: 0x0005CC70 File Offset: 0x0005AE70
		// (set) Token: 0x06000E23 RID: 3619 RVA: 0x000064B0 File Offset: 0x000046B0
		public static List<Class280> UsrHotKeyList
		{
			get
			{
				if (Class210.list_1 == null || Class210.list_1.Count == 0)
				{
					Class210.list_1 = Class210.smethod_1();
				}
				return Class210.list_1;
			}
			set
			{
				Class210.list_1 = value;
			}
		}

		// Token: 0x04000752 RID: 1874
		private static List<Class280> list_0;

		// Token: 0x04000753 RID: 1875
		private static List<Class280> list_1;

		// Token: 0x02000175 RID: 373
		[CompilerGenerated]
		private sealed class Class211
		{
			// Token: 0x06000E26 RID: 3622 RVA: 0x0005CCA4 File Offset: 0x0005AEA4
			internal bool method_0(Class280 class280_1)
			{
				return class280_1.Id == this.class280_0.Id;
			}

			// Token: 0x04000754 RID: 1876
			public Class280 class280_0;
		}

		// Token: 0x02000176 RID: 374
		[CompilerGenerated]
		private sealed class Class212
		{
			// Token: 0x06000E28 RID: 3624 RVA: 0x0005CCC8 File Offset: 0x0005AEC8
			internal bool method_0(Class280 class280_0)
			{
				return class280_0.Id == this.int_0;
			}

			// Token: 0x04000755 RID: 1877
			public int int_0;
		}

		// Token: 0x02000177 RID: 375
		[CompilerGenerated]
		private sealed class Class213
		{
			// Token: 0x06000E2A RID: 3626 RVA: 0x0005CCE8 File Offset: 0x0005AEE8
			internal bool method_0(Class280 class280_0)
			{
				return class280_0.Id == this.int_0;
			}

			// Token: 0x04000756 RID: 1878
			public int int_0;
		}
	}
}
