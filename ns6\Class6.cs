﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using ns18;

namespace ns6
{
	// Token: 0x02000009 RID: 9
	[CompilerGenerated]
	internal sealed class Class6<T, U>
	{
		// Token: 0x17000015 RID: 21
		// (get) Token: 0x06000031 RID: 49 RVA: 0x00010B74 File Offset: 0x0000ED74
		public T api
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000032 RID: 50 RVA: 0x00010B8C File Offset: 0x0000ED8C
		public U lc
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x06000033 RID: 51 RVA: 0x00002B29 File Offset: 0x00000D29
		[DebuggerHidden]
		public Class6(T gparam_2, U gparam_3)
		{
			this.gparam_0 = gparam_2;
			this.gparam_1 = gparam_3;
		}

		// Token: 0x06000034 RID: 52 RVA: 0x00010BA4 File Offset: 0x0000EDA4
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class6<T, U> @class = obj as Class6<T, U>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0))
			{
				result = EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000035 RID: 53 RVA: 0x00010BF4 File Offset: 0x0000EDF4
		[DebuggerHidden]
		public int GetHashCode()
		{
			return (-760999891 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1);
		}

		// Token: 0x06000036 RID: 54 RVA: 0x00010C34 File Offset: 0x0000EE34
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = Class521.smethod_0(512);
			object[] array = new object[2];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_4B;
				}
			}
			obj = ptr.ToString();
			IL_4B:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_86;
				}
			}
			obj2 = ptr2.ToString();
			IL_86:
			array[num2] = obj2;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000015 RID: 21
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x04000016 RID: 22
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;
	}
}
