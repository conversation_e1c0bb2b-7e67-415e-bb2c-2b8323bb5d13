﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns16;
using ns18;
using ns20;
using ns21;
using ns26;
using ns9;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000161 RID: 353
	internal sealed partial class BlindTestForm : Form
	{
		// Token: 0x14000070 RID: 112
		// (add) Token: 0x06000D5B RID: 3419 RVA: 0x000545D4 File Offset: 0x000527D4
		// (remove) Token: 0x06000D5C RID: 3420 RVA: 0x0005460C File Offset: 0x0005280C
		public event Delegate3 SelectionChanged
		{
			[CompilerGenerated]
			add
			{
				Delegate3 @delegate = this.delegate3_0;
				Delegate3 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate3 value2 = (Delegate3)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate3>(ref this.delegate3_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate3 @delegate = this.delegate3_0;
				Delegate3 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate3 value2 = (Delegate3)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate3>(ref this.delegate3_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000D5D RID: 3421 RVA: 0x00054644 File Offset: 0x00052844
		protected void method_0(int int_0, int int_1, DateTime? nullable_0)
		{
			EventArgs1 e = new EventArgs1(int_0, int_1, nullable_0, false, this.list_0);
			Delegate3 @delegate = this.delegate3_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x06000D5E RID: 3422 RVA: 0x00054678 File Offset: 0x00052878
		public BlindTestForm()
		{
			this.method_11();
			base.AutoScaleMode = AutoScaleMode.Dpi;
			this.method_1();
			base.Icon = (Icon)Class351.Resources.GetObject(Class521.smethod_0(22310));
			if (Base.UI.TransTabs != null)
			{
				this.list_1 = Base.UI.TransTabs.ZixuanStkSymbList;
			}
			if (Base.UI.Form.BlindTestForm_IfNoZixuan)
			{
				this.checkBox_0.Checked = false;
			}
			if (this.list_1 != null && this.list_1.Any<StkSymbol>())
			{
				this.checkBox_0.Click += this.checkBox_4_Click;
			}
			if (!TApp.IsStIncluded)
			{
				this.checkBox_2.Checked = false;
				this.checkBox_5.Checked = false;
				this.checkBox_6.Checked = false;
				this.checkBox_8.Checked = false;
				this.checkBox_7.Checked = false;
				this.checkBox_10.Checked = false;
				this.checkBox_9.Checked = false;
				this.method_2(false);
				this.groupBox_1.Enabled = false;
			}
			else
			{
				if (!Base.UI.Form.BlindTestForm_IfStIdx)
				{
					this.checkBox_2.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfStFond)
				{
					this.checkBox_5.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStMain)
				{
					this.checkBox_6.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStZX)
				{
					this.checkBox_8.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStCYB)
				{
					this.checkBox_7.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStKCB)
				{
					this.checkBox_10.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfStConvBond)
				{
					this.checkBox_9.Checked = false;
				}
				this.checkBox_2.Click += this.checkBox_4_Click;
				this.checkBox_5.Click += this.checkBox_4_Click;
				this.checkBox_6.Click += this.checkBox_4_Click;
				this.checkBox_8.Click += this.checkBox_4_Click;
				this.checkBox_7.Click += this.checkBox_4_Click;
				this.checkBox_10.Click += this.checkBox_4_Click;
				this.checkBox_9.Click += this.checkBox_4_Click;
			}
			if (!TApp.IsFtIncluded)
			{
				this.checkBox_1.Checked = false;
				this.checkBox_3.Checked = false;
				this.checkBox_4.Checked = false;
				this.method_3(false);
				this.groupBox_0.Enabled = false;
			}
			else
			{
				if (Base.UI.Form.BlindTestForm_IfNoFtMI)
				{
					this.checkBox_1.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfFtIdx)
				{
					this.checkBox_3.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfFtMth)
				{
					this.checkBox_4.Checked = false;
				}
				this.checkBox_1.Click += this.checkBox_4_Click;
				this.checkBox_3.Click += this.checkBox_4_Click;
				this.checkBox_4.Click += this.checkBox_4_Click;
			}
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			this.checkBox_4.CheckedChanged += this.checkBox_4_CheckedChanged;
			this.random_0 = new Random();
		}

		// Token: 0x06000D5F RID: 3423 RVA: 0x000549E0 File Offset: 0x00052BE0
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font(Class521.smethod_0(6998), num);
			}
			List<Control> list = new List<Control>();
			foreach (object obj in base.Controls)
			{
				Control item = (Control)obj;
				list.Add(item);
			}
			foreach (object obj2 in this.panel_0.Controls)
			{
				Control item2 = (Control)obj2;
				list.Add(item2);
			}
			foreach (object obj3 in this.groupBox_0.Controls)
			{
				Control item3 = (Control)obj3;
				list.Add(item3);
			}
			foreach (object obj4 in this.groupBox_1.Controls)
			{
				Control item4 = (Control)obj4;
				list.Add(item4);
			}
			foreach (object obj5 in this.groupBox_2.Controls)
			{
				Control item5 = (Control)obj5;
				list.Add(item5);
			}
			foreach (Control control in list)
			{
				if (control.Font.Size != num)
				{
					control.Font = new Font(Class521.smethod_0(6998), num);
				}
			}
		}

		// Token: 0x06000D60 RID: 3424 RVA: 0x00005EC1 File Offset: 0x000040C1
		private void checkBox_4_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_4.Checked && MessageBox.Show(Class521.smethod_0(22331), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
			{
				this.checkBox_4.Checked = false;
			}
		}

		// Token: 0x06000D61 RID: 3425 RVA: 0x00054C20 File Offset: 0x00052E20
		private void method_2(bool bool_0)
		{
			this.checkBox_2.Enabled = bool_0;
			this.checkBox_5.Enabled = bool_0;
			this.checkBox_6.Enabled = bool_0;
			this.checkBox_8.Enabled = bool_0;
			this.checkBox_7.Enabled = bool_0;
			this.checkBox_10.Enabled = bool_0;
			this.checkBox_9.Enabled = bool_0;
		}

		// Token: 0x06000D62 RID: 3426 RVA: 0x00005EFD File Offset: 0x000040FD
		private void method_3(bool bool_0)
		{
			this.checkBox_1.Enabled = bool_0;
			this.checkBox_3.Enabled = bool_0;
			this.checkBox_4.Enabled = bool_0;
		}

		// Token: 0x06000D63 RID: 3427 RVA: 0x00005F25 File Offset: 0x00004125
		private void BlindTestForm_Load(object sender, EventArgs e)
		{
			this.labelX_0.Text = BlindTestForm.string_0;
			this.radioButton_0.Checked = true;
			this.list_0 = new List<StkSymbol>();
			this.method_4();
		}

		// Token: 0x06000D64 RID: 3428 RVA: 0x000041B9 File Offset: 0x000023B9
		private void BlindTestForm_Shown(object sender, EventArgs e)
		{
		}

		// Token: 0x06000D65 RID: 3429 RVA: 0x00005F56 File Offset: 0x00004156
		private void method_4()
		{
			this.method_5();
			this.method_6();
		}

		// Token: 0x06000D66 RID: 3430 RVA: 0x00054C84 File Offset: 0x00052E84
		private void method_5()
		{
			List<StkSymbol> list = Base.Data.smethod_85(this.IsZiXuanSelected, this.checkBox_1.Enabled && this.checkBox_1.Checked, this.checkBox_3.Enabled && this.checkBox_3.Checked, this.checkBox_4.Enabled && this.checkBox_4.Checked, this.checkBox_2.Enabled && this.checkBox_2.Checked, this.checkBox_5.Enabled && this.checkBox_5.Checked, this.checkBox_6.Enabled && this.checkBox_6.Checked, this.checkBox_8.Enabled && this.checkBox_8.Checked, this.checkBox_7.Enabled && this.checkBox_7.Checked, this.checkBox_10.Enabled && this.checkBox_10.Checked, this.checkBox_9.Enabled && this.checkBox_9.Checked);
			if (!list.Any<StkSymbol>())
			{
				if (this.groupBox_0.Enabled)
				{
					List<StkSymbol> collection = Base.Data.smethod_93();
					list.AddRange(collection);
					this.checkBox_1.Checked = true;
				}
				else
				{
					List<StkSymbol> collection2 = Base.Data.smethod_101(false);
					list.AddRange(collection2);
					this.checkBox_6.Checked = true;
				}
			}
			this.list_0 = list;
		}

		// Token: 0x06000D67 RID: 3431 RVA: 0x00054E00 File Offset: 0x00053000
		private void method_6()
		{
			this.comboBox_0.Items.Clear();
			if (this.list_0.Count > 1)
			{
				this.comboBox_0.Items.Add(Class521.smethod_0(22533));
				using (List<StkSymbol>.Enumerator enumerator = this.list_0.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						StkSymbol stkSymbol = enumerator.Current;
						this.comboBox_0.Items.Add(stkSymbol.Desc);
					}
					goto IL_A1;
				}
			}
			this.comboBox_0.Items.Add(this.list_0.First<StkSymbol>().Code);
			IL_A1:
			this.comboBox_0.SelectedItem = this.comboBox_0.Items[0];
		}

		// Token: 0x06000D68 RID: 3432 RVA: 0x00054EDC File Offset: 0x000530DC
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_0.Items.Count > 1)
			{
				if (this.comboBox_0.SelectedIndex > 0)
				{
					if (this.groupBox_0.Enabled)
					{
						this.method_3(false);
					}
					if (this.groupBox_1.Enabled)
					{
						this.method_2(false);
					}
					if (this.groupBox_2.Enabled)
					{
						this.checkBox_0.Enabled = false;
					}
				}
				else
				{
					if (this.groupBox_2.Enabled)
					{
						this.checkBox_0.Enabled = true;
					}
					if (this.groupBox_0.Enabled)
					{
						this.method_3(true);
					}
					if (this.groupBox_1.Enabled)
					{
						this.method_2(true);
					}
				}
			}
		}

		// Token: 0x06000D69 RID: 3433 RVA: 0x00054F94 File Offset: 0x00053194
		private void checkBox_4_Click(object sender, EventArgs e)
		{
			CheckBox checkBox = sender as CheckBox;
			if (!checkBox.Checked)
			{
				if (this.IfNoneSelected)
				{
					checkBox.Checked = true;
				}
				else
				{
					this.method_4();
				}
			}
			else
			{
				this.method_4();
			}
		}

		// Token: 0x06000D6A RID: 3434 RVA: 0x00054FD4 File Offset: 0x000531D4
		private void button_0_Click(object sender, EventArgs e)
		{
			Class48.smethod_3(Class24.BlindTestEntering, Class521.smethod_0(22542) + Base.Data.CurrSelectedSymbol.Code);
			this.labelX_0.Text = Class521.smethod_0(22555);
			this.button_0.Enabled = false;
			this.button_1.Enabled = false;
			this.Refresh();
			StkSymbol stkSymbol = this.method_7();
			int num = 0;
			UsrStkMeta usrStkMeta;
			for (;;)
			{
				usrStkMeta = Base.Data.smethod_90(stkSymbol.ID);
				if (this.comboBox_0.SelectedIndex > 0)
				{
					break;
				}
				if (usrStkMeta != null && (usrStkMeta.EndDate.Value.Date - usrStkMeta.BeginDate.Value.Date).TotalDays >= (double)SymbMgr.MinBlindTestDays)
				{
					goto IL_135;
				}
				int seed = Environment.TickCount + num;
				this.random_0 = new Random(seed);
				stkSymbol = this.method_7();
				num++;
			}
			if (usrStkMeta == null)
			{
				this.labelX_0.Text = BlindTestForm.string_0;
				MessageBox.Show(Class521.smethod_0(22608), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				this.button_0.Enabled = true;
				this.button_1.Enabled = true;
				return;
			}
			IL_135:
			this.method_8(usrStkMeta, stkSymbol);
			this.button_0.Enabled = false;
			this.button_1.Enabled = false;
			this.labelX_0.Enabled = false;
			base.Close();
		}

		// Token: 0x06000D6B RID: 3435 RVA: 0x0005514C File Offset: 0x0005334C
		private StkSymbol method_7()
		{
			StkSymbol result = Base.Data.CurrSelectedSymbol;
			if (this.list_0.Count > 1)
			{
				if (this.comboBox_0.SelectedIndex > 0)
				{
					result = this.list_0[this.comboBox_0.SelectedIndex - 1];
					Base.UI.Form.IsSingleBlindTest = true;
				}
				else
				{
					int num = this.random_0.Next(0, this.list_0.Count);
					if (num == this.list_0.Count)
					{
						num = this.list_0.Count - 1;
					}
					result = this.list_0[num];
					Base.UI.Form.IsSingleBlindTest = false;
				}
			}
			else
			{
				Base.UI.Form.IsSingleBlindTest = true;
			}
			return result;
		}

		// Token: 0x06000D6C RID: 3436 RVA: 0x00055204 File Offset: 0x00053404
		private void method_8(UsrStkMeta usrStkMeta_0, StkSymbol stkSymbol_0)
		{
			double num = (usrStkMeta_0.EndDate.Value.Date - usrStkMeta_0.BeginDate.Value.Date).TotalDays;
			if (num < 1.0)
			{
				num = 1.0;
			}
			double d = num;
			if (num > 480.0)
			{
				d = num - 240.0;
			}
			else if (num > 240.0)
			{
				d = num - 120.0;
			}
			else if (num > 120.0)
			{
				d = num - 120.0;
			}
			else if (num > 60.0)
			{
				d = num - 30.0;
			}
			DateTime dateTime = usrStkMeta_0.BeginDate.Value.AddDays((double)this.random_0.Next(0, Convert.ToInt32(Math.Floor(d))));
			ExchgOBT exchgOBT = Base.Data.smethod_110(stkSymbol_0, dateTime);
			SymbNtTrDate symbNtTrDate = Base.Data.smethod_112(stkSymbol_0, dateTime);
			if (this.radioButton_0.Checked)
			{
				if (exchgOBT.DayCloseTime != null && exchgOBT.DayOpenTime != null)
				{
					bool withNightData = symbNtTrDate != null;
					dateTime = dateTime.Date.Add(exchgOBT.GetRandomTradingDT(withNightData, this.random_0).TimeOfDay);
				}
			}
			else
			{
				dateTime = dateTime.Date.Add(exchgOBT.DayOpenTime.Value.TimeOfDay);
			}
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				Base.UI.Form.IsInBlindTestMode = true;
				Base.UI.Form.LastSymbIDPriorToBlindTest = new int?(Base.Data.CurrSelectedSymbol.ID);
				Base.UI.Form.LastSymbDTPriorToBlindTest = new DateTime?(Base.Data.CurrDate);
			}
			if (this.groupBox_2.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfNoZixuan = !this.checkBox_0.Checked;
			}
			if (this.groupBox_1.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfStIdx = this.checkBox_2.Checked;
				Base.UI.Form.BlindTestForm_IfStFond = this.checkBox_5.Checked;
				Base.UI.Form.BlindTestForm_IfNoStMain = !this.checkBox_6.Checked;
				Base.UI.Form.BlindTestForm_IfNoStZX = !this.checkBox_8.Checked;
				Base.UI.Form.BlindTestForm_IfNoStCYB = !this.checkBox_7.Checked;
				Base.UI.Form.BlindTestForm_IfNoStKCB = !this.checkBox_10.Checked;
				Base.UI.Form.BlindTestForm_IfStConvBond = this.checkBox_9.Checked;
			}
			if (this.groupBox_0.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfNoFtMI = !this.checkBox_1.Checked;
				Base.UI.Form.BlindTestForm_IfFtIdx = this.checkBox_3.Checked;
				Base.UI.Form.BlindTestForm_IfFtMth = this.checkBox_4.Checked;
			}
			Base.UI.smethod_47();
			this.method_0(Base.Data.CurrSelectedSymbol.ID, stkSymbol_0.ID, new DateTime?(dateTime));
		}

		// Token: 0x06000D6D RID: 3437 RVA: 0x00005F66 File Offset: 0x00004166
		private void method_9()
		{
			MessageBox.Show(Class521.smethod_0(22701), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06000D6E RID: 3438 RVA: 0x00055524 File Offset: 0x00053724
		private int method_10(GroupBox groupBox_3)
		{
			return groupBox_3.Controls.OfType<CheckBox>().Where(new Func<CheckBox, bool>(BlindTestForm.<>c.<>9.method_0)).Count<CheckBox>();
		}

		// Token: 0x1700021B RID: 539
		// (get) Token: 0x06000D6F RID: 3439 RVA: 0x0005556C File Offset: 0x0005376C
		private bool IsNoFtSeleted
		{
			get
			{
				return this.method_10(this.groupBox_0) == 0;
			}
		}

		// Token: 0x1700021C RID: 540
		// (get) Token: 0x06000D70 RID: 3440 RVA: 0x0005558C File Offset: 0x0005378C
		private bool IsNoStSeleted
		{
			get
			{
				return this.method_10(this.groupBox_1) == 0;
			}
		}

		// Token: 0x1700021D RID: 541
		// (get) Token: 0x06000D71 RID: 3441 RVA: 0x000555AC File Offset: 0x000537AC
		private bool IsZiXuanSelected
		{
			get
			{
				bool result;
				if (this.checkBox_0.Enabled)
				{
					result = this.checkBox_0.Checked;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700021E RID: 542
		// (get) Token: 0x06000D72 RID: 3442 RVA: 0x000555DC File Offset: 0x000537DC
		private bool IfNoneSelected
		{
			get
			{
				bool result;
				if (this.IsNoFtSeleted && this.IsNoStSeleted)
				{
					result = !this.IsZiXuanSelected;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x06000D73 RID: 3443 RVA: 0x00005F87 File Offset: 0x00004187
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000D74 RID: 3444 RVA: 0x0005560C File Offset: 0x0005380C
		private void method_11()
		{
			this.label_0 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.labelX_0 = new LabelX();
			this.label_1 = new Label();
			this.panel_0 = new Panel();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.checkBox_1 = new CheckBox();
			this.checkBox_0 = new CheckBox();
			this.comboBox_0 = new ComboBox();
			this.groupBox_2 = new GroupBox();
			this.groupBox_1 = new GroupBox();
			this.checkBox_9 = new CheckBox();
			this.checkBox_10 = new CheckBox();
			this.checkBox_8 = new CheckBox();
			this.checkBox_7 = new CheckBox();
			this.checkBox_6 = new CheckBox();
			this.checkBox_5 = new CheckBox();
			this.checkBox_2 = new CheckBox();
			this.groupBox_0 = new GroupBox();
			this.checkBox_4 = new CheckBox();
			this.checkBox_3 = new CheckBox();
			this.pictureBox_0 = new PictureBox();
			this.panel_0.SuspendLayout();
			this.groupBox_2.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			this.groupBox_0.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_0.Location = new Point(60, 107);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(84, 20);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(22750);
			this.button_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.button_0.Location = new Point(424, 410);
			this.button_0.Name = Class521.smethod_0(10838);
			this.button_0.Size = new Size(120, 35);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(22771);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.button_1.Location = new Point(558, 410);
			this.button_1.Name = Class521.smethod_0(10825);
			this.button_1.Size = new Size(120, 35);
			this.button_1.TabIndex = 5;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.labelX_0.Location = new Point(89, 327);
			this.labelX_0.Name = Class521.smethod_0(16201);
			this.labelX_0.Size = new Size(591, 73);
			this.labelX_0.Style = eDotNetBarStyle.Office2003;
			this.labelX_0.TabIndex = 8;
			this.labelX_0.Text = Class521.smethod_0(16218);
			this.labelX_0.TextLineAlignment = StringAlignment.Near;
			this.labelX_0.WordWrap = true;
			this.label_1.AutoSize = true;
			this.label_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_1.Location = new Point(60, 69);
			this.label_1.Name = Class521.smethod_0(5849);
			this.label_1.Size = new Size(84, 20);
			this.label_1.TabIndex = 9;
			this.label_1.Text = Class521.smethod_0(22780);
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Location = new Point(154, 56);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(337, 40);
			this.panel_0.TabIndex = 10;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.radioButton_0.Location = new Point(3, 12);
			this.radioButton_0.Name = Class521.smethod_0(22801);
			this.radioButton_0.Size = new Size(90, 24);
			this.radioButton_0.TabIndex = 1;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(22830);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.radioButton_1.Location = new Point(156, 12);
			this.radioButton_1.Name = Class521.smethod_0(22847);
			this.radioButton_1.Size = new Size(90, 24);
			this.radioButton_1.TabIndex = 0;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(22872);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.label_2.AutoSize = true;
			this.label_2.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_2.Location = new Point(154, 32);
			this.label_2.Name = Class521.smethod_0(7019);
			this.label_2.Size = new Size(39, 20);
			this.label_2.TabIndex = 13;
			this.label_2.Text = Class521.smethod_0(22533);
			this.label_3.AutoSize = true;
			this.label_3.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_3.Location = new Point(60, 32);
			this.label_3.Name = Class521.smethod_0(5827);
			this.label_3.Size = new Size(84, 20);
			this.label_3.TabIndex = 12;
			this.label_3.Text = Class521.smethod_0(19821);
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.Checked = true;
			this.checkBox_1.CheckState = CheckState.Checked;
			this.checkBox_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_1.Location = new Point(21, 28);
			this.checkBox_1.Name = Class521.smethod_0(22889);
			this.checkBox_1.Size = new Size(91, 24);
			this.checkBox_1.TabIndex = 15;
			this.checkBox_1.Text = Class521.smethod_0(22906);
			this.checkBox_1.UseVisualStyleBackColor = true;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Checked = true;
			this.checkBox_0.CheckState = CheckState.Checked;
			this.checkBox_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_0.Location = new Point(25, 28);
			this.checkBox_0.Name = Class521.smethod_0(22923);
			this.checkBox_0.Size = new Size(91, 24);
			this.checkBox_0.TabIndex = 14;
			this.checkBox_0.Text = Class521.smethod_0(22944);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(156, 106);
			this.comboBox_0.Name = Class521.smethod_0(22961);
			this.comboBox_0.Size = new Size(202, 23);
			this.comboBox_0.TabIndex = 11;
			this.groupBox_2.Controls.Add(this.checkBox_0);
			this.groupBox_2.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.groupBox_2.Location = new Point(516, 143);
			this.groupBox_2.Name = Class521.smethod_0(22982);
			this.groupBox_2.Size = new Size(163, 67);
			this.groupBox_2.TabIndex = 21;
			this.groupBox_2.TabStop = false;
			this.groupBox_2.Text = Class521.smethod_0(23003);
			this.groupBox_1.Controls.Add(this.checkBox_9);
			this.groupBox_1.Controls.Add(this.checkBox_10);
			this.groupBox_1.Controls.Add(this.checkBox_8);
			this.groupBox_1.Controls.Add(this.checkBox_7);
			this.groupBox_1.Controls.Add(this.checkBox_6);
			this.groupBox_1.Controls.Add(this.checkBox_5);
			this.groupBox_1.Controls.Add(this.checkBox_2);
			this.groupBox_1.Location = new Point(54, 217);
			this.groupBox_1.Name = Class521.smethod_0(23012);
			this.groupBox_1.Size = new Size(624, 102);
			this.groupBox_1.TabIndex = 20;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(23029);
			this.checkBox_9.AutoSize = true;
			this.checkBox_9.Checked = true;
			this.checkBox_9.CheckState = CheckState.Checked;
			this.checkBox_9.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_9.Location = new Point(291, 61);
			this.checkBox_9.Name = Class521.smethod_0(23038);
			this.checkBox_9.Size = new Size(76, 24);
			this.checkBox_9.TabIndex = 23;
			this.checkBox_9.Text = Class521.smethod_0(23063);
			this.checkBox_9.UseVisualStyleBackColor = true;
			this.checkBox_10.AutoSize = true;
			this.checkBox_10.Checked = true;
			this.checkBox_10.CheckState = CheckState.Checked;
			this.checkBox_10.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_10.Location = new Point(156, 61);
			this.checkBox_10.Name = Class521.smethod_0(23076);
			this.checkBox_10.Size = new Size(76, 24);
			this.checkBox_10.TabIndex = 22;
			this.checkBox_10.Text = Class521.smethod_0(23093);
			this.checkBox_10.UseVisualStyleBackColor = true;
			this.checkBox_8.AutoSize = true;
			this.checkBox_8.Checked = true;
			this.checkBox_8.CheckState = CheckState.Checked;
			this.checkBox_8.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_8.Location = new Point(434, 25);
			this.checkBox_8.Name = Class521.smethod_0(23106);
			this.checkBox_8.Size = new Size(76, 24);
			this.checkBox_8.TabIndex = 21;
			this.checkBox_8.Text = Class521.smethod_0(23135);
			this.checkBox_8.UseVisualStyleBackColor = true;
			this.checkBox_7.AutoSize = true;
			this.checkBox_7.Checked = true;
			this.checkBox_7.CheckState = CheckState.Checked;
			this.checkBox_7.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_7.Location = new Point(21, 61);
			this.checkBox_7.Name = Class521.smethod_0(23148);
			this.checkBox_7.Size = new Size(76, 24);
			this.checkBox_7.TabIndex = 20;
			this.checkBox_7.Text = Class521.smethod_0(23177);
			this.checkBox_7.UseVisualStyleBackColor = true;
			this.checkBox_6.AutoSize = true;
			this.checkBox_6.Checked = true;
			this.checkBox_6.CheckState = CheckState.Checked;
			this.checkBox_6.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_6.Location = new Point(291, 25);
			this.checkBox_6.Name = Class521.smethod_0(23190);
			this.checkBox_6.Size = new Size(61, 24);
			this.checkBox_6.TabIndex = 19;
			this.checkBox_6.Text = Class521.smethod_0(23215);
			this.checkBox_6.UseVisualStyleBackColor = true;
			this.checkBox_5.AutoSize = true;
			this.checkBox_5.Checked = true;
			this.checkBox_5.CheckState = CheckState.Checked;
			this.checkBox_5.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_5.Location = new Point(156, 25);
			this.checkBox_5.Name = Class521.smethod_0(23224);
			this.checkBox_5.Size = new Size(61, 24);
			this.checkBox_5.TabIndex = 18;
			this.checkBox_5.Text = Class521.smethod_0(23245);
			this.checkBox_5.UseVisualStyleBackColor = true;
			this.checkBox_2.AutoSize = true;
			this.checkBox_2.Checked = true;
			this.checkBox_2.CheckState = CheckState.Checked;
			this.checkBox_2.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_2.Location = new Point(21, 25);
			this.checkBox_2.Name = Class521.smethod_0(23254);
			this.checkBox_2.Size = new Size(61, 24);
			this.checkBox_2.TabIndex = 17;
			this.checkBox_2.Text = Class521.smethod_0(23271);
			this.checkBox_2.UseVisualStyleBackColor = true;
			this.groupBox_0.Controls.Add(this.checkBox_4);
			this.groupBox_0.Controls.Add(this.checkBox_1);
			this.groupBox_0.Controls.Add(this.checkBox_3);
			this.groupBox_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.groupBox_0.Location = new Point(54, 143);
			this.groupBox_0.Name = Class521.smethod_0(23280);
			this.groupBox_0.Size = new Size(436, 67);
			this.groupBox_0.TabIndex = 19;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(23297);
			this.checkBox_4.AutoSize = true;
			this.checkBox_4.Checked = true;
			this.checkBox_4.CheckState = CheckState.Checked;
			this.checkBox_4.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_4.Location = new Point(291, 28);
			this.checkBox_4.Name = Class521.smethod_0(23306);
			this.checkBox_4.Size = new Size(91, 24);
			this.checkBox_4.TabIndex = 19;
			this.checkBox_4.Text = Class521.smethod_0(23327);
			this.checkBox_4.UseVisualStyleBackColor = true;
			this.checkBox_3.AutoSize = true;
			this.checkBox_3.Checked = true;
			this.checkBox_3.CheckState = CheckState.Checked;
			this.checkBox_3.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.checkBox_3.Location = new Point(156, 28);
			this.checkBox_3.Name = Class521.smethod_0(23344);
			this.checkBox_3.Size = new Size(91, 24);
			this.checkBox_3.TabIndex = 18;
			this.checkBox_3.Text = Class521.smethod_0(23361);
			this.checkBox_3.UseVisualStyleBackColor = true;
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(65, 326);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 20;
			this.pictureBox_0.TabStop = false;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(731, 456);
			base.Controls.Add(this.groupBox_2);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.comboBox_0);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.labelX_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(23378);
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(23399);
			base.Load += this.BlindTestForm_Load;
			base.Shown += this.BlindTestForm_Shown;
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			this.groupBox_2.ResumeLayout(false);
			this.groupBox_2.PerformLayout();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			((ISupportInitialize)this.pictureBox_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040006BB RID: 1723
		[CompilerGenerated]
		private Delegate3 delegate3_0;

		// Token: 0x040006BC RID: 1724
		private static readonly string string_0 = Class521.smethod_0(23416);

		// Token: 0x040006BD RID: 1725
		private List<StkSymbol> list_0;

		// Token: 0x040006BE RID: 1726
		private List<StkSymbol> list_1;

		// Token: 0x040006BF RID: 1727
		private Random random_0;

		// Token: 0x040006C0 RID: 1728
		private IContainer icontainer_0;

		// Token: 0x040006C1 RID: 1729
		private Label label_0;

		// Token: 0x040006C2 RID: 1730
		private Button button_0;

		// Token: 0x040006C3 RID: 1731
		private Button button_1;

		// Token: 0x040006C4 RID: 1732
		private LabelX labelX_0;

		// Token: 0x040006C5 RID: 1733
		private Label label_1;

		// Token: 0x040006C6 RID: 1734
		private Panel panel_0;

		// Token: 0x040006C7 RID: 1735
		private RadioButton radioButton_0;

		// Token: 0x040006C8 RID: 1736
		private RadioButton radioButton_1;

		// Token: 0x040006C9 RID: 1737
		private ComboBox comboBox_0;

		// Token: 0x040006CA RID: 1738
		private Label label_2;

		// Token: 0x040006CB RID: 1739
		private Label label_3;

		// Token: 0x040006CC RID: 1740
		private CheckBox checkBox_0;

		// Token: 0x040006CD RID: 1741
		private CheckBox checkBox_1;

		// Token: 0x040006CE RID: 1742
		private PictureBox pictureBox_0;

		// Token: 0x040006CF RID: 1743
		private CheckBox checkBox_2;

		// Token: 0x040006D0 RID: 1744
		private CheckBox checkBox_3;

		// Token: 0x040006D1 RID: 1745
		private GroupBox groupBox_0;

		// Token: 0x040006D2 RID: 1746
		private CheckBox checkBox_4;

		// Token: 0x040006D3 RID: 1747
		private GroupBox groupBox_1;

		// Token: 0x040006D4 RID: 1748
		private CheckBox checkBox_5;

		// Token: 0x040006D5 RID: 1749
		private CheckBox checkBox_6;

		// Token: 0x040006D6 RID: 1750
		private CheckBox checkBox_7;

		// Token: 0x040006D7 RID: 1751
		private CheckBox checkBox_8;

		// Token: 0x040006D8 RID: 1752
		private GroupBox groupBox_2;

		// Token: 0x040006D9 RID: 1753
		private CheckBox checkBox_9;

		// Token: 0x040006DA RID: 1754
		private CheckBox checkBox_10;
	}
}
