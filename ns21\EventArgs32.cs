﻿using System;
using System.Runtime.CompilerServices;
using ns17;
using TEx.SIndicator;

namespace ns21
{
	// Token: 0x02000303 RID: 771
	internal sealed class EventArgs32 : EventArgs
	{
		// Token: 0x170005C2 RID: 1474
		// (get) Token: 0x06002155 RID: 8533 RVA: 0x000EC9E8 File Offset: 0x000EABE8
		// (set) Token: 0x06002156 RID: 8534 RVA: 0x0000D705 File Offset: 0x0000B905
		public FormIndEditer IndEditer { get; private set; }

		// Token: 0x170005C3 RID: 1475
		// (get) Token: 0x06002157 RID: 8535 RVA: 0x000ECA00 File Offset: 0x000EAC00
		// (set) Token: 0x06002158 RID: 8536 RVA: 0x0000D710 File Offset: 0x0000B910
		public Enum25 DoType { get; private set; }

		// Token: 0x06002159 RID: 8537 RVA: 0x0000D71B File Offset: 0x0000B91B
		public EventArgs32(FormIndEditer formIndEditer_1, Enum25 enum25_1)
		{
			this.IndEditer = formIndEditer_1;
			this.DoType = enum25_1;
		}

		// Token: 0x04001037 RID: 4151
		[CompilerGenerated]
		private FormIndEditer formIndEditer_0;

		// Token: 0x04001038 RID: 4152
		[CompilerGenerated]
		private Enum25 enum25_0;
	}
}
