﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000075 RID: 117
	[Serializable]
	internal sealed class DrawLineDH : DrawObj, ISerializable
	{
		// Token: 0x06000435 RID: 1077 RVA: 0x00003742 File Offset: 0x00001942
		public DrawLineDH()
		{
		}

		// Token: 0x06000436 RID: 1078 RVA: 0x00003CE7 File Offset: 0x00001EE7
		public DrawLineDH(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y1, false)
		{
		}

		// Token: 0x06000437 RID: 1079 RVA: 0x00003CF6 File Offset: 0x00001EF6
		public DrawLineDH(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc) : base(chart, x1, y1, x2, y1, disableRstPrc)
		{
			base.Name = Class521.smethod_0(5255);
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000438 RID: 1080 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawLineDH(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000439 RID: 1081 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600043A RID: 1082 RVA: 0x0002389C File Offset: 0x00021A9C
		protected override Location vmethod_3(double double_1, double double_2, double double_3, double double_4)
		{
			return new Location(double_1, double_2, double_3 - double_1, 0.0, CoordType.AxisXYScale, AlignH.Left, AlignV.Top);
		}

		// Token: 0x0600043B RID: 1083 RVA: 0x000238C4 File Offset: 0x00021AC4
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_23(double_1, double_2, double_3, double_2, string_5);
			list.Add(item);
			return list;
		}
	}
}
