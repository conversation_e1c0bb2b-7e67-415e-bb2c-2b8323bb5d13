﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq.Expressions;
using System.Windows.Forms;
using System.Xml.Linq;
using DevComponents.AdvTree;
using ns11;
using ns18;
using ns20;
using ns26;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200005C RID: 92
	internal static class BaoDianMgr
	{
		// Token: 0x0600032C RID: 812 RVA: 0x0001EC04 File Offset: 0x0001CE04
		public static List<Node> smethod_0()
		{
			List<Node> list = new List<Node>();
			string text = Path.Combine(TApp.UserAcctFolder, BaoDianMgr.string_0);
			if (File.Exists(text))
			{
				List<Node> result;
				try
				{
					XDocument xdocument = XDocument.Load(text);
					if (xdocument == null)
					{
						goto IL_18F;
					}
					foreach (XElement xelement in xdocument.Root.Elements())
					{
						if (xelement.Name == Class521.smethod_0(4758))
						{
							Node node = BaoDianMgr.smethod_1(xelement);
							list.Add(node);
							foreach (XElement xelement2 in xelement.Elements())
							{
								if (xelement2.Name == Class521.smethod_0(4758))
								{
									Node node2 = BaoDianMgr.smethod_1(xelement2);
									node.Nodes.Add(node2);
									using (IEnumerator<XElement> enumerator3 = xelement2.Elements().GetEnumerator())
									{
										while (enumerator3.MoveNext())
										{
											XElement xelement_ = enumerator3.Current;
											Node node3 = BaoDianMgr.smethod_3(xelement_);
											node2.Nodes.Add(node3);
										}
										continue;
									}
								}
								if (xelement2.Name == Class521.smethod_0(4767))
								{
									Node node4 = BaoDianMgr.smethod_3(xelement2);
									node.Nodes.Add(node4);
								}
							}
						}
					}
					result = list;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					goto IL_18F;
				}
				return result;
			}
			IL_18F:
			list.Add(BaoDianMgr.smethod_2(Class521.smethod_0(4776)));
			return list;
		}

		// Token: 0x0600032D RID: 813 RVA: 0x0001EE20 File Offset: 0x0001D020
		private static Node smethod_1(XElement xelement_0)
		{
			return BaoDianMgr.smethod_2(xelement_0.Attribute(Class521.smethod_0(4793)).Value.ToString());
		}

		// Token: 0x0600032E RID: 814 RVA: 0x0001EE58 File Offset: 0x0001D058
		public static Node smethod_2(string string_1)
		{
			return new Node
			{
				Text = string_1,
				Image = Class375.FolderClosed,
				ImageExpanded = Class375.FolderOpen,
				ExpandVisibility = eNodeExpandVisibility.Auto
			};
		}

		// Token: 0x0600032F RID: 815 RVA: 0x0001EE94 File Offset: 0x0001D094
		private static Node smethod_3(XElement xelement_0)
		{
			return BaoDianMgr.smethod_4(BaoDianMgr.smethod_5(xelement_0));
		}

		// Token: 0x06000330 RID: 816 RVA: 0x0001EEB0 File Offset: 0x0001D0B0
		public static Node smethod_4(BaoDian baoDian_0)
		{
			Node result;
			if (baoDian_0 != null)
			{
				Node node = new Node();
				node.Text = baoDian_0.Name;
				node.Image = Class375.Book_angleHS;
				node.Tag = baoDian_0;
				node.Tooltip = Class521.smethod_0(4802);
				if (Base.Data.UsrStkSymbols.ContainsKey(baoDian_0.SymbolID))
				{
					string cnname = Base.Data.UsrStkSymbols[baoDian_0.SymbolID].CNName;
					node.Cells.Add(new Cell(cnname));
				}
				node.Cells.Add(new Cell(baoDian_0.SymbolTime.ToString(Class521.smethod_0(1702))));
				result = node;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000331 RID: 817 RVA: 0x0001EF64 File Offset: 0x0001D164
		private static BaoDian smethod_5(XElement xelement_0)
		{
			BaoDian baoDian = new BaoDian();
			try
			{
				BaoDian baoDian2 = baoDian;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				baoDian2.Group = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Group())), new ParameterExpression[]
				{
					parameterExpression
				}))).Value.ToString();
				BaoDian baoDian3 = baoDian;
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				baoDian3.Name = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Name())), new ParameterExpression[]
				{
					parameterExpression
				}))).Value.ToString();
				BaoDian baoDian4 = baoDian;
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				baoDian4.UID = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_UID())), new ParameterExpression[]
				{
					parameterExpression
				}))).Value.ToString();
				BaoDian baoDian5 = baoDian;
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				baoDian5.Note = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Note())), new ParameterExpression[]
				{
					parameterExpression
				}))).Value.ToString();
				BaoDian baoDian6 = baoDian;
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4840));
				baoDian6.SymbolID = int.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_SymbolID())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))).Value);
				BaoDian baoDian7 = baoDian;
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4840));
				baoDian7.SymbolTime = DateTime.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_SymbolTime())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))).Value);
				BaoDian baoDian8 = baoDian;
				Type typeFromHandle = typeof(PeriodType);
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4840));
				baoDian8.PeriodType = (PeriodType)Enum.Parse(typeFromHandle, xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_PeriodType())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))).Value);
				try
				{
					BaoDian baoDian9 = baoDian;
					parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4840));
					baoDian9.PeriodUnit = new int?(int.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_PeriodUnit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))).Value));
				}
				catch
				{
					baoDian.PeriodUnit = null;
				}
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				XAttribute xattribute = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_ScreenShot())), new ParameterExpression[]
				{
					parameterExpression
				})));
				if (xattribute != null && !string.IsNullOrEmpty(xattribute.Value))
				{
					baoDian.ScreenShot = Utility.Base64StrToBitmap(xattribute.Value, false, CompressAlgm.GZip);
				}
				parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
				string text = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_CreateTime())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))).Value.ToString();
				try
				{
					baoDian.CreateTime = DateTime.Parse(text);
				}
				catch
				{
					try
					{
						int year = int.Parse(text.Substring(0, 4));
						int month = int.Parse(text.Substring(4, 2));
						int day = int.Parse(text.Substring(6, 2));
						int hour = int.Parse(text.Substring(8, 2));
						int minute = int.Parse(text.Substring(10, 2));
						int second = int.Parse(text.Substring(12, 2));
						baoDian.CreateTime = new DateTime(year, month, day, hour, minute, second);
					}
					catch
					{
					}
				}
			}
			catch (Exception exception_)
			{
				Class48.smethod_4(exception_, false, Class521.smethod_0(4849));
				baoDian = null;
			}
			return baoDian;
		}

		// Token: 0x06000332 RID: 818 RVA: 0x0001F4B0 File Offset: 0x0001D6B0
		public static void smethod_6(ChtCtrl chtCtrl_0, string string_1 = null)
		{
			try
			{
				Bitmap bitmap = new Bitmap(chtCtrl_0.ClientSize.Width, chtCtrl_0.ClientSize.Height);
				chtCtrl_0.DrawToBitmap(bitmap, new Rectangle(0, 0, chtCtrl_0.ClientRectangle.Width, chtCtrl_0.ClientRectangle.Height));
				if (bitmap.Width > 600)
				{
					bitmap = Utility.SizeImage(bitmap, 600, Convert.ToInt32(Math.Round((double)(bitmap.Height * 600) * 1.0 / (double)bitmap.Width)));
				}
				Bitmap bitmap_ = bitmap.Clone(new Rectangle(0, 0, bitmap.Width, bitmap.Height), PixelFormat.Format4bppIndexed);
				Form0 form = new Form0();
				TransTabs transTabs = Base.UI.TransTabs;
				if (transTabs == null)
				{
					transTabs = Base.UI.SwitchedBehindTransTabs;
				}
				List<string> list_ = transTabs.method_94();
				form.method_0(chtCtrl_0, list_, bitmap_, string_1);
				form.StartPosition = FormStartPosition.CenterParent;
				form.ShowDialog(Base.UI.MainForm);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06000333 RID: 819 RVA: 0x0001F5C0 File Offset: 0x0001D7C0
		public static void smethod_7(Node node_0)
		{
			Form0 form = new Form0();
			List<string> list_ = Base.UI.TransTabs.method_94();
			form.method_1(node_0, list_);
			form.StartPosition = FormStartPosition.CenterParent;
			form.ShowDialog(Base.UI.MainForm);
		}

		// Token: 0x06000334 RID: 820 RVA: 0x00003605 File Offset: 0x00001805
		public static void smethod_8()
		{
			BaoDianMgr.smethod_9();
		}

		// Token: 0x06000335 RID: 821 RVA: 0x0001F5FC File Offset: 0x0001D7FC
		private static void smethod_9()
		{
			if (Base.UI.TransTabs != null)
			{
				string fileName = Path.Combine(TApp.UserAcctFolder, BaoDianMgr.string_0);
				XDocument xdocument = new XDocument();
				XElement xelement = new XElement(Class521.smethod_0(4886));
				xdocument.Add(xelement);
				foreach (object obj in Base.UI.TransTabs.method_95().Nodes)
				{
					Node node = (Node)obj;
					XElement xelement2 = BaoDianMgr.smethod_10(node);
					foreach (object obj2 in node.Nodes)
					{
						Node node2 = (Node)obj2;
						BaoDian baoDian = node2.Tag as BaoDian;
						if (baoDian == null)
						{
							XElement xelement3 = BaoDianMgr.smethod_10(node2);
							foreach (object obj3 in node2.Nodes)
							{
								XElement content = BaoDianMgr.smethod_11(((Node)obj3).Tag as BaoDian);
								xelement3.Add(content);
							}
							xelement2.Add(xelement3);
						}
						else
						{
							XElement content2 = BaoDianMgr.smethod_11(baoDian);
							xelement2.Add(content2);
						}
					}
					xelement.Add(xelement2);
				}
				xdocument.Save(fileName);
			}
		}

		// Token: 0x06000336 RID: 822 RVA: 0x0001F7C4 File Offset: 0x0001D9C4
		private static XElement smethod_10(Node node_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(4758));
			xelement.Add(new XAttribute(Class521.smethod_0(4793), node_0.Text));
			xelement.Add(new XAttribute(Class521.smethod_0(4895), node_0.FullPath));
			return xelement;
		}

		// Token: 0x06000337 RID: 823 RVA: 0x0001F82C File Offset: 0x0001DA2C
		private static XElement smethod_11(BaoDian baoDian_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(4767));
			XContainer xcontainer = xelement;
			ParameterExpression parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Group())), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.Group));
			XContainer xcontainer2 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer2.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Name())), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.Name));
			if (string.IsNullOrEmpty(baoDian_0.UID))
			{
				baoDian_0.UID = Utility.GetUniqueString(20);
			}
			XContainer xcontainer3 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer3.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_UID())), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.UID));
			XContainer xcontainer4 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer4.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_SymbolID())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.SymbolID));
			XContainer xcontainer5 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer5.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_SymbolTime())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.SymbolTime.ToString(Class521.smethod_0(4908))));
			XContainer xcontainer6 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer6.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_PeriodType())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.PeriodType));
			XContainer xcontainer7 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer7.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_PeriodUnit())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})), (baoDian_0.PeriodUnit == null) ? Class521.smethod_0(4933) : baoDian_0.PeriodUnit.ToString()));
			XContainer xcontainer8 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer8.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_Note())), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.Note));
			XContainer xcontainer9 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer9.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(BaoDian.get_CreateTime())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			})), baoDian_0.CreateTime.ToString(Class521.smethod_0(4938))));
			string value = Utility.BitmapToBase64Str(baoDian_0.ScreenShot, false, CompressAlgm.GZip);
			XContainer xcontainer10 = xelement;
			parameterExpression = Expression.Parameter(typeof(BaoDian), Class521.smethod_0(4835));
			xcontainer10.Add(new XAttribute(Utility.GetPropertyName<BaoDian>(Expression.Lambda<Func<BaoDian, object>>(Expression.Property(parameterExpression, methodof(BaoDian.get_ScreenShot())), new ParameterExpression[]
			{
				parameterExpression
			})), value));
			return xelement;
		}

		// Token: 0x04000122 RID: 290
		public static Action<BaoDian> action_0;

		// Token: 0x04000123 RID: 291
		public static Action<Node> action_1;

		// Token: 0x04000124 RID: 292
		public static string string_0 = Class521.smethod_0(4741);
	}
}
