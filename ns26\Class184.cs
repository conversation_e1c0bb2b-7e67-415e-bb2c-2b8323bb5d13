﻿using System;
using System.Windows.Forms;
using ns18;
using ns20;
using TEx;

namespace ns26
{
	// Token: 0x0200012F RID: 303
	internal static class Class184
	{
		// Token: 0x06000C8B RID: 3211 RVA: 0x0004A9C0 File Offset: 0x00048BC0
		public static void smethod_0(Exception exception_0)
		{
			Class184.smethod_1(exception_0, null);
		}

		// Token: 0x06000C8C RID: 3212 RVA: 0x0004A9E0 File Offset: 0x00048BE0
		public static void smethod_1(Exception exception_0, bool? nullable_0 = null)
		{
			string text = Environment.NewLine + Class521.smethod_0(11847);
			try
			{
				if (TApp.EnteredMainForm)
				{
					text = string.Concat(new string[]
					{
						text,
						Class521.smethod_0(11876),
						Base.Data.CurrDate.ToString(),
						Class521.smethod_0(11893),
						Base.Data.smethod_127()
					});
				}
			}
			catch
			{
			}
			Class48.smethod_4(exception_0, true, text);
			bool flag = false;
			if (nullable_0 != null)
			{
				flag = nullable_0.Value;
			}
			if (flag)
			{
				throw exception_0;
			}
		}

		// Token: 0x06000C8D RID: 3213 RVA: 0x0004AA84 File Offset: 0x00048C84
		public static void smethod_2(Exception exception_0, bool bool_0 = false)
		{
			string text = string.Concat(new string[]
			{
				Class521.smethod_0(11898),
				Environment.NewLine,
				exception_0.Message,
				Environment.NewLine,
				exception_0.StackTrace
			});
			if (exception_0.InnerException != null)
			{
				text = text + Environment.NewLine + exception_0.InnerException.Message;
			}
			text = text + Environment.NewLine + Environment.NewLine;
			if (bool_0)
			{
				text += Class521.smethod_0(11947);
			}
			DialogResult dialogResult = MessageBox.Show(text, Class521.smethod_0(11972), MessageBoxButtons.OKCancel, MessageBoxIcon.Hand);
			if (bool_0 && dialogResult == DialogResult.OK)
			{
				Application.Exit();
			}
		}
	}
}
