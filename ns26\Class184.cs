﻿using System;
using System.Windows.Forms;
using ns18;
using ns20;
using TEx;

namespace ns26
{
	// Token: 0x0200012F RID: 303
	internal static class ExceptionHandler
	{
		// Token: 0x06000C8B RID: 3211 RVA: 0x0004A9C0 File Offset: 0x00048BC0
		public static void HandleException(Exception exception)
		{
			ExceptionHandler.HandleException(exception, null);
		}

		// Token: 0x06000C8C RID: 3212 RVA: 0x0004A9E0 File Offset: 0x00048BE0
		public static void HandleException(Exception exception, bool? shouldRethrow = null)
		{
			string contextInfo = Environment.NewLine + StringResourceManager.GetString(11847);
			try
			{
				if (TApp.EnteredMainForm)
				{
					contextInfo = string.Concat(new string[]
					{
						contextInfo,
						StringResourceManager.GetString(11876),
						Base.Data.CurrDate.ToString(),
						StringResourceManager.GetString(11893),
						Base.Data.smethod_127()
					});
				}
			}
			catch
			{
			}
			Class48.smethod_4(exception, true, contextInfo);
			bool rethrow = false;
			if (shouldRethrow != null)
			{
				rethrow = shouldRethrow.Value;
			}
			if (rethrow)
			{
				throw exception;
			}
		}

		// Token: 0x06000C8D RID: 3213 RVA: 0x0004AA84 File Offset: 0x00048C84
		public static void ShowExceptionDialog(Exception exception, bool allowExit = false)
		{
			string errorMessage = string.Concat(new string[]
			{
				StringResourceManager.GetString(11898),
				Environment.NewLine,
				exception.Message,
				Environment.NewLine,
				exception.StackTrace
			});
			if (exception.InnerException != null)
			{
				errorMessage = errorMessage + Environment.NewLine + exception.InnerException.Message;
			}
			errorMessage = errorMessage + Environment.NewLine + Environment.NewLine;
			if (allowExit)
			{
				errorMessage += StringResourceManager.GetString(11947);
			}
			DialogResult dialogResult = MessageBox.Show(errorMessage, StringResourceManager.GetString(11972), MessageBoxButtons.OKCancel, MessageBoxIcon.Hand);
			if (allowExit && dialogResult == DialogResult.OK)
			{
				Application.Exit();
			}
		}
	}
}
