﻿using System;
using ns15;
using ns32;

namespace ns22
{
	// Token: 0x020003CF RID: 975
	internal sealed class Class517 : Class516
	{
		// Token: 0x060026F9 RID: 9977 RVA: 0x0000EFE7 File Offset: 0x0000D1E7
		public Class517() : base(UIntPtr.Zero)
		{
		}

		// Token: 0x060026FA RID: 9978 RVA: 0x0000EFF4 File Offset: 0x0000D1F4
		public Class517(UIntPtr uintptr_1) : base(uintptr_1)
		{
		}

		// Token: 0x060026FB RID: 9979 RVA: 0x0000EFFD File Offset: 0x0000D1FD
		public override void Dispose()
		{
			Class519.smethod_0(base.HKey);
		}

		// Token: 0x060026FC RID: 9980 RVA: 0x0000F00B File Offset: 0x0000D20B
		public override object vmethod_0(string string_0)
		{
			return Class519.GetValue(base.HKey, string_0);
		}

		// Token: 0x060026FD RID: 9981 RVA: 0x0000F019 File Offset: 0x0000D219
		public override bool vmethod_1(string string_0, out object object_0)
		{
			object_0 = Class519.GetValue(base.HKey, string_0);
			return object_0 != null;
		}
	}
}
