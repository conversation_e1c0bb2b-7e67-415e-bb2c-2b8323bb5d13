﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns25;
using TEx;
using TEx.Trading;

namespace ns26
{
	// Token: 0x02000215 RID: 533
	internal sealed class Class294 : Class292
	{
		// Token: 0x14000080 RID: 128
		// (add) Token: 0x060015D8 RID: 5592 RVA: 0x000965B0 File Offset: 0x000947B0
		// (remove) Token: 0x060015D9 RID: 5593 RVA: 0x000965E8 File Offset: 0x000947E8
		public event EventHandler CreateCondOrderRequested
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060015DA RID: 5594 RVA: 0x00008CE8 File Offset: 0x00006EE8
		protected void method_5()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x060015DB RID: 5595 RVA: 0x00008D03 File Offset: 0x00006F03
		public Class294()
		{
			base.RowContextMenuStripNeeded += this.Class294_RowContextMenuStripNeeded;
			base.Resize += this.Class294_Resize;
		}

		// Token: 0x060015DC RID: 5596 RVA: 0x00008D31 File Offset: 0x00006F31
		protected override void vmethod_1()
		{
			base.DataSource = Base.Trading.ShownCondOrdersList;
			this.method_7();
		}

		// Token: 0x060015DD RID: 5597 RVA: 0x00008ABA File Offset: 0x00006CBA
		public void method_6()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015DE RID: 5598 RVA: 0x00096620 File Offset: 0x00094820
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52869);
			toolStripMenuItem.Text = Class521.smethod_0(52898);
			toolStripMenuItem.Click += this.method_10;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = Class521.smethod_0(52923);
			toolStripMenuItem2.Text = Class521.smethod_0(52956);
			toolStripMenuItem2.Click += this.method_11;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015DF RID: 5599 RVA: 0x000966CC File Offset: 0x000948CC
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			DataGridViewRow dataGridViewRow = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex];
			DataGridViewCell dataGridViewCell = dataGridViewRow.Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
			if (dataGridViewCellFormattingEventArgs_0.ColumnIndex == 3)
			{
				base.method_2(dataGridViewCell);
			}
			if ((dataGridViewRow.DataBoundItem as ShownCondOrder).OrderStatus != OrderStatus.Active)
			{
				dataGridViewCellFormattingEventArgs_0.CellStyle.ForeColor = Color.Gray;
				dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = Color.Gray;
			}
			else
			{
				dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = dataGridViewCell.Style.ForeColor;
			}
		}

		// Token: 0x060015E0 RID: 5600 RVA: 0x00096758 File Offset: 0x00094958
		private void method_7()
		{
			if (base.Columns.Count > 0)
			{
				base.Columns[0].HeaderText = Class521.smethod_0(51577);
				base.Columns[0].DisplayIndex = 0;
				base.Columns[1].HeaderText = Class521.smethod_0(52973);
				base.Columns[1].DisplayIndex = 2;
				base.Columns[2].HeaderText = Class521.smethod_0(52530);
				base.Columns[2].DisplayIndex = 1;
				base.Columns[3].HeaderText = Class521.smethod_0(44797);
				base.Columns[3].DisplayIndex = 3;
				base.Columns[4].HeaderText = Class521.smethod_0(44840);
				base.Columns[4].DisplayIndex = 4;
				base.Columns[5].Visible = false;
				base.Columns[6].Visible = false;
				base.Columns[7].Visible = false;
				base.Columns[8].Visible = false;
				base.Columns[9].HeaderText = Class521.smethod_0(52990);
				base.Columns[9].DisplayIndex = 5;
				base.Columns[10].Visible = false;
				base.Columns[11].Visible = false;
				base.Columns[12].Visible = false;
				base.Columns[13].HeaderText = Class521.smethod_0(53007);
				base.Columns[13].DisplayIndex = 6;
				base.Columns[14].Visible = false;
				base.Columns[15].HeaderText = Class521.smethod_0(53024);
				base.Columns[15].DisplayIndex = 7;
				base.Columns[16].Visible = false;
				base.Columns[17].Visible = false;
				base.Columns[18].Visible = false;
				base.Columns[19].Visible = false;
				base.Columns[20].Visible = false;
				base.Columns[21].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						base.Columns[0].Visible = false;
					}
					else
					{
						base.Columns[0].Visible = true;
					}
					base.Columns[15].Visible = false;
				}
				else
				{
					base.Columns[0].Visible = true;
					base.Columns[15].Visible = true;
				}
				this.Refresh();
			}
		}

		// Token: 0x060015E1 RID: 5601 RVA: 0x00096A6C File Offset: 0x00094C6C
		private void Class294_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			dataGridViewRow.Selected = true;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(53041);
			toolStripMenuItem.Text = Class521.smethod_0(47035);
			toolStripMenuItem.Click += this.method_9;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = Class521.smethod_0(53066);
			toolStripMenuItem2.Text = Class521.smethod_0(5783);
			toolStripMenuItem2.Click += this.method_8;
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			ShownCondOrder shownCondOrder = dataGridViewRow.DataBoundItem as ShownCondOrder;
			if (shownCondOrder.OrderStatus != OrderStatus.Active)
			{
				for (int i = 0; i < e.ContextMenuStrip.Items.Count; i++)
				{
					e.ContextMenuStrip.Items[i].Enabled = false;
				}
			}
			else
			{
				for (int j = 0; j < e.ContextMenuStrip.Items.Count; j++)
				{
					e.ContextMenuStrip.Items[j].Enabled = true;
					e.ContextMenuStrip.Items[j].Tag = shownCondOrder.ID;
				}
			}
		}

		// Token: 0x060015E2 RID: 5602 RVA: 0x00008D46 File Offset: 0x00006F46
		private void method_8(object sender, EventArgs e)
		{
			Base.Trading.smethod_94((int)(sender as ToolStripMenuItem).Tag, OrderStatus.Canceled);
			this.Refresh();
		}

		// Token: 0x060015E3 RID: 5603 RVA: 0x00008D66 File Offset: 0x00006F66
		private void method_9(object sender, EventArgs e)
		{
			new Form12(Base.Trading.smethod_114((int)(sender as ToolStripMenuItem).Tag))
			{
				Owner = Base.UI.MainForm
			}.Show();
		}

		// Token: 0x060015E4 RID: 5604 RVA: 0x00008D94 File Offset: 0x00006F94
		private void method_10(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060015E5 RID: 5605 RVA: 0x00008D9E File Offset: 0x00006F9E
		private void method_11(object sender, EventArgs e)
		{
			Base.Trading.smethod_111();
			this.Refresh();
		}

		// Token: 0x060015E6 RID: 5606 RVA: 0x000041B9 File Offset: 0x000023B9
		private void Class294_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015E7 RID: 5607 RVA: 0x00096BD8 File Offset: 0x00094DD8
		private void method_12()
		{
			int num = 0;
			VScrollBar vscrollBar = base.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = base.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 625m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					if (base.Columns.Count > 1)
					{
						base.Columns[0].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
						base.Columns[1].Width = (flag ? Convert.ToInt32(Math.Floor(110 * num2 / d)) : 110);
						base.Columns[2].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
						base.Columns[3].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
						base.Columns[4].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
						base.Columns[9].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
						base.Columns[13].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
						base.Columns[15].Width = (flag ? Convert.ToInt32(Math.Floor(135 * num2 / d)) : 135);
					}
					return;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 430m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 490m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					base.Columns[0].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
				}
				catch
				{
				}
			}
			try
			{
				if (base.Columns.Count > 1)
				{
					base.Columns[1].Width = (flag ? Convert.ToInt32(Math.Floor(110 * num2 / d)) : 110);
					base.Columns[2].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
					base.Columns[3].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
					base.Columns[4].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
					base.Columns[9].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
					base.Columns[13].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
				}
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
		}

		// Token: 0x04000B14 RID: 2836
		[CompilerGenerated]
		private EventHandler eventHandler_0;
	}
}
