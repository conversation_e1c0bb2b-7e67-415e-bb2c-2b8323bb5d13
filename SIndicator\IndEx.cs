﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using ns19;
using ns25;
using ns26;
using ns31;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002DB RID: 731
	internal class IndEx : Indicator
	{
		// Token: 0x06002093 RID: 8339 RVA: 0x0000D370 File Offset: 0x0000B570
		protected IndEx(UserDefineInd udi)
		{
			this.list_0 = new List<ShapeCurve>();
			this.UDInd = udi;
			this.method_2();
		}

		// Token: 0x06002094 RID: 8340 RVA: 0x000E8568 File Offset: 0x000E6768
		public static IndEx smethod_0(UserDefineInd userDefineInd_2)
		{
			IndEx result;
			if (!(userDefineInd_2.Name == Class521.smethod_0(2652)) && !(userDefineInd_2.Name == Class521.smethod_0(96726)))
			{
				result = new IndEx(userDefineInd_2);
			}
			else
			{
				result = new Class350(userDefineInd_2);
			}
			return result;
		}

		// Token: 0x06002095 RID: 8341 RVA: 0x000E85B8 File Offset: 0x000E67B8
		public override void AddNewItem(int itemIdx, HisData newhd, bool ifAddCompleteItem)
		{
			if (base.IsSelected)
			{
				base.RemoveSelectedBoxPts();
			}
			if (base.Chart.HisDataPeriodSet.IsPeriod1m || ifAddCompleteItem)
			{
				if (itemIdx == base.EndIdx)
				{
					if (base.Chart.HisDataPeriodSet != null)
					{
						IList<HisData> values = base.Chart.HisDataPeriodSet.PeriodHisDataList.Values;
						HisData hisData_;
						if (itemIdx < values.Count && itemIdx >= 0)
						{
							hisData_ = values[itemIdx];
						}
						else
						{
							hisData_ = values[values.Count - 1];
						}
						this.method_0(itemIdx, hisData_, true);
						goto IL_C3;
					}
					goto IL_C3;
				}
				else
				{
					using (List<ShapeCurve>.Enumerator enumerator = this.list_0.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							ShapeCurve shapeCurve = enumerator.Current;
							shapeCurve.vmethod_2(itemIdx);
						}
						goto IL_C3;
					}
				}
			}
			this.method_0(itemIdx, newhd, true);
			IL_C3:
			for (int i = 0; i < base.CurveList.Count; i++)
			{
				if (!base.CurveList[i].Curve.IsVisible)
				{
					base.CurveList[i].Curve.IsVisible = true;
				}
			}
		}

		// Token: 0x06002096 RID: 8342 RVA: 0x000E86E0 File Offset: 0x000E68E0
		protected void method_0(int int_0, HisData hisData_0, bool bool_0)
		{
			DataArray[] array = this.method_8(int_0, hisData_0, bool_0);
			if (array != null)
			{
				DataArray[] array2 = this.method_1(array);
				for (int i = 0; i < this.list_0.Count; i++)
				{
					ShapeCurve shapeCurve = this.list_0[i];
					if (shapeCurve is Class400)
					{
						(shapeCurve as Class400).method_6(int_0, hisData_0, bool_0);
					}
					if (bool_0)
					{
						this.list_0[i].vmethod_3(int_0, array2[i]);
					}
					else
					{
						shapeCurve.vmethod_4(int_0, array2[i]);
					}
				}
			}
		}

		// Token: 0x06002097 RID: 8343 RVA: 0x000E8764 File Offset: 0x000E6964
		public override void RescaleAxis()
		{
			double num = 0.0;
			double num2 = 0.0;
			this.method_6(ref num, ref num2);
			double num3 = (num - num2) / 10.0;
			if (this.GraphControl != null && this.GraphControl.GraphPane != null)
			{
				if (num3 != 0.0)
				{
					this.GraphControl.GraphPane.YAxis.Scale.Max = num + num3;
					this.GraphControl.GraphPane.YAxis.Scale.Min = num2 - num3;
				}
				else
				{
					this.GraphControl.GraphPane.Y2Axis.Scale.MaxAuto = true;
					this.GraphControl.GraphPane.Y2Axis.Scale.MinAuto = true;
				}
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(96731)));
			}
		}

		// Token: 0x06002098 RID: 8344 RVA: 0x0000D392 File Offset: 0x0000B592
		public override void ResetCurve()
		{
			this.userDefineInd_0 = null;
			base.ResetCurve();
		}

		// Token: 0x06002099 RID: 8345 RVA: 0x000E8850 File Offset: 0x000E6A50
		private DataArray[] method_1(DataArray[] dataArray_0)
		{
			List<DataArray> list = new List<DataArray>();
			foreach (DataArray dataArray in dataArray_0.Where(new Func<DataArray, bool>(IndEx.<>c.<>9.method_0)).ToList<DataArray>())
			{
				if (dataArray.ShapeStr == Class521.smethod_0(96756))
				{
					List<DataArray> collection = Class383.smethod_1(dataArray);
					list.AddRange(collection);
				}
				else
				{
					list.Add(dataArray);
				}
			}
			if (list.Count != this.list_0.Count)
			{
				throw new Exception(Class521.smethod_0(96777));
			}
			return list.ToArray();
		}

		// Token: 0x0600209A RID: 8346 RVA: 0x0000D3A3 File Offset: 0x0000B5A3
		public override void UpdateLastItem(int itemIdx, HisData hd)
		{
			this.method_0(itemIdx, hd, false);
		}

		// Token: 0x0600209B RID: 8347 RVA: 0x0000D3B0 File Offset: 0x0000B5B0
		private void method_2()
		{
			base.IsMainChartInd = this.UDInd.UDS.MainK;
		}

		// Token: 0x0600209C RID: 8348 RVA: 0x000E8924 File Offset: 0x000E6B24
		protected void method_3(ChartTheme chartTheme_0, List<ShapeCurve> list_1)
		{
			if (list_1 != null && list_1.Count != 0)
			{
				List<Color> list = new List<Color>();
				List<ShapeCurve> list2 = list_1.Where(new Func<ShapeCurve, bool>(IndEx.<>c.<>9.method_1)).ToList<ShapeCurve>();
				List<ShapeCurve> list3 = new List<ShapeCurve>();
				foreach (ShapeCurve shapeCurve in list2)
				{
					try
					{
						Color? color = ParserEnvironment.smethod_7(shapeCurve.IndData.ColorStr);
						if (color == null)
						{
							list.Add(IndEx.smethod_1(chartTheme_0, list.ToArray()));
							list3.Add(shapeCurve);
						}
						else
						{
							shapeCurve.Curve.Color = color.Value;
						}
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
				if (list.Count != list3.Count)
				{
					throw new Exception(Class521.smethod_0(96818));
				}
				for (int i = 0; i < list3.Count; i++)
				{
					list3[i].Curve.Color = list[i];
				}
				foreach (ShapeCurve shapeCurve2 in list_1.Where(new Func<ShapeCurve, bool>(IndEx.<>c.<>9.method_2)).ToList<ShapeCurve>())
				{
					Color? color2 = ParserEnvironment.smethod_7(shapeCurve2.IndData.ColorStr);
					if (color2 != null)
					{
						shapeCurve2.Curve.Color = color2.Value;
					}
					if (shapeCurve2 is Class400 || shapeCurve2 is Class398)
					{
						JapaneseCandleStickItem japaneseCandleStickItem = shapeCurve2.Curve as JapaneseCandleStickItem;
						if (japaneseCandleStickItem != null)
						{
							this.method_4(japaneseCandleStickItem, chartTheme_0);
						}
					}
				}
			}
		}

		// Token: 0x0600209D RID: 8349 RVA: 0x000E8B20 File Offset: 0x000E6D20
		private void method_4(JapaneseCandleStickItem japaneseCandleStickItem_0, ChartTheme chartTheme_0)
		{
			Color color = Color.FromArgb(255, 255, 236);
			if (chartTheme_0 == ChartTheme.Classic)
			{
				japaneseCandleStickItem_0.Stick.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.RisingFill.Color = Color.Black;
				japaneseCandleStickItem_0.Stick.RisingBorder.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.FallingColor = Color.Cyan;
				japaneseCandleStickItem_0.Stick.FallingBorder.Color = Color.Cyan;
				japaneseCandleStickItem_0.Stick.FallingFill.Color = Color.Cyan;
			}
			else
			{
				japaneseCandleStickItem_0.Stick.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.RisingBorder.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.FallingColor = Color.Green;
				japaneseCandleStickItem_0.Stick.FallingBorder.Color = Color.Green;
				japaneseCandleStickItem_0.Stick.FallingFill.Color = Color.Green;
				if (chartTheme_0 == ChartTheme.Modern)
				{
					japaneseCandleStickItem_0.Stick.RisingFill.Color = Color.White;
				}
				else if (chartTheme_0 == ChartTheme.Yellow)
				{
					japaneseCandleStickItem_0.Stick.RisingFill.Color = color;
				}
			}
		}

		// Token: 0x0600209E RID: 8350 RVA: 0x000E8C50 File Offset: 0x000E6E50
		public override bool InitInd(ChartKLine chart)
		{
			bool result;
			if (!base.InitInd(chart))
			{
				result = false;
			}
			else
			{
				this.method_5();
				if (this.UDInd.method_1(chart.DP))
				{
					this.vmethod_0();
					result = true;
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x0600209F RID: 8351 RVA: 0x000E8C94 File Offset: 0x000E6E94
		private void method_5()
		{
			if (!this.UDInd.UDS.MainK && this.UDInd.UDS.YLine != Class521.smethod_0(1449))
			{
				string[] array = this.UDInd.UDS.YLine.Split(new char[]
				{
					';'
				});
				List<double> list = new List<double>();
				for (int i = 0; i < array.Length; i++)
				{
					double item;
					if (double.TryParse(array[i], out item))
					{
						list.Add(item);
					}
				}
				list.Sort();
				if (list.Any<double>())
				{
					YGridAxis yGridAxis = new YGridAxis(Class521.smethod_0(1449), list.ToArray());
					this.GraphControl.GraphPane.ResetYAxis(yGridAxis);
					base.Chart.ApplyTheme(Base.UI.Form.ChartTheme);
					this.GraphControl.GraphPane.YAxis.MajorGrid.IsVisible = true;
					this.GraphControl.GraphPane.YAxis.MinorTic.IsAllTics = false;
					this.GraphControl.GraphPane.YAxis.Scale.IsSkipFirstLabel = false;
					this.GraphControl.GraphPane.YAxis.Scale.IsSkipLastLabel = false;
					this.GraphControl.GraphPane.YAxis.MajorGrid.IsZeroLine = false;
					this.GraphControl.GraphPane.YAxis.MajorGrid.DashOff = 2f;
					this.GraphControl.GraphPane.YAxis.MinorTic.Size = 0f;
					this.GraphControl.GraphPane.YAxis.MajorTic.Size = 0f;
				}
			}
		}

		// Token: 0x060020A0 RID: 8352 RVA: 0x0000D3CA File Offset: 0x0000B5CA
		public override void ApplyTheme(ChartTheme theme)
		{
			base.ApplyTheme(theme);
			this.method_3(theme, this.list_0);
		}

		// Token: 0x060020A1 RID: 8353 RVA: 0x000E8E58 File Offset: 0x000E7058
		public static Color smethod_1(ChartTheme chartTheme_0, Color[] color_2)
		{
			IndEx.Class379 @class = new IndEx.Class379();
			@class.color_0 = color_2;
			Color[] source;
			if (chartTheme_0 == ChartTheme.Classic)
			{
				source = IndEx.color_0;
			}
			else
			{
				source = IndEx.color_1;
			}
			IEnumerable<Color> source2 = source.Where(new Func<Color, bool>(@class.method_0));
			Color result;
			if (source2.Any<Color>())
			{
				result = source2.First<Color>();
			}
			else
			{
				result = source.First<Color>();
			}
			return result;
		}

		// Token: 0x060020A2 RID: 8354 RVA: 0x000E8EB4 File Offset: 0x000E70B4
		protected virtual void vmethod_0()
		{
			base.InitItem();
			this.list_0.ForEach(new Action<ShapeCurve>(this.method_10));
			List<ShapeCurve> list = new List<ShapeCurve>();
			foreach (DataArray dataArray_ in this.UDInd.DataList.Where(new Func<DataArray, bool>(IndEx.<>c.<>9.method_3)))
			{
				List<ShapeCurve> collection = Class383.smethod_0(dataArray_, this.UDInd.DataProvider, this);
				list.AddRange(collection);
			}
			for (int i = 0; i < list.Count; i++)
			{
				Class383 @class = list[i];
				@class.vmethod_7(base.MaxSticksPerChart);
				Color? color = ParserEnvironment.smethod_7(@class.IndData.ColorStr);
				if (color != null)
				{
					@class.vmethod_6(this.CnName, this.GraphControl, color.Value);
				}
				else
				{
					@class.vmethod_6(this.CnName, this.GraphControl, Color.Red);
				}
				ShapeCurve shapeCurve = list[i];
				if (shapeCurve != null)
				{
					IndCurve item = new IndCurve(list[i].IndData.Name, shapeCurve.Curve, !list[i].IndData.NumVisibleLineNot);
					base.CurveList.Add(item);
				}
			}
			try
			{
				this.method_3(Base.UI.Form.ChartTheme, list);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			this.list_0 = list;
		}

		// Token: 0x060020A3 RID: 8355 RVA: 0x0000D3E2 File Offset: 0x0000B5E2
		public override void InitItem()
		{
			this.vmethod_0();
		}

		// Token: 0x060020A4 RID: 8356 RVA: 0x0000D3EC File Offset: 0x0000B5EC
		public override void RemoveFromChart()
		{
			this.list_0.ForEach(new Action<ShapeCurve>(this.method_11));
			base.RemoveFromChart();
		}

		// Token: 0x060020A5 RID: 8357 RVA: 0x000E905C File Offset: 0x000E725C
		public override Indicator GetIndNameByCurve(CurveItem curve)
		{
			foreach (ShapeCurve shapeCurve in this.list_0)
			{
				if (shapeCurve.Curve != null && shapeCurve.Curve == curve)
				{
					goto IL_4B;
				}
			}
			return null;
			IL_4B:
			return this;
		}

		// Token: 0x060020A6 RID: 8358 RVA: 0x000E90CC File Offset: 0x000E72CC
		private void method_6(ref double double_0, ref double double_1)
		{
			double_0 = -10000000.0;
			double_1 = 100000000.0;
			if (this.list_0 != null && this.list_0.Any<ShapeCurve>())
			{
				bool flag = this.list_0.Exists(new Predicate<ShapeCurve>(IndEx.<>c.<>9.method_4));
				for (int i = 0; i < this.list_0.Count; i++)
				{
					ShapeCurve shapeCurve = this.list_0[i];
					try
					{
						if (flag || (shapeCurve.Curve.IsVisible && !shapeCurve.IndData.NumVisibleLineNot))
						{
							shapeCurve.vmethod_8(ref double_0, ref double_1);
						}
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060020A7 RID: 8359 RVA: 0x000E9198 File Offset: 0x000E7398
		protected override string GetDesc()
		{
			string name = this.UDInd.Name;
			string[] array = this.UDInd.UDS.UserDefineParams.Select(new Func<UserDefineParam, string>(IndEx.<>c.<>9.method_5)).ToArray<string>();
			string result;
			if (array.Any<string>())
			{
				result = name + Class521.smethod_0(24872) + string.Join(Class521.smethod_0(4736), array) + Class521.smethod_0(5046);
			}
			else
			{
				result = name;
			}
			return result;
		}

		// Token: 0x060020A8 RID: 8360 RVA: 0x000E9228 File Offset: 0x000E7428
		public string method_7()
		{
			DataProvider dataProvider = this.UDInd.DataProvider;
			return string.Concat(new string[]
			{
				Class521.smethod_0(96887),
				this.EnName,
				Class521.smethod_0(96908),
				(dataProvider.TradingSymbol == null) ? string.Empty : dataProvider.TradingSymbol.ENName,
				Class521.smethod_0(96929),
				dataProvider.PeriodHisDataList.Keys.First<DateTime>().ToString(),
				Class521.smethod_0(3636),
				dataProvider.PeriodHisDataList.Keys.Last<DateTime>().ToString(),
				Class521.smethod_0(96954),
				dataProvider.PeriodHisDataList.Count.ToString()
			});
		}

		// Token: 0x060020A9 RID: 8361 RVA: 0x000E9308 File Offset: 0x000E7508
		protected DataArray[] method_8(int int_0, HisData hisData_0, bool bool_0)
		{
			IndEx.Class381 @class = new IndEx.Class381();
			@class.hisData_0 = hisData_0;
			DataProvider dataProvider = this.UDInd.DataProvider;
			if (int_0 >= dataProvider.PeriodHisDataList.Count)
			{
				Class184.smethod_0(new ArgumentOutOfRangeException(string.Concat(new object[]
				{
					int_0.ToString(),
					Class521.smethod_0(96971),
					dataProvider.PeriodHisDataList.Count,
					Class521.smethod_0(97004),
					this.method_7()
				})));
				int_0 = dataProvider.PeriodHisDataList.Count - 1;
			}
			SymbDataSet symbDataSet = base.Chart.SymbDataSet;
			DataArray[] result;
			if (!bool_0 && this.userDefineInd_0 != null)
			{
				if (this.userDefineInd_0 == null)
				{
					throw new Exception(Class521.smethod_0(97025));
				}
				HisData value = this.userDefineInd_0.DataProvider.PeriodHisDataList.Last<KeyValuePair<DateTime, HisData>>().Value;
				value.Close = @class.hisData_0.Close;
				if (@class.hisData_0.High > value.High)
				{
					value.High = @class.hisData_0.High;
				}
				if (@class.hisData_0.Low < value.Low)
				{
					value.Low = @class.hisData_0.Low;
				}
				double? volume = null;
				if (value.Date != @class.hisData_0.Date && int_0 > 0 && symbDataSet.CurrHisDataSet != null && symbDataSet.CurrHisDataSet.FetchedHisDataList != null)
				{
					List<HisData> list = symbDataSet.method_105(symbDataSet.CurrHisDataSet.FetchedHisDataList, dataProvider.PeriodHisDataList.Keys[int_0 - 1], @class.hisData_0.Date, new int?(1));
					if (list != null)
					{
						volume = list.Sum(new Func<HisData, double?>(IndEx.<>c.<>9.method_6));
					}
				}
				if (volume != null)
				{
					value.Volume = volume;
				}
				else
				{
					value.Volume += @class.hisData_0.Volume;
				}
				value.Amount = @class.hisData_0.Amount;
				result = this.userDefineInd_0.method_5();
			}
			else
			{
				IndEx.Class382 class2 = new IndEx.Class382();
				List<KeyValuePair<DateTime, HisData>> list2 = dataProvider.PeriodHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).ToList<KeyValuePair<DateTime, HisData>>();
				list2.Add(new KeyValuePair<DateTime, HisData>(@class.hisData_0.Date, @class.hisData_0));
				class2.sortedList_0 = new SortedList<DateTime, HisData>();
				list2.ForEach(new Action<KeyValuePair<DateTime, HisData>>(class2.method_0));
				DataProvider dp = new DataProvider(class2.sortedList_0, symbDataSet.CurrSymbol);
				this.userDefineInd_0 = new UserDefineInd(dp);
				this.userDefineInd_0.PE = new ParserEnvironment(this.UDInd.UDS.UserDefineParams, this.userDefineInd_0);
				this.userDefineInd_0.ProgrameTree = this.UDInd.ProgrameTree;
				result = this.userDefineInd_0.method_5();
			}
			return result;
		}

		// Token: 0x060020AA RID: 8362 RVA: 0x0000D40D File Offset: 0x0000B60D
		protected override double[] GetUpdatedLastOutputs(int itemIdx, HisData newHd)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060020AB RID: 8363 RVA: 0x000E963C File Offset: 0x000E783C
		public override void UpdateLastItem(int itemIdx, HDTick tick)
		{
			HisData hd = Base.Data.smethod_114(tick);
			this.UpdateLastItem(itemIdx, hd);
		}

		// Token: 0x060020AC RID: 8364 RVA: 0x000E965C File Offset: 0x000E785C
		public List<UserDefineParam> method_9()
		{
			return this.UDInd.UDS.UserDefineParams;
		}

		// Token: 0x060020AD RID: 8365 RVA: 0x000E9680 File Offset: 0x000E7880
		public override List<IndParam> GetIndParams()
		{
			return null;
		}

		// Token: 0x060020AE RID: 8366 RVA: 0x000041B9 File Offset: 0x000023B9
		public override void SetIndParams(List<IndParam> indParamLst)
		{
		}

		// Token: 0x170005B4 RID: 1460
		// (get) Token: 0x060020AF RID: 8367 RVA: 0x000E9694 File Offset: 0x000E7894
		public ZedGraphControl GraphControl
		{
			get
			{
				return base.Chart.ZedGraphControl;
			}
		}

		// Token: 0x170005B5 RID: 1461
		// (get) Token: 0x060020B0 RID: 8368 RVA: 0x000E96B0 File Offset: 0x000E78B0
		// (set) Token: 0x060020B1 RID: 8369 RVA: 0x0000D414 File Offset: 0x0000B614
		public UserDefineInd UDInd { get; private set; }

		// Token: 0x170005B6 RID: 1462
		// (get) Token: 0x060020B2 RID: 8370 RVA: 0x000E96C8 File Offset: 0x000E78C8
		public bool HasLast
		{
			get
			{
				return this.UDInd.HasLast;
			}
		}

		// Token: 0x170005B7 RID: 1463
		// (get) Token: 0x060020B3 RID: 8371 RVA: 0x000E96E4 File Offset: 0x000E78E4
		public override string CnName
		{
			get
			{
				return this.UDInd.Name;
			}
		}

		// Token: 0x170005B8 RID: 1464
		// (get) Token: 0x060020B4 RID: 8372 RVA: 0x000E96E4 File Offset: 0x000E78E4
		public override string EnName
		{
			get
			{
				return this.UDInd.Name;
			}
		}

		// Token: 0x170005B9 RID: 1465
		// (get) Token: 0x060020B5 RID: 8373 RVA: 0x000E9700 File Offset: 0x000E7900
		// (set) Token: 0x060020B6 RID: 8374 RVA: 0x0000D41F File Offset: 0x0000B61F
		public ESIndChartType MindChartType { get; set; }

		// Token: 0x170005BA RID: 1466
		// (get) Token: 0x060020B7 RID: 8375 RVA: 0x000E9718 File Offset: 0x000E7918
		// (set) Token: 0x060020B8 RID: 8376 RVA: 0x0000D42A File Offset: 0x0000B62A
		public List<ShapeCurve> IndShapes
		{
			get
			{
				return this.list_0;
			}
			private set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x060020BA RID: 8378 RVA: 0x0000D435 File Offset: 0x0000B635
		[CompilerGenerated]
		private void method_10(ShapeCurve shapeCurve_0)
		{
			shapeCurve_0.vmethod_1(base.Chart.ZedGraphControl);
		}

		// Token: 0x060020BB RID: 8379 RVA: 0x0000D435 File Offset: 0x0000B635
		[CompilerGenerated]
		private void method_11(ShapeCurve shapeCurve_0)
		{
			shapeCurve_0.vmethod_1(base.Chart.ZedGraphControl);
		}

		// Token: 0x04001007 RID: 4103
		protected List<ShapeCurve> list_0;

		// Token: 0x04001008 RID: 4104
		private static Color[] color_0 = new Color[]
		{
			Color.White,
			Color.Yellow,
			Color.Magenta,
			Color.LimeGreen,
			Color.MediumPurple,
			Color.Tomato,
			Color.Tan
		};

		// Token: 0x04001009 RID: 4105
		private static Color[] color_1 = new Color[]
		{
			Color.DarkBlue,
			Color.DarkOrange,
			Color.Purple,
			Color.Blue,
			Color.Violet,
			Color.DarkCyan,
			Color.DarkKhaki
		};

		// Token: 0x0400100A RID: 4106
		private UserDefineInd userDefineInd_0;

		// Token: 0x0400100B RID: 4107
		[CompilerGenerated]
		private UserDefineInd userDefineInd_1;

		// Token: 0x0400100C RID: 4108
		[CompilerGenerated]
		private ESIndChartType esindChartType_0;

		// Token: 0x020002DD RID: 733
		[CompilerGenerated]
		private sealed class Class379
		{
			// Token: 0x060020C6 RID: 8390 RVA: 0x000E989C File Offset: 0x000E7A9C
			internal bool method_0(Color color_1)
			{
				IndEx.Class380 @class = new IndEx.Class380();
				@class.color_0 = color_1;
				return !this.color_0.Any(new Func<Color, bool>(@class.method_0));
			}

			// Token: 0x04001015 RID: 4117
			public Color[] color_0;
		}

		// Token: 0x020002DE RID: 734
		[CompilerGenerated]
		private sealed class Class380
		{
			// Token: 0x060020C8 RID: 8392 RVA: 0x000E98D4 File Offset: 0x000E7AD4
			internal bool method_0(Color color_1)
			{
				return color_1 == this.color_0;
			}

			// Token: 0x04001016 RID: 4118
			public Color color_0;
		}

		// Token: 0x020002DF RID: 735
		[CompilerGenerated]
		private sealed class Class381
		{
			// Token: 0x060020CA RID: 8394 RVA: 0x000E98F4 File Offset: 0x000E7AF4
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key < this.hisData_0.Date;
			}

			// Token: 0x04001017 RID: 4119
			public HisData hisData_0;
		}

		// Token: 0x020002E0 RID: 736
		[CompilerGenerated]
		private sealed class Class382
		{
			// Token: 0x060020CC RID: 8396 RVA: 0x0000D458 File Offset: 0x0000B658
			internal void method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				this.sortedList_0.Add(keyValuePair_0.Key, keyValuePair_0.Value.Clone());
			}

			// Token: 0x04001018 RID: 4120
			public SortedList<DateTime, HisData> sortedList_0;
		}
	}
}
