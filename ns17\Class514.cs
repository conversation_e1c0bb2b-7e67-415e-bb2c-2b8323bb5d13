﻿using System;
using ns18;
using ns3;

namespace ns17
{
	// Token: 0x020003C5 RID: 965
	internal sealed class Class514
	{
		// Token: 0x170006BD RID: 1725
		// (get) Token: 0x060026DB RID: 9947 RVA: 0x0000EF48 File Offset: 0x0000D148
		public static string SubkeyApplication
		{
			get
			{
				return Class521.smethod_0(116118) + Class512.AppName;
			}
		}

		// Token: 0x170006BE RID: 1726
		// (get) Token: 0x060026DC RID: 9948 RVA: 0x0000EF5E File Offset: 0x0000D15E
		public static string WowSubkeyApplication
		{
			get
			{
				return Class521.smethod_0(116143) + Class512.AppName;
			}
		}

		// Token: 0x060026DD RID: 9949 RVA: 0x00002D25 File Offset: 0x00000F25
		private Class514()
		{
		}
	}
}
