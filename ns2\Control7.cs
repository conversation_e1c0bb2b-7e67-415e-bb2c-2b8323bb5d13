﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns1;
using ns18;
using ns7;

namespace ns2
{
	// Token: 0x0200023E RID: 574
	internal sealed class Control7 : UserControl
	{
		// Token: 0x1700041F RID: 1055
		// (get) Token: 0x06001893 RID: 6291 RVA: 0x000A9870 File Offset: 0x000A7A70
		// (set) Token: 0x06001894 RID: 6292 RVA: 0x0000A1EB File Offset: 0x000083EB
		public Color SelectedColor
		{
			get
			{
				return this.class311_0.SelectedColor;
			}
			set
			{
				this.class311_0.SelectedColor = value;
			}
		}

		// Token: 0x17000420 RID: 1056
		// (get) Token: 0x06001896 RID: 6294 RVA: 0x000A988C File Offset: 0x000A7A8C
		// (set) Token: 0x06001895 RID: 6293 RVA: 0x0000A1FB File Offset: 0x000083FB
		public bool Extended
		{
			get
			{
				return this.class311_0.Extended;
			}
			set
			{
				this.class311_0.Extended = value;
			}
		}

		// Token: 0x1400008C RID: 140
		// (add) Token: 0x06001897 RID: 6295 RVA: 0x000A98A8 File Offset: 0x000A7AA8
		// (remove) Token: 0x06001898 RID: 6296 RVA: 0x000A98E0 File Offset: 0x000A7AE0
		public event Delegate16 ColorChanged
		{
			[CompilerGenerated]
			add
			{
				Delegate16 @delegate = this.delegate16_0;
				Delegate16 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate16 value2 = (Delegate16)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate16 @delegate = this.delegate16_0;
				Delegate16 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate16 value2 = (Delegate16)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001899 RID: 6297 RVA: 0x0000A20B File Offset: 0x0000840B
		public Control7()
		{
			this.method_1();
			this.class311_0.ColorChanged += this.method_0;
		}

		// Token: 0x0600189A RID: 6298 RVA: 0x0000A232 File Offset: 0x00008432
		public void method_0(object sender, EventArgs12 e)
		{
			if (this.delegate16_0 != null)
			{
				this.delegate16_0(this, e);
			}
		}

		// Token: 0x0600189B RID: 6299 RVA: 0x0000A24B File Offset: 0x0000844B
		private void Control7_SizeChanged(object sender, EventArgs e)
		{
			this.class311_0.Location = new Point(0, 0);
			this.class311_0.Size = base.Size;
		}

		// Token: 0x0600189C RID: 6300 RVA: 0x0000A272 File Offset: 0x00008472
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600189D RID: 6301 RVA: 0x000A9918 File Offset: 0x000A7B18
		private void method_1()
		{
			this.class311_0 = new Control7.Class311();
			base.SuspendLayout();
			base.AutoScaleDimensions = new SizeF(6f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Name = Class521.smethod_0(64489);
			base.Size = new Size(103, 23);
			base.SizeChanged += this.Control7_SizeChanged;
			base.Controls.Add(this.class311_0);
			base.ResumeLayout(false);
		}

		// Token: 0x04000C48 RID: 3144
		[CompilerGenerated]
		private Delegate16 delegate16_0;

		// Token: 0x04000C49 RID: 3145
		private IContainer icontainer_0;

		// Token: 0x04000C4A RID: 3146
		private Control7.Class311 class311_0;

		// Token: 0x0200023F RID: 575
		private sealed class Class311 : CheckBox
		{
			// Token: 0x1400008D RID: 141
			// (add) Token: 0x0600189E RID: 6302 RVA: 0x000A99A0 File Offset: 0x000A7BA0
			// (remove) Token: 0x0600189F RID: 6303 RVA: 0x000A99D8 File Offset: 0x000A7BD8
			public event Delegate16 ColorChanged
			{
				[CompilerGenerated]
				add
				{
					Delegate16 @delegate = this.delegate16_0;
					Delegate16 delegate2;
					do
					{
						delegate2 = @delegate;
						Delegate16 value2 = (Delegate16)Delegate.Combine(delegate2, value);
						@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
					}
					while (@delegate != delegate2);
				}
				[CompilerGenerated]
				remove
				{
					Delegate16 @delegate = this.delegate16_0;
					Delegate16 delegate2;
					do
					{
						delegate2 = @delegate;
						Delegate16 value2 = (Delegate16)Delegate.Remove(delegate2, value);
						@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
					}
					while (@delegate != delegate2);
				}
			}

			// Token: 0x17000421 RID: 1057
			// (get) Token: 0x060018A0 RID: 6304 RVA: 0x000A9A10 File Offset: 0x000A7C10
			// (set) Token: 0x060018A1 RID: 6305 RVA: 0x0000A293 File Offset: 0x00008493
			public Color SelectedColor
			{
				get
				{
					return this.color_0;
				}
				set
				{
					this.color_0 = value;
					this.control8_0.SelectedColor = value;
				}
			}

			// Token: 0x17000422 RID: 1058
			// (get) Token: 0x060018A3 RID: 6307 RVA: 0x000A9A28 File Offset: 0x000A7C28
			// (set) Token: 0x060018A2 RID: 6306 RVA: 0x0000A2AA File Offset: 0x000084AA
			public bool Extended
			{
				get
				{
					return this.control8_0.ExtendedColors;
				}
				set
				{
					this.control8_0.ExtendedColors = value;
				}
			}

			// Token: 0x060018A4 RID: 6308 RVA: 0x0000A2BA File Offset: 0x000084BA
			public Class311() : this(false, Color.Black)
			{
			}

			// Token: 0x060018A5 RID: 6309 RVA: 0x000A9A44 File Offset: 0x000A7C44
			public Class311(bool bool_0, Color color_1)
			{
				base.SuspendLayout();
				base.Appearance = Appearance.Button;
				this.AutoSize = false;
				base.Size = new Size(103, 23);
				this.Text = Class521.smethod_0(1449);
				base.Paint += this.Class311_Paint;
				base.Click += this.Class311_Click;
				this.timer_0.Tick += this.timer_0_Tick;
				this.timer_0.Interval = 30;
				this.timer_0.Start();
				this.control8_0.ExtendedColors = bool_0;
				Control7.Class311.Control8 control = this.control8_0;
				this.color_0 = color_1;
				control.SelectedColor = color_1;
				base.ResumeLayout(false);
			}

			// Token: 0x060018A6 RID: 6310 RVA: 0x000A9B28 File Offset: 0x000A7D28
			private void Class311_Click(object sender, EventArgs e)
			{
				if (base.Checked)
				{
					this.class313_0 = new Control7.Class311.Class313(this.control8_0);
					Rectangle r = base.Bounds;
					r = base.Parent.RectangleToScreen(r);
					Point screenLocation = new Point(r.Left, r.Bottom);
					this.class313_0.ColorChanged += this.method_0;
					this.class313_0.Show(screenLocation);
					base.Enabled = false;
				}
			}

			// Token: 0x060018A7 RID: 6311 RVA: 0x000A9BA8 File Offset: 0x000A7DA8
			protected void method_0(object sender, EventArgs12 e)
			{
				if (this.delegate16_0 != null && e.color_0 != this.color_0)
				{
					this.color_0 = e.color_0;
					this.delegate16_0(this, e);
				}
				else
				{
					this.color_0 = e.color_0;
				}
			}

			// Token: 0x060018A8 RID: 6312 RVA: 0x000A9BFC File Offset: 0x000A7DFC
			private void Class311_Paint(object sender, PaintEventArgs e)
			{
				Rectangle rectangle_ = new Rectangle(base.ClientRectangle.Right - 18, base.ClientRectangle.Top, 18, base.ClientRectangle.Height);
				this.method_1(e.Graphics, rectangle_);
				Rectangle rectangle_2 = new Rectangle(base.ClientRectangle.Left + 5, base.ClientRectangle.Top + 5, base.ClientRectangle.Width - 21, base.ClientRectangle.Height - 11);
				this.method_2(e.Graphics, rectangle_2, this.color_0);
			}

			// Token: 0x060018A9 RID: 6313 RVA: 0x000A9CAC File Offset: 0x000A7EAC
			private void method_1(Graphics graphics_0, Rectangle rectangle_0)
			{
				Point[] array = new Point[3];
				int num = rectangle_0.Left + (rectangle_0.Right - rectangle_0.Left) / 2;
				int num2 = rectangle_0.Top + (rectangle_0.Bottom - rectangle_0.Top) / 2;
				array[0].X = num - 4;
				array[0].Y = num2 - 2;
				array[1].X = num + 4;
				array[1].Y = num2 - 2;
				array[2].X = num;
				array[2].Y = num2 + 2;
				SolidBrush brush = new SolidBrush(Color.FromArgb(base.Enabled ? 255 : 100, Color.Black));
				graphics_0.FillPolygon(brush, array);
			}

			// Token: 0x060018AA RID: 6314 RVA: 0x000A9D78 File Offset: 0x000A7F78
			private void method_2(Graphics graphics_0, Rectangle rectangle_0, Color color_1)
			{
				SolidBrush brush = new SolidBrush(Color.FromArgb(base.Enabled ? 255 : 100, color_1));
				graphics_0.FillRectangle(brush, rectangle_0);
				Pen pen = new Pen(Color.Black);
				graphics_0.DrawRectangle(pen, rectangle_0);
			}

			// Token: 0x060018AB RID: 6315 RVA: 0x0000A2C8 File Offset: 0x000084C8
			private void timer_0_Tick(object sender, EventArgs e)
			{
				if (this.class313_0 != null && !this.class313_0.Visible)
				{
					base.Checked = false;
					base.Enabled = true;
				}
			}

			// Token: 0x04000C4B RID: 3147
			private Control7.Class311.Class313 class313_0;

			// Token: 0x04000C4C RID: 3148
			private Control7.Class311.Control8 control8_0 = new Control7.Class311.Control8();

			// Token: 0x04000C4D RID: 3149
			private Color color_0 = Color.Black;

			// Token: 0x04000C4E RID: 3150
			private System.Windows.Forms.Timer timer_0 = new System.Windows.Forms.Timer();

			// Token: 0x04000C4F RID: 3151
			[CompilerGenerated]
			private Delegate16 delegate16_0;

			// Token: 0x02000240 RID: 576
			private sealed class Class312 : RadioButton
			{
				// Token: 0x060018AC RID: 6316 RVA: 0x000A9DC0 File Offset: 0x000A7FC0
				public Class312(Color color_0, Color color_1)
				{
					base.ClientSize = new Size(20, 20);
					base.Appearance = Appearance.Button;
					base.Name = Class521.smethod_0(115994);
					base.Visible = true;
					this.ForeColor = color_0;
					base.FlatAppearance.BorderColor = color_1;
					base.FlatAppearance.BorderSize = 0;
					base.FlatStyle = FlatStyle.Flat;
					base.Paint += this.Class312_Paint;
				}

				// Token: 0x060018AD RID: 6317 RVA: 0x000A9E3C File Offset: 0x000A803C
				private void Class312_Paint(object sender, PaintEventArgs e)
				{
					Rectangle rect = new Rectangle(base.ClientRectangle.Left + 4, base.ClientRectangle.Top + 4, base.ClientRectangle.Width - 9, base.ClientRectangle.Height - 9);
					e.Graphics.FillRectangle(new SolidBrush(this.ForeColor), rect);
					e.Graphics.DrawRectangle(new Pen(Color.Black), rect);
				}
			}

			// Token: 0x02000241 RID: 577
			private sealed class Class313 : ToolStripDropDown
			{
				// Token: 0x1400008E RID: 142
				// (add) Token: 0x060018AE RID: 6318 RVA: 0x000A9EC4 File Offset: 0x000A80C4
				// (remove) Token: 0x060018AF RID: 6319 RVA: 0x000A9EFC File Offset: 0x000A80FC
				public event Delegate16 ColorChanged
				{
					[CompilerGenerated]
					add
					{
						Delegate16 @delegate = this.delegate16_0;
						Delegate16 delegate2;
						do
						{
							delegate2 = @delegate;
							Delegate16 value2 = (Delegate16)Delegate.Combine(delegate2, value);
							@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
						}
						while (@delegate != delegate2);
					}
					[CompilerGenerated]
					remove
					{
						Delegate16 @delegate = this.delegate16_0;
						Delegate16 delegate2;
						do
						{
							delegate2 = @delegate;
							Delegate16 value2 = (Delegate16)Delegate.Remove(delegate2, value);
							@delegate = Interlocked.CompareExchange<Delegate16>(ref this.delegate16_0, value2, delegate2);
						}
						while (@delegate != delegate2);
					}
				}

				// Token: 0x17000423 RID: 1059
				// (get) Token: 0x060018B0 RID: 6320 RVA: 0x000A9F34 File Offset: 0x000A8134
				public Color SelectedColor
				{
					get
					{
						return this.control8_0.SelectedColor;
					}
				}

				// Token: 0x060018B1 RID: 6321 RVA: 0x000A9F50 File Offset: 0x000A8150
				public Class313(Control7.Class311.Control8 control8_1)
				{
					if (control8_1 == null)
					{
						throw new ArgumentNullException(Class521.smethod_0(116007));
					}
					this.control8_0 = control8_1;
					this.AutoSize = false;
					this.DoubleBuffered = true;
					base.ResizeRedraw = true;
					this.toolStripControlHost_0 = new ToolStripControlHost(control8_1);
					base.Padding = (base.Margin = (this.toolStripControlHost_0.Padding = (this.toolStripControlHost_0.Margin = Padding.Empty)));
					this.MinimumSize = control8_1.MinimumSize;
					control8_1.MinimumSize = control8_1.Size;
					this.MaximumSize = new Size(control8_1.Size.Width + 1, control8_1.Size.Height + 1);
					control8_1.MaximumSize = new Size(control8_1.Size.Width + 1, control8_1.Size.Height + 1);
					base.Size = new Size(control8_1.Size.Width + 1, control8_1.Size.Height + 1);
					control8_1.Location = Point.Empty;
					this.Items.Add(this.toolStripControlHost_0);
				}

				// Token: 0x060018B2 RID: 6322 RVA: 0x0000A2EF File Offset: 0x000084EF
				protected void OnClosed(ToolStripDropDownClosedEventArgs e)
				{
					if (this.delegate16_0 != null)
					{
						this.delegate16_0(this, new EventArgs12(this.SelectedColor));
					}
				}

				// Token: 0x04000C50 RID: 3152
				[CompilerGenerated]
				private Delegate16 delegate16_0;

				// Token: 0x04000C51 RID: 3153
				private ToolStripControlHost toolStripControlHost_0;

				// Token: 0x04000C52 RID: 3154
				private Control7.Class311.Control8 control8_0;
			}

			// Token: 0x02000242 RID: 578
			private sealed class Control8 : UserControl
			{
				// Token: 0x17000424 RID: 1060
				// (get) Token: 0x060018B4 RID: 6324 RVA: 0x000AA088 File Offset: 0x000A8288
				// (set) Token: 0x060018B3 RID: 6323 RVA: 0x0000A312 File Offset: 0x00008512
				public bool ExtendedColors
				{
					get
					{
						return this.bool_0;
					}
					set
					{
						this.bool_0 = value;
						this.method_1();
					}
				}

				// Token: 0x17000425 RID: 1061
				// (get) Token: 0x060018B5 RID: 6325 RVA: 0x000AA0A0 File Offset: 0x000A82A0
				// (set) Token: 0x060018B6 RID: 6326 RVA: 0x000AA0B8 File Offset: 0x000A82B8
				public Color SelectedColor
				{
					get
					{
						return this.color_2;
					}
					set
					{
						this.color_2 = value;
						Color[] array = this.bool_0 ? this.color_1 : this.color_0;
						for (int i = 0; i < array.Length; i++)
						{
							this.class312_0[i].Checked = (this.color_2 == array[i]);
						}
					}
				}

				// Token: 0x060018B7 RID: 6327 RVA: 0x0000A323 File Offset: 0x00008523
				private void method_0()
				{
					base.SuspendLayout();
					base.Name = Class521.smethod_0(116020);
					this.Text = Class521.smethod_0(1449);
					base.ResumeLayout(false);
				}

				// Token: 0x060018B8 RID: 6328 RVA: 0x000AA114 File Offset: 0x000A8314
				public Control8()
				{
					this.method_0();
					this.method_1();
					base.Paint += this.Control8_Paint;
				}

				// Token: 0x060018B9 RID: 6329 RVA: 0x000AA4C0 File Offset: 0x000A86C0
				private void method_1()
				{
					base.Controls.Clear();
					int num = 3;
					int num2 = 3;
					int num3 = this.bool_0 ? 8 : 4;
					Color[] array = this.bool_0 ? this.color_1 : this.color_0;
					this.class312_0 = new Control7.Class311.Class312[array.Length];
					if (this.bool_0)
					{
						base.ClientSize = new Size(166, 130);
					}
					else
					{
						base.ClientSize = new Size(86, 110);
					}
					for (int i = 0; i < array.Length; i++)
					{
						if (i > 0 && i % num3 == 0)
						{
							num2 += 20;
							num = 3;
						}
						this.class312_0[i] = new Control7.Class311.Class312(array[i], this.BackColor);
						this.class312_0[i].Location = new Point(num, num2);
						base.Controls.Add(this.class312_0[i]);
						this.class312_0[i].Click += this.method_2;
						if (this.color_2 == array[i])
						{
							this.class312_0[i].Checked = true;
						}
						num += 20;
					}
					this.button_0 = new Button();
					this.button_0.FlatStyle = FlatStyle.Popup;
					this.button_0.Text = Class521.smethod_0(116037);
					this.button_0.Font = new Font(Class521.smethod_0(7183), 8f, FontStyle.Regular, GraphicsUnit.Point, 134);
					this.button_0.Location = new Point(3, num2 + 20);
					this.button_0.ClientSize = new Size(this.bool_0 ? 160 : 80, 24);
					this.button_0.Click += this.button_0_Click;
					base.Controls.Add(this.button_0);
				}

				// Token: 0x060018BA RID: 6330 RVA: 0x0000A354 File Offset: 0x00008554
				private void Control8_Paint(object sender, PaintEventArgs e)
				{
					e.Graphics.DrawRectangle(new Pen(Color.Black), base.ClientRectangle);
				}

				// Token: 0x060018BB RID: 6331 RVA: 0x0000A373 File Offset: 0x00008573
				public void method_2(object sender, EventArgs e)
				{
					this.color_2 = ((Control7.Class311.Class312)sender).ForeColor;
					((ToolStripDropDown)base.Parent).Close();
				}

				// Token: 0x060018BC RID: 6332 RVA: 0x000AA6A4 File Offset: 0x000A88A4
				public void button_0_Click(object sender, EventArgs e)
				{
					ColorDialog colorDialog = new ColorDialog();
					colorDialog.Color = this.SelectedColor;
					if (colorDialog.ShowDialog(this) == DialogResult.OK)
					{
						this.color_2 = colorDialog.Color;
					}
					((ToolStripDropDown)base.Parent).Close();
				}

				// Token: 0x04000C53 RID: 3155
				private Color[] color_0 = new Color[]
				{
					Color.Black,
					Color.Gray,
					Color.Maroon,
					Color.Olive,
					Color.Green,
					Color.Teal,
					Color.Navy,
					Color.Purple,
					Color.White,
					Color.Silver,
					Color.Red,
					Color.Yellow,
					Color.Lime,
					Color.Aqua,
					Color.Blue,
					Color.Fuchsia
				};

				// Token: 0x04000C54 RID: 3156
				private Color[] color_1 = new Color[]
				{
					Color.Black,
					Color.Brown,
					Color.Olive,
					Color.DarkGreen,
					Color.FromArgb(0, 51, 102),
					Color.DarkBlue,
					Color.Indigo,
					Color.FromArgb(51, 51, 51),
					Color.DarkRed,
					Color.Orange,
					Color.FromArgb(128, 128, 0),
					Color.Green,
					Color.Teal,
					Color.Blue,
					Color.FromArgb(102, 102, 153),
					Color.FromArgb(128, 128, 128),
					Color.Red,
					Color.FromArgb(255, 153, 0),
					Color.Lime,
					Color.SeaGreen,
					Color.Aqua,
					Color.LightBlue,
					Color.Violet,
					Color.FromArgb(153, 153, 153),
					Color.Pink,
					Color.Gold,
					Color.Yellow,
					Color.FromArgb(0, 255, 0),
					Color.Turquoise,
					Color.SkyBlue,
					Color.Plum,
					Color.FromArgb(192, 192, 192),
					Color.FromArgb(255, 153, 204),
					Color.Tan,
					Color.LightYellow,
					Color.LightGreen,
					Color.FromArgb(204, 255, 255),
					Color.FromArgb(153, 204, 255),
					Color.Lavender,
					Color.White
				};

				// Token: 0x04000C55 RID: 3157
				private Control7.Class311.Class312[] class312_0;

				// Token: 0x04000C56 RID: 3158
				private Button button_0;

				// Token: 0x04000C57 RID: 3159
				private Color color_2 = Color.Black;

				// Token: 0x04000C58 RID: 3160
				private bool bool_0;
			}
		}
	}
}
