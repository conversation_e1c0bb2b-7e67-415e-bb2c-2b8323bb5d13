﻿using System;
using ns12;
using ns14;
using ns18;
using ns28;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns13
{
	// Token: 0x02000316 RID: 790
	internal sealed class Class419 : Class415
	{
		// Token: 0x060021F9 RID: 8697 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class419(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x060021FA RID: 8698 RVA: 0x000F11C8 File Offset: 0x000EF3C8
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_12 && this.Token.Symbol.HSymbolType != Enum26.const_14 && this.Token.Symbol.HSymbolType != Enum26.const_15 && this.Token.Symbol.HSymbolType != Enum26.const_16 && this.Token.Symbol.HSymbolType != Enum26.const_20)
			{
				if (this.Token.Symbol.HSymbolType != Enum26.const_13)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101968)));
				}
			}
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021FB RID: 8699 RVA: 0x000F1294 File Offset: 0x000EF494
		protected override double vmethod_3(double double_0, double double_1)
		{
			switch (this.Token.Symbol.HSymbolType)
			{
			case Enum26.const_12:
				if (double_0 > double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_13:
				if (double_0 != double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_14:
				if (double_0 >= double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_15:
				if (double_0 < double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_16:
				if (double_0 <= double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_20:
				if (double_0 == double_1)
				{
					return 1.0;
				}
				return 0.0;
			}
			throw new Exception(this.Token.method_0(Class521.smethod_0(101997)));
		}

		// Token: 0x060021FC RID: 8700 RVA: 0x000F13B4 File Offset: 0x000EF5B4
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			switch (this.Token.Symbol.HSymbolType)
			{
			case Enum26.const_12:
				return dataArray_0 > dataArray_1;
			case Enum26.const_13:
				return dataArray_0 != dataArray_1;
			case Enum26.const_14:
				return dataArray_0 >= dataArray_1;
			case Enum26.const_15:
				return dataArray_0 < dataArray_1;
			case Enum26.const_16:
				return dataArray_0 <= dataArray_1;
			case Enum26.const_20:
				return dataArray_0 == dataArray_1;
			}
			throw new Exception(this.Token.method_0(Class521.smethod_0(101997)));
		}

		// Token: 0x060021FD RID: 8701 RVA: 0x000F145C File Offset: 0x000EF65C
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			Class411 @class = Class417.smethod_0(tokenes_0);
			tokenes_0.method_1();
			HToken htoken = tokenes_0.Current;
			while (htoken.Symbol.HSymbolType == Enum26.const_12 || htoken.Symbol.HSymbolType == Enum26.const_14 || htoken.Symbol.HSymbolType == Enum26.const_15 || htoken.Symbol.HSymbolType == Enum26.const_16 || htoken.Symbol.HSymbolType == Enum26.const_20 || htoken.Symbol.HSymbolType == Enum26.const_13)
			{
				tokenes_0.method_1();
				Class411 class411_ = Class417.smethod_0(tokenes_0);
				@class = new Class419(htoken, @class, class411_);
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
			}
			tokenes_0.method_2();
			return @class;
		}
	}
}
