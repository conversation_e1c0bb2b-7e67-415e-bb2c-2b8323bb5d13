﻿using System;
using ns18;

namespace ns15
{
	// Token: 0x0200003F RID: 63
	internal sealed class Class23
	{
		// Token: 0x17000083 RID: 131
		// (get) Token: 0x060001E4 RID: 484 RVA: 0x00018B4C File Offset: 0x00016D4C
		public static string Info
		{
			get
			{
				return Class521.smethod_0(3663);
			}
		}

		// Token: 0x17000084 RID: 132
		// (get) Token: 0x060001E5 RID: 485 RVA: 0x00018B68 File Offset: 0x00016D68
		public static string Error
		{
			get
			{
				return Class521.smethod_0(3672);
			}
		}
	}
}
