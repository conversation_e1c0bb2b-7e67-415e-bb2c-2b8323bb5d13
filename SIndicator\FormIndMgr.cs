﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns17;
using ns18;
using ns21;
using ns9;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002D6 RID: 726
	public sealed partial class FormIndMgr : Form
	{
		// Token: 0x140000A0 RID: 160
		// (add) Token: 0x06002061 RID: 8289 RVA: 0x000E63FC File Offset: 0x000E45FC
		// (remove) Token: 0x06002062 RID: 8290 RVA: 0x000E6434 File Offset: 0x000E4634
		public event EventHandler ShownIndEditer
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002063 RID: 8291 RVA: 0x000E646C File Offset: 0x000E466C
		private void method_0(FormIndEditer formIndEditer_0, Enum25 enum25_0)
		{
			EventArgs32 e = new EventArgs32(formIndEditer_0, enum25_0);
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002064 RID: 8292 RVA: 0x000E6498 File Offset: 0x000E4698
		public FormIndMgr()
		{
			base.StartPosition = FormStartPosition.CenterScreen;
			this.method_10();
			Base.UI.smethod_54(this);
			Base.UI.smethod_55(this.dataGridView_0);
			base.Load += this.FormIndMgr_Load;
		}

		// Token: 0x06002065 RID: 8293 RVA: 0x000E64E8 File Offset: 0x000E46E8
		private void FormIndMgr_Load(object sender, EventArgs e)
		{
			string text = Class521.smethod_0(95916);
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_6;
			this.toolStripDropDownButton_0.DropDownItems.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = new ToolStripMenuItem();
			toolStripMenuItem2.Text = Class521.smethod_0(95933);
			toolStripMenuItem2.Click += this.method_5;
			this.toolStripDropDownButton_0.DropDownItems.Add(toolStripMenuItem2);
			this.dataGridView_0.ColumnHeadersVisible = false;
			this.dataGridView_0.RowHeadersVisible = false;
			this.dataGridView_0.ReadOnly = true;
			this.dataGridView_0.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridView_0.DoubleClick += this.dataGridView_0_DoubleClick;
			this.dataGridView_0.Click += this.dataGridView_0_Click;
			this.treeView_0.NodeMouseDoubleClick += this.treeView_0_NodeMouseDoubleClick;
			this.treeView_0.NodeMouseClick += this.treeView_0_NodeMouseClick;
			this.method_1(Class521.smethod_0(1449));
			this.textBox_0.ReadOnly = true;
			this.button_1.Enabled = false;
			this.button_2.Enabled = false;
		}

		// Token: 0x06002066 RID: 8294 RVA: 0x000E6630 File Offset: 0x000E4830
		private void method_1(string string_0)
		{
			this.treeView_0.Nodes.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				TreeNode treeNode = new TreeNode(userDefineIndGroup.Group);
				treeNode.Name = userDefineIndGroup.Group;
				this.treeView_0.Nodes.Add(treeNode);
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					TreeNode treeNode2 = new TreeNode(userDefineIndScript.NameAndScript);
					treeNode2.Name = userDefineIndScript.Name;
					treeNode.Nodes.Add(treeNode2);
					if (string_0 == userDefineIndScript.Name)
					{
						treeNode.Expand();
						this.treeView_0.SelectedNode = treeNode2;
						this.treeView_0.Focus();
					}
				}
			}
			this.method_9(string_0);
		}

		// Token: 0x06002067 RID: 8295 RVA: 0x000E675C File Offset: 0x000E495C
		private void treeView_0_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
		{
			if (e.Clicks == 1 && e.Node.Level == 0 && e.Button == MouseButtons.Right)
			{
				this.treeView_0.SelectedNode = e.Node;
				this.treeView_0.ContextMenuStrip = null;
				ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
				ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
				toolStripMenuItem.Text = Class521.smethod_0(95950);
				toolStripMenuItem.Tag = e.Node.Name;
				toolStripMenuItem.Click += this.method_3;
				toolStripMenuItem.VisibleChanged += this.method_2;
				contextMenuStrip.Items.Add(toolStripMenuItem);
				this.treeView_0.ContextMenuStrip = contextMenuStrip;
			}
			else if (e.Clicks == 1 && e.Node.Level == 1)
			{
				UserDefineIndScript userDefineIndScript = this.method_7(e.Node.Text.Split(new char[]
				{
					' '
				}).First<string>());
				if (userDefineIndScript != null)
				{
					this.button_1.Enabled = true;
					this.button_2.Enabled = true;
					this.textBox_0.Text = userDefineIndScript.Instruction.Replace(Class521.smethod_0(59499), Class521.smethod_0(95967));
					if (this.textBox_0.Text != Class521.smethod_0(1449))
					{
						this.textBox_0.SelectionStart = 0;
						this.textBox_0.ScrollToCaret();
					}
				}
				else
				{
					this.button_2.Enabled = false;
					this.button_1.Enabled = false;
				}
			}
			if (e.Button == MouseButtons.Left)
			{
				this.treeView_0.ContextMenuStrip = null;
			}
		}

		// Token: 0x06002068 RID: 8296 RVA: 0x0000D24E File Offset: 0x0000B44E
		private void method_2(object sender, EventArgs e)
		{
			if (!(sender as ToolStripMenuItem).Visible)
			{
				this.treeView_0.ContextMenuStrip = null;
			}
		}

		// Token: 0x06002069 RID: 8297 RVA: 0x000E6914 File Offset: 0x000E4B14
		private void method_3(object sender, EventArgs e)
		{
			string string_ = (sender as ToolStripMenuItem).Tag.ToString();
			this.method_4(null, string_);
			this.treeView_0.ContextMenuStrip = null;
		}

		// Token: 0x0600206A RID: 8298 RVA: 0x000E6948 File Offset: 0x000E4B48
		private void dataGridView_0_Click(object sender, EventArgs e)
		{
			this.dataGridView_0.CurrentRow.Selected = true;
			string string_ = this.dataGridView_0.CurrentRow.Cells[0].Value.ToString();
			UserDefineIndScript userDefineIndScript = this.method_7(string_);
			if (userDefineIndScript != null)
			{
				this.textBox_0.Text = Class521.smethod_0(1449);
				foreach (string str in userDefineIndScript.Instruction.Split(new char[]
				{
					'\n'
				}))
				{
					this.textBox_0.AppendText(str + Class521.smethod_0(95967));
				}
			}
		}

		// Token: 0x0600206B RID: 8299 RVA: 0x000E69F0 File Offset: 0x000E4BF0
		private void method_4(UserDefineIndScript userDefineIndScript_0, string string_0)
		{
			FormIndEditer formIndEditer = new FormIndEditer();
			if (userDefineIndScript_0 != null)
			{
				if (formIndEditer.method_8(userDefineIndScript_0))
				{
					this.method_0(formIndEditer, Enum25.flag_1);
					formIndEditer.IsAddNew = new bool?(false);
					formIndEditer.ShowDialog();
					this.method_1(userDefineIndScript_0.Name);
				}
			}
			else if (!string.IsNullOrEmpty(string_0))
			{
				formIndEditer.Group = string_0;
				this.method_0(formIndEditer, Enum25.flag_0);
				formIndEditer.IsAddNew = new bool?(true);
				formIndEditer.ShowDialog();
				this.method_1(formIndEditer.AddOrMdfIndName);
			}
		}

		// Token: 0x0600206C RID: 8300 RVA: 0x000E6A70 File Offset: 0x000E4C70
		private void dataGridView_0_DoubleClick(object sender, EventArgs e)
		{
			int index = this.dataGridView_0.CurrentRow.Index;
			if (index < this.bindingList_0.Count)
			{
				string name = this.bindingList_0[index].Name;
				UserDefineIndScript userDefineIndScript = this.method_7(name);
				if (userDefineIndScript != null)
				{
					this.method_4(userDefineIndScript, Class521.smethod_0(1449));
				}
				return;
			}
			throw new Exception(string.Format(Class521.smethod_0(95972), index));
		}

		// Token: 0x0600206D RID: 8301 RVA: 0x000E6AEC File Offset: 0x000E4CEC
		private void method_5(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeView_0.SelectedNode;
			if (selectedNode == null)
			{
				MessageBox.Show(Class521.smethod_0(96029));
			}
			else if (selectedNode.Level == 0)
			{
				if (MessageBox.Show(Class521.smethod_0(96074), Class521.smethod_0(96159), MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
				{
					string name = selectedNode.Name;
					this.treeView_0.Nodes.Remove(selectedNode);
					string b = UserDefineFileMgr.smethod_20(name);
					for (int i = 0; i < this.treeView_0.Nodes.Count; i++)
					{
						if (this.treeView_0.Nodes[i].Name == b)
						{
							this.treeView_0.SelectedNode = this.treeView_0.Nodes[i];
						}
					}
				}
			}
			else
			{
				MessageBox.Show(Class521.smethod_0(96168));
			}
		}

		// Token: 0x0600206E RID: 8302 RVA: 0x000E6BD4 File Offset: 0x000E4DD4
		private void method_6(object sender, EventArgs e)
		{
			IEnumerable<string> source = UserDefineFileMgr.UDGList.GroupBy(new Func<UserDefineIndGroup, string>(FormIndMgr.<>c.<>9.method_0)).Select(new Func<IGrouping<string, UserDefineIndGroup>, string>(FormIndMgr.<>c.<>9.method_1));
			Form22 form = new Form22();
			form.list_0 = source.ToList<string>();
			form.ShowDialog();
			if (form.bool_0)
			{
				this.treeView_0.Nodes.Add(form.GroupName);
				UserDefineFileMgr.smethod_11(form.GroupName);
			}
		}

		// Token: 0x0600206F RID: 8303 RVA: 0x000E6C74 File Offset: 0x000E4E74
		private void treeView_0_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
		{
			if (e.Clicks == 2 && e.Node.Level == 1)
			{
				UserDefineIndScript userDefineIndScript = this.method_7(e.Node.Text.Split(new char[]
				{
					' '
				}).First<string>());
				if (userDefineIndScript != null)
				{
					this.method_4(userDefineIndScript, Class521.smethod_0(1449));
				}
			}
		}

		// Token: 0x06002070 RID: 8304 RVA: 0x000E6CD8 File Offset: 0x000E4ED8
		private void button_0_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeView_0.SelectedNode;
			string string_ = Class521.smethod_0(1449);
			if (selectedNode != null)
			{
				if (selectedNode.Level == 0)
				{
					string_ = selectedNode.Text;
				}
				else
				{
					if (selectedNode.Level != 1)
					{
						throw new Exception(selectedNode.Name + Class521.smethod_0(96201));
					}
					string_ = selectedNode.Parent.Text;
				}
			}
			else
			{
				string_ = Class521.smethod_0(96230);
			}
			this.method_4(null, string_);
		}

		// Token: 0x06002071 RID: 8305 RVA: 0x000E6D58 File Offset: 0x000E4F58
		private void button_1_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeView_0.SelectedNode;
			this.method_8(selectedNode, Enum25.flag_1);
		}

		// Token: 0x06002072 RID: 8306 RVA: 0x000E6D7C File Offset: 0x000E4F7C
		private UserDefineIndScript method_7(string string_0)
		{
			UserDefineIndScript result;
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					if (userDefineIndScript.Name == string_0)
					{
						result = userDefineIndScript;
						goto IL_74;
					}
				}
			}
			return null;
			IL_74:
			return result;
		}

		// Token: 0x06002073 RID: 8307 RVA: 0x000E6E20 File Offset: 0x000E5020
		private void method_8(TreeNode treeNode_0, Enum25 enum25_0)
		{
			if (treeNode_0 != null)
			{
				if (treeNode_0.Level != 1)
				{
					MessageBox.Show(Class521.smethod_0(96243), Class521.smethod_0(72447), MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
				}
				else
				{
					UserDefineIndScript userDefineIndScript = this.method_7(treeNode_0.Text.Split(new char[]
					{
						' '
					}).First<string>());
					if (userDefineIndScript != null)
					{
						if (enum25_0 == Enum25.flag_2)
						{
							string string_ = UserDefineFileMgr.smethod_19(userDefineIndScript, treeNode_0.Parent.Text);
							this.method_1(string_);
						}
						else if (enum25_0 == Enum25.flag_1)
						{
							this.method_4(userDefineIndScript, Class521.smethod_0(1449));
						}
					}
				}
			}
			else
			{
				MessageBox.Show(Class521.smethod_0(96243), Class521.smethod_0(72447), MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06002074 RID: 8308 RVA: 0x000E6ED8 File Offset: 0x000E50D8
		private void button_2_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeView_0.SelectedNode;
			this.method_8(selectedNode, Enum25.flag_2);
		}

		// Token: 0x06002075 RID: 8309 RVA: 0x00004273 File Offset: 0x00002473
		private void button_3_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06002076 RID: 8310 RVA: 0x000E6EFC File Offset: 0x000E50FC
		private void method_9(string string_0)
		{
			this.bindingList_0.Clear();
			List<NameScript> list = new List<NameScript>();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					NameScript item = new NameScript(userDefineIndScript.Name, userDefineIndScript.Script);
					list.Add(item);
				}
			}
			list.Sort();
			this.bindingList_0 = new BindingList<NameScript>(list);
			this.dataGridView_0.DataSource = this.bindingList_0;
			if (this.dataGridView_0.ColumnCount == 2)
			{
				this.dataGridView_0.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
				this.dataGridView_0.Columns[1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
			}
			for (int i = 0; i < this.dataGridView_0.RowCount; i++)
			{
				if (this.dataGridView_0.Rows[i].Cells[0].Value as string == string_0)
				{
					this.dataGridView_0.FirstDisplayedScrollingRowIndex = i;
					return;
				}
			}
		}

		// Token: 0x06002077 RID: 8311 RVA: 0x0000D26B File Offset: 0x0000B46B
		private void tabControl_0_Selected(object sender, TabControlEventArgs e)
		{
			if (e.TabPage.Text == Class521.smethod_0(87005))
			{
				this.method_9(Class521.smethod_0(1449));
			}
		}

		// Token: 0x06002078 RID: 8312 RVA: 0x0000D29B File Offset: 0x0000B49B
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002079 RID: 8313 RVA: 0x000E7068 File Offset: 0x000E5268
		private void method_10()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(FormIndMgr));
			this.toolStrip_0 = new ToolStrip();
			this.toolStripDropDownButton_0 = new ToolStripDropDownButton();
			this.tabControl_0 = new TabControl();
			this.tabPage_0 = new TabPage();
			this.treeView_0 = new TreeView();
			this.tabPage_1 = new TabPage();
			this.dataGridView_0 = new DataGridView();
			this.textBox_0 = new TextBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.button_2 = new Button();
			this.button_3 = new Button();
			this.toolStrip_0.SuspendLayout();
			this.tabControl_0.SuspendLayout();
			this.tabPage_0.SuspendLayout();
			this.tabPage_1.SuspendLayout();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			base.SuspendLayout();
			this.toolStrip_0.ImageScalingSize = new Size(20, 20);
			this.toolStrip_0.Items.AddRange(new ToolStripItem[]
			{
				this.toolStripDropDownButton_0
			});
			this.toolStrip_0.Location = new Point(0, 0);
			this.toolStrip_0.Name = Class521.smethod_0(96280);
			this.toolStrip_0.Size = new Size(813, 27);
			this.toolStrip_0.TabIndex = 0;
			this.toolStrip_0.Text = Class521.smethod_0(96280);
			this.toolStripDropDownButton_0.DisplayStyle = ToolStripItemDisplayStyle.Text;
			this.toolStripDropDownButton_0.Image = (Image)componentResourceManager.GetObject(Class521.smethod_0(96297));
			this.toolStripDropDownButton_0.ImageTransparentColor = Color.Magenta;
			this.toolStripDropDownButton_0.Name = Class521.smethod_0(96338);
			this.toolStripDropDownButton_0.Size = new Size(83, 24);
			this.toolStripDropDownButton_0.Text = Class521.smethod_0(96371);
			this.toolStripDropDownButton_0.TextImageRelation = TextImageRelation.TextAboveImage;
			this.tabControl_0.Controls.Add(this.tabPage_0);
			this.tabControl_0.Controls.Add(this.tabPage_1);
			this.tabControl_0.Dock = DockStyle.Top;
			this.tabControl_0.Location = new Point(0, 27);
			this.tabControl_0.Margin = new Padding(3, 2, 3, 2);
			this.tabControl_0.Name = Class521.smethod_0(20184);
			this.tabControl_0.SelectedIndex = 0;
			this.tabControl_0.Size = new Size(813, 305);
			this.tabControl_0.TabIndex = 1;
			this.tabControl_0.Selected += this.tabControl_0_Selected;
			this.tabPage_0.Controls.Add(this.treeView_0);
			this.tabPage_0.Location = new Point(4, 25);
			this.tabPage_0.Margin = new Padding(3, 2, 3, 2);
			this.tabPage_0.Name = Class521.smethod_0(96388);
			this.tabPage_0.Padding = new Padding(3, 2, 3, 2);
			this.tabPage_0.Size = new Size(805, 276);
			this.tabPage_0.TabIndex = 0;
			this.tabPage_0.Text = Class521.smethod_0(96405);
			this.tabPage_0.UseVisualStyleBackColor = true;
			this.treeView_0.Dock = DockStyle.Fill;
			this.treeView_0.Location = new Point(3, 2);
			this.treeView_0.Margin = new Padding(3, 2, 3, 2);
			this.treeView_0.Name = Class521.smethod_0(96414);
			this.treeView_0.Size = new Size(799, 272);
			this.treeView_0.TabIndex = 0;
			this.tabPage_1.Controls.Add(this.dataGridView_0);
			this.tabPage_1.Location = new Point(4, 25);
			this.tabPage_1.Margin = new Padding(3, 2, 3, 2);
			this.tabPage_1.Name = Class521.smethod_0(96435);
			this.tabPage_1.Padding = new Padding(3, 2, 3, 2);
			this.tabPage_1.Size = new Size(1078, 276);
			this.tabPage_1.TabIndex = 1;
			this.tabPage_1.Text = Class521.smethod_0(87005);
			this.tabPage_1.UseVisualStyleBackColor = true;
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.Dock = DockStyle.Fill;
			this.dataGridView_0.Location = new Point(3, 2);
			this.dataGridView_0.Margin = new Padding(3, 2, 3, 2);
			this.dataGridView_0.Name = Class521.smethod_0(96448);
			this.dataGridView_0.RowTemplate.Height = 20;
			this.dataGridView_0.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView_0.Size = new Size(1072, 272);
			this.dataGridView_0.TabIndex = 0;
			this.textBox_0.Dock = DockStyle.Top;
			this.textBox_0.Location = new Point(0, 332);
			this.textBox_0.Margin = new Padding(3, 2, 3, 2);
			this.textBox_0.Multiline = true;
			this.textBox_0.Name = Class521.smethod_0(96473);
			this.textBox_0.ScrollBars = ScrollBars.Both;
			this.textBox_0.Size = new Size(813, 181);
			this.textBox_0.TabIndex = 2;
			this.button_0.Location = new Point(22, 528);
			this.button_0.Margin = new Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(96498);
			this.button_0.Size = new Size(95, 32);
			this.button_0.TabIndex = 3;
			this.button_0.Text = Class521.smethod_0(96511);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Location = new Point(129, 528);
			this.button_1.Margin = new Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(96520);
			this.button_1.Size = new Size(95, 32);
			this.button_1.TabIndex = 3;
			this.button_1.Text = Class521.smethod_0(22174);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.button_2.Location = new Point(236, 528);
			this.button_2.Margin = new Padding(3, 2, 3, 2);
			this.button_2.Name = Class521.smethod_0(96533);
			this.button_2.Size = new Size(95, 32);
			this.button_2.TabIndex = 3;
			this.button_2.Text = Class521.smethod_0(22208);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Location = new Point(698, 528);
			this.button_3.Margin = new Padding(3, 2, 3, 2);
			this.button_3.Name = Class521.smethod_0(95803);
			this.button_3.Size = new Size(95, 32);
			this.button_3.TabIndex = 3;
			this.button_3.Text = Class521.smethod_0(37203);
			this.button_3.UseVisualStyleBackColor = true;
			this.button_3.Click += this.button_3_Click;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(813, 577);
			base.Controls.Add(this.button_3);
			base.Controls.Add(this.button_2);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.tabControl_0);
			base.Controls.Add(this.toolStrip_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.Margin = new Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(96546);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(96563);
			this.toolStrip_0.ResumeLayout(false);
			this.toolStrip_0.PerformLayout();
			this.tabControl_0.ResumeLayout(false);
			this.tabPage_0.ResumeLayout(false);
			this.tabPage_1.ResumeLayout(false);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000FE7 RID: 4071
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000FE8 RID: 4072
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FE9 RID: 4073
		private IContainer icontainer_0;

		// Token: 0x04000FEA RID: 4074
		private ToolStrip toolStrip_0;

		// Token: 0x04000FEB RID: 4075
		private ToolStripDropDownButton toolStripDropDownButton_0;

		// Token: 0x04000FEC RID: 4076
		private TabControl tabControl_0;

		// Token: 0x04000FED RID: 4077
		private TabPage tabPage_0;

		// Token: 0x04000FEE RID: 4078
		private TabPage tabPage_1;

		// Token: 0x04000FEF RID: 4079
		private TextBox textBox_0;

		// Token: 0x04000FF0 RID: 4080
		private Button button_0;

		// Token: 0x04000FF1 RID: 4081
		private Button button_1;

		// Token: 0x04000FF2 RID: 4082
		private Button button_2;

		// Token: 0x04000FF3 RID: 4083
		private Button button_3;

		// Token: 0x04000FF4 RID: 4084
		private TreeView treeView_0;

		// Token: 0x04000FF5 RID: 4085
		private DataGridView dataGridView_0;
	}
}
