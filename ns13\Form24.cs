﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns9;
using TEx;

namespace ns13
{
	// Token: 0x020002D8 RID: 728
	internal sealed partial class Form24 : Form
	{
		// Token: 0x0600207E RID: 8318 RVA: 0x0000D2CA File Offset: 0x0000B4CA
		public Form24()
		{
			this.method_0();
			this.button_0.Click += this.button_0_Click;
			Base.UI.smethod_54(this);
		}

		// Token: 0x140000A1 RID: 161
		// (add) Token: 0x0600207F RID: 8319 RVA: 0x000E79FC File Offset: 0x000E5BFC
		// (remove) Token: 0x06002080 RID: 8320 RVA: 0x000E7A34 File Offset: 0x000E5C34
		public event EventHandler OnColor
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002081 RID: 8321 RVA: 0x000E7A6C File Offset: 0x000E5C6C
		private void button_0_Click(object sender, EventArgs e)
		{
			ColorDialog colorDialog = new ColorDialog();
			colorDialog.AllowFullOpen = true;
			colorDialog.ShowHelp = true;
			colorDialog.Color = Color.Red;
			colorDialog.AnyColor = true;
			if (colorDialog.ShowDialog() == DialogResult.OK && this.eventHandler_0 != null)
			{
				EventArgs31 e2 = new EventArgs31(colorDialog.Color);
				this.eventHandler_0(this, e2);
			}
		}

		// Token: 0x06002082 RID: 8322 RVA: 0x0000D2F7 File Offset: 0x0000B4F7
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002083 RID: 8323 RVA: 0x000E7AD0 File Offset: 0x000E5CD0
		private void method_0()
		{
			this.colorDialog_0 = new ColorDialog();
			this.button_0 = new Button();
			base.SuspendLayout();
			this.button_0.Location = new Point(225, 258);
			this.button_0.Name = Class521.smethod_0(95505);
			this.button_0.Size = new Size(96, 32);
			this.button_0.TabIndex = 0;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(548, 306);
			base.Controls.Add(this.button_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(96584);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(96605);
			base.ResumeLayout(false);
		}

		// Token: 0x04000FF9 RID: 4089
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000FFA RID: 4090
		private IContainer icontainer_0;

		// Token: 0x04000FFB RID: 4091
		private ColorDialog colorDialog_0;

		// Token: 0x04000FFC RID: 4092
		private Button button_0;
	}
}
