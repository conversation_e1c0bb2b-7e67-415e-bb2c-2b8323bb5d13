﻿using System;
using System.Xml.Linq;
using ns18;

namespace TEx
{
	// Token: 0x02000032 RID: 50
	[Serializable]
	internal sealed class TransTabCtrlParam
	{
		// Token: 0x06000150 RID: 336 RVA: 0x00016F2C File Offset: 0x0001512C
		public XElement GetXElement()
		{
			XElement xelement = new XElement(Class521.smethod_0(2581));
			xelement.SetAttributeValue(Class521.smethod_0(2137), Convert.ToInt32(this.HasParentSpContainer));
			xelement.SetAttributeValue(Class521.smethod_0(2195), Convert.ToInt32(this.IsInParentSpContainerPanel1));
			xelement.SetAttributeValue(Class521.smethod_0(2166), this.ParentSpContainerTag);
			return xelement;
		}

		// Token: 0x17000056 RID: 86
		// (get) Token: 0x06000151 RID: 337 RVA: 0x00016FB8 File Offset: 0x000151B8
		// (set) Token: 0x06000152 RID: 338 RVA: 0x00002F31 File Offset: 0x00001131
		public bool HasParentSpContainer
		{
			get
			{
				return this._HasParentSpContainer;
			}
			set
			{
				this._HasParentSpContainer = value;
			}
		}

		// Token: 0x17000057 RID: 87
		// (get) Token: 0x06000153 RID: 339 RVA: 0x00016FD0 File Offset: 0x000151D0
		// (set) Token: 0x06000154 RID: 340 RVA: 0x00002F3C File Offset: 0x0000113C
		public string ParentSpContainerTag
		{
			get
			{
				return this._ParentSpContainerTag;
			}
			set
			{
				this._ParentSpContainerTag = value;
			}
		}

		// Token: 0x17000058 RID: 88
		// (get) Token: 0x06000155 RID: 341 RVA: 0x00016FE8 File Offset: 0x000151E8
		// (set) Token: 0x06000156 RID: 342 RVA: 0x00002F47 File Offset: 0x00001147
		public bool IsInParentSpContainerPanel1
		{
			get
			{
				return this._IsInParentSpContainerPanel1;
			}
			set
			{
				this._IsInParentSpContainerPanel1 = value;
			}
		}

		// Token: 0x04000082 RID: 130
		private bool _HasParentSpContainer;

		// Token: 0x04000083 RID: 131
		private string _ParentSpContainerTag = Class521.smethod_0(1449);

		// Token: 0x04000084 RID: 132
		private bool _IsInParentSpContainerPanel1 = true;
	}
}
