﻿using System;

namespace ns12
{
	// Token: 0x020001DF RID: 479
	internal sealed class Class266
	{
		// Token: 0x060012B8 RID: 4792 RVA: 0x00002D25 File Offset: 0x00000F25
		public Class266()
		{
		}

		// Token: 0x060012B9 RID: 4793 RVA: 0x00007D02 File Offset: 0x00005F02
		public Class266(int int_1, string string_3, string string_4, string string_5)
		{
			this.Id = int_1;
			this.KeyStr = string_3;
			this.EnName = string_4;
			this.CnName = string_5;
		}

		// Token: 0x170002D0 RID: 720
		// (get) Token: 0x060012BA RID: 4794 RVA: 0x00083D44 File Offset: 0x00081F44
		// (set) Token: 0x060012BB RID: 4795 RVA: 0x00007D29 File Offset: 0x00005F29
		public int Id
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002D1 RID: 721
		// (get) Token: 0x060012BC RID: 4796 RVA: 0x00083D5C File Offset: 0x00081F5C
		// (set) Token: 0x060012BD RID: 4797 RVA: 0x00007D34 File Offset: 0x00005F34
		public string KeyStr
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002D2 RID: 722
		// (get) Token: 0x060012BE RID: 4798 RVA: 0x00083D74 File Offset: 0x00081F74
		// (set) Token: 0x060012BF RID: 4799 RVA: 0x00007D3F File Offset: 0x00005F3F
		public string EnName
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x170002D3 RID: 723
		// (get) Token: 0x060012C0 RID: 4800 RVA: 0x00083D8C File Offset: 0x00081F8C
		// (set) Token: 0x060012C1 RID: 4801 RVA: 0x00007D4A File Offset: 0x00005F4A
		public string CnName
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x040009BB RID: 2491
		private int int_0;

		// Token: 0x040009BC RID: 2492
		private string string_0;

		// Token: 0x040009BD RID: 2493
		private string string_1;

		// Token: 0x040009BE RID: 2494
		private string string_2;
	}
}
