﻿using System;
using System.Collections.Generic;
using System.Data;
using ns18;
using ns8;
using TEx.Comn;
using TEx.Util;

namespace ns6
{
	// Token: 0x02000026 RID: 38
	internal sealed class Class11 : Class10
	{
		// Token: 0x060000DE RID: 222 RVA: 0x00002D06 File Offset: 0x00000F06
		public Class11()
		{
			base.ResultCompressed = true;
		}

		// Token: 0x060000DF RID: 223 RVA: 0x00014170 File Offset: 0x00012370
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && reqDict.ContainsKey(Class521.smethod_0(1738)))
			{
				string string_ = rslt.data as string;
				this.method_1(string_, reqDict);
			}
		}

		// Token: 0x060000E0 RID: 224 RVA: 0x000141A8 File Offset: 0x000123A8
		private void method_1(string string_0, Dictionary<string, object> dictionary_0)
		{
			if (!string.IsNullOrEmpty(string_0))
			{
				DataTable dataTable = Utility.GetDataTableFromCsv(string_0, new int[1]);
				InternalDataCollectionBase columns = dataTable.Columns;
				DataTable dataTable2 = new DataTable();
				DataTable dataTable3 = new DataTable();
				foreach (object obj in columns)
				{
					DataColumn dataColumn = (DataColumn)obj;
					dataTable2.Columns.Add(new DataColumn(dataColumn.ColumnName, dataColumn.DataType));
					dataTable3.Columns.Add(new DataColumn(dataColumn.ColumnName, dataColumn.DataType));
				}
				DataRow[] array = dataTable.Select();
				int num = (array.Length < 12) ? 0 : (array.Length - 12);
				for (int i = array.Length - 1; i >= num; i--)
				{
					object[] array2 = array[i].ItemArray.Clone() as object[];
					string text = array2[0] as string;
					string str = text.Substring(0, 4);
					if (text.EndsWith(Class521.smethod_0(1755)))
					{
						array2[0] = str + Class521.smethod_0(1764);
					}
					else if (text.EndsWith(Class521.smethod_0(1769)))
					{
						array2[0] = str + Class521.smethod_0(1778);
					}
					else if (text.EndsWith(Class521.smethod_0(1783)))
					{
						array2[0] = str + Class521.smethod_0(1792);
					}
					else if (text.EndsWith(Class521.smethod_0(1797)))
					{
						array2[0] = str + Class521.smethod_0(1806);
					}
					dataTable3.Rows.Add(array2);
				}
				DataRow[] array3 = dataTable.Select(dataTable.Columns[0] + Class521.smethod_0(1811));
				num = ((array3.Length < 12) ? 0 : (array3.Length - 12));
				for (int j = array3.Length - 1; j >= num; j--)
				{
					object[] array4 = array3[j].ItemArray.Clone() as object[];
					array4[0] = (array4[0] as string).Substring(0, 4);
					dataTable2.Rows.Add(array4);
				}
				dataTable = Utility.GetSortedDataTable(dataTable, Class521.smethod_0(1494));
				dataTable2 = Utility.GetSortedDataTable(dataTable2, Class521.smethod_0(1494));
				dataTable3 = Utility.GetSortedDataTable(dataTable3, Class521.smethod_0(1494));
				dictionary_0[Class521.smethod_0(1581)] = dataTable;
				dictionary_0[Class521.smethod_0(1832)] = dataTable2;
				dictionary_0[Class521.smethod_0(1845)] = dataTable3;
			}
		}
	}
}
