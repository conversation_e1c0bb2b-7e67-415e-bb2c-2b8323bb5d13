﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000069 RID: 105
	[Serializable]
	internal sealed class DrawFibonacciExtLines : DrawObj, ISerializable
	{
		// Token: 0x060003DF RID: 991 RVA: 0x00003742 File Offset: 0x00001942
		public DrawFibonacciExtLines()
		{
		}

		// Token: 0x060003E0 RID: 992 RVA: 0x000039E9 File Offset: 0x00001BE9
		public DrawFibonacciExtLines(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5103);
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x060003E1 RID: 993 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawFibonacciExtLines(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003E2 RID: 994 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003E3 RID: 995 RVA: 0x000224A8 File Offset: 0x000206A8
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_1, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			TextObj textObj = base.method_27(chartCS_1, double_1, min, (double_3 - double_1).ToString(Class521.smethod_0(5136)), null, string_5);
			textObj.Location.AlignV = AlignV.Bottom;
			textObj.Location.AlignH = AlignH.Left;
			list.Add(textObj);
			foreach (DrawSublineParam drawSublineParam in base.SublineParamList)
			{
				if (drawSublineParam.Enabled)
				{
					double num = double_1 + Math.Round((double_3 - double_1) * drawSublineParam.Value);
					if (num >= max || num <= 0.0)
					{
						break;
					}
					LineObj item2 = base.method_22(chartCS_1, num, string_5);
					list.Add(item2);
					TextObj textObj2 = base.method_27(chartCS_1, num, min, drawSublineParam.Value.ToString(Class521.smethod_0(5141) + drawSublineParam.DigitNb.ToString()), null, string_5);
					textObj2.Location.AlignV = AlignV.Bottom;
					textObj2.Location.AlignH = AlignH.Left;
					list.Add(textObj2);
				}
			}
			return list;
		}

		// Token: 0x060003E4 RID: 996 RVA: 0x00022640 File Offset: 0x00020840
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				1.0,
				1.618,
				2.0,
				2.618,
				3.618
			});
			return base.method_28(list_, 1.0, 5000.0, 3);
		}
	}
}
