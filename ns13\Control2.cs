﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns0;
using ns10;
using ns11;
using ns14;
using ns18;
using ns26;
using ns4;
using ns6;
using ns8;
using ns9;
using TEx;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace ns13
{
	// Token: 0x02000219 RID: 537
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class Control2 : UserControl
	{
		// Token: 0x06001610 RID: 5648 RVA: 0x00008F13 File Offset: 0x00007113
		public Control2()
		{
			this.method_40();
		}

		// Token: 0x06001611 RID: 5649 RVA: 0x00097EA4 File Offset: 0x000960A4
		public void method_0()
		{
			this.button_0.Click += this.button_0_Click;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9.3f, false);
				Font font = new Font(Class521.smethod_0(7183), emSize, FontStyle.Bold);
				this.superTabControl_0.TabFont = font;
				this.superTabControl_0.SelectedTabFont = font;
			}
			this.superTabControl_0.SelectedTabChanged += this.method_13;
			this.tabControl_0.SelectedTabChanged += this.tabControl_0_SelectedTabChanged;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize2 = TApp.smethod_4(9f, false);
				Font font2 = new Font(Class521.smethod_0(7183), emSize2, FontStyle.Regular);
				this.tabControl_0.Font = font2;
				this.tabControl_0.SelectedTabFont = font2;
				emSize2 = TApp.smethod_4(9f, true);
				this.button_0.Font = new Font(Class521.smethod_0(7183), emSize2);
			}
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(0);
			if (this.superTabItem_3 != null)
			{
				this.superTabItem_3.Visible = false;
			}
			this.method_6();
			this.class11_0 = new Class11();
			this.class11_0.ResultReceived += this.class11_0_ResultReceived;
			this.class11_0.RequestError += this.class11_0_RequestError;
			this.dictionary_0 = new Dictionary<string, KeyValuePair<string, string>>();
			Base.Data.CurrSymblChanging += this.method_22;
			Base.Data.CurrSymblChanged += this.method_21;
			Base.Data.DateSelectionChanged += this.method_20;
			Base.UI.ChartThemeChanged += this.method_4;
			Base.UI.Form.BlindTestModeOn += this.method_8;
			Base.UI.Form.BlindTestModeOff += this.method_9;
			this.method_23();
			this.method_25();
			this.method_27();
			this.method_3();
			this.list_0 = new List<Class16>();
			this.method_29();
		}

		// Token: 0x06001612 RID: 5650 RVA: 0x000980B4 File Offset: 0x000962B4
		private DevComponents.DotNetBar.TabControl method_1(string[] string_0, bool bool_0 = true)
		{
			DevComponents.DotNetBar.TabControl tabControl = new DevComponents.DotNetBar.TabControl();
			tabControl.BackColor = Color.Transparent;
			tabControl.CanReorderTabs = true;
			tabControl.Dock = DockStyle.Fill;
			tabControl.Location = new Point(0, 0);
			tabControl.Margin = new System.Windows.Forms.Padding(0);
			tabControl.Padding = new System.Windows.Forms.Padding(0);
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font(Class521.smethod_0(7183), (float)(11.25 / TApp.DpiScale), FontStyle.Regular);
				tabControl.Font = font;
				tabControl.SelectedTabFont = font;
			}
			tabControl.SelectedTabIndex = 0;
			tabControl.Style = eTabStripStyle.Flat;
			tabControl.TabAlignment = eTabStripAlignment.Bottom;
			tabControl.TabIndex = 0;
			tabControl.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			foreach (string string_ in string_0)
			{
				TabItem tabItem = this.method_2(string_);
				tabControl.Tabs.Add(tabItem);
				TabControlPanel tabControlPanel = tabItem.AttachedControl as TabControlPanel;
				tabControl.Controls.Add(tabControlPanel);
				if (bool_0)
				{
					this.method_11(tabControlPanel);
				}
			}
			tabControl.ColorScheme = Base.UI.smethod_72();
			tabControl.SelectedTabChanged += this.method_34;
			return tabControl;
		}

		// Token: 0x06001613 RID: 5651 RVA: 0x000981D4 File Offset: 0x000963D4
		private TabItem method_2(string string_0)
		{
			TabItem tabItem = new TabItem();
			tabItem.Text = string_0;
			tabItem.AttachedControl = new TabControlPanel
			{
				Dock = DockStyle.Fill,
				Location = new Point(0, 0),
				Padding = new System.Windows.Forms.Padding(0),
				Style = 
				{
					BackColor1 = 
					{
						Color = SystemColors.Control
					},
					Border = eBorderType.SingleLine,
					BorderColor = 
					{
						Color = Color.FromArgb(93, 93, 93)
					},
					BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top),
					GradientAngle = -90
				},
				TabItem = tabItem
			};
			return tabItem;
		}

		// Token: 0x06001614 RID: 5652 RVA: 0x0009827C File Offset: 0x0009647C
		private void method_3()
		{
			this.smethod_0();
			Color backColor = Base.UI.smethod_34();
			Color foreColor = Base.UI.smethod_35();
			this.tableLayoutPanel_0.BackColor = backColor;
			this.label_2.ForeColor = foreColor;
			this.label_1.ForeColor = foreColor;
			this.label_0.ForeColor = foreColor;
			this.method_5();
			this.method_7();
			this.smethod_1();
		}

		// Token: 0x06001615 RID: 5653 RVA: 0x00008F2E File Offset: 0x0000712E
		private void method_4(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06001616 RID: 5654 RVA: 0x000982E0 File Offset: 0x000964E0
		private void method_5()
		{
			eSuperTabStyle tabStyle = Base.UI.smethod_71();
			this.superTabControl_0.TabStyle = tabStyle;
			TabColorScheme colorScheme = Base.UI.smethod_72();
			this.tabControl_0.ColorScheme = colorScheme;
			this.button_0.ForeColor = Color.Black;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.tableLayoutPanel_1.BackColor = Class181.color_3;
			}
			else
			{
				this.tableLayoutPanel_1.BackColor = Class181.color_9;
			}
			foreach (object obj in this.superTabControl_0.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						DevComponents.DotNetBar.TabControl tabControl = superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl;
						if (tabControl != null)
						{
							tabControl.ColorScheme = colorScheme;
						}
					}
				}
			}
		}

		// Token: 0x06001617 RID: 5655 RVA: 0x00008F38 File Offset: 0x00007138
		private void method_6()
		{
			this.method_10(new Action<Panel>(this.method_11));
		}

		// Token: 0x06001618 RID: 5656 RVA: 0x00008F4E File Offset: 0x0000714E
		private void method_7()
		{
			this.method_10(new Action<Panel>(this.method_12));
		}

		// Token: 0x06001619 RID: 5657 RVA: 0x00008F64 File Offset: 0x00007164
		private void method_8(object sender, EventArgs e)
		{
			this.method_25();
			this.method_27();
		}

		// Token: 0x0600161A RID: 5658 RVA: 0x00008F64 File Offset: 0x00007164
		private void method_9(object sender, EventArgs e)
		{
			this.method_25();
			this.method_27();
		}

		// Token: 0x0600161B RID: 5659 RVA: 0x000983DC File Offset: 0x000965DC
		private void method_10(Action<Panel> action_0)
		{
			foreach (object obj in this.tabControl_0.Controls)
			{
				if (obj is TabControlPanel)
				{
					action_0(obj as TabControlPanel);
				}
			}
			foreach (object obj2 in this.superTabControl_0.Controls)
			{
				if (obj2 is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj2 as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						foreach (object obj3 in (superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl).Controls)
						{
							if (obj3 is TabControlPanel)
							{
								action_0(obj3 as TabControlPanel);
							}
						}
					}
				}
			}
		}

		// Token: 0x0600161C RID: 5660 RVA: 0x00098518 File Offset: 0x00096718
		private void method_11(Panel panel_0)
		{
			bool flag = false;
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null && !stkSymbol.IsStockCompany)
			{
				flag = true;
			}
			Color backColor;
			Color foreColor;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				backColor = Class181.color_3;
				foreColor = Class181.color_4;
			}
			else
			{
				backColor = Color.FromKnownColor(KnownColor.Control);
				foreColor = Color.LightGray;
			}
			Panel panel = new Panel();
			panel.BackColor = backColor;
			panel.Dock = DockStyle.Fill;
			if (flag)
			{
				System.Windows.Forms.Label label = new System.Windows.Forms.Label();
				label.Text = Class521.smethod_0(53354);
				label.ForeColor = foreColor;
				label.Font = new Font(Class521.smethod_0(24023), 40f, FontStyle.Regular);
				label.TextAlign = ContentAlignment.MiddleCenter;
				label.Dock = DockStyle.Fill;
				panel.Controls.Add(label);
			}
			panel_0.Controls.Clear();
			panel_0.Controls.Add(panel);
		}

		// Token: 0x0600161D RID: 5661 RVA: 0x000985F4 File Offset: 0x000967F4
		private void method_12(Panel panel_0)
		{
			if (panel_0.Controls.Count > 0)
			{
				Control control = panel_0.Controls[0];
				if (control is Panel && !(control is TabControlPanel))
				{
					Panel panel = control as Panel;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						panel.BackColor = Class181.color_3;
					}
					else
					{
						panel.BackColor = Class181.color_9;
					}
					if (panel.Controls.Count > 0)
					{
						System.Windows.Forms.Label label = panel.Controls[0] as System.Windows.Forms.Label;
						if (label != null)
						{
							if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
							{
								label.ForeColor = Class181.color_4;
							}
							else
							{
								label.ForeColor = Color.LightGray;
							}
						}
					}
				}
			}
		}

		// Token: 0x0600161E RID: 5662 RVA: 0x000986A4 File Offset: 0x000968A4
		private void method_13(object sender, SuperTabStripSelectedTabChangedEventArgs e)
		{
			SuperTabItem superTabItem = e.NewValue as SuperTabItem;
			SuperTabControlPanel superTabControlPanel_ = superTabItem.AttachedControl as SuperTabControlPanel;
			if (superTabItem.Text == Class521.smethod_0(53375))
			{
				this.method_14(superTabControlPanel_);
			}
			else if (superTabItem.Text == Class521.smethod_0(53392))
			{
				this.method_15(superTabControlPanel_);
			}
			else if (superTabItem.Text == Class521.smethod_0(53413))
			{
				this.method_16(superTabControlPanel_);
			}
			else if (superTabItem.Text == Class521.smethod_0(53426))
			{
				this.method_17(superTabControlPanel_);
			}
			this.method_29();
		}

		// Token: 0x0600161F RID: 5663 RVA: 0x00098754 File Offset: 0x00096954
		private DevComponents.DotNetBar.TabControl method_14(SuperTabControlPanel superTabControlPanel_4 = null)
		{
			if (superTabControlPanel_4 == null)
			{
				superTabControlPanel_4 = this.superTabControlPanel_0;
			}
			string[] string_ = new string[]
			{
				Class521.smethod_0(53447),
				Class521.smethod_0(53464),
				Class521.smethod_0(53485),
				Class521.smethod_0(53510),
				Class521.smethod_0(53531),
				Class521.smethod_0(53548),
				Class521.smethod_0(53561)
			};
			return this.method_18(superTabControlPanel_4, string_);
		}

		// Token: 0x06001620 RID: 5664 RVA: 0x000987DC File Offset: 0x000969DC
		private DevComponents.DotNetBar.TabControl method_15(SuperTabControlPanel superTabControlPanel_4 = null)
		{
			if (superTabControlPanel_4 == null)
			{
				superTabControlPanel_4 = this.superTabControlPanel_3;
			}
			string[] string_ = Class521.smethod_0(53594).Split(new string[]
			{
				Class521.smethod_0(4736)
			}, StringSplitOptions.None);
			return this.method_18(superTabControlPanel_4, string_);
		}

		// Token: 0x06001621 RID: 5665 RVA: 0x00098824 File Offset: 0x00096A24
		private DevComponents.DotNetBar.TabControl method_16(SuperTabControlPanel superTabControlPanel_4 = null)
		{
			if (superTabControlPanel_4 == null)
			{
				superTabControlPanel_4 = this.superTabControlPanel_2;
			}
			string[] string_ = new string[]
			{
				Class521.smethod_0(53748),
				Class521.smethod_0(53765),
				Class521.smethod_0(53782),
				Class521.smethod_0(53799),
				Class521.smethod_0(53447),
				Class521.smethod_0(53816)
			};
			return this.method_18(superTabControlPanel_4, string_);
		}

		// Token: 0x06001622 RID: 5666 RVA: 0x000988A0 File Offset: 0x00096AA0
		private DevComponents.DotNetBar.TabControl method_17(SuperTabControlPanel superTabControlPanel_4 = null)
		{
			if (superTabControlPanel_4 == null)
			{
				superTabControlPanel_4 = this.superTabControlPanel_1;
			}
			string[] string_ = new string[]
			{
				Class521.smethod_0(53816),
				Class521.smethod_0(53829),
				Class521.smethod_0(53862),
				Class521.smethod_0(53895),
				Class521.smethod_0(53924),
				Class521.smethod_0(53941)
			};
			return this.method_18(superTabControlPanel_4, string_);
		}

		// Token: 0x06001623 RID: 5667 RVA: 0x0009891C File Offset: 0x00096B1C
		private DevComponents.DotNetBar.TabControl method_18(SuperTabControlPanel superTabControlPanel_4, string[] string_0)
		{
			DevComponents.DotNetBar.TabControl tabControl;
			if (superTabControlPanel_4.Controls.Count < 1)
			{
				tabControl = this.method_1(string_0, true);
				superTabControlPanel_4.Controls.Add(tabControl);
			}
			else
			{
				tabControl = (superTabControlPanel_4.Controls[0] as DevComponents.DotNetBar.TabControl);
			}
			return tabControl;
		}

		// Token: 0x06001624 RID: 5668 RVA: 0x00008F74 File Offset: 0x00007174
		private void method_19(object sender, EventArgs e)
		{
			this.method_27();
			this.method_29();
		}

		// Token: 0x06001625 RID: 5669 RVA: 0x00008F74 File Offset: 0x00007174
		private void method_20(object sender, EventArgs e)
		{
			this.method_27();
			this.method_29();
		}

		// Token: 0x06001626 RID: 5670 RVA: 0x00098968 File Offset: 0x00096B68
		private void method_21(EventArgs1 eventArgs1_0)
		{
			this.method_23();
			this.method_25();
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null)
			{
				if (!stkSymbol.IsStockCompany)
				{
					this.method_6();
					this.class287_0 = null;
				}
				else
				{
					this.method_29();
				}
			}
		}

		// Token: 0x06001627 RID: 5671 RVA: 0x00008F84 File Offset: 0x00007184
		private void method_22(EventArgs1 eventArgs1_0)
		{
			this.method_24();
		}

		// Token: 0x06001628 RID: 5672 RVA: 0x00008F8E File Offset: 0x0000718E
		private void method_23()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged += this.method_19;
			}
		}

		// Token: 0x06001629 RID: 5673 RVA: 0x00008FAF File Offset: 0x000071AF
		private void method_24()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged -= this.method_19;
			}
		}

		// Token: 0x0600162A RID: 5674 RVA: 0x000989AC File Offset: 0x00096BAC
		public void method_25()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.label_0.Text = Class521.smethod_0(53962);
				string caption = Class521.smethod_0(54007);
				this.toolTip_0.SetToolTip(this.label_0, caption);
			}
			else
			{
				StkSymbol stkSymbol = this.method_26();
				if (stkSymbol != null)
				{
					this.label_0.Text = stkSymbol.CNName + Class521.smethod_0(24872) + stkSymbol.Code + Class521.smethod_0(5046);
				}
				else
				{
					this.label_0.Text = Class521.smethod_0(1449);
				}
				this.toolTip_0.SetToolTip(this.label_0, Class521.smethod_0(1449));
			}
		}

		// Token: 0x0600162B RID: 5675 RVA: 0x00098A68 File Offset: 0x00096C68
		private StkSymbol method_26()
		{
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (stkSymbol == null && Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null)
			{
				stkSymbol = Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol;
			}
			return stkSymbol;
		}

		// Token: 0x0600162C RID: 5676 RVA: 0x00098AA8 File Offset: 0x00096CA8
		public void method_27()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.label_1.Text = Class521.smethod_0(54048);
				string caption = Class521.smethod_0(54085);
				this.toolTip_0.SetToolTip(this.label_1, caption);
			}
			else
			{
				string text = string.Empty;
				DateTime? dateTime = Base.Data.smethod_54();
				if (dateTime != null)
				{
					text = dateTime.Value.ToString(Class521.smethod_0(1702));
				}
				if (!string.IsNullOrEmpty(text))
				{
					this.label_1.Text = text;
				}
				else
				{
					this.label_1.Text = Class521.smethod_0(1449);
				}
				this.toolTip_0.SetToolTip(this.label_1, Class521.smethod_0(1449));
			}
		}

		// Token: 0x0600162D RID: 5677 RVA: 0x00098B6C File Offset: 0x00096D6C
		public void method_28()
		{
			foreach (object obj in this.superTabControl_0.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						Base.UI.smethod_69(superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl);
					}
				}
			}
		}

		// Token: 0x0600162E RID: 5678 RVA: 0x00098BF4 File Offset: 0x00096DF4
		private void method_29()
		{
			if (this.class11_0 != null)
			{
				StkSymbol stkSymbol = this.method_26();
				if (stkSymbol != null && stkSymbol.IsStockCompany)
				{
					string text = this.superTabControl_0.SelectedTab.Text;
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					string text2 = this.method_32();
					DateTime? dateTime = Base.Data.smethod_54();
					if (!string.IsNullOrEmpty(text2) && dateTime != null)
					{
						string quarterEndDateStr = Utility.GetQuarterEndDateStr(dateTime.Value, -1, Class521.smethod_0(1449));
						Enum2 @enum;
						string gparam_;
						if (text == Class521.smethod_0(53375))
						{
							@enum = Enum2.const_0;
							gparam_ = Class521.smethod_0(54122);
						}
						else if (text == Class521.smethod_0(53392))
						{
							@enum = Enum2.const_1;
							gparam_ = Class521.smethod_0(54139);
						}
						else if (text == Class521.smethod_0(53413))
						{
							@enum = Enum2.const_2;
							gparam_ = Class521.smethod_0(54156);
						}
						else
						{
							@enum = Enum2.const_3;
							gparam_ = Class521.smethod_0(54169);
						}
						Class16 @class = this.method_31(@enum, text2, quarterEndDateStr);
						if (@class == null)
						{
							if (this.method_30(text, text2, quarterEndDateStr))
							{
								string yearEndDateStr = Utility.GetYearEndDateStr(dateTime.Value, -10, Class521.smethod_0(1449));
								Class0<string, string, int, Class8<string, string, string>> value = new Class0<string, string, int, Class8<string, string, string>>(gparam_, TApp.LoginCode, this.class11_0.ResultCompressed ? 1 : 0, new Class8<string, string, string>(text2, yearEndDateStr, quarterEndDateStr));
								dictionary[Class521.smethod_0(1376)] = value;
								dictionary[Class521.smethod_0(1738)] = @enum;
								dictionary[Class521.smethod_0(54186)] = text2;
								dictionary[Class521.smethod_0(54195)] = quarterEndDateStr;
								this.class11_0.GetHttpResponseInBackground(dictionary);
								this.dictionary_0[text] = new KeyValuePair<string, string>(text2, quarterEndDateStr);
							}
						}
						else
						{
							this.method_33(@class.ReportAnlysType, @class.SrcYrDataTable, @class.SrcQtDataTable);
							this.method_37(null, null);
						}
					}
				}
			}
		}

		// Token: 0x0600162F RID: 5679 RVA: 0x00098DF0 File Offset: 0x00096FF0
		private bool method_30(string string_0, string string_1, string string_2)
		{
			bool result = true;
			if (this.dictionary_0 != null && this.dictionary_0.ContainsKey(string_0))
			{
				KeyValuePair<string, string> keyValuePair = this.dictionary_0[string_0];
				if (keyValuePair.Key == string_1 && keyValuePair.Value == string_2)
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x06001630 RID: 5680 RVA: 0x00098E48 File Offset: 0x00097048
		private Class16 method_31(Enum2 enum2_0, string string_0, string string_1)
		{
			Control2.Class297 @class = new Control2.Class297();
			@class.enum2_0 = enum2_0;
			@class.string_0 = string_0;
			@class.string_1 = string_1;
			Class16 result = null;
			if (this.list_0 != null)
			{
				result = this.list_0.SingleOrDefault(new Func<Class16, bool>(@class.method_0));
			}
			return result;
		}

		// Token: 0x06001631 RID: 5681 RVA: 0x00098E98 File Offset: 0x00097098
		private string method_32()
		{
			string result = null;
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null)
			{
				result = TExRoutine.GetCodeForFnDataApi(stkSymbol.Code, stkSymbol.ExchangeID);
			}
			return result;
		}

		// Token: 0x06001632 RID: 5682 RVA: 0x00098EC8 File Offset: 0x000970C8
		private void class11_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				Dictionary<string, object> requestDict = e.RequestDict;
				if (requestDict.ContainsKey(Class521.smethod_0(1832)))
				{
					Enum2 @enum = (Enum2)requestDict[Class521.smethod_0(1738)];
					DataTable dataTable = requestDict[Class521.smethod_0(1832)] as DataTable;
					DataTable dataTable2 = requestDict[Class521.smethod_0(1845)] as DataTable;
					this.method_33(@enum, dataTable, dataTable2);
					this.method_37(null, null);
					string string_ = requestDict[Class521.smethod_0(54186)] as string;
					string string_2 = requestDict[Class521.smethod_0(54195)] as string;
					DataTable dataTable_ = requestDict[Class521.smethod_0(1581)] as DataTable;
					Class16 item = new Class16(@enum, string_, string_2, dataTable_, dataTable, dataTable2);
					this.list_0.Add(item);
				}
			}
		}

		// Token: 0x06001633 RID: 5683 RVA: 0x00008FD0 File Offset: 0x000071D0
		private void class11_0_RequestError(object sender, ErrorEventArgs e)
		{
			Class184.smethod_0(e.Exception);
		}

		// Token: 0x06001634 RID: 5684 RVA: 0x00098FB4 File Offset: 0x000971B4
		private void method_33(Enum2 enum2_0, DataTable dataTable_0, DataTable dataTable_1)
		{
			if (this.tabControlPanel_1.Controls.Count > 0 && this.tabControlPanel_1.Controls[0] is Class287)
			{
				this.class287_0 = (this.tabControlPanel_1.Controls[0] as Class287);
			}
			else
			{
				this.class287_0 = new Class287();
				this.class287_0.SelectionChanged += this.class287_1_SelectionChanged;
				this.tabControlPanel_1.Controls.Clear();
				this.tabControlPanel_1.Controls.Add(this.class287_0);
			}
			DataTable rotatedDataTable = Utility.GetRotatedDataTable(dataTable_0, true, new int?(7));
			this.class287_0.ReportAnlysType = enum2_0;
			this.class287_0.SrcFnDataTable = dataTable_0;
			this.class287_0.method_1(rotatedDataTable);
			if (this.tabControlPanel_0.Controls.Count > 0 && this.tabControlPanel_0.Controls[0] is Class287)
			{
				this.class287_1 = (this.tabControlPanel_0.Controls[0] as Class287);
			}
			else
			{
				this.class287_1 = new Class287();
				this.class287_1.SelectionChanged += this.class287_1_SelectionChanged;
				this.tabControlPanel_0.Controls.Clear();
				this.tabControlPanel_0.Controls.Add(this.class287_1);
			}
			DataTable rotatedDataTable2 = Utility.GetRotatedDataTable(dataTable_1, true, new int?(7));
			this.class287_1.ReportAnlysType = enum2_0;
			this.class287_1.SrcFnDataTable = dataTable_1;
			this.class287_1.method_1(rotatedDataTable2);
		}

		// Token: 0x06001635 RID: 5685 RVA: 0x0009914C File Offset: 0x0009734C
		private void tabControl_0_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			Class287 class287_ = this.method_35();
			this.method_37(class287_, null);
		}

		// Token: 0x06001636 RID: 5686 RVA: 0x0009916C File Offset: 0x0009736C
		private void method_34(object sender, TabStripTabChangedEventArgs e)
		{
			DevComponents.DotNetBar.TabControl tabControl = sender as DevComponents.DotNetBar.TabControl;
			bool flag = false;
			if (tabControl.SelectedPanel.Controls.Count > 0 && tabControl.SelectedPanel.Controls[0] is TabControlPanel)
			{
				flag = true;
			}
			string text = e.NewTab.Text;
			if (!flag)
			{
				Class287 class287_ = this.method_35();
				this.method_37(class287_, text);
			}
			Class287 @class = this.method_35();
			if (@class != null)
			{
				foreach (object obj in ((IEnumerable)@class.Rows))
				{
					DataGridViewRow dataGridViewRow = (DataGridViewRow)obj;
					if (((string)dataGridViewRow.Cells[0].Value).StartsWith(text))
					{
						@class.IsSettingDataSource = true;
						if (!dataGridViewRow.Selected)
						{
							dataGridViewRow.Selected = true;
						}
						@class.IsSettingDataSource = false;
						break;
					}
				}
			}
		}

		// Token: 0x06001637 RID: 5687 RVA: 0x0009926C File Offset: 0x0009746C
		private Class287 method_35()
		{
			Class287 result = this.class287_0;
			if (this.tabControl_0.SelectedTabIndex == 1)
			{
				result = this.class287_1;
			}
			return result;
		}

		// Token: 0x06001638 RID: 5688 RVA: 0x0009929C File Offset: 0x0009749C
		private void class287_1_SelectionChanged(object sender, EventArgs e)
		{
			Class287 @class = sender as Class287;
			if (@class != null && !@class.IsSettingDataSource && @class.SelectedRows != null && @class.SelectedRows.Count > 0)
			{
				TabControlPanel tabControlPanel = @class.Parent as TabControlPanel;
				if (tabControlPanel != null && tabControlPanel.TabItem.IsSelected && @class.method_3() != null)
				{
					this.method_37(@class, null);
				}
			}
		}

		// Token: 0x06001639 RID: 5689 RVA: 0x00099300 File Offset: 0x00097500
		private DevComponents.DotNetBar.TabControl method_36(Enum2 enum2_0)
		{
			DevComponents.DotNetBar.TabControl result = null;
			if (enum2_0 == Enum2.const_0)
			{
				result = this.method_14(null);
			}
			else if (enum2_0 == Enum2.const_1)
			{
				result = this.method_15(null);
			}
			else if (enum2_0 == Enum2.const_2)
			{
				result = this.method_16(null);
			}
			else if (enum2_0 == Enum2.const_3)
			{
				result = this.method_17(null);
			}
			return result;
		}

		// Token: 0x0600163A RID: 5690 RVA: 0x00008FDF File Offset: 0x000071DF
		private void button_0_Click(object sender, EventArgs e)
		{
			Base.UI.MainForm.method_81();
		}

		// Token: 0x0600163B RID: 5691 RVA: 0x0009934C File Offset: 0x0009754C
		private void method_37(Class287 class287_2 = null, string string_0 = null)
		{
			if (class287_2 == null)
			{
				class287_2 = this.class287_0;
			}
			if (class287_2 != null && class287_2.Rows.Count >= 1)
			{
				if (string.IsNullOrEmpty(string_0))
				{
					DataGridViewRow dataGridViewRow = class287_2.method_3();
					if (dataGridViewRow != null)
					{
						string_0 = (string)dataGridViewRow.Cells[0].Value;
					}
				}
				if (!string.IsNullOrEmpty(string_0))
				{
					DevComponents.DotNetBar.TabControl tabControl = this.method_36(class287_2.ReportAnlysType);
					TabControlPanel tabControlPanel = null;
					foreach (object obj in tabControl.Controls)
					{
						if (obj is TabControlPanel)
						{
							TabControlPanel tabControlPanel2 = obj as TabControlPanel;
							if (string_0.Contains(tabControlPanel2.TabItem.Text))
							{
								tabControlPanel = tabControlPanel2;
								break;
							}
						}
					}
					if (tabControlPanel == null)
					{
						TabItem tabItem = new TabItem();
						tabItem.Text = string_0;
						tabControlPanel = new TabControlPanel();
						tabControlPanel.Dock = DockStyle.Fill;
						tabControlPanel.Location = new Point(0, 0);
						tabControlPanel.Padding = new System.Windows.Forms.Padding(0);
						tabControlPanel.Size = new Size(596, 278);
						tabControlPanel.Style.BackColor1.Color = SystemColors.Control;
						tabControlPanel.Style.Border = eBorderType.SingleLine;
						tabControlPanel.Style.BorderColor.Color = Color.FromArgb(93, 93, 93);
						tabControlPanel.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
						tabControlPanel.Style.GradientAngle = -90;
						tabControlPanel.TabItem = tabItem;
						tabItem.AttachedControl = tabControlPanel;
						tabControl.Tabs.Add(tabItem);
					}
					else if (tabControlPanel.Controls.Count > 0)
					{
						Control control = tabControlPanel.Controls[0];
						if (control is GraphCtrlStat)
						{
							(control as GraphCtrlStat).method_3();
						}
						tabControlPanel.Controls.Clear();
					}
					DataTable rotatedDataTable = Utility.GetRotatedDataTable(class287_2.SrcFnDataTable, true, new int?(10));
					this.method_38(tabControlPanel, rotatedDataTable);
					tabControl.SelectedPanel = tabControlPanel;
				}
			}
		}

		// Token: 0x0600163C RID: 5692 RVA: 0x0009955C File Offset: 0x0009775C
		private void method_38(TabControlPanel tabControlPanel_2, DataTable dataTable_0)
		{
			string text = tabControlPanel_2.TabItem.Text;
			DataRow dataRow = dataTable_0.Select(string.Concat(new object[]
			{
				dataTable_0.Columns[0],
				Class521.smethod_0(54208),
				text,
				Class521.smethod_0(54221)
			})).FirstOrDefault<DataRow>();
			if (dataRow != null)
			{
				DataColumnCollection columns = dataTable_0.Columns;
				object[] itemArray = dataRow.ItemArray;
				this.method_39(tabControlPanel_2, columns, itemArray);
			}
		}

		// Token: 0x0600163D RID: 5693 RVA: 0x000995D8 File Offset: 0x000977D8
		private void method_39(TabControlPanel tabControlPanel_2, DataColumnCollection dataColumnCollection_0, object[] object_0)
		{
			string text = tabControlPanel_2.TabItem.Text;
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = text;
			graphPane.XAxis.Type = AxisType.Text;
			graphPane.XAxis.Title.IsVisible = false;
			graphPane.YAxis.Title.Text = text;
			graphPane.Legend.IsVisible = false;
			graphPane.Chart.Border.IsVisible = false;
			if (object_0 != null)
			{
				List<string> list = new List<string>();
				List<double> list2 = new List<double>();
				int i = object_0.Length - 1;
				while (i > 0)
				{
					double item = double.NaN;
					object obj = object_0[i];
					if (obj == null || obj.GetType() != typeof(string))
					{
						goto IL_F2;
					}
					string text2 = obj as string;
					if (!string.IsNullOrEmpty(text2) && text2 != Class521.smethod_0(52046))
					{
						try
						{
							item = Convert.ToDouble(obj);
							goto IL_101;
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
							goto IL_101;
						}
						goto IL_F2;
					}
					IL_101:
					list2.Add(item);
					list.Add(dataColumnCollection_0[i].ColumnName);
					i--;
					continue;
					IL_F2:
					try
					{
						item = Convert.ToDouble(obj);
					}
					catch
					{
					}
					goto IL_101;
				}
				double[] array = list2.ToArray();
				Color color_ = Class181.color_17;
				graphPane.AddBar(text, null, array, color_).Bar.Border.IsVisible = false;
				graphCtrlStat.method_1(array);
				graphPane.XAxis.Scale.TextLabels = list.ToArray();
			}
			else
			{
				graphPane.YAxis.Scale.IsVisible = false;
			}
			graphCtrlStat.AxisChange();
			graphCtrlStat.Refresh();
			try
			{
				tabControlPanel_2.smethod_0();
				tabControlPanel_2.SuspendLayout();
				if (tabControlPanel_2.Controls.Count > 0)
				{
					Control control = tabControlPanel_2.Controls[0];
					if (control is GraphCtrlStat)
					{
						(control as GraphCtrlStat).method_3();
					}
					tabControlPanel_2.Controls.Clear();
				}
				tabControlPanel_2.Controls.Add(graphCtrlStat);
				tabControlPanel_2.ResumeLayout();
				tabControlPanel_2.smethod_1();
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
		}

		// Token: 0x0600163E RID: 5694 RVA: 0x0009980C File Offset: 0x00097A0C
		protected void CreateHandle()
		{
			if (!base.IsHandleCreated)
			{
				try
				{
					base.CreateHandle();
				}
				catch
				{
				}
				finally
				{
					if (!base.IsHandleCreated)
					{
						base.RecreateHandle();
					}
				}
			}
		}

		// Token: 0x0600163F RID: 5695 RVA: 0x00008FED File Offset: 0x000071ED
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001640 RID: 5696 RVA: 0x0009985C File Offset: 0x00097A5C
		private void method_40()
		{
			this.icontainer_0 = new Container();
			this.tableLayoutPanel_0 = new TableLayoutPanel();
			this.tableLayoutPanel_1 = new TableLayoutPanel();
			this.label_0 = new System.Windows.Forms.Label();
			this.label_1 = new System.Windows.Forms.Label();
			this.button_0 = new Button();
			this.label_2 = new System.Windows.Forms.Label();
			this.tableLayoutPanel_2 = new TableLayoutPanel();
			this.tabControl_0 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_1 = new TabControlPanel();
			this.tabItem_1 = new TabItem(this.icontainer_0);
			this.tabControlPanel_0 = new TabControlPanel();
			this.tabItem_0 = new TabItem(this.icontainer_0);
			this.superTabControl_0 = new SuperTabControl();
			this.superTabControlPanel_0 = new SuperTabControlPanel();
			this.superTabItem_3 = new SuperTabItem();
			this.superTabControlPanel_1 = new SuperTabControlPanel();
			this.superTabItem_0 = new SuperTabItem();
			this.superTabControlPanel_2 = new SuperTabControlPanel();
			this.superTabItem_1 = new SuperTabItem();
			this.superTabControlPanel_3 = new SuperTabControlPanel();
			this.superTabItem_2 = new SuperTabItem();
			this.tableLayoutPanel_0.SuspendLayout();
			this.tableLayoutPanel_1.SuspendLayout();
			this.tableLayoutPanel_2.SuspendLayout();
			((ISupportInitialize)this.tabControl_0).BeginInit();
			this.tabControl_0.SuspendLayout();
			((ISupportInitialize)this.superTabControl_0).BeginInit();
			this.superTabControl_0.SuspendLayout();
			base.SuspendLayout();
			this.tableLayoutPanel_0.ColumnCount = 1;
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Controls.Add(this.tableLayoutPanel_1, 0, 0);
			this.tableLayoutPanel_0.Controls.Add(this.tableLayoutPanel_2, 0, 1);
			this.tableLayoutPanel_0.Dock = DockStyle.Fill;
			this.tableLayoutPanel_0.Location = new Point(0, 0);
			this.tableLayoutPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_0.Name = Class521.smethod_0(53215);
			this.tableLayoutPanel_0.RowCount = 2;
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Absolute, 31f));
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Size = new Size(1324, 316);
			this.tableLayoutPanel_0.TabIndex = 0;
			this.tableLayoutPanel_1.ColumnCount = 4;
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 110f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 110f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Controls.Add(this.label_0, 0, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_1, 2, 0);
			this.tableLayoutPanel_1.Controls.Add(this.button_0, 3, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_2, 1, 0);
			this.tableLayoutPanel_1.Dock = DockStyle.Fill;
			this.tableLayoutPanel_1.Location = new Point(0, 0);
			this.tableLayoutPanel_1.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_1.Name = Class521.smethod_0(54226);
			this.tableLayoutPanel_1.RowCount = 1;
			this.tableLayoutPanel_1.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Size = new Size(1324, 31);
			this.tableLayoutPanel_1.TabIndex = 0;
			this.label_0.Anchor = AnchorStyles.None;
			this.label_0.AutoSize = true;
			this.label_0.BackColor = Color.Transparent;
			this.label_0.Location = new Point(34, 8);
			this.label_0.Name = Class521.smethod_0(54251);
			this.label_0.Size = new Size(131, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(54272);
			this.label_1.Anchor = AnchorStyles.Left;
			this.label_1.AutoSize = true;
			this.label_1.BackColor = Color.Transparent;
			this.label_1.Location = new Point(313, 8);
			this.label_1.Name = Class521.smethod_0(54301);
			this.label_1.Size = new Size(87, 15);
			this.label_1.TabIndex = 1;
			this.label_1.Text = Class521.smethod_0(54322);
			this.button_0.Anchor = AnchorStyles.Left;
			this.button_0.BackColor = Color.Transparent;
			this.button_0.Location = new Point(422, 2);
			this.button_0.Margin = new System.Windows.Forms.Padding(2);
			this.button_0.Name = Class521.smethod_0(54339);
			this.button_0.Size = new Size(87, 27);
			this.button_0.TabIndex = 2;
			this.button_0.Text = Class521.smethod_0(54360);
			this.button_0.UseVisualStyleBackColor = false;
			this.label_2.Anchor = AnchorStyles.Right;
			this.label_2.AutoSize = true;
			this.label_2.BackColor = Color.Transparent;
			this.label_2.Location = new Point(232, 8);
			this.label_2.Name = Class521.smethod_0(54377);
			this.label_2.Size = new Size(75, 15);
			this.label_2.TabIndex = 3;
			this.label_2.Text = Class521.smethod_0(54402);
			this.tableLayoutPanel_2.BackColor = Color.Transparent;
			this.tableLayoutPanel_2.ColumnCount = 2;
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
			this.tableLayoutPanel_2.Controls.Add(this.tabControl_0, 1, 0);
			this.tableLayoutPanel_2.Controls.Add(this.superTabControl_0, 0, 0);
			this.tableLayoutPanel_2.Dock = DockStyle.Fill;
			this.tableLayoutPanel_2.Location = new Point(0, 31);
			this.tableLayoutPanel_2.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_2.Name = Class521.smethod_0(54423);
			this.tableLayoutPanel_2.RowCount = 1;
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
			this.tableLayoutPanel_2.Size = new Size(1324, 285);
			this.tableLayoutPanel_2.TabIndex = 1;
			this.tabControl_0.BackColor = Color.Transparent;
			this.tabControl_0.CanReorderTabs = true;
			this.tabControl_0.Controls.Add(this.tabControlPanel_1);
			this.tabControl_0.Controls.Add(this.tabControlPanel_0);
			this.tabControl_0.Dock = DockStyle.Fill;
			this.tabControl_0.Location = new Point(662, 0);
			this.tabControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_0.Name = Class521.smethod_0(54448);
			this.tabControl_0.SelectedTabFont = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold);
			this.tabControl_0.SelectedTabIndex = 0;
			this.tabControl_0.Size = new Size(662, 285);
			this.tabControl_0.Style = eTabStripStyle.Flat;
			this.tabControl_0.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_0.TabIndex = 0;
			this.tabControl_0.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_0.Tabs.Add(this.tabItem_1);
			this.tabControl_0.Tabs.Add(this.tabItem_0);
			this.tabControl_0.Text = Class521.smethod_0(20184);
			this.tabControlPanel_1.Dock = DockStyle.Fill;
			this.tabControlPanel_1.Location = new Point(0, 0);
			this.tabControlPanel_1.Name = Class521.smethod_0(54473);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_1.Size = new Size(662, 257);
			this.tabControlPanel_1.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_1.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_1.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_1.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_1.Style.GradientAngle = -90;
			this.tabControlPanel_1.TabIndex = 1;
			this.tabControlPanel_1.TabItem = this.tabItem_1;
			this.tabItem_1.AttachedControl = this.tabControlPanel_1;
			this.tabItem_1.Name = Class521.smethod_0(54506);
			this.tabItem_1.Text = Class521.smethod_0(54531);
			this.tabControlPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_0.Location = new Point(0, 0);
			this.tabControlPanel_0.Name = Class521.smethod_0(54544);
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_0.Size = new Size(662, 257);
			this.tabControlPanel_0.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_0.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_0.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_0.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_0.Style.GradientAngle = -90;
			this.tabControlPanel_0.TabIndex = 2;
			this.tabControlPanel_0.TabItem = this.tabItem_0;
			this.tabItem_0.AttachedControl = this.tabControlPanel_0;
			this.tabItem_0.Name = Class521.smethod_0(54577);
			this.tabItem_0.Text = Class521.smethod_0(54602);
			this.superTabControl_0.ControlBox.CloseBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.MenuBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.SubItems.AddRange(new BaseItem[]
			{
				this.superTabControl_0.ControlBox.MenuBox,
				this.superTabControl_0.ControlBox.CloseBox
			});
			this.superTabControl_0.Controls.Add(this.superTabControlPanel_0);
			this.superTabControl_0.Controls.Add(this.superTabControlPanel_1);
			this.superTabControl_0.Controls.Add(this.superTabControlPanel_2);
			this.superTabControl_0.Controls.Add(this.superTabControlPanel_3);
			this.superTabControl_0.Dock = DockStyle.Fill;
			this.superTabControl_0.Location = new Point(0, 0);
			this.superTabControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.superTabControl_0.Name = Class521.smethod_0(54615);
			this.superTabControl_0.ReorderTabsEnabled = true;
			this.superTabControl_0.SelectedTabFont = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Bold);
			this.superTabControl_0.SelectedTabIndex = 0;
			this.superTabControl_0.Size = new Size(662, 285);
			this.superTabControl_0.TabAlignment = eTabStripAlignment.Left;
			this.superTabControl_0.TabFont = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.superTabControl_0.TabIndex = 1;
			this.superTabControl_0.Tabs.AddRange(new BaseItem[]
			{
				this.superTabItem_3,
				this.superTabItem_2,
				this.superTabItem_1,
				this.superTabItem_0
			});
			this.superTabControl_0.TabStyle = eSuperTabStyle.Office2010BackstageBlue;
			this.superTabControl_0.Text = Class521.smethod_0(54644);
			this.superTabControlPanel_0.Dock = DockStyle.Fill;
			this.superTabControlPanel_0.Location = new Point(109, 0);
			this.superTabControlPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.superTabControlPanel_0.Name = Class521.smethod_0(54669);
			this.superTabControlPanel_0.Size = new Size(553, 285);
			this.superTabControlPanel_0.TabIndex = 1;
			this.superTabControlPanel_0.TabItem = this.superTabItem_3;
			this.superTabItem_3.AttachedControl = this.superTabControlPanel_0;
			this.superTabItem_3.GlobalItem = false;
			this.superTabItem_3.Name = Class521.smethod_0(54698);
			this.superTabItem_3.Text = Class521.smethod_0(53375);
			this.superTabControlPanel_1.Dock = DockStyle.Fill;
			this.superTabControlPanel_1.Location = new Point(109, 0);
			this.superTabControlPanel_1.Name = Class521.smethod_0(54731);
			this.superTabControlPanel_1.Size = new Size(553, 285);
			this.superTabControlPanel_1.TabIndex = 0;
			this.superTabControlPanel_1.TabItem = this.superTabItem_0;
			this.superTabItem_0.AttachedControl = this.superTabControlPanel_1;
			this.superTabItem_0.GlobalItem = false;
			this.superTabItem_0.Name = Class521.smethod_0(54760);
			this.superTabItem_0.Text = Class521.smethod_0(53426);
			this.superTabControlPanel_2.Dock = DockStyle.Fill;
			this.superTabControlPanel_2.Location = new Point(109, 0);
			this.superTabControlPanel_2.Name = Class521.smethod_0(54797);
			this.superTabControlPanel_2.Size = new Size(553, 285);
			this.superTabControlPanel_2.TabIndex = 0;
			this.superTabControlPanel_2.TabItem = this.superTabItem_1;
			this.superTabItem_1.AttachedControl = this.superTabControlPanel_2;
			this.superTabItem_1.GlobalItem = false;
			this.superTabItem_1.Name = Class521.smethod_0(54826);
			this.superTabItem_1.Text = Class521.smethod_0(53413);
			this.superTabControlPanel_3.Dock = DockStyle.Fill;
			this.superTabControlPanel_3.Location = new Point(109, 0);
			this.superTabControlPanel_3.Name = Class521.smethod_0(54859);
			this.superTabControlPanel_3.Size = new Size(553, 285);
			this.superTabControlPanel_3.TabIndex = 0;
			this.superTabControlPanel_3.TabItem = this.superTabItem_2;
			this.superTabItem_2.AttachedControl = this.superTabControlPanel_3;
			this.superTabItem_2.GlobalItem = false;
			this.superTabItem_2.Name = Class521.smethod_0(54888);
			this.superTabItem_2.Text = Class521.smethod_0(53392);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tableLayoutPanel_0);
			base.Name = Class521.smethod_0(54925);
			base.Size = new Size(1324, 316);
			this.tableLayoutPanel_0.ResumeLayout(false);
			this.tableLayoutPanel_1.ResumeLayout(false);
			this.tableLayoutPanel_1.PerformLayout();
			this.tableLayoutPanel_2.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_0).EndInit();
			this.tabControl_0.ResumeLayout(false);
			((ISupportInitialize)this.superTabControl_0).EndInit();
			this.superTabControl_0.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000B22 RID: 2850
		private Class11 class11_0;

		// Token: 0x04000B23 RID: 2851
		private Class287 class287_0;

		// Token: 0x04000B24 RID: 2852
		private Class287 class287_1;

		// Token: 0x04000B25 RID: 2853
		private System.Windows.Forms.ToolTip toolTip_0 = new System.Windows.Forms.ToolTip();

		// Token: 0x04000B26 RID: 2854
		private const int int_0 = 10;

		// Token: 0x04000B27 RID: 2855
		private List<Class16> list_0;

		// Token: 0x04000B28 RID: 2856
		private Dictionary<string, KeyValuePair<string, string>> dictionary_0;

		// Token: 0x04000B29 RID: 2857
		private IContainer icontainer_0;

		// Token: 0x04000B2A RID: 2858
		private TableLayoutPanel tableLayoutPanel_0;

		// Token: 0x04000B2B RID: 2859
		private TableLayoutPanel tableLayoutPanel_1;

		// Token: 0x04000B2C RID: 2860
		private System.Windows.Forms.Label label_0;

		// Token: 0x04000B2D RID: 2861
		private System.Windows.Forms.Label label_1;

		// Token: 0x04000B2E RID: 2862
		private Button button_0;

		// Token: 0x04000B2F RID: 2863
		private SuperTabControl superTabControl_0;

		// Token: 0x04000B30 RID: 2864
		private SuperTabControlPanel superTabControlPanel_0;

		// Token: 0x04000B31 RID: 2865
		private SuperTabControlPanel superTabControlPanel_1;

		// Token: 0x04000B32 RID: 2866
		private SuperTabItem superTabItem_0;

		// Token: 0x04000B33 RID: 2867
		private SuperTabControlPanel superTabControlPanel_2;

		// Token: 0x04000B34 RID: 2868
		private SuperTabItem superTabItem_1;

		// Token: 0x04000B35 RID: 2869
		private SuperTabControlPanel superTabControlPanel_3;

		// Token: 0x04000B36 RID: 2870
		private SuperTabItem superTabItem_2;

		// Token: 0x04000B37 RID: 2871
		private DevComponents.DotNetBar.TabControl tabControl_0;

		// Token: 0x04000B38 RID: 2872
		private TabControlPanel tabControlPanel_0;

		// Token: 0x04000B39 RID: 2873
		private TabItem tabItem_0;

		// Token: 0x04000B3A RID: 2874
		private TabControlPanel tabControlPanel_1;

		// Token: 0x04000B3B RID: 2875
		private TabItem tabItem_1;

		// Token: 0x04000B3C RID: 2876
		private TableLayoutPanel tableLayoutPanel_2;

		// Token: 0x04000B3D RID: 2877
		private System.Windows.Forms.Label label_2;

		// Token: 0x04000B3E RID: 2878
		private SuperTabItem superTabItem_3;

		// Token: 0x0200021A RID: 538
		[CompilerGenerated]
		private sealed class Class297
		{
			// Token: 0x06001642 RID: 5698 RVA: 0x0009A8C8 File Offset: 0x00098AC8
			internal bool method_0(Class16 class16_0)
			{
				bool result;
				if (class16_0.ReportAnlysType == this.enum2_0 && class16_0.ts_code == this.string_0)
				{
					result = (class16_0.end_date == this.string_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000B3F RID: 2879
			public Enum2 enum2_0;

			// Token: 0x04000B40 RID: 2880
			public string string_0;

			// Token: 0x04000B41 RID: 2881
			public string string_1;
		}
	}
}
