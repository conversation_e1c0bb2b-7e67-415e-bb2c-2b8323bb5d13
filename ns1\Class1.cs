﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using ns18;

namespace ns1
{
	// Token: 0x02000003 RID: 3
	[CompilerGenerated]
	internal sealed class Class1<T>
	{
		// Token: 0x17000005 RID: 5
		// (get) Token: 0x06000009 RID: 9 RVA: 0x00010118 File Offset: 0x0000E318
		public T codes
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002A7B File Offset: 0x00000C7B
		[DebuggerHidden]
		public Class1(T gparam_1)
		{
			this.gparam_0 = gparam_1;
		}

		// Token: 0x0600000B RID: 11 RVA: 0x00010130 File Offset: 0x0000E330
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class1<T> @class = obj as Class1<T>;
			bool result;
			if (@class != null)
			{
				result = EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600000C RID: 12 RVA: 0x00010168 File Offset: 0x0000E368
		[DebuggerHidden]
		public int GetHashCode()
		{
			return 88452124 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0);
		}

		// Token: 0x0600000D RID: 13 RVA: 0x00010190 File Offset: 0x0000E390
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = Class521.smethod_0(166);
			object[] array = new object[1];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_4B;
				}
			}
			obj = ptr.ToString();
			IL_4B:
			array[num] = obj;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000005 RID: 5
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;
	}
}
