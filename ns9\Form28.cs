﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using ns18;
using ns21;
using ns22;
using ns23;
using ns28;

namespace ns9
{
	// Token: 0x0200041B RID: 1051
	internal sealed partial class Form28 : Form
	{
		// Token: 0x0600286F RID: 10351 RVA: 0x0000FC9E File Offset: 0x0000DE9E
		private string method_0(string string_0)
		{
			string_0 = string_0.Replace(Class521.smethod_0(121202), Class521.smethod_0(121215));
			string_0 = string_0.Replace(Class521.smethod_0(121244), Class521.smethod_0(121265));
			return string_0;
		}

		// Token: 0x06002870 RID: 10352 RVA: 0x0010FBD8 File Offset: 0x0010DDD8
		public Form28()
		{
			this.method_1();
			base.Icon = Class547.smethod_0();
			this.Text = this.method_0(this.Text);
			if (this.Text.Length == 0)
			{
				this.Text = Class521.smethod_0(121580);
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				control.Text = this.method_0(control.Text);
				foreach (object obj2 in control.Controls)
				{
					Control control2 = (Control)obj2;
					control2.Text = this.method_0(control2.Text);
				}
			}
		}

		// Token: 0x06002871 RID: 10353 RVA: 0x0010FCE0 File Offset: 0x0010DEE0
		public Form28(EventArgs36 eventArgs36_1) : this()
		{
			if (eventArgs36_1 == null)
			{
				return;
			}
			if (!eventArgs36_1.CanContinue)
			{
				this.button_0.Visible = false;
			}
			this.eventArgs36_0 = eventArgs36_1;
			if (eventArgs36_1.SecurityMessage.Length > 0)
			{
				this.class549_0.Text = eventArgs36_1.SecurityMessage;
			}
			else
			{
				StringBuilder stringBuilder = new StringBuilder();
				stringBuilder.Append(Class521.smethod_0(121605));
				if (eventArgs36_1.CanContinue)
				{
					stringBuilder.Append(Class521.smethod_0(121907));
				}
				stringBuilder.Append(eventArgs36_1.SecurityException.Message);
				this.class549_0.Text = this.method_0(stringBuilder.ToString());
			}
			int num = this.class549_0.Bottom + 60;
			if (num > base.ClientSize.Height)
			{
				base.ClientSize = new Size(base.ClientSize.Width, num);
			}
		}

		// Token: 0x06002872 RID: 10354 RVA: 0x0000FDB2 File Offset: 0x0000DFB2
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002873 RID: 10355 RVA: 0x0010FDC8 File Offset: 0x0010DFC8
		private void method_1()
		{
			this.button_1 = new Button();
			this.button_0 = new Button();
			this.control15_0 = new Control15();
			this.class549_0 = new Class549();
			this.control16_0 = new Control16();
			base.SuspendLayout();
			this.button_1.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_1.FlatStyle = FlatStyle.System;
			this.button_1.Location = new Point(406, 183);
			this.button_1.Name = Class521.smethod_0(122105);
			this.button_1.Size = new Size(140, 28);
			this.button_1.TabIndex = 0;
			this.button_1.Text = Class521.smethod_0(122122);
			this.button_1.Click += this.button_1_Click;
			this.button_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_0.FlatStyle = FlatStyle.System;
			this.button_0.Location = new Point(258, 183);
			this.button_0.Name = Class521.smethod_0(122131);
			this.button_0.Size = new Size(140, 28);
			this.button_0.TabIndex = 1;
			this.button_0.Text = Class521.smethod_0(122152);
			this.button_0.Click += this.button_0_Click;
			this.control15_0.BackColor = Color.FromArgb(36, 96, 179);
			this.control15_0.Dock = DockStyle.Top;
			this.control15_0.ForeColor = Color.White;
			this.control15_0.IconState = Enum36.const_2;
			this.control15_0.Image = null;
			this.control15_0.Location = new Point(0, 0);
			this.control15_0.Name = Class521.smethod_0(120304);
			this.control15_0.Size = new Size(560, 67);
			this.control15_0.TabIndex = 7;
			this.control15_0.TabStop = false;
			this.control15_0.Text = Class521.smethod_0(122165);
			this.class549_0.Anchor = (AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
			this.class549_0.FlatStyle = FlatStyle.System;
			this.class549_0.Location = new Point(28, 83);
			this.class549_0.Name = Class521.smethod_0(120378);
			this.class549_0.Size = new Size(510, 13);
			this.class549_0.TabIndex = 14;
			this.class549_0.Text = Class521.smethod_0(120378);
			this.class549_0.UseMnemonic = false;
			this.control16_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Left);
			this.control16_0.Cursor = Cursors.Hand;
			this.control16_0.Location = new Point(8, 181);
			this.control16_0.Name = Class521.smethod_0(122274);
			this.control16_0.Size = new Size(157, 37);
			this.control16_0.TabIndex = 15;
			this.control16_0.TabStop = false;
			this.control16_0.Text = Class521.smethod_0(122287);
			this.AutoScaleBaseSize = new Size(7, 15);
			this.BackColor = SystemColors.Window;
			base.ClientSize = new Size(560, 224);
			base.ControlBox = false;
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.control15_0);
			base.Controls.Add(this.class549_0);
			base.Controls.Add(this.control16_0);
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(122304);
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(121202);
			base.ResumeLayout(false);
		}

		// Token: 0x06002874 RID: 10356 RVA: 0x0000FDD1 File Offset: 0x0000DFD1
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.eventArgs36_0 != null)
			{
				this.eventArgs36_0.TryToContinue = true;
			}
			base.Close();
		}

		// Token: 0x06002875 RID: 10357 RVA: 0x0000FDED File Offset: 0x0000DFED
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.eventArgs36_0 != null)
			{
				this.eventArgs36_0.TryToContinue = false;
			}
			base.Close();
		}

		// Token: 0x04001444 RID: 5188
		private EventArgs36 eventArgs36_0;

		// Token: 0x04001445 RID: 5189
		private Button button_0;

		// Token: 0x04001446 RID: 5190
		private Button button_1;

		// Token: 0x04001447 RID: 5191
		private Control15 control15_0;

		// Token: 0x04001448 RID: 5192
		private Class549 class549_0;

		// Token: 0x04001449 RID: 5193
		private Control16 control16_0;

		// Token: 0x0400144A RID: 5194
		private IContainer icontainer_0;
	}
}
