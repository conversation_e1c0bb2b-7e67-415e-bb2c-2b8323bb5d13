﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;
using TEx.Trading;

namespace ns31
{
	// Token: 0x0200016B RID: 363
	internal sealed partial class Form11 : Form
	{
		// Token: 0x06000DC6 RID: 3526 RVA: 0x00059074 File Offset: 0x00057274
		public Form11(int int_0)
		{
			this.method_2();
			this.order_0 = Base.Trading.smethod_30(int_0);
			StkSymbol stkSymbol = SymbMgr.smethod_3(this.order_0.SymbolID);
			this.StkSymbol = stkSymbol;
			this.label_5.Text = Base.Trading.smethod_35((OrderType)this.order_0.OrderType);
			this.method_0(this.numericUpDown_1);
			this.method_1(this.numericUpDown_0);
			long num = this.order_0.Units;
			if (num < 1L)
			{
				num = 1L;
			}
			this.numericUpDown_0.Value = num;
			this.numericUpDown_1.Value = this.order_0.Price;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			base.Load += this.Form11_Load;
		}

		// Token: 0x06000DC7 RID: 3527 RVA: 0x00059170 File Offset: 0x00057370
		private void Form11_Load(object sender, EventArgs e)
		{
			if (base.Owner != null)
			{
				base.Location = new Point(base.Owner.Location.X + base.Owner.Width / 2 - base.Width / 2, base.Owner.Location.Y + base.Owner.Height / 2 - base.Height / 2);
			}
		}

		// Token: 0x06000DC8 RID: 3528 RVA: 0x000591E8 File Offset: 0x000573E8
		private void method_0(NumericUpDown numericUpDown_2)
		{
			decimal value = this.stkSymbol_0.LeastPriceVar.Value;
			numericUpDown_2.Increment = value;
			numericUpDown_2.Maximum = 99999999m;
			numericUpDown_2.Minimum = 0m;
			numericUpDown_2.DecimalPlaces = this.stkSymbol_0.DigitNb;
			numericUpDown_2.Increment = value;
		}

		// Token: 0x06000DC9 RID: 3529 RVA: 0x00006232 File Offset: 0x00004432
		private void method_1(NumericUpDown numericUpDown_2)
		{
			numericUpDown_2.Increment = 1m;
			numericUpDown_2.Maximum = 9999999m;
			numericUpDown_2.Minimum = 1m;
		}

		// Token: 0x06000DCA RID: 3530 RVA: 0x00059248 File Offset: 0x00057448
		private void button_1_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_0.Value;
			decimal value2 = this.numericUpDown_1.Value;
			if (this.order_0.Price != value2 || this.order_0.Units != value)
			{
				Base.Trading.smethod_63(this.Order.ID, value2, (long)Convert.ToInt32(value));
			}
			base.Close();
		}

		// Token: 0x06000DCB RID: 3531 RVA: 0x00004273 File Offset: 0x00002473
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000229 RID: 553
		// (get) Token: 0x06000DCC RID: 3532 RVA: 0x000592B8 File Offset: 0x000574B8
		// (set) Token: 0x06000DCD RID: 3533 RVA: 0x0000625C File Offset: 0x0000445C
		public Order Order
		{
			get
			{
				return this.order_0;
			}
			set
			{
				this.order_0 = value;
			}
		}

		// Token: 0x1700022A RID: 554
		// (get) Token: 0x06000DCE RID: 3534 RVA: 0x000592D0 File Offset: 0x000574D0
		// (set) Token: 0x06000DCF RID: 3535 RVA: 0x000592E8 File Offset: 0x000574E8
		public StkSymbol StkSymbol
		{
			get
			{
				return this.stkSymbol_0;
			}
			set
			{
				this.stkSymbol_0 = value;
				if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
				{
					this.label_0.Text = Class521.smethod_0(24382);
				}
				else
				{
					this.label_0.Text = this.stkSymbol_0.Code;
				}
			}
		}

		// Token: 0x06000DD0 RID: 3536 RVA: 0x00006267 File Offset: 0x00004467
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000DD1 RID: 3537 RVA: 0x00059344 File Offset: 0x00057544
		private void method_2()
		{
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.numericUpDown_1 = new NumericUpDown();
			this.label_5 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			((ISupportInitialize)this.numericUpDown_1).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(130, 25);
			this.label_0.Name = Class521.smethod_0(24391);
			this.label_0.Size = new Size(39, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(24408);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(75, 87);
			this.label_1.Name = Class521.smethod_0(5871);
			this.label_1.Size = new Size(52, 15);
			this.label_1.TabIndex = 2;
			this.label_1.Text = Class521.smethod_0(24417);
			this.numericUpDown_0.Location = new Point(133, 82);
			this.numericUpDown_0.Name = Class521.smethod_0(16172);
			this.numericUpDown_0.Size = new Size(67, 25);
			this.numericUpDown_0.TabIndex = 3;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(75, 118);
			this.label_2.Name = Class521.smethod_0(5827);
			this.label_2.Size = new Size(52, 15);
			this.label_2.TabIndex = 4;
			this.label_2.Text = Class521.smethod_0(24430);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(75, 25);
			this.label_3.Name = Class521.smethod_0(5849);
			this.label_3.Size = new Size(52, 15);
			this.label_3.TabIndex = 5;
			this.label_3.Text = Class521.smethod_0(5902);
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(75, 56);
			this.label_4.Name = Class521.smethod_0(7019);
			this.label_4.Size = new Size(52, 15);
			this.label_4.TabIndex = 6;
			this.label_4.Text = Class521.smethod_0(24443);
			this.numericUpDown_1.Location = new Point(133, 115);
			this.numericUpDown_1.Name = Class521.smethod_0(24456);
			this.numericUpDown_1.Size = new Size(103, 25);
			this.numericUpDown_1.TabIndex = 7;
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(130, 56);
			this.label_5.Name = Class521.smethod_0(24485);
			this.label_5.Size = new Size(67, 15);
			this.label_5.TabIndex = 8;
			this.label_5.Text = Class521.smethod_0(23718);
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(210, 163);
			this.button_0.Name = Class521.smethod_0(24207);
			this.button_0.Size = new Size(100, 29);
			this.button_0.TabIndex = 10;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(100, 163);
			this.button_1.Name = Class521.smethod_0(24224);
			this.button_1.Size = new Size(100, 29);
			this.button_1.TabIndex = 9;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(329, 206);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.numericUpDown_1);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.numericUpDown_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(24502);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.Manual;
			this.Text = Class521.smethod_0(24523);
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			((ISupportInitialize)this.numericUpDown_1).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000710 RID: 1808
		private Order order_0;

		// Token: 0x04000711 RID: 1809
		private StkSymbol stkSymbol_0;

		// Token: 0x04000712 RID: 1810
		private IContainer icontainer_0;

		// Token: 0x04000713 RID: 1811
		private Label label_0;

		// Token: 0x04000714 RID: 1812
		private Label label_1;

		// Token: 0x04000715 RID: 1813
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000716 RID: 1814
		private Label label_2;

		// Token: 0x04000717 RID: 1815
		private Label label_3;

		// Token: 0x04000718 RID: 1816
		private Label label_4;

		// Token: 0x04000719 RID: 1817
		private NumericUpDown numericUpDown_1;

		// Token: 0x0400071A RID: 1818
		private Label label_5;

		// Token: 0x0400071B RID: 1819
		private Button button_0;

		// Token: 0x0400071C RID: 1820
		private Button button_1;
	}
}
