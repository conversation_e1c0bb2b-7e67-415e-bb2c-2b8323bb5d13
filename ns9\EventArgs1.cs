﻿using System;
using System.Collections.Generic;
using TEx;

namespace ns9
{
	// Token: 0x02000085 RID: 133
	internal sealed class EventArgs1 : EventArgs
	{
		// Token: 0x06000475 RID: 1141 RVA: 0x00003ECA File Offset: 0x000020CA
		public EventArgs1(int int_2, int int_3, DateTime? nullable_1, bool bool_1, List<StkSymbol> list_1 = null)
		{
			this.int_0 = int_2;
			this.int_1 = int_3;
			this.nullable_0 = nullable_1;
			this.bool_0 = bool_1;
			this.list_0 = list_1;
		}

		// Token: 0x170000E2 RID: 226
		// (get) Token: 0x06000476 RID: 1142 RVA: 0x0002485C File Offset: 0x00022A5C
		public int OldSymbID
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x170000E3 RID: 227
		// (get) Token: 0x06000477 RID: 1143 RVA: 0x00024874 File Offset: 0x00022A74
		public int NewSymbID
		{
			get
			{
				return this.int_1;
			}
		}

		// Token: 0x170000E4 RID: 228
		// (get) Token: 0x06000478 RID: 1144 RVA: 0x0002488C File Offset: 0x00022A8C
		public DateTime? NewSymbLastDT
		{
			get
			{
				return this.nullable_0;
			}
		}

		// Token: 0x170000E5 RID: 229
		// (get) Token: 0x06000479 RID: 1145 RVA: 0x000248A4 File Offset: 0x00022AA4
		public bool IfOnlyNonSyncSelCht
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170000E6 RID: 230
		// (get) Token: 0x0600047A RID: 1146 RVA: 0x000248BC File Offset: 0x00022ABC
		public List<StkSymbol> SelectionSymbList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x040001B0 RID: 432
		private int int_0;

		// Token: 0x040001B1 RID: 433
		private int int_1;

		// Token: 0x040001B2 RID: 434
		private DateTime? nullable_0;

		// Token: 0x040001B3 RID: 435
		private bool bool_0;

		// Token: 0x040001B4 RID: 436
		private List<StkSymbol> list_0;
	}
}
