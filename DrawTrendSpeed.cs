﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns13;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006D RID: 109
	[Serializable]
	internal class DrawTrendSpeed : DrawSquare, ISerializable
	{
		// Token: 0x060003F8 RID: 1016 RVA: 0x00003ADA File Offset: 0x00001CDA
		public DrawTrendSpeed()
		{
		}

		// Token: 0x060003F9 RID: 1017 RVA: 0x00003AE2 File Offset: 0x00001CE2
		public DrawTrendSpeed(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5626);
			base.CanChgColor = false;
			base.IsOneClickLoc = false;
		}

		// Token: 0x060003FA RID: 1018 RVA: 0x00003B11 File Offset: 0x00001D11
		protected DrawTrendSpeed(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003FB RID: 1019 RVA: 0x00003B22 File Offset: 0x00001D22
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003FC RID: 1020 RVA: 0x00022A38 File Offset: 0x00020C38
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			BoxObj item = base.method_39(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			foreach (LineObj item2 in this.vmethod_24(chartCS_1, double_1, double_2, double_3, double_4, string_5))
			{
				list.Add(item2);
			}
			TextObj item3 = this.method_41(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			list.Add(item3);
			return list;
		}

		// Token: 0x060003FD RID: 1021 RVA: 0x00022AD4 File Offset: 0x00020CD4
		protected virtual List<LineObj> vmethod_24(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<LineObj> list = new List<LineObj>();
			double num = Math.Min(double_1, double_3);
			double num2 = Math.Max(double_1, double_3);
			double num3 = Math.Min(double_2, double_4);
			double num4 = Math.Max(double_2, double_4);
			double num5 = Math.Abs(double_4 - double_2);
			double double_5 = num;
			double double_6 = num2;
			double double_7;
			double double_8;
			double double_9;
			double double_10;
			if ((double_1 < double_3 && double_4 > double_2) || (double_3 < double_1 && double_2 > double_4))
			{
				double_7 = num3;
				double_8 = num4;
				double_9 = num4 - num5 / 3.0;
				double_10 = num4 - num5 / 3.0 * 2.0;
			}
			else
			{
				double_7 = num4;
				double_8 = num3 + num5 / 3.0;
				double_9 = num3 + num5 / 3.0 * 2.0;
				double_10 = num3;
			}
			this.method_40(list, chartCS_1, double_5, double_7, double_6, double_8, string_5);
			this.method_40(list, chartCS_1, double_5, double_7, double_6, double_9, string_5);
			this.method_40(list, chartCS_1, double_5, double_7, double_6, double_10, string_5);
			return list;
		}

		// Token: 0x060003FE RID: 1022 RVA: 0x00022BD0 File Offset: 0x00020DD0
		protected void method_40(List<LineObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			Class60 @class = DrawLine.smethod_0(chartCS_1, double_1, double_2, double_3, double_4);
			LineObj item = base.method_23(double_1, double_2, @class.X2, @class.Y2, string_5);
			list_2.Add(item);
		}

		// Token: 0x060003FF RID: 1023 RVA: 0x00022C0C File Offset: 0x00020E0C
		private TextObj method_41(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			double num = double_3 - double_1;
			double y = double_4 - double_2;
			double num2 = chartCS_1.GraphPane.YAxis.Scale.Max - chartCS_1.GraphPane.YAxis.Scale.Min;
			double num3 = chartCS_1.GraphPane.XAxis.Scale.Max - chartCS_1.GraphPane.XAxis.Scale.Min;
			float width = chartCS_1.GraphPane.Chart.Rect.Width;
			float height = chartCS_1.GraphPane.Chart.Rect.Height;
			double num4 = num2 / (double)height / (num3 / (double)width);
			double num5 = Math.Atan2(y, num * num4) * 57.29577951308232;
			if (num5 > -90.0 && num5 < 0.0)
			{
				num5 = -num5;
			}
			else if (num5 > -180.0 && num5 <= -90.0)
			{
				num5 += 180.0;
			}
			else if (num5 > 90.0 && num5 <= 180.0)
			{
				num5 = 180.0 - num5;
			}
			double double_5 = Math.Min(double_1, double_3);
			double double_6;
			if ((double_1 < double_3 && double_4 > double_2) || (double_3 < double_1 && double_2 > double_4))
			{
				double_6 = Math.Min(double_2, double_4);
			}
			else
			{
				double_6 = Math.Max(double_2, double_4);
			}
			TextObj textObj = base.method_27(chartCS_1, double_5, double_6, Math.Round(num5, 2).ToString() + Class521.smethod_0(5639), null, string_5);
			textObj.Location.AlignH = AlignH.Right;
			return textObj;
		}
	}
}
