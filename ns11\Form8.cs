﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using ns26;
using ns27;
using TEx;

namespace ns11
{
	// Token: 0x02000155 RID: 341
	internal sealed partial class Form8 : Form
	{
		// Token: 0x06000CF2 RID: 3314 RVA: 0x0004D074 File Offset: 0x0004B274
		public Form8(EventArgs22 eventArgs22_1)
		{
			this.method_0();
			StkSymbol stkSymbol = SymbMgr.smethod_3(eventArgs22_1.TranStock.SymbolID);
			this.eventArgs22_0 = eventArgs22_1;
			StSplit stSplit = eventArgs22_1.StSplit;
			long num = Convert.ToInt64(Math.Floor(eventArgs22_1.TranStock.Units / 10m * stSplit.RationedShares.Value * 10m));
			this.label_4.Text = stkSymbol.CNName;
			this.label_5.Text = stkSymbol.Code;
			this.label_7.Text = stSplit.Date.ToShortDateString();
			this.label_6.Text = stSplit.RationedSharePrice.Value.ToString();
			if (Base.Acct.CurrAccount.EndingBal != null)
			{
				long num2 = Convert.ToInt64(Math.Floor(Base.Acct.CurrAccount.EndingBal.Value / stSplit.RationedSharePrice.Value));
				if (num > num2)
				{
					num = num2;
				}
			}
			this.numericUpDown_0.Minimum = 1m;
			this.numericUpDown_0.Maximum = num;
			this.numericUpDown_0.Value = num;
			this.labelX_0.ForeColor = Color.Black;
			this.labelX_0.Text = Class521.smethod_0(15971) + num + Class521.smethod_0(16020);
			base.FormClosing += this.Form8_FormClosing;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
		}

		// Token: 0x06000CF3 RID: 3315 RVA: 0x0004D24C File Offset: 0x0004B44C
		private void Form8_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (this.eventArgs22_0.RationedShareBuyUnits == null)
			{
				this.eventArgs22_0.Cancel = true;
			}
		}

		// Token: 0x06000CF4 RID: 3316 RVA: 0x00005C31 File Offset: 0x00003E31
		private void button_1_Click(object sender, EventArgs e)
		{
			this.eventArgs22_0.RationedShareBuyUnits = new long?(Convert.ToInt64(this.numericUpDown_0.Value));
			base.Close();
		}

		// Token: 0x06000CF5 RID: 3317 RVA: 0x00005C5B File Offset: 0x00003E5B
		private void button_0_Click(object sender, EventArgs e)
		{
			this.eventArgs22_0.Cancel = true;
			base.Close();
		}

		// Token: 0x06000CF6 RID: 3318 RVA: 0x00005C71 File Offset: 0x00003E71
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000CF7 RID: 3319 RVA: 0x0004D27C File Offset: 0x0004B47C
		private void method_0()
		{
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.label_5 = new Label();
			this.label_6 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.pictureBox_0 = new PictureBox();
			this.labelX_0 = new LabelX();
			this.label_7 = new Label();
			this.label_8 = new Label();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			base.SuspendLayout();
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(293, 213);
			this.button_0.Name = Class521.smethod_0(10825);
			this.button_0.Size = new Size(110, 30);
			this.button_0.TabIndex = 7;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(172, 213);
			this.button_1.Name = Class521.smethod_0(10838);
			this.button_1.Size = new Size(110, 30);
			this.button_1.TabIndex = 6;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(107, 60);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(82, 15);
			this.label_0.TabIndex = 8;
			this.label_0.Text = Class521.smethod_0(16029);
			this.label_0.TextAlign = ContentAlignment.MiddleRight;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(107, 34);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(82, 15);
			this.label_1.TabIndex = 9;
			this.label_1.Text = Class521.smethod_0(16050);
			this.label_1.TextAlign = ContentAlignment.MiddleRight;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(107, 112);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(82, 15);
			this.label_2.TabIndex = 10;
			this.label_2.Text = Class521.smethod_0(16071);
			this.label_2.TextAlign = ContentAlignment.MiddleRight;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(107, 138);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(82, 15);
			this.label_3.TabIndex = 11;
			this.label_3.Text = Class521.smethod_0(16092);
			this.label_3.TextAlign = ContentAlignment.MiddleRight;
			this.label_4.BorderStyle = BorderStyle.Fixed3D;
			this.label_4.Location = new Point(193, 30);
			this.label_4.Name = Class521.smethod_0(16113);
			this.label_4.Size = new Size(130, 22);
			this.label_4.TabIndex = 12;
			this.label_4.Text = Class521.smethod_0(16113);
			this.label_4.TextAlign = ContentAlignment.MiddleLeft;
			this.label_5.BorderStyle = BorderStyle.Fixed3D;
			this.label_5.Location = new Point(193, 56);
			this.label_5.Name = Class521.smethod_0(16134);
			this.label_5.Size = new Size(130, 22);
			this.label_5.TabIndex = 13;
			this.label_5.Text = Class521.smethod_0(16134);
			this.label_5.TextAlign = ContentAlignment.MiddleLeft;
			this.label_6.BorderStyle = BorderStyle.Fixed3D;
			this.label_6.Location = new Point(193, 108);
			this.label_6.Name = Class521.smethod_0(16155);
			this.label_6.Size = new Size(130, 22);
			this.label_6.TabIndex = 14;
			this.label_6.Text = Class521.smethod_0(16155);
			this.label_6.TextAlign = ContentAlignment.MiddleLeft;
			this.numericUpDown_0.Location = new Point(193, 134);
			this.numericUpDown_0.Name = Class521.smethod_0(16172);
			this.numericUpDown_0.Size = new Size(130, 25);
			this.numericUpDown_0.TabIndex = 15;
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(84, 172);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 22;
			this.pictureBox_0.TabStop = false;
			this.labelX_0.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Font = new Font(Class521.smethod_0(7183), 9f);
			this.labelX_0.Location = new Point(107, 173);
			this.labelX_0.Name = Class521.smethod_0(16201);
			this.labelX_0.Size = new Size(291, 33);
			this.labelX_0.Style = eDotNetBarStyle.Office2003;
			this.labelX_0.TabIndex = 21;
			this.labelX_0.Text = Class521.smethod_0(16218);
			this.labelX_0.TextLineAlignment = StringAlignment.Near;
			this.labelX_0.WordWrap = true;
			this.label_7.BorderStyle = BorderStyle.Fixed3D;
			this.label_7.Location = new Point(193, 82);
			this.label_7.Name = Class521.smethod_0(5962);
			this.label_7.Size = new Size(130, 22);
			this.label_7.TabIndex = 24;
			this.label_7.Text = Class521.smethod_0(5962);
			this.label_7.TextAlign = ContentAlignment.MiddleLeft;
			this.label_8.AutoSize = true;
			this.label_8.Location = new Point(107, 86);
			this.label_8.Name = Class521.smethod_0(16231);
			this.label_8.Size = new Size(82, 15);
			this.label_8.TabIndex = 23;
			this.label_8.Text = Class521.smethod_0(16244);
			this.label_8.TextAlign = ContentAlignment.MiddleRight;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(431, 255);
			base.Controls.Add(this.label_7);
			base.Controls.Add(this.label_8);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.labelX_0);
			base.Controls.Add(this.numericUpDown_0);
			base.Controls.Add(this.label_6);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(16265);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(16294);
			base.TopMost = true;
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			((ISupportInitialize)this.pictureBox_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000645 RID: 1605
		private EventArgs22 eventArgs22_0;

		// Token: 0x04000646 RID: 1606
		private IContainer icontainer_0;

		// Token: 0x04000647 RID: 1607
		private Button button_0;

		// Token: 0x04000648 RID: 1608
		private Button button_1;

		// Token: 0x04000649 RID: 1609
		private Label label_0;

		// Token: 0x0400064A RID: 1610
		private Label label_1;

		// Token: 0x0400064B RID: 1611
		private Label label_2;

		// Token: 0x0400064C RID: 1612
		private Label label_3;

		// Token: 0x0400064D RID: 1613
		private Label label_4;

		// Token: 0x0400064E RID: 1614
		private Label label_5;

		// Token: 0x0400064F RID: 1615
		private Label label_6;

		// Token: 0x04000650 RID: 1616
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000651 RID: 1617
		private PictureBox pictureBox_0;

		// Token: 0x04000652 RID: 1618
		private LabelX labelX_0;

		// Token: 0x04000653 RID: 1619
		private Label label_7;

		// Token: 0x04000654 RID: 1620
		private Label label_8;
	}
}
