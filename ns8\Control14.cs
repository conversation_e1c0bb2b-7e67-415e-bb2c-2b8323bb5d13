﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns26;

namespace ns8
{
	// Token: 0x02000411 RID: 1041
	[DesignerCategory("Code")]
	internal sealed class Control14 : Control
	{
		// Token: 0x170006E5 RID: 1765
		// (get) Token: 0x0600282D RID: 10285 RVA: 0x0000F992 File Offset: 0x0000DB92
		// (set) Token: 0x0600282E RID: 10286 RVA: 0x0000F99A File Offset: 0x0000DB9A
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Browsable(true)]
		public string Text
		{
			get
			{
				return base.Text;
			}
			set
			{
				base.Text = value;
				this.Refresh();
			}
		}

		// Token: 0x0600282F RID: 10287 RVA: 0x0000F9A9 File Offset: 0x0000DBA9
		public void method_0()
		{
			this.timer_0.Enabled = false;
			this.image_0 = null;
			this.bool_0 = false;
			this.string_0 = string.Empty;
			this.Refresh();
			base.Height = 16;
		}

		// Token: 0x06002830 RID: 10288 RVA: 0x0000F9DE File Offset: 0x0000DBDE
		public void method_1()
		{
			this.timer_0.Enabled = true;
			this.image_0 = Class541.smethod_0(Class521.smethod_0(119534));
			this.bool_0 = true;
			this.Refresh();
		}

		// Token: 0x06002831 RID: 10289 RVA: 0x0000FA0E File Offset: 0x0000DC0E
		public void method_2()
		{
			this.method_3(string.Empty);
		}

		// Token: 0x06002832 RID: 10290 RVA: 0x0010D998 File Offset: 0x0010BB98
		public void method_3(string string_1)
		{
			this.string_0 = string_1;
			this.timer_0.Enabled = false;
			this.image_0 = Class541.smethod_0((string_1.Length > 0) ? Class521.smethod_0(119547) : Class521.smethod_0(9965));
			this.bool_1 = true;
			this.bool_0 = true;
			if (string_1.Length > 0)
			{
				base.Height = 100;
			}
			this.Refresh();
		}

		// Token: 0x06002833 RID: 10291 RVA: 0x0010DA08 File Offset: 0x0010BC08
		protected void OnResize(EventArgs e)
		{
			this.label_0.SetBounds(Convert.ToInt32(22f * this.float_0), Convert.ToInt32(this.float_1), base.Width - Convert.ToInt32(22f * this.float_0), base.Height - Convert.ToInt32(this.float_1));
			base.OnResize(e);
		}

		// Token: 0x06002834 RID: 10292 RVA: 0x0000FA1B File Offset: 0x0000DC1B
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002835 RID: 10293 RVA: 0x0010DA70 File Offset: 0x0010BC70
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			if (base.DesignMode)
			{
				this.image_0 = Class541.smethod_0(Class521.smethod_0(119534));
				this.bool_0 = true;
			}
			if (this.image_0 != null && this.bool_1)
			{
				e.Graphics.DrawImage(this.image_0, new Rectangle(0, 0, Convert.ToInt32(16f * this.float_0), Convert.ToInt32(16f * this.float_1)), new Rectangle(0, 0, 16, 16), GraphicsUnit.Pixel);
			}
			if (this.bool_0)
			{
				this.label_0.Text = ((this.string_0.Length > 0) ? (base.Text + Class521.smethod_0(5041) + this.string_0 + Class521.smethod_0(5046)) : base.Text);
				return;
			}
			this.label_0.Text = string.Empty;
		}

		// Token: 0x06002836 RID: 10294 RVA: 0x0010DB60 File Offset: 0x0010BD60
		public Control14()
		{
			this.timer_0.Interval = 250;
			this.timer_0.Tick += this.timer_0_Tick;
			this.label_0.FlatStyle = FlatStyle.System;
			base.Controls.Add(this.label_0);
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
			base.TabStop = false;
		}

		// Token: 0x06002837 RID: 10295 RVA: 0x0000FA3E File Offset: 0x0000DC3E
		public Control14(string string_1) : this()
		{
			base.Text = Class521.smethod_0(3636) + string_1;
		}

		// Token: 0x06002838 RID: 10296 RVA: 0x0000FA5C File Offset: 0x0000DC5C
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.image_0 != null)
				{
					this.image_0.Dispose();
				}
				this.timer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002839 RID: 10297 RVA: 0x0000FA86 File Offset: 0x0000DC86
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.bool_1 = !this.bool_1;
			this.Refresh();
		}

		// Token: 0x04001405 RID: 5125
		private readonly Label label_0 = new Label();

		// Token: 0x04001406 RID: 5126
		private Image image_0;

		// Token: 0x04001407 RID: 5127
		private bool bool_0;

		// Token: 0x04001408 RID: 5128
		private readonly Timer timer_0 = new Timer();

		// Token: 0x04001409 RID: 5129
		private bool bool_1 = true;

		// Token: 0x0400140A RID: 5130
		private string string_0 = string.Empty;

		// Token: 0x0400140B RID: 5131
		private float float_0 = 1f;

		// Token: 0x0400140C RID: 5132
		private float float_1 = 1f;
	}
}
