﻿using System;
using System.Windows.Forms;
using ns26;
using ns5;
using TEx;

namespace ns14
{
	// Token: 0x0200029B RID: 667
	internal sealed class Class56 : Class53
	{
		// Token: 0x06001D96 RID: 7574 RVA: 0x0000C63B File Offset: 0x0000A83B
		public Class56(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class375.zoomin);
		}

		// Token: 0x06001D97 RID: 7575 RVA: 0x0000C657 File Offset: 0x0000A857
		public Class56(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D98 RID: 7576 RVA: 0x0000C665 File Offset: 0x0000A865
		protected override void Class50_MouseEnter(object sender, EventArgs e)
		{
			base.Class50_MouseEnter(sender, e);
			base.method_0(Class375.zoomin_red);
		}

		// Token: 0x06001D99 RID: 7577 RVA: 0x0000C67C File Offset: 0x0000A87C
		protected override void Class50_MouseLeave(object sender, EventArgs e)
		{
			base.Class50_MouseLeave(sender, e);
			base.method_0(Class375.zoomin);
		}

		// Token: 0x06001D9A RID: 7578 RVA: 0x0000C693 File Offset: 0x0000A893
		protected override void Class50_Click(object sender, EventArgs e)
		{
			base.Class50_Click(sender, e);
			((ChtCtrl_KLine)base.ChtCtrl).method_117(false);
		}
	}
}
