﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace ns12
{
	// Token: 0x0200025D RID: 605
	internal sealed class Class327 : ToolStripMenuItem
	{
		// Token: 0x06001A97 RID: 6807 RVA: 0x0000B1FD File Offset: 0x000093FD
		public Class327(Color color_1)
		{
			this.color_0 = color_1;
		}

		// Token: 0x06001A98 RID: 6808 RVA: 0x000BC0A4 File Offset: 0x000BA2A4
		protected void OnPaint(PaintEventArgs e)
		{
			Graphics graphics = e.Graphics;
			int num = base.Width - 35;
			int num2 = base.Height / 2 + 1;
			using (Pen pen = new Pen(this.color_0, 6f))
			{
				graphics.DrawLine(pen, num, num2, num + 25, num2);
			}
			base.OnPaint(e);
		}

		// Token: 0x04000D6A RID: 3434
		private Color color_0;
	}
}
