﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;
using ns18;

namespace ns5
{
	// Token: 0x0200041D RID: 1053
	[CompilerGenerated]
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
	[DebuggerNonUserCode]
	internal sealed class Class552
	{
		// Token: 0x0600287C RID: 10364 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class552()
		{
		}

		// Token: 0x170006E9 RID: 1769
		// (get) Token: 0x0600287D RID: 10365 RVA: 0x0000FE4E File Offset: 0x0000E04E
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class552.resourceManager_0 == null)
				{
					Class552.resourceManager_0 = new ResourceManager(Class521.smethod_0(123015), typeof(Class552).Assembly);
				}
				return Class552.resourceManager_0;
			}
		}

		// Token: 0x170006EA RID: 1770
		// (get) Token: 0x0600287E RID: 10366 RVA: 0x0000FE7F File Offset: 0x0000E07F
		// (set) Token: 0x0600287F RID: 10367 RVA: 0x0000FE86 File Offset: 0x0000E086
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class552.cultureInfo_0;
			}
			set
			{
				Class552.cultureInfo_0 = value;
			}
		}

		// Token: 0x170006EB RID: 1771
		// (get) Token: 0x06002880 RID: 10368 RVA: 0x0000FE8E File Offset: 0x0000E08E
		internal static Bitmap TExLogo
		{
			get
			{
				return (Bitmap)Class552.ResourceManager.GetObject(Class521.smethod_0(123020), Class552.cultureInfo_0);
			}
		}

		// Token: 0x0400144B RID: 5195
		private static ResourceManager resourceManager_0;

		// Token: 0x0400144C RID: 5196
		private static CultureInfo cultureInfo_0;
	}
}
