﻿using System;
using System.Drawing;
using ns18;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns5
{
	// Token: 0x020002F0 RID: 752
	internal sealed class Class392 : ShapeCurve
	{
		// Token: 0x06002119 RID: 8473 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class392(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x0600211A RID: 8474 RVA: 0x000EB13C File Offset: 0x000E933C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_ColorDOT ind_ColorDOT;
			if (string_0 == Class521.smethod_0(98298))
			{
				ind_ColorDOT = zedGraphControl_0.GraphPane.AddColorDotItem(base.IndData.Name, base.DataView, SymbolType.Circle, Color.Red, Color.Blue);
			}
			else
			{
				ind_ColorDOT = zedGraphControl_0.GraphPane.AddColorDotItem(base.IndData.Name, base.DataView, SymbolType.Circle, color_0, color_0);
			}
			ind_ColorDOT.Line.IsVisible = false;
			this.curveItem_0 = ind_ColorDOT;
			ind_ColorDOT.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			base.method_3(string_0, ind_ColorDOT);
		}

		// Token: 0x0600211B RID: 8475 RVA: 0x000EB204 File Offset: 0x000E9404
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			double x = new XDate(base.method_0(int_0));
			if (int_0 > dataArray_1.Data.Length)
			{
				int_0 = dataArray_1.Data.Length - 1;
			}
			if (int_0 < 0)
			{
				int_0 = 0;
			}
			PointPair result;
			if (this.indEx_0.EnName == Class521.smethod_0(98298))
			{
				if (dataArray_1.OtherDataArrayList.Count != 1)
				{
					throw new Exception(Class521.smethod_0(97787));
				}
				double y = dataArray_1.Data[int_0];
				result = new PointPair(x, y, dataArray_1.OtherDataArrayList[0].Data[int_0]);
			}
			else
			{
				double y2 = dataArray_1.Data[int_0];
				result = new PointPair(x, y2, 1.0);
			}
			return result;
		}
	}
}
