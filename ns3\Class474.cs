﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx.ImportTrans;

namespace ns3
{
	// Token: 0x0200036C RID: 876
	internal class Class474
	{
		// Token: 0x0600249A RID: 9370 RVA: 0x000FE410 File Offset: 0x000FC610
		protected bool method_0(IStoreElement istoreElement_0, List<IStoreElement> list_0)
		{
			Class474.Class475 @class = new Class474.Class475();
			@class.istoreElement_0 = istoreElement_0;
			return list_0.Any(new Func<IStoreElement, bool>(@class.method_0));
		}

		// Token: 0x0600249B RID: 9371 RVA: 0x0000E443 File Offset: 0x0000C643
		public void method_1(string string_1)
		{
			this.string_0 = string_1;
		}

		// Token: 0x0600249C RID: 9372 RVA: 0x000FE440 File Offset: 0x000FC640
		protected virtual List<IStoreElement> vmethod_0()
		{
			return null;
		}

		// Token: 0x0600249D RID: 9373 RVA: 0x000FE454 File Offset: 0x000FC654
		public List<IStoreElement> imethod_3()
		{
			return this.vmethod_0();
		}

		// Token: 0x040011AC RID: 4524
		protected string string_0;

		// Token: 0x0200036D RID: 877
		[CompilerGenerated]
		private sealed class Class475
		{
			// Token: 0x060024A0 RID: 9376 RVA: 0x000FE46C File Offset: 0x000FC66C
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011AD RID: 4525
			public IStoreElement istoreElement_0;
		}
	}
}
