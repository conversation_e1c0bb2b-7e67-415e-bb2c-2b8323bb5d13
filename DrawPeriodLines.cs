﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000079 RID: 121
	[Serializable]
	internal sealed class DrawPeriodLines : DrawObj, ISerializable
	{
		// Token: 0x06000453 RID: 1107 RVA: 0x00003742 File Offset: 0x00001942
		public DrawPeriodLines()
		{
		}

		// Token: 0x06000454 RID: 1108 RVA: 0x00003DEC File Offset: 0x00001FEC
		public DrawPeriodLines(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5311);
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000455 RID: 1109 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawPeriodLines(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000456 RID: 1110 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000457 RID: 1111 RVA: 0x00023B5C File Offset: 0x00021D5C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_1, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			int num = Convert.ToInt32(Math.Round(double_3 - double_1));
			int num2 = Math.Abs(num);
			if (num2 > 0)
			{
				double min = chartCS_1.GraphPane.YAxis.Scale.Min;
				TextObj textObj = base.method_27(chartCS_1, double_1, min, num2.ToString(), null, string_5);
				textObj.Location.AlignV = AlignV.Bottom;
				list.Add(textObj);
				if (num < 0)
				{
					textObj.Location.AlignH = AlignH.Right;
				}
				else
				{
					textObj.Location.AlignH = AlignH.Left;
				}
				double num3 = double_1 + (double)num;
				while (num3 > 0.0 && num3 < max)
				{
					LineObj item2 = base.method_22(chartCS_1, num3, string_5);
					list.Add(item2);
					num3 += (double)num;
				}
			}
			return list;
		}
	}
}
