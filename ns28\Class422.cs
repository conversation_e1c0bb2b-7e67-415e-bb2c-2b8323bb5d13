﻿using System;
using System.Collections.Generic;
using ns11;
using ns12;
using ns18;
using ns26;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns28
{
	// Token: 0x0200032E RID: 814
	internal sealed class Class422 : Class415
	{
		// Token: 0x06002272 RID: 8818 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class422(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x06002273 RID: 8819 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x06002274 RID: 8820 RVA: 0x000F36DC File Offset: 0x000F18DC
		public static Class411 smethod_0(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			Class411 class411_ = TreeSentence.smethod_0(tokenes_0, parserEnvironment_0);
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30 && !(tokenes_0.Current.Symbol.Name != Class521.smethod_0(51510)))
			{
				HToken htoken_ = tokenes_0.Current;
				tokenes_0.method_1();
				Class411 result;
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
				{
					result = new Class422(htoken_, class411_, new Class412(new HToken(0, 0, new Class442(Enum26.const_30, Class521.smethod_0(103003)))));
				}
				else
				{
					Class411 class411_2 = Class422.smethod_1(tokenes_0, parserEnvironment_0);
					result = new Class422(htoken_, class411_, class411_2);
				}
				return result;
			}
			throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(102986)));
		}

		// Token: 0x06002275 RID: 8821 RVA: 0x000F37A8 File Offset: 0x000F19A8
		private static Class411 smethod_1(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			Class411 left = TreeSentence.smethod_0(tokenes_0, parserEnvironment_0);
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30 && !(tokenes_0.Current.Symbol.Name != Class521.smethod_0(51510)))
			{
				HToken token = tokenes_0.Current;
				tokenes_0.method_1();
				Class411 result;
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
				{
					result = new TreeSentence(token, left, new Class412(new HToken(0, 0, new Class442(Enum26.const_30, Class521.smethod_0(103003)))));
				}
				else
				{
					Class411 right = Class422.smethod_1(tokenes_0, parserEnvironment_0);
					result = new TreeSentence(token, left, right);
				}
				return result;
			}
			throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(102986)));
		}

		// Token: 0x06002276 RID: 8822 RVA: 0x000F3874 File Offset: 0x000F1A74
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_22)
			{
				throw new Exception(this.Token.method_0(Class521.smethod_0(51510)));
			}
			List<DataArray> list = new List<DataArray>();
			parserEnvironment_0.DataArrays = list;
			parserEnvironment_0.list_0 = new List<NameDoubleValue>();
			Class411 left = this.Left;
			Class411 right = this.Right;
			for (;;)
			{
				if (left.Token.Symbol.HSymbolType != Enum26.const_30)
				{
					object obj = left.vmethod_1(parserEnvironment_0);
					if (obj.GetType() == typeof(DataArray))
					{
						list.Add(obj as DataArray);
					}
					else if (obj.GetType() == typeof(NameDoubleValue))
					{
						parserEnvironment_0.list_0.Add(obj as NameDoubleValue);
					}
				}
				if (right.Token.Symbol.HSymbolType == Enum26.const_30)
				{
					break;
				}
				left = right.Left;
				right = right.Right;
			}
			return list;
		}
	}
}
