﻿using System;
using System.Drawing;
using System.Windows.Forms;
using ns10;
using ns18;
using ns21;
using ns26;
using ns9;
using TEx;

namespace ns7
{
	// Token: 0x0200029D RID: 669
	internal sealed class Class58 : Class50
	{
		// Token: 0x06001DA0 RID: 7584 RVA: 0x000D0264 File Offset: 0x000CE464
		public Class58(SplitterPanel splitterPanel_0, int int_4, int int_5, bool bool_6, bool bool_7) : base(splitterPanel_0, int_4, int_5, bool_7)
		{
			this.bool_3 = bool_6;
			ChtCtrl chtCtrl = (ChtCtrl)splitterPanel_0.Parent.Parent.Parent;
			if (Base.UI.Form.IsSwitchingChart)
			{
				chtCtrl = Base.UI.SelectedChtCtrl;
			}
			if (chtCtrl != null && chtCtrl.Parent != null && chtCtrl.Parent.Parent != null && chtCtrl.Parent.Parent is SplitContainer)
			{
				SplitContainer splitContainer = (SplitContainer)chtCtrl.Parent.Parent;
				SplitterPanel splitterPanel = (SplitterPanel)chtCtrl.Parent;
				if ((splitterPanel == splitContainer.Panel1 && splitContainer.Panel2Collapsed) || (splitterPanel == splitContainer.Panel2 && splitContainer.Panel1Collapsed))
				{
					this.bool_4 = true;
					base.method_0(Class375.window_restore_blue);
				}
				else
				{
					base.method_0(Class375.window_maximize_blue);
				}
			}
			else
			{
				this.bool_5 = true;
			}
		}

		// Token: 0x06001DA1 RID: 7585 RVA: 0x0000C725 File Offset: 0x0000A925
		public Class58(SplitterPanel splitterPanel_0, int int_4, int int_5, bool bool_6) : this(splitterPanel_0, int_4, int_5, bool_6, true)
		{
		}

		// Token: 0x170004AC RID: 1196
		// (get) Token: 0x06001DA2 RID: 7586 RVA: 0x000D0344 File Offset: 0x000CE544
		public bool IsMaximized
		{
			get
			{
				return this.bool_4;
			}
		}

		// Token: 0x170004AD RID: 1197
		// (get) Token: 0x06001DA3 RID: 7587 RVA: 0x000D035C File Offset: 0x000CE55C
		public bool IsInSingleChartPanel
		{
			get
			{
				return this.bool_5;
			}
		}

		// Token: 0x06001DA4 RID: 7588 RVA: 0x000D0374 File Offset: 0x000CE574
		protected override void Class50_MouseEnter(object sender, EventArgs e)
		{
			base.Class50_MouseEnter(sender, e);
			if (!this.bool_5)
			{
				if (!this.bool_4)
				{
					base.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(85590));
				}
				else
				{
					base.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(85619));
				}
			}
		}

		// Token: 0x06001DA5 RID: 7589 RVA: 0x0000C733 File Offset: 0x0000A933
		protected override void Class50_MouseLeave(object sender, EventArgs e)
		{
			base.Class50_MouseLeave(sender, e);
			if (!this.bool_5)
			{
				if (!this.bool_4)
				{
					base.Image = Class375.window_maximize_blue;
				}
				else
				{
					base.Image = Class375.window_restore_blue;
				}
			}
		}

		// Token: 0x06001DA6 RID: 7590 RVA: 0x000D03DC File Offset: 0x000CE5DC
		protected override void Class50_Click(object sender, EventArgs e)
		{
			if (!this.bool_5)
			{
				SplitterPanel splitterPanel = (SplitterPanel)base.Parent;
				if (this.bool_3)
				{
					this.method_1(splitterPanel);
				}
				SplitContainer splitContainer = (SplitContainer)splitterPanel.Parent;
				while (splitContainer.Parent.GetType() == typeof(SplitterPanel))
				{
					splitterPanel = (SplitterPanel)splitContainer.Parent;
					this.method_1(splitterPanel);
					splitContainer = (SplitContainer)splitterPanel.Parent;
				}
				this.bool_4 = !this.bool_4;
				this.Class50_MouseLeave(this, new EventArgs());
			}
		}

		// Token: 0x06001DA7 RID: 7591 RVA: 0x000D0470 File Offset: 0x000CE670
		private void method_1(SplitterPanel splitterPanel_0)
		{
			SplitContainer splitContainer = (SplitContainer)splitterPanel_0.Parent;
			Base.UI.MainForm.SuspendLayout();
			Base.UI.MainForm.smethod_0();
			if (splitterPanel_0 == splitContainer.Panel1)
			{
				if (this.bool_4)
				{
					if (splitContainer.Tag == null || (splitContainer.Tag != null && splitContainer.Tag.ToString() != Class521.smethod_0(2606)))
					{
						if (splitContainer is ChtCtrl)
						{
							if (((ChtCtrl)splitContainer).IfShowTickPanel)
							{
								splitContainer.Panel2Collapsed = !this.bool_4;
							}
						}
						else
						{
							splitContainer.Panel2Collapsed = !this.bool_4;
						}
					}
				}
				else
				{
					splitContainer.Panel2Collapsed = !this.bool_4;
				}
			}
			else
			{
				splitContainer.Panel1Collapsed = !this.bool_4;
			}
			Base.UI.MainForm.ResumeLayout();
			Base.UI.MainForm.smethod_1();
		}

		// Token: 0x04000E8F RID: 3727
		private bool bool_3;

		// Token: 0x04000E90 RID: 3728
		private bool bool_4;

		// Token: 0x04000E91 RID: 3729
		private bool bool_5;
	}
}
