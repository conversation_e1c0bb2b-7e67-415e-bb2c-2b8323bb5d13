﻿using System;
using System.Xml;

namespace ns22
{
	// Token: 0x0200040F RID: 1039
	internal sealed class Class548 : IDisposable
	{
		// Token: 0x06002826 RID: 10278 RVA: 0x0000F927 File Offset: 0x0000DB27
		public Class548(XmlWriter xmlWriter_1, string string_0)
		{
			this.xmlWriter_0 = xmlWriter_1;
			this.xmlWriter_0.WriteStartElement(string_0);
		}

		// Token: 0x06002827 RID: 10279 RVA: 0x0000F942 File Offset: 0x0000DB42
		public void Dispose()
		{
			this.xmlWriter_0.WriteEndElement();
		}

		// Token: 0x04001404 RID: 5124
		private readonly XmlWriter xmlWriter_0;
	}
}
