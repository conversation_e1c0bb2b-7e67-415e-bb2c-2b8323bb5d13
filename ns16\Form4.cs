﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns1;
using ns18;
using ns21;
using TEx;
using TEx.Comn;

namespace ns16
{
	// Token: 0x02000094 RID: 148
	internal sealed partial class Form4 : Form
	{
		// Token: 0x1400001F RID: 31
		// (add) Token: 0x060004E3 RID: 1251 RVA: 0x00027138 File Offset: 0x00025338
		// (remove) Token: 0x060004E4 RID: 1252 RVA: 0x00027170 File Offset: 0x00025370
		public event MsgEventHandler CondGroupSaved
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004E5 RID: 1253 RVA: 0x000042A9 File Offset: 0x000024A9
		protected void method_0(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x060004E6 RID: 1254 RVA: 0x000271A8 File Offset: 0x000253A8
		public Form4()
		{
			this.method_1();
			this.FilterCondsUserData = Class302.smethod_1();
			base.StartPosition = FormStartPosition.CenterScreen;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
		}

		// Token: 0x060004E7 RID: 1255 RVA: 0x00027204 File Offset: 0x00025404
		public Form4(List<FilterCond> list_1 = null, string string_1 = null) : this()
		{
			this.FilterCondsToBeSaved = list_1;
			this.CurrUsrCfgCondGrpName = string_1;
			string text = Class521.smethod_0(7513);
			if (!string.IsNullOrEmpty(string_1))
			{
				text = string_1;
			}
			this.textBox_0.Text = text;
		}

		// Token: 0x060004E8 RID: 1256 RVA: 0x00027248 File Offset: 0x00025448
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.FilterCondsToBeSaved != null)
			{
				Form4.Class59 @class = new Form4.Class59();
				@class.string_0 = this.textBox_0.Text.Trim();
				if (!string.IsNullOrEmpty(@class.string_0))
				{
					bool flag = false;
					List<Class303> list;
					if (this.FilterCondsUserData != null)
					{
						flag = this.FilterCondsUserData.CondGroups.Exists(new Predicate<Class303>(@class.method_0));
						list = this.FilterCondsUserData.CondGroups;
					}
					else
					{
						list = new List<Class303>();
						this.FilterCondsUserData = new Class302();
					}
					if (flag)
					{
						if (MessageBox.Show(Class521.smethod_0(7530), Class521.smethod_0(7587), MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						list.RemoveAll(new Predicate<Class303>(@class.method_1));
					}
					list.Add(new Class303
					{
						Name = @class.string_0,
						FilterConds = this.FilterCondsToBeSaved
					});
					Class302 filterCondsUserData = this.FilterCondsUserData;
					filterCondsUserData.CurrGrpName = @class.string_0;
					filterCondsUserData.CondGroups = list;
					if (filterCondsUserData.method_1())
					{
						this.method_0(@class.string_0);
					}
					base.Close();
				}
				else
				{
					this.textBox_0.Focus();
				}
			}
		}

		// Token: 0x060004E9 RID: 1257 RVA: 0x00004273 File Offset: 0x00002473
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000101 RID: 257
		// (get) Token: 0x060004EA RID: 1258 RVA: 0x00027370 File Offset: 0x00025570
		// (set) Token: 0x060004EB RID: 1259 RVA: 0x000042C6 File Offset: 0x000024C6
		public List<FilterCond> FilterCondsToBeSaved { get; set; }

		// Token: 0x17000102 RID: 258
		// (get) Token: 0x060004EC RID: 1260 RVA: 0x00027388 File Offset: 0x00025588
		// (set) Token: 0x060004ED RID: 1261 RVA: 0x000042D1 File Offset: 0x000024D1
		public Class302 FilterCondsUserData { get; set; }

		// Token: 0x17000103 RID: 259
		// (get) Token: 0x060004EE RID: 1262 RVA: 0x000273A0 File Offset: 0x000255A0
		// (set) Token: 0x060004EF RID: 1263 RVA: 0x000042DC File Offset: 0x000024DC
		public string CurrUsrCfgCondGrpName { get; set; }

		// Token: 0x060004F0 RID: 1264 RVA: 0x000042E7 File Offset: 0x000024E7
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060004F1 RID: 1265 RVA: 0x000273B8 File Offset: 0x000255B8
		private void method_1()
		{
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.textBox_0 = new TextBox();
			this.label_0 = new Label();
			base.SuspendLayout();
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(288, 157);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(104, 32);
			this.button_0.TabIndex = 10;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(166, 157);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(104, 32);
			this.button_1.TabIndex = 9;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.textBox_0.Location = new Point(137, 61);
			this.textBox_0.Name = Class521.smethod_0(7596);
			this.textBox_0.Size = new Size(238, 25);
			this.textBox_0.TabIndex = 7;
			this.label_0.Location = new Point(41, 64);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(90, 15);
			this.label_0.TabIndex = 6;
			this.label_0.Text = Class521.smethod_0(7609);
			this.label_0.TextAlign = ContentAlignment.MiddleRight;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(427, 196);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.Name = Class521.smethod_0(7634);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(7663);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040001FC RID: 508
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001FD RID: 509
		[CompilerGenerated]
		private List<FilterCond> list_0;

		// Token: 0x040001FE RID: 510
		[CompilerGenerated]
		private Class302 class302_0;

		// Token: 0x040001FF RID: 511
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000200 RID: 512
		private IContainer icontainer_0;

		// Token: 0x04000201 RID: 513
		private Button button_0;

		// Token: 0x04000202 RID: 514
		private Button button_1;

		// Token: 0x04000203 RID: 515
		private TextBox textBox_0;

		// Token: 0x04000204 RID: 516
		private Label label_0;

		// Token: 0x02000095 RID: 149
		[CompilerGenerated]
		private sealed class Class59
		{
			// Token: 0x060004F3 RID: 1267 RVA: 0x00027678 File Offset: 0x00025878
			internal bool method_0(Class303 class303_0)
			{
				return class303_0.Name.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x060004F4 RID: 1268 RVA: 0x00027678 File Offset: 0x00025878
			internal bool method_1(Class303 class303_0)
			{
				return class303_0.Name.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x04000205 RID: 517
			public string string_0;
		}
	}
}
