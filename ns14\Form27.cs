﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using ns13;
using ns18;
using ns21;
using ns22;
using ns26;
using ns28;
using ns29;
using ns4;
using ns5;
using ns8;

namespace ns14
{
	// Token: 0x02000419 RID: 1049
	internal sealed partial class Form27 : Form
	{
		// Token: 0x06002854 RID: 10324 RVA: 0x0010E3C4 File Offset: 0x0010C5C4
		public Form27(Class544 class544_1, EventArgs35 eventArgs35_1) : this()
		{
			int num = base.Height;
			this.eventArgs35_0 = eventArgs35_1;
			this.class544_0 = class544_1;
			this.class549_0.Text = eventArgs35_1.Exception.Message;
			num += this.class549_0.Height - base.FontHeight;
			if (!eventArgs35_1.CanContinue)
			{
				this.checkBox_0.Visible = false;
				num -= this.checkBox_0.Height;
			}
			if (num > base.Height)
			{
				base.Height = num;
			}
			if (eventArgs35_1.CanDebug)
			{
				class544_1.DebuggerLaunched += this.method_5;
			}
			if (!eventArgs35_1.CanSendReport)
			{
				this.button_1.Enabled = false;
				if (this.button_0.CanFocus)
				{
					this.button_0.Focus();
				}
			}
			this.textBox_0.Text = Class551.smethod_0(Class521.smethod_0(119889));
			class544_1.SendingReportFeedback += this.method_4;
		}

		// Token: 0x06002855 RID: 10325 RVA: 0x0010E4BC File Offset: 0x0010C6BC
		public Form27()
		{
			this.method_0();
			base.Size = new Size(419, 264);
			base.MinimizeBox = false;
			base.MaximizeBox = false;
			this.panel_0.Location = Point.Empty;
			this.panel_0.Dock = DockStyle.Fill;
			this.button_4.Location = this.button_3.Location;
			this.button_4.Size = this.button_3.Size;
			this.button_4.BringToFront();
			this.panel_1.Location = Point.Empty;
			this.panel_1.Dock = DockStyle.Fill;
			this.Text = this.method_1(this.Text);
			this.panel_2.Location = Point.Empty;
			this.panel_2.Dock = DockStyle.Fill;
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				control.Text = this.method_1(control.Text);
				foreach (object obj2 in control.Controls)
				{
					Control control2 = (Control)obj2;
					control2.Text = this.method_1(control2.Text);
				}
			}
		}

		// Token: 0x06002856 RID: 10326 RVA: 0x0000FC7F File Offset: 0x0000DE7F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002857 RID: 10327 RVA: 0x0010E648 File Offset: 0x0010C848
		private void method_0()
		{
			this.panel_0 = new Panel();
			this.checkBox_0 = new CheckBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.label_0 = new Label();
			this.control15_0 = new Control15();
			this.class549_0 = new Class549();
			this.panel_1 = new Panel();
			this.button_2 = new Button();
			this.button_3 = new Button();
			this.button_4 = new Button();
			this.control17_0 = new Control17();
			this.control15_1 = new Control15();
			this.control14_0 = new Control14();
			this.control14_1 = new Control14();
			this.control14_2 = new Control14();
			this.control14_3 = new Control14();
			this.panel_2 = new Panel();
			this.label_2 = new Label();
			this.checkBox_1 = new CheckBox();
			this.textBox_0 = new TextBox();
			this.control15_2 = new Control15();
			this.label_1 = new Label();
			this.button_5 = new Button();
			this.panel_0.SuspendLayout();
			this.panel_1.SuspendLayout();
			this.panel_2.SuspendLayout();
			base.SuspendLayout();
			this.panel_0.Controls.Add(this.checkBox_0);
			this.panel_0.Controls.Add(this.button_0);
			this.panel_0.Controls.Add(this.button_1);
			this.panel_0.Controls.Add(this.label_0);
			this.panel_0.Controls.Add(this.control15_0);
			this.panel_0.Controls.Add(this.class549_0);
			this.panel_0.Location = new Point(11, 9);
			this.panel_0.Name = Class521.smethod_0(119898);
			this.panel_0.Size = new Size(578, 267);
			this.panel_0.TabIndex = 0;
			this.checkBox_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Left);
			this.checkBox_0.FlatStyle = FlatStyle.System;
			this.checkBox_0.Location = new Point(31, 134);
			this.checkBox_0.Name = Class521.smethod_0(119923);
			this.checkBox_0.Size = new Size(316, 30);
			this.checkBox_0.TabIndex = 14;
			this.checkBox_0.Text = Class521.smethod_0(119948);
			this.checkBox_0.UseMnemonic = false;
			this.checkBox_0.CheckedChanged += this.checkBox_0_CheckedChanged;
			this.button_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_0.FlatStyle = FlatStyle.System;
			this.button_0.Location = new Point(455, 227);
			this.button_0.Name = Class521.smethod_0(119985);
			this.button_0.Size = new Size(105, 27);
			this.button_0.TabIndex = 6;
			this.button_0.Text = Class521.smethod_0(120006);
			this.button_0.Click += this.button_0_Click;
			this.button_1.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_1.FlatStyle = FlatStyle.System;
			this.button_1.Location = new Point(300, 227);
			this.button_1.Name = Class521.smethod_0(120019);
			this.button_1.Size = new Size(147, 27);
			this.button_1.TabIndex = 9;
			this.button_1.Text = Class521.smethod_0(120036);
			this.button_1.Click += this.button_1_Click;
			this.label_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
			this.label_0.FlatStyle = FlatStyle.System;
			this.label_0.Location = new Point(28, 169);
			this.label_0.Name = Class521.smethod_0(120061);
			this.label_0.Size = new Size(533, 51);
			this.label_0.TabIndex = 12;
			this.label_0.Text = Class521.smethod_0(120086);
			this.control15_0.BackColor = Color.FromArgb(36, 96, 179);
			this.control15_0.Dock = DockStyle.Top;
			this.control15_0.ForeColor = Color.White;
			this.control15_0.IconState = Enum36.const_1;
			this.control15_0.Image = Class552.TExLogo;
			this.control15_0.IconState = Enum36.const_1;
			this.control15_0.Location = new Point(0, 0);
			this.control15_0.Name = Class521.smethod_0(120304);
			this.control15_0.Size = new Size(578, 67);
			this.control15_0.TabIndex = 3;
			this.control15_0.TabStop = false;
			this.control15_0.Text = Class521.smethod_0(120325);
			this.class549_0.Anchor = (AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
			this.class549_0.FlatStyle = FlatStyle.System;
			this.class549_0.Location = new Point(28, 80);
			this.class549_0.Name = Class521.smethod_0(120378);
			this.class549_0.Size = new Size(533, 13);
			this.class549_0.TabIndex = 10;
			this.class549_0.Text = Class521.smethod_0(120378);
			this.class549_0.UseMnemonic = false;
			this.panel_1.Controls.Add(this.button_2);
			this.panel_1.Controls.Add(this.button_3);
			this.panel_1.Controls.Add(this.button_4);
			this.panel_1.Controls.Add(this.control17_0);
			this.panel_1.Controls.Add(this.control15_1);
			this.panel_1.Controls.Add(this.control14_0);
			this.panel_1.Controls.Add(this.control14_1);
			this.panel_1.Controls.Add(this.control14_2);
			this.panel_1.Controls.Add(this.control14_3);
			this.panel_1.Location = new Point(11, 305);
			this.panel_1.Name = Class521.smethod_0(120395);
			this.panel_1.Size = new Size(578, 267);
			this.panel_1.TabIndex = 2;
			this.panel_1.Visible = false;
			this.button_2.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_2.FlatStyle = FlatStyle.System;
			this.button_2.Location = new Point(448, 227);
			this.button_2.Name = Class521.smethod_0(120412);
			this.button_2.Size = new Size(112, 28);
			this.button_2.TabIndex = 10;
			this.button_2.Text = Class521.smethod_0(5783);
			this.button_2.Click += this.button_2_Click;
			this.button_3.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_3.Enabled = false;
			this.button_3.FlatStyle = FlatStyle.System;
			this.button_3.Location = new Point(325, 227);
			this.button_3.Name = Class521.smethod_0(9965);
			this.button_3.Size = new Size(112, 28);
			this.button_3.TabIndex = 22;
			this.button_3.Text = Class521.smethod_0(5801);
			this.button_3.Click += this.button_3_Click;
			this.button_4.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_4.FlatStyle = FlatStyle.System;
			this.button_4.Location = new Point(202, 227);
			this.button_4.Name = Class521.smethod_0(120433);
			this.button_4.Size = new Size(112, 28);
			this.button_4.TabIndex = 23;
			this.button_4.Text = Class521.smethod_0(120450);
			this.button_4.Visible = false;
			this.button_4.Click += this.button_4_Click;
			this.control17_0.Location = new Point(147, 138);
			this.control17_0.Name = Class521.smethod_0(120459);
			this.control17_0.Size = new Size(250, 42);
			this.control17_0.TabIndex = 11;
			this.control17_0.TabStop = false;
			this.control17_0.Visible = false;
			this.control15_1.BackColor = Color.FromArgb(36, 96, 179);
			this.control15_1.Dock = DockStyle.Top;
			this.control15_1.ForeColor = Color.White;
			this.control15_1.IconState = Enum36.const_1;
			this.control15_1.Image = Class552.TExLogo;
			this.control15_1.IconState = Enum36.const_1;
			this.control15_1.Location = new Point(0, 0);
			this.control15_1.Name = Class521.smethod_0(120484);
			this.control15_1.Size = new Size(578, 67);
			this.control15_1.TabIndex = 24;
			this.control15_1.TabStop = false;
			this.control15_1.Text = Class521.smethod_0(120505);
			this.control14_0.Location = new Point(34, 83);
			this.control14_0.Name = Class521.smethod_0(120566);
			this.control14_0.Size = new Size(515, 19);
			this.control14_0.TabIndex = 18;
			this.control14_0.TabStop = false;
			this.control14_0.Text = Class521.smethod_0(120591);
			this.control14_1.Location = new Point(34, 111);
			this.control14_1.Name = Class521.smethod_0(120616);
			this.control14_1.Size = new Size(515, 18);
			this.control14_1.TabIndex = 19;
			this.control14_1.TabStop = false;
			this.control14_1.Text = Class521.smethod_0(120641);
			this.control14_2.Location = new Point(34, 138);
			this.control14_2.Name = Class521.smethod_0(120674);
			this.control14_2.Size = new Size(515, 19);
			this.control14_2.TabIndex = 20;
			this.control14_2.TabStop = false;
			this.control14_2.Text = Class521.smethod_0(120703);
			this.control14_3.Location = new Point(34, 166);
			this.control14_3.Name = Class521.smethod_0(120720);
			this.control14_3.Size = new Size(515, 19);
			this.control14_3.TabIndex = 21;
			this.control14_3.TabStop = false;
			this.control14_3.Text = Class521.smethod_0(120745);
			this.panel_2.Controls.Add(this.label_2);
			this.panel_2.Controls.Add(this.checkBox_1);
			this.panel_2.Controls.Add(this.textBox_0);
			this.panel_2.Controls.Add(this.control15_2);
			this.panel_2.Controls.Add(this.label_1);
			this.panel_2.Controls.Add(this.button_5);
			this.panel_2.Location = new Point(15, 591);
			this.panel_2.Name = Class521.smethod_0(120754);
			this.panel_2.Size = new Size(579, 267);
			this.panel_2.TabIndex = 4;
			this.panel_2.Visible = false;
			this.label_2.FlatStyle = FlatStyle.System;
			this.label_2.Location = new Point(28, 130);
			this.label_2.Name = Class521.smethod_0(120771);
			this.label_2.Size = new Size(140, 19);
			this.label_2.TabIndex = 9;
			this.label_2.Text = Class521.smethod_0(120788);
			this.checkBox_1.FlatStyle = FlatStyle.System;
			this.checkBox_1.Location = new Point(168, 164);
			this.checkBox_1.Name = Class521.smethod_0(120809);
			this.checkBox_1.Size = new Size(325, 18);
			this.checkBox_1.TabIndex = 11;
			this.checkBox_1.Text = Class521.smethod_0(120830);
			this.checkBox_1.CheckedChanged += this.checkBox_1_CheckedChanged;
			this.textBox_0.Location = new Point(168, 127);
			this.textBox_0.Name = Class521.smethod_0(120859);
			this.textBox_0.Size = new Size(204, 22);
			this.textBox_0.TabIndex = 10;
			this.textBox_0.TextChanged += this.textBox_0_TextChanged;
			this.control15_2.BackColor = Color.FromArgb(36, 96, 179);
			this.control15_2.Dock = DockStyle.Top;
			this.control15_2.ForeColor = Color.White;
			this.control15_2.IconState = Enum36.const_1;
			this.control15_2.Image = Class552.TExLogo;
			this.control15_2.IconState = Enum36.const_1;
			this.control15_2.Location = new Point(0, 0);
			this.control15_2.Name = Class521.smethod_0(120868);
			this.control15_2.Size = new Size(579, 67);
			this.control15_2.TabIndex = 3;
			this.control15_2.TabStop = false;
			this.control15_2.Text = Class521.smethod_0(120889);
			this.label_1.Anchor = (AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
			this.label_1.FlatStyle = FlatStyle.System;
			this.label_1.Location = new Point(28, 80);
			this.label_1.Name = Class521.smethod_0(5849);
			this.label_1.Size = new Size(533, 49);
			this.label_1.TabIndex = 10;
			this.label_1.Text = Class521.smethod_0(120982);
			this.button_5.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.button_5.Enabled = false;
			this.button_5.FlatStyle = FlatStyle.System;
			this.button_5.Location = new Point(413, 227);
			this.button_5.Name = Class521.smethod_0(121144);
			this.button_5.Size = new Size(147, 28);
			this.button_5.TabIndex = 12;
			this.button_5.Text = Class521.smethod_0(120036);
			this.button_5.Click += this.button_5_Click;
			this.AutoScaleBaseSize = new Size(7, 15);
			this.BackColor = SystemColors.Window;
			base.ClientSize = new Size(602, 867);
			base.ControlBox = false;
			base.Controls.Add(this.panel_2);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.panel_1);
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.Name = Class521.smethod_0(121169);
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(121202);
			base.TopMost = true;
			this.panel_0.ResumeLayout(false);
			this.panel_1.ResumeLayout(false);
			this.panel_2.ResumeLayout(false);
			this.panel_2.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x06002858 RID: 10328 RVA: 0x0000FC9E File Offset: 0x0000DE9E
		private string method_1(string string_0)
		{
			string_0 = string_0.Replace(Class521.smethod_0(121202), Class521.smethod_0(121215));
			string_0 = string_0.Replace(Class521.smethod_0(121244), Class521.smethod_0(121265));
			return string_0;
		}

		// Token: 0x06002859 RID: 10329 RVA: 0x0010F6E4 File Offset: 0x0010D8E4
		public void method_2()
		{
			try
			{
				this.panel_2.Visible = false;
				this.panel_1.Visible = true;
				if (this.eventArgs35_0 != null)
				{
					this.method_3(new ThreadStart(this.method_8));
				}
			}
			catch
			{
			}
		}

		// Token: 0x0600285A RID: 10330 RVA: 0x0000FCD9 File Offset: 0x0000DED9
		private void button_1_Click(object sender, EventArgs e)
		{
			this.panel_0.Visible = false;
			this.panel_2.Visible = true;
		}

		// Token: 0x0600285B RID: 10331 RVA: 0x0000FCF3 File Offset: 0x0000DEF3
		private void method_3(ThreadStart threadStart_0)
		{
			this.thread_0 = new Thread(threadStart_0);
			this.thread_0.Start();
		}

		// Token: 0x0600285C RID: 10332 RVA: 0x0000FD0C File Offset: 0x0000DF0C
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x0600285D RID: 10333 RVA: 0x0010F738 File Offset: 0x0010D938
		private void button_2_Click(object sender, EventArgs e)
		{
			try
			{
				if (this.thread_0 != null)
				{
					this.thread_0.Abort();
				}
			}
			catch
			{
			}
			base.Close();
		}

		// Token: 0x0600285E RID: 10334 RVA: 0x0000FD0C File Offset: 0x0000DF0C
		private void button_3_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x0600285F RID: 10335 RVA: 0x0000FD14 File Offset: 0x0000DF14
		private void checkBox_0_CheckedChanged(object sender, EventArgs e)
		{
			this.eventArgs35_0.TryToContinue = this.checkBox_0.Checked;
		}

		// Token: 0x06002860 RID: 10336 RVA: 0x0010F774 File Offset: 0x0010D974
		private void method_4(object sender, EventArgs37 e)
		{
			try
			{
				base.Invoke(new Delegate38(this.method_6), new object[]
				{
					sender,
					e
				});
			}
			catch (InvalidOperationException)
			{
			}
		}

		// Token: 0x06002861 RID: 10337 RVA: 0x0010F7B8 File Offset: 0x0010D9B8
		private void method_5(object sender, EventArgs e)
		{
			try
			{
				base.Invoke(new EventHandler(this.method_7), new object[]
				{
					sender,
					e
				});
			}
			catch (InvalidOperationException)
			{
			}
		}

		// Token: 0x06002862 RID: 10338 RVA: 0x0000FD2C File Offset: 0x0000DF2C
		protected void OnClosing(CancelEventArgs e)
		{
			if (this.thread_0 != null && this.thread_0.IsAlive)
			{
				this.thread_0.Abort();
			}
			base.OnClosing(e);
		}

		// Token: 0x06002863 RID: 10339 RVA: 0x0010F7FC File Offset: 0x0010D9FC
		private void method_6(object sender, EventArgs37 e)
		{
			switch (e.Step)
			{
			case Enum35.const_0:
				if (e.Failed)
				{
					this.control14_0.method_3(e.ErrorMessage);
					this.button_4.Visible = true;
					this.button_4.Focus();
					return;
				}
				this.control14_0.method_1();
				return;
			case Enum35.const_1:
				if (e.Failed)
				{
					this.control14_1.method_3(e.ErrorMessage);
					this.button_4.Visible = true;
					this.button_4.Focus();
					return;
				}
				this.control14_0.method_2();
				this.control14_1.method_1();
				return;
			case Enum35.const_2:
				if (e.Failed)
				{
					this.control17_0.Visible = false;
					this.control14_2.method_3(e.ErrorMessage);
					this.button_4.Visible = true;
					this.button_4.Focus();
					return;
				}
				this.control14_1.method_2();
				this.control14_2.method_1();
				this.control17_0.Visible = true;
				return;
			case Enum35.const_3:
				this.control17_0.Visible = false;
				this.control14_2.method_2();
				this.control14_3.method_2();
				this.button_3.Enabled = true;
				this.button_3.Focus();
				this.button_2.Enabled = false;
				return;
			default:
				return;
			}
		}

		// Token: 0x06002864 RID: 10340 RVA: 0x0000FD0C File Offset: 0x0000DF0C
		private void method_7(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06002865 RID: 10341 RVA: 0x0010F958 File Offset: 0x0010DB58
		private void button_4_Click(object sender, EventArgs e)
		{
			this.button_4.Visible = false;
			this.control14_0.method_0();
			this.control14_1.method_0();
			this.control14_2.method_0();
			if (this.eventArgs35_0 != null)
			{
				this.method_3(new ThreadStart(this.method_8));
			}
		}

		// Token: 0x06002866 RID: 10342 RVA: 0x0000FD55 File Offset: 0x0000DF55
		private void method_8()
		{
			this.eventArgs35_0.method_6();
		}

		// Token: 0x06002867 RID: 10343 RVA: 0x0000FD63 File Offset: 0x0000DF63
		private void method_9(object sender, EventArgs e)
		{
			if (this.eventArgs35_0 != null)
			{
				this.method_3(new ThreadStart(this.eventArgs35_0.method_3));
			}
		}

		// Token: 0x06002868 RID: 10344 RVA: 0x0010F9AC File Offset: 0x0010DBAC
		private void button_5_Click(object sender, EventArgs e)
		{
			if (!this.checkBox_1.Checked && this.eventArgs35_0 != null)
			{
				this.eventArgs35_0.method_7(Class521.smethod_0(119889), this.textBox_0.Text);
				Class551.smethod_1(Class521.smethod_0(119889), this.textBox_0.Text);
			}
			this.method_2();
		}

		// Token: 0x06002869 RID: 10345 RVA: 0x0000FD84 File Offset: 0x0000DF84
		private void textBox_0_TextChanged(object sender, EventArgs e)
		{
			this.button_5.Enabled = (this.textBox_0.Text.Length > 0 || this.checkBox_1.Checked);
		}

		// Token: 0x0600286A RID: 10346 RVA: 0x0010FA10 File Offset: 0x0010DC10
		private void checkBox_1_CheckedChanged(object sender, EventArgs e)
		{
			this.textBox_0.Enabled = !this.checkBox_1.Checked;
			this.button_5.Enabled = (this.textBox_0.Text.Length > 0 || this.checkBox_1.Checked);
		}

		// Token: 0x0600286B RID: 10347 RVA: 0x0010FA64 File Offset: 0x0010DC64
		private void method_10(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.DefaultExt = Class521.smethod_0(121282);
			saveFileDialog.Filter = Class521.smethod_0(121307);
			saveFileDialog.Title = Class521.smethod_0(121396);
			if (saveFileDialog.ShowDialog(this) != DialogResult.Cancel)
			{
				if (this.eventArgs35_0.method_4(saveFileDialog.FileName))
				{
					MessageBox.Show(string.Format(Class521.smethod_0(121429), Class521.smethod_0(121265)), Class521.smethod_0(121215), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					base.Close();
					return;
				}
				MessageBox.Show(Class521.smethod_0(121502), Class521.smethod_0(121215), MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x04001427 RID: 5159
		private Class544 class544_0;

		// Token: 0x04001428 RID: 5160
		private EventArgs35 eventArgs35_0;

		// Token: 0x04001429 RID: 5161
		private Thread thread_0;

		// Token: 0x0400142A RID: 5162
		private CheckBox checkBox_0;

		// Token: 0x0400142B RID: 5163
		private Button button_0;

		// Token: 0x0400142C RID: 5164
		private Button button_1;

		// Token: 0x0400142D RID: 5165
		private Label label_0;

		// Token: 0x0400142E RID: 5166
		private Panel panel_0;

		// Token: 0x0400142F RID: 5167
		private Panel panel_1;

		// Token: 0x04001430 RID: 5168
		private Button button_2;

		// Token: 0x04001431 RID: 5169
		private Control17 control17_0;

		// Token: 0x04001432 RID: 5170
		private Control14 control14_0;

		// Token: 0x04001433 RID: 5171
		private Control14 control14_1;

		// Token: 0x04001434 RID: 5172
		private Control14 control14_2;

		// Token: 0x04001435 RID: 5173
		private Control14 control14_3;

		// Token: 0x04001436 RID: 5174
		private Button button_3;

		// Token: 0x04001437 RID: 5175
		private Button button_4;

		// Token: 0x04001438 RID: 5176
		private Control15 control15_0;

		// Token: 0x04001439 RID: 5177
		private Control15 control15_1;

		// Token: 0x0400143A RID: 5178
		private IContainer icontainer_0;

		// Token: 0x0400143B RID: 5179
		private Panel panel_2;

		// Token: 0x0400143C RID: 5180
		private Label label_1;

		// Token: 0x0400143D RID: 5181
		private Control15 control15_2;

		// Token: 0x0400143E RID: 5182
		private Button button_5;

		// Token: 0x0400143F RID: 5183
		private TextBox textBox_0;

		// Token: 0x04001440 RID: 5184
		private Label label_2;

		// Token: 0x04001441 RID: 5185
		private CheckBox checkBox_1;

		// Token: 0x04001442 RID: 5186
		private Class549 class549_0;
	}
}
