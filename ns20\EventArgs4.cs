﻿using System;
using ns22;

namespace ns20
{
	// Token: 0x0200008A RID: 138
	internal sealed class EventArgs4 : EventArgs
	{
		// Token: 0x0600048C RID: 1164 RVA: 0x00003F3F File Offset: 0x0000213F
		public EventArgs4(Enum4 enum4_1, int int_1)
		{
			this.enum4_0 = enum4_1;
			this.int_0 = int_1;
		}

		// Token: 0x170000EE RID: 238
		// (get) Token: 0x0600048D RID: 1165 RVA: 0x0002497C File Offset: 0x00022B7C
		public int Period
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x170000EF RID: 239
		// (get) Token: 0x0600048E RID: 1166 RVA: 0x00024994 File Offset: 0x00022B94
		public Enum4 NPeriodType
		{
			get
			{
				return this.enum4_0;
			}
		}

		// Token: 0x040001BC RID: 444
		private readonly Enum4 enum4_0;

		// Token: 0x040001BD RID: 445
		private readonly int int_0;
	}
}
