﻿using System;
using System.Drawing;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000033 RID: 51
	[Serializable]
	public sealed class BaoDian
	{
		// Token: 0x17000059 RID: 89
		// (get) Token: 0x06000158 RID: 344 RVA: 0x00017000 File Offset: 0x00015200
		// (set) Token: 0x06000159 RID: 345 RVA: 0x00002F71 File Offset: 0x00001171
		public string Name { get; set; }

		// Token: 0x1700005A RID: 90
		// (get) Token: 0x0600015A RID: 346 RVA: 0x00017018 File Offset: 0x00015218
		// (set) Token: 0x0600015B RID: 347 RVA: 0x00002F7C File Offset: 0x0000117C
		public string UID { get; set; }

		// Token: 0x1700005B RID: 91
		// (get) Token: 0x0600015C RID: 348 RVA: 0x00017030 File Offset: 0x00015230
		// (set) Token: 0x0600015D RID: 349 RVA: 0x00002F87 File Offset: 0x00001187
		public string Group { get; set; }

		// Token: 0x1700005C RID: 92
		// (get) Token: 0x0600015E RID: 350 RVA: 0x00017048 File Offset: 0x00015248
		// (set) Token: 0x0600015F RID: 351 RVA: 0x00002F92 File Offset: 0x00001192
		public int SymbolID { get; set; }

		// Token: 0x1700005D RID: 93
		// (get) Token: 0x06000160 RID: 352 RVA: 0x00017060 File Offset: 0x00015260
		// (set) Token: 0x06000161 RID: 353 RVA: 0x00002F9D File Offset: 0x0000119D
		public PeriodType PeriodType { get; set; }

		// Token: 0x1700005E RID: 94
		// (get) Token: 0x06000162 RID: 354 RVA: 0x00017078 File Offset: 0x00015278
		// (set) Token: 0x06000163 RID: 355 RVA: 0x00002FA8 File Offset: 0x000011A8
		public int? PeriodUnit { get; set; }

		// Token: 0x1700005F RID: 95
		// (get) Token: 0x06000164 RID: 356 RVA: 0x00017090 File Offset: 0x00015290
		// (set) Token: 0x06000165 RID: 357 RVA: 0x00002FB3 File Offset: 0x000011B3
		public DateTime SymbolTime { get; set; }

		// Token: 0x17000060 RID: 96
		// (get) Token: 0x06000166 RID: 358 RVA: 0x000170A8 File Offset: 0x000152A8
		// (set) Token: 0x06000167 RID: 359 RVA: 0x00002FBE File Offset: 0x000011BE
		public Bitmap ScreenShot { get; set; }

		// Token: 0x17000061 RID: 97
		// (get) Token: 0x06000168 RID: 360 RVA: 0x000170C0 File Offset: 0x000152C0
		// (set) Token: 0x06000169 RID: 361 RVA: 0x00002FC9 File Offset: 0x000011C9
		public string Note { get; set; }

		// Token: 0x17000062 RID: 98
		// (get) Token: 0x0600016A RID: 362 RVA: 0x000170D8 File Offset: 0x000152D8
		// (set) Token: 0x0600016B RID: 363 RVA: 0x00002FD4 File Offset: 0x000011D4
		public DateTime CreateTime { get; set; }
	}
}
