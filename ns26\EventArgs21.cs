﻿using System;

namespace ns26
{
	// Token: 0x02000278 RID: 632
	internal sealed class EventArgs21 : EventArgs
	{
		// Token: 0x06001B82 RID: 7042 RVA: 0x0000B63F File Offset: 0x0000983F
		public EventArgs21(bool bool_2, bool bool_3)
		{
			this.bool_1 = bool_2;
			this.bool_0 = bool_3;
		}

		// Token: 0x1700047D RID: 1149
		// (get) Token: 0x06001B83 RID: 7043 RVA: 0x000BFE4C File Offset: 0x000BE04C
		public bool IfTurnOnAutoStop
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x1700047E RID: 1150
		// (get) Token: 0x06001B84 RID: 7044 RVA: 0x000BFE64 File Offset: 0x000BE064
		public bool IfTurnOnAutoLimit
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x04000D99 RID: 3481
		private bool bool_0;

		// Token: 0x04000D9A RID: 3482
		private bool bool_1;
	}
}
