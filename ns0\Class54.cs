﻿using System;
using System.Windows.Forms;
using ns26;
using ns5;
using TEx;

namespace ns0
{
	// Token: 0x02000299 RID: 665
	internal sealed class Class54 : Class53
	{
		// Token: 0x06001D8C RID: 7564 RVA: 0x0000C58B File Offset: 0x0000A78B
		public Class54(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.Image = Class375.rewind;
		}

		// Token: 0x06001D8D RID: 7565 RVA: 0x0000C5A7 File Offset: 0x0000A7A7
		public Class54(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D8E RID: 7566 RVA: 0x0000C5B5 File Offset: 0x0000A7B5
		protected override void Class50_MouseEnter(object sender, EventArgs e)
		{
			base.Class50_MouseEnter(sender, e);
			base.Image = Class375.rewind_red;
		}

		// Token: 0x06001D8F RID: 7567 RVA: 0x0000C5CC File Offset: 0x0000A7CC
		protected override void Class50_MouseLeave(object sender, EventArgs e)
		{
			base.Class50_MouseLeave(sender, e);
			base.Image = Class375.rewind;
		}

		// Token: 0x06001D90 RID: 7568 RVA: 0x000D01F4 File Offset: 0x000CE3F4
		protected override void Class50_Click(object sender, EventArgs e)
		{
			base.Class50_Click(sender, e);
			ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)base.ChtCtrl;
			if (chtCtrl_KLine.IsInCrossReviewMode)
			{
				chtCtrl_KLine.IsInCrossReviewMode = false;
			}
			Base.UI.smethod_120();
		}
	}
}
