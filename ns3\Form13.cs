﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns16;
using ns18;
using ns26;
using ns4;
using ns9;
using TEx;
using TEx.Comn;

namespace ns3
{
	// Token: 0x02000170 RID: 368
	internal sealed partial class Form13 : Form
	{
		// Token: 0x14000074 RID: 116
		// (add) Token: 0x06000DFE RID: 3582 RVA: 0x0005B688 File Offset: 0x00059888
		// (remove) Token: 0x06000DFF RID: 3583 RVA: 0x0005B6C0 File Offset: 0x000598C0
		public event Delegate10 SymbParamsUpdated
		{
			[CompilerGenerated]
			add
			{
				Delegate10 @delegate = this.delegate10_0;
				Delegate10 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate10 value2 = (Delegate10)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate10>(ref this.delegate10_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate10 @delegate = this.delegate10_0;
				Delegate10 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate10 value2 = (Delegate10)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate10>(ref this.delegate10_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000E00 RID: 3584 RVA: 0x0005B6F8 File Offset: 0x000598F8
		protected void method_0(bool bool_3, bool bool_4)
		{
			EventArgs21 e = new EventArgs21(bool_3, bool_4);
			Delegate10 @delegate = this.delegate10_0;
			if (@delegate != null)
			{
				@delegate(this, e);
			}
		}

		// Token: 0x06000E01 RID: 3585 RVA: 0x000063D7 File Offset: 0x000045D7
		public Form13() : this(null, false, false, false)
		{
		}

		// Token: 0x06000E02 RID: 3586 RVA: 0x0005B724 File Offset: 0x00059924
		public Form13(List<StkSymbol> list_3, bool bool_3, bool bool_4, bool bool_5)
		{
			Form13.Class207 @class = new Form13.Class207();
			this.method_7();
			this.BackColor = SystemColors.Control;
			this.list_2 = list_3;
			this.bool_0 = bool_3;
			this.bool_1 = bool_4;
			this.bool_2 = bool_5;
			this.class308_0 = new Class308();
			this.class308_0.DialogResult = DialogResult.Cancel;
			this.class308_0.Name = Class521.smethod_0(25466);
			this.class308_0.TabIndex = 14;
			this.class308_0.Text = Class521.smethod_0(5783);
			this.class308_0.UseVisualStyleBackColor = true;
			this.class308_0.Visible = true;
			base.Controls.Add(this.class308_0);
			base.CancelButton = this.class308_0;
			this.list_0 = SymbMgr.LocalMstSymbolList;
			this.list_1 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			List<ExchgHouse> dataSource = Base.Data.smethod_87();
			this.comboBox_0.DisplayMember = Class521.smethod_0(16303);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.DataSource = dataSource;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			this.radioButton_1.CheckedChanged += this.radioButton_1_CheckedChanged;
			this.radioButton_0.CheckedChanged += this.radioButton_0_CheckedChanged;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Enabled = false;
			base.Shown += this.Form13_Shown;
			this.class308_0.Size = this.button_0.Size;
			this.class308_0.Location = new Point(this.button_0.Location.X + (this.button_0.Location.X - this.button_1.Location.X), this.button_0.Location.Y);
			if (this.bool_2)
			{
				Form13.Class208 class2 = new Form13.Class208();
				if (this.list_2 != null && this.list_2.Any<StkSymbol>())
				{
					class2.tradingSymbol_0 = this.list_2.First<StkSymbol>();
				}
				else
				{
					class2.tradingSymbol_0 = Base.UI.CurrSymbol;
				}
				if (class2.tradingSymbol_0 != null)
				{
					this.comboBox_0.SelectedItem = (this.comboBox_0.DataSource as List<ExchgHouse>).Single(new Func<ExchgHouse, bool>(class2.method_0));
				}
			}
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_0.SelectedItem;
			List<TradingSymbol> dataSource2 = this.list_0.Where(new Func<TradingSymbol, bool>(@class.method_0)).ToList<TradingSymbol>();
			this.control9_0.IfShowCurrSymbOnStartup = this.bool_2;
			this.control9_0.IfFocusOnAutoLimitOnStartup = this.bool_1;
			this.control9_0.IfFocusOnAutoStopOnStartup = this.bool_0;
			this.control9_0.DataSource = dataSource2;
			this.control9_0.IsInGlobalSymbls = true;
			if (list_3 != null && list_3.Any<StkSymbol>())
			{
				this.control9_0.method_2(list_3.First<StkSymbol>());
			}
			else
			{
				this.control9_0.method_2(null);
			}
			this.control9_0.FieldChanged += this.method_2;
			this.control9_0.FieldsChkNoChange += this.method_3;
		}

		// Token: 0x06000E03 RID: 3587 RVA: 0x000063E3 File Offset: 0x000045E3
		private void Form13_Load(object sender, EventArgs e)
		{
			this.radioButton_1.Checked = true;
		}

		// Token: 0x06000E04 RID: 3588 RVA: 0x000063F3 File Offset: 0x000045F3
		private void Form13_Shown(object sender, EventArgs e)
		{
			if (this.bool_2)
			{
				this.control9_0.Focus();
				this.control9_0.method_3();
			}
		}

		// Token: 0x06000E05 RID: 3589 RVA: 0x0005BA84 File Offset: 0x00059C84
		private BindingList<ExchgHouse> method_1()
		{
			BindingList<ExchgHouse> bindingList = new BindingList<ExchgHouse>();
			foreach (ExchgHouse item in Base.Data.ExchangeList)
			{
				bindingList.Add(item);
			}
			return bindingList;
		}

		// Token: 0x06000E06 RID: 3590 RVA: 0x0005BAE4 File Offset: 0x00059CE4
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			Form13.Class209 @class = new Form13.Class209();
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_0.SelectedItem;
			List<TradingSymbol> source;
			if (this.radioButton_1.Checked)
			{
				source = this.list_0;
			}
			else
			{
				source = this.list_1;
			}
			this.control9_0.DataSource = source.Where(new Func<TradingSymbol, bool>(@class.method_0)).ToList<TradingSymbol>();
		}

		// Token: 0x06000E07 RID: 3591 RVA: 0x0005BB50 File Offset: 0x00059D50
		private void radioButton_1_CheckedChanged(object sender, EventArgs e)
		{
			this.method_4();
			if (this.radioButton_1.Checked)
			{
				this.label_0.Text = Class521.smethod_0(25483);
			}
			else
			{
				this.label_0.Text = Class521.smethod_0(25584);
			}
		}

		// Token: 0x06000E08 RID: 3592 RVA: 0x000041B9 File Offset: 0x000023B9
		private void radioButton_0_CheckedChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000E09 RID: 3593 RVA: 0x00006416 File Offset: 0x00004616
		private void method_2(object sender, EventArgs e)
		{
			if (!this.button_1.Enabled)
			{
				this.button_1.Enabled = true;
			}
		}

		// Token: 0x06000E0A RID: 3594 RVA: 0x00006433 File Offset: 0x00004633
		private void method_3(object sender, EventArgs e)
		{
			if (this.button_1.Enabled)
			{
				this.button_1.Enabled = false;
			}
		}

		// Token: 0x06000E0B RID: 3595 RVA: 0x00006450 File Offset: 0x00004650
		private void button_1_Click(object sender, EventArgs e)
		{
			(sender as Button).Enabled = false;
			this.control9_0.method_21();
			this.method_4();
		}

		// Token: 0x06000E0C RID: 3596 RVA: 0x0005BBA0 File Offset: 0x00059DA0
		private void button_0_Click(object sender, EventArgs e)
		{
			this.control9_0.method_21();
			if ((this.bool_1 || this.bool_0) && this.list_2 != null)
			{
				foreach (StkSymbol stkSymbol in this.list_2)
				{
					StkSymbol stkSymbol2 = SymbMgr.smethod_3(stkSymbol.ID);
					if (this.bool_0 && stkSymbol2.AutoStopLossPoints == null)
					{
						if (MessageBox.Show(Class521.smethod_0(25697) + stkSymbol2.Desc + Class521.smethod_0(25710), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							this.method_0(this.bool_0, this.bool_1);
							base.Dispose();
						}
						return;
					}
					if (this.bool_1 && stkSymbol2.AutoLimitTakePoints == null)
					{
						if (MessageBox.Show(Class521.smethod_0(25697) + stkSymbol2.Desc + Class521.smethod_0(25783), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							this.method_0(this.bool_0, this.bool_1);
							base.Dispose();
						}
						return;
					}
				}
			}
			this.method_0(this.bool_0, this.bool_1);
			base.Dispose();
		}

		// Token: 0x06000E0D RID: 3597 RVA: 0x0005BD0C File Offset: 0x00059F0C
		private void method_4()
		{
			this.list_0 = SymbMgr.LocalMstSymbolList;
			this.list_1 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			int selectedIndex = this.comboBox_0.SelectedIndex;
			int currSymblLstIndex = this.control9_0.CurrSymblLstIndex;
			bool @checked = this.radioButton_1.Checked;
			List<ExchgHouse> dataSource = Base.Data.smethod_87();
			this.comboBox_0.ForeColor = this.comboBox_0.BackColor;
			this.comboBox_0.DataSource = dataSource;
			this.comboBox_0.SelectedIndex = selectedIndex;
			this.comboBox_0.ForeColor = default(Color);
			this.control9_0.IsInGlobalSymbls = @checked;
			this.control9_0.CurrSymblLstIndex = currSymblLstIndex;
		}

		// Token: 0x06000E0E RID: 3598 RVA: 0x00006471 File Offset: 0x00004671
		public void method_5()
		{
			this.control9_0.method_23();
		}

		// Token: 0x06000E0F RID: 3599 RVA: 0x00006480 File Offset: 0x00004680
		public void method_6()
		{
			this.control9_0.method_24();
		}

		// Token: 0x17000233 RID: 563
		// (get) Token: 0x06000E10 RID: 3600 RVA: 0x0005BDC8 File Offset: 0x00059FC8
		public Control9 SymbParamCtrl
		{
			get
			{
				return this.control9_0;
			}
		}

		// Token: 0x06000E11 RID: 3601 RVA: 0x0000648F File Offset: 0x0000468F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000E12 RID: 3602 RVA: 0x0005BDE0 File Offset: 0x00059FE0
		private void method_7()
		{
			new ComponentResourceManager(typeof(Form13));
			this.comboBox_0 = new ComboBox();
			this.panel_0 = new Panel();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.control9_0 = new Control9();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.pictureBox_0 = new PictureBox();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.panel_0.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			base.SuspendLayout();
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(32, 40);
			this.comboBox_0.Name = Class521.smethod_0(19518);
			this.comboBox_0.Size = new Size(160, 23);
			this.comboBox_0.TabIndex = 0;
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Location = new Point(216, 11);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(292, 57);
			this.panel_0.TabIndex = 2;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Location = new Point(8, 33);
			this.radioButton_0.Name = Class521.smethod_0(25856);
			this.radioButton_0.Size = new Size(268, 19);
			this.radioButton_0.TabIndex = 1;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(25881);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(8, 9);
			this.radioButton_1.Name = Class521.smethod_0(25946);
			this.radioButton_1.Size = new Size(253, 19);
			this.radioButton_1.TabIndex = 0;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(25967);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.control9_0.Location = new Point(13, 67);
			this.control9_0.Name = Class521.smethod_0(26028);
			this.control9_0.Size = new Size(723, 347);
			this.control9_0.TabIndex = 3;
			this.button_0.Location = new Point(479, 431);
			this.button_0.Name = Class521.smethod_0(7442);
			this.button_0.Size = new Size(120, 30);
			this.button_0.TabIndex = 13;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(341, 431);
			this.button_1.Name = Class521.smethod_0(26049);
			this.button_1.Size = new Size(120, 30);
			this.button_1.TabIndex = 15;
			this.button_1.Text = Class521.smethod_0(26062);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(506, 16);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 16;
			this.pictureBox_0.TabStop = false;
			this.label_0.Location = new Point(578, 19);
			this.label_0.Name = Class521.smethod_0(26071);
			this.label_0.Size = new Size(168, 54);
			this.label_0.TabIndex = 17;
			this.label_0.Text = Class521.smethod_0(25483);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(529, 19);
			this.label_1.Name = Class521.smethod_0(5871);
			this.label_1.Size = new Size(45, 15);
			this.label_1.TabIndex = 18;
			this.label_1.Text = Class521.smethod_0(26092);
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(29, 20);
			this.label_2.Name = Class521.smethod_0(5827);
			this.label_2.Size = new Size(67, 15);
			this.label_2.TabIndex = 19;
			this.label_2.Text = Class521.smethod_0(19501);
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(749, 480);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.control9_0);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.comboBox_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(26105);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(26130);
			base.Load += this.Form13_Load;
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			((ISupportInitialize)this.pictureBox_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x0400073B RID: 1851
		private List<TradingSymbol> list_0;

		// Token: 0x0400073C RID: 1852
		private List<TradingSymbol> list_1;

		// Token: 0x0400073D RID: 1853
		private List<StkSymbol> list_2;

		// Token: 0x0400073E RID: 1854
		private bool bool_0;

		// Token: 0x0400073F RID: 1855
		private bool bool_1;

		// Token: 0x04000740 RID: 1856
		private bool bool_2;

		// Token: 0x04000741 RID: 1857
		[CompilerGenerated]
		private Delegate10 delegate10_0;

		// Token: 0x04000742 RID: 1858
		private Class308 class308_0;

		// Token: 0x04000743 RID: 1859
		private IContainer icontainer_0;

		// Token: 0x04000744 RID: 1860
		private ComboBox comboBox_0;

		// Token: 0x04000745 RID: 1861
		private Panel panel_0;

		// Token: 0x04000746 RID: 1862
		private RadioButton radioButton_0;

		// Token: 0x04000747 RID: 1863
		private RadioButton radioButton_1;

		// Token: 0x04000748 RID: 1864
		private Control9 control9_0;

		// Token: 0x04000749 RID: 1865
		private Button button_0;

		// Token: 0x0400074A RID: 1866
		private Button button_1;

		// Token: 0x0400074B RID: 1867
		private PictureBox pictureBox_0;

		// Token: 0x0400074C RID: 1868
		private Label label_0;

		// Token: 0x0400074D RID: 1869
		private Label label_1;

		// Token: 0x0400074E RID: 1870
		private Label label_2;

		// Token: 0x02000171 RID: 369
		[CompilerGenerated]
		private sealed class Class207
		{
			// Token: 0x06000E14 RID: 3604 RVA: 0x0005C4D8 File Offset: 0x0005A6D8
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x0400074F RID: 1871
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x02000172 RID: 370
		[CompilerGenerated]
		private sealed class Class208
		{
			// Token: 0x06000E16 RID: 3606 RVA: 0x0005C4FC File Offset: 0x0005A6FC
			internal bool method_0(ExchgHouse exchgHouse_0)
			{
				return exchgHouse_0.ID == this.tradingSymbol_0.ExchangeID;
			}

			// Token: 0x04000750 RID: 1872
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x02000173 RID: 371
		[CompilerGenerated]
		private sealed class Class209
		{
			// Token: 0x06000E18 RID: 3608 RVA: 0x0005C520 File Offset: 0x0005A720
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x04000751 RID: 1873
			public ExchgHouse exchgHouse_0;
		}
	}
}
