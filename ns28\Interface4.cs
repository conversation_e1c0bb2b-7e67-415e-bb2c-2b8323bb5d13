﻿using System;
using System.Collections.Generic;
using TEx.ImportTrans;

namespace ns28
{
	// Token: 0x0200036B RID: 875
	internal interface Interface4
	{
		// Token: 0x06002495 RID: 9365
		void imethod_0(IStoreElement istoreElement_0);

		// Token: 0x06002496 RID: 9366
		void imethod_1(IStoreElement istoreElement_0);

		// Token: 0x06002497 RID: 9367
		void imethod_2(IStoreElement istoreElement_0);

		// Token: 0x06002498 RID: 9368
		List<IStoreElement> imethod_3();

		// Token: 0x06002499 RID: 9369
		IStoreElement imethod_4(string string_0);
	}
}
