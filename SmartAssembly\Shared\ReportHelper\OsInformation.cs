﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using ns13;
using ns15;
using ns16;
using ns18;
using ns20;
using ns9;

namespace SmartAssembly.Shared.ReportHelper
{
	// Token: 0x020003C7 RID: 967
	public static class OsInformation
	{
		// Token: 0x060026DE RID: 9950 RVA: 0x0000EF74 File Offset: 0x0000D174
		public static string smethod_0(Version version_0)
		{
			return OsInformation.smethod_1(version_0);
		}

		// Token: 0x060026DF RID: 9951 RVA: 0x00106060 File Offset: 0x00104260
		private static string smethod_1(Version version_0)
		{
			bool flag = OsVersionInformation.smethod_0();
			bool isX = OsVersionInformation.IsX64;
			int major = version_0.Major;
			if (major != 5)
			{
				if (major != 6)
				{
					if (major == 10)
					{
						if (version_0.Minor == 0)
						{
							if (flag)
							{
								return Class521.smethod_0(116271);
							}
							if (version_0.Build < 17623)
							{
								return Class521.smethod_0(116288);
							}
							return Class521.smethod_0(116317);
						}
					}
				}
				else
				{
					switch (version_0.Minor)
					{
					case 0:
						if (!flag)
						{
							return Class521.smethod_0(15820);
						}
						return Class521.smethod_0(15799);
					case 1:
						if (!flag)
						{
							return Class521.smethod_0(15862);
						}
						return Class521.smethod_0(15849);
					case 2:
						if (!flag)
						{
							return Class521.smethod_0(15908);
						}
						return Class521.smethod_0(15895);
					case 3:
						if (!flag)
						{
							return Class521.smethod_0(116221);
						}
						return Class521.smethod_0(116254);
					case 4:
						return Class521.smethod_0(116271);
					}
				}
			}
			else
			{
				switch (version_0.Minor)
				{
				case 0:
					return Class521.smethod_0(15736);
				case 1:
					return Class521.smethod_0(15753);
				case 2:
					if (!flag || !isX)
					{
						return Class521.smethod_0(15770);
					}
					return Class521.smethod_0(116184);
				}
			}
			return string.Format(Class521.smethod_0(116346), new object[]
			{
				version_0.Major,
				version_0.Minor,
				version_0.Build,
				version_0.Revision
			});
		}

		// Token: 0x060026E0 RID: 9952 RVA: 0x0000EF7C File Offset: 0x0000D17C
		public static Version smethod_2(Version version_0)
		{
			return OsInformation.smethod_6(OsInformation.smethod_5(), version_0);
		}

		// Token: 0x060026E1 RID: 9953 RVA: 0x0000EF89 File Offset: 0x0000D189
		public static void smethod_3(out Enum31 enum31_0, ref Version version_0, ref Version version_1, ref string string_0, out string string_1, out bool bool_0)
		{
			enum31_0 = OsInformation.smethod_5();
			version_1 = OsInformation.smethod_6(enum31_0, version_1);
			string_1 = OsInformation.smethod_4(version_1);
			bool_0 = OsVersionInformation.IsX64;
		}

		// Token: 0x060026E2 RID: 9954 RVA: 0x00106200 File Offset: 0x00104400
		public static string smethod_4(Version version_0)
		{
			return string.Format(Class521.smethod_0(116379), new object[]
			{
				version_0.Major,
				version_0.Minor,
				version_0.Build,
				version_0.Revision
			});
		}

		// Token: 0x060026E3 RID: 9955 RVA: 0x0000EFAF File Offset: 0x0000D1AF
		private static Enum31 smethod_5()
		{
			return Enum31.const_0;
		}

		// Token: 0x060026E4 RID: 9956 RVA: 0x0000EFB2 File Offset: 0x0000D1B2
		private static Version smethod_6(Enum31 enum31_0, Version version_0)
		{
			return OsInformation.smethod_7(version_0);
		}

		// Token: 0x060026E5 RID: 9957 RVA: 0x0010625C File Offset: 0x0010445C
		private static Version smethod_7(Version version_0)
		{
			try
			{
				if ((version_0.Major >= 6 && version_0.Minor >= 2) || (version_0.Major == 0 && version_0.Minor == 0))
				{
					Enum33 @enum;
					using (Class516 @class = Class518.smethod_0(Class515.uintptr_2, Enum34.const_0, Enum32.const_0, Class521.smethod_0(116424), out @enum) ?? Class518.smethod_0(Class515.uintptr_2, Enum34.const_0, Enum32.const_0, Class521.smethod_0(116485), out @enum))
					{
						if (@enum == Enum33.const_2)
						{
							int major = 0;
							int minor = 0;
							int build = 0;
							int revision = 0;
							object obj;
							object obj2;
							object obj3;
							object obj4;
							string string_;
							if (@class.vmethod_1(Class521.smethod_0(116562), out obj) && @class.vmethod_1(Class521.smethod_0(116599), out obj2))
							{
								if ((obj3 = obj) is int)
								{
									int num = (int)obj3;
									major = num;
								}
								if ((obj3 = obj2) is int)
								{
									int num2 = (int)obj3;
									minor = num2;
								}
							}
							else if (@class.vmethod_1(Class521.smethod_0(116636), out obj4) && (string_ = (obj4 as string)) != null)
							{
								Version version = OsInformation.smethod_8(string_);
								major = version.Major;
								minor = version.Minor;
							}
							object obj5;
							string string_2;
							if (@class.vmethod_1(Class521.smethod_0(116657), out obj5) && (string_2 = (obj5 as string)) != null)
							{
								build = OsInformation.smethod_8(string_2).Major;
							}
							object obj6;
							if (@class.vmethod_1(Class521.smethod_0(116682), out obj6) && (obj3 = obj6) is int)
							{
								int num3 = (int)obj3;
								revision = num3;
							}
							return new Version(major, minor, build, revision);
						}
					}
				}
			}
			catch
			{
			}
			return version_0;
		}

		// Token: 0x060026E6 RID: 9958 RVA: 0x00106420 File Offset: 0x00104620
		private static Version smethod_8(string string_0)
		{
			OsInformation.Struct18 @struct;
			@struct.string_0 = string_0;
			@struct.int_0 = 0;
			int[] array = new int[4];
			for (int i = 0; i < 4; i++)
			{
				OsInformation.smethod_9(ref @struct);
				int num = OsInformation.smethod_10(ref @struct);
				if (num == 0)
				{
					break;
				}
				int num2;
				if (int.TryParse(@struct.string_0.Substring(@struct.int_0, num), out num2))
				{
					array[i] = num2;
				}
				@struct.int_0 += num;
			}
			return new Version(array[0], array[1], array[2], array[3]);
		}

		// Token: 0x060026E7 RID: 9959 RVA: 0x001064A4 File Offset: 0x001046A4
		[CompilerGenerated]
		internal static void smethod_9(ref OsInformation.Struct18 struct18_0)
		{
			while (struct18_0.int_0 < struct18_0.string_0.Length && !char.IsNumber(struct18_0.string_0[struct18_0.int_0]))
			{
				int int_ = struct18_0.int_0;
				struct18_0.int_0 = int_ + 1;
			}
		}

		// Token: 0x060026E8 RID: 9960 RVA: 0x001064F0 File Offset: 0x001046F0
		[CompilerGenerated]
		internal static int smethod_10(ref OsInformation.Struct18 struct18_0)
		{
			int num = struct18_0.int_0;
			while (num < struct18_0.string_0.Length && char.IsNumber(struct18_0.string_0[num]))
			{
				num++;
			}
			return num - struct18_0.int_0;
		}

		// Token: 0x020003C8 RID: 968
		[CompilerGenerated]
		[StructLayout(LayoutKind.Auto)]
		private struct Struct18
		{
			// Token: 0x040012C3 RID: 4803
			public string string_0;

			// Token: 0x040012C4 RID: 4804
			public int int_0;
		}
	}
}
