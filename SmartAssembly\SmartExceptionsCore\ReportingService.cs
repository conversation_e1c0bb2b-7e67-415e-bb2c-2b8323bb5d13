﻿using System;
using System.Net;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Serialization;
using ns18;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000409 RID: 1033
	[WebServiceBinding(Name = "ReportingServiceSoap", Namespace = "http://www.smartassembly.com/webservices/Reporting/")]
	internal sealed class ReportingService : SoapHttpClientProtocol
	{
		// Token: 0x0600280C RID: 10252 RVA: 0x0000F84F File Offset: 0x0000DA4F
		public ReportingService(string serverUrl)
		{
			base.Url = serverUrl + Class521.smethod_0(119340);
			base.Timeout = 180000;
		}

		// Token: 0x0600280D RID: 10253 RVA: 0x0000F878 File Offset: 0x0000DA78
		[SoapDocumentMethod("http://www.smartassembly.com/webservices/Reporting/UploadReport2")]
		public string UploadReport2(string licenseID, [XmlElement(DataType = "base64Binary")] byte[] data, string email, string appFriendlyName, string buildFriendlyNumber)
		{
			return (string)base.Invoke(Class521.smethod_0(119361), new object[]
			{
				licenseID,
				data,
				email,
				appFriendlyName,
				buildFriendlyNumber
			})[0];
		}

		// Token: 0x0600280E RID: 10254 RVA: 0x0010D560 File Offset: 0x0010B760
		protected override WebRequest GetWebRequest(Uri uri)
		{
			WebRequest webRequest = base.GetWebRequest(uri);
			HttpWebRequest httpWebRequest = webRequest as HttpWebRequest;
			if (httpWebRequest != null)
			{
				httpWebRequest.ServicePoint.Expect100Continue = false;
			}
			return webRequest;
		}
	}
}
