﻿using System;
using System.Collections.Generic;
using ns2;

namespace ns13
{
	// Token: 0x02000061 RID: 97
	internal sealed class EventArgs0 : EventArgs
	{
		// Token: 0x0600034C RID: 844 RVA: 0x000036B8 File Offset: 0x000018B8
		public EventArgs0(List<Class19> list_1)
		{
			this.InfoMineList = list_1;
		}

		// Token: 0x170000C4 RID: 196
		// (get) Token: 0x0600034D RID: 845 RVA: 0x0001FF74 File Offset: 0x0001E174
		// (set) Token: 0x0600034E RID: 846 RVA: 0x000036C9 File Offset: 0x000018C9
		public List<Class19> InfoMineList
		{
			get
			{
				return this.list_0;
			}
			private set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x0400012B RID: 299
		private List<Class19> list_0;
	}
}
