﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns20
{
	// Token: 0x020002F5 RID: 757
	internal sealed class Class394 : ShapeCurve
	{
		// Token: 0x06002126 RID: 8486 RVA: 0x000EB6EC File Offset: 0x000E98EC
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			StickHiLowItem stickHiLowItem = zedGraphControl_0.GraphPane.AddStickHiLow(base.IndData.Name, base.DataView, color_0);
			stickHiLowItem.Line.Color = color_0;
			this.curveItem_0 = stickHiLowItem;
			stickHiLowItem.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			base.method_3(string_0, stickHiLowItem);
		}

		// Token: 0x06002127 RID: 8487 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class394(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002128 RID: 8488 RVA: 0x000EB774 File Offset: 0x000E9974
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			Class394.Class409 @class = new Class394.Class409();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_0);
			if (@class.dataArray_0.Data.Length < int_0 + 1)
			{
				throw new Exception(Class521.smethod_0(97541));
			}
			double num = @class.dataArray_0.Data[int_0];
			if (@class.dataArray_0.OtherDataArrayList.Count != 3)
			{
				throw new Exception(Class521.smethod_0(98421));
			}
			if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
			{
				throw new Exception(Class521.smethod_0(98474));
			}
			double y = @class.dataArray_0.Data[int_0];
			int num2 = (int)@class.dataArray_0.OtherDataArrayList[0].Data[int_0];
			int num3 = (int)@class.dataArray_0.OtherDataArrayList[1].Data[int_0];
			double z = @class.dataArray_0.OtherDataArrayList[2].Data[int_0];
			PointPair result;
			if (num2 == 1 && num3 == 1)
			{
				result = new PointPair(new XDate(dateTime), y, z);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x020002F6 RID: 758
		[CompilerGenerated]
		private sealed class Class409
		{
			// Token: 0x0600212A RID: 8490 RVA: 0x000EB8B8 File Offset: 0x000E9AB8
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x0400102F RID: 4143
			public DataArray dataArray_0;
		}
	}
}
