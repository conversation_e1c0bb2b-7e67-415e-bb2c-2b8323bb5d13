﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns18;
using ns28;
using ns3;
using TEx.Util;

namespace TEx.ImportTrans
{
	// Token: 0x02000371 RID: 881
	public sealed class CWrongNameStore : Class474, Interface4
	{
		// Token: 0x060024B4 RID: 9396 RVA: 0x0000E471 File Offset: 0x0000C671
		public CWrongNameStore()
		{
			this.string_0 = CfmmcRecImporter.smethod_34(CfmmcRecImporter.string_2);
		}

		// Token: 0x060024B5 RID: 9397 RVA: 0x000FE978 File Offset: 0x000FCB78
		private void method_2(List<IStoreElement> list_0)
		{
			File.Exists(this.string_0);
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement(Class521.smethod_0(105723));
			xdocument.Add(xelement);
			for (int i = 0; i < list_0.Count; i++)
			{
				CWrongUserName cwrongUserName_ = (CWrongUserName)list_0[i];
				XElement content = this.method_3(cwrongUserName_);
				xelement.Add(content);
			}
			xdocument.Save(this.string_0);
		}

		// Token: 0x060024B6 RID: 9398 RVA: 0x000FE9F0 File Offset: 0x000FCBF0
		private XElement method_3(CWrongUserName cwrongUserName_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(17781));
			xelement.SetAttributeValue(Class521.smethod_0(9713), cwrongUserName_0.string_0);
			xelement.SetAttributeValue(Class521.smethod_0(108598), cwrongUserName_0.string_1);
			xelement.SetAttributeValue(Class521.smethod_0(108611), cwrongUserName_0.dateTime_0);
			return xelement;
		}

		// Token: 0x060024B7 RID: 9399 RVA: 0x000FEA6C File Offset: 0x000FCC6C
		public void imethod_2(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class477 @class = new CWrongNameStore.Class477();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			list.RemoveAll(new Predicate<IStoreElement>(@class.method_0));
			this.method_2(list);
		}

		// Token: 0x060024B8 RID: 9400 RVA: 0x000FEAAC File Offset: 0x000FCCAC
		public void imethod_1(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class478 @class = new CWrongNameStore.Class478();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			int num = list.FindIndex(new Predicate<IStoreElement>(@class.method_0));
			if (num != -1)
			{
				list[num] = @class.istoreElement_0;
				this.method_2(list);
			}
		}

		// Token: 0x060024B9 RID: 9401 RVA: 0x000FEAFC File Offset: 0x000FCCFC
		public void imethod_0(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class479 @class = new CWrongNameStore.Class479();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			if (list != null && !list.Any(new Func<IStoreElement, bool>(@class.method_0)))
			{
				list.Add(@class.istoreElement_0);
				this.method_2(list);
			}
		}

		// Token: 0x060024BA RID: 9402 RVA: 0x000FEB4C File Offset: 0x000FCD4C
		private CWrongUserName method_4(XElement xelement_0)
		{
			string value = xelement_0.Attribute(Class521.smethod_0(9713)).Value;
			string value2 = xelement_0.Attribute(Class521.smethod_0(108598)).Value;
			DateTime date = Convert.ToDateTime(xelement_0.Attribute(Class521.smethod_0(108611)).Value);
			return new CWrongUserName(value, value2, date);
		}

		// Token: 0x060024BB RID: 9403 RVA: 0x000FEBBC File Offset: 0x000FCDBC
		private bool method_5(IStoreElement istoreElement_0)
		{
			return istoreElement_0 is CWrongUserName;
		}

		// Token: 0x060024BC RID: 9404 RVA: 0x000FEBD8 File Offset: 0x000FCDD8
		protected override List<IStoreElement> vmethod_0()
		{
			List<IStoreElement> result;
			if (Utility.FileExists(this.string_0))
			{
				List<CWrongUserName> list = new List<CWrongUserName>();
				foreach (XElement xelement_ in XDocument.Load(this.string_0).Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(17781)))
				{
					CWrongUserName item = this.method_4(xelement_);
					list.Add(item);
				}
				result = list.Select(new Func<CWrongUserName, IStoreElement>(CWrongNameStore.<>c.<>9.method_0)).ToList<IStoreElement>();
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060024BD RID: 9405 RVA: 0x000FECA8 File Offset: 0x000FCEA8
		public IStoreElement imethod_4(string string_1)
		{
			CWrongNameStore.Class480 @class = new CWrongNameStore.Class480();
			@class.string_0 = string_1;
			List<IStoreElement> list = base.imethod_3();
			IStoreElement result;
			if (list != null)
			{
				IStoreElement storeElement;
				try
				{
					storeElement = list.Single(new Func<IStoreElement, bool>(@class.method_0));
				}
				catch (Exception)
				{
					throw;
				}
				result = storeElement;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x02000372 RID: 882
		[CompilerGenerated]
		private sealed class Class477
		{
			// Token: 0x060024BF RID: 9407 RVA: 0x000FED00 File Offset: 0x000FCF00
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B1 RID: 4529
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000373 RID: 883
		[CompilerGenerated]
		private sealed class Class478
		{
			// Token: 0x060024C1 RID: 9409 RVA: 0x000FED28 File Offset: 0x000FCF28
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B2 RID: 4530
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000374 RID: 884
		[CompilerGenerated]
		private sealed class Class479
		{
			// Token: 0x060024C3 RID: 9411 RVA: 0x000FED50 File Offset: 0x000FCF50
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B3 RID: 4531
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000376 RID: 886
		[CompilerGenerated]
		private sealed class Class480
		{
			// Token: 0x060024C8 RID: 9416 RVA: 0x000FED78 File Offset: 0x000FCF78
			internal bool method_0(IStoreElement istoreElement_0)
			{
				return istoreElement_0.ID == this.string_0;
			}

			// Token: 0x040011B6 RID: 4534
			public string string_0;
		}
	}
}
