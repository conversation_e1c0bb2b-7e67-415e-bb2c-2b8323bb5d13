﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns20;
using ns22;
using ns9;

namespace ns1
{
	// Token: 0x02000097 RID: 151
	internal sealed partial class Form6 : Form
	{
		// Token: 0x14000021 RID: 33
		// (add) Token: 0x06000503 RID: 1283 RVA: 0x000280E8 File Offset: 0x000262E8
		// (remove) Token: 0x06000504 RID: 1284 RVA: 0x00028120 File Offset: 0x00026320
		internal event Delegate2 NPeriodSet
		{
			[CompilerGenerated]
			add
			{
				Delegate2 @delegate = this.delegate2_0;
				Delegate2 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate2 value2 = (Delegate2)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate2>(ref this.delegate2_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate2 @delegate = this.delegate2_0;
				Delegate2 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate2 value2 = (Delegate2)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate2>(ref this.delegate2_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000505 RID: 1285 RVA: 0x00028158 File Offset: 0x00026358
		protected void method_0(EventArgs4 eventArgs4_0)
		{
			Delegate2 @delegate = this.delegate2_0;
			if (@delegate != null)
			{
				@delegate(eventArgs4_0);
			}
		}

		// Token: 0x06000506 RID: 1286 RVA: 0x000043FE File Offset: 0x000025FE
		public Form6(Enum4 enum4_1) : this(enum4_1, 10)
		{
		}

		// Token: 0x06000507 RID: 1287 RVA: 0x00028178 File Offset: 0x00026378
		public Form6(Enum4 enum4_1, int int_0)
		{
			this.method_1();
			this.enum4_0 = enum4_1;
			this.numericUpDown_0.Minimum = 1m;
			this.numericUpDown_0.Maximum = 500m;
			this.numericUpDown_0.Value = int_0;
			switch (enum4_1)
			{
			case Enum4.const_0:
				this.label_0.Text = Class521.smethod_0(8992);
				break;
			case Enum4.const_1:
				this.label_0.Text = Class521.smethod_0(9021);
				break;
			case Enum4.const_2:
				this.label_0.Text = Class521.smethod_0(9050);
				break;
			}
			this.button_1.Click += this.button_1_Click;
			base.Shown += this.Form6_Shown;
		}

		// Token: 0x06000508 RID: 1288 RVA: 0x00004409 File Offset: 0x00002609
		private void Form6_Shown(object sender, EventArgs e)
		{
			this.numericUpDown_0.Select(0, this.numericUpDown_0.Text.Length);
		}

		// Token: 0x06000509 RID: 1289 RVA: 0x00028250 File Offset: 0x00026450
		private void button_1_Click(object sender, EventArgs e)
		{
			EventArgs4 eventArgs4_ = new EventArgs4(this.enum4_0, Convert.ToInt32(this.numericUpDown_0.Value));
			this.method_0(eventArgs4_);
			base.Close();
		}

		// Token: 0x0600050A RID: 1290 RVA: 0x00004429 File Offset: 0x00002629
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600050B RID: 1291 RVA: 0x00028288 File Offset: 0x00026488
		private void method_1()
		{
			this.label_0 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.button_0 = new Button();
			this.button_1 = new Button();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(69, 34);
			this.label_0.Name = Class521.smethod_0(9075);
			this.label_0.Size = new Size(112, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(8992);
			this.numericUpDown_0.Location = new Point(187, 30);
			this.numericUpDown_0.Name = Class521.smethod_0(9092);
			this.numericUpDown_0.Size = new Size(144, 25);
			this.numericUpDown_0.TabIndex = 1;
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(312, 89);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(111, 30);
			this.button_0.TabIndex = 7;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(187, 89);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(111, 30);
			this.button_1.TabIndex = 6;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(441, 130);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.numericUpDown_0);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(9113);
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(9134);
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000212 RID: 530
		[CompilerGenerated]
		private Delegate2 delegate2_0;

		// Token: 0x04000213 RID: 531
		private Enum4 enum4_0;

		// Token: 0x04000214 RID: 532
		private IContainer icontainer_0;

		// Token: 0x04000215 RID: 533
		private Label label_0;

		// Token: 0x04000216 RID: 534
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000217 RID: 535
		private Button button_0;

		// Token: 0x04000218 RID: 536
		private Button button_1;
	}
}
