﻿using System;
using System.Drawing;
using ns26;
using ns9;

namespace ns25
{
	// Token: 0x02000091 RID: 145
	internal sealed class Class51 : Class50
	{
		// Token: 0x060004C1 RID: 1217 RVA: 0x00004189 File Offset: 0x00002389
		public Class51()
		{
			base.NormalImage = Class375.check;
			base.Image = base.NormalImage;
			base.Size = new Size(35, 35);
		}

		// Token: 0x060004C2 RID: 1218 RVA: 0x000041B9 File Offset: 0x000023B9
		protected override void Class50_Click(object sender, EventArgs e)
		{
		}
	}
}
