﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using ns18;

namespace ns1
{
	// Token: 0x0200036A RID: 874
	internal sealed class Class473
	{
		// Token: 0x1700063A RID: 1594
		// (get) Token: 0x06002488 RID: 9352 RVA: 0x000FE25C File Offset: 0x000FC45C
		// (set) Token: 0x06002489 RID: 9353 RVA: 0x0000E3D8 File Offset: 0x0000C5D8
		public List<DateTime> DayList { get; private set; }

		// Token: 0x1700063B RID: 1595
		// (get) Token: 0x0600248A RID: 9354 RVA: 0x000FE274 File Offset: 0x000FC474
		// (set) Token: 0x0600248B RID: 9355 RVA: 0x0000E3E3 File Offset: 0x0000C5E3
		public int Idx { get; private set; }

		// Token: 0x1700063C RID: 1596
		// (get) Token: 0x0600248C RID: 9356 RVA: 0x000FE28C File Offset: 0x000FC48C
		// (set) Token: 0x0600248D RID: 9357 RVA: 0x0000E3EE File Offset: 0x0000C5EE
		public bool OK { get; private set; }

		// Token: 0x0600248E RID: 9358 RVA: 0x0000E3F9 File Offset: 0x0000C5F9
		public void method_0(bool bool_1)
		{
			this.OK = bool_1;
		}

		// Token: 0x0600248F RID: 9359 RVA: 0x0000E404 File Offset: 0x0000C604
		public Class473(DateTime dateTime_1)
		{
			this.DayList = new List<DateTime>();
			this.dateTime_0 = dateTime_1;
			this.method_1();
		}

		// Token: 0x06002490 RID: 9360 RVA: 0x0000E426 File Offset: 0x0000C626
		private void method_1()
		{
			this.DayList.Clear();
			this.Idx = -1;
			this.OK = false;
		}

		// Token: 0x1700063D RID: 1597
		// (get) Token: 0x06002491 RID: 9361 RVA: 0x000FE2A4 File Offset: 0x000FC4A4
		public DateTime CurrDay
		{
			get
			{
				DateTime result;
				if (this.Idx < this.DayList.Count)
				{
					result = this.DayList[this.Idx];
				}
				else
				{
					result = this.DayList[this.DayList.Count - 1];
				}
				return result;
			}
		}

		// Token: 0x06002492 RID: 9362 RVA: 0x000FE2F4 File Offset: 0x000FC4F4
		public void method_2(DateTime dateTime_1)
		{
			this.OK = false;
			this.Idx = this.DayList.IndexOf(dateTime_1);
			if (this.Idx == -1)
			{
				throw new Exception(string.Format(Class521.smethod_0(108517), dateTime_1));
			}
		}

		// Token: 0x06002493 RID: 9363 RVA: 0x000FE340 File Offset: 0x000FC540
		public bool method_3(DateTime dateTime_1)
		{
			this.method_1();
			DateTime dateTime = this.dateTime_0;
			bool flag = false;
			DateTime dateTime2 = dateTime_1.Date;
			while (dateTime2 <= dateTime.Date)
			{
				if (dateTime2.DayOfWeek != DayOfWeek.Saturday && dateTime2.DayOfWeek != DayOfWeek.Sunday)
				{
					this.DayList.Add(dateTime2);
					flag = true;
				}
				dateTime2 = dateTime2.AddDays(1.0);
			}
			if (flag)
			{
				this.Idx = 0;
			}
			return flag;
		}

		// Token: 0x06002494 RID: 9364 RVA: 0x000FE3B8 File Offset: 0x000FC5B8
		public void method_4()
		{
			if (!this.OK)
			{
				if (this.Idx < this.DayList.Count)
				{
					int idx = this.Idx;
					this.Idx = idx + 1;
				}
				if (this.Idx >= this.DayList.Count)
				{
					this.OK = true;
				}
			}
		}

		// Token: 0x040011A8 RID: 4520
		[CompilerGenerated]
		private List<DateTime> list_0;

		// Token: 0x040011A9 RID: 4521
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040011AA RID: 4522
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040011AB RID: 4523
		private DateTime dateTime_0;
	}
}
