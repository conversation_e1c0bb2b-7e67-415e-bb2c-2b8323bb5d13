﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns26;
using TEx;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace ns9
{
	// Token: 0x0200024B RID: 587
	internal sealed class Control9 : UserControl
	{
		// Token: 0x14000090 RID: 144
		// (add) Token: 0x060018F7 RID: 6391 RVA: 0x000ACF00 File Offset: 0x000AB100
		// (remove) Token: 0x060018F8 RID: 6392 RVA: 0x000ACF38 File Offset: 0x000AB138
		public event EventHandler FieldChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060018F9 RID: 6393 RVA: 0x000ACF70 File Offset: 0x000AB170
		protected void method_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x14000091 RID: 145
		// (add) Token: 0x060018FA RID: 6394 RVA: 0x000ACF98 File Offset: 0x000AB198
		// (remove) Token: 0x060018FB RID: 6395 RVA: 0x000ACFD0 File Offset: 0x000AB1D0
		public event EventHandler FieldsChkNoChange
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060018FC RID: 6396 RVA: 0x000AD008 File Offset: 0x000AB208
		protected void method_1()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x060018FD RID: 6397 RVA: 0x0000A494 File Offset: 0x00008694
		public Control9() : this(false)
		{
		}

		// Token: 0x060018FE RID: 6398 RVA: 0x000AD030 File Offset: 0x000AB230
		public Control9(bool bool_5)
		{
			this.method_25();
			this.listBox_0.SelectedIndexChanged += this.listBox_0_SelectedIndexChanged;
			this.checkBox_0.CheckStateChanged += this.checkBox_0_CheckStateChanged;
			this.textBox_0.KeyPress += this.textBox_4_KeyPress;
			this.textBox_1.KeyPress += this.textBox_4_KeyPress;
			this.textBox_5.KeyPress += this.textBox_4_KeyPress;
			this.textBox_7.KeyPress += this.textBox_4_KeyPress;
			this.textBox_2.KeyPress += this.textBox_4_KeyPress;
			this.textBox_3.KeyPress += this.textBox_4_KeyPress;
			this.textBox_4.KeyPress += this.textBox_4_KeyPress;
			this.textBox_0.TextChanged += this.textBox_0_TextChanged;
			this.textBox_1.TextChanged += this.textBox_1_TextChanged;
			this.textBox_5.TextChanged += this.textBox_5_TextChanged;
			this.textBox_7.TextChanged += this.textBox_7_TextChanged;
			this.textBox_2.TextChanged += this.textBox_2_TextChanged;
			this.textBox_3.TextChanged += this.textBox_3_TextChanged;
			this.textBox_4.TextChanged += this.textBox_4_TextChanged;
			this.textBox_0.Enter += this.textBox_0_Enter;
			this.textBox_1.Enter += this.textBox_1_Enter;
			this.textBox_5.Enter += this.textBox_5_Enter;
			this.textBox_7.Enter += this.textBox_7_Enter;
			this.textBox_2.Enter += this.textBox_2_Enter;
			this.textBox_3.Enter += this.textBox_3_Enter;
			this.textBox_4.Enter += this.textBox_4_Enter;
			this.textBox_4.Leave += this.textBox_4_Leave;
			this.textBox_0.Leave += this.textBox_0_Leave;
			this.textBox_1.Leave += this.textBox_1_Leave;
			this.textBox_7.Leave += this.textBox_7_Leave;
			this.textBox_5.Leave += this.textBox_5_Leave;
			this.textBox_2.Leave += this.textBox_2_Leave;
			this.textBox_3.Leave += this.textBox_3_Leave;
			this.list_2 = new List<TradingSymbol>();
			this.list_1 = new List<AcctSymbol>();
			this.bool_1 = false;
			this.bool_2 = bool_5;
		}

		// Token: 0x060018FF RID: 6399 RVA: 0x0000A49D File Offset: 0x0000869D
		private void Control9_Load(object sender, EventArgs e)
		{
			bool ifShowCurrSymbOnStartup = this.IfShowCurrSymbOnStartup;
		}

		// Token: 0x17000428 RID: 1064
		// (get) Token: 0x06001900 RID: 6400 RVA: 0x000AD324 File Offset: 0x000AB524
		// (set) Token: 0x06001901 RID: 6401 RVA: 0x000AD33C File Offset: 0x000AB53C
		public List<TradingSymbol> DataSource
		{
			get
			{
				return this.list_0;
			}
			set
			{
				if ((value == null && this.list_0 != null) || (this.list_0 == null && value != null) || this.list_0 != value)
				{
					this.list_0 = value;
					if (this.listBox_0.DisplayMember != Class521.smethod_0(66125))
					{
						this.listBox_0.DisplayMember = Class521.smethod_0(66125);
					}
					this.listBox_0.DataSource = this.list_0;
				}
			}
		}

		// Token: 0x17000429 RID: 1065
		// (get) Token: 0x06001902 RID: 6402 RVA: 0x000AD3B4 File Offset: 0x000AB5B4
		// (set) Token: 0x06001903 RID: 6403 RVA: 0x0000A4A8 File Offset: 0x000086A8
		public TradingSymbol LastSymblInEdit
		{
			get
			{
				return this.tradingSymbol_0;
			}
			set
			{
				this.tradingSymbol_0 = value;
			}
		}

		// Token: 0x1700042A RID: 1066
		// (get) Token: 0x06001904 RID: 6404 RVA: 0x000AD3CC File Offset: 0x000AB5CC
		// (set) Token: 0x06001905 RID: 6405 RVA: 0x0000A4B3 File Offset: 0x000086B3
		public TradingSymbol GlobalTsOfCurrAcctSymb
		{
			get
			{
				return this.tradingSymbol_1;
			}
			set
			{
				this.tradingSymbol_1 = value;
			}
		}

		// Token: 0x1700042B RID: 1067
		// (get) Token: 0x06001906 RID: 6406 RVA: 0x000AD3E4 File Offset: 0x000AB5E4
		public bool IfCurrTsAcctSymb
		{
			get
			{
				return this.tradingSymbol_1 != null;
			}
		}

		// Token: 0x1700042C RID: 1068
		// (get) Token: 0x06001907 RID: 6407 RVA: 0x000AD400 File Offset: 0x000AB600
		// (set) Token: 0x06001908 RID: 6408 RVA: 0x0000A4BE File Offset: 0x000086BE
		public List<TradingSymbol> ChangedGlobalSymblsLst
		{
			get
			{
				return this.list_2;
			}
			set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x1700042D RID: 1069
		// (get) Token: 0x06001909 RID: 6409 RVA: 0x000AD418 File Offset: 0x000AB618
		// (set) Token: 0x0600190A RID: 6410 RVA: 0x0000A4C9 File Offset: 0x000086C9
		public List<AcctSymbol> ChangedCurrAcctSymblsLst
		{
			get
			{
				return this.list_1;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x1700042E RID: 1070
		// (get) Token: 0x0600190B RID: 6411 RVA: 0x000AD430 File Offset: 0x000AB630
		// (set) Token: 0x0600190C RID: 6412 RVA: 0x0000A4D4 File Offset: 0x000086D4
		public bool IsInGlobalSymbls
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
				if (value)
				{
					this.checkBox_0.ThreeState = false;
				}
				else
				{
					this.checkBox_0.ThreeState = true;
				}
				this.method_20();
			}
		}

		// Token: 0x1700042F RID: 1071
		// (get) Token: 0x0600190D RID: 6413 RVA: 0x000AD448 File Offset: 0x000AB648
		public TradingSymbol CurrSelectedSymb
		{
			get
			{
				return this.listBox_0.SelectedItem as TradingSymbol;
			}
		}

		// Token: 0x17000430 RID: 1072
		// (get) Token: 0x0600190E RID: 6414 RVA: 0x000AD46C File Offset: 0x000AB66C
		// (set) Token: 0x0600190F RID: 6415 RVA: 0x000AD488 File Offset: 0x000AB688
		public int CurrSymblLstIndex
		{
			get
			{
				return this.listBox_0.SelectedIndex;
			}
			set
			{
				try
				{
					this.listBox_0.SelectedIndex = value;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x17000431 RID: 1073
		// (get) Token: 0x06001910 RID: 6416 RVA: 0x000AD4BC File Offset: 0x000AB6BC
		// (set) Token: 0x06001911 RID: 6417 RVA: 0x0000A502 File Offset: 0x00008702
		public bool IsBeingClosed
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x17000432 RID: 1074
		// (get) Token: 0x06001912 RID: 6418 RVA: 0x000AD4D4 File Offset: 0x000AB6D4
		// (set) Token: 0x06001913 RID: 6419 RVA: 0x0000A50D File Offset: 0x0000870D
		public bool IfShowCurrSymbOnStartup
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				this.bool_2 = value;
			}
		}

		// Token: 0x17000433 RID: 1075
		// (get) Token: 0x06001914 RID: 6420 RVA: 0x000AD4EC File Offset: 0x000AB6EC
		// (set) Token: 0x06001915 RID: 6421 RVA: 0x0000A518 File Offset: 0x00008718
		public bool IfFocusOnAutoStopOnStartup
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x17000434 RID: 1076
		// (get) Token: 0x06001916 RID: 6422 RVA: 0x000AD504 File Offset: 0x000AB704
		// (set) Token: 0x06001917 RID: 6423 RVA: 0x0000A523 File Offset: 0x00008723
		public bool IfFocusOnAutoLimitOnStartup
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				this.bool_4 = value;
			}
		}

		// Token: 0x06001918 RID: 6424 RVA: 0x000AD51C File Offset: 0x000AB71C
		public void method_2(StkSymbol stkSymbol_0)
		{
			if (this.IfShowCurrSymbOnStartup)
			{
				Control9.Class315 @class = new Control9.Class315();
				@class.stkSymbol_0 = stkSymbol_0;
				if (@class.stkSymbol_0 == null)
				{
					@class.stkSymbol_0 = Base.UI.CurrSymbol;
				}
				if (@class.stkSymbol_0 != null)
				{
					this.listBox_0.SelectedItem = (this.listBox_0.DataSource as List<TradingSymbol>).Single(new Func<TradingSymbol, bool>(@class.method_0));
				}
			}
		}

		// Token: 0x06001919 RID: 6425 RVA: 0x0000A52E File Offset: 0x0000872E
		public void method_3()
		{
			if (this.IfShowCurrSymbOnStartup)
			{
				if (this.IfFocusOnAutoStopOnStartup)
				{
					this.textBox_0.Focus();
				}
				else if (this.IfFocusOnAutoLimitOnStartup)
				{
					this.textBox_1.Focus();
				}
			}
		}

		// Token: 0x0600191A RID: 6426 RVA: 0x000AD588 File Offset: 0x000AB788
		private bool method_4()
		{
			return this.method_5(false);
		}

		// Token: 0x0600191B RID: 6427 RVA: 0x000AD5A0 File Offset: 0x000AB7A0
		private bool method_5(bool bool_5)
		{
			TradingSymbol lastSymblInEdit = this.LastSymblInEdit;
			bool result;
			if (lastSymblInEdit != null)
			{
				bool flag = false;
				object obj = this.method_6(this.textBox_0, lastSymblInEdit.AutoStopLossPoints, ref flag);
				object obj2 = this.method_6(this.textBox_1, lastSymblInEdit.AutoLimitTakePoints, ref flag);
				object obj3 = this.method_6(this.textBox_7, lastSymblInEdit.DefaultUnits, ref flag);
				object obj4 = this.method_6(this.textBox_3, (lastSymblInEdit.FeeRate != null) ? (lastSymblInEdit.FeeRate * 1000) : null, ref flag);
				if (obj4 != null)
				{
					obj4 = (decimal)obj4 / 1000m;
				}
				object obj5 = this.method_6(this.textBox_2, lastSymblInEdit.FeePerUnit, ref flag);
				object obj6 = this.method_6(this.textBox_5, lastSymblInEdit.AvgSlipg, ref flag);
				object obj7 = this.method_6(this.textBox_4, (lastSymblInEdit.MarginRate != null) ? (lastSymblInEdit.MarginRate * 100) : null, ref flag);
				if (obj7 != null)
				{
					obj7 = (decimal)obj7 / 100m;
				}
				bool? flag2;
				if (this.checkBox_0.CheckState == CheckState.Indeterminate)
				{
					flag2 = null;
				}
				else
				{
					flag2 = new bool?(this.checkBox_0.Checked);
				}
				bool? flag3 = flag2;
				bool? isOneSideFee = lastSymblInEdit.IsOneSideFee;
				if (!(flag3.GetValueOrDefault() == isOneSideFee.GetValueOrDefault() & flag3 != null == (isOneSideFee != null)))
				{
					flag = true;
				}
				if (flag && !bool_5)
				{
					Control9.Class316 @class = new Control9.Class316();
					@class.tradingSymbol_0 = new TradingSymbol();
					@class.tradingSymbol_0.ID = lastSymblInEdit.ID;
					@class.tradingSymbol_0.ENName = lastSymblInEdit.ENName;
					@class.tradingSymbol_0.CNName = lastSymblInEdit.CNName;
					@class.tradingSymbol_0.Code = lastSymblInEdit.Code;
					@class.tradingSymbol_0.ExchangeID = lastSymblInEdit.ExchangeID;
					@class.tradingSymbol_0.FeeType = lastSymblInEdit.FeeType;
					@class.tradingSymbol_0.TonsPerUnit = lastSymblInEdit.TonsPerUnit;
					@class.tradingSymbol_0.LeastPriceVar = lastSymblInEdit.LeastPriceVar;
					@class.tradingSymbol_0.Type = lastSymblInEdit.Type;
					if (obj == null)
					{
						@class.tradingSymbol_0.AutoStopLossPoints = null;
					}
					else
					{
						@class.tradingSymbol_0.AutoStopLossPoints = new decimal?(Convert.ToDecimal(obj));
					}
					if (obj2 == null)
					{
						@class.tradingSymbol_0.AutoLimitTakePoints = null;
					}
					else
					{
						@class.tradingSymbol_0.AutoLimitTakePoints = new decimal?(Convert.ToDecimal(obj2));
					}
					if (obj3 == null)
					{
						@class.tradingSymbol_0.DefaultUnits = null;
					}
					else
					{
						@class.tradingSymbol_0.DefaultUnits = new int?(Convert.ToInt32(obj3));
					}
					if (obj4 == null)
					{
						@class.tradingSymbol_0.FeeRate = null;
					}
					else
					{
						@class.tradingSymbol_0.FeeRate = new decimal?(Convert.ToDecimal(obj4));
					}
					if (obj5 == null)
					{
						@class.tradingSymbol_0.FeePerUnit = null;
					}
					else
					{
						@class.tradingSymbol_0.FeePerUnit = new decimal?(Convert.ToDecimal(obj5));
					}
					if (obj6 == null)
					{
						@class.tradingSymbol_0.AvgSlipg = null;
					}
					else
					{
						@class.tradingSymbol_0.AvgSlipg = new decimal?(Convert.ToDecimal(obj6));
					}
					if (obj7 == null)
					{
						@class.tradingSymbol_0.MarginRate = null;
					}
					else
					{
						@class.tradingSymbol_0.MarginRate = new decimal?(Convert.ToDecimal(obj7));
					}
					@class.tradingSymbol_0.IsOneSideFee = flag2;
					if (lastSymblInEdit is AcctSymbol)
					{
						Control9.Class317 class2 = new Control9.Class317();
						class2.acctSymbol_0 = new AcctSymbol();
						class2.acctSymbol_0.method_0(@class.tradingSymbol_0);
						class2.acctSymbol_0.AcctID = ((AcctSymbol)lastSymblInEdit).AcctID;
						class2.acctSymbol_0.IfAutoLimitTake = ((AcctSymbol)lastSymblInEdit).IfAutoLimitTake;
						class2.acctSymbol_0.IfAutoStopLoss = ((AcctSymbol)lastSymblInEdit).IfAutoStopLoss;
						try
						{
							this.ChangedCurrAcctSymblsLst.RemoveAll(new Predicate<AcctSymbol>(class2.method_0));
						}
						catch
						{
						}
						this.ChangedCurrAcctSymblsLst.Add(class2.acctSymbol_0);
					}
					else
					{
						try
						{
							this.ChangedGlobalSymblsLst.RemoveAll(new Predicate<TradingSymbol>(@class.method_0));
						}
						catch
						{
						}
						this.ChangedGlobalSymblsLst.Add(@class.tradingSymbol_0);
					}
					result = true;
				}
				else
				{
					result = flag;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600191C RID: 6428 RVA: 0x000ADAF4 File Offset: 0x000ABCF4
		private object method_6(TextBox textBox_12, object object_0, ref bool bool_5)
		{
			object result;
			if (!textBox_12.Enabled)
			{
				result = object_0;
			}
			else
			{
				bool flag = false;
				decimal? num = null;
				if (!string.IsNullOrEmpty(textBox_12.Text.Trim()))
				{
					if (textBox_12.ForeColor != Color.Gray)
					{
						object result2;
						try
						{
							num = new decimal?(Convert.ToDecimal(textBox_12.Text));
							if (object_0 != null)
							{
								object_0 = Convert.ToDecimal(object_0);
								if (num.Value != (decimal)object_0)
								{
									flag = true;
								}
							}
							else
							{
								flag = true;
							}
							goto IL_88;
						}
						catch
						{
							result2 = object_0;
						}
						return result2;
					}
				}
				else if (object_0 != null)
				{
					flag = true;
				}
				IL_88:
				if (!bool_5 && flag)
				{
					bool_5 = true;
				}
				result = num;
			}
			return result;
		}

		// Token: 0x0600191D RID: 6429 RVA: 0x000ADBB0 File Offset: 0x000ABDB0
		private void checkBox_0_CheckStateChanged(object sender, EventArgs e)
		{
			TradingSymbol tradingSymbol = this.listBox_0.SelectedItem as TradingSymbol;
			bool flag = false;
			if (tradingSymbol != null)
			{
				if (this.checkBox_0.CheckState == CheckState.Indeterminate)
				{
					if (tradingSymbol.IsOneSideFee != null)
					{
						flag = true;
					}
				}
				else if (tradingSymbol.IsOneSideFee == null || tradingSymbol.IsOneSideFee.Value != this.checkBox_0.Checked)
				{
					flag = true;
				}
			}
			if (flag)
			{
				this.method_0();
			}
			else
			{
				this.method_11();
			}
		}

		// Token: 0x0600191E RID: 6430 RVA: 0x000ADC38 File Offset: 0x000ABE38
		private void textBox_4_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
			{
				e.Handled = true;
			}
			if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600191F RID: 6431 RVA: 0x0000A564 File Offset: 0x00008764
		private void textBox_0_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_0, this.CurrSelectedSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001920 RID: 6432 RVA: 0x0000A59B File Offset: 0x0000879B
		private void textBox_0_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints, this.CurrSelectedSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001921 RID: 6433 RVA: 0x0000A5C9 File Offset: 0x000087C9
		private void textBox_0_Leave(object sender, EventArgs e)
		{
			this.method_12(this.textBox_0);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001922 RID: 6434 RVA: 0x0000A5F9 File Offset: 0x000087F9
		private void textBox_1_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_1, this.CurrSelectedSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001923 RID: 6435 RVA: 0x0000A630 File Offset: 0x00008830
		private void textBox_1_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints, this.CurrSelectedSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001924 RID: 6436 RVA: 0x0000A65E File Offset: 0x0000885E
		private void textBox_1_Leave(object sender, EventArgs e)
		{
			this.method_12(sender as TextBox);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001925 RID: 6437 RVA: 0x000ADC98 File Offset: 0x000ABE98
		private void textBox_3_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_8(this.textBox_3, this.CurrSelectedSymb.FeeRate, new decimal?(1000m));
			}
		}

		// Token: 0x06001926 RID: 6438 RVA: 0x0000A68E File Offset: 0x0000888E
		private void textBox_3_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeeRate, this.CurrSelectedSymb.FeeRate);
			}
		}

		// Token: 0x06001927 RID: 6439 RVA: 0x0000A6BC File Offset: 0x000088BC
		private void textBox_3_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_3);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_15(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeeRate, new decimal?(1000m));
			}
		}

		// Token: 0x06001928 RID: 6440 RVA: 0x0000A6FA File Offset: 0x000088FA
		private void textBox_2_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_2, this.CurrSelectedSymb.FeePerUnit);
			}
		}

		// Token: 0x06001929 RID: 6441 RVA: 0x0000A731 File Offset: 0x00008931
		private void textBox_2_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeePerUnit, this.CurrSelectedSymb.FeePerUnit);
			}
		}

		// Token: 0x0600192A RID: 6442 RVA: 0x0000A75F File Offset: 0x0000895F
		private void textBox_2_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_2);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeePerUnit);
			}
		}

		// Token: 0x0600192B RID: 6443 RVA: 0x000ADCEC File Offset: 0x000ABEEC
		private void textBox_4_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_8(this.textBox_4, this.CurrSelectedSymb.MarginRate, new decimal?(100m));
			}
		}

		// Token: 0x0600192C RID: 6444 RVA: 0x0000A78E File Offset: 0x0000898E
		private void textBox_4_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.MarginRate, this.CurrSelectedSymb.MarginRate);
			}
		}

		// Token: 0x0600192D RID: 6445 RVA: 0x000ADD3C File Offset: 0x000ABF3C
		private void textBox_4_Leave(object sender, EventArgs e)
		{
			TextBox textBox = sender as TextBox;
			string value = textBox.Text.Trim();
			if (!string.IsNullOrEmpty(value) && !this.bool_1 && Convert.ToDecimal(value) > 100m)
			{
				MessageBox.Show(Class521.smethod_0(66142), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				textBox.Focus();
			}
			else
			{
				this.method_12(this.textBox_4);
				if (this.IfCurrTsAcctSymb)
				{
					this.method_15(sender as TextBox, this.GlobalTsOfCurrAcctSymb.MarginRate, new decimal?(100m));
				}
			}
		}

		// Token: 0x0600192E RID: 6446 RVA: 0x0000A7BC File Offset: 0x000089BC
		private void textBox_5_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_5, this.CurrSelectedSymb.AvgSlipg);
			}
		}

		// Token: 0x0600192F RID: 6447 RVA: 0x0000A7F3 File Offset: 0x000089F3
		private void textBox_5_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AvgSlipg, this.CurrSelectedSymb.AvgSlipg);
			}
		}

		// Token: 0x06001930 RID: 6448 RVA: 0x0000A821 File Offset: 0x00008A21
		private void textBox_5_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_5);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AvgSlipg);
			}
		}

		// Token: 0x06001931 RID: 6449 RVA: 0x0000A850 File Offset: 0x00008A50
		private void textBox_7_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_7, this.CurrSelectedSymb.DefaultUnits);
			}
		}

		// Token: 0x06001932 RID: 6450 RVA: 0x000ADDE0 File Offset: 0x000ABFE0
		private void textBox_7_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				TextBox textBox_ = sender as TextBox;
				int? defaultUnits = this.GlobalTsOfCurrAcctSymb.DefaultUnits;
				decimal? nullable_ = (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null;
				defaultUnits = this.CurrSelectedSymb.DefaultUnits;
				this.method_16(textBox_, nullable_, (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null);
			}
		}

		// Token: 0x06001933 RID: 6451 RVA: 0x000ADE68 File Offset: 0x000AC068
		private void textBox_7_Leave(object sender, EventArgs e)
		{
			this.method_12(this.textBox_7);
			if (this.IfCurrTsAcctSymb)
			{
				TextBox textBox_ = sender as TextBox;
				int? defaultUnits = this.GlobalTsOfCurrAcctSymb.DefaultUnits;
				this.method_14(textBox_, (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null);
			}
		}

		// Token: 0x06001934 RID: 6452 RVA: 0x000ADECC File Offset: 0x000AC0CC
		private void method_7(TextBox textBox_12, object object_0)
		{
			this.method_8(textBox_12, object_0, null);
		}

		// Token: 0x06001935 RID: 6453 RVA: 0x0000A887 File Offset: 0x00008A87
		private void method_8(TextBox textBox_12, object object_0, decimal? nullable_0)
		{
			if (this.method_10(textBox_12, object_0, nullable_0))
			{
				this.method_0();
			}
			else
			{
				this.method_11();
			}
		}

		// Token: 0x06001936 RID: 6454 RVA: 0x000ADEEC File Offset: 0x000AC0EC
		private bool method_9(TextBox textBox_12, object object_0)
		{
			return this.method_10(textBox_12, object_0, null);
		}

		// Token: 0x06001937 RID: 6455 RVA: 0x000ADF10 File Offset: 0x000AC110
		private bool method_10(TextBox textBox_12, object object_0, decimal? nullable_0)
		{
			bool result = false;
			string value = textBox_12.Text.Trim();
			if (string.IsNullOrEmpty(value))
			{
				if (object_0 != null)
				{
					result = true;
				}
			}
			else
			{
				try
				{
					decimal d = Convert.ToDecimal(value);
					if (nullable_0 != null)
					{
						d /= nullable_0.Value;
					}
					if (object_0 != null)
					{
						if (d != Convert.ToDecimal(object_0))
						{
							result = true;
						}
					}
					else
					{
						result = true;
					}
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x06001938 RID: 6456 RVA: 0x000ADF8C File Offset: 0x000AC18C
		private void method_11()
		{
			if (!this.method_5(true))
			{
				if ((this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count >= 1) || (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count >= 1))
				{
					if (this.IfCurrTsAcctSymb)
					{
						if (this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count > 0)
						{
							Control9.Class318 @class = new Control9.Class318();
							@class.acctSymbol_0 = (this.CurrSelectedSymb as AcctSymbol);
							if (this.ChangedCurrAcctSymblsLst.Where(new Func<AcctSymbol, bool>(@class.method_0)).Any<AcctSymbol>())
							{
								this.ChangedCurrAcctSymblsLst.RemoveAll(new Predicate<AcctSymbol>(@class.method_1));
								this.method_1();
							}
						}
					}
					else if (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count > 0 && this.ChangedGlobalSymblsLst.Where(new Func<TradingSymbol, bool>(this.method_26)).Any<TradingSymbol>())
					{
						this.ChangedGlobalSymblsLst.RemoveAll(new Predicate<TradingSymbol>(this.method_27));
						this.method_1();
					}
				}
				else
				{
					this.method_1();
				}
			}
		}

		// Token: 0x06001939 RID: 6457 RVA: 0x000AE0B0 File Offset: 0x000AC2B0
		private bool method_12(TextBox textBox_12)
		{
			string value = textBox_12.Text.Trim();
			bool result;
			if (!string.IsNullOrEmpty(value) && !this.bool_1 && Convert.ToDecimal(value) == 0m)
			{
				MessageBox.Show(Class521.smethod_0(66191), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				textBox_12.Focus();
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600193A RID: 6458 RVA: 0x000AE118 File Offset: 0x000AC318
		private void method_13(TextBox textBox_12)
		{
			string value = textBox_12.Text.Trim();
			if (!string.IsNullOrEmpty(value) && Convert.ToDecimal(value) == 0m)
			{
				textBox_12.Text = Class521.smethod_0(2841);
			}
		}

		// Token: 0x0600193B RID: 6459 RVA: 0x000AE160 File Offset: 0x000AC360
		private void method_14(TextBox textBox_12, decimal? nullable_0)
		{
			this.method_15(textBox_12, nullable_0, null);
		}

		// Token: 0x0600193C RID: 6460 RVA: 0x000AE180 File Offset: 0x000AC380
		private void method_15(TextBox textBox_12, decimal? nullable_0, decimal? nullable_1)
		{
			if (this.IfCurrTsAcctSymb)
			{
				if (string.IsNullOrEmpty(textBox_12.Text.Trim()))
				{
					try
					{
						if (nullable_0 != null)
						{
							textBox_12.ForeColor = Color.Gray;
							if (nullable_1 != null)
							{
								nullable_0 *= nullable_1;
							}
							textBox_12.Text = Utility.GetStringWithoutEndZero(new decimal?(nullable_0.Value));
						}
						else
						{
							textBox_12.ForeColor = Color.Black;
							textBox_12.Text = string.Empty;
						}
						return;
					}
					catch
					{
						throw;
					}
				}
				textBox_12.ForeColor = Color.Black;
			}
		}

		// Token: 0x0600193D RID: 6461 RVA: 0x000AE258 File Offset: 0x000AC458
		private void method_16(TextBox textBox_12, decimal? nullable_0, decimal? nullable_1)
		{
			if (this.IfCurrTsAcctSymb && nullable_0 != null && nullable_1 == null && textBox_12.ForeColor == Color.Gray)
			{
				textBox_12.Text = string.Empty;
				textBox_12.ForeColor = default(Color);
			}
		}

		// Token: 0x0600193E RID: 6462 RVA: 0x000AE2B0 File Offset: 0x000AC4B0
		private void method_17(TextBox textBox_12, decimal? nullable_0, decimal? nullable_1)
		{
			this.method_18(textBox_12, nullable_0, nullable_1, null);
		}

		// Token: 0x0600193F RID: 6463 RVA: 0x000AE2D4 File Offset: 0x000AC4D4
		private void method_18(TextBox textBox_12, decimal? nullable_0, decimal? nullable_1, decimal? nullable_2)
		{
			if (this.IfCurrTsAcctSymb && nullable_0 == null)
			{
				if (nullable_1 != null)
				{
					if (nullable_2 != null)
					{
						nullable_1 *= nullable_2;
					}
					textBox_12.Text = Utility.GetStringWithoutEndZero(nullable_1);
					textBox_12.ForeColor = Color.Gray;
				}
				else
				{
					textBox_12.Text = string.Empty;
					textBox_12.ForeColor = default(Color);
				}
			}
			else
			{
				if (nullable_0 != null)
				{
					if (nullable_2 != null)
					{
						nullable_0 *= nullable_2;
					}
					textBox_12.Text = Utility.GetStringWithoutEndZero(nullable_0);
				}
				else
				{
					textBox_12.Text = string.Empty;
				}
				textBox_12.ForeColor = default(Color);
			}
		}

		// Token: 0x06001940 RID: 6464 RVA: 0x000AE3F4 File Offset: 0x000AC5F4
		private string method_19(decimal? nullable_0)
		{
			string result;
			if (nullable_0 != null)
			{
				string text = nullable_0.Value.ToString();
				if (nullable_0.Value < 1m)
				{
					text = Utility.GetStringWithoutEndZero(new decimal?(nullable_0.Value));
				}
				result = text;
			}
			else
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001941 RID: 6465 RVA: 0x000AE44C File Offset: 0x000AC64C
		private void method_20()
		{
			TradingSymbol tradingSymbol = this.listBox_0.SelectedItem as TradingSymbol;
			if (tradingSymbol != null && tradingSymbol.IsOneSideFee != null)
			{
				this.checkBox_0.Checked = tradingSymbol.IsOneSideFee.Value;
			}
			else if (!this.IsInGlobalSymbls)
			{
				this.checkBox_0.CheckState = CheckState.Indeterminate;
			}
		}

		// Token: 0x06001942 RID: 6466 RVA: 0x000AE4B0 File Offset: 0x000AC6B0
		public void method_21()
		{
			this.method_4();
			if (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count > 0)
			{
				foreach (TradingSymbol tradingSymbol in this.ChangedGlobalSymblsLst)
				{
					TradingSymbol tradingSymbol_ = SymbMgr.smethod_37(tradingSymbol.ID);
					this.method_22(tradingSymbol_, tradingSymbol, false);
				}
				SymbMgr.smethod_33();
				this.ChangedGlobalSymblsLst = new List<TradingSymbol>();
			}
			if (this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count > 0)
			{
				foreach (AcctSymbol acctSymbol in this.ChangedCurrAcctSymblsLst)
				{
					AcctSymbol tradingSymbol_2 = Base.Acct.smethod_30(acctSymbol.ID);
					this.method_22(tradingSymbol_2, acctSymbol, true);
				}
				Base.Acct.smethod_38();
				this.ChangedCurrAcctSymblsLst = new List<AcctSymbol>();
			}
			Control9.smethod_0();
		}

		// Token: 0x06001943 RID: 6467 RVA: 0x000AE5C4 File Offset: 0x000AC7C4
		private void method_22(TradingSymbol tradingSymbol_2, TradingSymbol tradingSymbol_3, bool bool_5)
		{
			if (tradingSymbol_3.AutoStopLossPoints == null || tradingSymbol_3.AutoStopLossPoints.Value > 0m)
			{
				tradingSymbol_2.AutoStopLossPoints = tradingSymbol_3.AutoStopLossPoints;
			}
			if (tradingSymbol_3.AutoLimitTakePoints != null)
			{
				decimal? num = tradingSymbol_3.AutoLimitTakePoints;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() > d & num != null))
				{
					goto IL_78;
				}
			}
			tradingSymbol_2.AutoLimitTakePoints = tradingSymbol_3.AutoLimitTakePoints;
			IL_78:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.FeeRate == null)
				{
					goto IL_C0;
				}
				decimal? num = tradingSymbol_3.FeeRate;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_C0;
				}
			}
			tradingSymbol_2.FeeRate = tradingSymbol_3.FeeRate;
			IL_C0:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.FeePerUnit == null)
				{
					goto IL_108;
				}
				decimal? num = tradingSymbol_3.FeePerUnit;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_108;
				}
			}
			tradingSymbol_2.FeePerUnit = tradingSymbol_3.FeePerUnit;
			IL_108:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.MarginRate == null)
				{
					goto IL_150;
				}
				decimal? num = tradingSymbol_3.MarginRate;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() > d & num != null))
				{
					goto IL_150;
				}
			}
			tradingSymbol_2.MarginRate = tradingSymbol_3.MarginRate;
			IL_150:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.AvgSlipg == null)
				{
					goto IL_198;
				}
				decimal? num = tradingSymbol_3.AvgSlipg;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_198;
				}
			}
			tradingSymbol_2.AvgSlipg = tradingSymbol_3.AvgSlipg;
			IL_198:
			if (tradingSymbol_3.DefaultUnits == null || tradingSymbol_3.DefaultUnits.Value >= 1)
			{
				tradingSymbol_2.DefaultUnits = tradingSymbol_3.DefaultUnits;
			}
			if (tradingSymbol_3.IsOneSideFee != null)
			{
				tradingSymbol_2.IsOneSideFee = tradingSymbol_3.IsOneSideFee;
			}
			else if (tradingSymbol_2 is AcctSymbol && bool_5)
			{
				(tradingSymbol_2 as AcctSymbol).IsOneSideFee = null;
			}
		}

		// Token: 0x06001944 RID: 6468 RVA: 0x000AE7D8 File Offset: 0x000AC9D8
		public static void smethod_0()
		{
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					symbDataSet.method_74();
				}
			}
		}

		// Token: 0x06001945 RID: 6469 RVA: 0x000AE834 File Offset: 0x000ACA34
		private void listBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			Control9.Class319 @class = new Control9.Class319();
			this.method_4();
			@class.tradingSymbol_0 = (this.listBox_0.SelectedItem as TradingSymbol);
			this.LastSymblInEdit = @class.tradingSymbol_0;
			if (@class.tradingSymbol_0 is AcctSymbol)
			{
				try
				{
					if (this.ChangedGlobalSymblsLst != null)
					{
						AcctSymbol acctSymbol = this.ChangedCurrAcctSymblsLst.SingleOrDefault(new Func<AcctSymbol, bool>(@class.method_0));
						if (acctSymbol != null)
						{
							@class.tradingSymbol_0 = acctSymbol;
						}
					}
				}
				catch
				{
				}
				try
				{
					TradingSymbol tradingSymbol = SymbMgr.LocalMstSymbolList.SingleOrDefault(new Func<TradingSymbol, bool>(@class.method_1));
					if (tradingSymbol != null)
					{
						this.GlobalTsOfCurrAcctSymb = tradingSymbol;
					}
					goto IL_C9;
				}
				catch
				{
					throw;
				}
			}
			try
			{
				if (this.ChangedGlobalSymblsLst != null)
				{
					TradingSymbol tradingSymbol2 = this.ChangedGlobalSymblsLst.SingleOrDefault(new Func<TradingSymbol, bool>(@class.method_2));
					if (tradingSymbol2 != null)
					{
						@class.tradingSymbol_0 = tradingSymbol2;
					}
				}
			}
			catch
			{
			}
			this.GlobalTsOfCurrAcctSymb = null;
			IL_C9:
			this.method_17(this.textBox_0, @class.tradingSymbol_0.AutoStopLossPoints, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints : null);
			this.method_17(this.textBox_1, @class.tradingSymbol_0.AutoLimitTakePoints, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints : null);
			this.method_17(this.textBox_5, @class.tradingSymbol_0.AvgSlipg, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AvgSlipg : null);
			TextBox textBox_ = this.textBox_7;
			int? num = @class.tradingSymbol_0.DefaultUnits;
			decimal? nullable_ = (num != null) ? new decimal?(num.GetValueOrDefault()) : null;
			num = (this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.DefaultUnits : null);
			this.method_17(textBox_, nullable_, (num != null) ? new decimal?(num.GetValueOrDefault()) : null);
			this.method_18(this.textBox_4, @class.tradingSymbol_0.MarginRate, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.MarginRate : null, new decimal?(100m));
			if (@class.tradingSymbol_0.FeeType == FeeType.FixedAmountPerUnit)
			{
				this.textBox_3.Enabled = false;
				this.textBox_3.Text = string.Empty;
				this.radioButton_1.Enabled = false;
				this.label_3.Enabled = false;
				this.textBox_2.Enabled = true;
				this.radioButton_0.Enabled = true;
				this.radioButton_0.Checked = true;
				this.label_2.Enabled = true;
				this.method_17(this.textBox_2, @class.tradingSymbol_0.FeePerUnit, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.FeePerUnit : null);
			}
			else
			{
				this.textBox_2.Enabled = false;
				this.textBox_2.Text = string.Empty;
				this.radioButton_0.Enabled = false;
				this.label_2.Enabled = false;
				this.textBox_3.Enabled = true;
				this.radioButton_1.Enabled = true;
				this.radioButton_1.Checked = true;
				this.label_3.Enabled = true;
				this.method_18(this.textBox_3, @class.tradingSymbol_0.FeeRate, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.FeeRate : null, new decimal?(1000m));
			}
			if (@class.tradingSymbol_0 is AcctSymbol)
			{
				this.checkBox_0.ThreeState = true;
			}
			else
			{
				this.checkBox_0.ThreeState = false;
			}
			if (@class.tradingSymbol_0.IsOneSideFee != null)
			{
				if (@class.tradingSymbol_0.IsOneSideFee.Value)
				{
					this.checkBox_0.Checked = true;
				}
				else
				{
					this.checkBox_0.Checked = false;
				}
			}
			else if (this.checkBox_0.ThreeState)
			{
				this.checkBox_0.CheckState = CheckState.Indeterminate;
			}
			this.textBox_8.Text = @class.tradingSymbol_0.AbbrCNName;
			this.textBox_9.Text = @class.tradingSymbol_0.AbbrCode;
			this.textBox_6.Text = ((@class.tradingSymbol_0.LeastPriceVar != null) ? Utility.GetStringWithoutEndZero(new decimal?(@class.tradingSymbol_0.LeastPriceVar.Value)) : string.Empty);
			this.textBox_10.Text = SymbMgr.smethod_1(@class.tradingSymbol_0.ExchangeID).AbbrName_CN;
			this.textBox_11.Text = ((@class.tradingSymbol_0.TonsPerUnit != null) ? @class.tradingSymbol_0.TonsPerUnit.Value.ToString() : string.Empty);
			this.textBox_6.Text = ((@class.tradingSymbol_0.LeastPriceVar != null) ? Utility.GetStringWithoutEndZero(new decimal?(@class.tradingSymbol_0.LeastPriceVar.Value)) : string.Empty);
			if (@class.tradingSymbol_0.ExchangeID != 1 && @class.tradingSymbol_0.ExchangeID != 5)
			{
				if (@class.tradingSymbol_0.ExchangeID != 6)
				{
					this.label_13.Text = Class521.smethod_0(66258);
					this.label_12.Text = Class521.smethod_0(66279);
					goto IL_5B3;
				}
			}
			this.label_13.Text = Class521.smethod_0(66224);
			this.label_12.Text = Class521.smethod_0(66245);
			IL_5B3:
			if (@class.tradingSymbol_0.AbbrCode.ToLower() == Class521.smethod_0(66292))
			{
				this.label_12.Text = Class521.smethod_0(66297);
			}
			else if (@class.tradingSymbol_0.AbbrCode.ToLower() == Class521.smethod_0(66310))
			{
				this.label_12.Text = Class521.smethod_0(66315);
			}
			this.method_20();
		}

		// Token: 0x06001946 RID: 6470 RVA: 0x0000A8A4 File Offset: 0x00008AA4
		public void method_23()
		{
			this.textBox_0.Focus();
		}

		// Token: 0x06001947 RID: 6471 RVA: 0x0000A8B4 File Offset: 0x00008AB4
		public void method_24()
		{
			this.textBox_1.Focus();
		}

		// Token: 0x06001948 RID: 6472 RVA: 0x0000A8C4 File Offset: 0x00008AC4
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001949 RID: 6473 RVA: 0x000AEE94 File Offset: 0x000AD094
		private void method_25()
		{
			this.listBox_0 = new ListBox();
			this.label_0 = new Label();
			this.groupBox_0 = new GroupBox();
			this.checkBox_0 = new CheckBox();
			this.panel_0 = new Panel();
			this.label_2 = new Label();
			this.textBox_2 = new TextBox();
			this.radioButton_0 = new RadioButton();
			this.label_3 = new Label();
			this.textBox_3 = new TextBox();
			this.radioButton_1 = new RadioButton();
			this.label_1 = new Label();
			this.textBox_0 = new TextBox();
			this.textBox_1 = new TextBox();
			this.groupBox_1 = new GroupBox();
			this.label_4 = new Label();
			this.textBox_4 = new TextBox();
			this.label_5 = new Label();
			this.textBox_5 = new TextBox();
			this.label_6 = new Label();
			this.textBox_6 = new TextBox();
			this.label_7 = new Label();
			this.textBox_7 = new TextBox();
			this.label_8 = new Label();
			this.groupBox_2 = new GroupBox();
			this.groupBox_4 = new GroupBox();
			this.label_12 = new Label();
			this.textBox_11 = new TextBox();
			this.label_13 = new Label();
			this.textBox_10 = new TextBox();
			this.label_11 = new Label();
			this.label_9 = new Label();
			this.textBox_8 = new TextBox();
			this.textBox_9 = new TextBox();
			this.label_10 = new Label();
			this.groupBox_3 = new GroupBox();
			this.groupBox_0.SuspendLayout();
			this.panel_0.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			this.groupBox_2.SuspendLayout();
			this.groupBox_4.SuspendLayout();
			this.groupBox_3.SuspendLayout();
			base.SuspendLayout();
			this.listBox_0.BorderStyle = BorderStyle.None;
			this.listBox_0.Font = new Font(Class521.smethod_0(7183), 9f);
			this.listBox_0.FormattingEnabled = true;
			this.listBox_0.ItemHeight = 15;
			this.listBox_0.Location = new Point(18, 30);
			this.listBox_0.Name = Class521.smethod_0(66324);
			this.listBox_0.Size = new Size(160, 300);
			this.listBox_0.TabIndex = 15;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(17, 28);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(105, 15);
			this.label_0.TabIndex = 1;
			this.label_0.Text = Class521.smethod_0(66341);
			this.groupBox_0.Controls.Add(this.checkBox_0);
			this.groupBox_0.Controls.Add(this.panel_0);
			this.groupBox_0.Location = new Point(194, 118);
			this.groupBox_0.Name = Class521.smethod_0(66370);
			this.groupBox_0.Size = new Size(252, 115);
			this.groupBox_0.TabIndex = 2;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(45021);
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(14, 85);
			this.checkBox_0.Name = Class521.smethod_0(66387);
			this.checkBox_0.Size = new Size(89, 19);
			this.checkBox_0.TabIndex = 6;
			this.checkBox_0.Text = Class521.smethod_0(66416);
			this.checkBox_0.ThreeState = true;
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.panel_0.Controls.Add(this.label_2);
			this.panel_0.Controls.Add(this.textBox_2);
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Controls.Add(this.label_3);
			this.panel_0.Controls.Add(this.textBox_3);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Location = new Point(10, 19);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(239, 65);
			this.panel_0.TabIndex = 10;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(205, 38);
			this.label_2.Name = Class521.smethod_0(66433);
			this.label_2.Size = new Size(22, 15);
			this.label_2.TabIndex = 12;
			this.label_2.Text = Class521.smethod_0(3554);
			this.textBox_2.Location = new Point(139, 35);
			this.textBox_2.Name = Class521.smethod_0(66458);
			this.textBox_2.Size = new Size(63, 25);
			this.textBox_2.TabIndex = 5;
			this.textBox_2.TextAlign = HorizontalAlignment.Center;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Location = new Point(4, 36);
			this.radioButton_0.Name = Class521.smethod_0(66479);
			this.radioButton_0.Size = new Size(126, 19);
			this.radioButton_0.TabIndex = 4;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(66504);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.label_3.AutoSize = true;
			this.label_3.Font = new Font(Class521.smethod_0(7183), 8.530189f);
			this.label_3.Location = new Point(208, 10);
			this.label_3.Name = Class521.smethod_0(66533);
			this.label_3.Size = new Size(22, 15);
			this.label_3.TabIndex = 2;
			this.label_3.Text = Class521.smethod_0(66562);
			this.textBox_3.Location = new Point(139, 5);
			this.textBox_3.Name = Class521.smethod_0(66567);
			this.textBox_3.Size = new Size(63, 25);
			this.textBox_3.TabIndex = 3;
			this.textBox_3.TextAlign = HorizontalAlignment.Center;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(4, 7);
			this.radioButton_1.Name = Class521.smethod_0(66592);
			this.radioButton_1.Size = new Size(126, 19);
			this.radioButton_1.TabIndex = 2;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(66617);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(17, 57);
			this.label_1.Name = Class521.smethod_0(7019);
			this.label_1.Size = new Size(105, 15);
			this.label_1.TabIndex = 5;
			this.label_1.Text = Class521.smethod_0(66646);
			this.textBox_0.Location = new Point(140, 24);
			this.textBox_0.Name = Class521.smethod_0(66675);
			this.textBox_0.Size = new Size(78, 25);
			this.textBox_0.TabIndex = 0;
			this.textBox_0.TextAlign = HorizontalAlignment.Center;
			this.textBox_1.Location = new Point(140, 54);
			this.textBox_1.Name = Class521.smethod_0(66700);
			this.textBox_1.Size = new Size(78, 25);
			this.textBox_1.TabIndex = 1;
			this.textBox_1.TextAlign = HorizontalAlignment.Center;
			this.groupBox_1.Controls.Add(this.textBox_1);
			this.groupBox_1.Controls.Add(this.label_0);
			this.groupBox_1.Controls.Add(this.label_1);
			this.groupBox_1.Controls.Add(this.textBox_0);
			this.groupBox_1.Location = new Point(194, 21);
			this.groupBox_1.Name = Class521.smethod_0(66725);
			this.groupBox_1.Size = new Size(252, 90);
			this.groupBox_1.TabIndex = 14;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(23756);
			this.label_4.Location = new Point(17, 26);
			this.label_4.Name = Class521.smethod_0(5827);
			this.label_4.Size = new Size(118, 20);
			this.label_4.TabIndex = 15;
			this.label_4.Text = Class521.smethod_0(66750);
			this.label_4.TextAlign = ContentAlignment.TopRight;
			this.textBox_4.Location = new Point(139, 23);
			this.textBox_4.Name = Class521.smethod_0(66775);
			this.textBox_4.Size = new Size(78, 25);
			this.textBox_4.TabIndex = 12;
			this.textBox_4.TextAlign = HorizontalAlignment.Center;
			this.label_5.AutoSize = true;
			this.label_5.Font = new Font(Class521.smethod_0(7183), 10f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_5.Location = new Point(223, 26);
			this.label_5.Name = Class521.smethod_0(5849);
			this.label_5.Size = new Size(17, 17);
			this.label_5.TabIndex = 17;
			this.label_5.Text = Class521.smethod_0(5356);
			this.textBox_5.Location = new Point(139, 53);
			this.textBox_5.Name = Class521.smethod_0(66800);
			this.textBox_5.Size = new Size(78, 25);
			this.textBox_5.TabIndex = 13;
			this.textBox_5.TextAlign = HorizontalAlignment.Center;
			this.label_6.Location = new Point(17, 56);
			this.label_6.Name = Class521.smethod_0(5893);
			this.label_6.Size = new Size(118, 20);
			this.label_6.TabIndex = 18;
			this.label_6.Text = Class521.smethod_0(66821);
			this.label_6.TextAlign = ContentAlignment.TopRight;
			this.textBox_6.Enabled = false;
			this.textBox_6.Location = new Point(128, 146);
			this.textBox_6.Name = Class521.smethod_0(66842);
			this.textBox_6.Size = new Size(61, 25);
			this.textBox_6.TabIndex = 11;
			this.textBox_6.TextAlign = HorizontalAlignment.Center;
			this.label_7.Location = new Point(5, 149);
			this.label_7.Name = Class521.smethod_0(5915);
			this.label_7.Size = new Size(118, 20);
			this.label_7.TabIndex = 20;
			this.label_7.Text = Class521.smethod_0(66871);
			this.label_7.TextAlign = ContentAlignment.TopRight;
			this.textBox_7.Location = new Point(395, 23);
			this.textBox_7.Name = Class521.smethod_0(66900);
			this.textBox_7.Size = new Size(78, 25);
			this.textBox_7.TabIndex = 14;
			this.textBox_7.TextAlign = HorizontalAlignment.Center;
			this.label_8.Location = new Point(273, 27);
			this.label_8.Name = Class521.smethod_0(40965);
			this.label_8.Size = new Size(118, 20);
			this.label_8.TabIndex = 22;
			this.label_8.Text = Class521.smethod_0(66933);
			this.label_8.TextAlign = ContentAlignment.TopRight;
			this.groupBox_2.Controls.Add(this.groupBox_4);
			this.groupBox_2.Controls.Add(this.groupBox_3);
			this.groupBox_2.Controls.Add(this.listBox_0);
			this.groupBox_2.Controls.Add(this.groupBox_1);
			this.groupBox_2.Controls.Add(this.groupBox_0);
			this.groupBox_2.Dock = DockStyle.Fill;
			this.groupBox_2.Location = new Point(0, 0);
			this.groupBox_2.Name = Class521.smethod_0(66962);
			this.groupBox_2.Size = new Size(722, 348);
			this.groupBox_2.TabIndex = 25;
			this.groupBox_2.TabStop = false;
			this.groupBox_4.Controls.Add(this.label_12);
			this.groupBox_4.Controls.Add(this.textBox_11);
			this.groupBox_4.Controls.Add(this.label_13);
			this.groupBox_4.Controls.Add(this.textBox_10);
			this.groupBox_4.Controls.Add(this.label_11);
			this.groupBox_4.Controls.Add(this.textBox_6);
			this.groupBox_4.Controls.Add(this.label_7);
			this.groupBox_4.Controls.Add(this.label_9);
			this.groupBox_4.Controls.Add(this.textBox_8);
			this.groupBox_4.Controls.Add(this.textBox_9);
			this.groupBox_4.Controls.Add(this.label_10);
			this.groupBox_4.Location = new Point(461, 21);
			this.groupBox_4.Name = Class521.smethod_0(66983);
			this.groupBox_4.Size = new Size(246, 189);
			this.groupBox_4.TabIndex = 29;
			this.groupBox_4.TabStop = false;
			this.groupBox_4.Text = Class521.smethod_0(67008);
			this.label_12.AutoSize = true;
			this.label_12.Location = new Point(195, 120);
			this.label_12.Name = Class521.smethod_0(67025);
			this.label_12.Size = new Size(45, 15);
			this.label_12.TabIndex = 34;
			this.label_12.Text = Class521.smethod_0(66279);
			this.textBox_11.Enabled = false;
			this.textBox_11.Location = new Point(128, 115);
			this.textBox_11.Name = Class521.smethod_0(67058);
			this.textBox_11.Size = new Size(61, 25);
			this.textBox_11.TabIndex = 10;
			this.textBox_11.TextAlign = HorizontalAlignment.Center;
			this.label_13.Location = new Point(5, 120);
			this.label_13.Name = Class521.smethod_0(67087);
			this.label_13.Size = new Size(118, 20);
			this.label_13.TabIndex = 32;
			this.label_13.Text = Class521.smethod_0(66258);
			this.label_13.TextAlign = ContentAlignment.TopRight;
			this.textBox_10.Enabled = false;
			this.textBox_10.Location = new Point(128, 84);
			this.textBox_10.Name = Class521.smethod_0(67112);
			this.textBox_10.Size = new Size(101, 25);
			this.textBox_10.TabIndex = 9;
			this.textBox_10.TextAlign = HorizontalAlignment.Center;
			this.label_11.Location = new Point(5, 89);
			this.label_11.Name = Class521.smethod_0(11267);
			this.label_11.Size = new Size(118, 20);
			this.label_11.TabIndex = 30;
			this.label_11.Text = Class521.smethod_0(58644);
			this.label_11.TextAlign = ContentAlignment.TopRight;
			this.label_9.Location = new Point(5, 29);
			this.label_9.Name = Class521.smethod_0(7268);
			this.label_9.Size = new Size(118, 20);
			this.label_9.TabIndex = 25;
			this.label_9.Text = Class521.smethod_0(67133);
			this.label_9.TextAlign = ContentAlignment.TopRight;
			this.textBox_8.Enabled = false;
			this.textBox_8.Location = new Point(128, 24);
			this.textBox_8.Name = Class521.smethod_0(67154);
			this.textBox_8.Size = new Size(101, 25);
			this.textBox_8.TabIndex = 7;
			this.textBox_8.TextAlign = HorizontalAlignment.Center;
			this.textBox_9.Enabled = false;
			this.textBox_9.Location = new Point(128, 54);
			this.textBox_9.Name = Class521.smethod_0(67179);
			this.textBox_9.Size = new Size(101, 25);
			this.textBox_9.TabIndex = 8;
			this.textBox_9.TextAlign = HorizontalAlignment.Center;
			this.label_10.Location = new Point(5, 59);
			this.label_10.Name = Class521.smethod_0(11233);
			this.label_10.Size = new Size(118, 20);
			this.label_10.TabIndex = 27;
			this.label_10.Text = Class521.smethod_0(67204);
			this.label_10.TextAlign = ContentAlignment.TopRight;
			this.groupBox_3.Controls.Add(this.label_6);
			this.groupBox_3.Controls.Add(this.label_4);
			this.groupBox_3.Controls.Add(this.textBox_4);
			this.groupBox_3.Controls.Add(this.textBox_7);
			this.groupBox_3.Controls.Add(this.textBox_5);
			this.groupBox_3.Controls.Add(this.label_8);
			this.groupBox_3.Controls.Add(this.label_5);
			this.groupBox_3.Location = new Point(194, 240);
			this.groupBox_3.Name = Class521.smethod_0(67225);
			this.groupBox_3.Size = new Size(513, 92);
			this.groupBox_3.TabIndex = 24;
			this.groupBox_3.TabStop = false;
			this.groupBox_3.Text = Class521.smethod_0(67246);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.groupBox_2);
			base.Name = Class521.smethod_0(67255);
			base.Size = new Size(722, 348);
			base.Load += this.Control9_Load;
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			this.groupBox_2.ResumeLayout(false);
			this.groupBox_4.ResumeLayout(false);
			this.groupBox_4.PerformLayout();
			this.groupBox_3.ResumeLayout(false);
			this.groupBox_3.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x0600194A RID: 6474 RVA: 0x000B0384 File Offset: 0x000AE584
		[CompilerGenerated]
		private bool method_26(TradingSymbol tradingSymbol_2)
		{
			return tradingSymbol_2.ID == this.CurrSelectedSymb.ID;
		}

		// Token: 0x0600194B RID: 6475 RVA: 0x000B0384 File Offset: 0x000AE584
		[CompilerGenerated]
		private bool method_27(TradingSymbol tradingSymbol_2)
		{
			return tradingSymbol_2.ID == this.CurrSelectedSymb.ID;
		}

		// Token: 0x04000C8B RID: 3211
		private List<TradingSymbol> list_0;

		// Token: 0x04000C8C RID: 3212
		private TradingSymbol tradingSymbol_0;

		// Token: 0x04000C8D RID: 3213
		private TradingSymbol tradingSymbol_1;

		// Token: 0x04000C8E RID: 3214
		private List<AcctSymbol> list_1;

		// Token: 0x04000C8F RID: 3215
		private List<TradingSymbol> list_2;

		// Token: 0x04000C90 RID: 3216
		private bool bool_0;

		// Token: 0x04000C91 RID: 3217
		private bool bool_1;

		// Token: 0x04000C92 RID: 3218
		private bool bool_2;

		// Token: 0x04000C93 RID: 3219
		private bool bool_3;

		// Token: 0x04000C94 RID: 3220
		private bool bool_4;

		// Token: 0x04000C95 RID: 3221
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000C96 RID: 3222
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000C97 RID: 3223
		private IContainer icontainer_0;

		// Token: 0x04000C98 RID: 3224
		private ListBox listBox_0;

		// Token: 0x04000C99 RID: 3225
		private Label label_0;

		// Token: 0x04000C9A RID: 3226
		private GroupBox groupBox_0;

		// Token: 0x04000C9B RID: 3227
		private Label label_1;

		// Token: 0x04000C9C RID: 3228
		private TextBox textBox_0;

		// Token: 0x04000C9D RID: 3229
		private TextBox textBox_1;

		// Token: 0x04000C9E RID: 3230
		private CheckBox checkBox_0;

		// Token: 0x04000C9F RID: 3231
		private Panel panel_0;

		// Token: 0x04000CA0 RID: 3232
		private Label label_2;

		// Token: 0x04000CA1 RID: 3233
		private TextBox textBox_2;

		// Token: 0x04000CA2 RID: 3234
		private RadioButton radioButton_0;

		// Token: 0x04000CA3 RID: 3235
		private Label label_3;

		// Token: 0x04000CA4 RID: 3236
		private TextBox textBox_3;

		// Token: 0x04000CA5 RID: 3237
		private RadioButton radioButton_1;

		// Token: 0x04000CA6 RID: 3238
		private GroupBox groupBox_1;

		// Token: 0x04000CA7 RID: 3239
		private Label label_4;

		// Token: 0x04000CA8 RID: 3240
		private TextBox textBox_4;

		// Token: 0x04000CA9 RID: 3241
		private Label label_5;

		// Token: 0x04000CAA RID: 3242
		private TextBox textBox_5;

		// Token: 0x04000CAB RID: 3243
		private Label label_6;

		// Token: 0x04000CAC RID: 3244
		private TextBox textBox_6;

		// Token: 0x04000CAD RID: 3245
		private Label label_7;

		// Token: 0x04000CAE RID: 3246
		private TextBox textBox_7;

		// Token: 0x04000CAF RID: 3247
		private Label label_8;

		// Token: 0x04000CB0 RID: 3248
		private GroupBox groupBox_2;

		// Token: 0x04000CB1 RID: 3249
		private GroupBox groupBox_3;

		// Token: 0x04000CB2 RID: 3250
		private TextBox textBox_8;

		// Token: 0x04000CB3 RID: 3251
		private Label label_9;

		// Token: 0x04000CB4 RID: 3252
		private TextBox textBox_9;

		// Token: 0x04000CB5 RID: 3253
		private Label label_10;

		// Token: 0x04000CB6 RID: 3254
		private GroupBox groupBox_4;

		// Token: 0x04000CB7 RID: 3255
		private TextBox textBox_10;

		// Token: 0x04000CB8 RID: 3256
		private Label label_11;

		// Token: 0x04000CB9 RID: 3257
		private Label label_12;

		// Token: 0x04000CBA RID: 3258
		private TextBox textBox_11;

		// Token: 0x04000CBB RID: 3259
		private Label label_13;

		// Token: 0x0200024C RID: 588
		[CompilerGenerated]
		private sealed class Class315
		{
			// Token: 0x0600194D RID: 6477 RVA: 0x000B03A8 File Offset: 0x000AE5A8
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ID == this.stkSymbol_0.MstSymbol.ID;
			}

			// Token: 0x04000CBC RID: 3260
			public StkSymbol stkSymbol_0;
		}

		// Token: 0x0200024D RID: 589
		[CompilerGenerated]
		private sealed class Class316
		{
			// Token: 0x0600194F RID: 6479 RVA: 0x000B03D4 File Offset: 0x000AE5D4
			internal bool method_0(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x04000CBD RID: 3261
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200024E RID: 590
		[CompilerGenerated]
		private sealed class Class317
		{
			// Token: 0x06001951 RID: 6481 RVA: 0x000B03F8 File Offset: 0x000AE5F8
			internal bool method_0(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID)
				{
					result = (acctSymbol_1.ID == this.acctSymbol_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000CBE RID: 3262
			public AcctSymbol acctSymbol_0;
		}

		// Token: 0x0200024F RID: 591
		[CompilerGenerated]
		private sealed class Class318
		{
			// Token: 0x06001953 RID: 6483 RVA: 0x000B0434 File Offset: 0x000AE634
			internal bool method_0(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.ID == this.acctSymbol_0.ID)
				{
					result = (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001954 RID: 6484 RVA: 0x000B0434 File Offset: 0x000AE634
			internal bool method_1(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.ID == this.acctSymbol_0.ID)
				{
					result = (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000CBF RID: 3263
			public AcctSymbol acctSymbol_0;
		}

		// Token: 0x02000250 RID: 592
		[CompilerGenerated]
		private sealed class Class319
		{
			// Token: 0x06001956 RID: 6486 RVA: 0x000B0470 File Offset: 0x000AE670
			internal bool method_0(AcctSymbol acctSymbol_0)
			{
				bool result;
				if (acctSymbol_0.AcctID == ((AcctSymbol)this.tradingSymbol_0).AcctID)
				{
					result = (acctSymbol_0.ID == this.tradingSymbol_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001957 RID: 6487 RVA: 0x000B04B0 File Offset: 0x000AE6B0
			internal bool method_1(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06001958 RID: 6488 RVA: 0x000B04B0 File Offset: 0x000AE6B0
			internal bool method_2(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x04000CC0 RID: 3264
			public TradingSymbol tradingSymbol_0;
		}
	}
}
