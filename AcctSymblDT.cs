﻿using System;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200017A RID: 378
	[Serializable]
	internal sealed class AcctSymblDT
	{
		// Token: 0x06000E37 RID: 3639 RVA: 0x000064DB File Offset: 0x000046DB
		public AcctSymblDT(int acctID, int stkID)
		{
			this._AcctID = acctID;
			this._StkID = stkID;
		}

		// Token: 0x06000E38 RID: 3640 RVA: 0x000064F3 File Offset: 0x000046F3
		public AcctSymblDT(int acctID, int stkID, HisData lastHisData) : this(acctID, stkID)
		{
			this._LastHisData = lastHisData;
		}

		// Token: 0x06000E39 RID: 3641 RVA: 0x00006506 File Offset: 0x00004706
		public AcctSymblDT(int acctID, int stkID, HisData lastHisData, HisData lastDayHisData) : this(acctID, stkID, lastHisData)
		{
			this._LastDayHisData = lastDayHisData;
		}

		// Token: 0x17000236 RID: 566
		// (get) Token: 0x06000E3A RID: 3642 RVA: 0x0005D748 File Offset: 0x0005B948
		// (set) Token: 0x06000E3B RID: 3643 RVA: 0x0000651B File Offset: 0x0000471B
		public int AcctID
		{
			get
			{
				return this._AcctID;
			}
			set
			{
				this._AcctID = value;
			}
		}

		// Token: 0x17000237 RID: 567
		// (get) Token: 0x06000E3C RID: 3644 RVA: 0x0005D760 File Offset: 0x0005B960
		// (set) Token: 0x06000E3D RID: 3645 RVA: 0x00006526 File Offset: 0x00004726
		public int StkID
		{
			get
			{
				return this._StkID;
			}
			set
			{
				this._StkID = value;
			}
		}

		// Token: 0x17000238 RID: 568
		// (get) Token: 0x06000E3E RID: 3646 RVA: 0x0005D778 File Offset: 0x0005B978
		public DateTime? LastDT
		{
			get
			{
				DateTime? result;
				if (this._LastHisData != null)
				{
					result = new DateTime?(this._LastHisData.Date);
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000239 RID: 569
		// (get) Token: 0x06000E3F RID: 3647 RVA: 0x0005D7B0 File Offset: 0x0005B9B0
		// (set) Token: 0x06000E40 RID: 3648 RVA: 0x00006531 File Offset: 0x00004731
		public HisData LastHisData
		{
			get
			{
				return this._LastHisData;
			}
			set
			{
				this._LastHisData = value;
			}
		}

		// Token: 0x1700023A RID: 570
		// (get) Token: 0x06000E41 RID: 3649 RVA: 0x0005D7C8 File Offset: 0x0005B9C8
		// (set) Token: 0x06000E42 RID: 3650 RVA: 0x0000653C File Offset: 0x0000473C
		public HisData LastDayHisData
		{
			get
			{
				return this._LastDayHisData;
			}
			set
			{
				this._LastDayHisData = value;
			}
		}

		// Token: 0x1700023B RID: 571
		// (get) Token: 0x06000E43 RID: 3651 RVA: 0x0005D7E0 File Offset: 0x0005B9E0
		// (set) Token: 0x06000E44 RID: 3652 RVA: 0x00006547 File Offset: 0x00004747
		public bool IfAutoStopLoss
		{
			get
			{
				return this._IfAutoStopLoss;
			}
			set
			{
				if (this._IfAutoStopLoss != value)
				{
					this._IfAutoStopLoss = value;
				}
			}
		}

		// Token: 0x1700023C RID: 572
		// (get) Token: 0x06000E45 RID: 3653 RVA: 0x0005D7F8 File Offset: 0x0005B9F8
		// (set) Token: 0x06000E46 RID: 3654 RVA: 0x0000655B File Offset: 0x0000475B
		public bool IfAutoLimitTake
		{
			get
			{
				return this._IfAutoLimitTake;
			}
			set
			{
				if (this._IfAutoLimitTake != value)
				{
					this._IfAutoLimitTake = value;
				}
			}
		}

		// Token: 0x0400076B RID: 1899
		private int _AcctID;

		// Token: 0x0400076C RID: 1900
		private int _StkID;

		// Token: 0x0400076D RID: 1901
		private HisData _LastHisData;

		// Token: 0x0400076E RID: 1902
		private HisData _LastDayHisData;

		// Token: 0x0400076F RID: 1903
		private bool _IfAutoStopLoss;

		// Token: 0x04000770 RID: 1904
		private bool _IfAutoLimitTake;
	}
}
