﻿using System;
using System.Windows.Forms;
using ns18;
using ns4;

namespace TEx
{
	// Token: 0x02000254 RID: 596
	internal sealed class TransTabCtrl : Control10
	{
		// Token: 0x06001972 RID: 6514 RVA: 0x000B0DC8 File Offset: 0x000AEFC8
		public TransTabCtrl(SplitterPanel panel) : base(panel)
		{
			if (Base.UI.SwitchedBehindTransTabs != null)
			{
				Base.UI.SwitchedBehindTransTabs.method_7(base.ContainerPanel);
				this._TransTabs = Base.UI.SwitchedBehindTransTabs;
				Base.UI.SwitchedBehindTransTabs = null;
			}
			else
			{
				this._TransTabs = new TransTabs(base.ContainerPanel);
			}
			this._TransTabs.ParentTransTabCtrl = this;
			base.PanelHeaderText = Class521.smethod_0(67422);
		}

		// Token: 0x06001973 RID: 6515 RVA: 0x0000AA12 File Offset: 0x00008C12
		protected override void button_Max_Click(object sender, EventArgs e)
		{
			this._TransTabs.Focus();
			base.button_Max_Click(sender, e);
		}

		// Token: 0x06001974 RID: 6516 RVA: 0x0000AA2A File Offset: 0x00008C2A
		public override void SetTransTabsMaximization()
		{
			base.SetTransTabsMaximization();
			Base.UI.Form.IsTransTabMaximized = !Base.UI.Form.IsTransTabMaximized;
		}

		// Token: 0x1700043C RID: 1084
		// (get) Token: 0x06001975 RID: 6517 RVA: 0x000B0E38 File Offset: 0x000AF038
		public TransTabs TransTabs
		{
			get
			{
				return this._TransTabs;
			}
		}

		// Token: 0x04000CC9 RID: 3273
		private TransTabs _TransTabs;
	}
}
