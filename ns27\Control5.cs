﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using TEx.Comn;

namespace ns27
{
	// Token: 0x02000236 RID: 566
	internal sealed class Control5 : UserControl
	{
		// Token: 0x06001799 RID: 6041 RVA: 0x00009AAC File Offset: 0x00007CAC
		public Control5()
		{
			this.method_6();
		}

		// Token: 0x0600179A RID: 6042 RVA: 0x000A3A78 File Offset: 0x000A1C78
		private void method_0(HDTick hdtick_1)
		{
			this.labelX_0.Text = this.method_2(hdtick_1, true);
			this.labelX_2.Text = this.method_2(hdtick_1, false);
			double num = hdtick_1.Price - this.hdtick_0.Price;
			this.labelItem_3.Text = this.method_3(Class521.smethod_0(59778), hdtick_1.Price.ToString(), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			num = this.double_0 - this.double_3;
			this.labelItem_3.Text = this.method_3(Class521.smethod_0(59818), this.double_0.ToString(), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			if (hdtick_1.Price > this.double_1)
			{
				this.double_1 = hdtick_1.Price;
			}
			if (hdtick_1.Price < this.double_2)
			{
				this.double_2 = hdtick_1.Price;
			}
			num = this.double_1 - this.hdtick_0.Price;
			this.labelItem_3.Text = this.method_3(Class521.smethod_0(59827), this.double_1.ToString(), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			num = this.double_2 - this.hdtick_0.Price;
			this.labelItem_3.Text = this.method_3(Class521.smethod_0(59836), this.double_2.ToString(), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			num = hdtick_1.Price - this.double_3;
			this.labelItem_4.Text = this.method_3(Class521.smethod_0(59845), num.ToString(), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			this.labelItem_5.Text = this.method_3(Class521.smethod_0(59854), (num / this.double_3).ToString(Class521.smethod_0(47501)), (num > 0.0) ? Class521.smethod_0(59813) : ((num == 0.0) ? Class521.smethod_0(59800) : Class521.smethod_0(59787)));
			this.labelItem_6.Text = this.method_3(Class521.smethod_0(59863), this.int_1.ToString(), Class521.smethod_0(59872));
			this.labelItem_6.Text = this.method_3(Class521.smethod_0(59881), hdtick_1.Volume.ToString(), Class521.smethod_0(59872));
			this.labelItem_6.Text = this.method_3(Class521.smethod_0(59890), hdtick_1.Amount.ToString(), Class521.smethod_0(59872));
			this.labelItem_12.Text = this.method_3(Class521.smethod_0(59899), (hdtick_1.Amount - this.int_0).ToString(), Class521.smethod_0(59872));
			this.labelItem_12.Text = this.method_3(Class521.smethod_0(59908), this.int_2.ToString(), Class521.smethod_0(59813));
			this.labelItem_12.Text = this.method_3(Class521.smethod_0(59917), this.int_3.ToString(), Class521.smethod_0(59787));
			int count = this.itemPanel_0.Items.Count;
			for (int i = 1; i < count; i++)
			{
				BaseItem baseItem = this.itemPanel_0.Items[i];
				if (i < count - 1)
				{
					baseItem.Text = this.itemPanel_0.Items[i + 1].Text;
				}
				else
				{
					num = hdtick_1.Price - this.hdtick_0.Price;
					baseItem.Text = string.Concat(new string[]
					{
						Class521.smethod_0(59926),
						hdtick_1.Date.ToString(Class521.smethod_0(59979)),
						Class521.smethod_0(59988),
						(hdtick_1.Price > this.double_3) ? Class521.smethod_0(59813) : Class521.smethod_0(59787),
						Class521.smethod_0(60069),
						hdtick_1.Price.ToString(),
						Class521.smethod_0(60074),
						hdtick_1.IsBuy ? Class521.smethod_0(59813) : Class521.smethod_0(59787),
						Class521.smethod_0(60069),
						hdtick_1.Volume.ToString(),
						Class521.smethod_0(60155),
						(hdtick_1.Amount - this.hdtick_0.Amount).ToString(Class521.smethod_0(60244)),
						Class521.smethod_0(60249),
						hdtick_1.IsBuy ? Class521.smethod_0(59813) : Class521.smethod_0(59787),
						Class521.smethod_0(60338),
						this.method_1(hdtick_1),
						Class521.smethod_0(60355)
					});
				}
			}
		}

		// Token: 0x0600179B RID: 6043 RVA: 0x000A4098 File Offset: 0x000A2298
		private string method_1(HDTick hdtick_1)
		{
			int num = hdtick_1.Amount - this.hdtick_0.Amount;
			string result;
			if (hdtick_1.Volume - num == 0)
			{
				result = Class521.smethod_0(60376);
			}
			else if (hdtick_1.IsBuy)
			{
				result = ((num == 0) ? Class521.smethod_0(60403) : ((num < 0) ? Class521.smethod_0(60394) : Class521.smethod_0(60385)));
			}
			else
			{
				result = ((num == 0) ? Class521.smethod_0(60430) : ((num < 0) ? Class521.smethod_0(60421) : Class521.smethod_0(60412)));
			}
			return result;
		}

		// Token: 0x0600179C RID: 6044 RVA: 0x000A4134 File Offset: 0x000A2334
		private string method_2(HDTick hdtick_1, bool bool_0)
		{
			return string.Concat(new string[]
			{
				Class521.smethod_0(60439),
				bool_0 ? Class521.smethod_0(25000) : Class521.smethod_0(24991),
				Class521.smethod_0(60504),
				(hdtick_1.S1Prc > this.hdtick_0.Price) ? Class521.smethod_0(59813) : Class521.smethod_0(60577),
				Class521.smethod_0(60594),
				hdtick_1.S1Prc.ToString(),
				Class521.smethod_0(60611),
				hdtick_1.S1Vol.ToString(),
				Class521.smethod_0(60355)
			});
		}

		// Token: 0x0600179D RID: 6045 RVA: 0x000A41FC File Offset: 0x000A23FC
		private string method_3(string string_0, string string_1, string string_2)
		{
			return string.Concat(new string[]
			{
				Class521.smethod_0(60704),
				string_0,
				Class521.smethod_0(60769),
				string_2,
				Class521.smethod_0(60834),
				string_1,
				Class521.smethod_0(60355)
			});
		}

		// Token: 0x0600179E RID: 6046 RVA: 0x000A4258 File Offset: 0x000A2458
		public void method_4(int int_4)
		{
			int num = int_4 - base.Height;
			this.itemPanel_0.Height += num;
			if (this.itemPanel_0.Controls.Count > 1 && this.itemPanel_0.Height / 22 < this.itemPanel_0.Items.Count)
			{
				int num2 = Convert.ToInt32(Math.Floor(Convert.ToDecimal(this.itemPanel_0.Height) / 22m));
				if (num2 > 1)
				{
					for (int i = num2 - 1; i < this.itemPanel_0.Controls.Count; i++)
					{
						this.itemPanel_0.Items.RemoveAt(this.itemPanel_0.Controls.Count - 1);
					}
				}
			}
			this.panelEx_0.Height += num;
			base.Height = int_4;
		}

		// Token: 0x0600179F RID: 6047 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_5(HDTick hdtick_1)
		{
		}

		// Token: 0x060017A0 RID: 6048 RVA: 0x00009ABC File Offset: 0x00007CBC
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060017A1 RID: 6049 RVA: 0x000A4340 File Offset: 0x000A2540
		private void method_6()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(Control5));
			this.panelEx_0 = new PanelEx();
			this.panelEx_1 = new PanelEx();
			this.itemPanel_2 = new ItemPanel();
			this.labelItem_9 = new LabelItem();
			this.labelItem_10 = new LabelItem();
			this.labelItem_11 = new LabelItem();
			this.labelItem_12 = new LabelItem();
			this.labelItem_13 = new LabelItem();
			this.labelItem_14 = new LabelItem();
			this.itemPanel_1 = new ItemPanel();
			this.labelItem_3 = new LabelItem();
			this.labelItem_4 = new LabelItem();
			this.labelItem_5 = new LabelItem();
			this.labelItem_6 = new LabelItem();
			this.labelItem_7 = new LabelItem();
			this.labelItem_8 = new LabelItem();
			this.labelX_2 = new LabelX();
			this.labelX_0 = new LabelX();
			this.labelX_1 = new LabelX();
			this.itemPanel_0 = new ItemPanel();
			this.labelItem_0 = new LabelItem();
			this.labelItem_1 = new LabelItem();
			this.labelItem_2 = new LabelItem();
			this.labelItem_15 = new LabelItem();
			this.panelEx_0.SuspendLayout();
			this.panelEx_1.SuspendLayout();
			base.SuspendLayout();
			this.panelEx_0.AntiAlias = false;
			this.panelEx_0.CanvasColor = SystemColors.Control;
			this.panelEx_0.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx_0.Controls.Add(this.panelEx_1);
			this.panelEx_0.Controls.Add(this.labelX_2);
			this.panelEx_0.Controls.Add(this.labelX_0);
			this.panelEx_0.Controls.Add(this.labelX_1);
			this.panelEx_0.Controls.Add(this.itemPanel_0);
			this.panelEx_0.Font = new Font(Class521.smethod_0(7183), 9.5f);
			this.panelEx_0.Location = new Point(0, 2);
			this.panelEx_0.Name = Class521.smethod_0(60851);
			this.panelEx_0.Size = new Size(220, 445);
			this.panelEx_0.Style.Alignment = StringAlignment.Center;
			this.panelEx_0.Style.BackColor1.Color = Color.Black;
			this.panelEx_0.Style.BackColor2.Color = Color.Black;
			this.panelEx_0.Style.Border = eBorderType.SingleLine;
			this.panelEx_0.Style.BorderColor.Color = Color.DarkRed;
			this.panelEx_0.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx_0.Style.GradientAngle = 90;
			this.panelEx_0.TabIndex = 1;
			this.panelEx_0.Text = Class521.smethod_0(60851);
			this.panelEx_1.CanvasColor = SystemColors.Control;
			this.panelEx_1.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx_1.Controls.Add(this.itemPanel_2);
			this.panelEx_1.Controls.Add(this.itemPanel_1);
			this.panelEx_1.Font = new Font(Class521.smethod_0(7183), 8.5f);
			this.panelEx_1.Location = new Point(0, 85);
			this.panelEx_1.Name = Class521.smethod_0(60864);
			this.panelEx_1.Size = new Size(220, 128);
			this.panelEx_1.Style.Alignment = StringAlignment.Center;
			this.panelEx_1.Style.BackColor1.Alpha = 0;
			this.panelEx_1.Style.BackColor1.Color = Color.Transparent;
			this.panelEx_1.Style.BackColor2.Alpha = 0;
			this.panelEx_1.Style.BackColor2.Color = Color.Transparent;
			this.panelEx_1.Style.Border = eBorderType.SingleLine;
			this.panelEx_1.Style.BorderColor.Color = Color.DarkRed;
			this.panelEx_1.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx_1.Style.GradientAngle = 90;
			this.panelEx_1.TabIndex = 6;
			this.panelEx_1.Text = Class521.smethod_0(60864);
			this.itemPanel_2.AntiAlias = false;
			this.itemPanel_2.BackgroundStyle.BackColor = Color.Transparent;
			this.itemPanel_2.BackgroundStyle.BorderColor = Color.Transparent;
			this.itemPanel_2.BackgroundStyle.Class = Class521.smethod_0(60877);
			this.itemPanel_2.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_2.ContainerControlProcessDialogKey = true;
			this.itemPanel_2.FadeEffect = false;
			this.itemPanel_2.Font = new Font(Class521.smethod_0(24023), 9f);
			this.itemPanel_2.Items.AddRange(new BaseItem[]
			{
				this.labelItem_9,
				this.labelItem_10,
				this.labelItem_11,
				this.labelItem_12,
				this.labelItem_13,
				this.labelItem_14
			});
			this.itemPanel_2.ItemSpacing = 4;
			this.itemPanel_2.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_2.Location = new Point(110, 0);
			this.itemPanel_2.Name = Class521.smethod_0(60890);
			this.itemPanel_2.Size = new Size(110, 128);
			this.itemPanel_2.TabIndex = 7;
			this.itemPanel_2.Text = Class521.smethod_0(60915);
			this.labelItem_9.Name = Class521.smethod_0(60932);
			this.labelItem_9.PaddingLeft = 3;
			this.labelItem_9.PaddingRight = 3;
			this.labelItem_9.Text = Class521.smethod_0(60953);
			this.labelItem_10.Name = Class521.smethod_0(61139);
			this.labelItem_10.PaddingLeft = 3;
			this.labelItem_10.PaddingRight = 3;
			this.labelItem_10.Text = Class521.smethod_0(61160);
			this.labelItem_11.Name = Class521.smethod_0(61342);
			this.labelItem_11.PaddingLeft = 3;
			this.labelItem_11.PaddingRight = 3;
			this.labelItem_11.Text = Class521.smethod_0(61363);
			this.labelItem_12.Name = Class521.smethod_0(61549);
			this.labelItem_12.PaddingLeft = 3;
			this.labelItem_12.PaddingRight = 3;
			this.labelItem_12.Text = Class521.smethod_0(61574);
			this.labelItem_13.Name = Class521.smethod_0(61756);
			this.labelItem_13.PaddingLeft = 3;
			this.labelItem_13.PaddingRight = 3;
			this.labelItem_13.Text = Class521.smethod_0(61781);
			this.labelItem_14.Name = Class521.smethod_0(61967);
			this.labelItem_14.PaddingLeft = 3;
			this.labelItem_14.PaddingRight = 3;
			this.labelItem_14.Text = Class521.smethod_0(61992);
			this.itemPanel_1.AntiAlias = false;
			this.itemPanel_1.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.itemPanel_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_1.ContainerControlProcessDialogKey = true;
			this.itemPanel_1.FadeEffect = false;
			this.itemPanel_1.Font = new Font(Class521.smethod_0(24023), 9f);
			this.itemPanel_1.Items.AddRange(new BaseItem[]
			{
				this.labelItem_3,
				this.labelItem_4,
				this.labelItem_5,
				this.labelItem_6,
				this.labelItem_7,
				this.labelItem_8
			});
			this.itemPanel_1.ItemSpacing = 4;
			this.itemPanel_1.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_1.Location = new Point(0, 0);
			this.itemPanel_1.Name = Class521.smethod_0(62178);
			this.itemPanel_1.Size = new Size(110, 128);
			this.itemPanel_1.TabIndex = 6;
			this.itemPanel_1.Text = Class521.smethod_0(60915);
			this.labelItem_3.Name = Class521.smethod_0(62203);
			this.labelItem_3.PaddingLeft = 3;
			this.labelItem_3.PaddingRight = 3;
			this.labelItem_3.PaddingTop = 2;
			this.labelItem_3.Text = Class521.smethod_0(62228);
			this.labelItem_4.Name = Class521.smethod_0(62410);
			this.labelItem_4.PaddingLeft = 3;
			this.labelItem_4.PaddingRight = 3;
			this.labelItem_4.Text = Class521.smethod_0(62431);
			this.labelItem_5.Name = Class521.smethod_0(62605);
			this.labelItem_5.PaddingLeft = 3;
			this.labelItem_5.PaddingRight = 3;
			this.labelItem_5.Text = Class521.smethod_0(62630);
			this.labelItem_6.Name = Class521.smethod_0(62812);
			this.labelItem_6.PaddingLeft = 3;
			this.labelItem_6.PaddingRight = 3;
			this.labelItem_6.Text = Class521.smethod_0(62837);
			this.labelItem_7.Name = Class521.smethod_0(63023);
			this.labelItem_7.PaddingLeft = 3;
			this.labelItem_7.PaddingRight = 3;
			this.labelItem_7.Text = Class521.smethod_0(63044);
			this.labelItem_8.Name = Class521.smethod_0(63230);
			this.labelItem_8.PaddingLeft = 3;
			this.labelItem_8.PaddingRight = 3;
			this.labelItem_8.Text = Class521.smethod_0(63255);
			this.labelX_2.BackgroundStyle.BorderBottom = eStyleBorderType.Solid;
			this.labelX_2.BackgroundStyle.BorderColor = Color.DarkRed;
			this.labelX_2.BackgroundStyle.BorderColor2 = Color.DarkRed;
			this.labelX_2.BackgroundStyle.BorderLeft = eStyleBorderType.Solid;
			this.labelX_2.BackgroundStyle.BorderRight = eStyleBorderType.Solid;
			this.labelX_2.BackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.labelX_2.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.labelX_2.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_2.Font = new Font(Class521.smethod_0(24023), 9.6f);
			this.labelX_2.Location = new Point(0, 57);
			this.labelX_2.Name = Class521.smethod_0(63437);
			this.labelX_2.PaddingBottom = 2;
			this.labelX_2.PaddingLeft = 6;
			this.labelX_2.PaddingRight = 6;
			this.labelX_2.PaddingTop = 2;
			this.labelX_2.Size = new Size(220, 26);
			this.labelX_2.TabIndex = 5;
			this.labelX_2.Text = componentResourceManager.GetString(Class521.smethod_0(63450));
			this.labelX_0.BackgroundStyle.BorderBottom = eStyleBorderType.Solid;
			this.labelX_0.BackgroundStyle.BorderColor = Color.DarkRed;
			this.labelX_0.BackgroundStyle.BorderColor2 = Color.DarkRed;
			this.labelX_0.BackgroundStyle.BorderLeft = eStyleBorderType.Solid;
			this.labelX_0.BackgroundStyle.BorderRight = eStyleBorderType.Solid;
			this.labelX_0.BackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.labelX_0.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Font = new Font(Class521.smethod_0(24023), 9.6f);
			this.labelX_0.Location = new Point(0, 29);
			this.labelX_0.Name = Class521.smethod_0(63471);
			this.labelX_0.PaddingBottom = 2;
			this.labelX_0.PaddingLeft = 6;
			this.labelX_0.PaddingRight = 6;
			this.labelX_0.PaddingTop = 2;
			this.labelX_0.Size = new Size(220, 26);
			this.labelX_0.TabIndex = 4;
			this.labelX_0.Text = componentResourceManager.GetString(Class521.smethod_0(63484));
			this.labelX_1.AntiAlias = false;
			this.labelX_1.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.labelX_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_1.Font = new Font(Class521.smethod_0(7183), 13.5f);
			this.labelX_1.ForeColor = Color.Yellow;
			this.labelX_1.Location = new Point(0, 5);
			this.labelX_1.Name = Class521.smethod_0(63505);
			this.labelX_1.Size = new Size(220, 26);
			this.labelX_1.TabIndex = 3;
			this.labelX_1.Text = Class521.smethod_0(63522);
			this.labelX_1.TextAlignment = StringAlignment.Center;
			this.itemPanel_0.AntiAlias = false;
			this.itemPanel_0.BackgroundStyle.BackColor = Color.Black;
			this.itemPanel_0.BackgroundStyle.BorderColor = Color.DarkRed;
			this.itemPanel_0.BackgroundStyle.Class = Class521.smethod_0(60877);
			this.itemPanel_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_0.ContainerControlProcessDialogKey = true;
			this.itemPanel_0.FadeEffect = false;
			this.itemPanel_0.Font = new Font(Class521.smethod_0(24023), 10f);
			this.itemPanel_0.Items.AddRange(new BaseItem[]
			{
				this.labelItem_0,
				this.labelItem_1,
				this.labelItem_2,
				this.labelItem_15
			});
			this.itemPanel_0.ItemSpacing = 4;
			this.itemPanel_0.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_0.Location = new Point(0, 212);
			this.itemPanel_0.Name = Class521.smethod_0(63547);
			this.itemPanel_0.Size = new Size(220, 233);
			this.itemPanel_0.TabIndex = 2;
			this.labelItem_0.Name = Class521.smethod_0(63576);
			this.labelItem_0.PaddingLeft = 5;
			this.labelItem_0.PaddingRight = 4;
			this.labelItem_0.PaddingTop = 3;
			this.labelItem_0.Text = componentResourceManager.GetString(Class521.smethod_0(63601));
			this.labelItem_1.Name = Class521.smethod_0(63634);
			this.labelItem_1.PaddingLeft = 3;
			this.labelItem_1.PaddingRight = 4;
			this.labelItem_1.Text = componentResourceManager.GetString(Class521.smethod_0(63651));
			this.labelItem_1.TextAlignment = StringAlignment.Far;
			this.labelItem_2.Name = Class521.smethod_0(63672);
			this.labelItem_2.PaddingLeft = 3;
			this.labelItem_2.PaddingRight = 4;
			this.labelItem_2.Text = componentResourceManager.GetString(Class521.smethod_0(63689));
			this.labelItem_2.TextAlignment = StringAlignment.Far;
			this.labelItem_15.Name = Class521.smethod_0(63710);
			this.labelItem_15.PaddingLeft = 3;
			this.labelItem_15.PaddingRight = 4;
			this.labelItem_15.Text = componentResourceManager.GetString(Class521.smethod_0(63727));
			base.AutoScaleDimensions = new SizeF(7f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = Color.Black;
			base.Controls.Add(this.panelEx_0);
			base.Name = Class521.smethod_0(63748);
			base.Size = new Size(220, 447);
			this.panelEx_0.ResumeLayout(false);
			this.panelEx_1.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000BF2 RID: 3058
		private HDTick hdtick_0;

		// Token: 0x04000BF3 RID: 3059
		private double double_0;

		// Token: 0x04000BF4 RID: 3060
		private double double_1;

		// Token: 0x04000BF5 RID: 3061
		private double double_2;

		// Token: 0x04000BF6 RID: 3062
		private double double_3;

		// Token: 0x04000BF7 RID: 3063
		private int int_0;

		// Token: 0x04000BF8 RID: 3064
		private int int_1;

		// Token: 0x04000BF9 RID: 3065
		private int int_2;

		// Token: 0x04000BFA RID: 3066
		private int int_3;

		// Token: 0x04000BFB RID: 3067
		private IContainer icontainer_0;

		// Token: 0x04000BFC RID: 3068
		private PanelEx panelEx_0;

		// Token: 0x04000BFD RID: 3069
		private ItemPanel itemPanel_0;

		// Token: 0x04000BFE RID: 3070
		private LabelItem labelItem_0;

		// Token: 0x04000BFF RID: 3071
		private LabelItem labelItem_1;

		// Token: 0x04000C00 RID: 3072
		private LabelItem labelItem_2;

		// Token: 0x04000C01 RID: 3073
		private LabelX labelX_0;

		// Token: 0x04000C02 RID: 3074
		private LabelX labelX_1;

		// Token: 0x04000C03 RID: 3075
		private LabelX labelX_2;

		// Token: 0x04000C04 RID: 3076
		private ItemPanel itemPanel_1;

		// Token: 0x04000C05 RID: 3077
		private PanelEx panelEx_1;

		// Token: 0x04000C06 RID: 3078
		private LabelItem labelItem_3;

		// Token: 0x04000C07 RID: 3079
		private LabelItem labelItem_4;

		// Token: 0x04000C08 RID: 3080
		private LabelItem labelItem_5;

		// Token: 0x04000C09 RID: 3081
		private LabelItem labelItem_6;

		// Token: 0x04000C0A RID: 3082
		private LabelItem labelItem_7;

		// Token: 0x04000C0B RID: 3083
		private LabelItem labelItem_8;

		// Token: 0x04000C0C RID: 3084
		private ItemPanel itemPanel_2;

		// Token: 0x04000C0D RID: 3085
		private LabelItem labelItem_9;

		// Token: 0x04000C0E RID: 3086
		private LabelItem labelItem_10;

		// Token: 0x04000C0F RID: 3087
		private LabelItem labelItem_11;

		// Token: 0x04000C10 RID: 3088
		private LabelItem labelItem_12;

		// Token: 0x04000C11 RID: 3089
		private LabelItem labelItem_13;

		// Token: 0x04000C12 RID: 3090
		private LabelItem labelItem_14;

		// Token: 0x04000C13 RID: 3091
		private LabelItem labelItem_15;
	}
}
