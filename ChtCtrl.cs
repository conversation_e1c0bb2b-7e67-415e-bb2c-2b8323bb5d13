﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns10;
using ns13;
using ns18;
using ns26;
using ns27;
using ns4;
using ns7;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000238 RID: 568
	internal abstract class ChtCtrl : SplitContainer, IDisposable, Interface1
	{
		// Token: 0x14000087 RID: 135
		// (add) Token: 0x060017A6 RID: 6054 RVA: 0x000A53DC File Offset: 0x000A35DC
		// (remove) Token: 0x060017A7 RID: 6055 RVA: 0x000A5414 File Offset: 0x000A3614
		public event EventHandler SymbDataSetChanging
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060017A8 RID: 6056 RVA: 0x000A544C File Offset: 0x000A364C
		private void method_0(SymbDataSet symbDataSet_1)
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(symbDataSet_1, e);
			}
		}

		// Token: 0x14000088 RID: 136
		// (add) Token: 0x060017A9 RID: 6057 RVA: 0x000A5474 File Offset: 0x000A3674
		// (remove) Token: 0x060017AA RID: 6058 RVA: 0x000A54AC File Offset: 0x000A36AC
		public event EventHandler SymbDataSetChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060017AB RID: 6059 RVA: 0x000A54E4 File Offset: 0x000A36E4
		private void method_1(SymbDataSet symbDataSet_1)
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(symbDataSet_1, e);
			}
		}

		// Token: 0x14000089 RID: 137
		// (add) Token: 0x060017AC RID: 6060 RVA: 0x000A550C File Offset: 0x000A370C
		// (remove) Token: 0x060017AD RID: 6061 RVA: 0x000A5544 File Offset: 0x000A3744
		public event EventHandler PeriodChanging
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060017AE RID: 6062 RVA: 0x000A557C File Offset: 0x000A377C
		private void method_2()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_2;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x1400008A RID: 138
		// (add) Token: 0x060017AF RID: 6063 RVA: 0x000A55A4 File Offset: 0x000A37A4
		// (remove) Token: 0x060017B0 RID: 6064 RVA: 0x000A55DC File Offset: 0x000A37DC
		public event EventHandler PeriodChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060017B1 RID: 6065 RVA: 0x000A5614 File Offset: 0x000A3814
		private void method_3()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_3;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x1400008B RID: 139
		// (add) Token: 0x060017B2 RID: 6066 RVA: 0x000A563C File Offset: 0x000A383C
		// (remove) Token: 0x060017B3 RID: 6067 RVA: 0x000A5674 File Offset: 0x000A3874
		public event EventHandler StkRationedPrcApplying
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060017B4 RID: 6068 RVA: 0x000A56AC File Offset: 0x000A38AC
		private void method_4()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_4;
			if (eventHandler != null)
			{
				eventHandler(null, e);
			}
		}

		// Token: 0x060017B5 RID: 6069 RVA: 0x000A56D4 File Offset: 0x000A38D4
		public ChtCtrl(SymbDataSet sds, HisDataPeriodSet hisDataPeriodSet, string tag, int maxSticksPerChart)
		{
			this.symbDataSet_0 = sds;
			this.hisDataPeriodSet_0 = hisDataPeriodSet;
			base.SuspendLayout();
			base.Name = Class521.smethod_0(63761);
			base.Dock = DockStyle.Fill;
			base.FixedPanel = FixedPanel.Panel2;
			base.Size = new Size(497, 407);
			base.Orientation = Orientation.Vertical;
			base.SplitterDistance = base.Width - 224;
			base.SplitterWidth = 1;
			base.BorderStyle = BorderStyle.None;
			base.TabStop = false;
			base.Tag = tag;
			this.MaxSticksPerChart = maxSticksPerChart;
			this.ChtSpC = new SplitContainer();
			this.ChtSpC.Dock = DockStyle.Fill;
			this.ChtSpC.Size = new Size(497, 407);
			this.ChtSpC.SplitterDistance = 278;
			this.ChtSpC.SplitterWidth = 1;
			this.ChtSpC.Orientation = Orientation.Horizontal;
			this.ChtSpC.BorderStyle = BorderStyle.None;
			this.ChtSpC.TabStop = false;
			base.Panel1.Controls.Add(this.ChtSpC);
			this.IfShowTickPanel = false;
			this.ApplyTheme(Base.UI.Form.ChartTheme);
			base.ResumeLayout();
			this.vmethod_0();
			base.Resize += this.ChtCtrl_Resize;
			this.ChtSpC.SplitterMoved += this.vmethod_32;
		}

		// Token: 0x060017B6 RID: 6070 RVA: 0x00009ADD File Offset: 0x00007CDD
		protected virtual void vmethod_0()
		{
			base.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
		}

		// Token: 0x060017B7 RID: 6071
		protected abstract int vmethod_1();

		// Token: 0x060017B8 RID: 6072 RVA: 0x000A585C File Offset: 0x000A3A5C
		protected virtual void vmethod_2()
		{
			this.class58_0 = new Class58(this.ChtSpC.Panel1, 22, 7, false);
			this.class58_0.Visible = false;
			this.class58_0.MouseLeave += this.class58_0_MouseLeave;
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = 100;
			this.timer_0.Tick += this.timer_0_Tick;
		}

		// Token: 0x060017B9 RID: 6073 RVA: 0x00009AED File Offset: 0x00007CED
		protected void class58_0_MouseLeave(object sender, EventArgs e)
		{
			this.vmethod_3();
		}

		// Token: 0x060017BA RID: 6074 RVA: 0x00009AF7 File Offset: 0x00007CF7
		protected virtual void vmethod_3()
		{
			this.class58_0.Visible = false;
			this.class58_0.Visible = true;
		}

		// Token: 0x060017BB RID: 6075 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_4(HisData hisData_0, bool bool_5)
		{
		}

		// Token: 0x060017BC RID: 6076 RVA: 0x000A58D8 File Offset: 0x000A3AD8
		protected virtual void vmethod_5(int int_4)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_4(int_4);
			}
		}

		// Token: 0x060017BD RID: 6077 RVA: 0x000A592C File Offset: 0x000A3B2C
		public void method_5()
		{
			HisData hisData_ = this.method_19(this.SymbDataSet.DateTimeOfLastRec);
			this.method_17(hisData_);
		}

		// Token: 0x060017BE RID: 6078 RVA: 0x000A5954 File Offset: 0x000A3B54
		protected virtual void vmethod_6(HisData hisData_0)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_5(hisData_0);
			}
		}

		// Token: 0x060017BF RID: 6079 RVA: 0x000A59A8 File Offset: 0x000A3BA8
		protected virtual void vmethod_7(HDTick hdtick_0)
		{
			HisData hisData_ = Base.Data.smethod_114(hdtick_0);
			this.vmethod_6(hisData_);
		}

		// Token: 0x060017C0 RID: 6080 RVA: 0x000A59C8 File Offset: 0x000A3BC8
		public virtual void vmethod_8(HisData hisData_0)
		{
			if (this.IndexOfLastItemShown < this.HisDataPeriodSet.PeriodHisDataList.Count && this.IndexOfLastItemShown >= 0)
			{
				if (this.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
				{
					int? periodUnits = this.HisDataPeriodSet.PeriodUnits;
					if (periodUnits.GetValueOrDefault() == 1 & periodUnits != null)
					{
						this.vmethod_6(hisData_0);
						int indexOfLastItemShown = this.IndexOfLastItemShown;
						this.IndexOfLastItemShown = indexOfLastItemShown + 1;
						goto IL_CD;
					}
				}
				if (!(this.HisDataPeriodSet.PeriodHisDataList.Values[this.IndexOfLastItemShown].Date < hisData_0.Date) && this.NbOfPtsDisplayedInChart >= 1)
				{
					int indexOfLastItemShown2 = this.IndexOfLastItemShown;
					this.method_7(indexOfLastItemShown2, hisData_0);
				}
				else
				{
					this.vmethod_6(hisData_0);
					int indexOfLastItemShown = this.IndexOfLastItemShown;
					this.IndexOfLastItemShown = indexOfLastItemShown + 1;
				}
				IL_CD:
				this.method_10(hisData_0);
			}
		}

		// Token: 0x060017C1 RID: 6081 RVA: 0x000A5AAC File Offset: 0x000A3CAC
		public virtual void vmethod_9(HDTick hdtick_0)
		{
			if (this.IndexOfLastItemShown < this.HisDataPeriodSet.Count)
			{
				HisData lastItemShown = this.LastItemShown;
				if (hdtick_0.Date > lastItemShown.Date)
				{
					this.vmethod_7(hdtick_0);
					int indexOfLastItemShown = this.IndexOfLastItemShown;
					this.IndexOfLastItemShown = indexOfLastItemShown + 1;
				}
				else
				{
					int indexOfLastItemShown2 = this.IndexOfLastItemShown;
					this.method_9(indexOfLastItemShown2, hdtick_0);
				}
				this.method_11(hdtick_0);
			}
		}

		// Token: 0x060017C2 RID: 6082 RVA: 0x000A5B18 File Offset: 0x000A3D18
		private void method_6(int int_4, HisData hisData_0, bool bool_5)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_7(int_4, hisData_0, bool_5);
			}
		}

		// Token: 0x060017C3 RID: 6083 RVA: 0x00009B13 File Offset: 0x00007D13
		private void method_7(int int_4, HisData hisData_0)
		{
			this.method_6(int_4, hisData_0, false);
		}

		// Token: 0x060017C4 RID: 6084 RVA: 0x000A5B70 File Offset: 0x000A3D70
		private void method_8(int int_4, HDTick hdtick_0, bool bool_5)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_9(int_4, hdtick_0, bool_5);
			}
		}

		// Token: 0x060017C5 RID: 6085 RVA: 0x00009B20 File Offset: 0x00007D20
		private void method_9(int int_4, HDTick hdtick_0)
		{
			this.method_8(int_4, hdtick_0, false);
		}

		// Token: 0x060017C6 RID: 6086 RVA: 0x000A5BC8 File Offset: 0x000A3DC8
		private void method_10(HisData hisData_0)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_11(hisData_0);
			}
		}

		// Token: 0x060017C7 RID: 6087 RVA: 0x00009B2D File Offset: 0x00007D2D
		private void method_11(HDTick hdtick_0)
		{
			this.method_10(Base.Data.smethod_114(hdtick_0));
		}

		// Token: 0x060017C8 RID: 6088 RVA: 0x00009B3D File Offset: 0x00007D3D
		public void method_12(PeriodType periodType_0, int? nullable_2)
		{
			if (this.SymbDataSet != null && this.SymbDataSet.CurrHisData != null)
			{
				this.vmethod_10(periodType_0, nullable_2, this.SymbDataSet.CurrHisData.Date);
			}
		}

		// Token: 0x060017C9 RID: 6089 RVA: 0x00009B6E File Offset: 0x00007D6E
		public void method_13(DateTime dateTime_0)
		{
			this.vmethod_10(this.PeriodType, this.PeriodUnits, dateTime_0);
		}

		// Token: 0x060017CA RID: 6090 RVA: 0x000A5C1C File Offset: 0x000A3E1C
		public virtual void vmethod_10(PeriodType periodType_0, int? nullable_2, DateTime dateTime_0)
		{
			this.method_2();
			StkSymbol stkSymbol = this.SymbDataSet.CurrSymbol;
			if (!Base.UI.Form.IsInBlindTestMode && this.IfNoSync && this.LinkedSymblId != null)
			{
				stkSymbol = this.LinkedSymbol;
			}
			if (this.HisDataPeriodSet.SymbId != stkSymbol.ID)
			{
				this.HisDataPeriodSet = this.SymbDataSet.method_58(periodType_0, nullable_2);
			}
			else if (HisDataPeriodSet.smethod_2(periodType_0, nullable_2))
			{
				int? num = nullable_2;
				if (num.GetValueOrDefault() == 1 & num != null)
				{
					this.HisDataPeriodSet = new HisDataPeriodSet(this.SymbDataSet, periodType_0, nullable_2);
				}
				else if (this.PeriodHisDataList != null && this.PeriodHisDataList.Count > 1)
				{
					this.vmethod_15(periodType_0, nullable_2);
				}
				else
				{
					this.HisDataPeriodSet = new HisDataPeriodSet(this.SymbDataSet, periodType_0, nullable_2);
				}
			}
			else
			{
				ChtCtrl.Class309 @class = new ChtCtrl.Class309();
				@class.dateTime_0 = this.CurrDateTime;
				this.HisDataPeriodSet = new HisDataPeriodSet();
				DateTime dateTime = this.HisDataList.Keys.Last<DateTime>();
				if (dateTime < @class.dateTime_0)
				{
					try
					{
						dateTime = this.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList.Keys.Last(new Func<DateTime, bool>(@class.method_0));
					}
					catch
					{
					}
				}
				this.HisDataPeriodSet = this.SymbDataSet.method_61(stkSymbol, periodType_0, nullable_2, new DateTime?(@class.dateTime_0), new DateTime?(dateTime));
			}
			this.vmethod_11(dateTime_0);
		}

		// Token: 0x060017CB RID: 6091 RVA: 0x000A5DB0 File Offset: 0x000A3FB0
		public void method_14()
		{
			this.vmethod_11((this.SymbDataSet.CurrDate != null) ? this.SymbDataSet.CurrDate.Value : Base.Data.CurrDate);
		}

		// Token: 0x060017CC RID: 6092 RVA: 0x00009B85 File Offset: 0x00007D85
		protected virtual void vmethod_11(DateTime dateTime_0)
		{
			this.method_18(dateTime_0);
			this.vmethod_4(this.SymbDataSet.LastHisData, true);
			this.method_3();
		}

		// Token: 0x060017CD RID: 6093 RVA: 0x00009BA8 File Offset: 0x00007DA8
		public void method_15()
		{
			if (this.SymbDataSet.CurrHisDataSet != null)
			{
				this.method_10(this.SymbDataSet.CurrHisDataSet.CurrHisData);
			}
		}

		// Token: 0x060017CE RID: 6094 RVA: 0x000A5DF4 File Offset: 0x000A3FF4
		public virtual void vmethod_12(SortedList<DateTime, HisData> sortedList_0)
		{
			if (this.IndexOfLastItemShown >= 0 && this.IndexOfLastItemShown < this.hisDataPeriodSet_0.PeriodHisDataList.Count)
			{
				DateTime dateTime_ = this.hisDataPeriodSet_0.PeriodHisDataList.Keys[this.IndexOfLastItemShown];
				if (this.IsNMinsPeriod)
				{
					this.HisDataPeriodSet.method_0();
				}
				this.method_16(dateTime_);
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(63774)));
			}
		}

		// Token: 0x060017CF RID: 6095 RVA: 0x000A5E70 File Offset: 0x000A4070
		protected void method_16(DateTime dateTime_0)
		{
			if (this.hisDataPeriodSet_0.PeriodHisDataList.Count <= this.IndexOfLastItemShown || (this.IndexOfLastItemShown >= 0 && this.IndexOfLastItemShown < this.hisDataPeriodSet_0.PeriodHisDataList.Count && this.hisDataPeriodSet_0.PeriodHisDataList.Keys[this.IndexOfLastItemShown] != dateTime_0))
			{
				this.IndexOfLastItemShown = Class339.smethod_0(this.hisDataPeriodSet_0.PeriodHisDataList, dateTime_0);
			}
		}

		// Token: 0x060017D0 RID: 6096 RVA: 0x00009BCF File Offset: 0x00007DCF
		public virtual void vmethod_13()
		{
			this.vmethod_14(true);
		}

		// Token: 0x060017D1 RID: 6097 RVA: 0x00009BDA File Offset: 0x00007DDA
		public virtual void vmethod_14(bool bool_5)
		{
			this.vmethod_16(this.PeriodType, this.PeriodUnits, bool_5);
		}

		// Token: 0x060017D2 RID: 6098 RVA: 0x00009BF1 File Offset: 0x00007DF1
		public virtual void vmethod_15(PeriodType periodType_0, int? nullable_2)
		{
			this.vmethod_16(periodType_0, nullable_2, true);
		}

		// Token: 0x060017D3 RID: 6099 RVA: 0x00009BFE File Offset: 0x00007DFE
		public virtual void vmethod_16(PeriodType periodType_0, int? nullable_2, bool bool_5)
		{
			if (periodType_0 == PeriodType.ByMins)
			{
				this.HisDataPeriodSet = new HisDataPeriodSet(this.SymbDataSet, periodType_0, nullable_2);
			}
		}

		// Token: 0x060017D4 RID: 6100 RVA: 0x00009C18 File Offset: 0x00007E18
		public void method_17(HisData hisData_0)
		{
			this.vmethod_18(hisData_0, true, true);
		}

		// Token: 0x060017D5 RID: 6101 RVA: 0x00009C25 File Offset: 0x00007E25
		public void method_18(DateTime dateTime_0)
		{
			this.vmethod_17(dateTime_0, true, true);
		}

		// Token: 0x060017D6 RID: 6102 RVA: 0x000A5EF4 File Offset: 0x000A40F4
		public virtual void vmethod_17(DateTime dateTime_0, bool bool_5, bool bool_6)
		{
			HisData hisData_ = this.method_19(dateTime_0);
			this.vmethod_18(hisData_, bool_5, bool_6);
		}

		// Token: 0x060017D7 RID: 6103 RVA: 0x00009C32 File Offset: 0x00007E32
		public virtual void vmethod_18(HisData hisData_0, bool bool_5, bool bool_6)
		{
			this.vmethod_19(hisData_0, bool_5, bool_6, true);
		}

		// Token: 0x060017D8 RID: 6104 RVA: 0x000A5F14 File Offset: 0x000A4114
		public virtual void vmethod_19(HisData hisData_0, bool bool_5, bool bool_6, bool bool_7)
		{
			if (this.SymbDataSet.CurrHisDataSet != null && ((!bool_5 && !bool_6) || !(hisData_0.Date < this.SymbDataSet.CurrStkMeta.BeginDate.Value)))
			{
				if (this.SymbDataSet == null || this.SymbDataSet.CurrHisDataSet == null || this.SymbDataSet.CurrHisDataSet.CurrExchgOBT == null || this.SymbDataSet.CurrHisDataSet.CurrExchgOBT.IsTradingDT(hisData_0.Date) || (!Base.UI.Form.IsSpanMoveNext && !Base.UI.Form.IsSpanMovePrev) || Base.UI.Form.LastSpanMoveDT == null || !Base.UI.Form.IsSpanMoveNext)
				{
					int num = -1;
					num = this.HisDataPeriodSet.method_37(hisData_0.Date, true);
					if (num >= 0)
					{
						if (num != this.IndexOfLastItemShown || !Base.UI.Form.IsSpanMovePrev || this.SymbDataSet.CurrHisDataSet.CurrExchgOBT.IsTradingDT(hisData_0.Date))
						{
							if (bool_7)
							{
								this.method_22();
								this.vmethod_5(num);
							}
							bool flag = false;
							if (!this.HisDataPeriodSet.IsPeriod1m)
							{
								try
								{
									if (this.SymbDataSet.CurrHisDataSet.CurrExchgOBT.IsTradingDT(hisData_0.Date) && !this.HisDataPeriodSet.PeriodHisDataList.ContainsKey(hisData_0.Date))
									{
										if (bool_7)
										{
											this.method_6(num, hisData_0, true);
										}
										flag = true;
									}
								}
								catch (Exception exception_)
								{
									Class184.smethod_0(exception_);
								}
							}
							if (bool_5)
							{
								this.IndexOfLastItemShown = num;
							}
							HisData hisData = this.HisDataPeriodSet.PeriodHisDataList.Values[num];
							if (!flag)
							{
								hisData_0 = hisData;
							}
							if (bool_6)
							{
								this.SymbDataSet.LastHisData = hisData_0;
								this.SymbDataSet.DateTimeOfLastRec = hisData_0.Date;
								this.SymbDataSet.CurrHisDataSet.CurrHisData = hisData_0;
							}
							if (bool_7)
							{
								this.vmethod_4(hisData_0, false);
								this.method_10(hisData_0);
							}
						}
					}
					else
					{
						Base.UI.smethod_125();
						this.method_21(hisData_0);
						this.SymbDataSet.DateTimeOfLastRec = hisData_0.Date;
					}
				}
			}
			else
			{
				if (!this.ChartList[0].ZedGraphControl.Visible)
				{
					this.vmethod_5(0);
				}
				this.method_10(hisData_0);
				this.method_21(hisData_0);
				this.SymbDataSet.DateTimeOfLastRec = hisData_0.Date;
			}
		}

		// Token: 0x060017D9 RID: 6105 RVA: 0x000A617C File Offset: 0x000A437C
		public HisData method_19(DateTime dateTime_0)
		{
			SortedList<DateTime, HisData> sortedList = this.HisDataList;
			if (sortedList == null || !sortedList.Any<KeyValuePair<DateTime, HisData>>() || dateTime_0 < sortedList.First<KeyValuePair<DateTime, HisData>>().Key || dateTime_0 > sortedList.Last<KeyValuePair<DateTime, HisData>>().Key)
			{
				if (this.IsPeriodLong && this.SymbDataSet.Curr1hPeriodHisData != null)
				{
					sortedList = this.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList;
				}
				else
				{
					sortedList = this.PeriodHisDataList;
				}
			}
			return this.method_20(sortedList, dateTime_0);
		}

		// Token: 0x060017DA RID: 6106 RVA: 0x000A6204 File Offset: 0x000A4404
		public HisData method_20(SortedList<DateTime, HisData> sortedList_0, DateTime dateTime_0)
		{
			HisData hisData;
			if (sortedList_0 != null && dateTime_0 >= this.SymbDataSet.CurrStkMeta.BeginDate.Value)
			{
				hisData = Class339.smethod_2(sortedList_0, dateTime_0);
				hisData = this.SymbDataSet.method_91(hisData);
			}
			else
			{
				hisData = new HisData();
				hisData.Date = dateTime_0;
				hisData.Close = 0.0;
				hisData.Volume = new double?(0.0);
				hisData.Amount = new double?(0.0);
			}
			return hisData;
		}

		// Token: 0x060017DB RID: 6107 RVA: 0x000A6294 File Offset: 0x000A4494
		public void method_21(HisData hisData_0)
		{
			this.method_22();
			foreach (ChartBase chartBase in this.ChartList)
			{
				foreach (CurveItem curveItem in chartBase.ZedGraphControl.GraphPane.CurveList)
				{
					curveItem.IsVisible = false;
				}
				foreach (GraphObj graphObj in chartBase.ZedGraphControl.GraphPane.GraphObjList)
				{
					graphObj.IsVisible = false;
				}
				chartBase.vmethod_22(hisData_0);
				string string_ = Class521.smethod_0(63835);
				if (!TApp.IsTrialUser && hisData_0 != null && hisData_0.Date < this.SymbDataSet.CurrStkMeta.BeginDate.Value)
				{
					string_ = Class521.smethod_0(63856);
				}
				chartBase.vmethod_13(string_);
				chartBase.ZedGraphControl.Refresh();
			}
		}

		// Token: 0x060017DC RID: 6108 RVA: 0x000A63E8 File Offset: 0x000A45E8
		public void method_22()
		{
			if (this.ChartList != null)
			{
				foreach (ChartBase chartBase in this.ChartList)
				{
					chartBase.vmethod_12();
					if (chartBase.ZedGraphControl.GraphPane != null)
					{
						foreach (CurveItem curveItem in chartBase.ZedGraphControl.GraphPane.CurveList)
						{
							if (!curveItem.IsVisible)
							{
								curveItem.IsVisible = true;
							}
						}
					}
					chartBase.method_5();
				}
			}
		}

		// Token: 0x060017DD RID: 6109 RVA: 0x00009C40 File Offset: 0x00007E40
		public virtual void vmethod_20()
		{
			this.ApplyTheme(Base.UI.Form.ChartTheme);
		}

		// Token: 0x060017DE RID: 6110 RVA: 0x000A64B0 File Offset: 0x000A46B0
		public virtual void ApplyTheme(ChartTheme theme)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.ApplyTheme(theme);
			}
			this.vmethod_21(theme);
		}

		// Token: 0x060017DF RID: 6111 RVA: 0x00009C54 File Offset: 0x00007E54
		public virtual void vmethod_21(ChartTheme chartTheme_0)
		{
			if (chartTheme_0 == ChartTheme.Classic)
			{
				this.method_23(Color.Black);
			}
			else if (chartTheme_0 == ChartTheme.Modern)
			{
				this.method_23(Class181.color_14);
			}
			else if (chartTheme_0 == ChartTheme.Yellow)
			{
				this.method_23(Class181.color_13);
			}
		}

		// Token: 0x060017E0 RID: 6112 RVA: 0x000A650C File Offset: 0x000A470C
		private void method_23(Color color_0)
		{
			if (!base.IsDisposed)
			{
				if (this.splitterPanel_0 != null && !this.splitterPanel_0.IsDisposed)
				{
					this.splitterPanel_0.BackColor = color_0;
				}
				this.BackColor = color_0;
				base.Panel1.BackColor = color_0;
				base.Panel2.BackColor = color_0;
				this.ChtSpC.Panel1.BackColor = color_0;
				this.ChtSpC.Panel2.BackColor = color_0;
			}
		}

		// Token: 0x060017E1 RID: 6113 RVA: 0x00009C88 File Offset: 0x00007E88
		public void method_24(ChartBase chartBase_0, bool bool_5)
		{
			if (chartBase_0 != null)
			{
				chartBase_0.IsXAxisVisible = bool_5;
				this.vmethod_33();
				chartBase_0.vmethod_3();
				chartBase_0.ApplyTheme(Base.UI.Form.ChartTheme);
			}
		}

		// Token: 0x060017E2 RID: 6114 RVA: 0x000A6588 File Offset: 0x000A4788
		public void method_25()
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.RevCrossYVal = null;
			}
		}

		// Token: 0x060017E3 RID: 6115 RVA: 0x000A65E4 File Offset: 0x000A47E4
		public void method_26()
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.method_61();
			}
		}

		// Token: 0x060017E4 RID: 6116 RVA: 0x000A6638 File Offset: 0x000A4838
		public virtual void vmethod_22()
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.method_67();
			}
		}

		// Token: 0x060017E5 RID: 6117 RVA: 0x000A668C File Offset: 0x000A488C
		public virtual void vmethod_23(bool bool_5)
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.method_66(bool_5);
			}
		}

		// Token: 0x060017E6 RID: 6118 RVA: 0x00009CB2 File Offset: 0x00007EB2
		protected virtual void vmethod_24(bool bool_5)
		{
			if (bool_5)
			{
				this.method_27();
			}
			else
			{
				this.method_28();
				if (this.IsInCrossReviewMode)
				{
					this.IsInCrossReviewMode = false;
					this.method_15();
				}
			}
		}

		// Token: 0x060017E7 RID: 6119 RVA: 0x00009CDC File Offset: 0x00007EDC
		public void method_27()
		{
			if (!base.IsDisposed)
			{
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					this.ContainerSpltPanel.BackColor = Class181.color_4;
				}
				else
				{
					this.ContainerSpltPanel.BackColor = Class181.color_8;
				}
			}
		}

		// Token: 0x060017E8 RID: 6120 RVA: 0x00009D18 File Offset: 0x00007F18
		public void method_28()
		{
			this.vmethod_21(Base.UI.Form.ChartTheme);
		}

		// Token: 0x060017E9 RID: 6121 RVA: 0x00009D2C File Offset: 0x00007F2C
		public virtual void vmethod_25(SplitContainer splitContainer_2, ChtCtrlParam chtCtrlParam_0)
		{
			this.method_29(splitContainer_2, chtCtrlParam_0);
			this.ChartList.Clear();
		}

		// Token: 0x060017EA RID: 6122 RVA: 0x00009D43 File Offset: 0x00007F43
		public void method_29(SplitContainer splitContainer_2, ChtCtrlParam chtCtrlParam_0)
		{
			if (splitContainer_2 != null)
			{
				if (chtCtrlParam_0.IsInParentSpContainerPanel1)
				{
					this.method_30(splitContainer_2.Panel1);
				}
				else
				{
					this.method_30(splitContainer_2.Panel2);
				}
			}
			this.ParentSpC = splitContainer_2;
		}

		// Token: 0x060017EB RID: 6123 RVA: 0x00009D73 File Offset: 0x00007F73
		private void method_30(SplitterPanel splitterPanel_1)
		{
			splitterPanel_1.Controls.Clear();
			splitterPanel_1.Controls.Add(this);
			this.ContainerSpltPanel = splitterPanel_1;
		}

		// Token: 0x060017EC RID: 6124 RVA: 0x000A66E0 File Offset: 0x000A48E0
		public virtual ChtCtrlParam vmethod_26()
		{
			ChtCtrlParam chtCtrlParam = new ChtCtrlParam();
			chtCtrlParam.HasParentSpContainer = false;
			chtCtrlParam.ParentSpContainerTag = Class521.smethod_0(1449);
			chtCtrlParam.IsInParentSpContainerPanel1 = true;
			if (base.Parent != null && base.Parent.GetType() == typeof(SplitterPanel))
			{
				SplitterPanel splitterPanel = (SplitterPanel)base.Parent;
				chtCtrlParam.HasParentSpContainer = true;
				chtCtrlParam.ParentSpContainerTag = splitterPanel.Parent.Tag.ToString();
				if (splitterPanel == ((SplitContainer)splitterPanel.Parent).Panel2)
				{
					chtCtrlParam.IsInParentSpContainerPanel1 = false;
				}
			}
			chtCtrlParam.IndexOfLastItemShown = this.IndexOfLastItemShown;
			chtCtrlParam.MaxSticksPerChart = this.MaxSticksPerChart;
			chtCtrlParam.PeriodType = this.HisDataPeriodSet.PeriodType;
			chtCtrlParam.PeriodUnits = this.HisDataPeriodSet.PeriodUnits;
			chtCtrlParam.Tag = base.Tag.ToString();
			chtCtrlParam.SpCParam_this = new SplitContainerParam
			{
				Panel2Collapsed = this.ChtSpC.Panel2Collapsed,
				Size = base.Size,
				SplitterDistance = this.ChtSpC.SplitterDistance
			};
			chtCtrlParam.IfNoSync = this.IfNoSync;
			chtCtrlParam.LinkedSymbolId = this.LinkedSymblId;
			chtCtrlParam.IsSwitchedBehind = this.IsSwitchedBehind;
			return chtCtrlParam;
		}

		// Token: 0x060017ED RID: 6125 RVA: 0x000239B0 File Offset: 0x00021BB0
		public virtual bool vmethod_27()
		{
			return true;
		}

		// Token: 0x060017EE RID: 6126 RVA: 0x000A6824 File Offset: 0x000A4A24
		protected virtual bool vmethod_28()
		{
			return this.class58_0.IsMouseEnter;
		}

		// Token: 0x060017EF RID: 6127 RVA: 0x000A6840 File Offset: 0x000A4A40
		protected virtual int vmethod_29()
		{
			int result;
			if (this.int_0 < this.int_1)
			{
				result = 0;
			}
			else
			{
				result = this.int_0 - this.int_1 + 1;
			}
			return result;
		}

		// Token: 0x060017F0 RID: 6128 RVA: 0x000A6874 File Offset: 0x000A4A74
		public void method_31()
		{
			if (this.Symbol.IsStock)
			{
				if (this.SymbDataSet.CurrStSplitList != null)
				{
					this.method_4();
					this.IsApplyingStkRationedPrc = true;
					this.HisDataPeriodSet.method_3();
					this.method_14();
					this.IsApplyingStkRationedPrc = false;
				}
				else if (this.SymbDataSet.CurrSymbStSpltList != null && this.SymbDataSet.CurrSymbStSpltList.Count > 0 && Base.UI.Form.IsSpanMovePrev && Base.UI.Form.LastSpanMoveDT != null && Base.UI.Form.LastSpanMoveDT.Value < this.SymbDataSet.CurrSymbStSpltList.First<StSplit>().Date)
				{
					this.method_13(Base.UI.Form.LastSpanMoveDT.Value);
				}
			}
		}

		// Token: 0x060017F1 RID: 6129 RVA: 0x000A6950 File Offset: 0x000A4B50
		public bool method_32(PeriodType? nullable_2, int? nullable_3)
		{
			bool result;
			try
			{
				if (nullable_2 != null && (this.PeriodType < nullable_2.Value || (this.PeriodType == nullable_2.Value && nullable_3 != null && ((this.PeriodUnits != null && this.PeriodUnits.Value < nullable_3.Value) || (this.PeriodUnits == null && this.PeriodUnits.Value > 1)))))
				{
					result = true;
					goto IL_89;
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return false;
			IL_89:
			return result;
		}

		// Token: 0x060017F2 RID: 6130 RVA: 0x000A69FC File Offset: 0x000A4BFC
		public bool method_33(ChtCtrl chtCtrl_0)
		{
			return this.method_32(new PeriodType?(chtCtrl_0.PeriodType), chtCtrl_0.PeriodUnits);
		}

		// Token: 0x060017F3 RID: 6131 RVA: 0x000A6A24 File Offset: 0x000A4C24
		public bool method_34(PeriodType? nullable_2, int? nullable_3)
		{
			if (nullable_2 != null && this.PeriodType == nullable_2.Value)
			{
				if (this.PeriodUnits != null || nullable_3 != null)
				{
					int? num;
					if (this.PeriodUnits == null)
					{
						num = nullable_3;
						if (num.GetValueOrDefault() == 1 & num != null)
						{
							goto IL_B5;
						}
					}
					num = this.PeriodUnits;
					if ((!(num.GetValueOrDefault() == 1 & num != null) || nullable_3 != null) && (this.PeriodUnits == null || nullable_3 == null || this.PeriodUnits.Value != nullable_3.Value))
					{
						goto IL_B9;
					}
				}
				IL_B5:
				return true;
			}
			IL_B9:
			return false;
		}

		// Token: 0x060017F4 RID: 6132 RVA: 0x000A6AF0 File Offset: 0x000A4CF0
		public bool method_35(ChtCtrl chtCtrl_0)
		{
			return this.method_34(new PeriodType?(chtCtrl_0.PeriodType), chtCtrl_0.PeriodUnits);
		}

		// Token: 0x060017F5 RID: 6133 RVA: 0x000A6B18 File Offset: 0x000A4D18
		public bool method_36(PeriodType? nullable_2, int? nullable_3)
		{
			bool result;
			if (nullable_2 != null && (this.PeriodType > nullable_2.Value || (this.PeriodType == nullable_2.Value && this.PeriodUnits != null && ((nullable_3 == null && this.PeriodUnits.Value > 1) || (nullable_3 != null && this.PeriodUnits.Value > nullable_3.Value)))))
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060017F6 RID: 6134 RVA: 0x000A6BA0 File Offset: 0x000A4DA0
		public bool method_37(ChtCtrl chtCtrl_0)
		{
			return this.method_36(new PeriodType?(chtCtrl_0.PeriodType), chtCtrl_0.PeriodUnits);
		}

		// Token: 0x170003E4 RID: 996
		// (get) Token: 0x060017F7 RID: 6135 RVA: 0x000A6BC8 File Offset: 0x000A4DC8
		// (set) Token: 0x060017F8 RID: 6136 RVA: 0x00009D95 File Offset: 0x00007F95
		public bool IsApplyingStkRationedPrc
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x170003E5 RID: 997
		// (get) Token: 0x060017F9 RID: 6137 RVA: 0x000A6BE0 File Offset: 0x000A4DE0
		// (set) Token: 0x060017FA RID: 6138 RVA: 0x00009DA0 File Offset: 0x00007FA0
		public SplitterPanel ContainerSpltPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
				this.splitterPanel_0.Padding = new Padding(1);
				this.vmethod_21(Base.UI.Form.ChartTheme);
			}
		}

		// Token: 0x170003E6 RID: 998
		// (get) Token: 0x060017FB RID: 6139 RVA: 0x000A6BF8 File Offset: 0x000A4DF8
		// (set) Token: 0x060017FC RID: 6140 RVA: 0x00009DCC File Offset: 0x00007FCC
		public SplitContainer ParentSpC
		{
			get
			{
				return this.splitContainer_0;
			}
			set
			{
				this.splitContainer_0 = value;
			}
		}

		// Token: 0x170003E7 RID: 999
		// (get) Token: 0x060017FD RID: 6141 RVA: 0x000A6C10 File Offset: 0x000A4E10
		// (set) Token: 0x060017FE RID: 6142 RVA: 0x00009DD7 File Offset: 0x00007FD7
		public SplitContainer ChtSpC
		{
			get
			{
				return this.splitContainer_1;
			}
			set
			{
				this.splitContainer_1 = value;
			}
		}

		// Token: 0x170003E8 RID: 1000
		// (get) Token: 0x060017FF RID: 6143 RVA: 0x000A6C28 File Offset: 0x000A4E28
		// (set) Token: 0x06001800 RID: 6144 RVA: 0x00009DE2 File Offset: 0x00007FE2
		public Control5 TickPanel
		{
			get
			{
				return this.control5_0;
			}
			set
			{
				this.control5_0 = value;
			}
		}

		// Token: 0x170003E9 RID: 1001
		// (get) Token: 0x06001801 RID: 6145 RVA: 0x000A6C40 File Offset: 0x000A4E40
		public SortedList<DateTime, HisData> HisDataList
		{
			get
			{
				SortedList<DateTime, HisData> result;
				if (this.SymbDataSet.CurrHisDataSet != null && this.SymbDataSet.CurrHisDataSet.FetchedHisDataList != null)
				{
					result = this.SymbDataSet.CurrHisDataSet.FetchedHisDataList;
				}
				else
				{
					result = this.PeriodHisDataList;
				}
				return result;
			}
		}

		// Token: 0x170003EA RID: 1002
		// (get) Token: 0x06001802 RID: 6146 RVA: 0x000A6C8C File Offset: 0x000A4E8C
		// (set) Token: 0x06001803 RID: 6147 RVA: 0x00009DED File Offset: 0x00007FED
		public HisDataPeriodSet HisDataPeriodSet
		{
			get
			{
				return this.hisDataPeriodSet_0;
			}
			set
			{
				this.hisDataPeriodSet_0 = value;
			}
		}

		// Token: 0x170003EB RID: 1003
		// (get) Token: 0x06001804 RID: 6148 RVA: 0x000A6CA4 File Offset: 0x000A4EA4
		// (set) Token: 0x06001805 RID: 6149 RVA: 0x00009DF8 File Offset: 0x00007FF8
		public int MaxSticksPerChart
		{
			get
			{
				return this.int_1;
			}
			set
			{
				this.int_1 = value;
			}
		}

		// Token: 0x170003EC RID: 1004
		// (get) Token: 0x06001806 RID: 6150 RVA: 0x000A6CBC File Offset: 0x000A4EBC
		public int IndexOfFirstItemShown
		{
			get
			{
				return this.vmethod_29();
			}
		}

		// Token: 0x170003ED RID: 1005
		// (get) Token: 0x06001807 RID: 6151 RVA: 0x000A6CD4 File Offset: 0x000A4ED4
		public HisData FirstItemShown
		{
			get
			{
				return this.HisDataPeriodSet.PeriodHisDataList.Values[this.IndexOfFirstItemShown];
			}
		}

		// Token: 0x170003EE RID: 1006
		// (get) Token: 0x06001808 RID: 6152 RVA: 0x000A6D00 File Offset: 0x000A4F00
		// (set) Token: 0x06001809 RID: 6153 RVA: 0x00009E03 File Offset: 0x00008003
		public int IndexOfLastItemShown
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170003EF RID: 1007
		// (get) Token: 0x0600180A RID: 6154 RVA: 0x000A6D18 File Offset: 0x000A4F18
		public HisData LastItemShown
		{
			get
			{
				HisData result;
				if (this.HisDataPeriodSet != null && this.HisDataPeriodSet.PeriodHisDataList != null && this.IndexOfLastItemShown >= 0 && this.IndexOfLastItemShown < this.HisDataPeriodSet.PeriodHisDataList.Count)
				{
					result = this.HisDataPeriodSet.PeriodHisDataList.Values[this.IndexOfLastItemShown];
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x170003F0 RID: 1008
		// (get) Token: 0x0600180B RID: 6155 RVA: 0x000A6D80 File Offset: 0x000A4F80
		public virtual DateTime CurrDateTime
		{
			get
			{
				return this.SymbDataSet.DateTimeOfLastRec;
			}
		}

		// Token: 0x170003F1 RID: 1009
		// (get) Token: 0x0600180C RID: 6156 RVA: 0x000A6D9C File Offset: 0x000A4F9C
		public int IndexOfLastItemShownInScr
		{
			get
			{
				return this.IndexOfLastItemShown - this.IndexOfFirstItemShown;
			}
		}

		// Token: 0x170003F2 RID: 1010
		// (get) Token: 0x0600180D RID: 6157 RVA: 0x000A6DBC File Offset: 0x000A4FBC
		public PeriodType PeriodType
		{
			get
			{
				return this.hisDataPeriodSet_0.PeriodType;
			}
		}

		// Token: 0x170003F3 RID: 1011
		// (get) Token: 0x0600180E RID: 6158 RVA: 0x000A6DD8 File Offset: 0x000A4FD8
		public int? PeriodUnits
		{
			get
			{
				return this.hisDataPeriodSet_0.PeriodUnits;
			}
		}

		// Token: 0x170003F4 RID: 1012
		// (get) Token: 0x0600180F RID: 6159 RVA: 0x000A6DF4 File Offset: 0x000A4FF4
		public bool IsPeriodLong
		{
			get
			{
				return this.hisDataPeriodSet_0.IsPeriodLong;
			}
		}

		// Token: 0x170003F5 RID: 1013
		// (get) Token: 0x06001810 RID: 6160 RVA: 0x000A6E10 File Offset: 0x000A5010
		public bool IsNMinsPeriod
		{
			get
			{
				return this.HisDataPeriodSet.IsNMinsPeriod;
			}
		}

		// Token: 0x170003F6 RID: 1014
		// (get) Token: 0x06001811 RID: 6161 RVA: 0x000A6E2C File Offset: 0x000A502C
		// (set) Token: 0x06001812 RID: 6162 RVA: 0x000A6E44 File Offset: 0x000A5044
		public bool IsInCrossReviewMode
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
				this.vmethod_23(value);
				if (!value)
				{
					this.RevCrossXVal = null;
				}
			}
		}

		// Token: 0x170003F7 RID: 1015
		// (get) Token: 0x06001813 RID: 6163 RVA: 0x000A6E74 File Offset: 0x000A5074
		public List<ChartBase> ChartList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x170003F8 RID: 1016
		// (get) Token: 0x06001814 RID: 6164 RVA: 0x000A6E8C File Offset: 0x000A508C
		public int NumberOfCharts
		{
			get
			{
				return this.ChartList.Count;
			}
		}

		// Token: 0x170003F9 RID: 1017
		// (get) Token: 0x06001815 RID: 6165 RVA: 0x000A6EA8 File Offset: 0x000A50A8
		// (set) Token: 0x06001816 RID: 6166 RVA: 0x00009E0E File Offset: 0x0000800E
		public double? RevCrossXVal
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x170003FA RID: 1018
		// (get) Token: 0x06001817 RID: 6167 RVA: 0x000A6EC0 File Offset: 0x000A50C0
		// (set) Token: 0x06001818 RID: 6168 RVA: 0x000A6ED8 File Offset: 0x000A50D8
		public bool IsSelected
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				if (this.bool_0 != value)
				{
					if (value)
					{
						foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
						{
							chtCtrl.IsSelected = false;
						}
					}
					this.vmethod_24(value);
					this.bool_0 = value;
					base.Invalidate();
				}
			}
		}

		// Token: 0x170003FB RID: 1019
		// (get) Token: 0x06001819 RID: 6169 RVA: 0x000A6F4C File Offset: 0x000A514C
		public ChartBase ChartWithXAxisShown
		{
			get
			{
				return this.ChartList.FirstOrDefault(new Func<ChartBase, bool>(ChtCtrl.<>c.<>9.method_0));
			}
		}

		// Token: 0x170003FC RID: 1020
		// (get) Token: 0x0600181A RID: 6170 RVA: 0x000A6F88 File Offset: 0x000A5188
		public bool ChartsHasRevCrossYVal
		{
			get
			{
				bool result;
				using (List<ChartBase>.Enumerator enumerator = this.ChartList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (enumerator.Current.RevCrossYVal != null)
						{
							result = true;
							goto IL_46;
						}
					}
				}
				return false;
				IL_46:
				return result;
			}
		}

		// Token: 0x170003FD RID: 1021
		// (get) Token: 0x0600181B RID: 6171 RVA: 0x000A6FF0 File Offset: 0x000A51F0
		public int ChartRect_LeftMargin
		{
			get
			{
				return this.int_2;
			}
		}

		// Token: 0x170003FE RID: 1022
		// (get) Token: 0x0600181C RID: 6172 RVA: 0x000A7008 File Offset: 0x000A5208
		public int ChartBottom_DTLabel_Height
		{
			get
			{
				return this.int_3;
			}
		}

		// Token: 0x170003FF RID: 1023
		// (get) Token: 0x0600181D RID: 6173 RVA: 0x000A7020 File Offset: 0x000A5220
		public bool IsMouseEntered
		{
			get
			{
				return base.ClientRectangle.Contains(base.PointToClient(Control.MousePosition));
			}
		}

		// Token: 0x17000400 RID: 1024
		// (get) Token: 0x0600181E RID: 6174 RVA: 0x000A704C File Offset: 0x000A524C
		public bool IsLastItemShown
		{
			get
			{
				return this.vmethod_27();
			}
		}

		// Token: 0x17000401 RID: 1025
		// (get) Token: 0x0600181F RID: 6175 RVA: 0x000A7064 File Offset: 0x000A5264
		public bool IsAnyChtCtrlBtnEntered
		{
			get
			{
				return this.vmethod_28();
			}
		}

		// Token: 0x17000402 RID: 1026
		// (get) Token: 0x06001820 RID: 6176 RVA: 0x000A707C File Offset: 0x000A527C
		// (set) Token: 0x06001821 RID: 6177 RVA: 0x000A7094 File Offset: 0x000A5294
		public bool IsSwitchedBehind
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				if (this.bool_2 != value)
				{
					if (value)
					{
						this.IsSelected = false;
						base.Visible = false;
					}
					else
					{
						this.ContainerSpltPanel.Controls.Clear();
						this.ContainerSpltPanel.Controls.Add(this);
						this.IsSelected = true;
						base.Visible = true;
					}
					this.bool_2 = value;
				}
			}
		}

		// Token: 0x17000403 RID: 1027
		// (get) Token: 0x06001822 RID: 6178 RVA: 0x000A70F8 File Offset: 0x000A52F8
		// (set) Token: 0x06001823 RID: 6179 RVA: 0x00009E19 File Offset: 0x00008019
		public bool IsReverse
		{
			get
			{
				return this.vmethod_30();
			}
			set
			{
				this.vmethod_31(value);
			}
		}

		// Token: 0x06001824 RID: 6180 RVA: 0x000A7110 File Offset: 0x000A5310
		protected virtual bool vmethod_30()
		{
			bool result;
			if (this.MainChart != null)
			{
				result = this.MainChart.GraphPane.YAxis.Scale.IsReverse;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001825 RID: 6181 RVA: 0x000A7148 File Offset: 0x000A5348
		protected virtual void vmethod_31(bool bool_5)
		{
			if (this.MainChart != null)
			{
				if (bool_5)
				{
					if (!this.MainChart.HeaderTextObj.Text.EndsWith(ChtCtrl.string_0))
					{
						TextObj headerTextObj = this.MainChart.HeaderTextObj;
						headerTextObj.Text += ChtCtrl.string_0;
					}
				}
				else if (this.MainChart.HeaderTextObj.Text.EndsWith(ChtCtrl.string_0))
				{
					this.MainChart.HeaderTextObj.Text = this.MainChart.HeaderTextObj.Text.Replace(ChtCtrl.string_0, Class521.smethod_0(1449));
				}
				this.method_38(this.MainChart, bool_5);
			}
		}

		// Token: 0x06001826 RID: 6182 RVA: 0x00009E24 File Offset: 0x00008024
		protected void method_38(ChartBase chartBase_0, bool bool_5)
		{
			if (chartBase_0 != null && chartBase_0.GraphPane.YAxis.Scale.IsReverse != bool_5)
			{
				chartBase_0.GraphPane.YAxis.Scale.IsReverse = bool_5;
				chartBase_0.ZedGraphControl.Refresh();
			}
		}

		// Token: 0x17000404 RID: 1028
		// (get) Token: 0x06001827 RID: 6183 RVA: 0x000A7200 File Offset: 0x000A5400
		// (set) Token: 0x06001828 RID: 6184 RVA: 0x00009E64 File Offset: 0x00008064
		public bool IfShowTickPanel
		{
			get
			{
				bool result;
				if (base.Panel2.Enabled && this.TickPanel != null)
				{
					result = this.TickPanel.Visible;
				}
				else
				{
					result = false;
				}
				return result;
			}
			set
			{
				this.method_39(value);
			}
		}

		// Token: 0x06001829 RID: 6185 RVA: 0x000A7238 File Offset: 0x000A5438
		private void method_39(bool bool_5)
		{
			if (bool_5)
			{
				if (!base.Panel2.Enabled)
				{
					base.Panel2Collapsed = false;
					base.FixedPanel = FixedPanel.Panel2;
					base.Panel2.Enabled = true;
					base.Panel2.Show();
					this.ChtCtrl_Resize(null, null);
				}
				if (this.TickPanel == null)
				{
					this.TickPanel = new Control5();
					base.Panel2.Controls.Add(this.TickPanel);
				}
				else
				{
					if (!this.TickPanel.Enabled)
					{
						this.TickPanel.Enabled = true;
					}
					if (!this.TickPanel.Visible)
					{
						this.TickPanel.Visible = true;
					}
				}
			}
			else
			{
				if (this.TickPanel != null)
				{
					this.TickPanel = null;
				}
				if (base.Panel2.Enabled)
				{
					base.Panel2.Enabled = false;
					base.Panel2.Hide();
					base.FixedPanel = FixedPanel.None;
					base.Panel2Collapsed = true;
					this.ChtCtrl_Resize(null, null);
				}
			}
		}

		// Token: 0x17000405 RID: 1029
		// (get) Token: 0x0600182A RID: 6186 RVA: 0x000A7330 File Offset: 0x000A5530
		public virtual ChartBase MainChart
		{
			get
			{
				return this.ChartList[0];
			}
		}

		// Token: 0x17000406 RID: 1030
		// (get) Token: 0x0600182B RID: 6187 RVA: 0x000A7350 File Offset: 0x000A5550
		public SortedList<DateTime, HisData> PeriodHisDataList
		{
			get
			{
				SortedList<DateTime, HisData> result;
				if (this.HisDataPeriodSet != null)
				{
					result = this.HisDataPeriodSet.PeriodHisDataList;
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x0600182C RID: 6188 RVA: 0x000A7378 File Offset: 0x000A5578
		public SortedList<DateTime, HisData> method_40(DateTime dateTime_0, ref bool bool_5)
		{
			bool_5 = false;
			if (Base.UI.Form.IsJustSpanMoved && Base.UI.Form.LastSpanMoveDT != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				if (selectedChtCtrl != null)
				{
					if (selectedChtCtrl.IsPeriodLong)
					{
						if (this.IsPeriodLong)
						{
							return this.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList;
						}
					}
					else if (selectedChtCtrl.SymbDataSet == this.SymbDataSet && (this.IsPeriodLong || (this.PeriodType == PeriodType.ByMins && this.PeriodUnits.Value >= selectedChtCtrl.PeriodUnits.Value && Utility.CanExactDiv(this.PeriodUnits.Value, selectedChtCtrl.PeriodUnits.Value))))
					{
						bool_5 = true;
						return selectedChtCtrl.PeriodHisDataList;
					}
				}
			}
			SortedList<DateTime, HisData> result = this.SymbDataSet.CurrHisDataSet.FetchedHisDataList;
			if (dateTime_0 > this.SymbDataSet.CurrHisDataSet.FetchedHisDataList.Keys.Last<DateTime>() || dateTime_0 <= this.SymbDataSet.CurrHisDataSet.FetchedHisDataList.Keys.First<DateTime>())
			{
				result = this.HisDataList;
			}
			return result;
		}

		// Token: 0x0600182D RID: 6189 RVA: 0x000A74B8 File Offset: 0x000A56B8
		public int? method_41()
		{
			if (Base.UI.Form.IsJustSpanMoved && Base.UI.Form.LastSpanMoveDT != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				if (selectedChtCtrl != null)
				{
					if (selectedChtCtrl.IsPeriodLong)
					{
						if (this.IsPeriodLong)
						{
							return new int?(60);
						}
					}
					else if (this.IsPeriodLong || (this.PeriodType == PeriodType.ByMins && this.PeriodUnits.Value > selectedChtCtrl.PeriodUnits.Value && Utility.CanExactDiv(this.PeriodUnits.Value, selectedChtCtrl.PeriodUnits.Value)))
					{
						return selectedChtCtrl.PeriodUnits;
					}
				}
			}
			return new int?(1);
		}

		// Token: 0x17000407 RID: 1031
		// (get) Token: 0x0600182E RID: 6190 RVA: 0x000A7570 File Offset: 0x000A5770
		// (set) Token: 0x0600182F RID: 6191 RVA: 0x00009E6F File Offset: 0x0000806F
		public bool IfNoSync { get; set; }

		// Token: 0x17000408 RID: 1032
		// (get) Token: 0x06001830 RID: 6192 RVA: 0x000A7588 File Offset: 0x000A5788
		// (set) Token: 0x06001831 RID: 6193 RVA: 0x00009E7A File Offset: 0x0000807A
		public int? LinkedSymblId
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x17000409 RID: 1033
		// (get) Token: 0x06001832 RID: 6194 RVA: 0x000A75A0 File Offset: 0x000A57A0
		public StkSymbol LinkedSymbol
		{
			get
			{
				StkSymbol result = null;
				if (this.LinkedSymblId != null)
				{
					result = Base.Acct.smethod_48(this.LinkedSymblId.Value);
				}
				return result;
			}
		}

		// Token: 0x1700040A RID: 1034
		// (get) Token: 0x06001833 RID: 6195 RVA: 0x000A75D8 File Offset: 0x000A57D8
		// (set) Token: 0x06001834 RID: 6196 RVA: 0x00009E85 File Offset: 0x00008085
		public SymbDataSet SymbDataSet
		{
			get
			{
				return this.symbDataSet_0;
			}
			set
			{
				if (this.symbDataSet_0 != value)
				{
					this.method_0(value);
					this.symbDataSet_0 = value;
					this.method_1(value);
				}
			}
		}

		// Token: 0x1700040B RID: 1035
		// (get) Token: 0x06001835 RID: 6197 RVA: 0x000A75F0 File Offset: 0x000A57F0
		public StkSymbol Symbol
		{
			get
			{
				return this.SymbDataSet.CurrSymbol;
			}
		}

		// Token: 0x1700040C RID: 1036
		// (get) Token: 0x06001836 RID: 6198 RVA: 0x000A760C File Offset: 0x000A580C
		public int NbOfPtsDisplayedInChart
		{
			get
			{
				return this.vmethod_1();
			}
		}

		// Token: 0x06001837 RID: 6199 RVA: 0x000A7624 File Offset: 0x000A5824
		public bool method_42()
		{
			bool result;
			if (this.SymbDataSet != null)
			{
				result = (this.SymbDataSet.CurrHisData != null);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001838 RID: 6200 RVA: 0x00009EA7 File Offset: 0x000080A7
		protected void ChtCtrl_Resize(object sender, EventArgs e)
		{
			this.vmethod_33();
			this.method_44();
			this.method_43();
		}

		// Token: 0x06001839 RID: 6201 RVA: 0x00009EBD File Offset: 0x000080BD
		protected virtual void vmethod_32(object sender, SplitterEventArgs e)
		{
			this.vmethod_33();
		}

		// Token: 0x0600183A RID: 6202 RVA: 0x00009EC7 File Offset: 0x000080C7
		public virtual void vmethod_33()
		{
			this.int_2 = 55 * this.ChtSpC.Width / 1000 + 30;
			this.int_3 = 6 * this.ChtSpC.Height / 150 + 8;
		}

		// Token: 0x0600183B RID: 6203 RVA: 0x00009F03 File Offset: 0x00008103
		private void method_43()
		{
			if (base.Panel2.Enabled && this.TickPanel != null)
			{
				this.TickPanel.method_4(base.Height - this.ChartBottom_DTLabel_Height + 1);
			}
		}

		// Token: 0x0600183C RID: 6204 RVA: 0x000A7650 File Offset: 0x000A5850
		public void method_44()
		{
			foreach (ChartBase chartBase in this.ChartList)
			{
				chartBase.vmethod_15();
			}
		}

		// Token: 0x0600183D RID: 6205 RVA: 0x000A76A4 File Offset: 0x000A58A4
		protected void method_45(object sender, EventArgs e)
		{
			Delegate15 method = new Delegate15(this.vmethod_34);
			base.Invoke(method);
		}

		// Token: 0x0600183E RID: 6206 RVA: 0x00009F36 File Offset: 0x00008136
		protected void method_46(object sender, EventArgs e)
		{
			this.timer_0.Start();
		}

		// Token: 0x0600183F RID: 6207 RVA: 0x000A76A4 File Offset: 0x000A58A4
		protected void method_47(object sender, EventArgs e)
		{
			Delegate15 method = new Delegate15(this.vmethod_34);
			base.Invoke(method);
		}

		// Token: 0x06001840 RID: 6208 RVA: 0x00009F45 File Offset: 0x00008145
		protected virtual void vmethod_34()
		{
			if (!this.class58_0.Visible)
			{
				this.class58_0.Visible = true;
			}
		}

		// Token: 0x06001841 RID: 6209 RVA: 0x00009F62 File Offset: 0x00008162
		protected virtual void timer_0_Tick(object sender, EventArgs e)
		{
			if (!this.IsAnyChtCtrlBtnEntered)
			{
				this.class58_0.Visible = false;
			}
			this.timer_0.Stop();
		}

		// Token: 0x06001842 RID: 6210 RVA: 0x000A76CC File Offset: 0x000A58CC
		public bool method_48(PeriodType periodType_0, int? nullable_2)
		{
			return this.HisDataPeriodSet.method_42(periodType_0, nullable_2);
		}

		// Token: 0x06001843 RID: 6211 RVA: 0x00009F85 File Offset: 0x00008185
		public void Dispose()
		{
			this.Dispose(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x06001844 RID: 6212 RVA: 0x000A76EC File Offset: 0x000A58EC
		~ChtCtrl()
		{
			this.Dispose(false);
		}

		// Token: 0x06001845 RID: 6213 RVA: 0x000A771C File Offset: 0x000A591C
		protected new virtual void Dispose(bool disposing)
		{
			if (disposing)
			{
				foreach (ChartBase chartBase in this.ChartList)
				{
					chartBase.System.IDisposable.Dispose();
				}
			}
		}

		// Token: 0x04000C14 RID: 3092
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000C15 RID: 3093
		private SplitContainer splitContainer_0;

		// Token: 0x04000C16 RID: 3094
		private SplitContainer splitContainer_1;

		// Token: 0x04000C17 RID: 3095
		private List<ChartBase> list_0 = new List<ChartBase>();

		// Token: 0x04000C18 RID: 3096
		private Control5 control5_0;

		// Token: 0x04000C19 RID: 3097
		private HisDataPeriodSet hisDataPeriodSet_0;

		// Token: 0x04000C1A RID: 3098
		private int int_0;

		// Token: 0x04000C1B RID: 3099
		private int int_1;

		// Token: 0x04000C1C RID: 3100
		private bool bool_0;

		// Token: 0x04000C1D RID: 3101
		private bool bool_1;

		// Token: 0x04000C1E RID: 3102
		private bool bool_2;

		// Token: 0x04000C1F RID: 3103
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x04000C20 RID: 3104
		private Class58 class58_0;

		// Token: 0x04000C21 RID: 3105
		private int int_2 = 55;

		// Token: 0x04000C22 RID: 3106
		private int int_3 = 22;

		// Token: 0x04000C23 RID: 3107
		public static readonly string string_0 = Class521.smethod_0(63869);

		// Token: 0x04000C24 RID: 3108
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000C25 RID: 3109
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000C26 RID: 3110
		[CompilerGenerated]
		private EventHandler eventHandler_2;

		// Token: 0x04000C27 RID: 3111
		[CompilerGenerated]
		private EventHandler eventHandler_3;

		// Token: 0x04000C28 RID: 3112
		[CompilerGenerated]
		private EventHandler eventHandler_4;

		// Token: 0x04000C29 RID: 3113
		private bool bool_3;

		// Token: 0x04000C2A RID: 3114
		private double? nullable_0;

		// Token: 0x04000C2B RID: 3115
		[CompilerGenerated]
		private bool bool_4;

		// Token: 0x04000C2C RID: 3116
		private int? nullable_1;

		// Token: 0x04000C2D RID: 3117
		private SymbDataSet symbDataSet_0;

		// Token: 0x02000239 RID: 569
		[CompilerGenerated]
		private sealed class Class309
		{
			// Token: 0x06001848 RID: 6216 RVA: 0x000A7774 File Offset: 0x000A5974
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1.Year == this.dateTime_0.Year;
			}

			// Token: 0x04000C2E RID: 3118
			public DateTime dateTime_0;
		}
	}
}
