﻿using System;
using ns18;
using ns2;
using TEx.ImportTrans.Captcha;

namespace ns25
{
	// Token: 0x0200037F RID: 895
	internal static class Class486
	{
		// Token: 0x060024FE RID: 9470 RVA: 0x0010002C File Offset: 0x000FE22C
		public static Class485 smethod_0(string string_0)
		{
			Class485 result;
			if (string_0 == Class521.smethod_0(109723))
			{
				result = new NaiveBeyes(25);
			}
			else
			{
				if (!(string_0 == Class521.smethod_0(108800)))
				{
					throw new Exception(Class521.smethod_0(109732));
				}
				result = new LevenClassifer();
			}
			return result;
		}
	}
}
