﻿using System;

namespace TEx.ImportTrans
{
	// Token: 0x02000356 RID: 854
	public sealed class BindingAcct
	{
		// Token: 0x060023AE RID: 9134 RVA: 0x00002D25 File Offset: 0x00000F25
		public BindingAcct()
		{
		}

		// Token: 0x060023AF RID: 9135 RVA: 0x0000DFCE File Offset: 0x0000C1CE
		public BindingAcct(string usrName, int id) : this()
		{
			this.string_0 = usrName;
			this.int_0 = id;
		}

		// Token: 0x060023B0 RID: 9136 RVA: 0x0000DFE6 File Offset: 0x0000C1E6
		public BindingAcct(string usrName, int id, DateTime? beginDate, DateTime? endDate) : this(usrName, id)
		{
			this.nullable_0 = beginDate;
			this.nullable_1 = endDate;
		}

		// Token: 0x17000620 RID: 1568
		// (get) Token: 0x060023B1 RID: 9137 RVA: 0x000F9CF4 File Offset: 0x000F7EF4
		// (set) Token: 0x060023B2 RID: 9138 RVA: 0x0000E001 File Offset: 0x0000C201
		public string UsrName
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x17000621 RID: 1569
		// (get) Token: 0x060023B3 RID: 9139 RVA: 0x000F9D0C File Offset: 0x000F7F0C
		// (set) Token: 0x060023B4 RID: 9140 RVA: 0x0000E00C File Offset: 0x0000C20C
		public int Id
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x17000622 RID: 1570
		// (get) Token: 0x060023B5 RID: 9141 RVA: 0x000F9D24 File Offset: 0x000F7F24
		// (set) Token: 0x060023B6 RID: 9142 RVA: 0x0000E017 File Offset: 0x0000C217
		public DateTime? BeginDate
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x17000623 RID: 1571
		// (get) Token: 0x060023B7 RID: 9143 RVA: 0x000F9D3C File Offset: 0x000F7F3C
		// (set) Token: 0x060023B8 RID: 9144 RVA: 0x0000E022 File Offset: 0x0000C222
		public DateTime? EndDate
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x0400113A RID: 4410
		private string string_0;

		// Token: 0x0400113B RID: 4411
		private int int_0;

		// Token: 0x0400113C RID: 4412
		private DateTime? nullable_0;

		// Token: 0x0400113D RID: 4413
		private DateTime? nullable_1;
	}
}
