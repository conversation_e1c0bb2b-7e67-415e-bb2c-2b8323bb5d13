﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns13;
using ns18;
using ns24;
using ns26;
using ns31;
using ns4;
using ns7;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020002AD RID: 685
	[Docking(DockingBehavior.AutoDock)]
	public sealed class TrdAnalysisPanel : UserControl
	{
		// Token: 0x06001E3E RID: 7742 RVA: 0x000D46D4 File Offset: 0x000D28D4
		public TrdAnalysisPanel()
		{
			this.method_45();
			this.tabControlPanel_0.ThemeAware = false;
			this.tabControlPanel_1.ThemeAware = false;
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_3.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_2.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_4.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_5.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_6.Padding = new System.Windows.Forms.Padding(0);
			this.panel_0.BorderStyle = BorderStyle.None;
			this.panel_1.BorderStyle = BorderStyle.None;
			this.comboBox_1.SelectedIndexChanged += this.comboBox_1_SelectedIndexChanged;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			this.comboBox_3.SelectedIndexChanged += this.comboBox_3_SelectedIndexChanged;
			this.comboBox_2.SelectedIndexChanged += this.comboBox_2_SelectedIndexChanged;
			this.method_3();
			this.method_0();
			Base.Trading.TransCreated += this.method_42;
			Base.Trading.TransactionsUpdated += this.method_43;
			Base.Acct.AccountChanged += this.method_41;
			Base.UI.ChartThemeChanged += this.method_2;
			this.control11_0.SelectedTabChanging += this.control11_0_SelectedTabChanging;
			base.Disposed += this.TrdAnalysisPanel_Disposed;
		}

		// Token: 0x06001E3F RID: 7743 RVA: 0x000D4A60 File Offset: 0x000D2C60
		private void TrdAnalysisPanel_Disposed(object sender, EventArgs e)
		{
			this.comboBox_0.SelectedIndexChanged -= this.comboBox_0_SelectedIndexChanged;
			this.comboBox_3.SelectedIndexChanged -= this.comboBox_3_SelectedIndexChanged;
			this.comboBox_2.SelectedIndexChanged -= this.comboBox_2_SelectedIndexChanged;
			Base.Trading.TransCreated -= this.method_42;
			Base.Trading.TransactionsUpdated -= this.method_43;
			Base.UI.ChartThemeChanged -= this.method_2;
			this.control11_0.SelectedTabChanging -= this.control11_0_SelectedTabChanging;
			this.control11_0 = null;
		}

		// Token: 0x06001E40 RID: 7744 RVA: 0x000D4B08 File Offset: 0x000D2D08
		private void method_0()
		{
			float emSize = TApp.smethod_4(9f, false);
			this.font_0 = new Font(Class521.smethod_0(24023), emSize, FontStyle.Bold);
			this.font_1 = new Font(Class521.smethod_0(24023), emSize, FontStyle.Regular);
			this.control11_0.Font = this.font_1;
			this.control11_0.SelectedTabFont = this.font_1;
			foreach (object obj in this.panel_0.Controls)
			{
				((System.Windows.Forms.Label)obj).Font = this.font_1;
			}
			foreach (object obj2 in this.class298_2.Controls)
			{
				((System.Windows.Forms.Label)obj2).Font = this.font_1;
			}
			emSize = TApp.smethod_4(8.25f, false);
			Font font = new Font(Class521.smethod_0(24023), emSize);
			foreach (object obj3 in this.class298_0.Controls)
			{
				Control control = (Control)obj3;
				if (control is ComboBox)
				{
					control.Font = font;
				}
				else
				{
					control.Font = this.font_1;
				}
			}
		}

		// Token: 0x06001E41 RID: 7745 RVA: 0x0000CAC8 File Offset: 0x0000ACC8
		public void method_1(ContextMenuStrip contextMenuStrip_0)
		{
			this.tabControlPanel_0.ContextMenuStrip = contextMenuStrip_0;
			this.tabControlPanel_1.ContextMenuStrip = contextMenuStrip_0;
		}

		// Token: 0x06001E42 RID: 7746 RVA: 0x0000CAE4 File Offset: 0x0000ACE4
		private void method_2(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06001E43 RID: 7747 RVA: 0x0000CAEE File Offset: 0x0000ACEE
		public void method_3()
		{
			this.method_4();
		}

		// Token: 0x06001E44 RID: 7748 RVA: 0x000D4CA0 File Offset: 0x000D2EA0
		private void method_4()
		{
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.panel_0.BackColor = Class181.color_4;
				this.label_1.ForeColor = Class181.color_10;
				this.label_5.ForeColor = Class181.color_10;
				this.label_3.ForeColor = Class181.color_10;
				this.label_7.ForeColor = Class181.color_10;
				this.label_9.ForeColor = Class181.color_10;
				this.label_11.ForeColor = Class181.color_10;
				this.label_13.ForeColor = Class181.color_10;
				this.label_15.ForeColor = Class181.color_10;
				this.label_14.ForeColor = Class181.color_10;
				this.label_10.ForeColor = Class181.color_10;
				this.label_12.ForeColor = Class181.color_10;
				this.label_4.ForeColor = Class181.color_10;
				this.label_6.ForeColor = Class181.color_10;
				this.label_0.ForeColor = Class181.color_10;
				this.panel_1.BackColor = Class181.color_4;
			}
			else
			{
				this.panel_0.BackColor = Class181.color_9;
				this.label_1.ForeColor = Class181.color_1;
				this.label_5.ForeColor = Class181.color_1;
				this.label_3.ForeColor = Class181.color_1;
				this.label_7.ForeColor = Class181.color_1;
				this.label_9.ForeColor = Class181.color_1;
				this.label_11.ForeColor = Class181.color_1;
				this.label_13.ForeColor = Class181.color_1;
				this.label_15.ForeColor = Class181.color_1;
				this.label_14.ForeColor = Class181.color_1;
				this.label_10.ForeColor = Class181.color_1;
				this.label_12.ForeColor = Class181.color_1;
				this.label_4.ForeColor = Class181.color_1;
				this.label_6.ForeColor = Class181.color_1;
				this.label_0.ForeColor = Class181.color_1;
				this.panel_1.BackColor = Class181.color_9;
			}
		}

		// Token: 0x06001E45 RID: 7749 RVA: 0x0000CAF8 File Offset: 0x0000ACF8
		private void control11_0_SelectedTabChanging(object sender, TabStripTabChangingEventArgs e)
		{
			this.method_5(e.NewTab);
		}

		// Token: 0x06001E46 RID: 7750 RVA: 0x000D4EC4 File Offset: 0x000D30C4
		private void method_5(TabItem tabItem_7)
		{
			string name = tabItem_7.Name;
			uint num = Class511.smethod_0(name);
			if (num <= 870461273U)
			{
				if (num != 364281376U)
				{
					if (num != 802314360U)
					{
						if (num == 870461273U)
						{
							if (name == Class521.smethod_0(86831))
							{
								this.method_7();
							}
						}
					}
					else if (name == Class521.smethod_0(86848))
					{
						this.method_10();
					}
				}
				else if (name == Class521.smethod_0(86932))
				{
					this.method_31();
				}
			}
			else if (num <= 2170067807U)
			{
				if (num != 2059299811U)
				{
					if (num == 2170067807U)
					{
						if (name == Class521.smethod_0(86865))
						{
							this.method_16();
						}
					}
				}
				else if (name == Class521.smethod_0(86957))
				{
					this.method_32();
				}
			}
			else if (num != 2758157361U)
			{
				if (num == 3147530452U)
				{
					if (name == Class521.smethod_0(86890))
					{
						this.method_21();
					}
				}
			}
			else if (name == Class521.smethod_0(86911))
			{
				this.method_29();
			}
		}

		// Token: 0x06001E47 RID: 7751 RVA: 0x0000CB08 File Offset: 0x0000AD08
		public void method_6()
		{
			this.method_5(this.control11_0.SelectedTab);
		}

		// Token: 0x06001E48 RID: 7752 RVA: 0x000D500C File Offset: 0x000D320C
		public void method_7()
		{
			this.method_38(this.label_14, Base.Acct.CurrAccount.AcctName.Trim());
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			this.method_38(this.label_10, iniBal.ToString(Class521.smethod_0(84628)));
			decimal num = Base.Acct.smethod_24();
			this.method_8(this.label_8, num);
			if (TApp.SrvParams.TExPkg.Value != TExPackage.SVIP_St)
			{
				this.method_38(this.label_0, Base.Trading.smethod_206().ToString(Class521.smethod_0(84628)));
			}
			else
			{
				this.label_15.Visible = false;
				this.label_0.Visible = false;
			}
			IQueryable<Transaction> iqueryable_ = Base.Trading.smethod_136();
			decimal num2 = Base.Trading.smethod_177(iqueryable_);
			decimal d = Base.Trading.smethod_181(iqueryable_);
			decimal num3 = iniBal + num2 - d;
			decimal num4 = num3 + num;
			decimal? endingBal = Base.Acct.CurrAccount.EndingBal;
			decimal d2 = num3;
			if (!(endingBal.GetValueOrDefault() == d2 & endingBal != null))
			{
				Base.Acct.CurrAccount.EndingBal = new decimal?(num3);
				Base.Acct.smethod_7();
			}
			this.method_38(this.label_12, num4.ToString(Class521.smethod_0(84628)));
			this.method_38(this.label_6, d.ToString(Class521.smethod_0(84628)));
			this.method_38(this.label_4, Base.Acct.smethod_21(num3, Base.Acct.CurrAccount.ID, true).ToString(Class521.smethod_0(84628)));
			decimal decimal_ = num2 - d;
			this.method_8(this.label_2, decimal_);
		}

		// Token: 0x06001E49 RID: 7753 RVA: 0x000D51B4 File Offset: 0x000D33B4
		private void method_8(Control control_0, decimal decimal_0)
		{
			string string_ = decimal_0.ToString(Class521.smethod_0(84628));
			this.method_38(control_0, string_);
			this.method_9(control_0, string_);
		}

		// Token: 0x06001E4A RID: 7754 RVA: 0x000D51E8 File Offset: 0x000D33E8
		private void method_9(Control control_0, string string_0)
		{
			double num = Convert.ToDouble(string_0.Replace(Class521.smethod_0(5356), Class521.smethod_0(1449)).Replace(Class521.smethod_0(66562), Class521.smethod_0(1449)).Replace(Class521.smethod_0(86978), Class521.smethod_0(1449)).Replace(Class521.smethod_0(86983), Class521.smethod_0(1449)));
			if (num > 0.0)
			{
				this.method_39(control_0, Class181.color_20);
			}
			else if (num < 0.0)
			{
				this.method_39(control_0, Class181.color_24);
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.method_39(control_0, Class181.color_10);
			}
			else
			{
				this.method_39(control_0, Class181.color_1);
			}
		}

		// Token: 0x06001E4B RID: 7755 RVA: 0x0000CB1D File Offset: 0x0000AD1D
		private void method_10()
		{
			this.method_11(true);
		}

		// Token: 0x06001E4C RID: 7756 RVA: 0x000D52BC File Offset: 0x000D34BC
		private void method_11(bool bool_0)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_14(queryable, bool_0);
				queryable = this.method_15(queryable, bool_0);
				queryable = this.method_13(queryable, bool_0);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E4D RID: 7757 RVA: 0x000D5304 File Offset: 0x000D3504
		private IQueryable<Transaction> method_12()
		{
			string text = string.Empty;
			if (this.comboBox_1.Enabled && this.comboBox_1.Items.Count > 0)
			{
				text = this.comboBox_1.Text;
			}
			this.comboBox_1.Items.Clear();
			if (Base.Trading.CurrHisTransList != null && Base.Trading.CurrHisTransList.Any<ShownHisTrans>())
			{
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = Class521.smethod_0(86988);
				this.comboBox_1.Items.Add(comboBoxItem);
			}
			TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
			comboBoxItem2.Text = Class521.smethod_0(40166);
			this.comboBox_1.Items.Add(comboBoxItem2);
			this.comboBox_1.SelectedIndexChanged -= this.comboBox_1_SelectedIndexChanged;
			this.comboBox_1.SelectedIndex = 0;
			if (this.comboBox_1.Items.Count > 1 && !string.IsNullOrEmpty(text) && text == Class521.smethod_0(40166))
			{
				this.comboBox_1.SelectedIndex = 1;
			}
			this.comboBox_1.SelectedIndexChanged += this.comboBox_1_SelectedIndexChanged;
			IQueryable<Transaction> result;
			if (this.comboBox_1.Text == Class521.smethod_0(86988))
			{
				result = Base.Trading.CurrHisTransList.Cast<Transaction>().AsQueryable<Transaction>();
			}
			else
			{
				result = Base.Trading.smethod_136();
			}
			return result;
		}

		// Token: 0x06001E4E RID: 7758 RVA: 0x000D5464 File Offset: 0x000D3664
		private IQueryable<Transaction> method_13(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			TrdAnalysisPanel.Class352 @class = new TrdAnalysisPanel.Class352();
			@class.nullable_0 = null;
			this.comboBox_2.SelectedIndexChanged -= this.comboBox_2_SelectedIndexChanged;
			if (this.comboBox_2.Enabled && this.comboBox_2.Items.Count > 0 && this.comboBox_2.SelectedIndex > 0)
			{
				@class.nullable_0 = new int?((int)(this.comboBox_2.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			if (bool_0)
			{
				this.comboBox_2.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = Class521.smethod_0(87005);
				this.comboBox_2.Items.Add(comboBoxItem);
				this.comboBox_2.SelectedItem = comboBoxItem;
				IQueryable<Transaction> source = iqueryable_0;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
				IEnumerable<int> enumerable = source.Select(Expression.Lambda<Func<Transaction, int>>(Expression.Property(parameterExpression, methodof(Transaction.get_SymbolID())), new ParameterExpression[]
				{
					parameterExpression
				})).Distinct<int>();
				List<StkSymbol> list = new List<StkSymbol>();
				foreach (int int_ in enumerable)
				{
					StkSymbol stkSymbol = SymbMgr.smethod_4(int_, false);
					if (stkSymbol != null)
					{
						list.Add(stkSymbol);
					}
				}
				list = list.OrderBy(new Func<StkSymbol, string>(TrdAnalysisPanel.<>c.<>9.method_0)).ToList<StkSymbol>();
				foreach (StkSymbol stkSymbol2 in list)
				{
					TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
					comboBoxItem2.Text = stkSymbol2.CNName + Class521.smethod_0(24872) + stkSymbol2.Code + Class521.smethod_0(5046);
					comboBoxItem2.Value = stkSymbol2.ID;
					this.comboBox_2.Items.Add(comboBoxItem2);
					if (@class.nullable_0 != null && @class.nullable_0.Value == stkSymbol2.ID)
					{
						this.comboBox_2.SelectedItem = comboBoxItem2;
					}
				}
				if (this.comboBox_2.SelectedItem == comboBoxItem)
				{
					@class.nullable_0 = null;
				}
			}
			this.comboBox_2.SelectedIndexChanged += this.comboBox_2_SelectedIndexChanged;
			if (@class.nullable_0 != null)
			{
				IQueryable<Transaction> source2 = iqueryable_0;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
				iqueryable_0 = source2.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_SymbolID())), Expression.Property(Expression.Field(Expression.Constant(@class, typeof(TrdAnalysisPanel.Class352)), fieldof(TrdAnalysisPanel.Class352.nullable_0)), methodof(int?.get_Value()))), new ParameterExpression[]
				{
					parameterExpression
				}));
			}
			return iqueryable_0;
		}

		// Token: 0x06001E4F RID: 7759 RVA: 0x000D5790 File Offset: 0x000D3990
		private IQueryable<Transaction> method_14(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			TrdAnalysisPanel.Class353 @class = new TrdAnalysisPanel.Class353();
			@class.nullable_0 = null;
			this.comboBox_0.SelectedIndexChanged -= this.comboBox_0_SelectedIndexChanged;
			if (this.comboBox_0.Enabled && this.comboBox_0.Items.Count > 0 && this.comboBox_0.SelectedIndex > 0)
			{
				@class.nullable_0 = new int?((int)(this.comboBox_0.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			if (bool_0)
			{
				this.comboBox_0.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = Class521.smethod_0(87005);
				this.comboBox_0.Items.Add(comboBoxItem);
				this.comboBox_0.SelectedItem = comboBoxItem;
				IQueryable<Transaction> source = iqueryable_0;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
				IQueryable<int> source2 = source.Select(Expression.Lambda<Func<Transaction, int>>(Expression.Property(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), methodof(DateTime.get_Year())), new ParameterExpression[]
				{
					parameterExpression
				})).Distinct<int>();
				parameterExpression = Expression.Parameter(typeof(int), Class521.smethod_0(87014));
				foreach (int num in source2.OrderBy(Expression.Lambda<Func<int, int>>(parameterExpression, new ParameterExpression[]
				{
					parameterExpression
				})))
				{
					TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
					comboBoxItem2.Text = num.ToString();
					comboBoxItem2.Value = num;
					this.comboBox_0.Items.Add(comboBoxItem2);
					if (@class.nullable_0 != null && @class.nullable_0.Value == num)
					{
						this.comboBox_0.SelectedItem = comboBoxItem2;
					}
				}
				if (this.comboBox_0.SelectedItem == comboBoxItem)
				{
					@class.nullable_0 = null;
				}
			}
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			if (@class.nullable_0 != null)
			{
				IQueryable<Transaction> source3 = iqueryable_0;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
				iqueryable_0 = source3.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.Equal(Expression.Property(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), methodof(DateTime.get_Year())), Expression.Property(Expression.Field(Expression.Constant(@class, typeof(TrdAnalysisPanel.Class353)), fieldof(TrdAnalysisPanel.Class353.nullable_0)), methodof(int?.get_Value()))), new ParameterExpression[]
				{
					parameterExpression
				}));
			}
			return iqueryable_0;
		}

		// Token: 0x06001E50 RID: 7760 RVA: 0x000D5A64 File Offset: 0x000D3C64
		private IQueryable<Transaction> method_15(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			TrdAnalysisPanel.Class354 @class = new TrdAnalysisPanel.Class354();
			@class.nullable_0 = null;
			this.comboBox_3.SelectedIndexChanged -= this.comboBox_3_SelectedIndexChanged;
			if (this.comboBox_3.Enabled && this.comboBox_3.Items.Count > 0 && this.comboBox_3.SelectedIndex > 0)
			{
				@class.nullable_0 = new int?((int)(this.comboBox_3.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			this.comboBox_3.Enabled = true;
			if (bool_0)
			{
				this.comboBox_3.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = Class521.smethod_0(87005);
				this.comboBox_3.Items.Add(comboBoxItem);
				this.comboBox_3.SelectedItem = comboBoxItem;
				if (this.comboBox_0.Items.Count > 2 && this.comboBox_0.SelectedIndex < 1)
				{
					this.comboBox_3.Enabled = false;
				}
				else
				{
					IQueryable<Transaction> source = iqueryable_0;
					ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
					IQueryable<int> source2 = source.Select(Expression.Lambda<Func<Transaction, int>>(Expression.Property(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), methodof(DateTime.get_Month())), new ParameterExpression[]
					{
						parameterExpression
					})).Distinct<int>();
					parameterExpression = Expression.Parameter(typeof(int), Class521.smethod_0(12283));
					foreach (int num in source2.OrderBy(Expression.Lambda<Func<int, int>>(parameterExpression, new ParameterExpression[]
					{
						parameterExpression
					})))
					{
						TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
						comboBoxItem2.Text = num.ToString(Class521.smethod_0(87019));
						comboBoxItem2.Value = num;
						this.comboBox_3.Items.Add(comboBoxItem2);
						if (@class.nullable_0 != null && @class.nullable_0.Value == num)
						{
							this.comboBox_3.SelectedItem = comboBoxItem2;
						}
					}
				}
				if (this.comboBox_3.SelectedItem == comboBoxItem)
				{
					@class.nullable_0 = null;
				}
			}
			this.comboBox_3.SelectedIndexChanged += this.comboBox_3_SelectedIndexChanged;
			if (@class.nullable_0 != null)
			{
				IQueryable<Transaction> source3 = iqueryable_0;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
				iqueryable_0 = source3.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.Equal(Expression.Property(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), methodof(DateTime.get_Month())), Expression.Property(Expression.Field(Expression.Constant(@class, typeof(TrdAnalysisPanel.Class354)), fieldof(TrdAnalysisPanel.Class354.nullable_0)), methodof(int?.get_Value()))), new ParameterExpression[]
				{
					parameterExpression
				}));
			}
			return iqueryable_0;
		}

		// Token: 0x06001E51 RID: 7761 RVA: 0x0000CB28 File Offset: 0x0000AD28
		private void comboBox_1_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_10();
		}

		// Token: 0x06001E52 RID: 7762 RVA: 0x000D5D80 File Offset: 0x000D3F80
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_14(queryable, false);
				queryable = this.method_15(queryable, true);
				queryable = this.method_13(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E53 RID: 7763 RVA: 0x000D5DC8 File Offset: 0x000D3FC8
		private void comboBox_3_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_15(queryable, false);
				queryable = this.method_14(queryable, true);
				queryable = this.method_13(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E54 RID: 7764 RVA: 0x000D5E10 File Offset: 0x000D4010
		private void comboBox_2_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_13(queryable, false);
				queryable = this.method_14(queryable, true);
				queryable = this.method_15(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E55 RID: 7765 RVA: 0x000D5E58 File Offset: 0x000D4058
		private void method_16()
		{
			this.tabControlPanel_2.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = Class521.smethod_0(87024);
			graphPane.XAxis.Title.Text = Class521.smethod_0(87053);
			graphPane.XAxis.Scale.MinorStep = 1.0;
			graphPane.XAxis.Scale.MinGrace = 1.0;
			graphPane.YAxis.Title.Text = Class521.smethod_0(87070);
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.XAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.Mag = 3;
			graphPane.Legend.FontSpec.Family = Class521.smethod_0(24023);
			graphPane.Title.FontSpec.Family = Class521.smethod_0(24023);
			IQueryable<Transaction> source = Base.Trading.smethod_136();
			ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			List<Transaction> list = source.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.OrElse(Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(2, typeof(int))), Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(4, typeof(int)))), new ParameterExpression[]
			{
				parameterExpression
			})).ToList<Transaction>();
			int num = list.Count<Transaction>();
			if (num == 0)
			{
				graphPane.XAxis.Scale.IsVisible = false;
			}
			else if (num > 10)
			{
				graphPane.XAxis.Scale.MajorStepAuto = true;
				graphPane.XAxis.Scale.IsVisible = true;
			}
			else
			{
				graphPane.XAxis.Scale.MajorStep = 1.0;
			}
			this.method_17(graphCtrlStat, list);
			this.tabControlPanel_2.Controls.Add(graphCtrlStat);
		}

		// Token: 0x06001E56 RID: 7766 RVA: 0x000D60AC File Offset: 0x000D42AC
		private void method_17(GraphCtrlStat graphCtrlStat_1, List<Transaction> list_1)
		{
			graphCtrlStat_1.GraphPane.CurveList.Clear();
			graphCtrlStat_1.Tag = list_1;
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			LineItem lineItem = this.method_19(graphCtrlStat_1, list_1, Class521.smethod_0(1449), null, Base.Acct.CurrAccount.IniBal);
			if (lineItem != null && lineItem.NPts > 0)
			{
				PointPair pointPair = lineItem.Points[lineItem.NPts - 1];
				double num = Convert.ToDouble(Base.Acct.smethod_42());
				if (pointPair.Y != num)
				{
					pointPair.Y = num;
				}
			}
			graphCtrlStat_1.method_1(new double[]
			{
				Convert.ToDouble(iniBal)
			});
			graphCtrlStat_1.AxisChange();
			graphCtrlStat_1.Refresh();
			graphCtrlStat_1.PointValueEvent += this.method_20;
		}

		// Token: 0x06001E57 RID: 7767 RVA: 0x000D616C File Offset: 0x000D436C
		private Color method_18(int int_1)
		{
			Color result;
			if (int_1 < this.color_0.Length)
			{
				result = this.color_0[int_1];
			}
			else
			{
				result = this.color_0[int_1 % (this.color_0.Length - 1)];
			}
			return result;
		}

		// Token: 0x06001E58 RID: 7768 RVA: 0x000D61B0 File Offset: 0x000D43B0
		private LineItem method_19(ZedGraphControl zedGraphControl_0, List<Transaction> list_1, string string_0, TradingSymbol tradingSymbol_0, decimal decimal_0)
		{
			TrdAnalysisPanel.Class355 @class = new TrdAnalysisPanel.Class355();
			@class.tradingSymbol_0 = tradingSymbol_0;
			LineItem lineItem = null;
			if (list_1 == null)
			{
				list_1 = (zedGraphControl_0.Tag as List<Transaction>).Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list_1 != null && list_1.Any<Transaction>())
			{
				PointPairList pointPairList = new PointPairList();
				Color color = this.method_18(zedGraphControl_0.GraphPane.CurveList.Count);
				lineItem = zedGraphControl_0.GraphPane.AddCurve(string_0, pointPairList, color, SymbolType.None);
				int num = 0;
				double num2 = Convert.ToDouble(decimal_0);
				PointPair point = new PointPair(0.0, num2);
				pointPairList.Add(point);
				foreach (Transaction transaction in list_1)
				{
					num++;
					int value = 2;
					if (@class.tradingSymbol_0 == null)
					{
						@class.tradingSymbol_0 = SymbMgr.smethod_3(transaction.SymbolID);
					}
					if (@class.tradingSymbol_0 != null && @class.tradingSymbol_0.IsOneSideFee != null && @class.tradingSymbol_0.IsOneSideFee.Value)
					{
						value = 1;
					}
					num2 += Convert.ToDouble(transaction.Profit) - Convert.ToDouble(transaction.Fee * value);
					pointPairList.Add(new PointPair(Convert.ToDouble(num), num2)
					{
						Tag = transaction
					});
				}
				lineItem.Tag = @class.tradingSymbol_0;
			}
			return lineItem;
		}

		// Token: 0x06001E59 RID: 7769 RVA: 0x000D6384 File Offset: 0x000D4584
		private string method_20(ZedGraphControl zedGraphControl_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0.Points[int_1];
			Transaction transaction = pointPair.Tag as Transaction;
			string result;
			try
			{
				result = string.Concat(new object[]
				{
					Class521.smethod_0(87087),
					Convert.ToInt32(Math.Round(pointPair.Y)),
					Environment.NewLine,
					(pointPair.Tag == null) ? string.Empty : (Class521.smethod_0(87100) + string.Format(Class521.smethod_0(87113), Convert.ToDateTime(transaction.CreateTime)) + Environment.NewLine),
					Class521.smethod_0(87122),
					Convert.ToInt32(pointPair.X)
				});
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001E5A RID: 7770 RVA: 0x000D6470 File Offset: 0x000D4670
		private void method_21()
		{
			this.tabControlPanel_3.Controls.Clear();
			this.graphCtrlStat_0 = new GraphCtrlStat(false);
			GraphPane graphPane = this.graphCtrlStat_0.GraphPane;
			graphPane.Title.Text = Class521.smethod_0(87135);
			graphPane.XAxis.Title.Text = Class521.smethod_0(87053);
			graphPane.XAxis.Scale.MinorStep = 1.0;
			graphPane.XAxis.Scale.MinGrace = 1.0;
			graphPane.YAxis.Title.Text = Class521.smethod_0(87164);
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.Legend.FontSpec.Family = Class521.smethod_0(24023);
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Title.FontSpec.Family = Class521.smethod_0(24023);
			IQueryable<Transaction> source = Base.Trading.smethod_136();
			ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			List<Transaction> list = source.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.OrElse(Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(2, typeof(int))), Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(4, typeof(int)))), new ParameterExpression[]
			{
				parameterExpression
			})).ToList<Transaction>();
			int num = list.Count<Transaction>();
			if (num == 0)
			{
				graphPane.XAxis.Scale.IsVisible = false;
			}
			else if (num > 10)
			{
				graphPane.XAxis.Scale.MajorStepAuto = true;
				graphPane.XAxis.Scale.IsVisible = true;
			}
			else
			{
				graphPane.XAxis.Scale.MajorStep = 1.0;
			}
			this.method_22(this.graphCtrlStat_0, list);
			this.tabControlPanel_3.Controls.Add(this.graphCtrlStat_0);
		}

		// Token: 0x06001E5B RID: 7771 RVA: 0x0000CB32 File Offset: 0x0000AD32
		private void method_22(ZedGraphControl zedGraphControl_0, List<Transaction> list_1)
		{
			zedGraphControl_0.Tag = list_1;
			this.method_24();
			zedGraphControl_0.ContextMenuBuilder += this.method_23;
			zedGraphControl_0.PointValueEvent += this.method_28;
		}

		// Token: 0x06001E5C RID: 7772 RVA: 0x000D66B0 File Offset: 0x000D48B0
		private void method_23(ZedGraphControl zedGraphControl_0, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			ToolStripItem[] array = new ToolStripItem[contextMenuStrip_0.Items.Count];
			contextMenuStrip_0.Items.CopyTo(array, 0);
			contextMenuStrip_0.Items.Clear();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(87173);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(87190);
			toolStripMenuItem2.Click += this.method_25;
			if (this.list_0 == null)
			{
				toolStripMenuItem2.Checked = true;
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(87207);
			toolStripMenuItem3.Click += this.method_26;
			if (this.list_0 != null && !this.list_0.Any<TradingSymbol>())
			{
				toolStripMenuItem3.Checked = true;
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
			toolStripMenuItem.DropDownItems.Add(new ToolStripSeparator());
			List<Transaction> list = zedGraphControl_0.Tag as List<Transaction>;
			if (list != null && list.Any<Transaction>())
			{
				foreach (TradingSymbol tradingSymbol in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_1)).Select(new Func<IGrouping<TradingSymbol, Transaction>, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_2)).OrderBy(new Func<TradingSymbol, string>(TrdAnalysisPanel.<>c.<>9.method_3)))
				{
					ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
					toolStripMenuItem4.Text = tradingSymbol.AbbrCode + Class521.smethod_0(24872) + tradingSymbol.AbbrCNName + Class521.smethod_0(5046);
					toolStripMenuItem4.Tag = tradingSymbol;
					if (this.list_0 == null || this.list_0.Contains(tradingSymbol))
					{
						toolStripMenuItem4.Checked = true;
					}
					toolStripMenuItem4.Click += this.method_27;
					toolStripMenuItem.DropDownItems.Add(toolStripMenuItem4);
				}
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = Class521.smethod_0(87224);
			toolStripMenuItem5.DropDownItems.AddRange(array);
			contextMenuStrip_0.Items.Add(toolStripMenuItem5);
		}

		// Token: 0x06001E5D RID: 7773 RVA: 0x000D6944 File Offset: 0x000D4B44
		private void method_24()
		{
			GraphCtrlStat graphCtrlStat = this.graphCtrlStat_0;
			List<Transaction> list = this.graphCtrlStat_0.Tag as List<Transaction>;
			list.RemoveAll(new Predicate<Transaction>(TrdAnalysisPanel.<>c.<>9.method_4));
			graphCtrlStat.GraphPane.CurveList.Clear();
			if (list != null && list.Any<Transaction>())
			{
				foreach (var <>f__AnonymousType in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_5)).Select(new Func<IGrouping<TradingSymbol, Transaction>, <>f__AnonymousType20<TradingSymbol, List<Transaction>>>(TrdAnalysisPanel.<>c.<>9.method_6)).Where(new Func<<>f__AnonymousType20<TradingSymbol, List<Transaction>>, bool>(TrdAnalysisPanel.<>c.<>9.method_8)).OrderBy(new Func<<>f__AnonymousType20<TradingSymbol, List<Transaction>>, string>(TrdAnalysisPanel.<>c.<>9.method_9)).Take(36))
				{
					this.method_19(graphCtrlStat, <>f__AnonymousType.Trans, <>f__AnonymousType.Symbol.AbbrCNName, <>f__AnonymousType.Symbol, 0m);
				}
				GraphPane graphPane = graphCtrlStat.GraphPane;
				if (graphPane.CurveList != null && graphPane.CurveList.Any<CurveItem>())
				{
					double num = graphPane.CurveList.Max(new Func<CurveItem, double>(TrdAnalysisPanel.<>c.<>9.method_10));
					double num2 = graphPane.CurveList.Min(new Func<CurveItem, double>(TrdAnalysisPanel.<>c.<>9.method_12));
					double[] double_ = new double[]
					{
						num,
						num2
					};
					graphCtrlStat.method_1(double_);
				}
			}
			graphCtrlStat.AxisChange();
			graphCtrlStat.Refresh();
		}

		// Token: 0x06001E5E RID: 7774 RVA: 0x0000CB67 File Offset: 0x0000AD67
		private void method_25(object sender, EventArgs e)
		{
			this.method_24();
			this.list_0 = null;
		}

		// Token: 0x06001E5F RID: 7775 RVA: 0x0000CB78 File Offset: 0x0000AD78
		private void method_26(object sender, EventArgs e)
		{
			this.graphCtrlStat_0.GraphPane.CurveList.Clear();
			this.graphCtrlStat_0.AxisChange();
			this.graphCtrlStat_0.Refresh();
			this.list_0 = new List<TradingSymbol>();
		}

		// Token: 0x06001E60 RID: 7776 RVA: 0x000D6B40 File Offset: 0x000D4D40
		private void method_27(object sender, EventArgs e)
		{
			TrdAnalysisPanel.Class356 @class = new TrdAnalysisPanel.Class356();
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			@class.tradingSymbol_0 = (toolStripMenuItem.Tag as TradingSymbol);
			if (toolStripMenuItem.Tag != null && !toolStripMenuItem.Checked)
			{
				if (this.list_0 == null)
				{
					this.list_0 = new List<TradingSymbol>();
				}
				this.method_19(this.graphCtrlStat_0, null, @class.tradingSymbol_0.AbbrCNName, @class.tradingSymbol_0, 0m);
				if (!this.list_0.Contains(@class.tradingSymbol_0))
				{
					this.list_0.Add(@class.tradingSymbol_0);
				}
			}
			else if (this.list_0 == null)
			{
				this.list_0 = new List<TradingSymbol>();
				List<Transaction> list = this.graphCtrlStat_0.Tag as List<Transaction>;
				if (list != null && list.Any<Transaction>())
				{
					foreach (TradingSymbol tradingSymbol in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_14)).Select(new Func<IGrouping<TradingSymbol, Transaction>, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_15)))
					{
						if (tradingSymbol != @class.tradingSymbol_0)
						{
							this.list_0.Add(tradingSymbol);
						}
					}
					this.graphCtrlStat_0.GraphPane.CurveList.RemoveAll(new Predicate<CurveItem>(@class.method_0));
				}
			}
			else
			{
				this.list_0.Remove(@class.tradingSymbol_0);
				this.graphCtrlStat_0.GraphPane.CurveList.RemoveAll(new Predicate<CurveItem>(@class.method_1));
			}
			this.graphCtrlStat_0.AxisChange();
			this.graphCtrlStat_0.Refresh();
		}

		// Token: 0x06001E61 RID: 7777 RVA: 0x000D6D1C File Offset: 0x000D4F1C
		private string method_28(ZedGraphControl zedGraphControl_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0.Points[int_1];
			TradingSymbol tradingSymbol = curveItem_0.Tag as TradingSymbol;
			Transaction transaction = pointPair.Tag as Transaction;
			string result;
			if (transaction != null)
			{
				DateTime dateTime = Convert.ToDateTime(transaction.CreateTime);
				result = string.Concat(new object[]
				{
					Class521.smethod_0(87241),
					tradingSymbol.AbbrCNName,
					Class521.smethod_0(24872),
					tradingSymbol.AbbrCode,
					Class521.smethod_0(5046),
					Environment.NewLine,
					Class521.smethod_0(87262),
					Convert.ToInt64(Math.Round(pointPair.Y)),
					Environment.NewLine,
					Class521.smethod_0(87283),
					transaction.Units,
					Environment.NewLine,
					(pointPair.Tag == null) ? string.Empty : (Class521.smethod_0(87304) + string.Format(Class521.smethod_0(87113), dateTime))
				});
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(87325)));
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001E62 RID: 7778 RVA: 0x000D6E5C File Offset: 0x000D505C
		private void method_29()
		{
			this.tabControlPanel_4.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = Class521.smethod_0(87346);
			graphPane.XAxis.Scale.IsVisible = false;
			graphPane.XAxis.MajorGrid.IsVisible = false;
			graphPane.XAxis.MinorGrid.IsVisible = false;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.XAxis.Title.Text = Class521.smethod_0(51577);
			graphPane.YAxis.Title.Text = Class521.smethod_0(52474);
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Legend.FontSpec.Family = Class521.smethod_0(24023);
			graphPane.Title.FontSpec.Family = Class521.smethod_0(24023);
			List<SymbolProfit> list = Base.Trading.smethod_214(new int?(28));
			if (list.Any<SymbolProfit>())
			{
				Color[] array = new Color[list.Count];
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = this.method_18(i);
				}
				for (int j = 0; j < list.Count; j++)
				{
					PointPairList pointPairList = new PointPairList();
					pointPairList.Add(Convert.ToDouble(j + 1), Convert.ToDouble(list[j].decimal_0));
					TradingSymbol tradingSymbol_ = list[j].tradingSymbol_0;
					if (tradingSymbol_ != null)
					{
						Color color = array[j];
						BarItem barItem = graphPane.AddBar(tradingSymbol_.AbbrCNName, pointPairList, color);
						barItem.Bar.Fill = new Fill(color, Color.White, color);
						barItem.Tag = tradingSymbol_.AbbrCNName;
					}
				}
				Base.UI.smethod_38(graphPane, list.Count);
				double num = Convert.ToDouble(list.Max(new Func<SymbolProfit, decimal>(TrdAnalysisPanel.<>c.<>9.method_16)));
				double num2 = Convert.ToDouble(list.Min(new Func<SymbolProfit, decimal>(TrdAnalysisPanel.<>c.<>9.method_17)));
				double[] double_ = new double[]
				{
					num,
					num2
				};
				graphCtrlStat.method_1(double_);
				BarItem.CreateBarLabels(graphPane, false, Class521.smethod_0(87375), Class521.smethod_0(4683), 13f, Color.Black, true, false, false);
				graphCtrlStat.PointValueEvent += new ZedGraphControl.PointValueHandler(this.method_30);
				graphCtrlStat.AxisChange();
				graphPane.YAxis.Scale.Max += graphPane.YAxis.Scale.MajorStep;
				if (graphPane.YAxis.Scale.Min < 0.0)
				{
					graphPane.YAxis.Scale.Min -= graphPane.YAxis.Scale.MajorStep;
				}
			}
			else
			{
				graphPane.YAxis.Scale.IsVisible = false;
			}
			this.tabControlPanel_4.Controls.Add(graphCtrlStat);
		}

		// Token: 0x06001E63 RID: 7779 RVA: 0x000D7188 File Offset: 0x000D5388
		private string method_30(object object_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0[int_1];
			string text = curveItem_0.Tag.ToString();
			return string.Concat(new string[]
			{
				Class521.smethod_0(87380),
				pointPair.Y.ToString(),
				Environment.NewLine,
				Class521.smethod_0(5902),
				text
			});
		}

		// Token: 0x06001E64 RID: 7780 RVA: 0x000D71F0 File Offset: 0x000D53F0
		private void method_31()
		{
			this.splitContainer_0.Panel1.Controls.Clear();
			this.splitContainer_0.Panel2.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphCtrlStat graphCtrlStat2 = new GraphCtrlStat(false);
			graphCtrlStat.IsEnableZoom = false;
			graphCtrlStat2.IsEnableZoom = false;
			IQueryable<Transaction> queryable = Base.Trading.smethod_136();
			decimal num = Base.Trading.smethod_179(queryable);
			decimal num2 = Base.Trading.smethod_180(queryable);
			IQueryable<Transaction> source = queryable;
			ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			IQueryable<Transaction> source2 = source.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.AndAlso(Expression.GreaterThan(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), Expression.Convert(Expression.Convert(Expression.Constant(0, typeof(int)), typeof(decimal), methodof(int)), typeof(decimal?))), Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(2, typeof(int)))), new ParameterExpression[]
			{
				parameterExpression
			}));
			parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			decimal? num3 = source2.Sum(Expression.Lambda<Func<Transaction, decimal?>>(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), new ParameterExpression[]
			{
				parameterExpression
			}));
			if (num3 == null)
			{
				num3 = new decimal?(0m);
			}
			IQueryable<Transaction> source3 = queryable;
			parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			IQueryable<Transaction> source4 = source3.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.AndAlso(Expression.LessThan(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), Expression.Convert(Expression.Convert(Expression.Constant(0, typeof(int)), typeof(decimal), methodof(int)), typeof(decimal?))), Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(2, typeof(int)))), new ParameterExpression[]
			{
				parameterExpression
			}));
			parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			decimal? num4 = source4.Sum(Expression.Lambda<Func<Transaction, decimal?>>(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), new ParameterExpression[]
			{
				parameterExpression
			}));
			if (num4 == null)
			{
				num4 = new decimal?(0m);
			}
			double num5;
			if (num != 0m)
			{
				num5 = Convert.ToDouble(num3 / num);
			}
			else
			{
				num5 = 0.0;
			}
			double num6;
			if (num2 != 0m)
			{
				num6 = Convert.ToDouble(num4 / num2);
			}
			else
			{
				num6 = 0.0;
			}
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = Class521.smethod_0(87393);
			graphPane.Title.FontSpec.Size = 22f;
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Legend.FontSpec.Size = 16f;
			graphPane.Legend.Position = LegendPos.InsideBotRight;
			PieItem pieItem = graphPane.AddPieSlice(num5, Color.Navy, Color.White, 45f, 0.0, Class521.smethod_0(87418));
			PieItem pieItem2 = graphPane.AddPieSlice(1.0 - num5, Color.Purple, Color.White, 45f, 0.0, Class521.smethod_0(87435));
			pieItem.LabelType = PieLabelType.Name_Percent;
			pieItem2.LabelType = PieLabelType.Name_Percent;
			pieItem.LabelDetail.FontSpec.Size = 16f;
			pieItem2.LabelDetail.FontSpec.Size = 16f;
			pieItem.LabelDetail.FontSpec.Border.IsVisible = false;
			pieItem2.LabelDetail.FontSpec.Border.IsVisible = false;
			graphCtrlStat.Dock = DockStyle.Fill;
			graphCtrlStat.AxisChange();
			this.splitContainer_0.Panel1.Controls.Add(graphCtrlStat);
			GraphPane graphPane2 = graphCtrlStat2.GraphPane;
			graphPane2.Title.Text = Class521.smethod_0(87452);
			graphPane2.Title.FontSpec.Size = 22f;
			graphPane2.Legend.Border.IsVisible = false;
			graphPane2.Legend.FontSpec.Size = 16f;
			graphPane2.Legend.Position = LegendPos.InsideBotRight;
			graphPane2.Legend.FontSpec.Family = Class521.smethod_0(24023);
			graphPane2.Title.FontSpec.Family = Class521.smethod_0(24023);
			PieItem pieItem3 = graphPane2.AddPieSlice(num6, Color.Navy, Color.White, 45f, 0.0, Class521.smethod_0(87477));
			PieItem pieItem4 = graphPane2.AddPieSlice(1.0 - num6, Color.Purple, Color.White, 45f, 0.0, Class521.smethod_0(87494));
			pieItem3.LabelType = PieLabelType.Name_Percent;
			pieItem4.LabelType = PieLabelType.Name_Percent;
			pieItem3.LabelDetail.FontSpec.Size = 16f;
			pieItem4.LabelDetail.FontSpec.Size = 16f;
			pieItem3.LabelDetail.FontSpec.Border.IsVisible = false;
			pieItem4.LabelDetail.FontSpec.Border.IsVisible = false;
			graphCtrlStat2.Dock = DockStyle.Fill;
			graphCtrlStat2.AxisChange();
			this.splitContainer_0.Panel2.Controls.Add(graphCtrlStat2);
		}

		// Token: 0x06001E65 RID: 7781 RVA: 0x000D7818 File Offset: 0x000D5A18
		private void method_32()
		{
			this.tabControlPanel_5.Controls.Clear();
			ZedGraphControl zedGraphControl = new GraphCtrlStat(false);
			GraphPane graphPane = zedGraphControl.GraphPane;
			graphPane.Title.Text = Class521.smethod_0(87511);
			graphPane.XAxis.Title.Text = Class521.smethod_0(87548);
			graphPane.YAxis.Title.Text = Class521.smethod_0(87164);
			graphPane.YAxis.MajorGrid.IsZeroLine = false;
			graphPane.XAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.Mag = 3;
			IQueryable<Transaction> source = Base.Trading.smethod_136();
			ParameterExpression parameterExpression = Expression.Parameter(typeof(Transaction), Class521.smethod_0(52097));
			List<Transaction> source2 = source.Where(Expression.Lambda<Func<Transaction, bool>>(Expression.OrElse(Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(2, typeof(int))), Expression.Equal(Expression.Property(parameterExpression, methodof(Transaction.get_TransType())), Expression.Constant(4, typeof(int)))), new ParameterExpression[]
			{
				parameterExpression
			})).ToList<Transaction>();
			if (source2.Any<Transaction>())
			{
				List<Transaction> list = source2.Where(new Func<Transaction, bool>(TrdAnalysisPanel.<>c.<>9.method_18)).ToList<Transaction>();
				int count = list.Count;
				if (count <= 0)
				{
					this.method_33(zedGraphControl);
				}
				else
				{
					List<TimeSpan> list2 = new List<TimeSpan>();
					double[] array = new double[count];
					double[] array2 = new double[count];
					double[] array3 = new double[count];
					for (int i = 0; i < count; i++)
					{
						TimeSpan? timeSpan = Base.Trading.smethod_150(list[i]);
						if (timeSpan != null)
						{
							list2.Add(timeSpan.Value);
							array2[i] = Convert.ToDouble(list[i].Profit);
							array3[i] = timeSpan.Value.TotalSeconds;
						}
					}
					if (!list2.Any<TimeSpan>())
					{
						this.method_33(zedGraphControl);
					}
					else
					{
						TimeSpan t = list2.Max<TimeSpan>();
						TimeSpan timeSpan2 = list2.Min<TimeSpan>();
						TimeSpan timeSpan3 = new TimeSpan(0, 5, 0);
						TimeSpan timeSpan4 = new TimeSpan(0, 15, 0);
						TimeSpan timeSpan5 = new TimeSpan(1, 0, 0);
						TimeSpan timeSpan6 = new TimeSpan(1, 0, 0, 0);
						TimeSpan timeSpan7 = new TimeSpan(7, 0, 0, 0);
						TimeSpan timeSpan8 = new TimeSpan(30, 0, 0, 0);
						TimeSpan timeSpan9 = new TimeSpan(365, 0, 0, 0);
						List<TimeSpan> list3 = new List<TimeSpan>();
						list3.Add(timeSpan3);
						list3.Add(timeSpan4);
						list3.Add(timeSpan5);
						list3.Add(timeSpan6);
						list3.Add(timeSpan7);
						list3.Add(timeSpan8);
						list3.Add(timeSpan9);
						TimeSpan timeSpan10;
						if (timeSpan2.TotalMinutes < 5.0)
						{
							timeSpan10 = timeSpan3;
						}
						else if (timeSpan2.TotalMinutes < 15.0)
						{
							timeSpan10 = timeSpan4;
						}
						else if (timeSpan2.TotalHours < 1.0)
						{
							timeSpan10 = timeSpan5;
						}
						else if (timeSpan2.TotalHours < 24.0)
						{
							timeSpan10 = timeSpan6;
						}
						else if (timeSpan2.TotalDays < 7.0)
						{
							timeSpan10 = timeSpan7;
						}
						else if (timeSpan2.TotalDays < 30.0)
						{
							timeSpan10 = timeSpan8;
						}
						else
						{
							timeSpan10 = timeSpan9;
						}
						TimeSpan timeSpan11;
						if (t > timeSpan9)
						{
							this.int_0 = Convert.ToInt32(Math.Floor(t.TotalDays / 365.0)) + 1;
							timeSpan11 = new TimeSpan(this.int_0 * 365, 0, 0, 0);
						}
						else if (t > timeSpan8)
						{
							timeSpan11 = timeSpan9;
						}
						else if (t > timeSpan7)
						{
							timeSpan11 = timeSpan8;
						}
						else if (t > timeSpan6)
						{
							timeSpan11 = timeSpan7;
						}
						else if (t > timeSpan5)
						{
							timeSpan11 = timeSpan6;
						}
						else if (t > timeSpan4)
						{
							timeSpan11 = timeSpan5;
						}
						else if (t > timeSpan3)
						{
							timeSpan11 = timeSpan4;
						}
						else
						{
							timeSpan11 = timeSpan3;
						}
						int num;
						if (t > timeSpan9)
						{
							num = 7;
						}
						else
						{
							num = list3.IndexOf(timeSpan11);
						}
						int num2 = num - list3.IndexOf(timeSpan10) + 1;
						double totalSeconds = timeSpan10.TotalSeconds;
						for (int j = 0; j < list2.Count; j++)
						{
							TimeSpan t2 = list2[j];
							if (t2 <= timeSpan10)
							{
								array[j] = t2.TotalSeconds;
							}
							else if (t2 > timeSpan9)
							{
								array[j] = (t2 - timeSpan9).TotalSeconds / (timeSpan11 - timeSpan9).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan9) + 1 - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan8)
							{
								array[j] = (t2 - timeSpan8).TotalSeconds / (timeSpan9 - timeSpan8).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan9) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan7)
							{
								array[j] = (t2 - timeSpan7).TotalSeconds / (timeSpan8 - timeSpan7).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan8) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan6)
							{
								array[j] = (t2 - timeSpan6).TotalSeconds / (timeSpan7 - timeSpan6).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan7) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan5)
							{
								array[j] = (t2 - timeSpan5).TotalSeconds / (timeSpan6 - timeSpan5).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan6) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan4)
							{
								array[j] = (t2 - timeSpan4).TotalSeconds / (timeSpan5 - timeSpan4).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan5) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan3)
							{
								array[j] = (t2 - timeSpan3).TotalSeconds / (timeSpan4 - timeSpan3).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan4) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
						}
						PointPairList points = new PointPairList(array, array2, array3);
						LineItem lineItem = graphPane.AddCurve(Class521.smethod_0(1449), points, Color.Red, SymbolType.Diamond);
						lineItem.Symbol.Size = 12f;
						lineItem.Symbol.Fill = new Fill(Color.Green, Color.Red);
						lineItem.Symbol.Border.IsVisible = false;
						lineItem.Line.IsVisible = false;
						lineItem.Symbol.Fill.Type = FillType.GradientByY;
						lineItem.Symbol.Fill.RangeMin = -1.0;
						lineItem.Symbol.Fill.RangeMax = 1.0;
						graphPane.Legend.IsVisible = false;
						graphPane.XAxis.MajorGrid.IsVisible = true;
						graphPane.XAxis.Scale.Max = (double)num2 * totalSeconds;
						graphPane.XAxis.ScaleFormatEvent += this.method_34;
						graphPane.XAxis.Scale.MajorStep = totalSeconds;
						zedGraphControl.PointValueEvent += new ZedGraphControl.PointValueHandler(this.method_35);
						zedGraphControl.AxisChange();
						this.tabControlPanel_5.Controls.Add(zedGraphControl);
					}
				}
			}
			else
			{
				this.method_33(zedGraphControl);
			}
		}

		// Token: 0x06001E66 RID: 7782 RVA: 0x0000CBB2 File Offset: 0x0000ADB2
		private void method_33(ZedGraphControl zedGraphControl_0)
		{
			GraphPane graphPane = zedGraphControl_0.GraphPane;
			graphPane.XAxis.Scale.IsVisible = false;
			graphPane.YAxis.Scale.IsVisible = false;
			this.tabControlPanel_5.Controls.Add(zedGraphControl_0);
		}

		// Token: 0x06001E67 RID: 7783 RVA: 0x000D803C File Offset: 0x000D623C
		private string method_34(GraphPane graphPane_0, Axis axis_0, double double_0, int int_1)
		{
			switch (int_1)
			{
			case 0:
				return Class521.smethod_0(2841);
			case 1:
				if (double_0 == 300.0)
				{
					return Class521.smethod_0(16351);
				}
				if (double_0 == 900.0)
				{
					return Class521.smethod_0(16364);
				}
				if (double_0 == 3600.0)
				{
					return Class521.smethod_0(37487);
				}
				if (double_0 == 86400.0)
				{
					return Class521.smethod_0(87565);
				}
				if (double_0 == 604800.0)
				{
					return Class521.smethod_0(87574);
				}
				if (double_0 == 2592000.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 == 31536000.0)
				{
					return Class521.smethod_0(87592);
				}
				if (this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 2:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(16364);
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return Class521.smethod_0(37487);
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return Class521.smethod_0(87565);
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return Class521.smethod_0(87574);
				}
				if (double_0 / (double)int_1 == 604800.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 / (double)int_1 == 2592000.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 31536000.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 3:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(37487);
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return Class521.smethod_0(87565);
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return Class521.smethod_0(87574);
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 / (double)int_1 == 604800.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 2592000.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 4:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(87565);
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return Class521.smethod_0(87574);
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 604800.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 5:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(87574);
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 86400.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 6:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(87583);
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 3600.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 7:
				if (double_0 / (double)int_1 == 300.0)
				{
					return Class521.smethod_0(87592);
				}
				if (double_0 / (double)int_1 == 900.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			case 8:
				if (this.int_0 > 0)
				{
					return this.int_0.ToString() + Class521.smethod_0(87601);
				}
				break;
			}
			return Class521.smethod_0(1449);
		}

		// Token: 0x06001E68 RID: 7784 RVA: 0x000D85BC File Offset: 0x000D67BC
		private string method_35(object object_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0[int_1];
			long ticks = Convert.ToInt64(10000000.0 * pointPair.Z);
			TimeSpan timeSpan = new TimeSpan(ticks);
			string str = Class521.smethod_0(87606);
			if (timeSpan.Days > 0)
			{
				str = str + timeSpan.Days.ToString() + Class521.smethod_0(87627);
			}
			if (timeSpan.Hours > 0)
			{
				str = str + timeSpan.Hours.ToString() + Class521.smethod_0(84754);
			}
			if (timeSpan.Minutes > 0)
			{
				str = str + timeSpan.Minutes.ToString() + Class521.smethod_0(17790);
			}
			if (timeSpan.Seconds > 0)
			{
				str = str + timeSpan.Seconds.ToString() + Class521.smethod_0(87632);
			}
			return str + Class521.smethod_0(87637) + pointPair.Y.ToString();
		}

		// Token: 0x06001E69 RID: 7785 RVA: 0x000D86C8 File Offset: 0x000D68C8
		private void method_36()
		{
			this.method_38(this.label_22, Base.Acct.CurrAccount.AcctName.Trim());
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			this.method_38(this.label_23, iniBal.ToString(Class521.smethod_0(74951)));
			decimal num = Base.Trading.smethod_178();
			this.method_8(this.label_16, num);
			decimal num2 = Base.Acct.smethod_42() + num;
			this.method_38(this.label_21, num2.ToString(Class521.smethod_0(74951)));
		}

		// Token: 0x06001E6A RID: 7786 RVA: 0x000D8758 File Offset: 0x000D6958
		private void method_37(IQueryable<Transaction> iqueryable_0)
		{
			decimal d = Base.Trading.smethod_177(iqueryable_0);
			decimal d2 = Base.Trading.smethod_181(iqueryable_0);
			decimal d3 = d - d2;
			decimal num = Base.Acct.smethod_44(iqueryable_0);
			this.method_38(this.label_27, num.ToString(Class521.smethod_0(84628)));
			string string_ = (d3 / num * 100m).ToString(Class521.smethod_0(87654)) + Class521.smethod_0(5356);
			this.method_38(this.label_29, string_);
			this.method_9(this.label_29, string_);
			string string_2 = d3.ToString(Class521.smethod_0(84628));
			this.method_38(this.label_59, string_2);
			this.method_9(this.label_59, string_2);
			decimal num2 = Base.Trading.smethod_179(iqueryable_0);
			this.method_38(this.label_61, num2.ToString(Class521.smethod_0(84628)));
			decimal num3 = Base.Trading.smethod_180(iqueryable_0);
			this.method_38(this.label_60, num3.ToString(Class521.smethod_0(84628)));
			this.method_38(this.label_48, Base.Trading.smethod_181(iqueryable_0).ToString(Class521.smethod_0(84628)));
			int num4 = Base.Trading.smethod_182(iqueryable_0);
			this.method_38(this.label_57, num4.ToString());
			if (num4 == 0)
			{
				this.method_38(this.label_56, Class521.smethod_0(2841));
				this.method_38(this.label_55, Class521.smethod_0(2841));
				this.method_38(this.label_42, Class521.smethod_0(18686));
				this.method_38(this.label_41, Class521.smethod_0(18686));
				this.method_38(this.label_58, Class521.smethod_0(18686));
				this.method_38(this.label_38, Class521.smethod_0(2841));
				this.method_38(this.label_32, Class521.smethod_0(18686));
				this.method_38(this.label_31, Class521.smethod_0(18686));
				this.method_38(this.label_37, Class521.smethod_0(2841));
				this.method_38(this.label_36, Class521.smethod_0(18686));
				this.method_38(this.label_54, Class521.smethod_0(18686));
				this.method_38(this.label_53, Class521.smethod_0(18686));
				this.method_38(this.label_52, Class521.smethod_0(18686));
				this.method_38(this.label_51, Class521.smethod_0(18686));
				this.method_38(this.label_50, Class521.smethod_0(2841));
				this.method_38(this.label_47, Class521.smethod_0(2841));
				this.method_38(this.label_46, Class521.smethod_0(2841));
				this.method_38(this.label_45, Class521.smethod_0(2841));
				this.method_38(this.label_44, Class521.smethod_0(2841));
				this.method_38(this.label_43, Class521.smethod_0(2841));
			}
			else
			{
				int num5 = Base.Trading.smethod_183(iqueryable_0);
				int num6 = Base.Trading.smethod_185(iqueryable_0);
				if (num5 == 0)
				{
					this.method_38(this.label_56, num5.ToString());
				}
				else
				{
					this.method_38(this.label_56, num5.ToString() + Class521.smethod_0(5041) + (100 * num6 / num5).ToString(Class521.smethod_0(87659)) + Class521.smethod_0(87664));
				}
				int num7 = Base.Trading.smethod_184(iqueryable_0);
				int num8 = Base.Trading.smethod_186(iqueryable_0);
				if (num7 == 0)
				{
					this.method_38(this.label_55, num7.ToString());
				}
				else
				{
					this.method_38(this.label_55, num7.ToString() + Class521.smethod_0(5041) + (100 * num8 / num7).ToString(Class521.smethod_0(87659)) + Class521.smethod_0(87664));
				}
				int num9 = Base.Trading.smethod_187(iqueryable_0);
				this.method_38(this.label_42, num9.ToString() + Class521.smethod_0(5041) + (100 * num9 / num4).ToString(Class521.smethod_0(87659)) + Class521.smethod_0(87664));
				int num10 = num4 - num9;
				this.method_38(this.label_41, num10.ToString() + Class521.smethod_0(5041) + (100 * num10 / num4).ToString(Class521.smethod_0(87659)) + Class521.smethod_0(87664));
				if (num9 != 0 || num10 != 0)
				{
					if (num9 == 0)
					{
						this.method_38(this.label_58, ((0m - 100 * num10 / num4 * (100m * num3 / num10)) / 10000m).ToString(Class521.smethod_0(87654)));
					}
					else if (num10 == 0)
					{
						this.method_38(this.label_58, ((100 * num9 / num4 * (100m * num2 / num9) - 0m) / 10000m).ToString(Class521.smethod_0(87654)));
					}
					else
					{
						this.method_38(this.label_58, ((100 * num9 / num4 * (100m * num2 / num9) + 100 * num10 / num4 * (100m * num3 / num10)) / 10000m).ToString(Class521.smethod_0(87654)));
					}
				}
				this.method_38(this.label_38, Base.Trading.smethod_188(iqueryable_0).ToString(Class521.smethod_0(84628)));
				Class61 @class = Base.Trading.smethod_189(iqueryable_0);
				if (@class != null)
				{
					decimal decimal_ = @class.decimal_2;
					this.method_38(this.label_37, decimal_.ToString(Class521.smethod_0(84628)));
					this.method_38(this.label_32, (100m * Math.Abs(decimal_ / (num + @class.decimal_1))).ToString(Class521.smethod_0(87654)) + Class521.smethod_0(5356));
					this.method_38(this.label_31, @class.decimal_0.ToString(Class521.smethod_0(84628)));
					if (decimal_ != 0m)
					{
						this.method_38(this.label_36, Math.Abs((num2 + num3) / decimal_).ToString(Class521.smethod_0(87654)));
					}
					else
					{
						this.method_38(this.label_36, Class521.smethod_0(18686));
					}
				}
				else
				{
					this.method_38(this.label_37, Class521.smethod_0(2841));
					this.method_38(this.label_32, Class521.smethod_0(18686));
					this.method_38(this.label_31, Class521.smethod_0(18686));
					this.method_38(this.label_36, Class521.smethod_0(18686));
				}
				this.method_38(this.label_54, Base.Trading.smethod_203(iqueryable_0).ToString(Class521.smethod_0(84628)));
				this.method_38(this.label_53, Base.Trading.smethod_204(iqueryable_0).ToString(Class521.smethod_0(84628)));
				if (num9 == 0)
				{
					this.method_38(this.label_52, Class521.smethod_0(18686));
				}
				else
				{
					this.method_38(this.label_52, (num2 / num9).ToString(Class521.smethod_0(84628)));
				}
				if (num10 == 0)
				{
					this.method_38(this.label_51, Class521.smethod_0(18686));
				}
				else
				{
					this.method_38(this.label_51, (num3 / num10).ToString(Class521.smethod_0(84628)));
				}
				this.method_38(this.label_50, Base.Trading.smethod_192(iqueryable_0).ToString() + Class521.smethod_0(5041) + Base.Trading.smethod_193(iqueryable_0).ToString(Class521.smethod_0(87669)) + Class521.smethod_0(5046));
				this.method_38(this.label_47, Base.Trading.smethod_200(iqueryable_0).ToString() + Class521.smethod_0(5041) + Base.Trading.smethod_201(iqueryable_0).ToString(Class521.smethod_0(87669)) + Class521.smethod_0(5046));
				this.method_38(this.label_46, Base.Trading.smethod_191(iqueryable_0).ToString(Class521.smethod_0(84628)));
				this.method_38(this.label_45, Base.Trading.smethod_190(iqueryable_0).ToString(Class521.smethod_0(84628)));
				if (num9 == 0)
				{
					this.method_38(this.label_44, Class521.smethod_0(18686));
				}
				else
				{
					this.method_38(this.label_44, Base.Trading.smethod_195(iqueryable_0).ToString(Class521.smethod_0(87654)));
				}
				if (num10 == 0)
				{
					this.method_38(this.label_43, Class521.smethod_0(18686));
				}
				else
				{
					this.method_38(this.label_43, Base.Trading.smethod_196(iqueryable_0).ToString(Class521.smethod_0(87654)));
				}
			}
		}

		// Token: 0x06001E6B RID: 7787 RVA: 0x0000CBEE File Offset: 0x0000ADEE
		public void method_38(Control control_0, string string_0)
		{
			if (control_0.InvokeRequired)
			{
				control_0.BeginInvoke(new TrdAnalysisPanel.Delegate34(this.method_38), new object[]
				{
					control_0,
					string_0
				});
			}
			else
			{
				control_0.Text = string_0;
			}
		}

		// Token: 0x06001E6C RID: 7788 RVA: 0x0000CC24 File Offset: 0x0000AE24
		public void method_39(Control control_0, Color color_1)
		{
			if (control_0.InvokeRequired)
			{
				control_0.BeginInvoke(new TrdAnalysisPanel.Delegate35(this.method_39), new object[]
				{
					control_0,
					color_1
				});
			}
			else
			{
				control_0.ForeColor = color_1;
			}
		}

		// Token: 0x06001E6D RID: 7789 RVA: 0x0000CC5F File Offset: 0x0000AE5F
		public void method_40(string string_0)
		{
			this.control11_0.method_0(string_0);
		}

		// Token: 0x06001E6E RID: 7790 RVA: 0x0000CC6F File Offset: 0x0000AE6F
		private void method_41(object sender, EventArgs e)
		{
			if (!base.IsDisposed && base.IsHandleCreated)
			{
				this.method_6();
			}
		}

		// Token: 0x06001E6F RID: 7791 RVA: 0x0000CC89 File Offset: 0x0000AE89
		private void method_42(EventArgs17 eventArgs17_0)
		{
			this.method_44(false);
		}

		// Token: 0x06001E70 RID: 7792 RVA: 0x0000CC89 File Offset: 0x0000AE89
		private void method_43(object sender, EventArgs e)
		{
			this.method_44(false);
		}

		// Token: 0x06001E71 RID: 7793 RVA: 0x000D9120 File Offset: 0x000D7320
		private void method_44(bool bool_0 = false)
		{
			if (!bool_0 || this.tabItem_0.Visible)
			{
				string text = this.tabItem_0.Parent.SelectedTab.Text;
				if (text.Equals(Class521.smethod_0(87674)))
				{
					this.method_7();
				}
				else if (text.Equals(Class521.smethod_0(83172)))
				{
					this.method_10();
				}
			}
		}

		// Token: 0x170004CA RID: 1226
		// (set) Token: 0x06001E72 RID: 7794 RVA: 0x0000CC94 File Offset: 0x0000AE94
		public TabColorScheme ColorScheme
		{
			set
			{
				if (this.control11_0 != null)
				{
					this.control11_0.ColorScheme = value;
				}
			}
		}

		// Token: 0x06001E73 RID: 7795 RVA: 0x0000CCAC File Offset: 0x0000AEAC
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001E74 RID: 7796 RVA: 0x000D9188 File Offset: 0x000D7388
		private void method_45()
		{
			this.icontainer_0 = new Container();
			this.control11_0 = new Control11();
			this.tabControlPanel_0 = new TabControlPanel();
			this.panel_0 = new Panel();
			this.label_0 = new System.Windows.Forms.Label();
			this.label_1 = new System.Windows.Forms.Label();
			this.label_2 = new System.Windows.Forms.Label();
			this.label_3 = new System.Windows.Forms.Label();
			this.label_4 = new System.Windows.Forms.Label();
			this.label_5 = new System.Windows.Forms.Label();
			this.label_6 = new System.Windows.Forms.Label();
			this.label_7 = new System.Windows.Forms.Label();
			this.label_8 = new System.Windows.Forms.Label();
			this.label_9 = new System.Windows.Forms.Label();
			this.label_10 = new System.Windows.Forms.Label();
			this.label_11 = new System.Windows.Forms.Label();
			this.label_12 = new System.Windows.Forms.Label();
			this.label_13 = new System.Windows.Forms.Label();
			this.label_14 = new System.Windows.Forms.Label();
			this.label_15 = new System.Windows.Forms.Label();
			this.tabItem_0 = new TabItem(this.icontainer_0);
			this.tabControlPanel_1 = new TabControlPanel();
			this.panel_1 = new Panel();
			this.class298_0 = new Class298();
			this.label_16 = new System.Windows.Forms.Label();
			this.label_17 = new System.Windows.Forms.Label();
			this.label_18 = new System.Windows.Forms.Label();
			this.label_19 = new System.Windows.Forms.Label();
			this.label_20 = new System.Windows.Forms.Label();
			this.label_21 = new System.Windows.Forms.Label();
			this.label_22 = new System.Windows.Forms.Label();
			this.label_23 = new System.Windows.Forms.Label();
			this.class298_1 = new Class298();
			this.comboBox_0 = new ComboBox();
			this.comboBox_1 = new ComboBox();
			this.comboBox_2 = new ComboBox();
			this.label_24 = new System.Windows.Forms.Label();
			this.label_25 = new System.Windows.Forms.Label();
			this.label_26 = new System.Windows.Forms.Label();
			this.comboBox_3 = new ComboBox();
			this.groupBox_0 = new GroupBox();
			this.class298_2 = new Class298();
			this.label_27 = new System.Windows.Forms.Label();
			this.label_28 = new System.Windows.Forms.Label();
			this.label_29 = new System.Windows.Forms.Label();
			this.label_30 = new System.Windows.Forms.Label();
			this.label_31 = new System.Windows.Forms.Label();
			this.label_32 = new System.Windows.Forms.Label();
			this.label_33 = new System.Windows.Forms.Label();
			this.label_34 = new System.Windows.Forms.Label();
			this.label_35 = new System.Windows.Forms.Label();
			this.label_36 = new System.Windows.Forms.Label();
			this.label_37 = new System.Windows.Forms.Label();
			this.label_38 = new System.Windows.Forms.Label();
			this.label_39 = new System.Windows.Forms.Label();
			this.label_40 = new System.Windows.Forms.Label();
			this.label_41 = new System.Windows.Forms.Label();
			this.label_42 = new System.Windows.Forms.Label();
			this.label_43 = new System.Windows.Forms.Label();
			this.label_44 = new System.Windows.Forms.Label();
			this.label_45 = new System.Windows.Forms.Label();
			this.label_46 = new System.Windows.Forms.Label();
			this.label_47 = new System.Windows.Forms.Label();
			this.label_48 = new System.Windows.Forms.Label();
			this.label_49 = new System.Windows.Forms.Label();
			this.label_50 = new System.Windows.Forms.Label();
			this.label_51 = new System.Windows.Forms.Label();
			this.label_52 = new System.Windows.Forms.Label();
			this.label_53 = new System.Windows.Forms.Label();
			this.label_54 = new System.Windows.Forms.Label();
			this.label_55 = new System.Windows.Forms.Label();
			this.label_56 = new System.Windows.Forms.Label();
			this.label_57 = new System.Windows.Forms.Label();
			this.label_58 = new System.Windows.Forms.Label();
			this.label_59 = new System.Windows.Forms.Label();
			this.label_60 = new System.Windows.Forms.Label();
			this.label_61 = new System.Windows.Forms.Label();
			this.label_62 = new System.Windows.Forms.Label();
			this.label_63 = new System.Windows.Forms.Label();
			this.label_64 = new System.Windows.Forms.Label();
			this.label_65 = new System.Windows.Forms.Label();
			this.label_66 = new System.Windows.Forms.Label();
			this.label_67 = new System.Windows.Forms.Label();
			this.label_68 = new System.Windows.Forms.Label();
			this.label_69 = new System.Windows.Forms.Label();
			this.label_70 = new System.Windows.Forms.Label();
			this.label_71 = new System.Windows.Forms.Label();
			this.label_72 = new System.Windows.Forms.Label();
			this.label_73 = new System.Windows.Forms.Label();
			this.label_74 = new System.Windows.Forms.Label();
			this.label_75 = new System.Windows.Forms.Label();
			this.label_76 = new System.Windows.Forms.Label();
			this.label_77 = new System.Windows.Forms.Label();
			this.label_78 = new System.Windows.Forms.Label();
			this.label_79 = new System.Windows.Forms.Label();
			this.label_80 = new System.Windows.Forms.Label();
			this.label_81 = new System.Windows.Forms.Label();
			this.label_82 = new System.Windows.Forms.Label();
			this.label_83 = new System.Windows.Forms.Label();
			this.label_84 = new System.Windows.Forms.Label();
			this.label_85 = new System.Windows.Forms.Label();
			this.tabItem_1 = new TabItem(this.icontainer_0);
			this.tabControlPanel_2 = new TabControlPanel();
			this.tabItem_2 = new TabItem(this.icontainer_0);
			this.tabControlPanel_3 = new TabControlPanel();
			this.tabItem_3 = new TabItem(this.icontainer_0);
			this.tabControlPanel_4 = new TabControlPanel();
			this.tabItem_4 = new TabItem(this.icontainer_0);
			this.tabControlPanel_5 = new TabControlPanel();
			this.tabItem_5 = new TabItem(this.icontainer_0);
			this.tabControlPanel_6 = new TabControlPanel();
			this.splitContainer_0 = new SplitContainer();
			this.tabItem_6 = new TabItem(this.icontainer_0);
			((ISupportInitialize)this.control11_0).BeginInit();
			this.control11_0.SuspendLayout();
			this.tabControlPanel_0.SuspendLayout();
			this.panel_0.SuspendLayout();
			this.tabControlPanel_1.SuspendLayout();
			this.panel_1.SuspendLayout();
			this.class298_0.SuspendLayout();
			this.class298_1.SuspendLayout();
			this.class298_2.SuspendLayout();
			this.tabControlPanel_6.SuspendLayout();
			this.splitContainer_0.SuspendLayout();
			base.SuspendLayout();
			this.control11_0.CanReorderTabs = true;
			this.control11_0.Controls.Add(this.tabControlPanel_0);
			this.control11_0.Controls.Add(this.tabControlPanel_1);
			this.control11_0.Controls.Add(this.tabControlPanel_2);
			this.control11_0.Controls.Add(this.tabControlPanel_3);
			this.control11_0.Controls.Add(this.tabControlPanel_4);
			this.control11_0.Controls.Add(this.tabControlPanel_5);
			this.control11_0.Controls.Add(this.tabControlPanel_6);
			this.control11_0.Dock = DockStyle.Fill;
			this.control11_0.Font = new Font(Class521.smethod_0(7183), 9f);
			this.control11_0.Location = new Point(0, 0);
			this.control11_0.MinimumSize = new Size(229, 138);
			this.control11_0.Name = Class521.smethod_0(87691);
			this.control11_0.SelectedTabFont = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold);
			this.control11_0.SelectedTabIndex = 0;
			this.control11_0.Size = new Size(1189, 676);
			this.control11_0.Style = eTabStripStyle.Flat;
			this.control11_0.TabAlignment = eTabStripAlignment.Bottom;
			this.control11_0.TabIndex = 3;
			this.control11_0.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.control11_0.Tabs.Add(this.tabItem_0);
			this.control11_0.Tabs.Add(this.tabItem_1);
			this.control11_0.Tabs.Add(this.tabItem_2);
			this.control11_0.Tabs.Add(this.tabItem_3);
			this.control11_0.Tabs.Add(this.tabItem_4);
			this.control11_0.Tabs.Add(this.tabItem_6);
			this.control11_0.Tabs.Add(this.tabItem_5);
			this.control11_0.Text = Class521.smethod_0(87708);
			this.tabControlPanel_0.AutoScroll = true;
			this.tabControlPanel_0.CanvasColor = SystemColors.Control;
			this.tabControlPanel_0.ColorSchemeStyle = eDotNetBarStyle.Metro;
			this.tabControlPanel_0.Controls.Add(this.panel_0);
			this.tabControlPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_0.Location = new Point(0, 0);
			this.tabControlPanel_0.Name = Class521.smethod_0(87721);
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_0.Size = new Size(1189, 648);
			this.tabControlPanel_0.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_0.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_0.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_0.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_0.Style.GradientAngle = -90;
			this.tabControlPanel_0.TabIndex = 4;
			this.tabControlPanel_0.TabItem = this.tabItem_0;
			this.panel_0.BackColor = Color.Transparent;
			this.panel_0.Controls.Add(this.label_0);
			this.panel_0.Controls.Add(this.label_1);
			this.panel_0.Controls.Add(this.label_2);
			this.panel_0.Controls.Add(this.label_3);
			this.panel_0.Controls.Add(this.label_4);
			this.panel_0.Controls.Add(this.label_5);
			this.panel_0.Controls.Add(this.label_6);
			this.panel_0.Controls.Add(this.label_7);
			this.panel_0.Controls.Add(this.label_8);
			this.panel_0.Controls.Add(this.label_9);
			this.panel_0.Controls.Add(this.label_10);
			this.panel_0.Controls.Add(this.label_11);
			this.panel_0.Controls.Add(this.label_12);
			this.panel_0.Controls.Add(this.label_13);
			this.panel_0.Controls.Add(this.label_14);
			this.panel_0.Controls.Add(this.label_15);
			this.panel_0.Dock = DockStyle.Fill;
			this.panel_0.Location = new Point(1, 1);
			this.panel_0.Name = Class521.smethod_0(87750);
			this.panel_0.Size = new Size(1187, 646);
			this.panel_0.TabIndex = 16;
			this.label_0.BackColor = Color.Transparent;
			this.label_0.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_0.Location = new Point(456, 131);
			this.label_0.MinimumSize = new Size(114, 0);
			this.label_0.Name = Class521.smethod_0(87771);
			this.label_0.Size = new Size(135, 20);
			this.label_0.TabIndex = 15;
			this.label_0.Text = Class521.smethod_0(87771);
			this.label_0.TextAlign = ContentAlignment.MiddleRight;
			this.label_1.BackColor = Color.Transparent;
			this.label_1.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_1.Location = new Point(29, 23);
			this.label_1.Name = Class521.smethod_0(87796);
			this.label_1.Size = new Size(100, 20);
			this.label_1.TabIndex = 0;
			this.label_1.Text = Class521.smethod_0(20404);
			this.label_1.TextAlign = ContentAlignment.MiddleLeft;
			this.label_2.BackColor = Color.Transparent;
			this.label_2.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_2.Location = new Point(456, 95);
			this.label_2.MinimumSize = new Size(114, 0);
			this.label_2.Name = Class521.smethod_0(87821);
			this.label_2.Size = new Size(135, 20);
			this.label_2.TabIndex = 14;
			this.label_2.Text = Class521.smethod_0(87821);
			this.label_2.TextAlign = ContentAlignment.MiddleRight;
			this.label_3.BackColor = Color.Transparent;
			this.label_3.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_3.Location = new Point(29, 59);
			this.label_3.Name = Class521.smethod_0(87838);
			this.label_3.Size = new Size(100, 20);
			this.label_3.TabIndex = 1;
			this.label_3.Text = Class521.smethod_0(87871);
			this.label_3.TextAlign = ContentAlignment.MiddleLeft;
			this.label_4.BackColor = Color.Transparent;
			this.label_4.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_4.Location = new Point(456, 59);
			this.label_4.MinimumSize = new Size(114, 0);
			this.label_4.Name = Class521.smethod_0(87892);
			this.label_4.Size = new Size(135, 20);
			this.label_4.TabIndex = 13;
			this.label_4.Text = Class521.smethod_0(87892);
			this.label_4.TextAlign = ContentAlignment.MiddleRight;
			this.label_5.BackColor = Color.Transparent;
			this.label_5.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_5.Location = new Point(354, 23);
			this.label_5.Name = Class521.smethod_0(87917);
			this.label_5.Size = new Size(100, 20);
			this.label_5.TabIndex = 2;
			this.label_5.Text = Class521.smethod_0(64730);
			this.label_5.TextAlign = ContentAlignment.MiddleLeft;
			this.label_6.BackColor = Color.Transparent;
			this.label_6.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_6.Location = new Point(140, 131);
			this.label_6.MinimumSize = new Size(114, 0);
			this.label_6.Name = Class521.smethod_0(87950);
			this.label_6.Size = new Size(135, 20);
			this.label_6.TabIndex = 12;
			this.label_6.Text = Class521.smethod_0(87950);
			this.label_6.TextAlign = ContentAlignment.MiddleRight;
			this.label_7.BackColor = Color.Transparent;
			this.label_7.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_7.Location = new Point(354, 59);
			this.label_7.Name = Class521.smethod_0(87967);
			this.label_7.Size = new Size(100, 20);
			this.label_7.TabIndex = 3;
			this.label_7.Text = Class521.smethod_0(70863);
			this.label_7.TextAlign = ContentAlignment.MiddleLeft;
			this.label_8.BackColor = Color.Transparent;
			this.label_8.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_8.Location = new Point(140, 95);
			this.label_8.MinimumSize = new Size(114, 0);
			this.label_8.Name = Class521.smethod_0(88000);
			this.label_8.Size = new Size(135, 20);
			this.label_8.TabIndex = 11;
			this.label_8.Text = Class521.smethod_0(88000);
			this.label_8.TextAlign = ContentAlignment.MiddleRight;
			this.label_9.BackColor = Color.Transparent;
			this.label_9.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_9.Location = new Point(29, 95);
			this.label_9.Name = Class521.smethod_0(88025);
			this.label_9.Size = new Size(100, 20);
			this.label_9.TabIndex = 4;
			this.label_9.Text = Class521.smethod_0(88058);
			this.label_9.TextAlign = ContentAlignment.MiddleLeft;
			this.label_10.BackColor = Color.Transparent;
			this.label_10.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_10.Location = new Point(456, 23);
			this.label_10.MinimumSize = new Size(114, 0);
			this.label_10.Name = Class521.smethod_0(88079);
			this.label_10.Size = new Size(135, 20);
			this.label_10.TabIndex = 10;
			this.label_10.Text = Class521.smethod_0(88104);
			this.label_10.TextAlign = ContentAlignment.MiddleRight;
			this.label_11.BackColor = Color.Transparent;
			this.label_11.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_11.Location = new Point(354, 95);
			this.label_11.Name = Class521.smethod_0(88121);
			this.label_11.Size = new Size(100, 20);
			this.label_11.TabIndex = 5;
			this.label_11.Text = Class521.smethod_0(88154);
			this.label_11.TextAlign = ContentAlignment.MiddleLeft;
			this.label_12.BackColor = Color.Transparent;
			this.label_12.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_12.Location = new Point(140, 59);
			this.label_12.MinimumSize = new Size(114, 0);
			this.label_12.Name = Class521.smethod_0(88175);
			this.label_12.Size = new Size(135, 20);
			this.label_12.TabIndex = 9;
			this.label_12.Text = Class521.smethod_0(88200);
			this.label_12.TextAlign = ContentAlignment.MiddleRight;
			this.label_13.BackColor = Color.Transparent;
			this.label_13.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_13.Location = new Point(29, 131);
			this.label_13.Name = Class521.smethod_0(88217);
			this.label_13.Size = new Size(100, 20);
			this.label_13.TabIndex = 6;
			this.label_13.Text = Class521.smethod_0(88246);
			this.label_13.TextAlign = ContentAlignment.MiddleLeft;
			this.label_14.BackColor = Color.Transparent;
			this.label_14.Font = new Font(Class521.smethod_0(24023), 8.6f);
			this.label_14.Location = new Point(140, 23);
			this.label_14.MinimumSize = new Size(114, 0);
			this.label_14.Name = Class521.smethod_0(88263);
			this.label_14.Size = new Size(135, 20);
			this.label_14.TabIndex = 8;
			this.label_14.Text = Class521.smethod_0(88263);
			this.label_14.TextAlign = ContentAlignment.MiddleRight;
			this.label_15.BackColor = Color.Transparent;
			this.label_15.Font = new Font(Class521.smethod_0(7183), 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_15.Location = new Point(354, 131);
			this.label_15.Name = Class521.smethod_0(88284);
			this.label_15.Size = new Size(100, 20);
			this.label_15.TabIndex = 7;
			this.label_15.Text = Class521.smethod_0(88313);
			this.label_15.TextAlign = ContentAlignment.MiddleLeft;
			this.tabItem_0.AttachedControl = this.tabControlPanel_0;
			this.tabItem_0.Name = Class521.smethod_0(86831);
			this.tabItem_0.Text = Class521.smethod_0(87674);
			this.tabControlPanel_1.AutoScroll = true;
			this.tabControlPanel_1.Controls.Add(this.panel_1);
			this.tabControlPanel_1.Dock = DockStyle.Fill;
			this.tabControlPanel_1.Location = new Point(0, 0);
			this.tabControlPanel_1.Name = Class521.smethod_0(88330);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_1.Size = new Size(1189, 648);
			this.tabControlPanel_1.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_1.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_1.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_1.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_1.Style.GradientAngle = -90;
			this.tabControlPanel_1.TabIndex = 5;
			this.tabControlPanel_1.TabItem = this.tabItem_1;
			this.panel_1.AutoScroll = true;
			this.panel_1.Controls.Add(this.class298_0);
			this.panel_1.Controls.Add(this.class298_1);
			this.panel_1.Controls.Add(this.groupBox_0);
			this.panel_1.Controls.Add(this.class298_2);
			this.panel_1.Dock = DockStyle.Fill;
			this.panel_1.Location = new Point(1, 1);
			this.panel_1.Margin = new System.Windows.Forms.Padding(0);
			this.panel_1.Name = Class521.smethod_0(88363);
			this.panel_1.Size = new Size(1187, 646);
			this.panel_1.TabIndex = 32;
			this.class298_0.BackColor = Color.Transparent;
			this.class298_0.BorderColor = Color.Empty;
			this.class298_0.Controls.Add(this.label_16);
			this.class298_0.Controls.Add(this.label_17);
			this.class298_0.Controls.Add(this.label_18);
			this.class298_0.Controls.Add(this.label_19);
			this.class298_0.Controls.Add(this.label_20);
			this.class298_0.Controls.Add(this.label_21);
			this.class298_0.Controls.Add(this.label_22);
			this.class298_0.Controls.Add(this.label_23);
			this.class298_0.DrawCustomBorder = false;
			this.class298_0.Location = new Point(6, 6);
			this.class298_0.Name = Class521.smethod_0(88388);
			this.class298_0.Size = new Size(838, 34);
			this.class298_0.TabIndex = 30;
			this.label_16.BackColor = Color.Transparent;
			this.label_16.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_16.Location = new Point(718, 4);
			this.label_16.Name = Class521.smethod_0(88421);
			this.label_16.Size = new Size(104, 20);
			this.label_16.TabIndex = 18;
			this.label_16.Text = Class521.smethod_0(88421);
			this.label_16.TextAlign = ContentAlignment.BottomRight;
			this.label_17.BackColor = Color.Transparent;
			this.label_17.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_17.Location = new Point(3, 5);
			this.label_17.Name = Class521.smethod_0(5871);
			this.label_17.Size = new Size(65, 20);
			this.label_17.TabIndex = 2;
			this.label_17.Text = Class521.smethod_0(20404);
			this.label_17.TextAlign = ContentAlignment.BottomLeft;
			this.label_18.BackColor = Color.Transparent;
			this.label_18.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_18.Location = new Point(196, 5);
			this.label_18.Name = Class521.smethod_0(5827);
			this.label_18.Size = new Size(100, 20);
			this.label_18.TabIndex = 3;
			this.label_18.Text = Class521.smethod_0(64730);
			this.label_18.TextAlign = ContentAlignment.BottomLeft;
			this.label_19.BackColor = Color.Transparent;
			this.label_19.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_19.Location = new Point(615, 5);
			this.label_19.Name = Class521.smethod_0(7019);
			this.label_19.Size = new Size(100, 20);
			this.label_19.TabIndex = 5;
			this.label_19.Text = Class521.smethod_0(88058);
			this.label_19.TextAlign = ContentAlignment.BottomLeft;
			this.label_20.BackColor = Color.Transparent;
			this.label_20.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_20.Location = new Point(410, 5);
			this.label_20.Name = Class521.smethod_0(5915);
			this.label_20.Size = new Size(100, 20);
			this.label_20.TabIndex = 7;
			this.label_20.Text = Class521.smethod_0(88442);
			this.label_20.TextAlign = ContentAlignment.BottomLeft;
			this.label_21.BackColor = Color.Transparent;
			this.label_21.Font = new Font(Class521.smethod_0(24023), 9f);
			this.label_21.Location = new Point(471, 5);
			this.label_21.Name = Class521.smethod_0(88463);
			this.label_21.Size = new Size(124, 20);
			this.label_21.TabIndex = 19;
			this.label_21.Text = Class521.smethod_0(88463);
			this.label_21.TextAlign = ContentAlignment.BottomRight;
			this.label_22.BackColor = Color.Transparent;
			this.label_22.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_22.Location = new Point(61, 5);
			this.label_22.Name = Class521.smethod_0(88480);
			this.label_22.Size = new Size(120, 20);
			this.label_22.TabIndex = 15;
			this.label_22.Text = Class521.smethod_0(88480);
			this.label_22.TextAlign = ContentAlignment.BottomRight;
			this.label_23.BackColor = Color.Transparent;
			this.label_23.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_23.Location = new Point(275, 5);
			this.label_23.Name = Class521.smethod_0(88497);
			this.label_23.Size = new Size(114, 20);
			this.label_23.TabIndex = 16;
			this.label_23.Text = Class521.smethod_0(88497);
			this.label_23.TextAlign = ContentAlignment.BottomRight;
			this.class298_1.BackColor = Color.Transparent;
			this.class298_1.BorderColor = Color.Empty;
			this.class298_1.Controls.Add(this.comboBox_0);
			this.class298_1.Controls.Add(this.comboBox_1);
			this.class298_1.Controls.Add(this.comboBox_2);
			this.class298_1.Controls.Add(this.label_24);
			this.class298_1.Controls.Add(this.label_25);
			this.class298_1.Controls.Add(this.label_26);
			this.class298_1.Controls.Add(this.comboBox_3);
			this.class298_1.DrawCustomBorder = false;
			this.class298_1.Location = new Point(6, 46);
			this.class298_1.Name = Class521.smethod_0(88514);
			this.class298_1.Size = new Size(838, 30);
			this.class298_1.TabIndex = 31;
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.Font = new Font(Class521.smethod_0(24023), 9f);
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(277, 2);
			this.comboBox_0.Name = Class521.smethod_0(88543);
			this.comboBox_0.Size = new Size(75, 26);
			this.comboBox_0.TabIndex = 22;
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Location = new Point(1, 2);
			this.comboBox_1.Name = Class521.smethod_0(88564);
			this.comboBox_1.Size = new Size(118, 26);
			this.comboBox_1.TabIndex = 10;
			this.comboBox_2.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_2.Font = new Font(Class521.smethod_0(24023), 9f);
			this.comboBox_2.FormattingEnabled = true;
			this.comboBox_2.Location = new Point(659, 2);
			this.comboBox_2.Name = Class521.smethod_0(88613);
			this.comboBox_2.Size = new Size(161, 26);
			this.comboBox_2.TabIndex = 26;
			this.label_24.BackColor = Color.Transparent;
			this.label_24.Location = new Point(201, 7);
			this.label_24.Name = Class521.smethod_0(88634);
			this.label_24.Size = new Size(70, 20);
			this.label_24.TabIndex = 21;
			this.label_24.Text = Class521.smethod_0(88651);
			this.label_24.TextAlign = ContentAlignment.MiddleRight;
			this.label_25.BackColor = Color.Transparent;
			this.label_25.Location = new Point(584, 7);
			this.label_25.Name = Class521.smethod_0(88664);
			this.label_25.Size = new Size(70, 20);
			this.label_25.TabIndex = 25;
			this.label_25.Text = Class521.smethod_0(5902);
			this.label_25.TextAlign = ContentAlignment.MiddleRight;
			this.label_26.BackColor = Color.Transparent;
			this.label_26.Location = new Point(389, 7);
			this.label_26.Name = Class521.smethod_0(88685);
			this.label_26.Size = new Size(70, 20);
			this.label_26.TabIndex = 23;
			this.label_26.Text = Class521.smethod_0(88702);
			this.label_26.TextAlign = ContentAlignment.MiddleRight;
			this.comboBox_3.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_3.Font = new Font(Class521.smethod_0(24023), 9f);
			this.comboBox_3.FormattingEnabled = true;
			this.comboBox_3.Location = new Point(464, 2);
			this.comboBox_3.Name = Class521.smethod_0(88715);
			this.comboBox_3.Size = new Size(75, 26);
			this.comboBox_3.TabIndex = 24;
			this.groupBox_0.Location = new Point(6, 39);
			this.groupBox_0.Name = Class521.smethod_0(10647);
			this.groupBox_0.Size = new Size(820, 2);
			this.groupBox_0.TabIndex = 20;
			this.groupBox_0.TabStop = false;
			this.class298_2.BackColor = Color.Transparent;
			this.class298_2.BorderColor = Color.Empty;
			this.class298_2.Controls.Add(this.label_27);
			this.class298_2.Controls.Add(this.label_28);
			this.class298_2.Controls.Add(this.label_29);
			this.class298_2.Controls.Add(this.label_30);
			this.class298_2.Controls.Add(this.label_31);
			this.class298_2.Controls.Add(this.label_32);
			this.class298_2.Controls.Add(this.label_33);
			this.class298_2.Controls.Add(this.label_34);
			this.class298_2.Controls.Add(this.label_35);
			this.class298_2.Controls.Add(this.label_36);
			this.class298_2.Controls.Add(this.label_37);
			this.class298_2.Controls.Add(this.label_38);
			this.class298_2.Controls.Add(this.label_39);
			this.class298_2.Controls.Add(this.label_40);
			this.class298_2.Controls.Add(this.label_41);
			this.class298_2.Controls.Add(this.label_42);
			this.class298_2.Controls.Add(this.label_43);
			this.class298_2.Controls.Add(this.label_44);
			this.class298_2.Controls.Add(this.label_45);
			this.class298_2.Controls.Add(this.label_46);
			this.class298_2.Controls.Add(this.label_47);
			this.class298_2.Controls.Add(this.label_48);
			this.class298_2.Controls.Add(this.label_49);
			this.class298_2.Controls.Add(this.label_50);
			this.class298_2.Controls.Add(this.label_51);
			this.class298_2.Controls.Add(this.label_52);
			this.class298_2.Controls.Add(this.label_53);
			this.class298_2.Controls.Add(this.label_54);
			this.class298_2.Controls.Add(this.label_55);
			this.class298_2.Controls.Add(this.label_56);
			this.class298_2.Controls.Add(this.label_57);
			this.class298_2.Controls.Add(this.label_58);
			this.class298_2.Controls.Add(this.label_59);
			this.class298_2.Controls.Add(this.label_60);
			this.class298_2.Controls.Add(this.label_61);
			this.class298_2.Controls.Add(this.label_62);
			this.class298_2.Controls.Add(this.label_63);
			this.class298_2.Controls.Add(this.label_64);
			this.class298_2.Controls.Add(this.label_65);
			this.class298_2.Controls.Add(this.label_66);
			this.class298_2.Controls.Add(this.label_67);
			this.class298_2.Controls.Add(this.label_68);
			this.class298_2.Controls.Add(this.label_69);
			this.class298_2.Controls.Add(this.label_70);
			this.class298_2.Controls.Add(this.label_71);
			this.class298_2.Controls.Add(this.label_72);
			this.class298_2.Controls.Add(this.label_73);
			this.class298_2.Controls.Add(this.label_74);
			this.class298_2.Controls.Add(this.label_75);
			this.class298_2.Controls.Add(this.label_76);
			this.class298_2.Controls.Add(this.label_77);
			this.class298_2.Controls.Add(this.label_78);
			this.class298_2.Controls.Add(this.label_79);
			this.class298_2.Controls.Add(this.label_80);
			this.class298_2.Controls.Add(this.label_81);
			this.class298_2.Controls.Add(this.label_82);
			this.class298_2.Controls.Add(this.label_83);
			this.class298_2.Controls.Add(this.label_84);
			this.class298_2.Controls.Add(this.label_85);
			this.class298_2.DrawCustomBorder = false;
			this.class298_2.Location = new Point(6, 84);
			this.class298_2.Margin = new System.Windows.Forms.Padding(3, 3, 3, 12);
			this.class298_2.Name = Class521.smethod_0(88736);
			this.class298_2.Size = new Size(845, 331);
			this.class298_2.TabIndex = 8;
			this.label_27.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_27.Location = new Point(412, 42);
			this.label_27.Name = Class521.smethod_0(88761);
			this.label_27.Size = new Size(130, 20);
			this.label_27.TabIndex = 54;
			this.label_27.Text = Class521.smethod_0(88761);
			this.label_27.TextAlign = ContentAlignment.MiddleRight;
			this.label_28.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_28.Location = new Point(274, 42);
			this.label_28.Name = Class521.smethod_0(88782);
			this.label_28.Size = new Size(140, 20);
			this.label_28.TabIndex = 30;
			this.label_28.Text = Class521.smethod_0(88795);
			this.label_28.TextAlign = ContentAlignment.MiddleRight;
			this.label_29.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_29.Location = new Point(693, 42);
			this.label_29.Name = Class521.smethod_0(88816);
			this.label_29.Size = new Size(130, 20);
			this.label_29.TabIndex = 21;
			this.label_29.Text = Class521.smethod_0(88816);
			this.label_29.TextAlign = ContentAlignment.MiddleRight;
			this.label_30.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_30.Location = new Point(556, 42);
			this.label_30.Name = Class521.smethod_0(88837);
			this.label_30.Size = new Size(144, 20);
			this.label_30.TabIndex = 20;
			this.label_30.Text = Class521.smethod_0(88858);
			this.label_30.TextAlign = ContentAlignment.MiddleRight;
			this.label_31.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_31.Location = new Point(693, 126);
			this.label_31.Name = Class521.smethod_0(88875);
			this.label_31.Size = new Size(130, 20);
			this.label_31.TabIndex = 53;
			this.label_31.Text = Class521.smethod_0(88875);
			this.label_31.TextAlign = ContentAlignment.MiddleRight;
			this.label_32.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_32.Location = new Point(412, 126);
			this.label_32.Name = Class521.smethod_0(88896);
			this.label_32.Size = new Size(130, 20);
			this.label_32.TabIndex = 52;
			this.label_32.Text = Class521.smethod_0(88925);
			this.label_32.TextAlign = ContentAlignment.MiddleRight;
			this.label_33.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_33.Location = new Point(556, 126);
			this.label_33.Name = Class521.smethod_0(88954);
			this.label_33.Size = new Size(144, 20);
			this.label_33.TabIndex = 51;
			this.label_33.Text = Class521.smethod_0(88967);
			this.label_33.TextAlign = ContentAlignment.MiddleRight;
			this.label_34.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_34.Location = new Point(274, 126);
			this.label_34.Name = Class521.smethod_0(88988);
			this.label_34.Size = new Size(140, 20);
			this.label_34.TabIndex = 50;
			this.label_34.Text = Class521.smethod_0(89001);
			this.label_34.TextAlign = ContentAlignment.MiddleRight;
			this.label_35.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_35.Location = new Point(556, 154);
			this.label_35.Name = Class521.smethod_0(89026);
			this.label_35.Size = new Size(144, 20);
			this.label_35.TabIndex = 49;
			this.label_35.Text = Class521.smethod_0(89039);
			this.label_35.TextAlign = ContentAlignment.MiddleRight;
			this.label_36.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_36.Location = new Point(693, 154);
			this.label_36.Name = Class521.smethod_0(89072);
			this.label_36.Size = new Size(130, 20);
			this.label_36.TabIndex = 48;
			this.label_36.Text = Class521.smethod_0(89072);
			this.label_36.TextAlign = ContentAlignment.MiddleRight;
			this.label_37.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_37.Location = new Point(412, 154);
			this.label_37.Name = Class521.smethod_0(89101);
			this.label_37.Size = new Size(130, 20);
			this.label_37.TabIndex = 47;
			this.label_37.Text = Class521.smethod_0(89101);
			this.label_37.TextAlign = ContentAlignment.MiddleRight;
			this.label_38.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_38.Location = new Point(136, 154);
			this.label_38.Name = Class521.smethod_0(89126);
			this.label_38.Size = new Size(130, 20);
			this.label_38.TabIndex = 46;
			this.label_38.Text = Class521.smethod_0(89126);
			this.label_38.TextAlign = ContentAlignment.MiddleRight;
			this.label_39.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_39.Location = new Point(274, 154);
			this.label_39.Name = Class521.smethod_0(89151);
			this.label_39.Size = new Size(140, 20);
			this.label_39.TabIndex = 6;
			this.label_39.Text = Class521.smethod_0(89164);
			this.label_39.TextAlign = ContentAlignment.MiddleRight;
			this.label_40.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_40.Location = new Point(4, 154);
			this.label_40.Name = Class521.smethod_0(89189);
			this.label_40.Size = new Size(140, 20);
			this.label_40.TabIndex = 44;
			this.label_40.Text = Class521.smethod_0(89202);
			this.label_40.TextAlign = ContentAlignment.MiddleRight;
			this.label_41.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_41.ImageAlign = ContentAlignment.MiddleRight;
			this.label_41.Location = new Point(693, 98);
			this.label_41.Name = Class521.smethod_0(89231);
			this.label_41.Size = new Size(130, 20);
			this.label_41.TabIndex = 43;
			this.label_41.Text = Class521.smethod_0(89231);
			this.label_41.TextAlign = ContentAlignment.MiddleRight;
			this.label_42.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_42.Location = new Point(412, 98);
			this.label_42.Name = Class521.smethod_0(89260);
			this.label_42.Size = new Size(130, 20);
			this.label_42.TabIndex = 36;
			this.label_42.Text = Class521.smethod_0(89260);
			this.label_42.TextAlign = ContentAlignment.MiddleRight;
			this.label_43.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_43.ImageAlign = ContentAlignment.MiddleRight;
			this.label_43.Location = new Point(693, 294);
			this.label_43.Name = Class521.smethod_0(89289);
			this.label_43.Size = new Size(130, 20);
			this.label_43.TabIndex = 42;
			this.label_43.Text = Class521.smethod_0(89289);
			this.label_43.TextAlign = ContentAlignment.MiddleRight;
			this.label_44.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_44.Location = new Point(412, 294);
			this.label_44.Name = Class521.smethod_0(89318);
			this.label_44.Size = new Size(130, 20);
			this.label_44.TabIndex = 41;
			this.label_44.Text = Class521.smethod_0(89318);
			this.label_44.TextAlign = ContentAlignment.MiddleRight;
			this.label_45.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_45.ImageAlign = ContentAlignment.MiddleRight;
			this.label_45.Location = new Point(693, 266);
			this.label_45.Name = Class521.smethod_0(89343);
			this.label_45.Size = new Size(130, 20);
			this.label_45.TabIndex = 41;
			this.label_45.Text = Class521.smethod_0(89343);
			this.label_45.TextAlign = ContentAlignment.MiddleRight;
			this.label_46.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_46.Location = new Point(412, 266);
			this.label_46.Name = Class521.smethod_0(89372);
			this.label_46.Size = new Size(130, 20);
			this.label_46.TabIndex = 40;
			this.label_46.Text = Class521.smethod_0(89372);
			this.label_46.TextAlign = ContentAlignment.MiddleRight;
			this.label_47.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_47.ImageAlign = ContentAlignment.MiddleRight;
			this.label_47.Location = new Point(693, 238);
			this.label_47.Name = Class521.smethod_0(89397);
			this.label_47.Size = new Size(130, 20);
			this.label_47.TabIndex = 40;
			this.label_47.Text = Class521.smethod_0(89397);
			this.label_47.TextAlign = ContentAlignment.MiddleRight;
			this.label_48.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_48.ImageAlign = ContentAlignment.MiddleRight;
			this.label_48.Location = new Point(136, 42);
			this.label_48.Name = Class521.smethod_0(89426);
			this.label_48.Size = new Size(130, 20);
			this.label_48.TabIndex = 31;
			this.label_48.Text = Class521.smethod_0(89426);
			this.label_48.TextAlign = ContentAlignment.MiddleRight;
			this.label_49.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_49.Location = new Point(4, 42);
			this.label_49.Name = Class521.smethod_0(89439);
			this.label_49.Size = new Size(140, 20);
			this.label_49.TabIndex = 19;
			this.label_49.Text = Class521.smethod_0(88246);
			this.label_49.TextAlign = ContentAlignment.MiddleRight;
			this.label_50.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_50.Location = new Point(412, 238);
			this.label_50.Name = Class521.smethod_0(89452);
			this.label_50.Size = new Size(130, 20);
			this.label_50.TabIndex = 39;
			this.label_50.Text = Class521.smethod_0(89452);
			this.label_50.TextAlign = ContentAlignment.MiddleRight;
			this.label_51.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_51.ImageAlign = ContentAlignment.MiddleRight;
			this.label_51.Location = new Point(693, 210);
			this.label_51.Name = Class521.smethod_0(89477);
			this.label_51.Size = new Size(130, 20);
			this.label_51.TabIndex = 38;
			this.label_51.Text = Class521.smethod_0(89477);
			this.label_51.TextAlign = ContentAlignment.MiddleRight;
			this.label_52.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_52.Location = new Point(412, 210);
			this.label_52.Name = Class521.smethod_0(89498);
			this.label_52.Size = new Size(130, 20);
			this.label_52.TabIndex = 37;
			this.label_52.Text = Class521.smethod_0(89498);
			this.label_52.TextAlign = ContentAlignment.MiddleRight;
			this.label_53.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_53.ImageAlign = ContentAlignment.MiddleRight;
			this.label_53.Location = new Point(693, 182);
			this.label_53.Name = Class521.smethod_0(89519);
			this.label_53.Size = new Size(130, 20);
			this.label_53.TabIndex = 36;
			this.label_53.Text = Class521.smethod_0(89519);
			this.label_53.TextAlign = ContentAlignment.MiddleRight;
			this.label_54.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_54.Location = new Point(412, 182);
			this.label_54.Name = Class521.smethod_0(89540);
			this.label_54.Size = new Size(130, 20);
			this.label_54.TabIndex = 35;
			this.label_54.Text = Class521.smethod_0(89540);
			this.label_54.TextAlign = ContentAlignment.MiddleRight;
			this.label_55.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_55.ImageAlign = ContentAlignment.MiddleRight;
			this.label_55.Location = new Point(693, 70);
			this.label_55.Name = Class521.smethod_0(89561);
			this.label_55.Size = new Size(130, 20);
			this.label_55.TabIndex = 34;
			this.label_55.Text = Class521.smethod_0(89561);
			this.label_55.TextAlign = ContentAlignment.MiddleRight;
			this.label_56.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_56.Location = new Point(412, 70);
			this.label_56.Name = Class521.smethod_0(89582);
			this.label_56.Size = new Size(130, 20);
			this.label_56.TabIndex = 33;
			this.label_56.Text = Class521.smethod_0(89582);
			this.label_56.TextAlign = ContentAlignment.MiddleRight;
			this.label_57.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_57.Location = new Point(136, 98);
			this.label_57.Name = Class521.smethod_0(89603);
			this.label_57.Size = new Size(130, 20);
			this.label_57.TabIndex = 32;
			this.label_57.Text = Class521.smethod_0(89603);
			this.label_57.TextAlign = ContentAlignment.MiddleRight;
			this.label_58.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_58.Location = new Point(136, 70);
			this.label_58.Name = Class521.smethod_0(89628);
			this.label_58.Size = new Size(130, 20);
			this.label_58.TabIndex = 30;
			this.label_58.Text = Class521.smethod_0(89628);
			this.label_58.TextAlign = ContentAlignment.MiddleRight;
			this.label_59.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_59.ImageAlign = ContentAlignment.MiddleRight;
			this.label_59.Location = new Point(693, 14);
			this.label_59.Name = Class521.smethod_0(89653);
			this.label_59.Size = new Size(130, 20);
			this.label_59.TabIndex = 28;
			this.label_59.Text = Class521.smethod_0(89653);
			this.label_59.TextAlign = ContentAlignment.MiddleRight;
			this.label_60.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_60.Location = new Point(412, 14);
			this.label_60.Name = Class521.smethod_0(89674);
			this.label_60.Size = new Size(130, 20);
			this.label_60.TabIndex = 27;
			this.label_60.Text = Class521.smethod_0(89674);
			this.label_60.TextAlign = ContentAlignment.MiddleRight;
			this.label_61.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_61.Location = new Point(136, 14);
			this.label_61.Name = Class521.smethod_0(89695);
			this.label_61.Size = new Size(130, 20);
			this.label_61.TabIndex = 26;
			this.label_61.Text = Class521.smethod_0(89695);
			this.label_61.TextAlign = ContentAlignment.MiddleRight;
			this.label_62.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_62.Location = new Point(556, 294);
			this.label_62.Name = Class521.smethod_0(89716);
			this.label_62.Size = new Size(144, 20);
			this.label_62.TabIndex = 25;
			this.label_62.Text = Class521.smethod_0(89729);
			this.label_62.TextAlign = ContentAlignment.MiddleRight;
			this.label_63.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_63.Location = new Point(274, 294);
			this.label_63.Name = Class521.smethod_0(89750);
			this.label_63.Size = new Size(140, 20);
			this.label_63.TabIndex = 24;
			this.label_63.Text = Class521.smethod_0(89763);
			this.label_63.TextAlign = ContentAlignment.MiddleRight;
			this.label_64.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_64.Location = new Point(4, 294);
			this.label_64.Name = Class521.smethod_0(89784);
			this.label_64.Size = new Size(140, 20);
			this.label_64.TabIndex = 23;
			this.label_64.Text = Class521.smethod_0(89797);
			this.label_64.TextAlign = ContentAlignment.MiddleRight;
			this.label_65.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_65.Location = new Point(556, 266);
			this.label_65.Name = Class521.smethod_0(89818);
			this.label_65.Size = new Size(144, 20);
			this.label_65.TabIndex = 22;
			this.label_65.Text = Class521.smethod_0(89831);
			this.label_65.TextAlign = ContentAlignment.MiddleRight;
			this.label_66.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_66.Location = new Point(274, 266);
			this.label_66.Name = Class521.smethod_0(89852);
			this.label_66.Size = new Size(140, 20);
			this.label_66.TabIndex = 21;
			this.label_66.Text = Class521.smethod_0(89865);
			this.label_66.TextAlign = ContentAlignment.MiddleRight;
			this.label_67.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_67.Location = new Point(4, 266);
			this.label_67.Name = Class521.smethod_0(89886);
			this.label_67.Size = new Size(140, 20);
			this.label_67.TabIndex = 20;
			this.label_67.Text = Class521.smethod_0(89899);
			this.label_67.TextAlign = ContentAlignment.MiddleRight;
			this.label_68.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_68.Location = new Point(556, 238);
			this.label_68.Name = Class521.smethod_0(89920);
			this.label_68.Size = new Size(144, 20);
			this.label_68.TabIndex = 18;
			this.label_68.Text = Class521.smethod_0(89933);
			this.label_68.TextAlign = ContentAlignment.MiddleRight;
			this.label_69.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_69.Location = new Point(274, 238);
			this.label_69.Name = Class521.smethod_0(89958);
			this.label_69.Size = new Size(140, 20);
			this.label_69.TabIndex = 17;
			this.label_69.Text = Class521.smethod_0(89971);
			this.label_69.TextAlign = ContentAlignment.MiddleRight;
			this.label_70.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_70.Location = new Point(4, 238);
			this.label_70.Name = Class521.smethod_0(89996);
			this.label_70.Size = new Size(140, 20);
			this.label_70.TabIndex = 16;
			this.label_70.Text = Class521.smethod_0(89899);
			this.label_70.TextAlign = ContentAlignment.MiddleRight;
			this.label_71.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_71.Location = new Point(556, 210);
			this.label_71.Name = Class521.smethod_0(90009);
			this.label_71.Size = new Size(144, 20);
			this.label_71.TabIndex = 15;
			this.label_71.Text = Class521.smethod_0(90022);
			this.label_71.TextAlign = ContentAlignment.MiddleRight;
			this.label_72.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_72.Location = new Point(274, 210);
			this.label_72.Name = Class521.smethod_0(90043);
			this.label_72.Size = new Size(140, 20);
			this.label_72.TabIndex = 14;
			this.label_72.Text = Class521.smethod_0(90056);
			this.label_72.TextAlign = ContentAlignment.MiddleRight;
			this.label_73.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_73.Location = new Point(4, 210);
			this.label_73.Name = Class521.smethod_0(90077);
			this.label_73.Size = new Size(140, 20);
			this.label_73.TabIndex = 13;
			this.label_73.Text = Class521.smethod_0(90090);
			this.label_73.TextAlign = ContentAlignment.MiddleRight;
			this.label_74.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_74.Location = new Point(4, 182);
			this.label_74.Name = Class521.smethod_0(90103);
			this.label_74.Size = new Size(140, 20);
			this.label_74.TabIndex = 12;
			this.label_74.Text = Class521.smethod_0(90116);
			this.label_74.TextAlign = ContentAlignment.MiddleRight;
			this.label_75.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_75.Location = new Point(556, 182);
			this.label_75.Name = Class521.smethod_0(90129);
			this.label_75.Size = new Size(144, 20);
			this.label_75.TabIndex = 11;
			this.label_75.Text = Class521.smethod_0(90022);
			this.label_75.TextAlign = ContentAlignment.MiddleRight;
			this.label_76.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_76.Location = new Point(274, 182);
			this.label_76.Name = Class521.smethod_0(90142);
			this.label_76.Size = new Size(140, 20);
			this.label_76.TabIndex = 10;
			this.label_76.Text = Class521.smethod_0(90056);
			this.label_76.TextAlign = ContentAlignment.MiddleRight;
			this.label_77.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_77.Location = new Point(556, 98);
			this.label_77.Name = Class521.smethod_0(10464);
			this.label_77.Size = new Size(144, 20);
			this.label_77.TabIndex = 9;
			this.label_77.Text = Class521.smethod_0(90155);
			this.label_77.TextAlign = ContentAlignment.MiddleRight;
			this.label_78.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_78.Location = new Point(274, 98);
			this.label_78.Name = Class521.smethod_0(90180);
			this.label_78.Size = new Size(140, 20);
			this.label_78.TabIndex = 8;
			this.label_78.Text = Class521.smethod_0(90193);
			this.label_78.TextAlign = ContentAlignment.MiddleRight;
			this.label_79.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_79.Location = new Point(556, 70);
			this.label_79.Name = Class521.smethod_0(90218);
			this.label_79.Size = new Size(144, 20);
			this.label_79.TabIndex = 7;
			this.label_79.Text = Class521.smethod_0(90231);
			this.label_79.TextAlign = ContentAlignment.MiddleRight;
			this.label_80.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_80.Location = new Point(274, 70);
			this.label_80.Name = Class521.smethod_0(20696);
			this.label_80.Size = new Size(140, 20);
			this.label_80.TabIndex = 6;
			this.label_80.Text = Class521.smethod_0(90256);
			this.label_80.TextAlign = ContentAlignment.MiddleRight;
			this.label_81.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_81.Location = new Point(4, 98);
			this.label_81.Name = Class521.smethod_0(20859);
			this.label_81.Size = new Size(140, 20);
			this.label_81.TabIndex = 5;
			this.label_81.Text = Class521.smethod_0(90281);
			this.label_81.TextAlign = ContentAlignment.MiddleRight;
			this.label_82.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_82.Location = new Point(4, 70);
			this.label_82.Name = Class521.smethod_0(40965);
			this.label_82.Size = new Size(140, 20);
			this.label_82.TabIndex = 4;
			this.label_82.Text = Class521.smethod_0(90306);
			this.label_82.TextAlign = ContentAlignment.MiddleRight;
			this.label_83.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_83.Location = new Point(556, 14);
			this.label_83.Name = Class521.smethod_0(11267);
			this.label_83.Size = new Size(144, 20);
			this.label_83.TabIndex = 2;
			this.label_83.Text = Class521.smethod_0(90327);
			this.label_83.TextAlign = ContentAlignment.MiddleRight;
			this.label_84.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_84.Location = new Point(274, 14);
			this.label_84.Name = Class521.smethod_0(11233);
			this.label_84.Size = new Size(140, 20);
			this.label_84.TabIndex = 1;
			this.label_84.Text = Class521.smethod_0(90344);
			this.label_84.TextAlign = ContentAlignment.MiddleRight;
			this.label_85.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_85.Location = new Point(4, 14);
			this.label_85.Name = Class521.smethod_0(7268);
			this.label_85.Size = new Size(140, 20);
			this.label_85.TabIndex = 0;
			this.label_85.Text = Class521.smethod_0(90361);
			this.label_85.TextAlign = ContentAlignment.MiddleRight;
			this.tabItem_1.AttachedControl = this.tabControlPanel_1;
			this.tabItem_1.Name = Class521.smethod_0(86848);
			this.tabItem_1.Text = Class521.smethod_0(83172);
			this.tabControlPanel_2.AutoScroll = true;
			this.tabControlPanel_2.Dock = DockStyle.Fill;
			this.tabControlPanel_2.Location = new Point(0, 0);
			this.tabControlPanel_2.Name = Class521.smethod_0(90378);
			this.tabControlPanel_2.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_2.Size = new Size(1189, 648);
			this.tabControlPanel_2.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_2.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_2.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_2.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_2.Style.GradientAngle = -90;
			this.tabControlPanel_2.TabIndex = 6;
			this.tabControlPanel_2.TabItem = this.tabItem_2;
			this.tabItem_2.AttachedControl = this.tabControlPanel_2;
			this.tabItem_2.Name = Class521.smethod_0(86865);
			this.tabItem_2.Text = Class521.smethod_0(83234);
			this.tabControlPanel_3.Dock = DockStyle.Fill;
			this.tabControlPanel_3.Location = new Point(0, 0);
			this.tabControlPanel_3.Name = Class521.smethod_0(90415);
			this.tabControlPanel_3.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_3.Size = new Size(1189, 648);
			this.tabControlPanel_3.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_3.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_3.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_3.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_3.Style.GradientAngle = -90;
			this.tabControlPanel_3.TabIndex = 10;
			this.tabControlPanel_3.TabItem = this.tabItem_3;
			this.tabItem_3.AttachedControl = this.tabControlPanel_3;
			this.tabItem_3.Name = Class521.smethod_0(86890);
			this.tabItem_3.Text = Class521.smethod_0(83296);
			this.tabControlPanel_4.AutoScroll = true;
			this.tabControlPanel_4.Dock = DockStyle.Fill;
			this.tabControlPanel_4.Location = new Point(0, 0);
			this.tabControlPanel_4.Name = Class521.smethod_0(90448);
			this.tabControlPanel_4.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_4.Size = new Size(1189, 648);
			this.tabControlPanel_4.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_4.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_4.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_4.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_4.Style.GradientAngle = -90;
			this.tabControlPanel_4.TabIndex = 7;
			this.tabControlPanel_4.TabItem = this.tabItem_4;
			this.tabItem_4.AttachedControl = this.tabControlPanel_4;
			this.tabItem_4.Name = Class521.smethod_0(86911);
			this.tabItem_4.Text = Class521.smethod_0(83358);
			this.tabControlPanel_5.Dock = DockStyle.Fill;
			this.tabControlPanel_5.Location = new Point(0, 0);
			this.tabControlPanel_5.Name = Class521.smethod_0(90481);
			this.tabControlPanel_5.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_5.Size = new Size(1189, 648);
			this.tabControlPanel_5.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_5.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_5.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_5.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_5.Style.GradientAngle = -90;
			this.tabControlPanel_5.TabIndex = 9;
			this.tabControlPanel_5.TabItem = this.tabItem_5;
			this.tabItem_5.AttachedControl = this.tabControlPanel_5;
			this.tabItem_5.Name = Class521.smethod_0(86957);
			this.tabItem_5.Text = Class521.smethod_0(83482);
			this.tabControlPanel_6.Controls.Add(this.splitContainer_0);
			this.tabControlPanel_6.Dock = DockStyle.Fill;
			this.tabControlPanel_6.Location = new Point(0, 0);
			this.tabControlPanel_6.Name = Class521.smethod_0(90514);
			this.tabControlPanel_6.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_6.Size = new Size(1189, 648);
			this.tabControlPanel_6.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_6.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_6.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_6.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_6.Style.GradientAngle = -90;
			this.tabControlPanel_6.TabIndex = 8;
			this.tabControlPanel_6.TabItem = this.tabItem_6;
			this.splitContainer_0.Dock = DockStyle.Fill;
			this.splitContainer_0.IsSplitterFixed = true;
			this.splitContainer_0.Location = new Point(1, 1);
			this.splitContainer_0.Name = Class521.smethod_0(90551);
			this.splitContainer_0.Size = new Size(1187, 646);
			this.splitContainer_0.SplitterDistance = 585;
			this.splitContainer_0.SplitterWidth = 1;
			this.splitContainer_0.TabIndex = 0;
			this.tabItem_6.AttachedControl = this.tabControlPanel_6;
			this.tabItem_6.Name = Class521.smethod_0(86932);
			this.tabItem_6.Text = Class521.smethod_0(83420);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.control11_0);
			base.Name = Class521.smethod_0(90588);
			base.Size = new Size(1189, 676);
			((ISupportInitialize)this.control11_0).EndInit();
			this.control11_0.ResumeLayout(false);
			this.tabControlPanel_0.ResumeLayout(false);
			this.panel_0.ResumeLayout(false);
			this.tabControlPanel_1.ResumeLayout(false);
			this.panel_1.ResumeLayout(false);
			this.class298_0.ResumeLayout(false);
			this.class298_1.ResumeLayout(false);
			this.class298_2.ResumeLayout(false);
			this.tabControlPanel_6.ResumeLayout(false);
			this.splitContainer_0.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000EE5 RID: 3813
		private int int_0;

		// Token: 0x04000EE6 RID: 3814
		private Font font_0;

		// Token: 0x04000EE7 RID: 3815
		private Font font_1;

		// Token: 0x04000EE8 RID: 3816
		private Color[] color_0 = new Color[]
		{
			Color.Blue,
			Color.LightSalmon,
			Color.LightSeaGreen,
			Color.LimeGreen,
			Color.Aqua,
			Color.BurlyWood,
			Color.MediumOrchid,
			Color.Coral,
			Color.CornflowerBlue,
			Color.MistyRose,
			Color.DarkGray,
			Color.DarkKhaki,
			Color.DarkMagenta,
			Color.DarkOrange,
			Color.DarkOrchid,
			Color.DarkRed,
			Color.DarkSalmon,
			Color.PaleVioletRed,
			Color.PaleVioletRed,
			Color.PeachPuff,
			Color.DarkViolet,
			Color.DeepPink,
			Color.DeepSkyBlue,
			Color.Yellow,
			Color.YellowGreen,
			Color.Wheat,
			Color.Peru,
			Color.DarkSlateGray,
			Color.DarkSeaGreen,
			Color.DodgerBlue,
			Color.PowderBlue,
			Color.IndianRed,
			Color.LightCyan,
			Color.Firebrick,
			Color.DarkTurquoise,
			Color.OliveDrab,
			Color.DarkSlateBlue,
			Color.Maroon
		};

		// Token: 0x04000EE9 RID: 3817
		private GraphCtrlStat graphCtrlStat_0;

		// Token: 0x04000EEA RID: 3818
		private List<TradingSymbol> list_0;

		// Token: 0x04000EEB RID: 3819
		private IContainer icontainer_0;

		// Token: 0x04000EEC RID: 3820
		private Control11 control11_0;

		// Token: 0x04000EED RID: 3821
		private TabControlPanel tabControlPanel_0;

		// Token: 0x04000EEE RID: 3822
		private Panel panel_0;

		// Token: 0x04000EEF RID: 3823
		private System.Windows.Forms.Label label_0;

		// Token: 0x04000EF0 RID: 3824
		private System.Windows.Forms.Label label_1;

		// Token: 0x04000EF1 RID: 3825
		private System.Windows.Forms.Label label_2;

		// Token: 0x04000EF2 RID: 3826
		private System.Windows.Forms.Label label_3;

		// Token: 0x04000EF3 RID: 3827
		private System.Windows.Forms.Label label_4;

		// Token: 0x04000EF4 RID: 3828
		private System.Windows.Forms.Label label_5;

		// Token: 0x04000EF5 RID: 3829
		private System.Windows.Forms.Label label_6;

		// Token: 0x04000EF6 RID: 3830
		private System.Windows.Forms.Label label_7;

		// Token: 0x04000EF7 RID: 3831
		private System.Windows.Forms.Label label_8;

		// Token: 0x04000EF8 RID: 3832
		private System.Windows.Forms.Label label_9;

		// Token: 0x04000EF9 RID: 3833
		private System.Windows.Forms.Label label_10;

		// Token: 0x04000EFA RID: 3834
		private System.Windows.Forms.Label label_11;

		// Token: 0x04000EFB RID: 3835
		private System.Windows.Forms.Label label_12;

		// Token: 0x04000EFC RID: 3836
		private System.Windows.Forms.Label label_13;

		// Token: 0x04000EFD RID: 3837
		private System.Windows.Forms.Label label_14;

		// Token: 0x04000EFE RID: 3838
		private System.Windows.Forms.Label label_15;

		// Token: 0x04000EFF RID: 3839
		private TabItem tabItem_0;

		// Token: 0x04000F00 RID: 3840
		private TabControlPanel tabControlPanel_1;

		// Token: 0x04000F01 RID: 3841
		private Panel panel_1;

		// Token: 0x04000F02 RID: 3842
		private Class298 class298_0;

		// Token: 0x04000F03 RID: 3843
		private System.Windows.Forms.Label label_16;

		// Token: 0x04000F04 RID: 3844
		private System.Windows.Forms.Label label_17;

		// Token: 0x04000F05 RID: 3845
		private System.Windows.Forms.Label label_18;

		// Token: 0x04000F06 RID: 3846
		private System.Windows.Forms.Label label_19;

		// Token: 0x04000F07 RID: 3847
		private System.Windows.Forms.Label label_20;

		// Token: 0x04000F08 RID: 3848
		private System.Windows.Forms.Label label_21;

		// Token: 0x04000F09 RID: 3849
		private System.Windows.Forms.Label label_22;

		// Token: 0x04000F0A RID: 3850
		private System.Windows.Forms.Label label_23;

		// Token: 0x04000F0B RID: 3851
		private Class298 class298_1;

		// Token: 0x04000F0C RID: 3852
		private ComboBox comboBox_0;

		// Token: 0x04000F0D RID: 3853
		private ComboBox comboBox_1;

		// Token: 0x04000F0E RID: 3854
		private ComboBox comboBox_2;

		// Token: 0x04000F0F RID: 3855
		private System.Windows.Forms.Label label_24;

		// Token: 0x04000F10 RID: 3856
		private System.Windows.Forms.Label label_25;

		// Token: 0x04000F11 RID: 3857
		private System.Windows.Forms.Label label_26;

		// Token: 0x04000F12 RID: 3858
		private ComboBox comboBox_3;

		// Token: 0x04000F13 RID: 3859
		private GroupBox groupBox_0;

		// Token: 0x04000F14 RID: 3860
		private Class298 class298_2;

		// Token: 0x04000F15 RID: 3861
		private System.Windows.Forms.Label label_27;

		// Token: 0x04000F16 RID: 3862
		private System.Windows.Forms.Label label_28;

		// Token: 0x04000F17 RID: 3863
		private System.Windows.Forms.Label label_29;

		// Token: 0x04000F18 RID: 3864
		private System.Windows.Forms.Label label_30;

		// Token: 0x04000F19 RID: 3865
		private System.Windows.Forms.Label label_31;

		// Token: 0x04000F1A RID: 3866
		private System.Windows.Forms.Label label_32;

		// Token: 0x04000F1B RID: 3867
		private System.Windows.Forms.Label label_33;

		// Token: 0x04000F1C RID: 3868
		private System.Windows.Forms.Label label_34;

		// Token: 0x04000F1D RID: 3869
		private System.Windows.Forms.Label label_35;

		// Token: 0x04000F1E RID: 3870
		private System.Windows.Forms.Label label_36;

		// Token: 0x04000F1F RID: 3871
		private System.Windows.Forms.Label label_37;

		// Token: 0x04000F20 RID: 3872
		private System.Windows.Forms.Label label_38;

		// Token: 0x04000F21 RID: 3873
		private System.Windows.Forms.Label label_39;

		// Token: 0x04000F22 RID: 3874
		private System.Windows.Forms.Label label_40;

		// Token: 0x04000F23 RID: 3875
		private System.Windows.Forms.Label label_41;

		// Token: 0x04000F24 RID: 3876
		private System.Windows.Forms.Label label_42;

		// Token: 0x04000F25 RID: 3877
		private System.Windows.Forms.Label label_43;

		// Token: 0x04000F26 RID: 3878
		private System.Windows.Forms.Label label_44;

		// Token: 0x04000F27 RID: 3879
		private System.Windows.Forms.Label label_45;

		// Token: 0x04000F28 RID: 3880
		private System.Windows.Forms.Label label_46;

		// Token: 0x04000F29 RID: 3881
		private System.Windows.Forms.Label label_47;

		// Token: 0x04000F2A RID: 3882
		private System.Windows.Forms.Label label_48;

		// Token: 0x04000F2B RID: 3883
		private System.Windows.Forms.Label label_49;

		// Token: 0x04000F2C RID: 3884
		private System.Windows.Forms.Label label_50;

		// Token: 0x04000F2D RID: 3885
		private System.Windows.Forms.Label label_51;

		// Token: 0x04000F2E RID: 3886
		private System.Windows.Forms.Label label_52;

		// Token: 0x04000F2F RID: 3887
		private System.Windows.Forms.Label label_53;

		// Token: 0x04000F30 RID: 3888
		private System.Windows.Forms.Label label_54;

		// Token: 0x04000F31 RID: 3889
		private System.Windows.Forms.Label label_55;

		// Token: 0x04000F32 RID: 3890
		private System.Windows.Forms.Label label_56;

		// Token: 0x04000F33 RID: 3891
		private System.Windows.Forms.Label label_57;

		// Token: 0x04000F34 RID: 3892
		private System.Windows.Forms.Label label_58;

		// Token: 0x04000F35 RID: 3893
		private System.Windows.Forms.Label label_59;

		// Token: 0x04000F36 RID: 3894
		private System.Windows.Forms.Label label_60;

		// Token: 0x04000F37 RID: 3895
		private System.Windows.Forms.Label label_61;

		// Token: 0x04000F38 RID: 3896
		private System.Windows.Forms.Label label_62;

		// Token: 0x04000F39 RID: 3897
		private System.Windows.Forms.Label label_63;

		// Token: 0x04000F3A RID: 3898
		private System.Windows.Forms.Label label_64;

		// Token: 0x04000F3B RID: 3899
		private System.Windows.Forms.Label label_65;

		// Token: 0x04000F3C RID: 3900
		private System.Windows.Forms.Label label_66;

		// Token: 0x04000F3D RID: 3901
		private System.Windows.Forms.Label label_67;

		// Token: 0x04000F3E RID: 3902
		private System.Windows.Forms.Label label_68;

		// Token: 0x04000F3F RID: 3903
		private System.Windows.Forms.Label label_69;

		// Token: 0x04000F40 RID: 3904
		private System.Windows.Forms.Label label_70;

		// Token: 0x04000F41 RID: 3905
		private System.Windows.Forms.Label label_71;

		// Token: 0x04000F42 RID: 3906
		private System.Windows.Forms.Label label_72;

		// Token: 0x04000F43 RID: 3907
		private System.Windows.Forms.Label label_73;

		// Token: 0x04000F44 RID: 3908
		private System.Windows.Forms.Label label_74;

		// Token: 0x04000F45 RID: 3909
		private System.Windows.Forms.Label label_75;

		// Token: 0x04000F46 RID: 3910
		private System.Windows.Forms.Label label_76;

		// Token: 0x04000F47 RID: 3911
		private System.Windows.Forms.Label label_77;

		// Token: 0x04000F48 RID: 3912
		private System.Windows.Forms.Label label_78;

		// Token: 0x04000F49 RID: 3913
		private System.Windows.Forms.Label label_79;

		// Token: 0x04000F4A RID: 3914
		private System.Windows.Forms.Label label_80;

		// Token: 0x04000F4B RID: 3915
		private System.Windows.Forms.Label label_81;

		// Token: 0x04000F4C RID: 3916
		private System.Windows.Forms.Label label_82;

		// Token: 0x04000F4D RID: 3917
		private System.Windows.Forms.Label label_83;

		// Token: 0x04000F4E RID: 3918
		private System.Windows.Forms.Label label_84;

		// Token: 0x04000F4F RID: 3919
		private System.Windows.Forms.Label label_85;

		// Token: 0x04000F50 RID: 3920
		private TabItem tabItem_1;

		// Token: 0x04000F51 RID: 3921
		private TabControlPanel tabControlPanel_2;

		// Token: 0x04000F52 RID: 3922
		private TabItem tabItem_2;

		// Token: 0x04000F53 RID: 3923
		private TabControlPanel tabControlPanel_3;

		// Token: 0x04000F54 RID: 3924
		private TabItem tabItem_3;

		// Token: 0x04000F55 RID: 3925
		private TabControlPanel tabControlPanel_4;

		// Token: 0x04000F56 RID: 3926
		private TabItem tabItem_4;

		// Token: 0x04000F57 RID: 3927
		private TabControlPanel tabControlPanel_5;

		// Token: 0x04000F58 RID: 3928
		private TabItem tabItem_5;

		// Token: 0x04000F59 RID: 3929
		private TabControlPanel tabControlPanel_6;

		// Token: 0x04000F5A RID: 3930
		private SplitContainer splitContainer_0;

		// Token: 0x04000F5B RID: 3931
		private TabItem tabItem_6;

		// Token: 0x020002AE RID: 686
		// (Invoke) Token: 0x06001E76 RID: 7798
		private delegate void Delegate34(Control ctrl, string strshow);

		// Token: 0x020002AF RID: 687
		// (Invoke) Token: 0x06001E7A RID: 7802
		private delegate void Delegate35(Control ctrl, Color color);

		// Token: 0x020002B0 RID: 688
		[CompilerGenerated]
		private sealed class Class352
		{
			// Token: 0x04000F5C RID: 3932
			public int? nullable_0;
		}

		// Token: 0x020002B2 RID: 690
		[CompilerGenerated]
		private sealed class Class353
		{
			// Token: 0x04000F71 RID: 3953
			public int? nullable_0;
		}

		// Token: 0x020002B3 RID: 691
		[CompilerGenerated]
		private sealed class Class354
		{
			// Token: 0x04000F72 RID: 3954
			public int? nullable_0;
		}

		// Token: 0x020002B4 RID: 692
		[CompilerGenerated]
		private sealed class Class355
		{
			// Token: 0x06001E96 RID: 7830 RVA: 0x000DE5B0 File Offset: 0x000DC7B0
			internal bool method_0(Transaction transaction_0)
			{
				return Base.Trading.smethod_215(transaction_0.SymbolID) == this.tradingSymbol_0;
			}

			// Token: 0x04000F73 RID: 3955
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x020002B5 RID: 693
		[CompilerGenerated]
		private sealed class Class356
		{
			// Token: 0x06001E98 RID: 7832 RVA: 0x000DE5D4 File Offset: 0x000DC7D4
			internal bool method_0(CurveItem curveItem_0)
			{
				return curveItem_0.Tag == this.tradingSymbol_0;
			}

			// Token: 0x06001E99 RID: 7833 RVA: 0x000DE5D4 File Offset: 0x000DC7D4
			internal bool method_1(CurveItem curveItem_0)
			{
				return curveItem_0.Tag == this.tradingSymbol_0;
			}

			// Token: 0x04000F74 RID: 3956
			public TradingSymbol tradingSymbol_0;
		}
	}
}
