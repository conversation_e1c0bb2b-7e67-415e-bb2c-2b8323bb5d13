﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.TextMarkup;
using ns17;
using ns18;
using ns26;
using ns3;
using TEx;

namespace ns5
{
	// Token: 0x02000056 RID: 86
	internal sealed class Class43
	{
		// Token: 0x14000016 RID: 22
		// (add) Token: 0x060002FB RID: 763 RVA: 0x0001E2FC File Offset: 0x0001C4FC
		// (remove) Token: 0x060002FC RID: 764 RVA: 0x0001E334 File Offset: 0x0001C534
		public event SuperTooltipEventHandler BeforeTooltipDisplay
		{
			[CompilerGenerated]
			add
			{
				SuperTooltipEventHandler superTooltipEventHandler = this.superTooltipEventHandler_0;
				SuperTooltipEventHandler superTooltipEventHandler2;
				do
				{
					superTooltipEventHandler2 = superTooltipEventHandler;
					SuperTooltipEventHandler value2 = (SuperTooltipEventHandler)Delegate.Combine(superTooltipEventHandler2, value);
					superTooltipEventHandler = Interlocked.CompareExchange<SuperTooltipEventHandler>(ref this.superTooltipEventHandler_0, value2, superTooltipEventHandler2);
				}
				while (superTooltipEventHandler != superTooltipEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				SuperTooltipEventHandler superTooltipEventHandler = this.superTooltipEventHandler_0;
				SuperTooltipEventHandler superTooltipEventHandler2;
				do
				{
					superTooltipEventHandler2 = superTooltipEventHandler;
					SuperTooltipEventHandler value2 = (SuperTooltipEventHandler)Delegate.Remove(superTooltipEventHandler2, value);
					superTooltipEventHandler = Interlocked.CompareExchange<SuperTooltipEventHandler>(ref this.superTooltipEventHandler_0, value2, superTooltipEventHandler2);
				}
				while (superTooltipEventHandler != superTooltipEventHandler2);
			}
		}

		// Token: 0x060002FD RID: 765 RVA: 0x000034B5 File Offset: 0x000016B5
		protected void method_0(Class46 class46_0, SuperTooltipEventArgs superTooltipEventArgs_0)
		{
			SuperTooltipEventHandler superTooltipEventHandler = this.superTooltipEventHandler_0;
			if (superTooltipEventHandler != null)
			{
				superTooltipEventHandler(class46_0, superTooltipEventArgs_0);
			}
		}

		// Token: 0x060002FE RID: 766 RVA: 0x0001E36C File Offset: 0x0001C56C
		public Class43(int int_3 = 20000, int int_4 = 10000)
		{
			MarkupSettings.NormalHyperlink.UnderlineStyle = eHyperlinkUnderlineStyle.None;
			MarkupSettings.VisitedHyperlink.UnderlineStyle = eHyperlinkUnderlineStyle.None;
			this.superTooltip_0 = new SuperTooltip();
			this.superTooltip_0.DefaultFont = new Font(Class521.smethod_0(4683), TApp.smethod_4(8f, false));
			this.superTooltip_0.DefaultTooltipSettings.HeaderVisible = false;
			this.superTooltip_0.BeforeTooltipDisplay += this.superTooltip_0_BeforeTooltipDisplay;
			this.superTooltip_0.MarkupLinkClick += this.superTooltip_0_MarkupLinkClick;
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = int_3;
			this.timer_0.Tick += this.timer_0_Tick;
			this.timer_0.Start();
			this.timer_1 = new System.Windows.Forms.Timer();
			this.timer_1.Interval = int_4;
			this.timer_1.Tick += this.timer_1_Tick;
			if (!string.IsNullOrEmpty(Base.UI.Form.NoShowTooltips))
			{
				try
				{
					this.NoShowToolTipNames = Base.UI.Form.NoShowTooltips.Split(new char[]
					{
						','
					}).ToList<string>();
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060002FF RID: 767 RVA: 0x0001E4C4 File Offset: 0x0001C6C4
		public void method_1(string string_0, IComponent icomponent_0, Enum0 enum0_0, SuperTooltipInfo superTooltipInfo_0)
		{
			if (this.TooltipObjList == null)
			{
				this.TooltipObjList = new List<Class46>();
			}
			if (this.NoShowToolTipNames == null || !this.NoShowToolTipNames.Contains(string_0))
			{
				this.TooltipObjList.Add(new Class46(this.superTooltip_0, string_0, icomponent_0, superTooltipInfo_0, enum0_0));
			}
		}

		// Token: 0x06000300 RID: 768 RVA: 0x0001E518 File Offset: 0x0001C718
		private void timer_0_Tick(object sender, EventArgs e)
		{
			if (!this.IsDisposing)
			{
				int num = this.int_0;
				if (this.TooltipObjList != null && this.TooltipObjList.Count > 0)
				{
					num++;
					if (num >= this.TooltipObjList.Count)
					{
						num = 0;
					}
					if (num >= 0)
					{
						Class46 @class = this.TooltipObjList[num];
						if (@class.Control != null)
						{
							this.int_0 = num;
							@class.SuperTooltipProvider.method_0();
							this.timer_1.Start();
						}
					}
				}
			}
		}

		// Token: 0x06000301 RID: 769 RVA: 0x0001E598 File Offset: 0x0001C798
		private void timer_1_Tick(object sender, EventArgs e)
		{
			Class46 currToolTipObj = this.CurrToolTipObj;
			if (currToolTipObj != null)
			{
				currToolTipObj.SuperTooltipProvider.method_1();
			}
			this.timer_1.Stop();
		}

		// Token: 0x06000302 RID: 770 RVA: 0x0001E5C8 File Offset: 0x0001C7C8
		private void superTooltip_0_BeforeTooltipDisplay(object sender, SuperTooltipEventArgs e)
		{
			if (this.TooltipObjList != null && this.int_0 >= 0)
			{
				if (this.int_0 < this.TooltipObjList.Count)
				{
					SuperTooltip superTooltip = sender as SuperTooltip;
					Class46 @class = this.TooltipObjList[this.int_0];
					this.method_0(@class, e);
					if (!e.Cancel)
					{
						Rectangle rectangle = @class.SuperTooltipProvider.DevComponents.DotNetBar.ISuperTooltipInfoProvider.ComponentRectangle;
						int num = 0;
						int num2 = 0;
						if (@class.Location == Enum0.const_1)
						{
							num = rectangle.Width - 20;
							num2 = rectangle.Height;
						}
						else if (@class.Location == Enum0.const_0)
						{
							num = rectangle.Width - 20;
							num2 = -superTooltip.SuperTooltipControl.Height;
						}
						else if (@class.Location == Enum0.const_2)
						{
							num = rectangle.Width - 250;
							num2 = 55;
						}
						else if (@class.Location == Enum0.const_3)
						{
							num = rectangle.Width - 250;
							num2 = rectangle.Height - superTooltip.SuperTooltipControl.Height;
							if (num2 < 0)
							{
								num2 = 0;
							}
						}
						e.Location = new Point(rectangle.X + num, rectangle.Y + num2);
					}
				}
				else
				{
					e.Cancel = true;
				}
			}
			else
			{
				e.Cancel = true;
			}
		}

		// Token: 0x06000303 RID: 771 RVA: 0x0001E704 File Offset: 0x0001C904
		private void superTooltip_0_MarkupLinkClick(object sender, MarkupLinkClickEventArgs e)
		{
			Class43.Class44 @class = new Class43.Class44();
			@class.markupLinkClickEventArgs_0 = e;
			if (@class.markupLinkClickEventArgs_0.Name == Class521.smethod_0(4692))
			{
				try
				{
					Help.ShowHelp(Base.UI.MainForm, Class521.smethod_0(4701), HelpNavigator.Topic, @class.markupLinkClickEventArgs_0.HRef);
					this.TooltipObjList.RemoveAll(new Predicate<Class46>(@class.method_0));
					return;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					return;
				}
			}
			if (@class.markupLinkClickEventArgs_0.Name == Class521.smethod_0(4722))
			{
				try
				{
					Process.Start(new ProcessStartInfo(@class.markupLinkClickEventArgs_0.HRef));
					this.TooltipObjList.RemoveAll(new Predicate<Class46>(@class.method_1));
					return;
				}
				catch
				{
					return;
				}
			}
			if (@class.markupLinkClickEventArgs_0.Name == Class521.smethod_0(4727))
			{
				Class43.Class45 class2 = new Class43.Class45();
				class2.string_0 = @class.markupLinkClickEventArgs_0.HRef;
				if (this.NoShowToolTipNames == null)
				{
					this.NoShowToolTipNames = new List<string>
					{
						class2.string_0
					};
				}
				else if (!this.NoShowToolTipNames.Contains(class2.string_0))
				{
					this.NoShowToolTipNames.Add(class2.string_0);
				}
				this.TooltipObjList.RemoveAll(new Predicate<Class46>(class2.method_0));
				Base.UI.Form.NoShowTooltips = string.Join(Class521.smethod_0(4736), this.NoShowToolTipNames.ToArray());
				Base.UI.smethod_47();
			}
		}

		// Token: 0x06000304 RID: 772 RVA: 0x000034CC File Offset: 0x000016CC
		public void method_2()
		{
			this.superTooltip_0.HideTooltip();
			this.timer_0.Stop();
			this.timer_0.Start();
		}

		// Token: 0x06000305 RID: 773 RVA: 0x000034F1 File Offset: 0x000016F1
		public void method_3()
		{
			this.IsDisposing = true;
			this.timer_0.Enabled = false;
			this.timer_0.Dispose();
		}

		// Token: 0x170000B6 RID: 182
		// (get) Token: 0x06000306 RID: 774 RVA: 0x0001E8B4 File Offset: 0x0001CAB4
		// (set) Token: 0x06000307 RID: 775 RVA: 0x00003513 File Offset: 0x00001713
		public List<Class46> TooltipObjList
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170000B7 RID: 183
		// (get) Token: 0x06000308 RID: 776 RVA: 0x0001E8CC File Offset: 0x0001CACC
		public Class46 CurrToolTipObj
		{
			get
			{
				Class46 result;
				if (this.TooltipObjList != null && this.int_0 >= 0 && this.int_0 < this.TooltipObjList.Count)
				{
					result = this.TooltipObjList[this.int_0];
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x170000B8 RID: 184
		// (get) Token: 0x06000309 RID: 777 RVA: 0x0001E918 File Offset: 0x0001CB18
		// (set) Token: 0x0600030A RID: 778 RVA: 0x0000351E File Offset: 0x0000171E
		public int DisplayInverval { get; set; }

		// Token: 0x170000B9 RID: 185
		// (get) Token: 0x0600030B RID: 779 RVA: 0x0001E930 File Offset: 0x0001CB30
		// (set) Token: 0x0600030C RID: 780 RVA: 0x00003529 File Offset: 0x00001729
		public int DisplayDuration { get; set; }

		// Token: 0x170000BA RID: 186
		// (get) Token: 0x0600030D RID: 781 RVA: 0x0001E948 File Offset: 0x0001CB48
		// (set) Token: 0x0600030E RID: 782 RVA: 0x00003534 File Offset: 0x00001734
		public bool Enable
		{
			get
			{
				return this.timer_0.Enabled;
			}
			set
			{
				if (value)
				{
					this.timer_0.Start();
				}
				else
				{
					this.superTooltip_0.HideTooltip();
					this.timer_0.Stop();
				}
			}
		}

		// Token: 0x170000BB RID: 187
		// (get) Token: 0x0600030F RID: 783 RVA: 0x0001E964 File Offset: 0x0001CB64
		// (set) Token: 0x06000310 RID: 784 RVA: 0x0000355E File Offset: 0x0000175E
		public bool IsDisposing { get; set; }

		// Token: 0x170000BC RID: 188
		// (get) Token: 0x06000311 RID: 785 RVA: 0x0001E97C File Offset: 0x0001CB7C
		// (set) Token: 0x06000312 RID: 786 RVA: 0x00003569 File Offset: 0x00001769
		public List<string> NoShowToolTipNames { get; set; }

		// Token: 0x04000109 RID: 265
		[CompilerGenerated]
		private SuperTooltipEventHandler superTooltipEventHandler_0;

		// Token: 0x0400010A RID: 266
		private SuperTooltip superTooltip_0;

		// Token: 0x0400010B RID: 267
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x0400010C RID: 268
		private System.Windows.Forms.Timer timer_1;

		// Token: 0x0400010D RID: 269
		private int int_0 = -1;

		// Token: 0x0400010E RID: 270
		private List<Class46> list_0;

		// Token: 0x0400010F RID: 271
		[CompilerGenerated]
		private int int_1;

		// Token: 0x04000110 RID: 272
		[CompilerGenerated]
		private int int_2;

		// Token: 0x04000111 RID: 273
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000112 RID: 274
		[CompilerGenerated]
		private List<string> list_1;

		// Token: 0x02000057 RID: 87
		[CompilerGenerated]
		private sealed class Class44
		{
			// Token: 0x06000314 RID: 788 RVA: 0x0001E994 File Offset: 0x0001CB94
			internal bool method_0(Class46 class46_0)
			{
				return class46_0.SuperTooltipInfo.BodyText.Contains(this.markupLinkClickEventArgs_0.HRef);
			}

			// Token: 0x06000315 RID: 789 RVA: 0x0001E994 File Offset: 0x0001CB94
			internal bool method_1(Class46 class46_0)
			{
				return class46_0.SuperTooltipInfo.BodyText.Contains(this.markupLinkClickEventArgs_0.HRef);
			}

			// Token: 0x04000113 RID: 275
			public MarkupLinkClickEventArgs markupLinkClickEventArgs_0;
		}

		// Token: 0x02000058 RID: 88
		[CompilerGenerated]
		private sealed class Class45
		{
			// Token: 0x06000317 RID: 791 RVA: 0x0001E9C0 File Offset: 0x0001CBC0
			internal bool method_0(Class46 class46_0)
			{
				return class46_0.Name == this.string_0;
			}

			// Token: 0x04000114 RID: 276
			public string string_0;
		}
	}
}
