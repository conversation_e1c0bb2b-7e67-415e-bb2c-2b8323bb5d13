﻿using System;

namespace TEx
{
	// Token: 0x020001D8 RID: 472
	[Serializable]
	public sealed class DrawLineStyle
	{
		// Token: 0x0600127B RID: 4731 RVA: 0x00002D25 File Offset: 0x00000F25
		public DrawLineStyle()
		{
		}

		// Token: 0x0600127C RID: 4732 RVA: 0x00007B34 File Offset: 0x00005D34
		public DrawLineStyle(DrawLineType lineType, DrawLineWidth width = DrawLineWidth.Normal, float[] dashPattern = null)
		{
			this.LineType = lineType;
			this.DashPattern = dashPattern;
			this.LineWidth = width;
		}

		// Token: 0x170002C3 RID: 707
		// (get) Token: 0x0600127D RID: 4733 RVA: 0x00083470 File Offset: 0x00081670
		// (set) Token: 0x0600127E RID: 4734 RVA: 0x00007B53 File Offset: 0x00005D53
		public DrawLineType LineType { get; set; }

		// Token: 0x170002C4 RID: 708
		// (get) Token: 0x0600127F RID: 4735 RVA: 0x00083488 File Offset: 0x00081688
		// (set) Token: 0x06001280 RID: 4736 RVA: 0x00007B5E File Offset: 0x00005D5E
		public float[] DashPattern
		{
			get
			{
				float[] result;
				if (this._DashPattern == null)
				{
					result = DrawLineStyle.smethod_1(this.LineType);
				}
				else
				{
					result = this._DashPattern;
				}
				return result;
			}
			set
			{
				this._DashPattern = value;
			}
		}

		// Token: 0x170002C5 RID: 709
		// (get) Token: 0x06001281 RID: 4737 RVA: 0x000834B8 File Offset: 0x000816B8
		// (set) Token: 0x06001282 RID: 4738 RVA: 0x00007B69 File Offset: 0x00005D69
		public DrawLineWidth LineWidth { get; set; }

		// Token: 0x170002C6 RID: 710
		// (get) Token: 0x06001283 RID: 4739 RVA: 0x000834D0 File Offset: 0x000816D0
		public float PenWidth
		{
			get
			{
				return DrawLineStyle.smethod_0(this.LineWidth);
			}
		}

		// Token: 0x06001284 RID: 4740 RVA: 0x000834EC File Offset: 0x000816EC
		public static float smethod_0(DrawLineWidth drawLineWidth_0)
		{
			float result = 1f;
			switch (drawLineWidth_0)
			{
			case DrawLineWidth.Medium:
				result = 2f;
				break;
			case DrawLineWidth.Thick:
				result = 3f;
				break;
			case DrawLineWidth.Thicker:
				result = 5f;
				break;
			case DrawLineWidth.Thickest:
				result = 7f;
				break;
			}
			return result;
		}

		// Token: 0x06001285 RID: 4741 RVA: 0x00083540 File Offset: 0x00081740
		public static float[] smethod_1(DrawLineType drawLineType_0)
		{
			float[] result = null;
			if (drawLineType_0 == DrawLineType.Dot)
			{
				result = new float[]
				{
					3f,
					3f
				};
			}
			else if (drawLineType_0 == DrawLineType.Dash)
			{
				result = new float[]
				{
					8f,
					3f
				};
			}
			else if (drawLineType_0 == DrawLineType.DotDash)
			{
				result = new float[]
				{
					3f,
					3f,
					8f,
					3f
				};
			}
			else if (drawLineType_0 == DrawLineType.DotDotDash)
			{
				result = new float[]
				{
					3f,
					3f,
					3f,
					3f,
					8f,
					3f
				};
			}
			return result;
		}

		// Token: 0x040009A3 RID: 2467
		private float[] _DashPattern;
	}
}
