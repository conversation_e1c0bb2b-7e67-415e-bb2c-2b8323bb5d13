﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns0;
using ns1;
using ns13;
using ns18;
using ns2;
using ns26;
using ns8;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200001C RID: 28
	internal sealed class InfoMineApiWorker : Class10
	{
		// Token: 0x060000A6 RID: 166 RVA: 0x00002D06 File Offset: 0x00000F06
		public InfoMineApiWorker()
		{
			base.ResultCompressed = true;
		}

		// Token: 0x060000A7 RID: 167 RVA: 0x00012730 File Offset: 0x00010930
		public void method_1(StkSymbol stkSymbol_0)
		{
			if (stkSymbol_0 != null)
			{
				List<StkSymbol> list_ = new List<StkSymbol>
				{
					stkSymbol_0
				};
				this.method_2(list_);
			}
		}

		// Token: 0x060000A8 RID: 168 RVA: 0x00012758 File Offset: 0x00010958
		public void method_2(List<StkSymbol> list_0)
		{
			if (list_0 != null && list_0.Count > 0)
			{
				Dictionary<string, object> dictionary = new Dictionary<string, object>();
				string[] array = list_0.Select(new Func<StkSymbol, string>(InfoMineApiWorker.<>c.<>9.method_0)).Where(new Func<string, bool>(InfoMineApiWorker.<>c.<>9.method_1)).ToArray<string>();
				if (array.Length != 0)
				{
					Class0<string, string, int, Class1<string[]>> value = new Class0<string, string, int, Class1<string[]>>(Class521.smethod_0(1359), TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class1<string[]>(array));
					dictionary[Class521.smethod_0(1376)] = value;
					base.GetHttpResponseInBackground(dictionary);
				}
			}
		}

		// Token: 0x060000A9 RID: 169 RVA: 0x00012814 File Offset: 0x00010A14
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && rslt.data != null)
			{
				try
				{
					Class21 @class = JsonConvert.DeserializeObject<Class21>(rslt.data as string);
					List<Class19> list = new List<Class19>();
					if (@class != null)
					{
						string[] tscodes = @class.tscodes;
						for (int i = 0; i < tscodes.Length; i++)
						{
							InfoMineApiWorker.Class9 class2 = new InfoMineApiWorker.Class9();
							class2.string_0 = tscodes[i];
							Class19 class3 = new Class19();
							class3.tscode = class2.string_0;
							if (@class.forecasts != null)
							{
								class3.forecasts = @class.forecasts.Where(new Func<Forecast, bool>(class2.method_0)).ToList<Forecast>();
							}
							if (@class.expresses != null)
							{
								class3.expresses = @class.expresses.Where(new Func<Express, bool>(class2.method_1)).ToList<Express>();
							}
							class3.FetchDate = DateTime.Now;
							class3.method_2();
							list.Add(class3);
						}
					}
					reqDict[Class521.smethod_0(1389)] = list;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x0200001E RID: 30
		[CompilerGenerated]
		private sealed class Class9
		{
			// Token: 0x060000AF RID: 175 RVA: 0x00012964 File Offset: 0x00010B64
			internal bool method_0(Forecast forecast_0)
			{
				return forecast_0.ts_code == this.string_0;
			}

			// Token: 0x060000B0 RID: 176 RVA: 0x00012988 File Offset: 0x00010B88
			internal bool method_1(Express express_0)
			{
				return express_0.ts_code == this.string_0;
			}

			// Token: 0x04000041 RID: 65
			public string string_0;
		}
	}
}
