﻿using System;
using System.Drawing;
using System.Windows.Forms;
using ns26;
using ns9;

namespace ns7
{
	// Token: 0x02000205 RID: 517
	internal sealed class Class52 : Class50
	{
		// Token: 0x06001517 RID: 5399 RVA: 0x0008EC5C File Offset: 0x0008CE5C
		public Class52()
		{
			base.NormalImage = Class375.cross_gray;
			base.HoverImage = Class375.cross_lightgray;
			base.Image = base.NormalImage;
			base.Size = new Size(18, 18);
			this.Cursor = Cursors.Hand;
		}

		// Token: 0x06001518 RID: 5400 RVA: 0x000041B9 File Offset: 0x000023B9
		protected override void Class50_Click(object sender, EventArgs e)
		{
		}
	}
}
