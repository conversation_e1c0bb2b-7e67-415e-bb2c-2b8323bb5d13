﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x02000385 RID: 901
	public static class ImageBeyes
	{
		// Token: 0x0600250F RID: 9487 RVA: 0x001005B8 File Offset: 0x000FE7B8
		public static List<double> smethod_0(string string_0)
		{
			Bitmap bitmap = new Bitmap(string_0);
			List<Point> list = new List<Point>();
			for (int i = 0; i < bitmap.Width; i++)
			{
				for (int j = 0; j < bitmap.Height; j++)
				{
					if (bitmap.GetPixel(i, j).R < 100)
					{
						list.Add(new Point(i, j));
					}
				}
			}
			return ImageBeyes.smethod_3(list, 5, 5);
		}

		// Token: 0x06002510 RID: 9488 RVA: 0x00100624 File Offset: 0x000FE824
		public static List<double> smethod_1(List<Point> list_0)
		{
			return ImageBeyes.smethod_3(list_0, 5, 5);
		}

		// Token: 0x06002511 RID: 9489 RVA: 0x00100640 File Offset: 0x000FE840
		public static List<List<double>> smethod_2(Bitmap bitmap_0, List<Rectangle> list_0)
		{
			ImageBeyes.Class487 @class = new ImageBeyes.Class487();
			@class.bitmap_0 = bitmap_0;
			return list_0.Select(new Func<Rectangle, List<Point>>(@class.method_0)).Select(new Func<List<Point>, List<double>>(ImageBeyes.<>c.<>9.method_0)).ToList<List<double>>();
		}

		// Token: 0x06002512 RID: 9490 RVA: 0x0010069C File Offset: 0x000FE89C
		public static List<double> smethod_3(List<Point> list_0, int int_0, int int_1)
		{
			List<double> list = new List<double>();
			Rectangle rectangle = ImageBeyes.smethod_5(list_0);
			List<int> list2 = ImageBeyes.smethod_4(rectangle.Height, int_0);
			List<int> list3 = ImageBeyes.smethod_4(rectangle.Width, int_1);
			List<Rectangle> list4 = new List<Rectangle>();
			int num = 0;
			for (int i = 0; i < list2.Count; i++)
			{
				int num2 = 0;
				for (int j = 0; j < list3.Count; j++)
				{
					list4.Add(new Rectangle(num2 + rectangle.X, num + rectangle.Y, list3[j], list2[i]));
					num2 += list3[j];
				}
				num += list2[i];
			}
			for (int k = 0; k < list4.Count; k++)
			{
				list.Add(0.0);
			}
			foreach (Point point in list_0)
			{
				for (int l = 0; l < list4.Count; l++)
				{
					if (point.X >= list4[l].Left && point.X < list4[l].Right && point.Y >= list4[l].Top && point.Y < list4[l].Bottom)
					{
						List<double> list5 = list;
						int index = l;
						double value = list5[index] + 1.0;
						list5[index] = value;
						break;
					}
				}
			}
			double num3 = 0.0;
			for (int m = 0; m < list.Count; m++)
			{
				num3 += list[m];
			}
			if (num3 != (double)list_0.Count)
			{
				throw new Exception(Class521.smethod_0(109761));
			}
			for (int n = 0; n < list4.Count; n++)
			{
				list[n] /= (double)(list4[n].Width * list4[n].Height);
			}
			return list;
		}

		// Token: 0x06002513 RID: 9491 RVA: 0x00100900 File Offset: 0x000FEB00
		public static List<int> smethod_4(int int_0, int int_1)
		{
			List<int> list = new List<int>();
			int item = int_0 / int_1;
			int num = int_0 % int_1;
			for (int i = 0; i < int_1; i++)
			{
				list.Add(item);
			}
			for (int j = 0; j < num; j++)
			{
				List<int> list2 = list;
				int index = j;
				list2[index]++;
			}
			return list;
		}

		// Token: 0x06002514 RID: 9492 RVA: 0x00100960 File Offset: 0x000FEB60
		private static Rectangle smethod_5(List<Point> list_0)
		{
			int num = 268435455;
			int num2 = 268435455;
			int num3 = 0;
			int num4 = 0;
			foreach (Point point in list_0)
			{
				if (point.X > num3)
				{
					num3 = point.X;
				}
				if (point.X < num)
				{
					num = point.X;
				}
				if (point.Y > num4)
				{
					num4 = point.Y;
				}
				if (point.Y < num2)
				{
					num2 = point.Y;
				}
			}
			return new Rectangle
			{
				X = num,
				Width = num3 - num + 1,
				Y = num2,
				Height = num4 - num2 + 1
			};
		}

		// Token: 0x02000386 RID: 902
		[CompilerGenerated]
		private sealed class Class487
		{
			// Token: 0x06002516 RID: 9494 RVA: 0x00100A3C File Offset: 0x000FEC3C
			internal List<Point> method_0(Rectangle rectangle_0)
			{
				return ImageProcessor.smethod_1(this.bitmap_0, rectangle_0);
			}

			// Token: 0x040011DC RID: 4572
			public Bitmap bitmap_0;
		}
	}
}
