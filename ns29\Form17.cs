﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns29
{
	// Token: 0x020001A8 RID: 424
	internal sealed partial class Form17 : Form
	{
		// Token: 0x1400007A RID: 122
		// (add) Token: 0x06001052 RID: 4178 RVA: 0x0006DF84 File Offset: 0x0006C184
		// (remove) Token: 0x06001053 RID: 4179 RVA: 0x0006DFBC File Offset: 0x0006C1BC
		public event EventHandler PageSaved
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001054 RID: 4180 RVA: 0x0006DFF4 File Offset: 0x0006C1F4
		protected void method_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06001055 RID: 4181 RVA: 0x00006F27 File Offset: 0x00005127
		public Form17(ChartUISettings? nullable_1, bool bool_1)
		{
			this.method_1();
			this.nullable_0 = nullable_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x06001056 RID: 4182 RVA: 0x0006E01C File Offset: 0x0006C21C
		public Form17() : this(null, true)
		{
		}

		// Token: 0x06001057 RID: 4183 RVA: 0x0006E03C File Offset: 0x0006C23C
		private void Form17_Load(object sender, EventArgs e)
		{
			foreach (ChartPage chartPage in Base.UI.ChartPageList)
			{
				this.listBox_0.Items.Add(chartPage.Name);
			}
		}

		// Token: 0x06001058 RID: 4184 RVA: 0x00004273 File Offset: 0x00002473
		private void button_1_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06001059 RID: 4185 RVA: 0x00006F45 File Offset: 0x00005145
		private void listBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.listBox_0.SelectedItem != null)
			{
				this.textBox_0.Text = this.listBox_0.SelectedItem.ToString();
			}
		}

		// Token: 0x0600105A RID: 4186 RVA: 0x0006E0A4 File Offset: 0x0006C2A4
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.textBox_0.Text.Length > 0)
			{
				if (Base.UI.ChartPageList.Where(new Func<ChartPage, bool>(this.method_2)).Any<ChartPage>())
				{
					if (MessageBox.Show(Class521.smethod_0(37316), Class521.smethod_0(10032), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
					{
						Base.UI.smethod_84(this.nullable_0, this.textBox_0.Text, this.bool_0);
						this.method_0();
					}
				}
				else
				{
					Base.UI.smethod_84(this.nullable_0, this.textBox_0.Text, this.bool_0);
					this.method_0();
				}
				base.Close();
			}
		}

		// Token: 0x17000267 RID: 615
		// (get) Token: 0x0600105B RID: 4187 RVA: 0x0006E154 File Offset: 0x0006C354
		// (set) Token: 0x0600105C RID: 4188 RVA: 0x00006F71 File Offset: 0x00005171
		public bool IfSetSavedPageAsCurrPage
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x0600105D RID: 4189 RVA: 0x00006F7C File Offset: 0x0000517C
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600105E RID: 4190 RVA: 0x0006E16C File Offset: 0x0006C36C
		private void method_1()
		{
			this.label_0 = new Label();
			this.textBox_0 = new TextBox();
			this.listBox_0 = new ListBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.label_1 = new Label();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(28, 29);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(77, 14);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(37357);
			this.textBox_0.Location = new Point(111, 26);
			this.textBox_0.Name = Class521.smethod_0(37378);
			this.textBox_0.Size = new Size(207, 22);
			this.textBox_0.TabIndex = 1;
			this.listBox_0.FormattingEnabled = true;
			this.listBox_0.Location = new Point(31, 80);
			this.listBox_0.Name = Class521.smethod_0(37403);
			this.listBox_0.Size = new Size(287, 121);
			this.listBox_0.TabIndex = 2;
			this.listBox_0.SelectedIndexChanged += this.listBox_0_SelectedIndexChanged;
			this.button_0.Location = new Point(130, 214);
			this.button_0.Name = Class521.smethod_0(7442);
			this.button_0.Size = new Size(91, 28);
			this.button_0.TabIndex = 3;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Location = new Point(237, 214);
			this.button_1.Name = Class521.smethod_0(7421);
			this.button_1.Size = new Size(91, 28);
			this.button_1.TabIndex = 4;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(28, 59);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(70, 14);
			this.label_1.TabIndex = 5;
			this.label_1.Text = Class521.smethod_0(37424);
			base.AutoScaleDimensions = new SizeF(7f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(350, 256);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.listBox_0);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.label_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(37445);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(37466);
			base.Load += this.Form17_Load;
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x0600105F RID: 4191 RVA: 0x0006E560 File Offset: 0x0006C760
		[CompilerGenerated]
		private bool method_2(ChartPage chartPage_0)
		{
			return chartPage_0.Name == this.textBox_0.Text;
		}

		// Token: 0x0400082A RID: 2090
		private bool bool_0;

		// Token: 0x0400082B RID: 2091
		private ChartUISettings? nullable_0;

		// Token: 0x0400082C RID: 2092
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400082D RID: 2093
		private IContainer icontainer_0;

		// Token: 0x0400082E RID: 2094
		private Label label_0;

		// Token: 0x0400082F RID: 2095
		private TextBox textBox_0;

		// Token: 0x04000830 RID: 2096
		private ListBox listBox_0;

		// Token: 0x04000831 RID: 2097
		private Button button_0;

		// Token: 0x04000832 RID: 2098
		private Button button_1;

		// Token: 0x04000833 RID: 2099
		private Label label_1;
	}
}
