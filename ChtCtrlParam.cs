﻿using System;
using System.Xml.Linq;
using ns18;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020001CE RID: 462
	[Serializable]
	internal class ChtCtrlParam
	{
		// Token: 0x06001219 RID: 4633 RVA: 0x00082290 File Offset: 0x00080490
		public ChtCtrlParam method_0(ChtCtrlParam chtCtrlParam_0)
		{
			chtCtrlParam_0.HasParentSpContainer = this.HasParentSpContainer;
			chtCtrlParam_0.IndexOfLastItemShown = this.IndexOfLastItemShown;
			chtCtrlParam_0.IsInParentSpContainerPanel1 = this.IsInParentSpContainerPanel1;
			chtCtrlParam_0.IfShowTickBidPanel = this.IfShowTickBidPanel;
			chtCtrlParam_0.MaxSticksPerChart = this.MaxSticksPerChart;
			chtCtrlParam_0.ParentSpContainerTag = this.ParentSpContainerTag;
			chtCtrlParam_0.PeriodUnits = this.PeriodUnits;
			chtCtrlParam_0.PeriodType = this.PeriodType;
			chtCtrlParam_0.SpCParam_this = this.SpCParam_this;
			chtCtrlParam_0.IfNoSync = this.IfNoSync;
			chtCtrlParam_0.LinkedSymbolId = this.LinkedSymbolId;
			chtCtrlParam_0.IsSwitchedBehind = this.IsSwitchedBehind;
			chtCtrlParam_0.Tag = this.Tag;
			return chtCtrlParam_0;
		}

		// Token: 0x0600121A RID: 4634 RVA: 0x00082340 File Offset: 0x00080540
		public virtual XElement vmethod_0()
		{
			XElement xelement = new XElement(Class521.smethod_0(2036), new object[]
			{
				this.SpCParam_this.GetXElement(Class521.smethod_0(2116)),
				new XElement(Class521.smethod_0(2137), Convert.ToInt32(this.HasParentSpContainer)),
				new XElement(Class521.smethod_0(2166), this.ParentSpContainerTag),
				new XElement(Class521.smethod_0(2195), Convert.ToInt32(this.IsInParentSpContainerPanel1)),
				new XElement(Class521.smethod_0(2232), Convert.ToInt32(this.IfShowTickBidPanel)),
				new XElement(Class521.smethod_0(2257), this.Tag),
				new XElement(Class521.smethod_0(2262), Convert.ToInt32(this.PeriodType)),
				new XElement(Class521.smethod_0(2279), (this.PeriodUnits != null) ? Convert.ToInt32(this.PeriodUnits).ToString() : string.Empty),
				new XElement(Class521.smethod_0(2296), Convert.ToInt32(this.MaxSticksPerChart)),
				new XElement(Class521.smethod_0(2321), Convert.ToInt32(this.IsSwitchedBehind))
			});
			if (this.IsSwitchedBehind)
			{
				xelement.SetAttributeValue(Class521.smethod_0(2082), Convert.ToInt32(false));
			}
			else
			{
				xelement.SetAttributeValue(Class521.smethod_0(2082), Convert.ToInt32(this.IfNoSync));
				if (this.LinkedSymbolId != null)
				{
					xelement.SetAttributeValue(Class521.smethod_0(2095), Convert.ToInt32(this.LinkedSymbolId.Value));
				}
			}
			return xelement;
		}

		// Token: 0x170002A1 RID: 673
		// (get) Token: 0x0600121B RID: 4635 RVA: 0x00082584 File Offset: 0x00080784
		// (set) Token: 0x0600121C RID: 4636 RVA: 0x0000792A File Offset: 0x00005B2A
		public SplitContainerParam SpCParam_this
		{
			get
			{
				return this._SpCParam_this;
			}
			set
			{
				this._SpCParam_this = value;
			}
		}

		// Token: 0x170002A2 RID: 674
		// (get) Token: 0x0600121D RID: 4637 RVA: 0x0008259C File Offset: 0x0008079C
		// (set) Token: 0x0600121E RID: 4638 RVA: 0x00007935 File Offset: 0x00005B35
		public int MaxSticksPerChart
		{
			get
			{
				return this._MaxSticksPerChart;
			}
			set
			{
				this._MaxSticksPerChart = value;
			}
		}

		// Token: 0x170002A3 RID: 675
		// (get) Token: 0x0600121F RID: 4639 RVA: 0x000825B4 File Offset: 0x000807B4
		public int IndexOfFirstItemShown
		{
			get
			{
				int result;
				if (this._IndexOfLastItemShown < this._MaxSticksPerChart)
				{
					result = 0;
				}
				else
				{
					result = this._IndexOfLastItemShown - this._MaxSticksPerChart + 1;
				}
				return result;
			}
		}

		// Token: 0x170002A4 RID: 676
		// (get) Token: 0x06001220 RID: 4640 RVA: 0x000825E8 File Offset: 0x000807E8
		// (set) Token: 0x06001221 RID: 4641 RVA: 0x00007940 File Offset: 0x00005B40
		public int IndexOfLastItemShown
		{
			get
			{
				return this._IndexOfLastItemShown;
			}
			set
			{
				this._IndexOfLastItemShown = value;
			}
		}

		// Token: 0x170002A5 RID: 677
		// (get) Token: 0x06001222 RID: 4642 RVA: 0x00082600 File Offset: 0x00080800
		// (set) Token: 0x06001223 RID: 4643 RVA: 0x0000794B File Offset: 0x00005B4B
		public PeriodType PeriodType
		{
			get
			{
				return this._PeriodType;
			}
			set
			{
				this._PeriodType = value;
			}
		}

		// Token: 0x170002A6 RID: 678
		// (get) Token: 0x06001224 RID: 4644 RVA: 0x00082618 File Offset: 0x00080818
		// (set) Token: 0x06001225 RID: 4645 RVA: 0x00007956 File Offset: 0x00005B56
		public int? PeriodUnits
		{
			get
			{
				return this._PeriodUnits;
			}
			set
			{
				this._PeriodUnits = value;
			}
		}

		// Token: 0x170002A7 RID: 679
		// (get) Token: 0x06001226 RID: 4646 RVA: 0x00082630 File Offset: 0x00080830
		// (set) Token: 0x06001227 RID: 4647 RVA: 0x00007961 File Offset: 0x00005B61
		[Obsolete]
		public int? PeriodInMins
		{
			get
			{
				return this._PeriodInMins;
			}
			set
			{
				this._PeriodInMins = value;
			}
		}

		// Token: 0x170002A8 RID: 680
		// (get) Token: 0x06001228 RID: 4648 RVA: 0x00082648 File Offset: 0x00080848
		// (set) Token: 0x06001229 RID: 4649 RVA: 0x0000796C File Offset: 0x00005B6C
		public string Tag
		{
			get
			{
				return this._Tag;
			}
			set
			{
				this._Tag = value;
			}
		}

		// Token: 0x170002A9 RID: 681
		// (get) Token: 0x0600122A RID: 4650 RVA: 0x00082660 File Offset: 0x00080860
		// (set) Token: 0x0600122B RID: 4651 RVA: 0x00007977 File Offset: 0x00005B77
		public bool HasParentSpContainer
		{
			get
			{
				return this._HasParentSpContainer;
			}
			set
			{
				this._HasParentSpContainer = value;
			}
		}

		// Token: 0x170002AA RID: 682
		// (get) Token: 0x0600122C RID: 4652 RVA: 0x00082678 File Offset: 0x00080878
		// (set) Token: 0x0600122D RID: 4653 RVA: 0x00007982 File Offset: 0x00005B82
		public string ParentSpContainerTag
		{
			get
			{
				return this._ParentSpContainerTag;
			}
			set
			{
				this._ParentSpContainerTag = value;
			}
		}

		// Token: 0x170002AB RID: 683
		// (get) Token: 0x0600122E RID: 4654 RVA: 0x00082690 File Offset: 0x00080890
		// (set) Token: 0x0600122F RID: 4655 RVA: 0x0000798D File Offset: 0x00005B8D
		public bool IsInParentSpContainerPanel1
		{
			get
			{
				return this._IsInParentSpContainerPanel1;
			}
			set
			{
				this._IsInParentSpContainerPanel1 = value;
			}
		}

		// Token: 0x170002AC RID: 684
		// (get) Token: 0x06001230 RID: 4656 RVA: 0x000826A8 File Offset: 0x000808A8
		// (set) Token: 0x06001231 RID: 4657 RVA: 0x00007998 File Offset: 0x00005B98
		public bool IfShowTickBidPanel
		{
			get
			{
				return this._IfShowTickBidPanel;
			}
			set
			{
				this._IfShowTickBidPanel = value;
			}
		}

		// Token: 0x170002AD RID: 685
		// (get) Token: 0x06001232 RID: 4658 RVA: 0x000826C0 File Offset: 0x000808C0
		// (set) Token: 0x06001233 RID: 4659 RVA: 0x000079A3 File Offset: 0x00005BA3
		public bool IfNoSync
		{
			get
			{
				return this._IfNoSync;
			}
			set
			{
				this._IfNoSync = value;
			}
		}

		// Token: 0x170002AE RID: 686
		// (get) Token: 0x06001234 RID: 4660 RVA: 0x000826D8 File Offset: 0x000808D8
		// (set) Token: 0x06001235 RID: 4661 RVA: 0x000079AE File Offset: 0x00005BAE
		public int? LinkedSymbolId
		{
			get
			{
				return this._LinkedSymbolId;
			}
			set
			{
				this._LinkedSymbolId = value;
			}
		}

		// Token: 0x170002AF RID: 687
		// (get) Token: 0x06001236 RID: 4662 RVA: 0x000826F0 File Offset: 0x000808F0
		// (set) Token: 0x06001237 RID: 4663 RVA: 0x000079B9 File Offset: 0x00005BB9
		public bool IsSwitchedBehind
		{
			get
			{
				return this._IsSwitchedBehind;
			}
			set
			{
				this._IsSwitchedBehind = value;
			}
		}

		// Token: 0x0400097B RID: 2427
		private SplitContainerParam _SpCParam_this;

		// Token: 0x0400097C RID: 2428
		private int _MaxSticksPerChart;

		// Token: 0x0400097D RID: 2429
		private int _IndexOfLastItemShown;

		// Token: 0x0400097E RID: 2430
		private PeriodType _PeriodType;

		// Token: 0x0400097F RID: 2431
		private int? _PeriodUnits;

		// Token: 0x04000980 RID: 2432
		private int? _PeriodInMins;

		// Token: 0x04000981 RID: 2433
		private string _Tag;

		// Token: 0x04000982 RID: 2434
		private bool _HasParentSpContainer;

		// Token: 0x04000983 RID: 2435
		private string _ParentSpContainerTag;

		// Token: 0x04000984 RID: 2436
		private bool _IsInParentSpContainerPanel1;

		// Token: 0x04000985 RID: 2437
		private bool _IfShowTickBidPanel;

		// Token: 0x04000986 RID: 2438
		private bool _IfNoSync;

		// Token: 0x04000987 RID: 2439
		private int? _LinkedSymbolId;

		// Token: 0x04000988 RID: 2440
		public bool _IsSwitchedBehind;
	}
}
