﻿using System;
using System.Collections.Generic;
using ns10;
using ns11;
using ns12;
using ns18;
using ns21;
using ns26;
using ns28;
using ns7;
using TEx.SIndicator;

namespace ns9
{
	// Token: 0x02000315 RID: 789
	internal sealed class Class418 : Class415
	{
		// Token: 0x060021F4 RID: 8692 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class418(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x060021F5 RID: 8693 RVA: 0x000F0F04 File Offset: 0x000EF104
		public static Class411 smethod_0(Tokenes tokenes_0, HToken htoken_1)
		{
			HToken htoken_2 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_33, Class521.smethod_0(1449)));
			Class411 result;
			if (tokenes_0.Current.Symbol.Name == Class521.smethod_0(5046))
			{
				Class442 symbol = new Class442(Enum26.const_31, Class521.smethod_0(1449));
				HToken htoken_3 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, symbol);
				Class412 class411_ = new Class412(htoken_3);
				Class412 class411_2 = new Class412(htoken_3);
				result = new Class418(htoken_2, class411_, class411_2);
			}
			else
			{
				Class411 class411_3 = Class416.smethod_0(tokenes_0);
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_24)
				{
					Class412 class411_4 = new Class412(new HToken(0, 0, new Class442(Enum26.const_31, Class521.smethod_0(1449))));
					result = new Class418(htoken_2, class411_3, class411_4);
				}
				else
				{
					if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_21)
					{
						throw new Exception(htoken_1.method_0(Class521.smethod_0(101943)));
					}
					tokenes_0.method_1();
					Class411 class411_5 = Class418.smethod_1(tokenes_0, htoken_1);
					result = new Class418(htoken_2, class411_3, class411_5);
				}
			}
			return result;
		}

		// Token: 0x060021F6 RID: 8694 RVA: 0x000F1044 File Offset: 0x000EF244
		private static Class411 smethod_1(Tokenes tokenes_0, HToken htoken_1)
		{
			HToken htoken_2 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_32, Class521.smethod_0(23972)));
			Class411 class411_ = Class416.smethod_0(tokenes_0);
			tokenes_0.method_1();
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
			{
				tokenes_0.method_1();
				Class411 class411_2 = Class418.smethod_1(tokenes_0, htoken_1);
				result = new Class424(htoken_2, class411_, class411_2);
			}
			else
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_24)
				{
					throw new Exception(htoken_1.method_0(Class521.smethod_0(101943)));
				}
				Class412 class411_3 = new Class412(new HToken(0, 0, new Class442(Enum26.const_31, Class521.smethod_0(1449))));
				result = new Class424(htoken_2, class411_, class411_3);
			}
			return result;
		}

		// Token: 0x060021F7 RID: 8695 RVA: 0x000F1114 File Offset: 0x000EF314
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			List<object> list = new List<object>();
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			if (obj != null)
			{
				list.Add(obj);
			}
			Class411 right = this.Right;
			while (right.GetType() == typeof(Class424))
			{
				list.Add(right.Left.vmethod_1(parserEnvironment_0));
				if (right.Right.Token.Symbol.HSymbolType == Enum26.const_31)
				{
					IL_A0:
					return list;
				}
				right = right.Right;
			}
			if (right.Token.Symbol.HSymbolType != Enum26.const_31)
			{
				throw new Exception(this.Right.Token.method_0(Class521.smethod_0(101503)));
			}
			goto IL_A0;
		}

		// Token: 0x060021F8 RID: 8696 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}
	}
}
