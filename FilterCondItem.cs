﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns15;
using ns18;
using ns26;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000216 RID: 534
	public sealed class FilterCondItem : UserControl
	{
		// Token: 0x060015E8 RID: 5608 RVA: 0x00008DAD File Offset: 0x00006FAD
		public FilterCondItem()
		{
			this.method_6();
		}

		// Token: 0x060015E9 RID: 5609 RVA: 0x00097030 File Offset: 0x00095230
		public FilterCondItem(FilterCond cond)
		{
			this.method_6();
			this.method_0();
			this.tableLayoutPanel_0.BackColor = Color.FromKnownColor(KnownColor.Transparent);
			this.BackColor = Color.FromKnownColor(KnownColor.Transparent);
			this.method_1();
			this.FilterCond = cond;
			this.CondName = cond.Name;
			this.UnitType = cond.UnitType;
			this.ComparisonOpt = cond.Opt;
			if (cond != null && cond.Value != null)
			{
				this.textBox_0.Text = Convert.ToString(cond.Value);
			}
			base.Margin = new Padding(1);
			base.Padding = new Padding(0);
		}

		// Token: 0x060015EA RID: 5610 RVA: 0x000970E8 File Offset: 0x000952E8
		private void method_0()
		{
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font(Class521.smethod_0(7183), (float)(11.25 / TApp.DpiScale), FontStyle.Regular, GraphicsUnit.Point, 134);
				this.label_1.Font = font;
				this.label_0.Font = font;
				this.comboBox_0.Font = font;
			}
		}

		// Token: 0x060015EB RID: 5611 RVA: 0x0009714C File Offset: 0x0009534C
		private void method_1()
		{
			this.list_0 = new List<ComboBoxItem>
			{
				new ComboBoxItem(Class521.smethod_0(53095), ComparisonOpt.Bigger),
				new ComboBoxItem(Class521.smethod_0(53104), ComparisonOpt.Less),
				new ComboBoxItem(Class521.smethod_0(53113), ComparisonOpt.Equal),
				new ComboBoxItem(Class521.smethod_0(53122), ComparisonOpt.BiggerOrEqual),
				new ComboBoxItem(Class521.smethod_0(53139), ComparisonOpt.LessOrEqual)
			};
			this.comboBox_0.Items.Clear();
			this.comboBox_0.DataSource = this.list_0;
			this.comboBox_0.SelectedIndex = 0;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			this.toolTip_0 = new ToolTip();
			this.panel_0.Click += this.panel_0_Click;
			this.panel_0.MouseEnter += this.panel_0_MouseEnter;
			this.panel_0.MouseLeave += this.panel_0_MouseLeave;
			base.ParentChanged += this.FilterCondItem_ParentChanged;
			this.textBox_0.Enter += this.textBox_0_Enter;
			this.textBox_0.MouseHover += this.textBox_0_MouseHover;
			this.textBox_0.Leave += this.textBox_0_Leave;
		}

		// Token: 0x060015EC RID: 5612 RVA: 0x000972D8 File Offset: 0x000954D8
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			int selectedIndex = this.comboBox_0.SelectedIndex;
			ComboBoxItem comboBoxItem = this.list_0[selectedIndex];
			this.FilterCond.Opt = (ComparisonOpt)comboBoxItem.Value;
		}

		// Token: 0x060015ED RID: 5613 RVA: 0x00008DBD File Offset: 0x00006FBD
		private void method_2(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x060015EE RID: 5614 RVA: 0x00097318 File Offset: 0x00095518
		public void method_3()
		{
			Color foreColor = Base.UI.smethod_35();
			this.label_0.ForeColor = foreColor;
			this.label_1.ForeColor = foreColor;
		}

		// Token: 0x060015EF RID: 5615 RVA: 0x00097348 File Offset: 0x00095548
		private void panel_0_Click(object sender, EventArgs e)
		{
			Control parent = base.Parent;
			if (parent != null)
			{
				parent.SizeChanged -= this.method_5;
				base.ParentChanged -= this.FilterCondItem_ParentChanged;
				parent.Controls.Remove(this);
			}
		}

		// Token: 0x060015F0 RID: 5616 RVA: 0x00008DC7 File Offset: 0x00006FC7
		private void textBox_0_MouseHover(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060015F1 RID: 5617 RVA: 0x00008DC7 File Offset: 0x00006FC7
		private void textBox_0_Enter(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060015F2 RID: 5618 RVA: 0x00097394 File Offset: 0x00095594
		private void textBox_0_Leave(object sender, EventArgs e)
		{
			try
			{
				double? value = null;
				string text = this.textBox_0.Text;
				if (!string.IsNullOrEmpty(text))
				{
					value = new double?(Convert.ToDouble(text));
					this.FilterCond.Value = value;
				}
				else
				{
					this.FilterCond.Value = null;
				}
			}
			catch
			{
				MessageBox.Show(Class521.smethod_0(53156), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_0.Focus();
			}
		}

		// Token: 0x060015F3 RID: 5619 RVA: 0x00008DD1 File Offset: 0x00006FD1
		private void method_4()
		{
			if (!string.IsNullOrEmpty(this.FilterCond.Hint))
			{
				this.toolTip_0.SetToolTip(this.textBox_0, this.FilterCond.Hint);
			}
		}

		// Token: 0x060015F4 RID: 5620 RVA: 0x00008E03 File Offset: 0x00007003
		private void panel_0_MouseEnter(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.panel_0, Class521.smethod_0(53189));
			this.panel_0.BackgroundImage = Class375.remove_blue_16x;
		}

		// Token: 0x060015F5 RID: 5621 RVA: 0x00008E32 File Offset: 0x00007032
		private void panel_0_MouseLeave(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.panel_0, null);
			this.panel_0.BackgroundImage = Class375.remove_gray_16x;
		}

		// Token: 0x060015F6 RID: 5622 RVA: 0x0009742C File Offset: 0x0009562C
		private void FilterCondItem_ParentChanged(object sender, EventArgs e)
		{
			Control parent = base.Parent;
			if (parent != null)
			{
				int verticalScrollBarWidth = SystemInformation.VerticalScrollBarWidth;
				base.Width = parent.Width - SystemInformation.VerticalScrollBarWidth - 2;
				parent.SizeChanged += this.method_5;
			}
		}

		// Token: 0x060015F7 RID: 5623 RVA: 0x00008E58 File Offset: 0x00007058
		private void method_5(object sender, EventArgs e)
		{
			base.Width = (sender as Control).Width - SystemInformation.VerticalScrollBarWidth - 2;
		}

		// Token: 0x1700038C RID: 908
		// (get) Token: 0x060015F8 RID: 5624 RVA: 0x00097474 File Offset: 0x00095674
		// (set) Token: 0x060015F9 RID: 5625 RVA: 0x00008E75 File Offset: 0x00007075
		public FilterCond FilterCond { get; set; }

		// Token: 0x1700038D RID: 909
		// (get) Token: 0x060015FA RID: 5626 RVA: 0x0009748C File Offset: 0x0009568C
		// (set) Token: 0x060015FB RID: 5627 RVA: 0x00008E80 File Offset: 0x00007080
		public string CondName
		{
			get
			{
				return this.label_0.Text;
			}
			set
			{
				this.label_0.Text = value;
			}
		}

		// Token: 0x1700038E RID: 910
		// (get) Token: 0x060015FC RID: 5628 RVA: 0x000974A8 File Offset: 0x000956A8
		// (set) Token: 0x060015FD RID: 5629 RVA: 0x000974C0 File Offset: 0x000956C0
		public Enum20 UnitType
		{
			get
			{
				return this.enum20_0;
			}
			set
			{
				if (value == Enum20.const_1)
				{
					this.label_1.Text = Class521.smethod_0(53206);
				}
				else if (value == Enum20.const_2)
				{
					this.label_1.Text = Class521.smethod_0(1567);
				}
				else if (value == Enum20.const_3)
				{
					this.label_1.Text = Class521.smethod_0(1576);
				}
				else
				{
					this.label_1.Text = Class521.smethod_0(1449);
				}
				this.enum20_0 = value;
			}
		}

		// Token: 0x1700038F RID: 911
		// (get) Token: 0x060015FE RID: 5630 RVA: 0x0009753C File Offset: 0x0009573C
		// (set) Token: 0x060015FF RID: 5631 RVA: 0x00097570 File Offset: 0x00095770
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				int selectedIndex = this.comboBox_0.SelectedIndex;
				return (ComparisonOpt)this.list_0[selectedIndex].Value;
			}
			set
			{
				FilterCondItem.Class295 @class = new FilterCondItem.Class295();
				@class.comparisonOpt_0 = value;
				int selectedIndex = this.list_0.FindIndex(new Predicate<ComboBoxItem>(@class.method_0));
				this.comboBox_0.SelectedIndex = selectedIndex;
			}
		}

		// Token: 0x17000390 RID: 912
		// (get) Token: 0x06001600 RID: 5632 RVA: 0x000975B0 File Offset: 0x000957B0
		// (set) Token: 0x06001601 RID: 5633 RVA: 0x00008E90 File Offset: 0x00007090
		public double? CondValue
		{
			get
			{
				double? result = null;
				string text = this.textBox_0.Text;
				if (!string.IsNullOrEmpty(text))
				{
					result = new double?(Convert.ToDouble(text));
				}
				return result;
			}
			set
			{
				if (value != null)
				{
					this.textBox_0.Text = Convert.ToString(value);
				}
				this.FilterCond.Value = value;
			}
		}

		// Token: 0x17000391 RID: 913
		// (get) Token: 0x06001602 RID: 5634 RVA: 0x000975EC File Offset: 0x000957EC
		public bool IsInInputState
		{
			get
			{
				return this.textBox_0.Focused;
			}
		}

		// Token: 0x06001603 RID: 5635 RVA: 0x00008EBF File Offset: 0x000070BF
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001604 RID: 5636 RVA: 0x00097608 File Offset: 0x00095808
		private void method_6()
		{
			this.tableLayoutPanel_0 = new TableLayoutPanel();
			this.label_0 = new Label();
			this.comboBox_0 = new ComboBox();
			this.textBox_0 = new TextBox();
			this.label_1 = new Label();
			this.panel_0 = new Panel();
			this.tableLayoutPanel_0.SuspendLayout();
			base.SuspendLayout();
			this.tableLayoutPanel_0.ColumnCount = 5;
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 51f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 23f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 26f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 44f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 21f));
			this.tableLayoutPanel_0.Controls.Add(this.label_0, 0, 0);
			this.tableLayoutPanel_0.Controls.Add(this.comboBox_0, 1, 0);
			this.tableLayoutPanel_0.Controls.Add(this.textBox_0, 2, 0);
			this.tableLayoutPanel_0.Controls.Add(this.label_1, 3, 0);
			this.tableLayoutPanel_0.Controls.Add(this.panel_0, 4, 0);
			this.tableLayoutPanel_0.Dock = DockStyle.Fill;
			this.tableLayoutPanel_0.Location = new Point(0, 0);
			this.tableLayoutPanel_0.Margin = new Padding(0);
			this.tableLayoutPanel_0.MaximumSize = new Size(500, 26);
			this.tableLayoutPanel_0.MinimumSize = new Size(400, 26);
			this.tableLayoutPanel_0.Name = Class521.smethod_0(53215);
			this.tableLayoutPanel_0.RowCount = 1;
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Size = new Size(400, 26);
			this.tableLayoutPanel_0.TabIndex = 0;
			this.label_0.Anchor = AnchorStyles.Left;
			this.label_0.AutoSize = true;
			this.label_0.Font = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_0.Location = new Point(3, 5);
			this.label_0.Name = Class521.smethod_0(53236);
			this.label_0.Size = new Size(120, 16);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(53236);
			this.comboBox_0.Dock = DockStyle.Fill;
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.ItemHeight = 15;
			this.comboBox_0.Location = new Point(170, 0);
			this.comboBox_0.Margin = new Padding(0);
			this.comboBox_0.MinimumSize = new Size(70, 0);
			this.comboBox_0.Name = Class521.smethod_0(53257);
			this.comboBox_0.Size = new Size(77, 23);
			this.comboBox_0.TabIndex = 1;
			this.textBox_0.Anchor = (AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
			this.textBox_0.BorderStyle = BorderStyle.None;
			this.textBox_0.Font = new Font(Class521.smethod_0(24023), 9f);
			this.textBox_0.Location = new Point(250, 0);
			this.textBox_0.Margin = new Padding(3, 0, 3, 0);
			this.textBox_0.MaximumSize = new Size(0, 28);
			this.textBox_0.MinimumSize = new Size(0, 23);
			this.textBox_0.Name = Class521.smethod_0(53274);
			this.textBox_0.RightToLeft = RightToLeft.No;
			this.textBox_0.Size = new Size(81, 23);
			this.textBox_0.TabIndex = 2;
			this.textBox_0.TextAlign = HorizontalAlignment.Right;
			this.label_1.Anchor = AnchorStyles.Left;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(337, 5);
			this.label_1.Name = Class521.smethod_0(53295);
			this.label_1.Size = new Size(0, 15);
			this.label_1.TabIndex = 3;
			this.panel_0.BackgroundImage = Class375.remove_gray_16x;
			this.panel_0.BackgroundImageLayout = ImageLayout.Center;
			this.panel_0.Location = new Point(378, 0);
			this.panel_0.Margin = new Padding(0);
			this.panel_0.Name = Class521.smethod_0(53312);
			this.panel_0.Size = new Size(22, 26);
			this.panel_0.TabIndex = 4;
			base.AutoScaleMode = AutoScaleMode.None;
			this.BackColor = SystemColors.Control;
			base.Controls.Add(this.tableLayoutPanel_0);
			this.DoubleBuffered = true;
			base.Margin = new Padding(0);
			this.MaximumSize = new Size(480, 25);
			this.MinimumSize = new Size(390, 25);
			base.Name = Class521.smethod_0(53333);
			base.Size = new Size(390, 25);
			this.tableLayoutPanel_0.ResumeLayout(false);
			this.tableLayoutPanel_0.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000B15 RID: 2837
		private List<ComboBoxItem> list_0;

		// Token: 0x04000B16 RID: 2838
		private ToolTip toolTip_0;

		// Token: 0x04000B17 RID: 2839
		[CompilerGenerated]
		private FilterCond filterCond_0;

		// Token: 0x04000B18 RID: 2840
		private Enum20 enum20_0;

		// Token: 0x04000B19 RID: 2841
		private IContainer icontainer_0;

		// Token: 0x04000B1A RID: 2842
		private TableLayoutPanel tableLayoutPanel_0;

		// Token: 0x04000B1B RID: 2843
		private Label label_0;

		// Token: 0x04000B1C RID: 2844
		private ComboBox comboBox_0;

		// Token: 0x04000B1D RID: 2845
		private TextBox textBox_0;

		// Token: 0x04000B1E RID: 2846
		private Label label_1;

		// Token: 0x04000B1F RID: 2847
		private Panel panel_0;

		// Token: 0x02000217 RID: 535
		[CompilerGenerated]
		private sealed class Class295
		{
			// Token: 0x06001606 RID: 5638 RVA: 0x00097BBC File Offset: 0x00095DBC
			internal bool method_0(ComboBoxItem comboBoxItem_0)
			{
				return (ComparisonOpt)comboBoxItem_0.Value == this.comparisonOpt_0;
			}

			// Token: 0x04000B20 RID: 2848
			public ComparisonOpt comparisonOpt_0;
		}
	}
}
