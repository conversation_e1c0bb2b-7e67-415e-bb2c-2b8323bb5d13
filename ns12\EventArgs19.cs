﻿using System;

namespace ns12
{
	// Token: 0x02000276 RID: 630
	internal sealed class EventArgs19 : EventArgs
	{
		// Token: 0x06001B7A RID: 7034 RVA: 0x0000B5F5 File Offset: 0x000097F5
		public EventArgs19(bool bool_1)
		{
			this.bool_0 = bool_1;
		}

		// Token: 0x1700047A RID: 1146
		// (get) Token: 0x06001B7B RID: 7035 RVA: 0x000BFE04 File Offset: 0x000BE004
		// (set) Token: 0x06001B7C RID: 7036 RVA: 0x0000B606 File Offset: 0x00009806
		public bool Cancel
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x04000D96 RID: 3478
		private bool bool_0;
	}
}
