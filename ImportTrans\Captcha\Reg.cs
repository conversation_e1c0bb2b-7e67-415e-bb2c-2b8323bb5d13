﻿using System;
using System.Runtime.CompilerServices;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x020003AA RID: 938
	public sealed class Reg
	{
		// Token: 0x060025D1 RID: 9681 RVA: 0x0000E729 File Offset: 0x0000C929
		public Reg(string name = "", double persent = 0.0)
		{
			this.Name = name;
			this.Persent = persent;
		}

		// Token: 0x1700064A RID: 1610
		// (get) Token: 0x060025D2 RID: 9682 RVA: 0x00103A9C File Offset: 0x00101C9C
		// (set) Token: 0x060025D3 RID: 9683 RVA: 0x0000E741 File Offset: 0x0000C941
		public string Name { get; set; }

		// Token: 0x1700064B RID: 1611
		// (get) Token: 0x060025D4 RID: 9684 RVA: 0x00103AB4 File Offset: 0x00101CB4
		// (set) Token: 0x060025D5 RID: 9685 RVA: 0x0000E74C File Offset: 0x0000C94C
		public double Persent { get; set; }

		// Token: 0x04001241 RID: 4673
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04001242 RID: 4674
		[CompilerGenerated]
		private double double_0;
	}
}
