﻿using System;
using System.Drawing;
using ns18;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns12
{
	// Token: 0x020002F7 RID: 759
	internal sealed class Class395 : ShapeCurve
	{
		// Token: 0x0600212B RID: 8491 RVA: 0x000EB8E4 File Offset: 0x000E9AE4
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			if (base.IndData.SingleData.Contains(Class521.smethod_0(98539)))
			{
				this.string_0 = (string)base.IndData.SingleData[Class521.smethod_0(98539)];
			}
			Ind_TextItem ind_TextItem = zedGraphControl_0.GraphPane.AddTextItem(base.IndData.Name, base.DataView, SymbolType.None, Class521.smethod_0(98548));
			ind_TextItem.Symbol.Border.Color = color_0;
			this.curveItem_0 = ind_TextItem;
			ind_TextItem.Line.IsVisible = false;
			ind_TextItem.Tag = string_1 + Class521.smethod_0(2712) + base.IndData.Name;
			base.method_3(string_1, ind_TextItem);
		}

		// Token: 0x0600212C RID: 8492 RVA: 0x0000D64F File Offset: 0x0000B84F
		public Class395(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x0600212D RID: 8493 RVA: 0x000EB9CC File Offset: 0x000E9BCC
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_0);
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				throw new Exception(Class521.smethod_0(97541));
			}
			double num = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList.Count != 2)
			{
				throw new Exception(Class521.smethod_0(98360));
			}
			int num2 = (int)dataArray_1.OtherDataArrayList[0].Data[int_0];
			double d = dataArray_1.OtherDataArrayList[1].Data[int_0];
			PointPair result;
			if (num2 == 1 && !double.IsNaN(d) && !double.IsNaN(num))
			{
				result = new PointPair(new XDate(dateTime), num, 1.0, d.ToString(this.string_0));
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, 0.0);
			}
			return result;
		}

		// Token: 0x04001030 RID: 4144
		private string string_0 = Class521.smethod_0(1449);
	}
}
