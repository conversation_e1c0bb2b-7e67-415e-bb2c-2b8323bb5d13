﻿using System;
using System.Collections.Generic;
using ns12;
using ns28;
using ns7;
using TEx.SIndicator;

namespace ns10
{
	// Token: 0x02000318 RID: 792
	internal sealed class Class421 : Class415
	{
		// Token: 0x06002201 RID: 8705 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class421(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x06002202 RID: 8706 RVA: 0x000F1600 File Offset: 0x000EF800
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			List<string> list = new List<string>();
			list.Add(this.Token.Symbol.Name);
			Class411 @class = this.Left;
			while (@class.Token.Symbol.HSymbolType != Enum26.const_30)
			{
				Class411 left = @class.Left;
				list.Add(left.Token.Symbol.Name);
				@class = @class.Right;
			}
			return list;
		}
	}
}
