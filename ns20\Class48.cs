﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using ns15;
using ns16;
using ns18;
using TEx;
using TEx.Util;

namespace ns20
{
	// Token: 0x02000062 RID: 98
	internal static class Class48
	{
		// Token: 0x0600034F RID: 847 RVA: 0x0001FF8C File Offset: 0x0001E18C
		public static void smethod_0(string string_1, string string_2)
		{
			StreamWriter streamWriter = null;
			try
			{
				string text = TApp.StartedUp ? (TApp.UserAcctFolder + Class521.smethod_0(4976)) : Class521.smethod_0(4967);
				Utility.CreateDir(text);
				streamWriter = new StreamWriter(text + string_1, true, Encoding.UTF8);
				string value = string.Format(Class521.smethod_0(4985), DateTime.Now) + Class521.smethod_0(5018) + string_2;
				streamWriter.WriteLine(value);
			}
			catch
			{
			}
			if (streamWriter != null)
			{
				try
				{
					streamWriter.Close();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000350 RID: 848 RVA: 0x00020040 File Offset: 0x0001E240
		public static void smethod_1(string string_1, string string_2, string string_3)
		{
			Class48.smethod_0(Class521.smethod_0(5023), string.Concat(new string[]
			{
				string_1,
				Class521.smethod_0(5018),
				string_2,
				Class521.smethod_0(5018),
				string_3
			}));
		}

		// Token: 0x06000351 RID: 849 RVA: 0x000036D4 File Offset: 0x000018D4
		public static void smethod_2(string string_1)
		{
			Class48.smethod_0(Class521.smethod_0(5023), Class23.Info + Class521.smethod_0(5018) + string_1);
		}

		// Token: 0x06000352 RID: 850 RVA: 0x00020090 File Offset: 0x0001E290
		public static void smethod_3(string string_1, string string_2)
		{
			Class48.smethod_0(Class521.smethod_0(5023), string.Concat(new string[]
			{
				Class23.Info,
				Class521.smethod_0(5018),
				string_1,
				Class521.smethod_0(5018),
				string_2
			}));
		}

		// Token: 0x06000353 RID: 851 RVA: 0x000200E4 File Offset: 0x0001E2E4
		public static void smethod_4(Exception exception_0, bool bool_0 = true, string string_1 = null)
		{
			string text = exception_0.Message + Class521.smethod_0(5036) + exception_0.StackTrace;
			if (bool_0 && exception_0.InnerException != null)
			{
				text = text + Class521.smethod_0(5041) + exception_0.InnerException.Message + Class521.smethod_0(5046);
			}
			if (!string.IsNullOrEmpty(string_1))
			{
				text = text + Class521.smethod_0(5036) + string_1;
			}
			Class48.smethod_0(Class521.smethod_0(5023), string.Concat(new string[]
			{
				Class23.Error,
				Class521.smethod_0(5018),
				Class24.ExceptionOccurred,
				Class521.smethod_0(5018),
				text
			}));
		}

		// Token: 0x06000354 RID: 852 RVA: 0x000201A4 File Offset: 0x0001E3A4
		public static bool smethod_5(int int_0)
		{
			return Class48.smethod_6(Class48.LogFilePath, int_0);
		}

		// Token: 0x06000355 RID: 853 RVA: 0x000201C0 File Offset: 0x0001E3C0
		public static bool smethod_6(string string_1, int int_0)
		{
			bool result = false;
			FileInfo fileInfo = new FileInfo(Class48.LogFilePath);
			if (fileInfo.Exists && (double)fileInfo.Length / 1024.0 > (double)int_0)
			{
				List<string> list = new List<string>(File.ReadAllLines(fileInfo.FullName));
				int num = Convert.ToInt32(Math.Round((double)list.Count / 2.0));
				if (num > 0)
				{
					list.RemoveRange(0, num);
					try
					{
						File.WriteAllLines(fileInfo.FullName, list.ToArray());
						result = true;
					}
					catch
					{
					}
				}
			}
			return result;
		}

		// Token: 0x170000C5 RID: 197
		// (get) Token: 0x06000356 RID: 854 RVA: 0x00020260 File Offset: 0x0001E460
		public static string LogFilePath
		{
			get
			{
				return TApp.UserAcctFolder + Class521.smethod_0(5051);
			}
		}

		// Token: 0x0400012C RID: 300
		private const string string_0 = "TEx.log";
	}
}
