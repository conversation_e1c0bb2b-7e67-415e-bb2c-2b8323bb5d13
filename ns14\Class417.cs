﻿using System;
using ns12;
using ns18;
using ns28;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns14
{
	// Token: 0x02000310 RID: 784
	internal sealed class Class417 : Class415
	{
		// Token: 0x060021D0 RID: 8656 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class417(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x060021D1 RID: 8657 RVA: 0x000F0334 File Offset: 0x000EE534
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_6 && this.Token.Symbol.HSymbolType != Enum26.const_7)
			{
				throw new Exception(this.Token.method_0(Class521.smethod_0(101570)));
			}
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021D2 RID: 8658 RVA: 0x000F03A8 File Offset: 0x000EE5A8
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_6)
			{
				if (hsymbolType != Enum26.const_7)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101570)));
				}
				result = double_0 - double_1;
			}
			else
			{
				result = double_0 + double_1;
			}
			return result;
		}

		// Token: 0x060021D3 RID: 8659 RVA: 0x000F03F8 File Offset: 0x000EE5F8
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_6)
			{
				if (hsymbolType != Enum26.const_7)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101570)));
				}
				result = dataArray_0 - dataArray_1;
			}
			else
			{
				result = dataArray_0 + dataArray_1;
			}
			return result;
		}

		// Token: 0x060021D4 RID: 8660 RVA: 0x000F0450 File Offset: 0x000EE650
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			Class411 @class = Class423.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0(Class521.smethod_0(101603)));
			}
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = @class;
			}
			else
			{
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
				{
					result = Class417.smethod_1(@class, tokenes_0);
				}
				else
				{
					tokenes_0.method_2();
					result = @class;
				}
			}
			return result;
		}

		// Token: 0x060021D5 RID: 8661 RVA: 0x000F04E4 File Offset: 0x000EE6E4
		private static Class411 smethod_1(Class411 class411_2, Tokenes tokenes_0)
		{
			if (!(tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7))
			{
				throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(101570)));
			}
			HToken htoken = tokenes_0.Current;
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(htoken.method_0(Class521.smethod_0(101624)));
			}
			Class411 @class = Class423.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0(Class521.smethod_0(101645)));
			}
			Class417 class2 = new Class417(htoken, class411_2, @class);
			tokenes_0.method_1();
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
			{
				result = Class417.smethod_1(class2, tokenes_0);
			}
			else if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = class2;
			}
			else
			{
				tokenes_0.method_2();
				result = class2;
			}
			return result;
		}

		// Token: 0x060021D6 RID: 8662 RVA: 0x000F05F8 File Offset: 0x000EE7F8
		public string ToString()
		{
			return base.ToString();
		}

		// Token: 0x170005D1 RID: 1489
		// (get) Token: 0x060021D7 RID: 8663 RVA: 0x000F0610 File Offset: 0x000EE810
		// (set) Token: 0x060021D8 RID: 8664 RVA: 0x0000D99E File Offset: 0x0000BB9E
		public override Class411 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005D2 RID: 1490
		// (get) Token: 0x060021D9 RID: 8665 RVA: 0x000F0628 File Offset: 0x000EE828
		// (set) Token: 0x060021DA RID: 8666 RVA: 0x0000D9A9 File Offset: 0x0000BBA9
		public override Class411 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005D3 RID: 1491
		// (get) Token: 0x060021DB RID: 8667 RVA: 0x000F0640 File Offset: 0x000EE840
		// (set) Token: 0x060021DC RID: 8668 RVA: 0x0000D9B4 File Offset: 0x0000BBB4
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}
	}
}
