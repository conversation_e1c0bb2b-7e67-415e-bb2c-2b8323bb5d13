﻿using System;
using System.ComponentModel;
using System.Runtime.Serialization;
using ns18;

namespace TEx.Trading
{
	// Token: 0x020003AF RID: 943
	[Serializable]
	public class Transaction : ISerializable
	{
		// Token: 0x06002626 RID: 9766 RVA: 0x00002D25 File Offset: 0x00000F25
		public Transaction()
		{
		}

		// Token: 0x06002627 RID: 9767 RVA: 0x00104964 File Offset: 0x00102B64
		protected Transaction(SerializationInfo info, StreamingContext context)
		{
			this.int_0 = info.GetInt32(Class521.smethod_0(110723));
			this.int_1 = info.GetInt32(Class521.smethod_0(110728));
			this.int_2 = info.GetInt32(Class521.smethod_0(110741));
			this.int_3 = info.GetInt32(Class521.smethod_0(110758));
			this.decimal_0 = info.GetDecimal(Class521.smethod_0(110771));
			this.long_0 = Convert.ToInt64(info.GetInt32(Class521.smethod_0(110780)));
			int? num = (int?)info.GetValue(Class521.smethod_0(110789), typeof(int?));
			if (num != null)
			{
				this.nullable_0 = new long?(Convert.ToInt64(num.Value));
			}
			this.nullable_1 = (decimal?)info.GetValue(Class521.smethod_0(110806), typeof(decimal?));
			this.nullable_2 = (decimal?)info.GetValue(Class521.smethod_0(110815), typeof(decimal?));
			this.nullable_3 = (int?)info.GetValue(Class521.smethod_0(110828), typeof(int?));
			this.dateTime_0 = info.GetDateTime(Class521.smethod_0(110849));
			this.nullable_4 = (DateTime?)info.GetValue(Class521.smethod_0(110866), typeof(DateTime?));
			try
			{
				this.string_0 = info.GetString(Class521.smethod_0(110883));
			}
			catch
			{
			}
			try
			{
				this.dateTime_1 = info.GetDateTime(Class521.smethod_0(110892));
				this.nullable_5 = (DateTime?)info.GetValue(Class521.smethod_0(110909), typeof(DateTime?));
			}
			catch
			{
			}
		}

		// Token: 0x06002628 RID: 9768 RVA: 0x00104B64 File Offset: 0x00102D64
		public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(110723), this.int_0);
			info.AddValue(Class521.smethod_0(110728), this.int_1);
			info.AddValue(Class521.smethod_0(110741), this.int_2);
			info.AddValue(Class521.smethod_0(110758), this.int_3);
			info.AddValue(Class521.smethod_0(110771), this.decimal_0);
			int value;
			if (this.long_0 > 2147483647L)
			{
				value = int.MaxValue;
			}
			else
			{
				value = Convert.ToInt32(this.long_0);
			}
			info.AddValue(Class521.smethod_0(110780), value);
			long? num = this.nullable_0;
			int value2;
			if (num.GetValueOrDefault() > 2147483647L & num != null)
			{
				value2 = int.MaxValue;
			}
			else
			{
				value2 = Convert.ToInt32(this.nullable_0);
			}
			info.AddValue(Class521.smethod_0(110789), value2);
			info.AddValue(Class521.smethod_0(110806), this.nullable_1);
			info.AddValue(Class521.smethod_0(110815), this.nullable_2);
			info.AddValue(Class521.smethod_0(110828), this.nullable_3);
			info.AddValue(Class521.smethod_0(110883), this.string_0);
			info.AddValue(Class521.smethod_0(110849), this.dateTime_0);
			info.AddValue(Class521.smethod_0(110866), this.nullable_4);
			info.AddValue(Class521.smethod_0(110892), this.dateTime_1);
			info.AddValue(Class521.smethod_0(110909), this.nullable_5);
		}

		// Token: 0x17000671 RID: 1649
		// (get) Token: 0x06002629 RID: 9769 RVA: 0x00104D38 File Offset: 0x00102F38
		// (set) Token: 0x0600262A RID: 9770 RVA: 0x0000E94A File Offset: 0x0000CB4A
		[Browsable(false)]
		public int ID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				if (this.int_0 != value)
				{
					this.int_0 = value;
				}
			}
		}

		// Token: 0x17000672 RID: 1650
		// (get) Token: 0x0600262B RID: 9771 RVA: 0x00104D50 File Offset: 0x00102F50
		// (set) Token: 0x0600262C RID: 9772 RVA: 0x0000E95E File Offset: 0x0000CB5E
		[Browsable(false)]
		public int AcctID
		{
			get
			{
				return this.int_1;
			}
			set
			{
				if (this.int_1 != value)
				{
					this.int_1 = value;
				}
			}
		}

		// Token: 0x17000673 RID: 1651
		// (get) Token: 0x0600262D RID: 9773 RVA: 0x00104D68 File Offset: 0x00102F68
		// (set) Token: 0x0600262E RID: 9774 RVA: 0x0000E972 File Offset: 0x0000CB72
		[Browsable(false)]
		public int TransType
		{
			get
			{
				return this.int_2;
			}
			set
			{
				if (this.int_2 != value)
				{
					this.int_2 = value;
				}
			}
		}

		// Token: 0x17000674 RID: 1652
		// (get) Token: 0x0600262F RID: 9775 RVA: 0x00104D80 File Offset: 0x00102F80
		// (set) Token: 0x06002630 RID: 9776 RVA: 0x0000E986 File Offset: 0x0000CB86
		[Browsable(false)]
		public int SymbolID
		{
			get
			{
				return this.int_3;
			}
			set
			{
				if (this.int_3 != value)
				{
					this.int_3 = value;
				}
			}
		}

		// Token: 0x17000675 RID: 1653
		// (get) Token: 0x06002631 RID: 9777 RVA: 0x00104D98 File Offset: 0x00102F98
		// (set) Token: 0x06002632 RID: 9778 RVA: 0x0000E99A File Offset: 0x0000CB9A
		[DisplayName("数量")]
		public long Units
		{
			get
			{
				return this.long_0;
			}
			set
			{
				if (this.long_0 != value)
				{
					this.long_0 = value;
				}
			}
		}

		// Token: 0x17000676 RID: 1654
		// (get) Token: 0x06002633 RID: 9779 RVA: 0x00104DB0 File Offset: 0x00102FB0
		// (set) Token: 0x06002634 RID: 9780 RVA: 0x0000E9AE File Offset: 0x0000CBAE
		[DisplayName("开仓均价")]
		public decimal Price
		{
			get
			{
				return this.decimal_0;
			}
			set
			{
				if (this.decimal_0 != value)
				{
					this.decimal_0 = value;
				}
			}
		}

		// Token: 0x17000677 RID: 1655
		// (get) Token: 0x06002635 RID: 9781 RVA: 0x00104DC8 File Offset: 0x00102FC8
		// (set) Token: 0x06002636 RID: 9782 RVA: 0x00104DE0 File Offset: 0x00102FE0
		[DisplayName("持仓")]
		public long? OpenUnits
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				long? num = this.nullable_0;
				long? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_0 = value;
				}
			}
		}

		// Token: 0x17000678 RID: 1656
		// (get) Token: 0x06002637 RID: 9783 RVA: 0x00104E24 File Offset: 0x00103024
		// (set) Token: 0x06002638 RID: 9784 RVA: 0x00104E3C File Offset: 0x0010303C
		[DisplayName("手续费")]
		public decimal? Fee
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				decimal? num = this.nullable_1;
				decimal? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_1 = value;
				}
			}
		}

		// Token: 0x17000679 RID: 1657
		// (get) Token: 0x06002639 RID: 9785 RVA: 0x00104E84 File Offset: 0x00103084
		// (set) Token: 0x0600263A RID: 9786 RVA: 0x00104E9C File Offset: 0x0010309C
		[DisplayName("盈利")]
		public decimal? Profit
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				decimal? num = this.nullable_2;
				decimal? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_2 = value;
				}
			}
		}

		// Token: 0x1700067A RID: 1658
		// (get) Token: 0x0600263B RID: 9787 RVA: 0x00104EE4 File Offset: 0x001030E4
		// (set) Token: 0x0600263C RID: 9788 RVA: 0x00104EFC File Offset: 0x001030FC
		[Browsable(false)]
		public int? ClosedTransID
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				int? num = this.nullable_3;
				int? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_3 = value;
				}
			}
		}

		// Token: 0x1700067B RID: 1659
		// (get) Token: 0x0600263D RID: 9789 RVA: 0x00104F40 File Offset: 0x00103140
		// (set) Token: 0x0600263E RID: 9790 RVA: 0x0000E9C7 File Offset: 0x0000CBC7
		[DisplayName("成交时间")]
		public DateTime CreateTime
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				if (this.dateTime_0 != value)
				{
					this.dateTime_0 = value;
				}
			}
		}

		// Token: 0x1700067C RID: 1660
		// (get) Token: 0x0600263F RID: 9791 RVA: 0x00104F58 File Offset: 0x00103158
		// (set) Token: 0x06002640 RID: 9792 RVA: 0x00104F70 File Offset: 0x00103170
		[DisplayName("更新时间")]
		public DateTime? UpdateTime
		{
			get
			{
				return this.nullable_4;
			}
			set
			{
				if (this.nullable_4 != value)
				{
					this.nullable_4 = value;
				}
			}
		}

		// Token: 0x1700067D RID: 1661
		// (get) Token: 0x06002641 RID: 9793 RVA: 0x00104FC4 File Offset: 0x001031C4
		// (set) Token: 0x06002642 RID: 9794 RVA: 0x0000E9E0 File Offset: 0x0000CBE0
		[Browsable(false)]
		public DateTime CreateTimeN
		{
			get
			{
				return this.dateTime_1;
			}
			set
			{
				if (this.dateTime_1 != value)
				{
					this.dateTime_1 = value;
				}
			}
		}

		// Token: 0x1700067E RID: 1662
		// (get) Token: 0x06002643 RID: 9795 RVA: 0x00104FDC File Offset: 0x001031DC
		// (set) Token: 0x06002644 RID: 9796 RVA: 0x00104FF4 File Offset: 0x001031F4
		[Browsable(false)]
		public DateTime? UpdateTimeN
		{
			get
			{
				return this.nullable_5;
			}
			set
			{
				if (this.nullable_5 != value)
				{
					this.nullable_5 = value;
				}
			}
		}

		// Token: 0x1700067F RID: 1663
		// (get) Token: 0x06002645 RID: 9797 RVA: 0x00105048 File Offset: 0x00103248
		// (set) Token: 0x06002646 RID: 9798 RVA: 0x0000E9F9 File Offset: 0x0000CBF9
		[DisplayName("注释")]
		public string Notes
		{
			get
			{
				return this.string_0;
			}
			set
			{
				if (this.string_0 != value)
				{
					this.string_0 = value;
				}
			}
		}

		// Token: 0x17000680 RID: 1664
		// (get) Token: 0x06002647 RID: 9799 RVA: 0x00105060 File Offset: 0x00103260
		[Browsable(false)]
		public bool IsOpen
		{
			get
			{
				bool result;
				if (this.TransType != 1)
				{
					result = (this.TransType == 3);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x04001263 RID: 4707
		private int int_0;

		// Token: 0x04001264 RID: 4708
		private int int_1;

		// Token: 0x04001265 RID: 4709
		private int int_2;

		// Token: 0x04001266 RID: 4710
		private int int_3;

		// Token: 0x04001267 RID: 4711
		private long long_0;

		// Token: 0x04001268 RID: 4712
		private decimal decimal_0;

		// Token: 0x04001269 RID: 4713
		private long? nullable_0;

		// Token: 0x0400126A RID: 4714
		private decimal? nullable_1;

		// Token: 0x0400126B RID: 4715
		private decimal? nullable_2;

		// Token: 0x0400126C RID: 4716
		private int? nullable_3;

		// Token: 0x0400126D RID: 4717
		private DateTime dateTime_0;

		// Token: 0x0400126E RID: 4718
		private DateTime? nullable_4;

		// Token: 0x0400126F RID: 4719
		private DateTime dateTime_1;

		// Token: 0x04001270 RID: 4720
		private DateTime? nullable_5;

		// Token: 0x04001271 RID: 4721
		private string string_0;
	}
}
