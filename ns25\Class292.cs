﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns4;
using TEx;

namespace ns25
{
	// Token: 0x02000211 RID: 529
	[DesignerCategory("Code")]
	internal abstract class Class292 : DataGridView
	{
		// Token: 0x060015A6 RID: 5542 RVA: 0x00008AFF File Offset: 0x00006CFF
		public Class292()
		{
			this.vmethod_2();
			this.vmethod_0();
			this.vmethod_1();
			base.CellFormatting += this.Class292_CellFormatting;
			Base.UI.ChartThemeChanged += this.method_1;
		}

		// Token: 0x060015A7 RID: 5543
		protected abstract void vmethod_0();

		// Token: 0x060015A8 RID: 5544
		protected abstract void vmethod_1();

		// Token: 0x060015A9 RID: 5545 RVA: 0x00094218 File Offset: 0x00092418
		protected virtual void vmethod_2()
		{
			this.vmethod_4();
			base.RowHeadersVisible = false;
			base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
			base.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			base.CellBorderStyle = DataGridViewCellBorderStyle.None;
			base.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.DefaultCellStyle.Font = new Font(Class521.smethod_0(24023), TApp.smethod_4(8.5f, false), FontStyle.Regular);
			base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			base.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			base.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.ColumnHeadersDefaultCellStyle.Font = new Font(Class521.smethod_0(7183), TApp.smethod_4(8.8f, false), FontStyle.Regular);
			base.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			base.AllowUserToAddRows = false;
			base.AllowUserToDeleteRows = false;
			base.AllowUserToOrderColumns = true;
			base.BorderStyle = BorderStyle.None;
			base.ReadOnly = true;
			this.Dock = DockStyle.Fill;
			base.MultiSelect = false;
			base.AllowUserToResizeRows = false;
		}

		// Token: 0x060015AA RID: 5546 RVA: 0x00008B3E File Offset: 0x00006D3E
		private void Class292_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			this.method_0(e);
			this.vmethod_3(e);
		}

		// Token: 0x060015AB RID: 5547 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
		}

		// Token: 0x060015AC RID: 5548 RVA: 0x00094308 File Offset: 0x00092508
		public void method_0(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			lock (dataGridViewCellFormattingEventArgs_0)
			{
				DataGridViewCell dataGridViewCell = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex].Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
				if (dataGridViewCellFormattingEventArgs_0.CellStyle != null)
				{
					dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = dataGridViewCell.Style.ForeColor;
				}
			}
		}

		// Token: 0x060015AD RID: 5549 RVA: 0x00008B50 File Offset: 0x00006D50
		private void method_1(object sender, EventArgs e)
		{
			this.vmethod_4();
			this.vmethod_0();
		}

		// Token: 0x060015AE RID: 5550 RVA: 0x00094378 File Offset: 0x00092578
		protected virtual void vmethod_4()
		{
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				base.BackgroundColor = Class181.color_3;
				base.RowsDefaultCellStyle.ForeColor = Class181.color_1;
				base.RowsDefaultCellStyle.BackColor = Class181.color_10;
				base.AlternatingRowsDefaultCellStyle.BackColor = Class181.color_9;
				base.DefaultCellStyle.SelectionBackColor = Class181.color_8;
			}
			else
			{
				base.BackgroundColor = Color.FromKnownColor(KnownColor.Control);
				base.RowsDefaultCellStyle.BackColor = Color.White;
				base.AlternatingRowsDefaultCellStyle.BackColor = Color.FloralWhite;
				base.DefaultCellStyle.SelectionBackColor = Color.Moccasin;
			}
		}

		// Token: 0x060015AF RID: 5551 RVA: 0x0009441C File Offset: 0x0009261C
		protected void method_2(DataGridViewCell dataGridViewCell_0)
		{
			if (dataGridViewCell_0.Value != null)
			{
				string text = (string)dataGridViewCell_0.Value;
				if (text.Contains(Class521.smethod_0(18676)) & dataGridViewCell_0.Style.ForeColor != Color.Red)
				{
					dataGridViewCell_0.Style.ForeColor = Color.Red;
				}
				else if (text.Contains(Class521.smethod_0(18671)) & dataGridViewCell_0.Style.ForeColor != Color.Green)
				{
					dataGridViewCell_0.Style.ForeColor = Color.Green;
				}
			}
		}

		// Token: 0x060015B0 RID: 5552 RVA: 0x000944B4 File Offset: 0x000926B4
		protected void method_3(DataGridViewCell dataGridViewCell_0)
		{
			if (dataGridViewCell_0.Value != null)
			{
				decimal d = (decimal)dataGridViewCell_0.Value;
				Color foreColor = dataGridViewCell_0.Style.ForeColor;
				if (d > 0m & foreColor != Color.Red)
				{
					dataGridViewCell_0.Style.ForeColor = Color.Red;
				}
				else if (d == 0m & foreColor != Color.Black)
				{
					dataGridViewCell_0.Style.ForeColor = Color.Black;
				}
				else if (d < 0m & foreColor != Color.Green)
				{
					dataGridViewCell_0.Style.ForeColor = Color.Green;
				}
			}
		}

		// Token: 0x060015B1 RID: 5553 RVA: 0x00094568 File Offset: 0x00092768
		protected int method_4(decimal decimal_0)
		{
			int result = 0;
			if (decimal_0 != 0m && decimal_0 < 2147483647m && decimal_0 > -2147483648m)
			{
				int num = (int)decimal_0;
				if (num != 0)
				{
					decimal num2 = decimal_0 % num;
					if (num2 != 0m && Math.Abs(num2) >= 0.005m)
					{
						result = 2;
					}
				}
				else if (Math.Abs(decimal_0) >= 0.005m)
				{
					result = 2;
				}
			}
			return result;
		}
	}
}
