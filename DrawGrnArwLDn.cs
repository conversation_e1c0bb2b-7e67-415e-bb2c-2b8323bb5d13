﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns18;
using ns26;

namespace TEx
{
	// Token: 0x02000196 RID: 406
	[Serializable]
	internal sealed class DrawGrnArwLDn : DrawRedArwUp, ISerializable
	{
		// Token: 0x06000FAF RID: 4015 RVA: 0x00006B2F File Offset: 0x00004D2F
		public DrawGrnArwLDn()
		{
		}

		// Token: 0x06000FB0 RID: 4016 RVA: 0x00006BA4 File Offset: 0x00004DA4
		public DrawGrnArwLDn(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(27613);
		}

		// Token: 0x06000FB1 RID: 4017 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawGrnArwLDn(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FB2 RID: 4018 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FB3 RID: 4019 RVA: 0x00064FBC File Offset: 0x000631BC
		protected override Image vmethod_24()
		{
			return Class375.GreenArrow_LDown;
		}
	}
}
