﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using ns18;

namespace ns3
{
	// Token: 0x02000005 RID: 5
	[CompilerGenerated]
	internal sealed class Class3<T, U, V>
	{
		// Token: 0x1700000B RID: 11
		// (get) Token: 0x06000017 RID: 23 RVA: 0x000104E4 File Offset: 0x0000E6E4
		public T type
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x1700000C RID: 12
		// (get) Token: 0x06000018 RID: 24 RVA: 0x000104FC File Offset: 0x0000E6FC
		public U codes
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x1700000D RID: 13
		// (get) Token: 0x06000019 RID: 25 RVA: 0x00010514 File Offset: 0x0000E714
		public V date
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x0600001A RID: 26 RVA: 0x00002ABB File Offset: 0x00000CBB
		[DebuggerHidden]
		public Class3(T gparam_3, U gparam_4, V gparam_5)
		{
			this.gparam_0 = gparam_3;
			this.gparam_1 = gparam_4;
			this.gparam_2 = gparam_5;
		}

		// Token: 0x0600001B RID: 27 RVA: 0x0001052C File Offset: 0x0000E72C
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class3<T, U, V> @class = obj as Class3<T, U, V>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1))
			{
				result = EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600001C RID: 28 RVA: 0x00010594 File Offset: 0x0000E794
		[DebuggerHidden]
		public int GetHashCode()
		{
			return ((562362864 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2);
		}

		// Token: 0x0600001D RID: 29 RVA: 0x000105EC File Offset: 0x0000E7EC
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = Class521.smethod_0(304);
			object[] array = new object[3];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_4B;
				}
			}
			obj = ptr.ToString();
			IL_4B:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_86;
				}
			}
			obj2 = ptr2.ToString();
			IL_86:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C5;
				}
			}
			obj3 = ptr3.ToString();
			IL_C5:
			array[num3] = obj3;
			return string.Format(provider, format, array);
		}

		// Token: 0x0400000B RID: 11
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x0400000C RID: 12
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x0400000D RID: 13
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;
	}
}
