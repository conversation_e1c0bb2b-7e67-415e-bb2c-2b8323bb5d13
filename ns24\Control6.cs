﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns24
{
	// Token: 0x0200023C RID: 572
	internal sealed class Control6 : UserControl
	{
		// Token: 0x0600186D RID: 6253 RVA: 0x0000A048 File Offset: 0x00008248
		public Control6()
		{
		}

		// Token: 0x0600186E RID: 6254 RVA: 0x0000A050 File Offset: 0x00008250
		public Control6(DrawObj drawObj_1, DateTime dateTime_1, double double_1) : this(drawObj_1, dateTime_1, double_1, 1)
		{
		}

		// Token: 0x0600186F RID: 6255 RVA: 0x0000A05C File Offset: 0x0000825C
		public Control6(DrawObj drawObj_1, DateTime dateTime_1, double double_1, int int_1)
		{
			this.method_0();
			this.drawObj_0 = drawObj_1;
			this.XDateTime = dateTime_1;
			this.YValue = double_1;
			this.Index = int_1;
		}

		// Token: 0x17000415 RID: 1045
		// (get) Token: 0x06001870 RID: 6256 RVA: 0x000A89B8 File Offset: 0x000A6BB8
		// (set) Token: 0x06001871 RID: 6257 RVA: 0x0000A089 File Offset: 0x00008289
		public DateTime XDateTime
		{
			get
			{
				DateTime result;
				try
				{
					result = this.dateTimePicker_1.Value;
					result = result.Date;
					result = result.Add(this.dateTimePicker_0.Value.TimeOfDay);
				}
				catch
				{
					result = this.dateTime_0;
				}
				return result;
			}
			set
			{
				this.dateTime_0 = value;
				this.dateTimePicker_1.Value = value;
				this.dateTimePicker_0.Value = value;
			}
		}

		// Token: 0x17000416 RID: 1046
		// (get) Token: 0x06001872 RID: 6258 RVA: 0x000A8A18 File Offset: 0x000A6C18
		// (set) Token: 0x06001873 RID: 6259 RVA: 0x000A8A58 File Offset: 0x000A6C58
		public double YValue
		{
			get
			{
				double result;
				try
				{
					result = Convert.ToDouble(this.textBox_0.Text);
				}
				catch
				{
					result = this.double_0;
				}
				return result;
			}
			set
			{
				this.double_0 = value;
				int num = (this.drawObj_0.Chart.Symbol.DigitNb < 2) ? 2 : this.drawObj_0.Chart.Symbol.DigitNb;
				this.textBox_0.Text = value.ToString(Class521.smethod_0(5141) + num.ToString());
			}
		}

		// Token: 0x17000417 RID: 1047
		// (get) Token: 0x06001874 RID: 6260 RVA: 0x000A8AC8 File Offset: 0x000A6CC8
		// (set) Token: 0x06001875 RID: 6261 RVA: 0x000A8AE0 File Offset: 0x000A6CE0
		public int Index
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
				string str = Class521.smethod_0(64149);
				if (value == 2)
				{
					str = Class521.smethod_0(64154);
				}
				this.groupBox_0.Text = Class521.smethod_0(64159) + str + Class521.smethod_0(24271);
			}
		}

		// Token: 0x06001876 RID: 6262 RVA: 0x0000A0AC File Offset: 0x000082AC
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001877 RID: 6263 RVA: 0x000A8B38 File Offset: 0x000A6D38
		private void method_0()
		{
			this.dateTimePicker_0 = new DateTimePicker();
			this.dateTimePicker_1 = new DateTimePicker();
			this.groupBox_0 = new GroupBox();
			this.textBox_0 = new TextBox();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.groupBox_0.SuspendLayout();
			base.SuspendLayout();
			this.dateTimePicker_0.Format = DateTimePickerFormat.Time;
			this.dateTimePicker_0.Location = new Point(236, 31);
			this.dateTimePicker_0.Name = Class521.smethod_0(64168);
			this.dateTimePicker_0.ShowUpDown = true;
			this.dateTimePicker_0.Size = new Size(113, 25);
			this.dateTimePicker_0.TabIndex = 1;
			this.dateTimePicker_1.Location = new Point(71, 31);
			this.dateTimePicker_1.Name = Class521.smethod_0(64189);
			this.dateTimePicker_1.Size = new Size(151, 25);
			this.dateTimePicker_1.TabIndex = 2;
			this.groupBox_0.Controls.Add(this.textBox_0);
			this.groupBox_0.Controls.Add(this.label_0);
			this.groupBox_0.Controls.Add(this.label_1);
			this.groupBox_0.Controls.Add(this.dateTimePicker_0);
			this.groupBox_0.Controls.Add(this.dateTimePicker_1);
			this.groupBox_0.Location = new Point(3, 3);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(370, 120);
			this.groupBox_0.TabIndex = 3;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(64210);
			this.textBox_0.Location = new Point(71, 75);
			this.textBox_0.Name = Class521.smethod_0(64231);
			this.textBox_0.Size = new Size(151, 25);
			this.textBox_0.TabIndex = 5;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(17, 78);
			this.label_0.Name = Class521.smethod_0(5827);
			this.label_0.Size = new Size(45, 15);
			this.label_0.TabIndex = 4;
			this.label_0.Text = Class521.smethod_0(64248);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(17, 35);
			this.label_1.Name = Class521.smethod_0(5871);
			this.label_1.Size = new Size(45, 15);
			this.label_1.TabIndex = 3;
			this.label_1.Text = Class521.smethod_0(64261);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.groupBox_0);
			base.Name = Class521.smethod_0(64274);
			base.Size = new Size(376, 126);
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000C38 RID: 3128
		private DateTime dateTime_0;

		// Token: 0x04000C39 RID: 3129
		private double double_0;

		// Token: 0x04000C3A RID: 3130
		private int int_0;

		// Token: 0x04000C3B RID: 3131
		private DrawObj drawObj_0;

		// Token: 0x04000C3C RID: 3132
		private IContainer icontainer_0;

		// Token: 0x04000C3D RID: 3133
		private DateTimePicker dateTimePicker_0;

		// Token: 0x04000C3E RID: 3134
		private DateTimePicker dateTimePicker_1;

		// Token: 0x04000C3F RID: 3135
		private GroupBox groupBox_0;

		// Token: 0x04000C40 RID: 3136
		private TextBox textBox_0;

		// Token: 0x04000C41 RID: 3137
		private Label label_0;

		// Token: 0x04000C42 RID: 3138
		private Label label_1;
	}
}
