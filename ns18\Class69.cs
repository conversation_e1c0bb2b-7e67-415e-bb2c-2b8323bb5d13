﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using TEx;
using TEx.Trading;

namespace ns18
{
	// Token: 0x020000AE RID: 174
	internal sealed class Class69 : TOdrLine
	{
		// Token: 0x0600072E RID: 1838 RVA: 0x00004EEF File Offset: 0x000030EF
		public Class69(ChartCS chartCS_1, Order order_1) : base(chartCS_1, order_1.Price)
		{
			this.order_0 = order_1;
			this.vmethod_1();
			base.method_0();
		}

		// Token: 0x0600072F RID: 1839 RVA: 0x00004F13 File Offset: 0x00003113
		public override void vmethod_1()
		{
			base.Text = this.method_8();
			if (base.TextBox != null)
			{
				base.TextBox.Text = base.Text;
			}
		}

		// Token: 0x06000730 RID: 1840 RVA: 0x0002EE38 File Offset: 0x0002D038
		private string method_8()
		{
			return Base.Trading.smethod_35((OrderType)this.Order.OrderType) + this.method_10() + this.Order.Price;
		}

		// Token: 0x06000731 RID: 1841 RVA: 0x0002EE74 File Offset: 0x0002D074
		private string method_9()
		{
			return string.Concat(new string[]
			{
				Base.Trading.smethod_35((OrderType)this.Order.OrderType),
				this.method_10(),
				Class521.smethod_0(11744),
				base.method_5(base.Price),
				Class521.smethod_0(5046)
			});
		}

		// Token: 0x06000732 RID: 1842 RVA: 0x0002EED8 File Offset: 0x0002D0D8
		private string method_10()
		{
			return this.Order.Units.ToString() + (base.Chart.Symbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734));
		}

		// Token: 0x06000733 RID: 1843 RVA: 0x0002EF2C File Offset: 0x0002D12C
		protected override Color vmethod_7()
		{
			Color result;
			if (this.IsLong)
			{
				result = Color.Red;
			}
			else if (this.IsShort)
			{
				result = Color.Green;
			}
			else
			{
				result = default(Color);
			}
			return result;
		}

		// Token: 0x06000734 RID: 1844 RVA: 0x0002D1CC File Offset: 0x0002B3CC
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Dot;
		}

		// Token: 0x06000735 RID: 1845 RVA: 0x0002EF68 File Offset: 0x0002D168
		public override void vmethod_4(double double_0)
		{
			decimal num = base.Chart.SymbDataSet.method_69(double_0);
			Base.Trading.smethod_64(this.order_0.ID, num, this.order_0.Units, false);
			base.Price = num;
			base.vmethod_4(Convert.ToDouble(num));
		}

		// Token: 0x06000736 RID: 1846 RVA: 0x0002EFBC File Offset: 0x0002D1BC
		protected override double vmethod_3()
		{
			return Convert.ToDouble(this.order_0.Price);
		}

		// Token: 0x170001AB RID: 427
		// (get) Token: 0x06000737 RID: 1847 RVA: 0x0002EFE0 File Offset: 0x0002D1E0
		// (set) Token: 0x06000738 RID: 1848 RVA: 0x00004F3C File Offset: 0x0000313C
		public Order Order
		{
			get
			{
				return this.order_0;
			}
			set
			{
				this.order_0 = value;
			}
		}

		// Token: 0x170001AC RID: 428
		// (get) Token: 0x06000739 RID: 1849 RVA: 0x0002EFF8 File Offset: 0x0002D1F8
		public bool IsLong
		{
			get
			{
				bool result;
				if (this.Order.OrderType != 0)
				{
					result = (this.Order.OrderType == 3);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170001AD RID: 429
		// (get) Token: 0x0600073A RID: 1850 RVA: 0x0002F028 File Offset: 0x0002D228
		public bool IsShort
		{
			get
			{
				bool result;
				if (this.Order.OrderType != 2)
				{
					result = (this.Order.OrderType == 1);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170001AE RID: 430
		// (get) Token: 0x0600073B RID: 1851 RVA: 0x0002F05C File Offset: 0x0002D25C
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.Order.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.Order.SymbolID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170001AF RID: 431
		// (get) Token: 0x0600073C RID: 1852 RVA: 0x0002F0A8 File Offset: 0x0002D2A8
		// (set) Token: 0x0600073D RID: 1853 RVA: 0x0002F0F8 File Offset: 0x0002D2F8
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (this.IsLong)
				{
					result = (base.Line.Line.Color != Color.Red);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Green);
				}
				return result;
			}
			set
			{
				Color color;
				if (this.IsLong)
				{
					color = (value ? Color.Coral : Color.Red);
				}
				else
				{
					color = (value ? Color.LimeGreen : Color.Green);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (value)
				{
					base.TextBox.Text = this.method_9();
				}
				else
				{
					base.TextBox.Text = this.method_8();
				}
			}
		}

		// Token: 0x0400032A RID: 810
		private Order order_0;
	}
}
