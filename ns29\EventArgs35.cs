﻿using System;
using System.IO;
using ns1;

namespace ns29
{
	// Token: 0x020003FC RID: 1020
	internal sealed class EventArgs35 : EventArgs
	{
		// Token: 0x170006D0 RID: 1744
		// (get) Token: 0x060027B1 RID: 10161 RVA: 0x0000F434 File Offset: 0x0000D634
		public Exception Exception
		{
			get
			{
				return this.exception_0;
			}
		}

		// Token: 0x170006D1 RID: 1745
		// (get) Token: 0x060027B2 RID: 10162 RVA: 0x0000F43C File Offset: 0x0000D63C
		public bool CanDebug
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170006D2 RID: 1746
		// (get) Token: 0x060027B3 RID: 10163 RVA: 0x0000F444 File Offset: 0x0000D644
		public bool CanSendReport
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x170006D3 RID: 1747
		// (get) Token: 0x060027B4 RID: 10164 RVA: 0x0000F44C File Offset: 0x0000D64C
		public bool ShowContinueCheckbox
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x170006D4 RID: 1748
		// (get) Token: 0x060027B5 RID: 10165 RVA: 0x0000F44C File Offset: 0x0000D64C
		[Obsolete("Use ShowContinueCheckbox instead, as this is now also false when the builder has chosen not to show the checkbox.")]
		public bool CanContinue
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x060027B6 RID: 10166 RVA: 0x0000F454 File Offset: 0x0000D654
		internal void method_0(bool bool_4)
		{
			this.bool_2 = bool_4;
		}

		// Token: 0x060027B7 RID: 10167 RVA: 0x0000F45D File Offset: 0x0000D65D
		internal void method_1()
		{
			this.bool_0 = true;
		}

		// Token: 0x060027B8 RID: 10168 RVA: 0x0000F466 File Offset: 0x0000D666
		internal void method_2()
		{
			this.bool_1 = false;
		}

		// Token: 0x170006D5 RID: 1749
		// (get) Token: 0x060027B9 RID: 10169 RVA: 0x0000F46F File Offset: 0x0000D66F
		// (set) Token: 0x060027BA RID: 10170 RVA: 0x0000F477 File Offset: 0x0000D677
		public bool TryToContinue
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x060027BB RID: 10171 RVA: 0x0000F480 File Offset: 0x0000D680
		public void method_3()
		{
			if (this.bool_0)
			{
				this.class536_0.method_22();
			}
		}

		// Token: 0x060027BC RID: 10172 RVA: 0x0000F495 File Offset: 0x0000D695
		public bool method_4(string string_0)
		{
			if (File.Exists(string_0))
			{
				File.Delete(string_0);
			}
			return this.class536_0.method_23(string_0);
		}

		// Token: 0x060027BD RID: 10173 RVA: 0x0000F4B1 File Offset: 0x0000D6B1
		public byte[] method_5()
		{
			return this.class536_0.method_12();
		}

		// Token: 0x060027BE RID: 10174 RVA: 0x0000F4BE File Offset: 0x0000D6BE
		public bool method_6()
		{
			return this.bool_1 && this.class536_0.method_19();
		}

		// Token: 0x060027BF RID: 10175 RVA: 0x0000F4D5 File Offset: 0x0000D6D5
		public void method_7(string string_0, string string_1)
		{
			this.class536_0.method_17(string_0, string_1);
		}

		// Token: 0x060027C0 RID: 10176 RVA: 0x0000F4E4 File Offset: 0x0000D6E4
		public void method_8(string string_0, string string_1)
		{
			this.class536_0.method_18(string_0, string_1);
		}

		// Token: 0x060027C1 RID: 10177 RVA: 0x0000F4F3 File Offset: 0x0000D6F3
		internal EventArgs35(Class536 class536_1, Exception exception_1)
		{
			this.class536_0 = class536_1;
			this.exception_0 = exception_1;
		}

		// Token: 0x040013BA RID: 5050
		private Class536 class536_0;

		// Token: 0x040013BB RID: 5051
		private Exception exception_0;

		// Token: 0x040013BC RID: 5052
		private bool bool_0;

		// Token: 0x040013BD RID: 5053
		private bool bool_1 = true;

		// Token: 0x040013BE RID: 5054
		private bool bool_2 = true;

		// Token: 0x040013BF RID: 5055
		private bool bool_3;
	}
}
