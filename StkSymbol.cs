﻿using System;
using System.Runtime.CompilerServices;
using ns18;
using ns26;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001FE RID: 510
	public sealed class StkSymbol : TradingSymbol
	{
		// Token: 0x06001479 RID: 5241 RVA: 0x0000837D File Offset: 0x0000657D
		public StkSymbol()
		{
		}

		// Token: 0x0600147A RID: 5242 RVA: 0x00008385 File Offset: 0x00006585
		public StkSymbol(TradingSymbol mstSymbl) : this()
		{
			this.tradingSymbol_0 = mstSymbl;
		}

		// Token: 0x17000343 RID: 835
		// (get) Token: 0x0600147B RID: 5243 RVA: 0x0008A7D0 File Offset: 0x000889D0
		// (set) Token: 0x0600147C RID: 5244 RVA: 0x00008396 File Offset: 0x00006596
		public TradingSymbol MstSymbol
		{
			get
			{
				return this.tradingSymbol_0;
			}
			set
			{
				this.tradingSymbol_0 = value;
			}
		}

		// Token: 0x0600147D RID: 5245 RVA: 0x0008A7E8 File Offset: 0x000889E8
		private int method_0(string string_3)
		{
			int num = 0;
			for (int i = 0; i < string_3.Length; i++)
			{
				if (char.IsDigit(string_3[i]))
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x17000344 RID: 836
		// (get) Token: 0x0600147E RID: 5246 RVA: 0x0008A824 File Offset: 0x00088A24
		public string AbbrCode
		{
			get
			{
				string text = base.Code.Trim();
				int num = 0;
				string text2 = text;
				int num2 = 0;
				while (num2 < text2.Length && !char.IsDigit(text2[num2]))
				{
					num++;
					num2++;
				}
				return text.Substring(0, num);
			}
		}

		// Token: 0x17000345 RID: 837
		// (get) Token: 0x0600147F RID: 5247 RVA: 0x0008A874 File Offset: 0x00088A74
		public int DigitNb
		{
			get
			{
				int result;
				if (this.MstSymbol != null)
				{
					result = this.MstSymbol.DigitNb;
				}
				else
				{
					result = 0;
				}
				return result;
			}
		}

		// Token: 0x17000346 RID: 838
		// (get) Token: 0x06001480 RID: 5248 RVA: 0x0008A89C File Offset: 0x00088A9C
		public FeeType FeeType
		{
			get
			{
				FeeType result;
				if (this.MstSymbol != null)
				{
					result = this.MstSymbol.FeeType;
				}
				else
				{
					result = FeeType.PercentageOfValue;
				}
				return result;
			}
		}

		// Token: 0x17000347 RID: 839
		// (get) Token: 0x06001481 RID: 5249 RVA: 0x0008A8C4 File Offset: 0x00088AC4
		public int? TonsPerUnit
		{
			get
			{
				int? result;
				if (this.MstSymbol != null)
				{
					result = this.MstSymbol.TonsPerUnit;
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000348 RID: 840
		// (get) Token: 0x06001482 RID: 5250 RVA: 0x0008A8F4 File Offset: 0x00088AF4
		public decimal? LeastPriceVar
		{
			get
			{
				decimal? result;
				if (this.MstSymbol != null)
				{
					result = this.MstSymbol.LeastPriceVar;
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000349 RID: 841
		// (get) Token: 0x06001483 RID: 5251 RVA: 0x0008A924 File Offset: 0x00088B24
		public bool? IsOneSideFee
		{
			get
			{
				bool? result;
				if (this.MstSymbol != null)
				{
					result = this.MstSymbol.IsOneSideFee;
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x1700034A RID: 842
		// (get) Token: 0x06001484 RID: 5252 RVA: 0x0008A954 File Offset: 0x00088B54
		public decimal? MarginRate
		{
			get
			{
				return this.MstSymbol.MarginRate;
			}
		}

		// Token: 0x1700034B RID: 843
		// (get) Token: 0x06001485 RID: 5253 RVA: 0x0008A970 File Offset: 0x00088B70
		public decimal? AvgSlipg
		{
			get
			{
				return this.MstSymbol.AvgSlipg;
			}
		}

		// Token: 0x1700034C RID: 844
		// (get) Token: 0x06001486 RID: 5254 RVA: 0x0008A98C File Offset: 0x00088B8C
		public decimal? FeePerUnit
		{
			get
			{
				return this.MstSymbol.FeePerUnit;
			}
		}

		// Token: 0x1700034D RID: 845
		// (get) Token: 0x06001487 RID: 5255 RVA: 0x0008A9A8 File Offset: 0x00088BA8
		public decimal? FeeRate
		{
			get
			{
				return this.MstSymbol.FeeRate;
			}
		}

		// Token: 0x1700034E RID: 846
		// (get) Token: 0x06001488 RID: 5256 RVA: 0x0008A9C4 File Offset: 0x00088BC4
		// (set) Token: 0x06001489 RID: 5257 RVA: 0x000083A1 File Offset: 0x000065A1
		public decimal? AutoLimitTakePoints
		{
			get
			{
				decimal? result;
				if (this._AutoLimitTakePoints != null)
				{
					result = new decimal?(this._AutoLimitTakePoints.Value);
				}
				else
				{
					result = this.MstSymbol.AutoLimitTakePoints;
				}
				return result;
			}
			set
			{
				this._AutoLimitTakePoints = value;
			}
		}

		// Token: 0x1700034F RID: 847
		// (get) Token: 0x0600148A RID: 5258 RVA: 0x0008AA00 File Offset: 0x00088C00
		// (set) Token: 0x0600148B RID: 5259 RVA: 0x000083AC File Offset: 0x000065AC
		public decimal? AutoStopLossPoints
		{
			get
			{
				decimal? result;
				if (this._AutoStopLossPoints != null)
				{
					result = new decimal?(this._AutoStopLossPoints.Value);
				}
				else
				{
					result = this.MstSymbol.AutoStopLossPoints;
				}
				return result;
			}
			set
			{
				this._AutoStopLossPoints = value;
			}
		}

		// Token: 0x17000350 RID: 848
		// (get) Token: 0x0600148C RID: 5260 RVA: 0x0008AA3C File Offset: 0x00088C3C
		// (set) Token: 0x0600148D RID: 5261 RVA: 0x000083B7 File Offset: 0x000065B7
		public int? DefaultUnits
		{
			get
			{
				int? result;
				if (this._DefaultUnits != null)
				{
					result = new int?(this._DefaultUnits.Value);
				}
				else
				{
					result = this.MstSymbol.DefaultUnits;
				}
				return result;
			}
			set
			{
				this._DefaultUnits = value;
			}
		}

		// Token: 0x17000351 RID: 849
		// (get) Token: 0x0600148E RID: 5262 RVA: 0x0008AA78 File Offset: 0x00088C78
		public string MIStkCode
		{
			get
			{
				string result;
				if (base.IsFutures)
				{
					string abbrCode = this.AbbrCode;
					if (this.method_0(base.Code) == 4)
					{
						result = abbrCode + Class521.smethod_0(50231);
					}
					else
					{
						result = abbrCode + Class521.smethod_0(36100);
					}
				}
				else
				{
					result = base.Code;
				}
				return result;
			}
		}

		// Token: 0x17000352 RID: 850
		// (get) Token: 0x0600148F RID: 5263 RVA: 0x0008AAD4 File Offset: 0x00088CD4
		public bool IsFuturesMI
		{
			get
			{
				bool result;
				if (base.IsFutures)
				{
					result = base.Code.EndsWith(Class521.smethod_0(36100));
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000353 RID: 851
		// (get) Token: 0x06001490 RID: 5264 RVA: 0x0008AB08 File Offset: 0x00088D08
		public bool IsStandAloneSymbol
		{
			get
			{
				bool result;
				if (base.IsFutures)
				{
					result = this.IsFuturesMI;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000354 RID: 852
		// (get) Token: 0x06001491 RID: 5265 RVA: 0x0008AB2C File Offset: 0x00088D2C
		public bool IsStockCompany
		{
			get
			{
				bool result;
				if (this.ExchangeID == 5 && base.Code.StartsWith(Class521.smethod_0(12199)))
				{
					result = true;
				}
				else if (this.ExchangeID == 6)
				{
					if (!base.Code.StartsWith(Class521.smethod_0(36100)))
					{
						result = base.Code.StartsWith(Class521.smethod_0(50240));
					}
					else
					{
						result = true;
					}
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000355 RID: 853
		// (get) Token: 0x06001492 RID: 5266 RVA: 0x0008ABA0 File Offset: 0x00088DA0
		public bool IsConvertableBond
		{
			get
			{
				bool result;
				if (this.ExchangeID == 5 && base.Code.StartsWith(Class521.smethod_0(50245)))
				{
					result = true;
				}
				else if (this.ExchangeID == 6)
				{
					result = base.Code.StartsWith(Class521.smethod_0(50250));
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000356 RID: 854
		// (get) Token: 0x06001493 RID: 5267 RVA: 0x0008ABF8 File Offset: 0x00088DF8
		public bool IsT0Bond
		{
			get
			{
				bool result;
				if (this.ExchangeID == 5 && (base.Code == Class521.smethod_0(50255) || base.Code == Class521.smethod_0(50264) || base.Code == Class521.smethod_0(50273) || base.Code == Class521.smethod_0(50282) || base.Code == Class521.smethod_0(50291)))
				{
					result = true;
				}
				else if ((this.ExchangeID != 6 || !(base.Code == Class521.smethod_0(50300))) && !(base.Code == Class521.smethod_0(50309)))
				{
					result = (base.Code == Class521.smethod_0(50318));
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x06001494 RID: 5268 RVA: 0x0008ACDC File Offset: 0x00088EDC
		public string method_1()
		{
			string result = string.Empty;
			try
			{
				result = TExRoutine.GetCodeForFnDataApi(base.Code, this.ExchangeID);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x17000357 RID: 855
		// (get) Token: 0x06001495 RID: 5269 RVA: 0x0008AD20 File Offset: 0x00088F20
		// (set) Token: 0x06001496 RID: 5270 RVA: 0x000083C2 File Offset: 0x000065C2
		public string IdxClassLv1 { get; set; }

		// Token: 0x17000358 RID: 856
		// (get) Token: 0x06001497 RID: 5271 RVA: 0x0008AD38 File Offset: 0x00088F38
		// (set) Token: 0x06001498 RID: 5272 RVA: 0x000083CD File Offset: 0x000065CD
		public string IdxClassLv2 { get; set; }

		// Token: 0x17000359 RID: 857
		// (get) Token: 0x06001499 RID: 5273 RVA: 0x0008AD50 File Offset: 0x00088F50
		// (set) Token: 0x0600149A RID: 5274 RVA: 0x000083D8 File Offset: 0x000065D8
		public string IdxClassLv3 { get; set; }

		// Token: 0x04000AA3 RID: 2723
		private TradingSymbol tradingSymbol_0;

		// Token: 0x04000AA4 RID: 2724
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000AA5 RID: 2725
		[CompilerGenerated]
		private string string_1;

		// Token: 0x04000AA6 RID: 2726
		[CompilerGenerated]
		private string string_2;
	}
}
