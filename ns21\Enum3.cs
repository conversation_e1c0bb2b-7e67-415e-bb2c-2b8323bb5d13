﻿using System;

namespace ns21
{
	// Token: 0x0200007F RID: 127
	internal enum FunctionKeyId
	{
		// Token: 0x0400016E RID: 366
		OpenLong = 200,
		// Token: 0x0400016F RID: 367
		OpenShort,
		// Token: 0x04000170 RID: 368
		CloseLong,
		// Token: 0x04000171 RID: 369
		CloseShort,
		// Token: 0x04000172 RID: 370
		CloseAll,
		// Token: 0x04000173 RID: 371
		CancelAll,
		// Token: 0x04000174 RID: 372
		Function6,
		// Token: 0x04000175 RID: 373
		Function7,
		// Token: 0x04000176 RID: 374
		Function8,
		// Token: 0x04000177 RID: 375
		Function9,
		// Token: 0x04000178 RID: 376
		Chart1 = 300,
		// Token: 0x04000179 RID: 377
		Chart2,
		// Token: 0x0400017A RID: 378
		Chart3,
		// Token: 0x0400017B RID: 379
		Chart4,
		// Token: 0x0400017C RID: 380
		Chart5 = 306,
		// Token: 0x0400017D RID: 381
		Chart6,
		// Token: 0x0400017E RID: 382
		Chart7,
		// Token: 0x0400017F RID: 383
		Chart8,
		// Token: 0x04000180 RID: 384
		Chart9,
		// Token: 0x04000181 RID: 385
		Chart10,
		// Token: 0x04000182 RID: 386
		Chart11,
		// Token: 0x04000183 RID: 387
		Chart12,
		// Token: 0x04000184 RID: 388
		Special1 = 330,
		// Token: 0x04000185 RID: 389
		Special2 = 340,
		// Token: 0x04000186 RID: 390
		Special3 = 350,
		// Token: 0x04000187 RID: 391
		F1 = 250,
		// Token: 0x04000188 RID: 392
		F2,
		// Token: 0x04000189 RID: 393
		F3,
		// Token: 0x0400018A RID: 394
		F4,
		// Token: 0x0400018B RID: 395
		F5,
		// Token: 0x0400018C RID: 396
		F6,
		// Token: 0x0400018D RID: 397
		F7,
		// Token: 0x0400018E RID: 398
		F8,
		// Token: 0x0400018F RID: 399
		F9,
		// Token: 0x04000190 RID: 400
		F10,
		// Token: 0x04000191 RID: 401
		F11,
		// Token: 0x04000192 RID: 402
		F12,
		// Token: 0x04000193 RID: 403
		Ctrl1 = 270,
		// Token: 0x04000194 RID: 404
		Ctrl2,
		// Token: 0x04000195 RID: 405
		Ctrl3
	}
}
