﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns25
{
	// Token: 0x020002D0 RID: 720
	internal sealed partial class Form23 : Form
	{
		// Token: 0x06002038 RID: 8248 RVA: 0x000E5004 File Offset: 0x000E3204
		public Form23()
		{
			this.method_5();
			base.Load += this.Form23_Load;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			Base.UI.smethod_54(this);
			base.StartPosition = FormStartPosition.CenterParent;
		}

		// Token: 0x06002039 RID: 8249 RVA: 0x0000460C File Offset: 0x0000280C
		private void button_0_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x0600203A RID: 8250 RVA: 0x0000D1B2 File Offset: 0x0000B3B2
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.string_0 != null)
			{
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Abort;
			}
			base.Close();
		}

		// Token: 0x0600203B RID: 8251 RVA: 0x000E5094 File Offset: 0x000E3294
		private void Form23_Load(object sender, EventArgs e)
		{
			try
			{
				this.method_0();
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x0600203C RID: 8252 RVA: 0x000E50D8 File Offset: 0x000E32D8
		private void method_0()
		{
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			string[] manifestResourceNames = executingAssembly.GetManifestResourceNames();
			new List<string>();
			foreach (string text in manifestResourceNames)
			{
				if (text.Contains(Class521.smethod_0(95581)))
				{
					string[] array2 = text.Split(new char[]
					{
						'.'
					});
					if (array2.Length == 5)
					{
						Image backgroundImage = Image.FromStream(executingAssembly.GetManifestResourceStream(text));
						PictureBox pictureBox = new PictureBox();
						pictureBox.Width = 25;
						pictureBox.Height = 25;
						pictureBox.BorderStyle = BorderStyle.FixedSingle;
						pictureBox.SizeMode = PictureBoxSizeMode.StretchImage;
						pictureBox.BackgroundImageLayout = ImageLayout.Stretch;
						pictureBox.BackgroundImage = backgroundImage;
						pictureBox.Tag = array2[3];
						int num = int.Parse(array2[3]);
						this.toolTip_0.SetToolTip(pictureBox, Class521.smethod_0(95610) + num);
						pictureBox.Click += this.method_4;
						pictureBox.DoubleClick += this.method_3;
						pictureBox.MouseEnter += this.method_2;
						pictureBox.MouseLeave += this.method_1;
						this.sortedList_0.Add(num, pictureBox);
					}
				}
			}
			for (int j = 0; j < this.sortedList_0.Count; j++)
			{
				int num2 = j / 16;
				int left = j % 16 * 25 + 5;
				int top = num2 * 25 + 30;
				this.groupBox_0.Controls.Add(this.sortedList_0.Values[j]);
				this.sortedList_0.Values[j].Left = left;
				this.sortedList_0.Values[j].Top = top;
			}
		}

		// Token: 0x0600203D RID: 8253 RVA: 0x000E52AC File Offset: 0x000E34AC
		private void method_1(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				pictureBox.BackColor = Color.White;
			}
		}

		// Token: 0x0600203E RID: 8254 RVA: 0x000E52D4 File Offset: 0x000E34D4
		private void method_2(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				pictureBox.BackColor = Color.LightGreen;
			}
		}

		// Token: 0x0600203F RID: 8255 RVA: 0x000E52FC File Offset: 0x000E34FC
		private void method_3(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				this.string_0 = (pictureBox.Tag as string);
			}
			if (this.string_0 != null)
			{
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Abort;
			}
			base.Close();
		}

		// Token: 0x06002040 RID: 8256 RVA: 0x000E5344 File Offset: 0x000E3544
		private void method_4(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			for (int i = 0; i < this.groupBox_0.Controls.Count; i++)
			{
				PictureBox pictureBox2 = this.groupBox_0.Controls[i] as PictureBox;
				if (pictureBox2 != null && pictureBox2.BorderStyle == BorderStyle.Fixed3D)
				{
					pictureBox2.BorderStyle = BorderStyle.FixedSingle;
				}
			}
			if (pictureBox != null)
			{
				this.string_0 = (pictureBox.Tag as string);
				pictureBox.Select();
				pictureBox.BorderStyle = BorderStyle.Fixed3D;
			}
		}

		// Token: 0x06002041 RID: 8257 RVA: 0x0000D1D4 File Offset: 0x0000B3D4
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002042 RID: 8258 RVA: 0x000E53C0 File Offset: 0x000E35C0
		private void method_5()
		{
			this.groupBox_0 = new GroupBox();
			this.label_0 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			base.SuspendLayout();
			this.groupBox_0.Location = new Point(20, 12);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(408, 189);
			this.groupBox_0.TabIndex = 0;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(95619);
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(29, 219);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(131, 15);
			this.label_0.TabIndex = 1;
			this.label_0.Text = Class521.smethod_0(95628);
			this.button_0.Location = new Point(330, 210);
			this.button_0.Name = Class521.smethod_0(95518);
			this.button_0.Size = new Size(98, 32);
			this.button_0.TabIndex = 2;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(219, 210);
			this.button_1.Name = Class521.smethod_0(95505);
			this.button_1.Size = new Size(98, 32);
			this.button_1.TabIndex = 2;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(449, 256);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.groupBox_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(95657);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(95610);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000FCD RID: 4045
		private SortedList<int, PictureBox> sortedList_0 = new SortedList<int, PictureBox>();

		// Token: 0x04000FCE RID: 4046
		private ToolTip toolTip_0 = new ToolTip();

		// Token: 0x04000FCF RID: 4047
		public string string_0 = Class521.smethod_0(1449);

		// Token: 0x04000FD0 RID: 4048
		private IContainer icontainer_0;

		// Token: 0x04000FD1 RID: 4049
		private GroupBox groupBox_0;

		// Token: 0x04000FD2 RID: 4050
		private Label label_0;

		// Token: 0x04000FD3 RID: 4051
		private Button button_0;

		// Token: 0x04000FD4 RID: 4052
		private Button button_1;
	}
}
