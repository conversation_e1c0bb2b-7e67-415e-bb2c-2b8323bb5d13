﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;
using TEx.Comn;

namespace ns20
{
	// Token: 0x020001A7 RID: 423
	internal sealed partial class Form16 : Form
	{
		// Token: 0x06001048 RID: 4168 RVA: 0x00006E90 File Offset: 0x00005090
		public Form16()
		{
			base.AutoScaleMode = AutoScaleMode.None;
			this.method_0();
		}

		// Token: 0x06001049 RID: 4169 RVA: 0x00006EA7 File Offset: 0x000050A7
		public Form16(string string_1) : this()
		{
			this.string_0 = string_1;
		}

		// Token: 0x0600104A RID: 4170 RVA: 0x00006EB8 File Offset: 0x000050B8
		public Form16(LogonNoticeInfo logonNoticeInfo_1) : this()
		{
			this.logonNoticeInfo_0 = logonNoticeInfo_1;
		}

		// Token: 0x0600104B RID: 4171 RVA: 0x0006DAC4 File Offset: 0x0006BCC4
		private void Form16_Load(object sender, EventArgs e)
		{
			float defaultScaledFontSize = TApp.DefaultScaledFontSize;
			if (this.checkBox_0.Font.Size != defaultScaledFontSize)
			{
				this.checkBox_0.Font = new Font(Class521.smethod_0(7183), defaultScaledFontSize);
			}
			if (this.button_0.Font.Size != defaultScaledFontSize)
			{
				this.button_0.Font = new Font(Class521.smethod_0(7183), defaultScaledFontSize);
			}
			if (!string.IsNullOrEmpty(this.string_0))
			{
				this.checkBox_0.Hide();
				this.webBrowser_0.DocumentText = this.string_0;
			}
			else if (this.logonNoticeInfo_0 != null)
			{
				string text = this.logonNoticeInfo_0.HTMLUrl;
				if (string.IsNullOrEmpty(text))
				{
					return;
				}
				if (text.Equals(Class521.smethod_0(37173)))
				{
					return;
				}
				if (!text.StartsWith(Class521.smethod_0(9769)) && !text.StartsWith(Class521.smethod_0(27697)))
				{
					text = Class521.smethod_0(9769) + text;
				}
				try
				{
					this.webBrowser_0.Navigate(new Uri(text));
				}
				catch
				{
					base.Close();
					return;
				}
			}
			this.checkBox_0.Checked = true;
			this.webBrowser_0.Navigating += this.webBrowser_0_Navigating;
		}

		// Token: 0x0600104C RID: 4172 RVA: 0x0006DC20 File Offset: 0x0006BE20
		private void button_0_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfShowSameLogonNoticeNextTime == this.checkBox_0.Checked)
			{
				Base.UI.Form.IfShowSameLogonNoticeNextTime = !this.checkBox_0.Checked;
			}
			if (this.logonNoticeInfo_0 != null)
			{
				Base.UI.Form.LastDisplayedLogonNoticeDT = new DateTime?(this.logonNoticeInfo_0.DateTime);
			}
			Base.UI.smethod_47();
			base.Close();
		}

		// Token: 0x0600104D RID: 4173 RVA: 0x00006EC9 File Offset: 0x000050C9
		private void webBrowser_0_Navigating(object sender, WebBrowserNavigatingEventArgs e)
		{
			e.Cancel = true;
			Process.Start(new ProcessStartInfo
			{
				FileName = e.Url.ToString()
			});
			base.Close();
		}

		// Token: 0x17000266 RID: 614
		// (get) Token: 0x0600104E RID: 4174 RVA: 0x0006DC8C File Offset: 0x0006BE8C
		// (set) Token: 0x0600104F RID: 4175 RVA: 0x00006EF6 File Offset: 0x000050F6
		public bool NoticeAgainChkBoxEnabled
		{
			get
			{
				return this.checkBox_0.Enabled;
			}
			set
			{
				this.checkBox_0.Enabled = value;
			}
		}

		// Token: 0x06001050 RID: 4176 RVA: 0x00006F06 File Offset: 0x00005106
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001051 RID: 4177 RVA: 0x0006DCA8 File Offset: 0x0006BEA8
		private void method_0()
		{
			this.button_0 = new Button();
			this.webBrowser_0 = new WebBrowser();
			this.checkBox_0 = new CheckBox();
			base.SuspendLayout();
			this.button_0.Font = new Font(Class521.smethod_0(7183), 9f);
			this.button_0.Location = new Point(572, 512);
			this.button_0.Name = Class521.smethod_0(37190);
			this.button_0.Size = new Size(136, 35);
			this.button_0.TabIndex = 1;
			this.button_0.Text = Class521.smethod_0(37203);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.webBrowser_0.AllowWebBrowserDrop = false;
			this.webBrowser_0.IsWebBrowserContextMenuEnabled = false;
			this.webBrowser_0.Location = new Point(-1, 0);
			this.webBrowser_0.MinimumSize = new Size(23, 23);
			this.webBrowser_0.Name = Class521.smethod_0(37212);
			this.webBrowser_0.Size = new Size(818, 498);
			this.webBrowser_0.TabIndex = 2;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Font = new Font(Class521.smethod_0(7183), 9f);
			this.checkBox_0.Location = new Point(86, 521);
			this.checkBox_0.Name = Class521.smethod_0(37229);
			this.checkBox_0.Size = new Size(209, 19);
			this.checkBox_0.TabIndex = 3;
			this.checkBox_0.Text = Class521.smethod_0(37254);
			this.checkBox_0.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(818, 557);
			base.Controls.Add(this.checkBox_0);
			base.Controls.Add(this.webBrowser_0);
			base.Controls.Add(this.button_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(37303);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(7587);
			base.Load += this.Form16_Load;
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000824 RID: 2084
		private string string_0;

		// Token: 0x04000825 RID: 2085
		private LogonNoticeInfo logonNoticeInfo_0;

		// Token: 0x04000826 RID: 2086
		private IContainer icontainer_0;

		// Token: 0x04000827 RID: 2087
		private Button button_0;

		// Token: 0x04000828 RID: 2088
		private WebBrowser webBrowser_0;

		// Token: 0x04000829 RID: 2089
		private CheckBox checkBox_0;
	}
}
