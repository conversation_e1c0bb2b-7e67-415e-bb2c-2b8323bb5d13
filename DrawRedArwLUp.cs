﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns18;
using ns26;

namespace TEx
{
	// Token: 0x020001DB RID: 475
	[Serializable]
	internal sealed class DrawRedArwLUp : DrawRedArwUp, ISerializable
	{
		// Token: 0x06001286 RID: 4742 RVA: 0x00006B2F File Offset: 0x00004D2F
		public DrawRedArwLUp()
		{
		}

		// Token: 0x06001287 RID: 4743 RVA: 0x00007B74 File Offset: 0x00005D74
		public DrawRedArwLUp(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(28436);
		}

		// Token: 0x06001288 RID: 4744 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawRedArwLUp(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06001289 RID: 4745 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600128A RID: 4746 RVA: 0x000835BC File Offset: 0x000817BC
		protected override Image vmethod_24()
		{
			return Class375.RedArrow_LUp;
		}
	}
}
