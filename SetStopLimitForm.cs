﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns26;
using TEx.Comn;
using TEx.Trading;

namespace TEx
{
	// Token: 0x0200029E RID: 670
	internal sealed partial class SetStopLimitForm : Form
	{
		// Token: 0x1400009E RID: 158
		// (add) Token: 0x06001DA8 RID: 7592 RVA: 0x000D0548 File Offset: 0x000CE748
		// (remove) Token: 0x06001DA9 RID: 7593 RVA: 0x000D0580 File Offset: 0x000CE780
		public event EventHandler StopLimitUpdated
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001DAA RID: 7594 RVA: 0x0000C767 File Offset: 0x0000A967
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06001DAB RID: 7595 RVA: 0x0000C782 File Offset: 0x0000A982
		public SetStopLimitForm()
		{
			this.method_5();
		}

		// Token: 0x06001DAC RID: 7596 RVA: 0x000D05B8 File Offset: 0x000CE7B8
		private void SetStopLimitForm_Load(object sender, EventArgs e)
		{
			if (base.Tag is ShownOpenTrans)
			{
				this.transaction_0 = (base.Tag as ShownOpenTrans);
			}
			else if (base.Tag is Transaction)
			{
				this.transaction_0 = (base.Tag as Transaction);
			}
			this.method_1();
			this.method_2();
			this.button_3.Enabled = false;
			this.button_0.Enabled = false;
			if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
			{
				this.label_3.Text = Class521.smethod_0(24382);
			}
			else
			{
				StkSymbol stkSymbol = SymbMgr.smethod_3(this.transaction_0.SymbolID);
				this.label_3.Text = stkSymbol.CNName + Class521.smethod_0(24872) + stkSymbol.Code + Class521.smethod_0(5046);
			}
			base.Deactivate += this.SetStopLimitForm_Deactivate;
			base.Activated += this.SetStopLimitForm_Activated;
		}

		// Token: 0x06001DAD RID: 7597 RVA: 0x0000603C File Offset: 0x0000423C
		private void SetStopLimitForm_Deactivate(object sender, EventArgs e)
		{
			base.TopMost = false;
		}

		// Token: 0x06001DAE RID: 7598 RVA: 0x00006047 File Offset: 0x00004247
		private void SetStopLimitForm_Activated(object sender, EventArgs e)
		{
			base.TopMost = true;
		}

		// Token: 0x06001DAF RID: 7599 RVA: 0x000D06C0 File Offset: 0x000CE8C0
		private void method_1()
		{
			if (this.transaction_0 != null)
			{
				if (this.transaction_0.ID < 0)
				{
					this.panel_2.Visible = false;
					this.panel_1.Top -= 35;
					base.Height -= 35;
				}
				if (this.transaction_0.OpenUnits != null)
				{
					this.numericUpDown_0.Maximum = (int)this.transaction_0.OpenUnits.Value;
				}
				else
				{
					this.numericUpDown_0.Maximum = (int)this.transaction_0.Units;
				}
				this.numericUpDown_0.Value = this.numericUpDown_0.Maximum;
				if (this.transaction_0 is ShownOpenTrans)
				{
					ShownOpenTrans shownOpenTrans = this.transaction_0 as ShownOpenTrans;
					if (shownOpenTrans.UsableUnits > 0L)
					{
						if (shownOpenTrans.UsableUnits <= this.numericUpDown_0.Maximum)
						{
							this.numericUpDown_0.Value = shownOpenTrans.UsableUnits;
						}
						else
						{
							this.numericUpDown_0.Value = this.numericUpDown_0.Maximum;
						}
					}
				}
				this.numericUpDown_0.Minimum = 1m;
				this.checkBox_0.CheckedChanged += this.checkBox_0_CheckedChanged;
				this.checkBox_1.CheckedChanged += this.checkBox_1_CheckedChanged;
				this.checkBox_0.Checked = true;
				this.checkBox_1.Checked = false;
				this.numericUpDown_2.Enabled = false;
				this.label_1.Enabled = false;
				StkSymbol stkSymbol = Base.Acct.smethod_48(this.transaction_0.SymbolID);
				this.hisData_0 = Base.Data.smethod_52(this.transaction_0.SymbolID);
				if (this.hisData_0 == null)
				{
					throw new Exception(Class521.smethod_0(85685));
				}
				decimal num = (stkSymbol.LeastPriceVar != null) ? stkSymbol.LeastPriceVar.Value : 1m;
				decimal num2 = Convert.ToDecimal(this.hisData_0.Close);
				decimal num3 = num2;
				decimal num4 = num * 100m;
				if (stkSymbol.AutoStopLossPoints != null)
				{
					if (this.transaction_0.TransType == 3)
					{
						num3 = this.transaction_0.Price + stkSymbol.AutoStopLossPoints.Value;
						if (num3 <= num2)
						{
							num3 = num2 + num4;
						}
					}
					else if (this.transaction_0.TransType == 1)
					{
						num3 = this.transaction_0.Price - stkSymbol.AutoStopLossPoints.Value;
						if (num3 >= num2)
						{
							num3 = num2 - num4;
						}
					}
				}
				else if (this.transaction_0.TransType == 3)
				{
					num3 = this.transaction_0.Price + num4;
					if (num3 <= num2)
					{
						num3 = num2 + num4;
					}
				}
				else if (this.transaction_0.TransType == 1)
				{
					num3 = this.transaction_0.Price - num4;
					if (num3 >= num2)
					{
						num3 = num2 - num4;
					}
				}
				decimal num5 = num2;
				if (stkSymbol.AutoLimitTakePoints != null)
				{
					if (this.transaction_0.TransType == 3)
					{
						num5 = this.transaction_0.Price - stkSymbol.AutoLimitTakePoints.Value;
						if (num5 >= num2)
						{
							num5 = num2 - num4;
						}
					}
					else if (this.transaction_0.TransType == 1)
					{
						num5 = this.transaction_0.Price + stkSymbol.AutoLimitTakePoints.Value;
						if (num5 <= num2)
						{
							num5 = num2 + num4;
						}
					}
				}
				else if (this.transaction_0.TransType == 3)
				{
					num5 = this.transaction_0.Price - num4;
					if (num5 >= num2)
					{
						num5 = num2 - num4;
					}
				}
				else if (this.transaction_0.TransType == 1)
				{
					num5 = this.transaction_0.Price + num4;
					if (num5 <= num2)
					{
						num5 = num2 + num4;
					}
				}
				this.numericUpDown_1.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_1.Increment = stkSymbol.LeastPriceVar.Value;
				this.numericUpDown_2.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_2.Increment = stkSymbol.LeastPriceVar.Value;
				num3 = Math.Round(num3, stkSymbol.DigitNb);
				num5 = Math.Round(num5, stkSymbol.DigitNb);
				decimal num6 = Math.Round(this.transaction_0.Price, stkSymbol.DigitNb);
				if (this.transaction_0.TransType == 3)
				{
					if (num2 < this.transaction_0.Price)
					{
						this.numericUpDown_1.Minimum = num2;
					}
					else
					{
						this.numericUpDown_1.Minimum = num6;
					}
					this.numericUpDown_1.Maximum = 1000000m;
					if (num3 > 0m)
					{
						this.numericUpDown_1.Value = num3;
					}
					this.numericUpDown_2.Minimum = 0m;
					this.numericUpDown_2.Maximum = num6;
					if (num5 > 0m)
					{
						this.numericUpDown_2.Value = num5;
					}
				}
				else
				{
					this.numericUpDown_1.Minimum = 0m;
					if (num2 > this.transaction_0.Price)
					{
						this.numericUpDown_1.Maximum = num2;
					}
					else
					{
						this.numericUpDown_1.Maximum = num6;
					}
					if (num3 > 0m)
					{
						this.numericUpDown_1.Value = num3;
					}
					this.numericUpDown_2.Minimum = num6;
					this.numericUpDown_2.Maximum = 1000000m;
					if (num5 > 0m)
					{
						this.numericUpDown_2.Value = num5;
					}
				}
				this.numericUpDown_1.ValueChanged += this.numericUpDown_1_ValueChanged;
				this.numericUpDown_2.ValueChanged += this.numericUpDown_2_ValueChanged;
				this.checkBox_2.CheckedChanged += this.checkBox_2_CheckedChanged;
				this.label_6.Enabled = false;
				this.numericUpDown_3.Enabled = false;
				this.numericUpDown_3.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_3.Minimum = num;
				this.numericUpDown_3.Maximum = 1000000m;
				this.numericUpDown_3.Increment = num;
				if (num4 > 0m)
				{
					this.numericUpDown_3.Value = num4;
				}
				this.button_2.Focus();
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(85644)));
			}
		}

		// Token: 0x06001DB0 RID: 7600 RVA: 0x000D0DBC File Offset: 0x000CEFBC
		private void method_2()
		{
			this.bindingList_0 = Base.Trading.smethod_101(this.transaction_0.ID);
			this.list_0 = new List<int>();
			this.dataGridView_0 = new DataGridView();
			this.dataGridView_0.Location = new Point(28, 82);
			this.dataGridView_0.Name = Class521.smethod_0(85718);
			this.dataGridView_0.Size = new Size(350, 115);
			this.dataGridView_0.RowTemplate.Height = 22;
			this.dataGridView_0.DataSource = this.bindingList_0;
			this.panel_0.Controls.Add(this.dataGridView_0);
			this.method_3(this.dataGridView_0);
			this.dataGridView_0.Columns[0].Visible = false;
			this.dataGridView_0.Columns[1].Visible = false;
			this.dataGridView_0.Columns[2].Visible = false;
			this.dataGridView_0.Columns[3].Visible = false;
			this.dataGridView_0.Columns[4].Visible = true;
			this.dataGridView_0.Columns[5].Visible = true;
			this.dataGridView_0.Columns[6].Visible = true;
			this.dataGridView_0.Columns[7].Visible = false;
			this.dataGridView_0.Columns[8].Visible = false;
			this.dataGridView_0.Columns[9].Visible = false;
			this.dataGridView_0.Columns[10].Visible = false;
			this.dataGridView_0.Columns[11].Visible = false;
			this.dataGridView_0.Columns[4].HeaderText = Class521.smethod_0(52539);
			this.dataGridView_0.Columns[5].HeaderText = Class521.smethod_0(85743);
			this.dataGridView_0.Columns[6].HeaderText = Class521.smethod_0(85760);
			int width = this.dataGridView_0.Width;
			if (this.transaction_0.ID < 0)
			{
				this.dataGridView_0.Columns[12].Visible = false;
				this.dataGridView_0.Columns[4].Width = Convert.ToInt32(Math.Round((double)width * 0.275));
				this.dataGridView_0.Columns[5].Width = Convert.ToInt32(Math.Round((double)width * 0.362));
				this.dataGridView_0.Columns[6].Width = Convert.ToInt32(Math.Round((double)width * 0.362));
			}
			else
			{
				this.dataGridView_0.Columns[12].Visible = true;
				this.dataGridView_0.Columns[12].HeaderText = Class521.smethod_0(85777);
				this.dataGridView_0.Columns[4].Width = Convert.ToInt32(Math.Round((double)width * 0.201));
				this.dataGridView_0.Columns[5].Width = Convert.ToInt32(Math.Round((double)width * 0.262));
				this.dataGridView_0.Columns[6].Width = Convert.ToInt32(Math.Round((double)width * 0.262));
				this.dataGridView_0.Columns[12].Width = Convert.ToInt32(Math.Round((double)width * 0.272));
			}
			this.dataGridView_0.RowEnter += this.dataGridView_0_RowEnter;
			this.dataGridView_0.RowLeave += this.dataGridView_0_RowLeave;
			this.dataGridView_0.CellBeginEdit += this.dataGridView_0_CellBeginEdit;
			this.dataGridView_0.CellEndEdit += this.dataGridView_0_CellEndEdit;
			this.dataGridView_0.DataError += this.dataGridView_0_DataError;
			this.dataGridView_0.CellFormatting += this.dataGridView_0_CellFormatting;
		}

		// Token: 0x06001DB1 RID: 7601 RVA: 0x000D1228 File Offset: 0x000CF428
		private void method_3(DataGridView dataGridView_1)
		{
			dataGridView_1.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			dataGridView_1.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			dataGridView_1.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
			dataGridView_1.ColumnHeadersDefaultCellStyle.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Regular);
			dataGridView_1.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			dataGridView_1.BackgroundColor = Color.FromKnownColor(KnownColor.Control);
			dataGridView_1.BorderStyle = BorderStyle.None;
			dataGridView_1.CellBorderStyle = DataGridViewCellBorderStyle.None;
			dataGridView_1.AlternatingRowsDefaultCellStyle.BackColor = Color.FloralWhite;
			dataGridView_1.DefaultCellStyle.Alignment = DataGridViewContentAlignment.BottomRight;
			dataGridView_1.RowHeadersVisible = false;
			dataGridView_1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			dataGridView_1.AllowUserToAddRows = false;
			dataGridView_1.AllowUserToDeleteRows = false;
			dataGridView_1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
			dataGridView_1.Dock = DockStyle.Fill;
		}

		// Token: 0x06001DB2 RID: 7602 RVA: 0x000D12E4 File Offset: 0x000CF4E4
		private void checkBox_0_CheckedChanged(object sender, EventArgs e)
		{
			bool flag = false;
			if (this.checkBox_0.Checked)
			{
				this.numericUpDown_1.Enabled = true;
				this.label_0.Enabled = true;
				if (this.checkBox_2.Checked)
				{
					this.checkBox_2.Checked = false;
					flag = true;
				}
			}
			else
			{
				this.numericUpDown_1.Enabled = false;
				this.label_0.Enabled = false;
			}
			if (flag)
			{
				this.checkBox_2_CheckedChanged(this.checkBox_2, null);
			}
		}

		// Token: 0x06001DB3 RID: 7603 RVA: 0x000D1360 File Offset: 0x000CF560
		private void checkBox_1_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_1.Checked)
			{
				this.numericUpDown_2.Enabled = true;
				this.label_1.Enabled = true;
			}
			else
			{
				this.numericUpDown_2.Enabled = false;
				this.label_1.Enabled = false;
			}
		}

		// Token: 0x06001DB4 RID: 7604 RVA: 0x000D13B0 File Offset: 0x000CF5B0
		private void checkBox_2_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_2.Checked)
			{
				this.numericUpDown_3.Enabled = true;
				this.label_6.Enabled = true;
				this.checkBox_0.Checked = false;
			}
			else
			{
				this.numericUpDown_3.Enabled = false;
				this.label_6.Enabled = false;
			}
			this.checkBox_0_CheckedChanged(this.checkBox_0, null);
		}

		// Token: 0x06001DB5 RID: 7605 RVA: 0x0000C792 File Offset: 0x0000A992
		private void dataGridView_0_RowEnter(object sender, DataGridViewCellEventArgs e)
		{
			this.button_3.Enabled = true;
			this.button_0.Enabled = true;
		}

		// Token: 0x06001DB6 RID: 7606 RVA: 0x0000C7AE File Offset: 0x0000A9AE
		private void dataGridView_0_RowLeave(object sender, DataGridViewCellEventArgs e)
		{
			if (!this.button_3.Focused)
			{
				this.button_3.Enabled = false;
			}
			if (!this.button_0.Focused)
			{
				this.button_0.Enabled = false;
			}
		}

		// Token: 0x06001DB7 RID: 7607 RVA: 0x000D1418 File Offset: 0x000CF618
		private void dataGridView_0_CellBeginEdit(object sender, DataGridViewCellCancelEventArgs e)
		{
			if (e.RowIndex >= 0)
			{
				if ((this.dataGridView_0.Rows[e.RowIndex].DataBoundItem as ShownSLOrder).TrailingStopPts != null)
				{
					if (e.ColumnIndex != 12 && e.ColumnIndex != 4)
					{
						e.Cancel = true;
					}
				}
				else if (e.ColumnIndex == 12)
				{
					e.Cancel = true;
				}
			}
		}

		// Token: 0x06001DB8 RID: 7608 RVA: 0x000D148C File Offset: 0x000CF68C
		private void dataGridView_0_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex == 4)
			{
				long num = this.bindingList_0.Select(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_0)).Sum();
				long? openUnits = this.transaction_0.OpenUnits;
				if (num > openUnits.GetValueOrDefault() & openUnits != null)
				{
					Base.UI.smethod_161();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				long num2 = -1L;
				try
				{
					num2 = Convert.ToInt64(this.dataGridView_0.Rows[e.RowIndex].Cells[e.ColumnIndex].Value);
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (num2 <= 0L)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 5)
			{
				decimal d = -1m;
				object value = this.dataGridView_0.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value != null && !string.IsNullOrEmpty(value.ToString()))
					{
						d = Convert.ToDecimal(value);
					}
					else
					{
						object value2 = this.dataGridView_0.Rows[e.RowIndex].Cells[6].Value;
						if (value2 != null && !string.IsNullOrEmpty(value2.ToString()))
						{
							return;
						}
					}
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				decimal d2 = Convert.ToDecimal(this.hisData_0.Close);
				if (this.transaction_0.TransType == 1 && d >= d2)
				{
					MessageBox.Show(Class521.smethod_0(85802), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (this.transaction_0.TransType == 3 && d <= d2)
				{
					MessageBox.Show(Class521.smethod_0(85843), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 6)
			{
				decimal d3 = -1m;
				object value3 = this.dataGridView_0.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value3 != null && !string.IsNullOrEmpty(value3.ToString()))
					{
						d3 = Convert.ToDecimal(value3);
					}
					else
					{
						object value4 = this.dataGridView_0.Rows[e.RowIndex].Cells[5].Value;
						if (value4 != null && !string.IsNullOrEmpty(value4.ToString()))
						{
							return;
						}
					}
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d3 <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				decimal d4 = Convert.ToDecimal(this.hisData_0.Close);
				if (this.transaction_0.TransType == 1 && d3 <= d4)
				{
					MessageBox.Show(Class521.smethod_0(85884), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (this.transaction_0.TransType == 3 && d3 >= d4)
				{
					MessageBox.Show(Class521.smethod_0(85925), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 12)
			{
				decimal d5 = -1m;
				object value5 = this.dataGridView_0.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value5 == null || string.IsNullOrEmpty(value5.ToString()))
					{
						Base.UI.smethod_160();
						this.method_4(e.ColumnIndex, e.RowIndex);
						return;
					}
					d5 = Convert.ToDecimal(value5);
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d5 <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
				}
			}
		}

		// Token: 0x06001DB9 RID: 7609 RVA: 0x000D1998 File Offset: 0x000CFB98
		private void method_4(int int_0, int int_1)
		{
			this.dataGridView_0.Focus();
			this.dataGridView_0.CurrentCell = this.dataGridView_0.Rows[int_1].Cells[int_0];
			this.dataGridView_0.BeginEdit(true);
		}

		// Token: 0x06001DBA RID: 7610 RVA: 0x000D19E8 File Offset: 0x000CFBE8
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.checkBox_1.Checked || this.checkBox_0.Checked || this.checkBox_2.Checked)
			{
				decimal value = this.numericUpDown_1.Value;
				decimal value2 = this.numericUpDown_2.Value;
				decimal value3 = this.numericUpDown_3.Value;
				decimal d = Convert.ToDecimal(this.hisData_0.Close);
				if (this.checkBox_0.Checked)
				{
					if (value == 0m)
					{
						Base.UI.smethod_162();
						this.numericUpDown_1.Focus();
						return;
					}
					if (this.transaction_0.TransType == 1 && value >= d)
					{
						MessageBox.Show(Class521.smethod_0(85802), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_1.Focus();
						return;
					}
					if (this.transaction_0.TransType == 3 && value <= d)
					{
						MessageBox.Show(Class521.smethod_0(85843), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_1.Focus();
						return;
					}
				}
				if (this.checkBox_1.Checked)
				{
					if (value2 == 0m)
					{
						Base.UI.smethod_162();
						this.numericUpDown_2.Focus();
						return;
					}
					if (this.transaction_0.TransType == 1 && value2 <= d)
					{
						MessageBox.Show(Class521.smethod_0(85884), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_2.Focus();
						return;
					}
					if (this.transaction_0.TransType == 3 && value2 >= d)
					{
						MessageBox.Show(Class521.smethod_0(85925), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_2.Focus();
						return;
					}
				}
				long num = 0L;
				long num2 = 0L;
				if (this.bindingList_0 != null)
				{
					num = this.bindingList_0.Where(new Func<ShownSLOrder, bool>(SetStopLimitForm.<>c.<>9.method_1)).Sum(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_2));
					num2 = this.bindingList_0.Where(new Func<ShownSLOrder, bool>(SetStopLimitForm.<>c.<>9.method_3)).Sum(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_4));
				}
				long num3 = Convert.ToInt64(this.numericUpDown_0.Value);
				if (this.checkBox_0.Checked)
				{
					long num4 = num3 + num;
					long? openUnits = this.transaction_0.OpenUnits;
					if (num4 > openUnits.GetValueOrDefault() & openUnits != null)
					{
						goto IL_312;
					}
				}
				if (this.checkBox_1.Checked)
				{
					long num5 = num3 + num2;
					long? openUnits = this.transaction_0.OpenUnits;
					if (num5 > openUnits.GetValueOrDefault() & openUnits != null)
					{
						goto IL_312;
					}
				}
				ShownSLOrder shownSLOrder = new ShownSLOrder();
				shownSLOrder.SymbID = this.transaction_0.SymbolID;
				shownSLOrder.SymbCode = SymbMgr.smethod_3(this.transaction_0.SymbolID).Code;
				shownSLOrder.Units = Convert.ToInt64(this.numericUpDown_0.Value);
				shownSLOrder.TransID = this.transaction_0.ID;
				if (this.checkBox_0.Checked)
				{
					shownSLOrder.StopPrice = new decimal?(value);
				}
				else if (this.checkBox_2.Checked)
				{
					shownSLOrder.TrailingStopPts = new decimal?(value3);
				}
				if (this.checkBox_1.Checked)
				{
					shownSLOrder.LimitPrice = new decimal?(value2);
				}
				if (this.bindingList_0 == null)
				{
					this.bindingList_0 = new BindingList<ShownSLOrder>();
				}
				this.bindingList_0.Add(shownSLOrder);
				return;
				IL_312:
				Base.UI.smethod_161();
				this.numericUpDown_1.Focus();
			}
		}

		// Token: 0x06001DBB RID: 7611 RVA: 0x000D1DF0 File Offset: 0x000CFFF0
		private void button_2_Click(object sender, EventArgs e)
		{
			bool flag = false;
			if (this.bindingList_0 != null && this.bindingList_0.Any<ShownSLOrder>())
			{
				foreach (ShownSLOrder shownSLOrder in this.bindingList_0)
				{
					if (shownSLOrder.LimitCondOdrId == null && shownSLOrder.StopCondOdrId == null && shownSLOrder.TrailingStopCondOdrId == null)
					{
						if (shownSLOrder.StopPrice != null)
						{
							Base.Trading.smethod_85(this.transaction_0, new decimal?(shownSLOrder.StopPrice.Value), null, shownSLOrder.Units);
						}
						else if (shownSLOrder.TrailingStopPts != null)
						{
							Base.Trading.smethod_85(this.transaction_0, null, new decimal?(shownSLOrder.TrailingStopPts.Value), shownSLOrder.Units);
						}
						if (shownSLOrder.LimitPrice != null)
						{
							Base.Trading.smethod_85(this.transaction_0, new decimal?(shownSLOrder.LimitPrice.Value), null, shownSLOrder.Units);
						}
					}
					else
					{
						decimal num = Convert.ToDecimal(shownSLOrder.Units);
						if (shownSLOrder.LimitCondOdrId != null && shownSLOrder.LimitPrice != null)
						{
							Base.Trading.smethod_92(shownSLOrder.LimitCondOdrId.Value, shownSLOrder.LimitPrice.Value, 0m, num);
						}
						if (shownSLOrder.StopCondOdrId != null)
						{
							if (shownSLOrder.StopPrice != null)
							{
								Base.Trading.smethod_92(shownSLOrder.StopCondOdrId.Value, shownSLOrder.StopPrice.Value, 0m, num);
							}
						}
						else if (shownSLOrder.TrailingStopCondOdrId != null && shownSLOrder.TrailingStopPts != null)
						{
							Base.Trading.smethod_93(shownSLOrder.TrailingStopCondOdrId.Value, null, null, shownSLOrder.TrailingStopPts, num, true);
						}
					}
				}
				flag = true;
			}
			if (this.list_0 != null)
			{
				foreach (int int_ in this.list_0)
				{
					Base.Trading.smethod_94(int_, OrderStatus.Canceled);
					flag = true;
				}
			}
			if (flag)
			{
				this.method_0();
			}
			base.Dispose();
		}

		// Token: 0x06001DBC RID: 7612 RVA: 0x000D20E0 File Offset: 0x000D02E0
		private void button_0_Click(object sender, EventArgs e)
		{
			this.dataGridView_0.Focus();
			try
			{
				this.dataGridView_0.BeginEdit(true);
			}
			catch
			{
			}
		}

		// Token: 0x06001DBD RID: 7613 RVA: 0x000D2120 File Offset: 0x000D0320
		private void button_3_Click(object sender, EventArgs e)
		{
			if (this.dataGridView_0 != null)
			{
				if (this.dataGridView_0.SelectedRows.Count > 0)
				{
					using (IEnumerator enumerator = this.dataGridView_0.SelectedRows.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							object obj = enumerator.Current;
							ShownSLOrder shownSLOrder = ((DataGridViewRow)obj).DataBoundItem as ShownSLOrder;
							if (shownSLOrder.LimitCondOdrId != null)
							{
								this.list_0.Add(shownSLOrder.LimitCondOdrId.Value);
							}
							if (shownSLOrder.StopCondOdrId != null)
							{
								this.list_0.Add(shownSLOrder.StopCondOdrId.Value);
							}
							this.bindingList_0.Remove(shownSLOrder);
						}
						goto IL_17A;
					}
				}
				if (this.dataGridView_0.SelectedCells.Count > 0)
				{
					int rowIndex = this.dataGridView_0.SelectedCells[0].RowIndex;
					ShownSLOrder shownSLOrder2 = this.dataGridView_0.Rows[rowIndex].DataBoundItem as ShownSLOrder;
					if (shownSLOrder2.LimitCondOdrId != null)
					{
						this.list_0.Add(shownSLOrder2.LimitCondOdrId.Value);
					}
					if (shownSLOrder2.StopCondOdrId != null)
					{
						this.list_0.Add(shownSLOrder2.StopCondOdrId.Value);
					}
					this.bindingList_0.Remove(shownSLOrder2);
				}
				IL_17A:
				this.dataGridView_0.Refresh();
			}
		}

		// Token: 0x06001DBE RID: 7614 RVA: 0x0000C7E4 File Offset: 0x0000A9E4
		private void button_4_Click(object sender, EventArgs e)
		{
			base.Dispose();
		}

		// Token: 0x06001DBF RID: 7615 RVA: 0x0000C7EE File Offset: 0x0000A9EE
		private void dataGridView_0_DataError(object sender, DataGridViewDataErrorEventArgs e)
		{
			Base.UI.smethod_160();
		}

		// Token: 0x06001DC0 RID: 7616 RVA: 0x0000C7F7 File Offset: 0x0000A9F7
		private void dataGridView_0_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			e.CellStyle.SelectionBackColor = Color.Moccasin;
			e.CellStyle.SelectionForeColor = e.CellStyle.ForeColor;
		}

		// Token: 0x06001DC1 RID: 7617 RVA: 0x0000C821 File Offset: 0x0000AA21
		private void numericUpDown_1_ValueChanged(object sender, EventArgs e)
		{
			if (!this.checkBox_0.Checked)
			{
				this.checkBox_0.Checked = true;
			}
		}

		// Token: 0x06001DC2 RID: 7618 RVA: 0x0000C83E File Offset: 0x0000AA3E
		private void numericUpDown_2_ValueChanged(object sender, EventArgs e)
		{
			if (!this.checkBox_1.Checked)
			{
				this.checkBox_1.Checked = true;
			}
		}

		// Token: 0x06001DC3 RID: 7619 RVA: 0x0000C85B File Offset: 0x0000AA5B
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001DC4 RID: 7620 RVA: 0x000D22C4 File Offset: 0x000D04C4
		private void method_5()
		{
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.numericUpDown_0 = new NumericUpDown();
			this.button_2 = new Button();
			this.button_3 = new Button();
			this.label_3 = new Label();
			this.button_4 = new Button();
			this.panel_0 = new Panel();
			this.label_4 = new Label();
			this.numericUpDown_1 = new NumericUpDown();
			this.numericUpDown_2 = new NumericUpDown();
			this.checkBox_0 = new CheckBox();
			this.checkBox_1 = new CheckBox();
			this.label_5 = new Label();
			this.pictureBox_0 = new PictureBox();
			this.checkBox_2 = new CheckBox();
			this.numericUpDown_3 = new NumericUpDown();
			this.groupBox_0 = new GroupBox();
			this.label_6 = new Label();
			this.panel_1 = new Panel();
			this.panel_2 = new Panel();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			((ISupportInitialize)this.numericUpDown_1).BeginInit();
			((ISupportInitialize)this.numericUpDown_2).BeginInit();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			((ISupportInitialize)this.numericUpDown_3).BeginInit();
			this.panel_1.SuspendLayout();
			this.panel_2.SuspendLayout();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(240, 26);
			this.label_0.Name = Class521.smethod_0(85966);
			this.label_0.Size = new Size(60, 15);
			this.label_0.TabIndex = 1;
			this.label_0.Text = Class521.smethod_0(85987);
			this.label_0.TextAlign = ContentAlignment.MiddleRight;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(240, 57);
			this.label_1.Name = Class521.smethod_0(86004);
			this.label_1.Size = new Size(60, 15);
			this.label_1.TabIndex = 2;
			this.label_1.Text = Class521.smethod_0(86025);
			this.label_1.TextAlign = ContentAlignment.MiddleRight;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(34, 57);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(45, 15);
			this.label_2.TabIndex = 4;
			this.label_2.Text = Class521.smethod_0(86042);
			this.label_2.TextAlign = ContentAlignment.MiddleRight;
			this.button_0.Location = new Point(439, 47);
			this.button_0.Name = Class521.smethod_0(86055);
			this.button_0.Size = new Size(110, 32);
			this.button_0.TabIndex = 5;
			this.button_0.Text = Class521.smethod_0(22174);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Location = new Point(439, 7);
			this.button_1.Name = Class521.smethod_0(86072);
			this.button_1.Size = new Size(110, 32);
			this.button_1.TabIndex = 4;
			this.button_1.Text = Class521.smethod_0(59015);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.numericUpDown_0.BorderStyle = BorderStyle.FixedSingle;
			this.numericUpDown_0.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_0.Location = new Point(84, 53);
			this.numericUpDown_0.Name = Class521.smethod_0(16172);
			this.numericUpDown_0.Size = new Size(92, 24);
			this.numericUpDown_0.TabIndex = 1;
			this.button_2.Location = new Point(439, 150);
			this.button_2.Name = Class521.smethod_0(7442);
			this.button_2.Size = new Size(110, 32);
			this.button_2.TabIndex = 7;
			this.button_2.Text = Class521.smethod_0(5801);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Location = new Point(439, 87);
			this.button_3.Name = Class521.smethod_0(86089);
			this.button_3.Size = new Size(110, 32);
			this.button_3.TabIndex = 6;
			this.button_3.Text = Class521.smethod_0(22208);
			this.button_3.UseVisualStyleBackColor = true;
			this.button_3.Click += this.button_3_Click;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(83, 26);
			this.label_3.Name = Class521.smethod_0(86106);
			this.label_3.Size = new Size(23, 15);
			this.label_3.TabIndex = 8;
			this.label_3.Text = Class521.smethod_0(47576);
			this.button_4.DialogResult = DialogResult.Cancel;
			this.button_4.Location = new Point(439, 189);
			this.button_4.Name = Class521.smethod_0(7421);
			this.button_4.Size = new Size(110, 32);
			this.button_4.TabIndex = 8;
			this.button_4.Text = Class521.smethod_0(5783);
			this.button_4.UseVisualStyleBackColor = true;
			this.button_4.Click += this.button_4_Click;
			this.panel_0.BorderStyle = BorderStyle.Fixed3D;
			this.panel_0.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.panel_0.Location = new Point(22, 8);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(396, 157);
			this.panel_0.TabIndex = 9;
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(34, 26);
			this.label_4.Name = Class521.smethod_0(7019);
			this.label_4.Size = new Size(45, 15);
			this.label_4.TabIndex = 10;
			this.label_4.Text = Class521.smethod_0(4587);
			this.label_4.TextAlign = ContentAlignment.MiddleRight;
			this.numericUpDown_1.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_1.Location = new Point(305, 22);
			this.numericUpDown_1.Name = Class521.smethod_0(86123);
			this.numericUpDown_1.Size = new Size(100, 24);
			this.numericUpDown_1.TabIndex = 11;
			this.numericUpDown_2.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_2.Location = new Point(305, 53);
			this.numericUpDown_2.Name = Class521.smethod_0(86156);
			this.numericUpDown_2.Size = new Size(100, 24);
			this.numericUpDown_2.TabIndex = 12;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(414, 26);
			this.checkBox_0.Name = Class521.smethod_0(86193);
			this.checkBox_0.Size = new Size(18, 17);
			this.checkBox_0.TabIndex = 13;
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.Location = new Point(414, 57);
			this.checkBox_1.Name = Class521.smethod_0(86214);
			this.checkBox_1.Size = new Size(18, 17);
			this.checkBox_1.TabIndex = 14;
			this.checkBox_1.UseVisualStyleBackColor = true;
			this.label_5.Location = new Point(49, 182);
			this.label_5.Name = Class521.smethod_0(5871);
			this.label_5.Size = new Size(369, 45);
			this.label_5.TabIndex = 20;
			this.label_5.Text = Class521.smethod_0(86235);
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(23, 181);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 19;
			this.pictureBox_0.TabStop = false;
			this.checkBox_2.AutoSize = true;
			this.checkBox_2.Location = new Point(402, 22);
			this.checkBox_2.Name = Class521.smethod_0(25211);
			this.checkBox_2.Size = new Size(18, 17);
			this.checkBox_2.TabIndex = 21;
			this.checkBox_2.UseVisualStyleBackColor = true;
			this.numericUpDown_3.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_3.Location = new Point(293, 17);
			this.numericUpDown_3.Name = Class521.smethod_0(25170);
			this.numericUpDown_3.Size = new Size(100, 24);
			this.numericUpDown_3.TabIndex = 22;
			this.groupBox_0.Location = new Point(21, 7);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(399, 2);
			this.groupBox_0.TabIndex = 23;
			this.groupBox_0.TabStop = false;
			this.label_6.AutoSize = true;
			this.label_6.Location = new Point(183, 22);
			this.label_6.Name = Class521.smethod_0(25136);
			this.label_6.Size = new Size(105, 15);
			this.label_6.TabIndex = 24;
			this.label_6.Text = Class521.smethod_0(86393);
			this.label_6.TextAlign = ContentAlignment.MiddleRight;
			this.panel_1.Controls.Add(this.panel_0);
			this.panel_1.Controls.Add(this.button_0);
			this.panel_1.Controls.Add(this.button_1);
			this.panel_1.Controls.Add(this.button_2);
			this.panel_1.Controls.Add(this.button_3);
			this.panel_1.Controls.Add(this.label_5);
			this.panel_1.Controls.Add(this.button_4);
			this.panel_1.Controls.Add(this.pictureBox_0);
			this.panel_1.Location = new Point(12, 125);
			this.panel_1.Name = Class521.smethod_0(86422);
			this.panel_1.Size = new Size(558, 231);
			this.panel_1.TabIndex = 25;
			this.panel_2.Controls.Add(this.groupBox_0);
			this.panel_2.Controls.Add(this.checkBox_2);
			this.panel_2.Controls.Add(this.label_6);
			this.panel_2.Controls.Add(this.numericUpDown_3);
			this.panel_2.Location = new Point(12, 80);
			this.panel_2.Name = Class521.smethod_0(86447);
			this.panel_2.Size = new Size(558, 44);
			this.panel_2.TabIndex = 26;
			base.AcceptButton = this.button_2;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_4;
			base.ClientSize = new Size(580, 361);
			base.Controls.Add(this.panel_2);
			base.Controls.Add(this.panel_1);
			base.Controls.Add(this.checkBox_1);
			base.Controls.Add(this.checkBox_0);
			base.Controls.Add(this.numericUpDown_2);
			base.Controls.Add(this.numericUpDown_1);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.numericUpDown_0);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(86480);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(86505);
			base.TopMost = true;
			base.Load += this.SetStopLimitForm_Load;
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			((ISupportInitialize)this.numericUpDown_1).EndInit();
			((ISupportInitialize)this.numericUpDown_2).EndInit();
			((ISupportInitialize)this.pictureBox_0).EndInit();
			((ISupportInitialize)this.numericUpDown_3).EndInit();
			this.panel_1.ResumeLayout(false);
			this.panel_2.ResumeLayout(false);
			this.panel_2.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000E92 RID: 3730
		private Transaction transaction_0;

		// Token: 0x04000E93 RID: 3731
		private BindingList<ShownSLOrder> bindingList_0;

		// Token: 0x04000E94 RID: 3732
		private DataGridView dataGridView_0;

		// Token: 0x04000E95 RID: 3733
		private List<int> list_0;

		// Token: 0x04000E96 RID: 3734
		private HisData hisData_0;

		// Token: 0x04000E97 RID: 3735
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000E98 RID: 3736
		private IContainer icontainer_0;

		// Token: 0x04000E99 RID: 3737
		private Label label_0;

		// Token: 0x04000E9A RID: 3738
		private Label label_1;

		// Token: 0x04000E9B RID: 3739
		private Label label_2;

		// Token: 0x04000E9C RID: 3740
		private Button button_0;

		// Token: 0x04000E9D RID: 3741
		private Button button_1;

		// Token: 0x04000E9E RID: 3742
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000E9F RID: 3743
		private Button button_2;

		// Token: 0x04000EA0 RID: 3744
		private Button button_3;

		// Token: 0x04000EA1 RID: 3745
		private Label label_3;

		// Token: 0x04000EA2 RID: 3746
		private Button button_4;

		// Token: 0x04000EA3 RID: 3747
		private Panel panel_0;

		// Token: 0x04000EA4 RID: 3748
		private Label label_4;

		// Token: 0x04000EA5 RID: 3749
		private NumericUpDown numericUpDown_1;

		// Token: 0x04000EA6 RID: 3750
		private NumericUpDown numericUpDown_2;

		// Token: 0x04000EA7 RID: 3751
		private CheckBox checkBox_0;

		// Token: 0x04000EA8 RID: 3752
		private CheckBox checkBox_1;

		// Token: 0x04000EA9 RID: 3753
		private Label label_5;

		// Token: 0x04000EAA RID: 3754
		private PictureBox pictureBox_0;

		// Token: 0x04000EAB RID: 3755
		private CheckBox checkBox_2;

		// Token: 0x04000EAC RID: 3756
		private NumericUpDown numericUpDown_3;

		// Token: 0x04000EAD RID: 3757
		private GroupBox groupBox_0;

		// Token: 0x04000EAE RID: 3758
		private Label label_6;

		// Token: 0x04000EAF RID: 3759
		private Panel panel_1;

		// Token: 0x04000EB0 RID: 3760
		private Panel panel_2;
	}
}
