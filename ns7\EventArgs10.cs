﻿using System;
using TEx;

namespace ns7
{
	// Token: 0x020001F6 RID: 502
	internal sealed class EventArgs10 : EventArgs
	{
		// Token: 0x060013AD RID: 5037 RVA: 0x00007FA5 File Offset: 0x000061A5
		public EventArgs10(QuickWndItem quickWndItem_1)
		{
			this.quickWndItem_0 = quickWndItem_1;
		}

		// Token: 0x170002F4 RID: 756
		// (get) Token: 0x060013AE RID: 5038 RVA: 0x00088730 File Offset: 0x00086930
		public QuickWndItem QuickWndItem
		{
			get
			{
				return this.quickWndItem_0;
			}
		}

		// Token: 0x04000A3E RID: 2622
		private readonly QuickWndItem quickWndItem_0;
	}
}
