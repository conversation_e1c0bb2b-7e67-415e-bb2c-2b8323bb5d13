﻿using System;
using ns2;
using TEx.Comn;

namespace ns24
{
	// Token: 0x020003AB RID: 939
	internal sealed class Class509
	{
		// Token: 0x1700064C RID: 1612
		// (get) Token: 0x060025D6 RID: 9686 RVA: 0x00103ACC File Offset: 0x00101CCC
		// (set) Token: 0x060025D7 RID: 9687 RVA: 0x0000E757 File Offset: 0x0000C957
		public int SymbID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x1700064D RID: 1613
		// (get) Token: 0x060025D8 RID: 9688 RVA: 0x00103AE4 File Offset: 0x00101CE4
		// (set) Token: 0x060025D9 RID: 9689 RVA: 0x0000E762 File Offset: 0x0000C962
		public string SymbCode
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x1700064E RID: 1614
		// (get) Token: 0x060025DA RID: 9690 RVA: 0x00103AFC File Offset: 0x00101CFC
		// (set) Token: 0x060025DB RID: 9691 RVA: 0x0000E76D File Offset: 0x0000C96D
		public decimal? StopPoints
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x1700064F RID: 1615
		// (get) Token: 0x060025DC RID: 9692 RVA: 0x00103B14 File Offset: 0x00101D14
		// (set) Token: 0x060025DD RID: 9693 RVA: 0x0000E778 File Offset: 0x0000C978
		public decimal? LimitPoints
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x17000650 RID: 1616
		// (get) Token: 0x060025DE RID: 9694 RVA: 0x00103B2C File Offset: 0x00101D2C
		// (set) Token: 0x060025DF RID: 9695 RVA: 0x0000E783 File Offset: 0x0000C983
		public FeeType FeeType
		{
			get
			{
				return this.feeType_0;
			}
			set
			{
				this.feeType_0 = value;
			}
		}

		// Token: 0x17000651 RID: 1617
		// (get) Token: 0x060025E0 RID: 9696 RVA: 0x00103B44 File Offset: 0x00101D44
		// (set) Token: 0x060025E1 RID: 9697 RVA: 0x0000E78E File Offset: 0x0000C98E
		public decimal? FeeRate
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x17000652 RID: 1618
		// (get) Token: 0x060025E2 RID: 9698 RVA: 0x00103B5C File Offset: 0x00101D5C
		// (set) Token: 0x060025E3 RID: 9699 RVA: 0x0000E799 File Offset: 0x0000C999
		public decimal? FeePerUnit
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				this.nullable_3 = value;
			}
		}

		// Token: 0x17000653 RID: 1619
		// (get) Token: 0x060025E4 RID: 9700 RVA: 0x00103B74 File Offset: 0x00101D74
		// (set) Token: 0x060025E5 RID: 9701 RVA: 0x0000E7A4 File Offset: 0x0000C9A4
		public Enum14 OneSideFee
		{
			get
			{
				return this.enum14_0;
			}
			set
			{
				this.enum14_0 = value;
			}
		}

		// Token: 0x17000654 RID: 1620
		// (get) Token: 0x060025E6 RID: 9702 RVA: 0x00103B8C File Offset: 0x00101D8C
		// (set) Token: 0x060025E7 RID: 9703 RVA: 0x0000E7AF File Offset: 0x0000C9AF
		public decimal? MarginRate
		{
			get
			{
				return this.nullable_4;
			}
			set
			{
				this.nullable_4 = value;
			}
		}

		// Token: 0x17000655 RID: 1621
		// (get) Token: 0x060025E8 RID: 9704 RVA: 0x00103BA4 File Offset: 0x00101DA4
		// (set) Token: 0x060025E9 RID: 9705 RVA: 0x0000E7BA File Offset: 0x0000C9BA
		public decimal? AvgSlipg
		{
			get
			{
				return this.nullable_5;
			}
			set
			{
				this.nullable_5 = value;
			}
		}

		// Token: 0x17000656 RID: 1622
		// (get) Token: 0x060025EA RID: 9706 RVA: 0x00103BBC File Offset: 0x00101DBC
		// (set) Token: 0x060025EB RID: 9707 RVA: 0x0000E7C5 File Offset: 0x0000C9C5
		public int? DefaultUnits
		{
			get
			{
				return this.nullable_6;
			}
			set
			{
				this.nullable_6 = value;
			}
		}

		// Token: 0x04001243 RID: 4675
		private int int_0;

		// Token: 0x04001244 RID: 4676
		private string string_0;

		// Token: 0x04001245 RID: 4677
		private decimal? nullable_0;

		// Token: 0x04001246 RID: 4678
		private decimal? nullable_1;

		// Token: 0x04001247 RID: 4679
		private FeeType feeType_0;

		// Token: 0x04001248 RID: 4680
		private decimal? nullable_2;

		// Token: 0x04001249 RID: 4681
		private decimal? nullable_3;

		// Token: 0x0400124A RID: 4682
		private Enum14 enum14_0;

		// Token: 0x0400124B RID: 4683
		private decimal? nullable_4;

		// Token: 0x0400124C RID: 4684
		private decimal? nullable_5;

		// Token: 0x0400124D RID: 4685
		private int? nullable_6;
	}
}
