﻿using System;

namespace TEx
{
	// Token: 0x020001F7 RID: 503
	public sealed class QuickWndItem
	{
		// Token: 0x170002F5 RID: 757
		// (get) Token: 0x060013AF RID: 5039 RVA: 0x00088748 File Offset: 0x00086948
		// (set) Token: 0x060013B0 RID: 5040 RVA: 0x00007FB6 File Offset: 0x000061B6
		public string Code
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002F6 RID: 758
		// (get) Token: 0x060013B1 RID: 5041 RVA: 0x00088760 File Offset: 0x00086960
		// (set) Token: 0x060013B2 RID: 5042 RVA: 0x00007FC1 File Offset: 0x000061C1
		public string Description
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x170002F7 RID: 759
		// (get) Token: 0x060013B3 RID: 5043 RVA: 0x00088778 File Offset: 0x00086978
		// (set) Token: 0x060013B4 RID: 5044 RVA: 0x00007FCC File Offset: 0x000061CC
		public string HidenCode
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x170002F8 RID: 760
		// (get) Token: 0x060013B5 RID: 5045 RVA: 0x00088790 File Offset: 0x00086990
		// (set) Token: 0x060013B6 RID: 5046 RVA: 0x00007FD7 File Offset: 0x000061D7
		public object LinkObj
		{
			get
			{
				return this.object_0;
			}
			set
			{
				this.object_0 = value;
			}
		}

		// Token: 0x04000A3F RID: 2623
		private string string_0;

		// Token: 0x04000A40 RID: 2624
		private string string_1;

		// Token: 0x04000A41 RID: 2625
		private string string_2;

		// Token: 0x04000A42 RID: 2626
		private object object_0;
	}
}
