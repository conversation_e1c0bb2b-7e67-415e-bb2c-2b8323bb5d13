﻿using System;
using System.Net;
using System.Web.Services;
using System.Web.Services.Protocols;
using ns18;
using ns30;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000408 RID: 1032
	[WebServiceBinding(Name = "LoginServiceSoap", Namespace = "http://www.smartassembly.com/webservices/UploadReportLogin/")]
	internal sealed class UploadReportLoginService : SoapHttpClientProtocol
	{
		// Token: 0x06002809 RID: 10249 RVA: 0x0000F7FF File Offset: 0x0000D9FF
		public UploadReportLoginService()
		{
			base.Url = Class546.string_0 + Class521.smethod_0(119290);
			base.Timeout = 30000;
		}

		// Token: 0x0600280A RID: 10250 RVA: 0x0000F82C File Offset: 0x0000DA2C
		[SoapDocumentMethod("http://www.smartassembly.com/webservices/UploadReportLogin/GetServerURL")]
		public string GetServerURL(string licenseID)
		{
			return (string)base.Invoke(Class521.smethod_0(119323), new object[]
			{
				licenseID
			})[0];
		}

		// Token: 0x0600280B RID: 10251 RVA: 0x0010D560 File Offset: 0x0010B760
		protected override WebRequest GetWebRequest(Uri uri)
		{
			WebRequest webRequest = base.GetWebRequest(uri);
			HttpWebRequest httpWebRequest = webRequest as HttpWebRequest;
			if (httpWebRequest != null)
			{
				httpWebRequest.ServicePoint.Expect100Continue = false;
			}
			return webRequest;
		}
	}
}
