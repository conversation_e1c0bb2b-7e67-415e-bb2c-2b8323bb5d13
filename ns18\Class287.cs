﻿using System;
using System.Data;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns12;
using ns14;

namespace ns18
{
	// Token: 0x0200020A RID: 522
	internal sealed class Class287 : Class285
	{
		// Token: 0x0600155F RID: 5471 RVA: 0x00091F00 File Offset: 0x00090100
		protected override void Class285_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			base.Class285_CellFormatting(sender, e);
			DataGridView dataGridView = sender as DataGridView;
			if (e.Value != null && !this.vmethod_4(dataGridView, e))
			{
				e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
			}
			if (dataGridView.Rows[e.RowIndex].Selected)
			{
				e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
			}
		}

		// Token: 0x06001560 RID: 5472 RVA: 0x00091F74 File Offset: 0x00090174
		protected override bool vmethod_4(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return dataGridViewCellFormattingEventArgs_0.ColumnIndex > 0;
		}

		// Token: 0x06001561 RID: 5473 RVA: 0x0000894A File Offset: 0x00006B4A
		protected override void vmethod_0()
		{
			base.vmethod_0();
		}

		// Token: 0x06001562 RID: 5474 RVA: 0x00091F90 File Offset: 0x00090190
		protected override void vmethod_3()
		{
			base.Columns[0].FillWeight = 175f;
			foreach (object obj in base.Columns)
			{
				((DataGridViewColumn)obj).SortMode = DataGridViewColumnSortMode.NotSortable;
			}
			base.vmethod_3();
		}

		// Token: 0x17000383 RID: 899
		// (get) Token: 0x06001563 RID: 5475 RVA: 0x00092008 File Offset: 0x00090208
		// (set) Token: 0x06001564 RID: 5476 RVA: 0x00008954 File Offset: 0x00006B54
		public Enum2 ReportAnlysType { get; set; }

		// Token: 0x17000384 RID: 900
		// (get) Token: 0x06001565 RID: 5477 RVA: 0x00092020 File Offset: 0x00090220
		// (set) Token: 0x06001566 RID: 5478 RVA: 0x0000895F File Offset: 0x00006B5F
		public DataTable SrcFnDataTable { get; set; }

		// Token: 0x06001567 RID: 5479 RVA: 0x0000896A File Offset: 0x00006B6A
		public Class287() : base(false)
		{
		}

		// Token: 0x04000B02 RID: 2818
		[CompilerGenerated]
		private Enum2 enum2_0;

		// Token: 0x04000B03 RID: 2819
		[CompilerGenerated]
		private DataTable dataTable_0;
	}
}
