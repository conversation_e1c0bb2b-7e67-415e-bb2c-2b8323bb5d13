﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using ns0;
using ns11;
using ns18;
using ns20;
using ns28;
using ns3;
using ns7;
using ns8;
using TEx.Trading;

namespace TEx.ImportTrans
{
	// Token: 0x02000365 RID: 869
	internal static class CfmmcRecImporter
	{
		// Token: 0x140000B0 RID: 176
		// (add) Token: 0x06002432 RID: 9266 RVA: 0x000FD0E0 File Offset: 0x000FB2E0
		// (remove) Token: 0x06002433 RID: 9267 RVA: 0x000FD118 File Offset: 0x000FB318
		public static event EventHandler LoggingIn
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002434 RID: 9268 RVA: 0x0000E21F File Offset: 0x0000C41F
		private static void smethod_0()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_0);
		}

		// Token: 0x140000B1 RID: 177
		// (add) Token: 0x06002435 RID: 9269 RVA: 0x000FD150 File Offset: 0x000FB350
		// (remove) Token: 0x06002436 RID: 9270 RVA: 0x000FD188 File Offset: 0x000FB388
		public static event EventHandler LoginSuccess
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002437 RID: 9271 RVA: 0x0000E22D File Offset: 0x0000C42D
		private static void smethod_1()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_1);
		}

		// Token: 0x140000B2 RID: 178
		// (add) Token: 0x06002438 RID: 9272 RVA: 0x000FD1C0 File Offset: 0x000FB3C0
		// (remove) Token: 0x06002439 RID: 9273 RVA: 0x000FD1F8 File Offset: 0x000FB3F8
		public static event EventHandler IdPswdCheckFailed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600243A RID: 9274 RVA: 0x0000E23B File Offset: 0x0000C43B
		private static void smethod_2()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_2);
		}

		// Token: 0x140000B3 RID: 179
		// (add) Token: 0x0600243B RID: 9275 RVA: 0x000FD230 File Offset: 0x000FB430
		// (remove) Token: 0x0600243C RID: 9276 RVA: 0x000FD268 File Offset: 0x000FB468
		public static event Delegate29 RecImportSuccess
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600243D RID: 9277 RVA: 0x000FD2A0 File Offset: 0x000FB4A0
		private static void smethod_3(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_0(null, e);
		}

		// Token: 0x140000B4 RID: 180
		// (add) Token: 0x0600243E RID: 9278 RVA: 0x000FD2C4 File Offset: 0x000FB4C4
		// (remove) Token: 0x0600243F RID: 9279 RVA: 0x000FD2FC File Offset: 0x000FB4FC
		public static event Delegate29 RecImportFailed
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002440 RID: 9280 RVA: 0x000FD334 File Offset: 0x000FB534
		private static void smethod_4(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_1(null, e);
		}

		// Token: 0x06002441 RID: 9281 RVA: 0x0000E249 File Offset: 0x0000C449
		private static void smethod_5(EventArgs25 eventArgs25_0)
		{
			if (eventArgs25_0 != null)
			{
				CfmmcRecImporter.smethod_4(eventArgs25_0.TotalRecs, eventArgs25_0.Result, eventArgs25_0.Msg);
			}
		}

		// Token: 0x140000B5 RID: 181
		// (add) Token: 0x06002442 RID: 9282 RVA: 0x000FD358 File Offset: 0x000FB558
		// (remove) Token: 0x06002443 RID: 9283 RVA: 0x000FD390 File Offset: 0x000FB590
		public static event EventHandler NoRecNeedToDnldDetected
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002444 RID: 9284 RVA: 0x0000E267 File Offset: 0x0000C467
		private static void smethod_6()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_3);
		}

		// Token: 0x140000B6 RID: 182
		// (add) Token: 0x06002445 RID: 9285 RVA: 0x000FD3C8 File Offset: 0x000FB5C8
		// (remove) Token: 0x06002446 RID: 9286 RVA: 0x000FD400 File Offset: 0x000FB600
		public static event Delegate29 NotifyDnRecIndex
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_2;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_2, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_2;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_2, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002447 RID: 9287 RVA: 0x000FD438 File Offset: 0x000FB638
		private static void smethod_7(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_2(null, e);
		}

		// Token: 0x06002448 RID: 9288 RVA: 0x0000E275 File Offset: 0x0000C475
		private static void smethod_8(EventArgs25 eventArgs25_0)
		{
			CfmmcRecImporter.smethod_7(eventArgs25_0.TotalRecs, eventArgs25_0.Result, eventArgs25_0.Msg);
		}

		// Token: 0x140000B7 RID: 183
		// (add) Token: 0x06002449 RID: 9289 RVA: 0x000FD45C File Offset: 0x000FB65C
		// (remove) Token: 0x0600244A RID: 9290 RVA: 0x000FD494 File Offset: 0x000FB694
		public static event Delegate29 StartDownloadRecs
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_3;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_3, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_3;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_3, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600244B RID: 9291 RVA: 0x000FD4CC File Offset: 0x000FB6CC
		private static void smethod_9(int int_2)
		{
			EventArgs25 e = new EventArgs25(int_2, true, string.Empty);
			CfmmcRecImporter.delegate29_3(new object(), e);
		}

		// Token: 0x140000B8 RID: 184
		// (add) Token: 0x0600244C RID: 9292 RVA: 0x000FD4F8 File Offset: 0x000FB6F8
		// (remove) Token: 0x0600244D RID: 9293 RVA: 0x000FD530 File Offset: 0x000FB730
		public static event EventHandler CfmmcAcctsAsynDownloaded
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600244E RID: 9294 RVA: 0x0000E290 File Offset: 0x0000C490
		private static void smethod_10()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_4);
		}

		// Token: 0x0600244F RID: 9295 RVA: 0x00031150 File Offset: 0x0002F350
		private static void smethod_11(EventHandler eventHandler_5)
		{
			EventArgs e = new EventArgs();
			if (eventHandler_5 != null)
			{
				eventHandler_5(null, e);
			}
		}

		// Token: 0x06002450 RID: 9296 RVA: 0x000FD568 File Offset: 0x000FB768
		static CfmmcRecImporter()
		{
			CfmmcRecImporter.timer_0.Elapsed += CfmmcRecImporter.smethod_14;
		}

		// Token: 0x06002451 RID: 9297 RVA: 0x0000E29E File Offset: 0x0000C49E
		public static void smethod_12()
		{
			CfmmcRecImporter.smethod_13();
			CfmmcRecImporter.timer_0.Start();
		}

		// Token: 0x06002452 RID: 9298 RVA: 0x000FD5DC File Offset: 0x000FB7DC
		private static void smethod_13()
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (cfmmcAutoDnldConfig != null)
			{
				DateTime now = DateTime.Now;
				DateTime dateTime;
				if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
				{
					dateTime = now.Date.Add(cfmmcAutoDnldConfig.BeginTime.TimeOfDay);
					if (dateTime < now)
					{
						dateTime = dateTime.AddDays(1.0);
					}
				}
				else
				{
					int num = cfmmcAutoDnldConfig.WklyDnldDayOfWeek - now.DayOfWeek;
					if (num < 0)
					{
						num += 7;
					}
					dateTime = now.Date.AddDays((double)num).Add(cfmmcAutoDnldConfig.BeginTime.TimeOfDay);
					if (dateTime < now)
					{
						dateTime = dateTime.AddDays(7.0);
					}
				}
				double totalMilliseconds = (dateTime - now).TotalMilliseconds;
				CfmmcRecImporter.timer_0.Interval = (double)Convert.ToInt32(totalMilliseconds);
			}
		}

		// Token: 0x06002453 RID: 9299 RVA: 0x000FD6CC File Offset: 0x000FB8CC
		private static void smethod_14(object sender, ElapsedEventArgs e)
		{
			Class481 @class = new Class481(Base.UI.Form.CfmmcAutoDnldConfig);
			CfmmcRecImporter.timer_0.Enabled = @class.TimerEnabled;
			if (@class.TimerInterval > 0)
			{
				CfmmcRecImporter.timer_0.Interval = (double)@class.TimerInterval;
			}
			if (@class.DownEnable)
			{
				CfmmcRecImporter.smethod_37();
			}
			CfmmcRecImporter.smethod_13();
		}

		// Token: 0x06002454 RID: 9300 RVA: 0x000FD728 File Offset: 0x000FB928
		private static void smethod_15(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.RecDnSuccess += CfmmcRecImporter.smethod_23;
			cfmmcWebDnloader_0.RecDnFailed += CfmmcRecImporter.smethod_17;
			cfmmcWebDnloader_0.IdPswdCheckFailed += CfmmcRecImporter.smethod_18;
			cfmmcWebDnloader_0.NotifyDnRecIndex += CfmmcRecImporter.smethod_19;
			cfmmcWebDnloader_0.LoggingIn += CfmmcRecImporter.smethod_20;
			cfmmcWebDnloader_0.LoginSuccess += CfmmcRecImporter.smethod_21;
		}

		// Token: 0x06002455 RID: 9301 RVA: 0x000FD7A4 File Offset: 0x000FB9A4
		private static void smethod_16(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.RecDnSuccess -= CfmmcRecImporter.smethod_23;
			cfmmcWebDnloader_0.RecDnFailed -= CfmmcRecImporter.smethod_17;
			cfmmcWebDnloader_0.IdPswdCheckFailed -= CfmmcRecImporter.smethod_18;
			cfmmcWebDnloader_0.NotifyDnRecIndex -= CfmmcRecImporter.smethod_19;
			cfmmcWebDnloader_0.LoggingIn -= CfmmcRecImporter.smethod_20;
			cfmmcWebDnloader_0.LoginSuccess -= CfmmcRecImporter.smethod_21;
		}

		// Token: 0x06002456 RID: 9302 RVA: 0x0000E2B1 File Offset: 0x0000C4B1
		private static void smethod_17(object sender, EventArgs25 e)
		{
			CfmmcRecImporter.smethod_5(e);
			CfmmcWebDnloader cfmmcWebDnloader = sender as CfmmcWebDnloader;
			CfmmcRecImporter.smethod_41(cfmmcWebDnloader);
			cfmmcWebDnloader.method_2();
		}

		// Token: 0x06002457 RID: 9303 RVA: 0x0000E2CD File Offset: 0x0000C4CD
		private static void smethod_18(object sender, EventArgs26 e)
		{
			CfmmcRecImporter.smethod_2();
			CfmmcRecImporter.smethod_22(e.UsrId, e.Password);
		}

		// Token: 0x06002458 RID: 9304 RVA: 0x0000E2E7 File Offset: 0x0000C4E7
		private static void smethod_19(object sender, EventArgs25 e)
		{
			CfmmcRecImporter.smethod_8(e);
		}

		// Token: 0x06002459 RID: 9305 RVA: 0x0000E2F1 File Offset: 0x0000C4F1
		private static void smethod_20(object sender, EventArgs e)
		{
			CfmmcRecImporter.smethod_0();
		}

		// Token: 0x0600245A RID: 9306 RVA: 0x0000E2FA File Offset: 0x0000C4FA
		private static void smethod_21(object sender, EventArgs e)
		{
			CfmmcRecImporter.smethod_1();
		}

		// Token: 0x0600245B RID: 9307 RVA: 0x000FD820 File Offset: 0x000FBA20
		private static void smethod_22(string string_3, string string_4)
		{
			CWrongUserName istoreElement_ = new CWrongUserName(string_3, string_4, DateTime.Now);
			((Interface4)new CWrongNameStore()).imethod_0(istoreElement_);
		}

		// Token: 0x0600245C RID: 9308 RVA: 0x000FD848 File Offset: 0x000FBA48
		private static void smethod_23(object sender, EventArgs e)
		{
			int num = 0;
			try
			{
				CfmmcWebDnloader cfmmcWebDnloader = sender as CfmmcWebDnloader;
				CfmmcRecImporter.smethod_24(cfmmcWebDnloader.RecordList, cfmmcWebDnloader.UserName);
				CfmmcRecImporter.smethod_25(cfmmcWebDnloader);
				num = cfmmcWebDnloader.RecordList.Count;
				string string_;
				if (num > 0)
				{
					string_ = string.Format(Class521.smethod_0(107947), num);
				}
				else
				{
					string_ = Class521.smethod_0(108032);
				}
				CfmmcRecImporter.smethod_3(num, true, string_);
				CfmmcRecImporter.smethod_41(cfmmcWebDnloader);
				cfmmcWebDnloader.method_2();
			}
			catch (Exception ex)
			{
				Class470.smethod_1(ex.Message);
				CfmmcRecImporter.smethod_4(num, false, ex.Message);
			}
		}

		// Token: 0x0600245D RID: 9309 RVA: 0x0000E303 File Offset: 0x0000C503
		private static void smethod_24(List<List<string>> list_2, string string_3)
		{
			if (list_2 != null && list_2.Any<List<string>>())
			{
				Class466.smethod_14(list_2, CfmmcRecImporter.CfmmcRecFileLocalPath, string_3);
			}
		}

		// Token: 0x0600245E RID: 9310 RVA: 0x000FD8EC File Offset: 0x000FBAEC
		public static void smethod_25(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			Class451 @class = CfmmcRecImporter.smethod_26(cfmmcWebDnloader_0);
			if (@class.HasData)
			{
				List<Transaction> list = CfmmcRecImporter.smethod_27(@class);
				if (list != null && list.Any<Transaction>())
				{
					CfmmcAcct cfmmcAcct = Class466.smethod_11(cfmmcWebDnloader_0.UserName);
					cfmmcAcct.EndDate = new DateTime?(@class.Data.Max(new Func<TransData, DateTime>(CfmmcRecImporter.<>c.<>9.method_0)));
					DateTime dateTime = list.Min(new Func<Transaction, DateTime>(CfmmcRecImporter.<>c.<>9.method_1));
					if (dateTime < cfmmcAcct.BeginDate)
					{
						cfmmcAcct.BeginDate = new DateTime?(dateTime);
					}
					cfmmcAcct.LastDownloadTime = new DateTime?(DateTime.Now);
					Class466.smethod_0(cfmmcAcct);
				}
			}
		}

		// Token: 0x0600245F RID: 9311 RVA: 0x000FD9D8 File Offset: 0x000FBBD8
		public static Class451 smethod_26(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			Class451 @class = new Class451(TransData.string_0, cfmmcWebDnloader_0.UserName);
			for (int i = 0; i < cfmmcWebDnloader_0.RecordList.Count; i++)
			{
				TransData item = new TransData(cfmmcWebDnloader_0.RecordList[i], cfmmcWebDnloader_0.UserName);
				@class.Data.Add(item);
			}
			return @class;
		}

		// Token: 0x06002460 RID: 9312 RVA: 0x000FDA38 File Offset: 0x000FBC38
		public static List<Transaction> smethod_27(Class451 class451_0)
		{
			CfmmcAcct cfmmcAcct = Class466.smethod_11(class451_0.CfmmcAcctID);
			List<Transaction> list = TransFileImporter.smethod_5(class451_0);
			for (int i = 0; i < cfmmcAcct.BindingAccts.Count; i++)
			{
				TransFileImporter.smethod_2(list, cfmmcAcct.BindingAccts[i].Id);
			}
			return list;
		}

		// Token: 0x06002461 RID: 9313 RVA: 0x000FDA8C File Offset: 0x000FBC8C
		[Obsolete]
		private static CfmmcAcct smethod_28(Account account_0, CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			CfmmcRecImporter.Class472 @class = new CfmmcRecImporter.Class472();
			@class.account_0 = account_0;
			CfmmcAcct cfmmcAcct = new CfmmcAcct();
			cfmmcAcct.ID = cfmmcWebDnloader_0.UserName;
			cfmmcAcct.Password = cfmmcWebDnloader_0.PassWord;
			if (cfmmcWebDnloader_0.RecordList.Any<List<string>>())
			{
				cfmmcAcct.BeginDate = new DateTime?(Convert.ToDateTime(cfmmcWebDnloader_0.RecordList.First<List<string>>()[12]));
			}
			cfmmcAcct.EndDate = new DateTime?(DateTime.Now);
			try
			{
				cfmmcAcct.BindingAccts.Single(new Func<BindingAcct, bool>(@class.method_0));
			}
			catch
			{
				BindingAcct bindingAcct = new BindingAcct();
				bindingAcct.UsrName = TApp.UserName;
				bindingAcct.Id = @class.account_0.ID;
				bindingAcct.BeginDate = null;
				bindingAcct.EndDate = null;
				if (cfmmcAcct.BindingAccts == null)
				{
					List<BindingAcct> bindingAccts = new List<BindingAcct>();
					cfmmcAcct.BindingAccts = bindingAccts;
					cfmmcAcct.BindingAccts.Add(bindingAcct);
				}
				else
				{
					cfmmcAcct.BindingAccts.Add(bindingAcct);
				}
			}
			return cfmmcAcct;
		}

		// Token: 0x06002462 RID: 9314 RVA: 0x000FDBA8 File Offset: 0x000FBDA8
		public static bool smethod_29(CWrongUserName cwrongUserName_0)
		{
			bool result;
			if (MessageBox.Show(string.Format(Class521.smethod_0(108101), cwrongUserName_0.string_0, cwrongUserName_0.string_1, cwrongUserName_0.dateTime_0), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002463 RID: 9315 RVA: 0x000FDBFC File Offset: 0x000FBDFC
		private static bool smethod_30(string string_3, string string_4)
		{
			Interface4 @interface = new CWrongNameStore();
			CWrongUserName cwrongUserName = new CWrongUserName(string_3, string_4, DateTime.Now);
			bool result;
			if (@interface.imethod_4(string_3 + string_4) != null)
			{
				if (CfmmcRecImporter.smethod_29(cwrongUserName))
				{
					@interface.imethod_2(cwrongUserName);
					result = true;
				}
				else
				{
					result = false;
				}
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06002464 RID: 9316 RVA: 0x000FDC48 File Offset: 0x000FBE48
		public static bool smethod_31(CfmmcAcct cfmmcAcct_0)
		{
			CfmmcWebDnloader cfmmcWebDnloader = new CfmmcWebDnloader(cfmmcAcct_0);
			cfmmcWebDnloader.IsInBackground = false;
			cfmmcWebDnloader.method_1();
			CfmmcRecImporter.smethod_15(cfmmcWebDnloader);
			CfmmcRecImporter.list_0.Add(cfmmcWebDnloader);
			return CfmmcRecImporter.smethod_32(cfmmcWebDnloader, true);
		}

		// Token: 0x06002465 RID: 9317 RVA: 0x000FDC88 File Offset: 0x000FBE88
		public static bool smethod_32(CfmmcWebDnloader cfmmcWebDnloader_0, bool bool_1)
		{
			bool result;
			if (!CfmmcRecImporter.smethod_30(cfmmcWebDnloader_0.UserName, cfmmcWebDnloader_0.PassWord))
			{
				result = false;
			}
			else
			{
				result = CfmmcRecImporter.smethod_33(cfmmcWebDnloader_0, bool_1);
			}
			return result;
		}

		// Token: 0x06002466 RID: 9318 RVA: 0x000FDCB8 File Offset: 0x000FBEB8
		private static bool smethod_33(CfmmcWebDnloader cfmmcWebDnloader_0, bool bool_1)
		{
			if (bool_1)
			{
				if (!cfmmcWebDnloader_0.IsDnldNeeded)
				{
					CfmmcRecImporter.smethod_6();
					CfmmcRecImporter.smethod_16(cfmmcWebDnloader_0);
					cfmmcWebDnloader_0.method_2();
					return false;
				}
				CfmmcRecImporter.smethod_9(cfmmcWebDnloader_0.DnDayList.Count);
				cfmmcWebDnloader_0.method_4();
			}
			else
			{
				cfmmcWebDnloader_0.method_7();
				CfmmcRecImporter.smethod_9(cfmmcWebDnloader_0.DnDayList.Count);
				cfmmcWebDnloader_0.method_4();
			}
			return true;
		}

		// Token: 0x06002467 RID: 9319 RVA: 0x000FDD20 File Offset: 0x000FBF20
		public static string smethod_34(string string_3)
		{
			return Path.Combine(TApp.UserAcctFolder, string_3);
		}

		// Token: 0x17000634 RID: 1588
		// (get) Token: 0x06002468 RID: 9320 RVA: 0x000FDD3C File Offset: 0x000FBF3C
		public static string CfmmcRecFileLocalPath
		{
			get
			{
				return CfmmcRecImporter.smethod_34(CfmmcRecImporter.string_0);
			}
		}

		// Token: 0x17000635 RID: 1589
		// (get) Token: 0x06002469 RID: 9321 RVA: 0x000FDD58 File Offset: 0x000FBF58
		// (set) Token: 0x0600246A RID: 9322 RVA: 0x0000E320 File Offset: 0x0000C520
		public static int IEMajor { get; set; }

		// Token: 0x0600246B RID: 9323 RVA: 0x000FDD70 File Offset: 0x000FBF70
		private static bool smethod_35()
		{
			bool result;
			try
			{
				if (CfmmcRecImporter.IEMajor <= 6)
				{
					result = false;
					goto IL_D5;
				}
			}
			catch (Exception)
			{
				result = false;
				goto IL_D5;
			}
			List<CfmmcAcct> list = Class466.smethod_9();
			if (!list.Any<CfmmcAcct>())
			{
				return false;
			}
			IEnumerable<CfmmcAcct> source = list.Where(new Func<CfmmcAcct, bool>(CfmmcRecImporter.<>c.<>9.method_2));
			if (!source.Any<CfmmcAcct>())
			{
				return false;
			}
			list = source.ToList<CfmmcAcct>();
			CfmmcRecImporter.list_1 = new List<CfmmcAcct>();
			foreach (CfmmcAcct cfmmcAcct in list)
			{
				if (new CfmmcWebDnloader(cfmmcAcct).IsDnldNeeded)
				{
					CfmmcRecImporter.list_1.Add(cfmmcAcct);
				}
			}
			if (!CfmmcRecImporter.list_1.Any<CfmmcAcct>())
			{
				return false;
			}
			CfmmcRecImporter.int_0 = 0;
			return true;
			IL_D5:
			return result;
		}

		// Token: 0x0600246C RID: 9324 RVA: 0x000FDE78 File Offset: 0x000FC078
		[Obsolete]
		private static bool smethod_36(CfmmcAcct cfmmcAcct_0)
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			bool result;
			if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
			{
				if (cfmmcAcct_0.EndDate != null)
				{
					if ((cfmmcAcct_0.EndDate.Value - DateTime.Now).Days < 1)
					{
						result = true;
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
			}
			else if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每周)
			{
				DateTime dateTime = DateTime.Now;
				while (dateTime.DayOfWeek != DayOfWeek.Saturday)
				{
					dateTime = dateTime.AddDays(-1.0);
				}
				if (cfmmcAcct_0.EndDate != null)
				{
					if (cfmmcAcct_0.EndDate.Value.Date < dateTime.Date)
					{
						result = true;
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600246D RID: 9325 RVA: 0x0000E32A File Offset: 0x0000C52A
		public static void smethod_37()
		{
			if (CfmmcRecImporter.smethod_35())
			{
				if (CfmmcRecImporter.list_1 != null && CfmmcRecImporter.list_1.Any<CfmmcAcct>())
				{
					CfmmcRecImporter.int_0 = 0;
					CfmmcRecImporter.smethod_38(CfmmcRecImporter.int_0);
				}
			}
		}

		// Token: 0x0600246E RID: 9326 RVA: 0x0000E35A File Offset: 0x0000C55A
		private static void smethod_38(int int_2)
		{
			CfmmcRecImporter.smethod_39(CfmmcRecImporter.list_1[int_2]);
		}

		// Token: 0x0600246F RID: 9327 RVA: 0x000FDF54 File Offset: 0x000FC154
		public static bool smethod_39(CfmmcAcct cfmmcAcct_0)
		{
			Class469 parameter = new Class469(cfmmcAcct_0, cfmmcAcct_0.EndDate);
			Thread thread = new Thread(new ParameterizedThreadStart(CfmmcRecImporter.smethod_40));
			thread.IsBackground = true;
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start(parameter);
			return true;
		}

		// Token: 0x06002470 RID: 9328 RVA: 0x000FDF98 File Offset: 0x000FC198
		private static void smethod_40(object object_0)
		{
			CfmmcWebDnloader cfmmcWebDnloader = new CfmmcWebDnloader((object_0 as Class469).cfmmcAcct_0);
			cfmmcWebDnloader.IsInBackground = true;
			cfmmcWebDnloader.method_1();
			CfmmcRecImporter.smethod_15(cfmmcWebDnloader);
			if (CfmmcRecImporter.smethod_32(cfmmcWebDnloader, true))
			{
				CfmmcRecImporter.bool_0 = true;
				while (CfmmcRecImporter.bool_0)
				{
					Thread.Sleep(1);
					Application.DoEvents();
				}
			}
		}

		// Token: 0x06002471 RID: 9329 RVA: 0x000FDFF0 File Offset: 0x000FC1F0
		private static bool smethod_41(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			CfmmcRecImporter.bool_0 = false;
			if (cfmmcWebDnloader_0 != null)
			{
				CfmmcRecImporter.smethod_42(cfmmcWebDnloader_0);
			}
			bool result;
			if (CfmmcRecImporter.int_0 >= 0)
			{
				Thread.Sleep(100);
				CfmmcRecImporter.int_0++;
				if (CfmmcRecImporter.int_0 < CfmmcRecImporter.list_1.Count)
				{
					CfmmcRecImporter.smethod_38(CfmmcRecImporter.int_0);
					result = true;
				}
				else
				{
					CfmmcRecImporter.smethod_10();
					CfmmcRecImporter.int_0 = -1;
					result = false;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002472 RID: 9330 RVA: 0x0000E36F File Offset: 0x0000C56F
		public static void smethod_42(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.method_3();
			CfmmcRecImporter.smethod_16(cfmmcWebDnloader_0);
			cfmmcWebDnloader_0.method_2();
		}

		// Token: 0x06002473 RID: 9331 RVA: 0x0000E385 File Offset: 0x0000C585
		public static void smethod_43()
		{
			CfmmcRecImporter.smethod_44(false);
		}

		// Token: 0x06002474 RID: 9332 RVA: 0x000FE05C File Offset: 0x000FC25C
		public static void smethod_44(bool bool_1)
		{
			for (int i = 0; i < CfmmcRecImporter.list_0.Count; i++)
			{
				CfmmcWebDnloader cfmmcWebDnloader = CfmmcRecImporter.list_0[i];
				if (cfmmcWebDnloader != null && (!bool_1 || (bool_1 && !cfmmcWebDnloader.IsInBackground)))
				{
					CfmmcRecImporter.smethod_16(cfmmcWebDnloader);
					cfmmcWebDnloader.method_2();
				}
			}
		}

		// Token: 0x17000636 RID: 1590
		// (get) Token: 0x06002475 RID: 9333 RVA: 0x000FE0AC File Offset: 0x000FC2AC
		// (set) Token: 0x06002476 RID: 9334 RVA: 0x0000E38F File Offset: 0x0000C58F
		public static bool IsDownloading
		{
			get
			{
				return CfmmcRecImporter.bool_0;
			}
			set
			{
				CfmmcRecImporter.bool_0 = value;
			}
		}

		// Token: 0x0400118B RID: 4491
		internal static readonly string string_0 = Class521.smethod_0(107876);

		// Token: 0x0400118C RID: 4492
		internal static readonly string string_1 = Class521.smethod_0(107905);

		// Token: 0x0400118D RID: 4493
		internal static readonly string string_2 = Class521.smethod_0(107930);

		// Token: 0x0400118E RID: 4494
		private static System.Timers.Timer timer_0 = new System.Timers.Timer();

		// Token: 0x0400118F RID: 4495
		private static List<CfmmcWebDnloader> list_0 = new List<CfmmcWebDnloader>();

		// Token: 0x04001190 RID: 4496
		private static int int_0 = -1;

		// Token: 0x04001191 RID: 4497
		private static List<CfmmcAcct> list_1;

		// Token: 0x04001192 RID: 4498
		[CompilerGenerated]
		private static EventHandler eventHandler_0;

		// Token: 0x04001193 RID: 4499
		[CompilerGenerated]
		private static EventHandler eventHandler_1;

		// Token: 0x04001194 RID: 4500
		[CompilerGenerated]
		private static EventHandler eventHandler_2;

		// Token: 0x04001195 RID: 4501
		[CompilerGenerated]
		private static Delegate29 delegate29_0;

		// Token: 0x04001196 RID: 4502
		[CompilerGenerated]
		private static Delegate29 delegate29_1;

		// Token: 0x04001197 RID: 4503
		[CompilerGenerated]
		private static EventHandler eventHandler_3;

		// Token: 0x04001198 RID: 4504
		[CompilerGenerated]
		private static Delegate29 delegate29_2;

		// Token: 0x04001199 RID: 4505
		[CompilerGenerated]
		private static Delegate29 delegate29_3;

		// Token: 0x0400119A RID: 4506
		[CompilerGenerated]
		private static EventHandler eventHandler_4;

		// Token: 0x0400119B RID: 4507
		[CompilerGenerated]
		private static int int_1;

		// Token: 0x0400119C RID: 4508
		private static bool bool_0 = false;

		// Token: 0x02000367 RID: 871
		[CompilerGenerated]
		private sealed class Class472
		{
			// Token: 0x0600247E RID: 9342 RVA: 0x000FE144 File Offset: 0x000FC344
			internal bool method_0(BindingAcct bindingAcct_0)
			{
				return bindingAcct_0.Id == this.account_0.ID;
			}

			// Token: 0x040011A2 RID: 4514
			public Account account_0;
		}
	}
}
