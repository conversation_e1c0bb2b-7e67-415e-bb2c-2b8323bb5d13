﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using System.Xml.Linq;
using ns12;
using ns13;
using ns18;
using ns26;
using ns30;
using ns4;
using ns9;
using TEx;

namespace ns22
{
	// Token: 0x02000179 RID: 377
	internal sealed partial class Form14 : Form
	{
		// Token: 0x14000075 RID: 117
		// (add) Token: 0x06000E2B RID: 3627 RVA: 0x0005CD08 File Offset: 0x0005AF08
		// (remove) Token: 0x06000E2C RID: 3628 RVA: 0x0005CD40 File Offset: 0x0005AF40
		public event EventHandler HotKeyCfgChanging
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000E2D RID: 3629 RVA: 0x0005CD78 File Offset: 0x0005AF78
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000076 RID: 118
		// (add) Token: 0x06000E2E RID: 3630 RVA: 0x0005CDA0 File Offset: 0x0005AFA0
		// (remove) Token: 0x06000E2F RID: 3631 RVA: 0x0005CDD8 File Offset: 0x0005AFD8
		public event EventHandler HotKeyCfgChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000E30 RID: 3632 RVA: 0x0005CE10 File Offset: 0x0005B010
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06000E31 RID: 3633 RVA: 0x0005CE38 File Offset: 0x0005B038
		public Form14()
		{
			this.method_3();
			base.Load += this.Form14_Load;
			this.class308_0 = new Class308();
			this.class308_0.DialogResult = DialogResult.Cancel;
			this.class308_0.Name = Class521.smethod_0(25466);
			this.class308_0.TabIndex = this.button_0.TabIndex + 1;
			this.class308_0.Text = Class521.smethod_0(5783);
			this.class308_0.UseVisualStyleBackColor = true;
			this.class308_0.Visible = true;
			base.Controls.Add(this.class308_0);
			base.CancelButton = this.class308_0;
		}

		// Token: 0x06000E32 RID: 3634 RVA: 0x0005CEF4 File Offset: 0x0005B0F4
		private void Form14_Load(object sender, EventArgs e)
		{
			List<Class280> usrHotKeyList = Class210.UsrHotKeyList;
			List<Class266> usrFnKeyList = Class186.UsrFnKeyList;
			this.list_0 = new List<Control3>();
			int x = 20;
			int num = 5;
			for (int i = 0; i < usrHotKeyList.Count; i++)
			{
				Control3 control = new Control3(usrHotKeyList[i]);
				control.Location = new Point(x, num);
				this.panel_0.Controls.Add(control);
				this.list_0.Add(control);
				num += control.Height;
			}
			for (int j = 0; j < usrFnKeyList.Count; j++)
			{
				Control3 control2 = new Control3(usrFnKeyList[j]);
				control2.Location = new Point(x, num);
				this.panel_0.Controls.Add(control2);
				this.list_0.Add(control2);
				num += control2.Height;
			}
			foreach (Control3 control3 in this.list_0)
			{
				control3.InputBox.Leave += this.method_2;
			}
			this.class308_0.Size = this.button_0.Size;
			this.class308_0.Location = new Point(this.button_0.Location.X + this.button_0.Width + Convert.ToInt32(Math.Ceiling(this.button_0.Width / 8m)), this.button_0.Location.Y);
		}

		// Token: 0x06000E33 RID: 3635 RVA: 0x0005D0AC File Offset: 0x0005B2AC
		private void method_2(object sender, EventArgs e)
		{
			TextBox textBox = sender as TextBox;
			foreach (Control3 control in this.list_0)
			{
				string text = textBox.Text.Trim();
				if (control.InputBox != textBox && control.InputBox.Text.Trim() == text && text != string.Empty)
				{
					MessageBox.Show(Class521.smethod_0(27419), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					textBox.Focus();
					break;
				}
			}
		}

		// Token: 0x06000E34 RID: 3636 RVA: 0x0005D164 File Offset: 0x0005B364
		private void button_0_Click(object sender, EventArgs e)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement(Class521.smethod_0(12376));
			XElement xelement2 = new XElement(Class521.smethod_0(27321));
			XElement xelement3 = new XElement(Class521.smethod_0(12393));
			List<Class280> list = new List<Class280>();
			List<Class266> list2 = new List<Class266>();
			this.method_0();
			foreach (Control3 control in this.list_0)
			{
				if (control.IsHotKey)
				{
					XElement xelement4 = new XElement(Class521.smethod_0(27334));
					control.HotKey.KeyModifier = control.Modifier;
					control.HotKey.Key = control.Key;
					xelement4.SetAttributeValue(Class521.smethod_0(12411), control.HotKey.Id);
					xelement4.SetAttributeValue(Class521.smethod_0(27343), Convert.ToString((int)control.Modifier));
					xelement4.SetAttributeValue(Class521.smethod_0(27356), Convert.ToString((int)control.Key));
					xelement2.Add(xelement4);
					list.Add(control.HotKey);
				}
				else
				{
					XElement xelement5 = new XElement(Class521.smethod_0(12402));
					control.FnKey.KeyStr = control.FnKeyStr.Trim();
					xelement5.SetAttributeValue(Class521.smethod_0(12411), control.FnKey.Id);
					xelement5.SetAttributeValue(Class521.smethod_0(12416), control.FnKey.KeyStr);
					xelement3.Add(xelement5);
					list2.Add(control.FnKey);
				}
			}
			xelement.Add(xelement2);
			xelement.Add(xelement3);
			xdocument.Add(xelement);
			xdocument.Save(Base.UI.smethod_43());
			Class210.UsrHotKeyList = list;
			Class186.UsrFnKeyList = list2;
			Base.UI.smethod_164();
			this.method_1();
			base.Dispose();
		}

		// Token: 0x06000E35 RID: 3637 RVA: 0x000064BA File Offset: 0x000046BA
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000E36 RID: 3638 RVA: 0x0005D3BC File Offset: 0x0005B5BC
		private void method_3()
		{
			this.button_0 = new Button();
			this.panel_0 = new Panel();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.pictureBox_0 = new PictureBox();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			base.SuspendLayout();
			this.button_0.Location = new Point(299, 372);
			this.button_0.Name = Class521.smethod_0(7442);
			this.button_0.Size = new Size(120, 30);
			this.button_0.TabIndex = 10;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.panel_0.AutoScroll = true;
			this.panel_0.BorderStyle = BorderStyle.FixedSingle;
			this.panel_0.Location = new Point(33, 39);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(519, 324);
			this.panel_0.TabIndex = 12;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(59, 16);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(45, 15);
			this.label_0.TabIndex = 21;
			this.label_0.Text = Class521.smethod_0(26092);
			this.label_1.Location = new Point(106, 16);
			this.label_1.Name = Class521.smethod_0(26071);
			this.label_1.Size = new Size(346, 20);
			this.label_1.TabIndex = 20;
			this.label_1.Text = Class521.smethod_0(27476);
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(36, 13);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 19;
			this.pictureBox_0.TabStop = false;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(588, 411);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.button_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(27549);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(27570);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000761 RID: 1889
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000762 RID: 1890
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000763 RID: 1891
		private List<Control3> list_0;

		// Token: 0x04000764 RID: 1892
		private Class308 class308_0;

		// Token: 0x04000765 RID: 1893
		private IContainer icontainer_0;

		// Token: 0x04000766 RID: 1894
		private Button button_0;

		// Token: 0x04000767 RID: 1895
		private Panel panel_0;

		// Token: 0x04000768 RID: 1896
		private Label label_0;

		// Token: 0x04000769 RID: 1897
		private Label label_1;

		// Token: 0x0400076A RID: 1898
		private PictureBox pictureBox_0;
	}
}
