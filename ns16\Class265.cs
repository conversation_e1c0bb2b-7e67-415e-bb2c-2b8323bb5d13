﻿using System;
using System.ComponentModel;
using System.IO;
using System.Timers;
using System.Xml;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Sources;
using ns18;
using ns21;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns16
{
	// Token: 0x020001DE RID: 478
	internal abstract class Class265
	{
		// Token: 0x0600129D RID: 4765 RVA: 0x00007C0C File Offset: 0x00005E0C
		public Class265()
		{
			this.timer_0.Interval = 1000.0;
			this.timer_0.Elapsed += new ElapsedEventHandler(this.timer_0_Elapsed);
		}

		// Token: 0x0600129E RID: 4766 RVA: 0x00007C41 File Offset: 0x00005E41
		public Class265(DatFileInfo datFileInfo_1) : this()
		{
			this.DFileInfo = datFileInfo_1;
		}

		// Token: 0x0600129F RID: 4767 RVA: 0x00007C52 File Offset: 0x00005E52
		public void method_0()
		{
			this.method_1(this.DFileInfo);
		}

		// Token: 0x060012A0 RID: 4768 RVA: 0x00083808 File Offset: 0x00081A08
		public object method_1(DatFileInfo datFileInfo_1)
		{
			this.IsUpdatingFile = true;
			this.DFileInfo = datFileInfo_1;
			this.FileObj = null;
			this.method_3();
			this.IsReadingFile = true;
			this.FileObj = this.vmethod_0();
			this.IsReadingFile = false;
			return this.FileObj;
		}

		// Token: 0x060012A1 RID: 4769 RVA: 0x00083854 File Offset: 0x00081A54
		public void method_2(DatFileInfo datFileInfo_1)
		{
			this.IsUpdatingFile = true;
			this.DFileInfo = datFileInfo_1;
			this.FileObj = null;
			if (this.backgroundWorker_0 == null)
			{
				if (!string.IsNullOrEmpty(this.FileFeedSrc))
				{
					this.backgroundWorker_0 = new BackgroundWorker();
					this.backgroundWorker_0.WorkerReportsProgress = true;
					this.backgroundWorker_0.WorkerSupportsCancellation = true;
					this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
					this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
					this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
					this.backgroundWorker_0.RunWorkerAsync(this.FileFeedSrc);
				}
			}
			else if (!this.backgroundWorker_0.IsBusy && !string.IsNullOrEmpty(this.FileFeedSrc))
			{
				this.backgroundWorker_0.RunWorkerAsync(this.FileFeedSrc);
			}
		}

		// Token: 0x060012A2 RID: 4770 RVA: 0x000041B9 File Offset: 0x000023B9
		private void timer_0_Elapsed(object sender, EventArgs e)
		{
		}

		// Token: 0x060012A3 RID: 4771 RVA: 0x0008393C File Offset: 0x00081B3C
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			string string_ = e.Argument.ToString();
			this.method_4(string_);
			e.Result = this.DFileInfo.FileName;
		}

		// Token: 0x060012A4 RID: 4772 RVA: 0x00007C63 File Offset: 0x00005E63
		private void method_3()
		{
			this.method_4(this.FileFeedSrc);
		}

		// Token: 0x060012A5 RID: 4773 RVA: 0x00083970 File Offset: 0x00081B70
		private void method_4(string string_1)
		{
			if (!string.IsNullOrEmpty(string_1))
			{
				UpdateManager instance = UpdateManager.Instance;
				instance.UpdateSource = new SimpleWebSource();
				instance.Config.TempFolder = Path.Combine(TApp.string_10, Class521.smethod_0(29061));
				instance.ReinstateIfRestarted();
				if (Class185.smethod_0(new MemorySource(string_1)) > 0)
				{
					try
					{
						instance.PrepareUpdates();
						instance.ApplyUpdates(false);
						instance.CleanUp();
					}
					catch (Exception)
					{
						instance.CleanUp();
						throw;
					}
				}
				this.IsUpdatingFile = false;
			}
		}

		// Token: 0x060012A6 RID: 4774 RVA: 0x00007C73 File Offset: 0x00005E73
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			int progressPercentage = e.ProgressPercentage;
		}

		// Token: 0x060012A7 RID: 4775 RVA: 0x00007C7E File Offset: 0x00005E7E
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			if (e.Error == null && !e.Cancelled && e.Result != null)
			{
				this.IsReadingFile = true;
				this.FileObj = this.vmethod_1(e.Result.ToString());
			}
		}

		// Token: 0x060012A8 RID: 4776 RVA: 0x00083A00 File Offset: 0x00081C00
		protected virtual object vmethod_0()
		{
			return this.vmethod_1(this.DFileInfo.FileName);
		}

		// Token: 0x060012A9 RID: 4777
		protected abstract object vmethod_1(string string_1);

		// Token: 0x060012AA RID: 4778 RVA: 0x00083A24 File Offset: 0x00081C24
		public string method_5(DatFileInfo datFileInfo_1)
		{
			XmlDocument xmlDocument = new XmlDocument();
			XmlDeclaration newChild = xmlDocument.CreateXmlDeclaration(Class521.smethod_0(11802), Class521.smethod_0(11807), null);
			string value = TApp.FULLHOST + datFileInfo_1.Path.Replace(Class521.smethod_0(1733), Class521.smethod_0(1449)).Replace(Class521.smethod_0(45611), Class521.smethod_0(24570));
			xmlDocument.AppendChild(newChild);
			XmlElement xmlElement = xmlDocument.CreateElement(Class521.smethod_0(11816));
			xmlElement.SetAttribute(Class521.smethod_0(11825), value);
			xmlDocument.AppendChild(xmlElement);
			XmlElement xmlElement2 = xmlDocument.CreateElement(Class521.smethod_0(11838));
			XmlElement xmlElement3 = xmlDocument.CreateElement(Class521.smethod_0(12561));
			xmlElement3.SetAttribute(Class521.smethod_0(12582), Class521.smethod_0(12595));
			xmlElement3.SetAttribute(Class521.smethod_0(12626), Class521.smethod_0(11789) + datFileInfo_1.FileName);
			xmlElement3.SetAttribute(Class521.smethod_0(12639), datFileInfo_1.FileSize.ToString());
			XmlElement xmlElement4 = xmlDocument.CreateElement(Class521.smethod_0(12652));
			XmlElement xmlElement5 = xmlDocument.CreateElement(Class521.smethod_0(12669));
			xmlElement5.SetAttribute(Class521.smethod_0(12698), Class521.smethod_0(12707));
			xmlElement4.AppendChild(xmlElement5);
			xmlElement5 = xmlDocument.CreateElement(Class521.smethod_0(12716));
			xmlElement5.SetAttribute(Class521.smethod_0(12698), Class521.smethod_0(12707));
			xmlElement5.SetAttribute(Class521.smethod_0(12741), Class521.smethod_0(12750));
			xmlElement5.SetAttribute(Class521.smethod_0(12755), datFileInfo_1.FileSize.ToString());
			xmlElement4.AppendChild(xmlElement5);
			xmlElement5 = xmlDocument.CreateElement(Class521.smethod_0(12764));
			xmlElement5.SetAttribute(Class521.smethod_0(12698), Class521.smethod_0(12707));
			xmlElement5.SetAttribute(Class521.smethod_0(12793), Class521.smethod_0(12810));
			xmlElement5.SetAttribute(Class521.smethod_0(12819), datFileInfo_1.SHA);
			xmlElement4.AppendChild(xmlElement5);
			xmlElement3.AppendChild(xmlElement4);
			xmlElement2.AppendChild(xmlElement3);
			xmlElement.AppendChild(xmlElement2);
			return Utility.ConvertXMLDocToString(xmlDocument);
		}

		// Token: 0x170002C9 RID: 713
		// (get) Token: 0x060012AB RID: 4779 RVA: 0x00083C90 File Offset: 0x00081E90
		// (set) Token: 0x060012AC RID: 4780 RVA: 0x00007CB8 File Offset: 0x00005EB8
		public DatFileInfo DFileInfo
		{
			get
			{
				return this.datFileInfo_0;
			}
			set
			{
				this.datFileInfo_0 = value;
				this.method_5(value);
			}
		}

		// Token: 0x170002CA RID: 714
		// (get) Token: 0x060012AD RID: 4781 RVA: 0x00083CA8 File Offset: 0x00081EA8
		// (set) Token: 0x060012AE RID: 4782 RVA: 0x00007CCB File Offset: 0x00005ECB
		public string FileFeedSrc
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002CB RID: 715
		// (get) Token: 0x060012AF RID: 4783 RVA: 0x00083CC0 File Offset: 0x00081EC0
		public bool IsBusy
		{
			get
			{
				bool result;
				if (!this.bool_0)
				{
					result = this.bool_1;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170002CC RID: 716
		// (get) Token: 0x060012B0 RID: 4784 RVA: 0x00083CE4 File Offset: 0x00081EE4
		// (set) Token: 0x060012B1 RID: 4785 RVA: 0x00007CD6 File Offset: 0x00005ED6
		public bool IsUpdatingFile
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170002CD RID: 717
		// (get) Token: 0x060012B2 RID: 4786 RVA: 0x00083CFC File Offset: 0x00081EFC
		// (set) Token: 0x060012B3 RID: 4787 RVA: 0x00007CE1 File Offset: 0x00005EE1
		public bool IsReadingFile
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x170002CE RID: 718
		// (get) Token: 0x060012B4 RID: 4788 RVA: 0x00083D14 File Offset: 0x00081F14
		// (set) Token: 0x060012B5 RID: 4789 RVA: 0x00007CEC File Offset: 0x00005EEC
		public int SecsToWait
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002CF RID: 719
		// (get) Token: 0x060012B6 RID: 4790 RVA: 0x00083D2C File Offset: 0x00081F2C
		// (set) Token: 0x060012B7 RID: 4791 RVA: 0x00007CF7 File Offset: 0x00005EF7
		public object FileObj
		{
			get
			{
				return this.object_0;
			}
			set
			{
				this.object_0 = value;
			}
		}

		// Token: 0x040009B3 RID: 2483
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x040009B4 RID: 2484
		private DatFileInfo datFileInfo_0;

		// Token: 0x040009B5 RID: 2485
		private string string_0;

		// Token: 0x040009B6 RID: 2486
		private Timer timer_0;

		// Token: 0x040009B7 RID: 2487
		private bool bool_0;

		// Token: 0x040009B8 RID: 2488
		private bool bool_1;

		// Token: 0x040009B9 RID: 2489
		private int int_0;

		// Token: 0x040009BA RID: 2490
		private object object_0;
	}
}
