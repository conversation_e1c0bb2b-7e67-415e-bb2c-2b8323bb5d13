﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns24
{
	// Token: 0x020002E9 RID: 745
	internal sealed class Class388 : ShapeCurve
	{
		// Token: 0x06002108 RID: 8456 RVA: 0x000EAB50 File Offset: 0x000E8D50
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_SLLine ind_SLLine = zedGraphControl_0.GraphPane.AddSLLine(base.IndData.Name, base.DataView, color_0, SymbolType.None);
			base.method_3(string_0, ind_SLLine);
			ind_SLLine.Line.Color = color_0;
			this.curveItem_0 = ind_SLLine;
			this.curveItem_0.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			ind_SLLine.Len = this.int_0;
			ind_SLLine.Slope = this.int_1;
			ind_SLLine.Expand = this.int_2;
		}

		// Token: 0x06002109 RID: 8457 RVA: 0x000EAC04 File Offset: 0x000E8E04
		public Class388(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
			this.int_0 = (int)((double)dataArray_1.SingleData[Class521.smethod_0(97836)]);
			this.int_1 = (int)((double)dataArray_1.SingleData[Class521.smethod_0(97841)]);
			this.int_2 = (int)((double)dataArray_1.SingleData[Class521.smethod_0(97850)]);
			if (this.int_2 < 0 || this.int_2 > 3)
			{
				this.int_2 = 0;
			}
		}

		// Token: 0x0600210A RID: 8458 RVA: 0x000EACA0 File Offset: 0x000E8EA0
		protected override PointPair vmethod_0(int int_3, DataArray dataArray_1)
		{
			Class388.Class406 @class = new Class388.Class406();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_3);
			double x = new XDate(dateTime);
			if (@class.dataArray_0.Data.Length < int_3 + 1)
			{
				throw new Exception(Class521.smethod_0(97541));
			}
			double y = @class.dataArray_0.Data[int_3];
			if (@class.dataArray_0.OtherDataArrayList.Count != 1)
			{
				throw new Exception(Class521.smethod_0(97859));
			}
			if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
			{
				throw new Exception(Class521.smethod_0(97904));
			}
			PointPair result;
			if ((int)@class.dataArray_0.OtherDataArrayList[0].Data[int_3] == 1)
			{
				result = new PointPair(x, y);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN);
			}
			return result;
		}

		// Token: 0x04001026 RID: 4134
		private int int_0 = 1;

		// Token: 0x04001027 RID: 4135
		private int int_1;

		// Token: 0x04001028 RID: 4136
		private int int_2;

		// Token: 0x020002EA RID: 746
		[CompilerGenerated]
		private sealed class Class406
		{
			// Token: 0x0600210C RID: 8460 RVA: 0x000EAD98 File Offset: 0x000E8F98
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x04001029 RID: 4137
			public DataArray dataArray_0;
		}
	}
}
