﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Linq;
using ns18;
using TEx.Inds;
using TEx.SIndicator;

namespace ns9
{
	// Token: 0x02000306 RID: 774
	internal static class Class425
	{
		// Token: 0x06002166 RID: 8550 RVA: 0x000ECA30 File Offset: 0x000EAC30
		public static string smethod_0(XDocument xdocument_0)
		{
			string value = xdocument_0.Element(Class521.smethod_0(98905)).Attribute(Class521.smethod_0(1858)).Value;
			string text = Class425.smethod_1(xdocument_0.Element(Class521.smethod_0(98905)).Element(Class521.smethod_0(98918)));
			return string.Format(Class521.smethod_0(98931), new object[]
			{
				value,
				Class521.smethod_0(99024),
				text,
				Class521.smethod_0(47398)
			});
		}

		// Token: 0x06002167 RID: 8551 RVA: 0x000ECAD8 File Offset: 0x000EACD8
		private static string smethod_1(XElement xelement_0)
		{
			string text = Class521.smethod_0(1449);
			foreach (XElement xelement_ in xelement_0.Elements(Class521.smethod_0(2507)))
			{
				text += Class425.smethod_2(xelement_);
			}
			return text;
		}

		// Token: 0x06002168 RID: 8552 RVA: 0x000ECB4C File Offset: 0x000EAD4C
		private static string smethod_2(XElement xelement_0)
		{
			string text = string.Format(Class521.smethod_0(99029), xelement_0.Attribute(Class521.smethod_0(1858)).Value);
			text += Class521.smethod_0(99078);
			text += string.Format(Class521.smethod_0(99083), xelement_0.Element(Class521.smethod_0(99128)).Value);
			text += string.Format(Class521.smethod_0(99141), xelement_0.Attribute(Class521.smethod_0(99178)).Value);
			XElement xelement = xelement_0.Element(Class521.smethod_0(99187));
			if (xelement != null)
			{
				foreach (XElement xelement2 in xelement.Elements(Class521.smethod_0(99196)))
				{
					text += string.Format(Class521.smethod_0(99205), xelement2.Attribute(Class521.smethod_0(1858)).Value, xelement2.Attribute(Class521.smethod_0(48861)).Value);
				}
			}
			text += Class521.smethod_0(99238);
			text += Class521.smethod_0(99295);
			text += Class521.smethod_0(99412);
			text += Class521.smethod_0(99078);
			text += Class425.smethod_3(xelement_0.Element(Class521.smethod_0(36238)).Value);
			text += Class521.smethod_0(99449);
			return text;
		}

		// Token: 0x06002169 RID: 8553 RVA: 0x000ECD28 File Offset: 0x000EAF28
		private static string smethod_3(string string_0)
		{
			List<string> list = new List<string>();
			string[] array = string_0.Split(new char[]
			{
				';'
			});
			string text = Class521.smethod_0(1449);
			for (int i = 0; i < array.Length; i++)
			{
				string[] array2 = array[i].Trim().Split(new char[]
				{
					','
				});
				text += Class425.smethod_4(array2[0].Trim(), list);
			}
			string text2 = Class521.smethod_0(1449);
			for (int j = 0; j < list.Count; j++)
			{
				text2 = text2 + list[j].Trim() + Class521.smethod_0(4736);
			}
			if (text2 != Class521.smethod_0(1449))
			{
				text2.Remove(text2.Count<char>() - 1);
				string text3 = Class521.smethod_0(99454);
				text3 += text2;
				text3 += Class521.smethod_0(99487);
				text += text3;
			}
			return text;
		}

		// Token: 0x0600216A RID: 8554 RVA: 0x000ECE34 File Offset: 0x000EB034
		private static string smethod_4(string string_0, List<string> list_0)
		{
			int num = string_0.Count<char>();
			int num2 = string_0.IndexOf(Class521.smethod_0(99492), 0, num);
			string text = Class521.smethod_0(1449);
			string arg = Class521.smethod_0(1449);
			if (num2 == -1)
			{
				int num3 = string_0.IndexOf(Class521.smethod_0(50733), 0, num);
				if (num3 == -1)
				{
					throw new IndexOutOfRangeException();
				}
				text = string_0.Substring(0, num3);
				arg = string_0.Substring(num3 + 1, num - num3 - 1);
				list_0.Add(text);
			}
			else
			{
				text = string_0.Substring(0, num2);
				arg = string_0.Substring(num2 + 2, num - num2 - 1);
			}
			return string.Format(Class521.smethod_0(99497), text, arg);
		}

		// Token: 0x0600216B RID: 8555 RVA: 0x000ECEE8 File Offset: 0x000EB0E8
		public static void smethod_5(string string_0, string string_1, UserDefineIndScript userDefineIndScript_0)
		{
			XDocument xdocument = new XDocument();
			if (File.Exists(string_0))
			{
				xdocument = XDocument.Load(string_0);
			}
			XElement xelement = xdocument.Element(Class521.smethod_0(98905));
			if (xelement == null)
			{
				xelement = new XElement(Class521.smethod_0(98905), new XAttribute(Class521.smethod_0(1858), Class521.smethod_0(99550)));
				xdocument.Add(xelement);
			}
			XElement xelement2 = xelement.Element(Class521.smethod_0(98918));
			if (xelement2 == null)
			{
				xelement2 = new XElement(Class521.smethod_0(98918));
				xelement.Add(xelement2);
			}
			XElement content = Class425.smethod_6(userDefineIndScript_0);
			foreach (XElement xelement3 in xelement2.Elements(Class521.smethod_0(2507)))
			{
				if (xelement3.Attribute(Class521.smethod_0(1858)).Value == userDefineIndScript_0.Name)
				{
					xelement3.Remove();
					break;
				}
			}
			xelement2.Add(content);
			xdocument.Save(string_0);
			string contents = Class425.smethod_0(xdocument);
			File.WriteAllText(string_1, contents);
		}

		// Token: 0x0600216C RID: 8556 RVA: 0x000ED03C File Offset: 0x000EB23C
		private static XElement smethod_6(UserDefineIndScript userDefineIndScript_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(2507), new object[]
			{
				new XAttribute(Class521.smethod_0(1858), userDefineIndScript_0.Name),
				new XAttribute(Class521.smethod_0(99178), userDefineIndScript_0.MainK)
			});
			XElement content = new XElement(Class521.smethod_0(99128), userDefineIndScript_0.Script);
			xelement.Add(content);
			XElement xelement2 = new XElement(Class521.smethod_0(99187));
			xelement.Add(xelement2);
			for (int i = 0; i < userDefineIndScript_0.UserDefineParams.Count; i++)
			{
				UserDefineParam userDefineParam = userDefineIndScript_0.UserDefineParams[i];
				XElement content2 = new XElement(Class521.smethod_0(99196), new object[]
				{
					new XAttribute(Class521.smethod_0(1858), userDefineParam.Name),
					new XAttribute(Class521.smethod_0(99571), userDefineParam.Max),
					new XAttribute(Class521.smethod_0(99576), userDefineParam.Min),
					new XAttribute(Class521.smethod_0(48861), userDefineParam.Value),
					new XAttribute(Class521.smethod_0(99581), userDefineParam.Step)
				});
				xelement2.Add(content2);
			}
			XElement content3 = new XElement(Class521.smethod_0(36238), userDefineIndScript_0.Code);
			xelement.Add(content3);
			return xelement;
		}
	}
}
