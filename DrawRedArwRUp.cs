﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns18;
using ns26;

namespace TEx
{
	// Token: 0x020001DC RID: 476
	[Serializable]
	internal sealed class DrawRedArwRUp : DrawRedArwUp, ISerializable
	{
		// Token: 0x0600128B RID: 4747 RVA: 0x00006B2F File Offset: 0x00004D2F
		public DrawRedArwRUp()
		{
		}

		// Token: 0x0600128C RID: 4748 RVA: 0x00007B95 File Offset: 0x00005D95
		public DrawRedArwRUp(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(28398);
		}

		// Token: 0x0600128D RID: 4749 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawRedArwRUp(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600128E RID: 4750 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600128F RID: 4751 RVA: 0x000835D4 File Offset: 0x000817D4
		protected override Image vmethod_24()
		{
			return Class375.RedArrow_RUp;
		}
	}
}
