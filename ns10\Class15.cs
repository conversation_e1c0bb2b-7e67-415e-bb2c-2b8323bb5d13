﻿using System;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using ns18;

namespace ns10
{
	// Token: 0x02000035 RID: 53
	internal static class Class15
	{
		// Token: 0x06000172 RID: 370
		[DllImport("user32.dll")]
		public static extern int SendMessage(IntPtr intptr_0, int int_1, bool bool_0, int int_2);

		// Token: 0x06000173 RID: 371 RVA: 0x00017120 File Offset: 0x00015320
		public static void smethod_0(this Control control_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				try
				{
					Class15.SendMessage(control_0.<PERSON>le, 11, false, 0);
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000174 RID: 372 RVA: 0x00017160 File Offset: 0x00015360
		public static void smethod_1(this Control control_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				try
				{
					Class15.SendMessage(control_0.Handle, 11, true, 0);
					control_0.Refresh();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000175 RID: 373 RVA: 0x0000300D File Offset: 0x0000120D
		public static void smethod_2(this Control control_0, bool bool_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				control_0.GetType().GetProperty(Class521.smethod_0(3155), BindingFlags.Instance | BindingFlags.NonPublic).SetValue(control_0, bool_0, null);
			}
		}

		// Token: 0x04000091 RID: 145
		private const int int_0 = 11;
	}
}
