﻿using System;
using TEx;

namespace ns26
{
	// Token: 0x020002A3 RID: 675
	internal sealed class EventArgs27 : EventArgs
	{
		// Token: 0x06001E18 RID: 7704 RVA: 0x0000C9A3 File Offset: 0x0000ABA3
		public EventArgs27(Indicator indicator_1, ChartBase chartBase_1)
		{
			this.indicator_0 = indicator_1;
			this.chartBase_0 = chartBase_1;
		}

		// Token: 0x04000ECB RID: 3787
		public Indicator indicator_0;

		// Token: 0x04000ECC RID: 3788
		public ChartBase chartBase_0;
	}
}
