﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x020001D0 RID: 464
	[Serializable]
	internal sealed class ChartParam
	{
		// Token: 0x06001241 RID: 4673 RVA: 0x00002D25 File Offset: 0x00000F25
		public ChartParam()
		{
		}

		// Token: 0x06001242 RID: 4674 RVA: 0x00082C5C File Offset: 0x00080E5C
		public ChartParam(ChartType chartType, List<Indicator> indList, List<UserDefineIndScript> UDSList, bool isYAxisLogType)
		{
			ChartParam.Class262 @class = new ChartParam.Class262();
			this.ChartType = chartType;
			this.IsYAxisLogType = isYAxisLogType;
			this._UDSList = UDSList;
			this._IndList = null;
			@class.list_0 = this.method_0(indList);
			if (@class.list_0 != null && @class.list_0.Any<UserDefineIndScript>())
			{
				ChartParam.Class263 class2 = new ChartParam.Class263();
				class2.class262_0 = @class;
				class2.int_0 = 0;
				while (class2.int_0 < class2.class262_0.list_0.Count)
				{
					if (!this._UDSList.Any(new Func<UserDefineIndScript, bool>(class2.method_0)))
					{
						this._UDSList.Add(class2.class262_0.list_0[class2.int_0]);
					}
					int int_ = class2.int_0;
					class2.int_0 = int_ + 1;
				}
			}
		}

		// Token: 0x06001243 RID: 4675 RVA: 0x00082D34 File Offset: 0x00080F34
		private List<UserDefineIndScript> method_0(List<Indicator> list_0)
		{
			List<UserDefineIndScript> result;
			if (list_0 == null)
			{
				result = new List<UserDefineIndScript>();
			}
			else
			{
				result = list_0.Select(new Func<Indicator, UserDefineIndScript>(this.method_2)).Where(new Func<UserDefineIndScript, bool>(ChartParam.<>c.<>9.method_0)).ToList<UserDefineIndScript>();
			}
			return result;
		}

		// Token: 0x06001244 RID: 4676 RVA: 0x00082D8C File Offset: 0x00080F8C
		private UserDefineIndScript method_1(Indicator indicator_0)
		{
			ChartParam.Class264 @class = new ChartParam.Class264();
			@class.indicator_0 = indicator_0;
			IndEx indEx = @class.indicator_0 as IndEx;
			UserDefineIndScript result;
			if (@class.indicator_0 == null)
			{
				result = null;
			}
			else if (indEx != null)
			{
				result = null;
			}
			else if (@class.indicator_0.EnName == Class521.smethod_0(2717))
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(ChartParam.<>c.<>9.method_1));
				if (userDefineIndScript != null)
				{
					result = (userDefineIndScript.System.ICloneable.Clone() as UserDefineIndScript);
				}
				else
				{
					result = null;
				}
			}
			else
			{
				UserDefineIndScript userDefineIndScript2 = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
				if (userDefineIndScript2 != null)
				{
					result = (userDefineIndScript2.System.ICloneable.Clone() as UserDefineIndScript);
				}
				else
				{
					result = null;
				}
			}
			return result;
		}

		// Token: 0x170002B3 RID: 691
		// (get) Token: 0x06001245 RID: 4677 RVA: 0x00082E5C File Offset: 0x0008105C
		// (set) Token: 0x06001246 RID: 4678 RVA: 0x000079ED File Offset: 0x00005BED
		public ChartType ChartType
		{
			get
			{
				return this._ChartType;
			}
			set
			{
				this._ChartType = value;
			}
		}

		// Token: 0x170002B4 RID: 692
		// (get) Token: 0x06001247 RID: 4679 RVA: 0x00082E74 File Offset: 0x00081074
		// (set) Token: 0x06001248 RID: 4680 RVA: 0x000079F8 File Offset: 0x00005BF8
		public bool IsYAxisLogType
		{
			get
			{
				return this._IsYAxisLogType;
			}
			set
			{
				this._IsYAxisLogType = value;
			}
		}

		// Token: 0x170002B5 RID: 693
		// (get) Token: 0x06001249 RID: 4681 RVA: 0x00082E8C File Offset: 0x0008108C
		// (set) Token: 0x0600124A RID: 4682 RVA: 0x00082EA4 File Offset: 0x000810A4
		public List<Indicator> IndList
		{
			get
			{
				return this._IndList;
			}
			set
			{
				List<UserDefineIndScript> list = this.method_0(value);
				if (list != null && list.Any<UserDefineIndScript>())
				{
					if (this._UDSList != null)
					{
						this._UDSList.AddRange(list);
					}
					else
					{
						this._UDSList = new List<UserDefineIndScript>(list);
					}
				}
			}
		}

		// Token: 0x170002B6 RID: 694
		// (get) Token: 0x0600124B RID: 4683 RVA: 0x00082EE8 File Offset: 0x000810E8
		// (set) Token: 0x0600124C RID: 4684 RVA: 0x00007A03 File Offset: 0x00005C03
		public List<UserDefineIndScript> UDSList
		{
			get
			{
				return this._UDSList;
			}
			set
			{
				this._UDSList = value;
			}
		}

		// Token: 0x0600124D RID: 4685 RVA: 0x00082F00 File Offset: 0x00081100
		[CompilerGenerated]
		private UserDefineIndScript method_2(Indicator indicator_0)
		{
			return this.method_1(indicator_0);
		}

		// Token: 0x0400098C RID: 2444
		private ChartType _ChartType;

		// Token: 0x0400098D RID: 2445
		private List<Indicator> _IndList;

		// Token: 0x0400098E RID: 2446
		private List<UserDefineIndScript> _UDSList;

		// Token: 0x0400098F RID: 2447
		private bool _IsYAxisLogType;

		// Token: 0x020001D1 RID: 465
		[CompilerGenerated]
		private sealed class Class262
		{
			// Token: 0x04000990 RID: 2448
			public List<UserDefineIndScript> list_0;
		}

		// Token: 0x020001D2 RID: 466
		[CompilerGenerated]
		private sealed class Class263
		{
			// Token: 0x06001250 RID: 4688 RVA: 0x00082F18 File Offset: 0x00081118
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.class262_0.list_0[this.int_0].Name;
			}

			// Token: 0x04000991 RID: 2449
			public int int_0;

			// Token: 0x04000992 RID: 2450
			public ChartParam.Class262 class262_0;
		}

		// Token: 0x020001D4 RID: 468
		[CompilerGenerated]
		private sealed class Class264
		{
			// Token: 0x06001256 RID: 4694 RVA: 0x00082F90 File Offset: 0x00081190
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.indicator_0.EnName;
			}

			// Token: 0x04000996 RID: 2454
			public Indicator indicator_0;
		}
	}
}
