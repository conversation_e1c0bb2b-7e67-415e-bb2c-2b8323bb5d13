﻿using System;
using System.ComponentModel;

namespace TEx.Trading
{
	// Token: 0x020003B5 RID: 949
	[Serializable]
	internal sealed class ShownHisTrans : Transaction
	{
		// Token: 0x170006A0 RID: 1696
		// (get) Token: 0x0600269A RID: 9882 RVA: 0x00105B70 File Offset: 0x00103D70
		// (set) Token: 0x0600269B RID: 9883 RVA: 0x0000EDA1 File Offset: 0x0000CFA1
		[DisplayName("品种")]
		public string SymblCode
		{
			get
			{
				return this._SymblCode;
			}
			set
			{
				this._SymblCode = value;
			}
		}

		// Token: 0x170006A1 RID: 1697
		// (get) Token: 0x0600269C RID: 9884 RVA: 0x00105B88 File Offset: 0x00103D88
		// (set) Token: 0x0600269D RID: 9885 RVA: 0x0000EDAC File Offset: 0x0000CFAC
		[DisplayName("属性")]
		public string TransTypeDesc
		{
			get
			{
				return this._TransTypeDesc;
			}
			set
			{
				this._TransTypeDesc = value;
			}
		}

		// Token: 0x04001298 RID: 4760
		private string _SymblCode;

		// Token: 0x04001299 RID: 4761
		private string _TransTypeDesc;
	}
}
