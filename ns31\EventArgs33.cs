﻿using System;
using System.Runtime.CompilerServices;

namespace ns31
{
	// Token: 0x02000304 RID: 772
	internal sealed class EventArgs33 : EventArgs
	{
		// Token: 0x170005C4 RID: 1476
		// (get) Token: 0x0600215A RID: 8538 RVA: 0x000ECA18 File Offset: 0x000EAC18
		// (set) Token: 0x0600215B RID: 8539 RVA: 0x0000D733 File Offset: 0x0000B933
		public string Group { get; set; }

		// Token: 0x0600215C RID: 8540 RVA: 0x0000D73E File Offset: 0x0000B93E
		public EventArgs33(string string_1)
		{
			this.Group = string_1;
		}

		// Token: 0x04001039 RID: 4153
		[CompilerGenerated]
		private string string_0;
	}
}
