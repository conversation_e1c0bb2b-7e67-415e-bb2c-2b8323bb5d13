﻿using System;

namespace ns13
{
	// Token: 0x020003CC RID: 972
	internal static class Class515
	{
		// Token: 0x040012D7 RID: 4823
		public static readonly UIntPtr uintptr_0 = new UIntPtr(2147483648U);

		// Token: 0x040012D8 RID: 4824
		public static readonly UIntPtr uintptr_1 = new UIntPtr(2147483649U);

		// Token: 0x040012D9 RID: 4825
		public static readonly UIntPtr uintptr_2 = new UIntPtr(2147483650U);

		// Token: 0x040012DA RID: 4826
		public static readonly UIntPtr uintptr_3 = new UIntPtr(2147483651U);

		// Token: 0x040012DB RID: 4827
		public static readonly UIntPtr uintptr_4 = new UIntPtr(2147483652U);

		// Token: 0x040012DC RID: 4828
		public static readonly UIntPtr uintptr_5 = new UIntPtr(2147483653U);

		// Token: 0x040012DD RID: 4829
		public static readonly UIntPtr uintptr_6 = new UIntPtr(2147483654U);
	}
}
