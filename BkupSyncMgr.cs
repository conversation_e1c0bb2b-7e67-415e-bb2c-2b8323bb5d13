﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Timers;
using System.Windows.Forms;
using System.Xml.Linq;
using ns18;
using ns23;
using ns26;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200009A RID: 154
	internal static class BkupSyncMgr
	{
		// Token: 0x06000516 RID: 1302 RVA: 0x0000449D File Offset: 0x0000269D
		static BkupSyncMgr()
		{
			if (!TApp.IsTrialUser)
			{
				BkupSyncMgr.smethod_0();
			}
		}

		// Token: 0x06000517 RID: 1303 RVA: 0x000044AD File Offset: 0x000026AD
		private static void smethod_0()
		{
			BkupSyncMgr.timer_0 = new System.Timers.Timer();
			BkupSyncMgr.timer_0.Elapsed += BkupSyncMgr.smethod_3;
			Base.UI.Form.BackupSyncPeriodicallyChanged += BkupSyncMgr.smethod_1;
		}

		// Token: 0x06000518 RID: 1304 RVA: 0x000285BC File Offset: 0x000267BC
		private static void smethod_1(object sender, EventArgs e)
		{
			if (!TApp.IsTrialUser && Base.UI.Form.BackupSyncPeriodically)
			{
				double num = Base.UI.Form.method_15();
				if (BkupSyncMgr.SyncTimer.Interval != num)
				{
					BkupSyncMgr.SyncTimer.Interval = num;
				}
				if (!BkupSyncMgr.SyncTimer.Enabled)
				{
					BkupSyncMgr.SyncTimer.Start();
				}
			}
			else
			{
				BkupSyncMgr.SyncTimer.Stop();
			}
		}

		// Token: 0x17000108 RID: 264
		// (get) Token: 0x06000519 RID: 1305 RVA: 0x00028624 File Offset: 0x00026824
		// (set) Token: 0x0600051A RID: 1306 RVA: 0x000044E7 File Offset: 0x000026E7
		public static System.Timers.Timer SyncTimer
		{
			get
			{
				return BkupSyncMgr.timer_0;
			}
			set
			{
				BkupSyncMgr.timer_0 = value;
			}
		}

		// Token: 0x0600051B RID: 1307 RVA: 0x000044F1 File Offset: 0x000026F1
		public static void smethod_2()
		{
			if (BkupSyncMgr.timer_0 == null)
			{
				BkupSyncMgr.smethod_0();
			}
			BkupSyncMgr.timer_0.Interval = Base.UI.Form.method_15();
			BkupSyncMgr.timer_0.Start();
		}

		// Token: 0x0600051C RID: 1308 RVA: 0x0000451F File Offset: 0x0000271F
		private static void smethod_3(object sender, ElapsedEventArgs e)
		{
			BkupSyncMgr.smethod_4(null);
		}

		// Token: 0x0600051D RID: 1309 RVA: 0x0002863C File Offset: 0x0002683C
		public static bool smethod_4(SplashScreen splashScreen_0)
		{
			return BkupSyncMgr.smethod_5(splashScreen_0, null, true);
		}

		// Token: 0x0600051E RID: 1310 RVA: 0x00028660 File Offset: 0x00026860
		public static bool smethod_5(SplashScreen splashScreen_0, bool? nullable_0, bool bool_0 = false)
		{
			bool result = false;
			try
			{
				if (splashScreen_0 != null)
				{
					splashScreen_0.UpdateDispMsg(Class521.smethod_0(9155));
				}
				result = BkupSyncMgr.smethod_6(ConnMgr.smethod_0(), splashScreen_0, nullable_0);
				ConnMgr.smethod_3();
				if (bool_0)
				{
					string string_ = TApp.string_3;
					if (TApp.HOST == TApp.string_3)
					{
						string_ = TApp.string_0;
					}
					if (ConnMgr.smethod_9(string_))
					{
						result = BkupSyncMgr.smethod_6(ConnMgr.smethod_1(string_), null, nullable_0);
						ConnMgr.smethod_3();
					}
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
				if (splashScreen_0 != null)
				{
					splashScreen_0.Close();
				}
			}
			return result;
		}

		// Token: 0x0600051F RID: 1311 RVA: 0x000286F8 File Offset: 0x000268F8
		private static bool smethod_6(ITExSrv itexSrv_0, SplashScreen splashScreen_0 = null, bool? nullable_0 = null)
		{
			SrvParam srvParam = BkupSyncMgr.smethod_10(itexSrv_0);
			bool result;
			if (srvParam != null)
			{
				BkupSyncMgr.smethod_7(itexSrv_0, srvParam, splashScreen_0, nullable_0);
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000520 RID: 1312 RVA: 0x00028724 File Offset: 0x00026924
		public static bool smethod_7(ITExSrv itexSrv_0, SrvParam srvParam_0, SplashScreen splashScreen_0, bool? nullable_0)
		{
			bool flag = false;
			if (splashScreen_0 == null)
			{
				splashScreen_0 = new SplashScreen();
				flag = true;
			}
			splashScreen_0.UpdateDispMsg(Class521.smethod_0(9155));
			TApp.ReqSyncFileSpms = BkupSyncMgr.smethod_16(srvParam_0.Value as List<SrvParam>, nullable_0);
			bool result;
			if (TApp.ReqSyncFileSpms != null)
			{
				splashScreen_0.UpdateDispMsg(Class521.smethod_0(9196));
				BkupSyncMgr.smethod_20(TApp.ReqSyncFileSpms, itexSrv_0, TApp.UserName, TApp.Ver);
				if (flag)
				{
					splashScreen_0.Close();
				}
				result = true;
			}
			else
			{
				if (flag)
				{
					splashScreen_0.Close();
				}
				result = false;
			}
			return result;
		}

		// Token: 0x06000521 RID: 1313 RVA: 0x000287AC File Offset: 0x000269AC
		public static bool smethod_8()
		{
			return BkupSyncMgr.smethod_9(null, false);
		}

		// Token: 0x06000522 RID: 1314 RVA: 0x000287C4 File Offset: 0x000269C4
		public static bool smethod_9(BkupSyncCnfmWnd bkupSyncCnfmWnd_0, bool bool_0)
		{
			bool backupSyncAutoOverwritingLocalFile = Base.UI.Form.BackupSyncAutoOverwritingLocalFile;
			bool flag = Base.UI.Form.BackupSyncConflictTreatmt != null && Base.UI.Form.BackupSyncConflictTreatmt.Value == BackupSyncConflictTreatmt.AutoSyncNoPrompt;
			if (!backupSyncAutoOverwritingLocalFile || !flag)
			{
				List<SyncParam> list = BkupSyncMgr.smethod_18(TApp.ReqSyncFileSpms);
				if (list != null && list.Any<SyncParam>())
				{
					IEnumerable<SyncParam> source = list.Where(new Func<SyncParam, bool>(BkupSyncMgr.<>c.<>9.method_0));
					IEnumerable<SyncParam> source2 = list.Where(new Func<SyncParam, bool>(BkupSyncMgr.<>c.<>9.method_1));
					if (bool_0 || (source.Any<SyncParam>() && !backupSyncAutoOverwritingLocalFile) || (source2.Any<SyncParam>() && !flag))
					{
						if (bkupSyncCnfmWnd_0 == null)
						{
							bkupSyncCnfmWnd_0 = new BkupSyncCnfmWnd(list);
						}
						bkupSyncCnfmWnd_0.SyncPmLst = list;
						if (bkupSyncCnfmWnd_0.ShowDialog() != DialogResult.OK)
						{
							TApp.ReqSyncFileSpms = null;
							return false;
						}
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06000523 RID: 1315 RVA: 0x000288C8 File Offset: 0x00026AC8
		public static SrvParam smethod_10(ITExSrv itexSrv_0)
		{
			SrvParam result = null;
			XDocument xdocument = BkupSyncMgr.smethod_11();
			if (xdocument != null)
			{
				byte[] info = Utility.GenCompressedBinaryArray(Utility.ConvertXMLDocToString(xdocument), CompressAlgm.LZMA, null, true);
				byte[] syncFileSrvParam = itexSrv_0.GetSyncFileSrvParam(TApp.Ver, info);
				if (syncFileSrvParam != null)
				{
					result = (Utility.DecompressBytes(syncFileSrvParam, CompressAlgm.Deflate) as SrvParam);
				}
			}
			return result;
		}

		// Token: 0x06000524 RID: 1316 RVA: 0x00028914 File Offset: 0x00026B14
		public static XDocument smethod_11()
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement(Class521.smethod_0(9237));
			xelement.Add(new XElement(Class521.smethod_0(9250), TApp.UserName));
			xelement.Add(new XElement(Class521.smethod_0(9263), TApp.App));
			xelement = BkupSyncMgr.smethod_12(xelement);
			xdocument.Add(xelement);
			return xdocument;
		}

		// Token: 0x06000525 RID: 1317 RVA: 0x0002898C File Offset: 0x00026B8C
		public static XElement smethod_12(XElement xelement_0)
		{
			string userName = TApp.UserName;
			if (!userName.Equals(Class521.smethod_0(9268), StringComparison.InvariantCultureIgnoreCase) && !userName.Equals(Class521.smethod_0(9277), StringComparison.InvariantCultureIgnoreCase))
			{
				string content = Class521.smethod_0(2841);
				if (Base.UI.Form.BackupDirection != null && Base.UI.Form.BackupDirection.Value != BackupDirection.BiDirection)
				{
					content = Base.UI.Form.BackupDirection.Value.ToString();
				}
				xelement_0.Add(new XElement(Class521.smethod_0(9294), content));
				XElement xelement = new XElement(Class521.smethod_0(9307));
				if (!Base.UI.Form.BackupNoUIParams)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9316), xelement);
				}
				if (!Base.UI.Form.BackupNoSymbParams)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9329), xelement);
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9346), xelement);
				}
				if (!Base.UI.Form.BackupNoPages)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9363), xelement);
				}
				if (!Base.UI.Form.BackupNoPages)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9376), xelement);
				}
				BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(4741), xelement);
				BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9393), xelement);
				if (!Base.UI.Form.BackupNoZiXuan)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9410), xelement);
				}
				if (!Base.UI.Form.BackupNoDrawObjs)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9427), xelement);
				}
				if (!Base.UI.Form.BackupNoAcctTrans)
				{
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9444), xelement);
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9457), xelement);
					BkupSyncMgr.smethod_13(Class521.smethod_0(1449), Class521.smethod_0(9474), xelement);
					BkupSyncMgr.smethod_13(Class521.smethod_0(9495), Class521.smethod_0(9508), xelement);
					DirectoryInfo directoryInfo = new DirectoryInfo(TApp.UserAcctFolder + Class521.smethod_0(9525));
					if (directoryInfo.Exists)
					{
						DirectoryInfo[] directories = directoryInfo.GetDirectories();
						if (directories.Any<DirectoryInfo>())
						{
							foreach (DirectoryInfo directoryInfo2 in directories)
							{
								string string_ = Class521.smethod_0(9538) + directoryInfo2.Name;
								BkupSyncMgr.smethod_13(string_, Class521.smethod_0(9551), xelement);
								BkupSyncMgr.smethod_13(string_, Class521.smethod_0(9568), xelement);
								BkupSyncMgr.smethod_13(string_, Class521.smethod_0(9585), xelement);
							}
						}
					}
				}
				BkupSyncMgr.smethod_13(Class521.smethod_0(9598), Class521.smethod_0(5023), xelement);
				xelement_0.Add(xelement);
			}
			return xelement_0;
		}

		// Token: 0x06000526 RID: 1318 RVA: 0x0000452A File Offset: 0x0000272A
		private static void smethod_13(string string_0, string string_1, XElement xelement_0)
		{
			BkupSyncMgr.smethod_14(TApp.UserAcctFolder, string_0, string_1, xelement_0);
		}

		// Token: 0x06000527 RID: 1319 RVA: 0x00028CC0 File Offset: 0x00026EC0
		private static void smethod_14(string string_0, string string_1, string string_2, XElement xelement_0)
		{
			string value = string.Empty;
			string value2 = string.Empty;
			FileInfo fileInfo = new FileInfo(Path.Combine(Path.Combine(string_0, string_1), string_2));
			if (fileInfo.Exists)
			{
				byte[] bytesFromFile = Utility.GetBytesFromFile(fileInfo);
				if (bytesFromFile != null && bytesFromFile.Length != 0)
				{
					value = Utility.GetChecksum(bytesFromFile);
				}
				value2 = fileInfo.LastWriteTimeUtc.ToString();
			}
			XElement xelement = new XElement(Class521.smethod_0(9603));
			xelement.SetAttributeValue(Class521.smethod_0(1858), fileInfo.Name);
			xelement.SetAttributeValue(Class521.smethod_0(9612), value);
			xelement.SetAttributeValue(Class521.smethod_0(9617), value2);
			xelement.SetAttributeValue(Class521.smethod_0(9626), string_1);
			xelement_0.Add(xelement);
		}

		// Token: 0x06000528 RID: 1320 RVA: 0x00028D98 File Offset: 0x00026F98
		public static List<SrvParam> smethod_15(List<SrvParam> list_0)
		{
			return BkupSyncMgr.smethod_16(list_0, null);
		}

		// Token: 0x06000529 RID: 1321 RVA: 0x00028DB8 File Offset: 0x00026FB8
		public static List<SrvParam> smethod_16(List<SrvParam> list_0, bool? nullable_0)
		{
			List<SrvParam> list = new List<SrvParam>();
			bool flag = Base.UI.Form.UserID.Equals(Class521.smethod_0(9268), StringComparison.InvariantCultureIgnoreCase) || Base.UI.Form.UserID.Equals(Class521.smethod_0(9277), StringComparison.InvariantCultureIgnoreCase);
			bool flag2 = Base.UI.Form.BackupDirection == null || Base.UI.Form.BackupDirection.Value == BackupDirection.BiDirection;
			bool backupSyncAutoOverwritingLocalFile = Base.UI.Form.BackupSyncAutoOverwritingLocalFile;
			BackupSyncConflictTreatmt backupSyncConflictTreatmt;
			if (Base.UI.Form.BackupSyncConflictTreatmt != null && Base.UI.Form.BackupSyncConflictTreatmt.Value != BackupSyncConflictTreatmt.PromptConflict)
			{
				if (Base.UI.Form.BackupSyncConflictTreatmt.Value == BackupSyncConflictTreatmt.AutoSyncNoPrompt)
				{
					backupSyncConflictTreatmt = BackupSyncConflictTreatmt.AutoSyncNoPrompt;
				}
				else
				{
					backupSyncConflictTreatmt = BackupSyncConflictTreatmt.CancelSync;
				}
			}
			else
			{
				backupSyncConflictTreatmt = BackupSyncConflictTreatmt.PromptConflict;
			}
			foreach (SrvParam srvParam in list_0)
			{
				string note = srvParam.Note;
				FileInfo fileInfo = new FileInfo(Path.Combine(Path.Combine(TApp.UserAcctFolder, note), srvParam.FileName));
				bool? flag3 = null;
				bool bool_ = false;
				if (nullable_0 != null)
				{
					if (nullable_0.Value)
					{
						if (srvParam.Value != null)
						{
							bool_ = true;
							flag3 = new bool?(nullable_0.Value);
						}
						else
						{
							flag3 = null;
						}
					}
					else if (fileInfo.Exists && fileInfo.Length > 0L)
					{
						bool_ = true;
						flag3 = new bool?(nullable_0.Value);
					}
					else
					{
						flag3 = new bool?(false);
					}
				}
				else if (srvParam.Value != null)
				{
					DateTime dateTime = (DateTime)srvParam.Value;
					if (fileInfo.Exists && fileInfo.Length > 0L)
					{
						DateTime lastWriteTimeUtc = fileInfo.LastWriteTimeUtc;
						if (dateTime != lastWriteTimeUtc)
						{
							if (dateTime > lastWriteTimeUtc)
							{
								if (flag2)
								{
									flag3 = new bool?(true);
									if (!backupSyncAutoOverwritingLocalFile)
									{
										bool_ = true;
									}
								}
								else if (!flag && backupSyncConflictTreatmt != BackupSyncConflictTreatmt.CancelSync)
								{
									flag3 = new bool?(false);
									if (backupSyncConflictTreatmt == BackupSyncConflictTreatmt.PromptConflict)
									{
										bool_ = true;
									}
								}
							}
							else if (!flag)
							{
								flag3 = new bool?(false);
							}
							else if (flag2)
							{
								flag3 = new bool?(true);
								if (!backupSyncAutoOverwritingLocalFile)
								{
									bool_ = true;
								}
							}
						}
					}
					else
					{
						if (flag2)
						{
							string text = Class521.smethod_0(9538);
							if (TApp.EnteredMainForm && note.Contains(text))
							{
								string value = note.Replace(text, string.Empty);
								try
								{
									int item = Convert.ToInt32(value);
									if (Base.Acct.NewAccountIDs.Contains(item))
									{
										flag3 = new bool?(false);
									}
									else if (!Base.Acct.DeletedAccountIDs.Contains(item))
									{
										flag3 = new bool?(true);
									}
									goto IL_335;
								}
								catch (Exception exception_)
								{
									Class184.smethod_0(exception_);
									goto IL_335;
								}
							}
							flag3 = new bool?(true);
						}
						IL_335:
						if (!backupSyncAutoOverwritingLocalFile)
						{
							bool_ = true;
						}
					}
				}
				else if (fileInfo.Exists && fileInfo.Length > 0L)
				{
					flag3 = new bool?(false);
				}
				if (flag3 != null)
				{
					SrvParam item2 = BkupSyncMgr.smethod_17(flag3.Value, srvParam.FileName, note, fileInfo, bool_);
					list.Add(item2);
				}
			}
			if (TApp.EnteredMainForm)
			{
				if (Base.Acct.DeletedAccountIDs.Any<int>())
				{
					list.Add(new SrvParam
					{
						Name = Class521.smethod_0(9635),
						Value = Base.Acct.DeletedAccountIDs,
						DataType = typeof(List<int>),
						Date = DateTime.Now
					});
				}
				if (Base.Acct.NewAccountIDs.Any<int>())
				{
					list.Add(new SrvParam
					{
						Name = Class521.smethod_0(9652),
						Value = Base.Acct.NewAccountIDs,
						DataType = typeof(List<int>),
						Date = DateTime.Now
					});
				}
			}
			return list;
		}

		// Token: 0x0600052A RID: 1322 RVA: 0x00029200 File Offset: 0x00027400
		private static SrvParam smethod_17(bool bool_0, string string_0, string string_1, FileInfo fileInfo_0, bool bool_1)
		{
			SrvParam srvParam = new SrvParam();
			string name;
			if (bool_0)
			{
				name = Class521.smethod_0(9669);
			}
			else
			{
				name = Class521.smethod_0(9682);
			}
			srvParam.FileName = string_0;
			srvParam.Note = string_1;
			srvParam.Name = name;
			if (!bool_0 && fileInfo_0 != null)
			{
				srvParam.Value = Utility.GetBytesFromFile(fileInfo_0);
				srvParam.Date = fileInfo_0.LastWriteTimeUtc;
			}
			if (bool_1)
			{
				srvParam.Ver = Class521.smethod_0(9695);
			}
			return srvParam;
		}

		// Token: 0x0600052B RID: 1323 RVA: 0x0002927C File Offset: 0x0002747C
		public static List<SyncParam> smethod_18(List<SrvParam> list_0)
		{
			List<SyncParam> list = null;
			List<SyncParam> result;
			if (list_0 == null)
			{
				result = null;
			}
			else
			{
				if (list_0.Exists(new Predicate<SrvParam>(BkupSyncMgr.<>c.<>9.method_2)))
				{
					list = new List<SyncParam>();
					IEnumerable<SrvParam> source = list_0.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_3));
					if (source.Any<SrvParam>())
					{
						IEnumerable<SrvParam> source2 = source.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_4));
						IEnumerable<SrvParam> source3 = source.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_5));
						bool flag = source2.Any<SrvParam>();
						bool flag2 = source3.Any<SrvParam>();
						if (flag || flag2)
						{
							bool flag3 = true;
							if (flag && flag2)
							{
								bool flag4 = source2.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_6)).Any<SrvParam>();
								bool flag5 = source3.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_7)).Any<SrvParam>();
								if (flag4 && !flag5)
								{
									flag3 = false;
								}
							}
							else if (flag)
							{
								flag3 = false;
							}
							if (flag3)
							{
								list.Add(new SyncParam
								{
									enum8_0 = Enum8.const_0,
									bool_0 = false
								});
							}
							else
							{
								list.Add(new SyncParam
								{
									enum8_0 = Enum8.const_0,
									bool_0 = true,
									bool_1 = true
								});
							}
						}
					}
					IEnumerable<SrvParam> source4 = list_0.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_8));
					if (source4.Any<SrvParam>())
					{
						if (source4.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_9)).Any<SrvParam>())
						{
							list.Add(new SyncParam
							{
								enum8_0 = Enum8.const_2,
								bool_0 = false
							});
						}
						else if (source4.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_10)).Any<SrvParam>())
						{
							list.Add(new SyncParam
							{
								enum8_0 = Enum8.const_2,
								bool_0 = true,
								bool_1 = true
							});
						}
					}
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9316), Enum8.const_1);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9410), Enum8.const_4);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9363), Enum8.const_3);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9376), Enum8.const_6);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(4741), Enum8.const_7);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9427), Enum8.const_5);
					BkupSyncMgr.smethod_19(list, list_0, Class521.smethod_0(9393), Enum8.const_8);
				}
				result = list;
			}
			return result;
		}

		// Token: 0x0600052C RID: 1324 RVA: 0x00029574 File Offset: 0x00027774
		private static void smethod_19(List<SyncParam> list_0, List<SrvParam> list_1, string string_0, Enum8 enum8_0)
		{
			BkupSyncMgr.Class62 @class = new BkupSyncMgr.Class62();
			@class.string_0 = string_0;
			IEnumerable<SrvParam> source = list_1.Where(new Func<SrvParam, bool>(@class.method_0));
			if (source.Any<SrvParam>())
			{
				if (source.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_11)).Any<SrvParam>())
				{
					list_0.Add(new SyncParam
					{
						enum8_0 = enum8_0,
						bool_0 = true,
						bool_1 = true
					});
				}
				else if (source.Where(new Func<SrvParam, bool>(BkupSyncMgr.<>c.<>9.method_12)).Any<SrvParam>())
				{
					list_0.Add(new SyncParam
					{
						enum8_0 = enum8_0,
						bool_0 = false
					});
				}
			}
		}

		// Token: 0x0600052D RID: 1325 RVA: 0x00029648 File Offset: 0x00027848
		public static void smethod_20(List<SrvParam> list_0, ITExSrv itexSrv_0, string string_0, string string_1)
		{
			if (list_0 != null && list_0.Any<SrvParam>())
			{
				byte[] cmprsdSpms = Utility.GenCompressedBinaryArray(TApp.ReqSyncFileSpms, CompressAlgm.LZMA, null, true);
				byte[] array = itexSrv_0.SendSyncFileReqs(string_0, string_1, cmprsdSpms);
				if (array != null)
				{
					foreach (SrvParam srvParam in (Utility.DecompressBytes(array, CompressAlgm.Deflate) as List<SrvParam>))
					{
						try
						{
							string note = srvParam.Note;
							string fileName = srvParam.FileName;
							string path = TApp.UserAcctFolder;
							if (!string.IsNullOrEmpty(note))
							{
								path = Path.Combine(path, note);
							}
							string text = Path.Combine(path, fileName);
							string path2 = Path.Combine(Path.Combine(TApp.UserAcctFolder, Class521.smethod_0(9704)), DateTime.Now.ToShortDateString().ToString());
							if (!string.IsNullOrEmpty(note))
							{
								path2 = Path.Combine(path2, note);
							}
							Utility.ChkCopyFiles(text, Path.Combine(path2, fileName));
							try
							{
								Utility.GenFileFromBytes((byte[])srvParam.Value, text).LastWriteTimeUtc = srvParam.Date;
							}
							catch (Exception exception_)
							{
								Class184.smethod_0(exception_);
							}
							if (fileName.Equals(Class521.smethod_0(9316), StringComparison.InvariantCultureIgnoreCase))
							{
								Base.UI.smethod_172();
							}
						}
						catch
						{
							throw;
						}
					}
				}
			}
		}

		// Token: 0x04000220 RID: 544
		private static System.Timers.Timer timer_0;

		// Token: 0x0200009C RID: 156
		[CompilerGenerated]
		private sealed class Class62
		{
			// Token: 0x0600053E RID: 1342 RVA: 0x00029928 File Offset: 0x00027B28
			internal bool method_0(SrvParam srvParam_0)
			{
				return srvParam_0.FileName.Equals(this.string_0);
			}

			// Token: 0x0400022F RID: 559
			public string string_0;
		}
	}
}
