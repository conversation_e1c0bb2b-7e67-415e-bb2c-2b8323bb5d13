﻿using System;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security;
using System.Security.Permissions;
using System.Threading;
using System.Web.Services.Protocols;
using System.Windows.Forms;
using ns1;
using ns11;
using ns13;
using ns16;
using ns18;
using ns29;
using ns4;
using ns6;
using ns9;

namespace ns28
{
	// Token: 0x02000407 RID: 1031
	internal abstract class Class544
	{
		// Token: 0x140000C1 RID: 193
		// (add) Token: 0x060027F1 RID: 10225 RVA: 0x0010CE80 File Offset: 0x0010B080
		// (remove) Token: 0x060027F2 RID: 10226 RVA: 0x0010CEB8 File Offset: 0x0010B0B8
		public event EventHandler DebuggerLaunched
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x140000C2 RID: 194
		// (add) Token: 0x060027F3 RID: 10227 RVA: 0x0010CEF0 File Offset: 0x0010B0F0
		// (remove) Token: 0x060027F4 RID: 10228 RVA: 0x0010CF28 File Offset: 0x0010B128
		public event Delegate38 SendingReportFeedback
		{
			[CompilerGenerated]
			add
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x060027F5 RID: 10229
		protected abstract void vmethod_0(EventArgs35 eventArgs35_0);

		// Token: 0x060027F6 RID: 10230
		protected abstract void vmethod_1(EventArgs34 eventArgs34_0);

		// Token: 0x060027F7 RID: 10231
		protected abstract void vmethod_2(EventArgs36 eventArgs36_0);

		// Token: 0x060027F8 RID: 10232 RVA: 0x0000F78F File Offset: 0x0000D98F
		[SecurityPermission(SecurityAction.Demand, UnmanagedCode = true)]
		public static void smethod_0(Class544 class544_1)
		{
			if (class544_1 != null)
			{
				Class544.class544_0 = class544_1;
				AppDomain.CurrentDomain.UnhandledException += class544_1.method_1;
				Application.ThreadException += class544_1.method_0;
			}
		}

		// Token: 0x170006DF RID: 1759
		// (get) Token: 0x060027F9 RID: 10233 RVA: 0x0010CF60 File Offset: 0x0010B160
		private static Class544 Handler
		{
			get
			{
				if (Class544.class544_0 == null)
				{
					foreach (Type type in Assembly.GetExecutingAssembly().GetTypes())
					{
						if (type != null && type.BaseType != null && type.BaseType == typeof(Class544))
						{
							try
							{
								Class544.class544_0 = (Class544)Activator.CreateInstance(type, true);
								if (Class544.class544_0 != null)
								{
									break;
								}
							}
							catch
							{
							}
						}
					}
				}
				return Class544.class544_0;
			}
		}

		// Token: 0x060027FA RID: 10234 RVA: 0x0010CFE4 File Offset: 0x0010B1E4
		public static void smethod_1(Exception exception_0, object[] object_0)
		{
			if (exception_0 != null && exception_0 is SecurityException && Class544.string_2 == Class521.smethod_0(4933) && Class544.Handler.method_3((SecurityException)exception_0))
			{
				return;
			}
			Class542.smethod_11(exception_0, object_0);
			Class544.Handler.method_4(exception_0, false, false);
		}

		// Token: 0x060027FB RID: 10235 RVA: 0x0010D03C File Offset: 0x0010B23C
		public static void smethod_2(Exception exception_0)
		{
			if (exception_0 != null && exception_0 is SecurityException && Class544.string_2 == Class521.smethod_0(4933) && Class544.Handler.method_3((SecurityException)exception_0))
			{
				return;
			}
			Class544.Handler.method_4(exception_0, false, false);
		}

		// Token: 0x060027FC RID: 10236 RVA: 0x0010D08C File Offset: 0x0010B28C
		public static Exception smethod_3(Exception exception_0, object[] object_0)
		{
			try
			{
				if (exception_0.GetType() == typeof(Exception) && exception_0.Message == Class521.smethod_0(119052))
				{
					exception_0 = exception_0.InnerException;
				}
				else
				{
					Class542.smethod_11(exception_0, object_0);
				}
				Class544.Handler.method_4(exception_0, true, false);
			}
			catch
			{
			}
			return new SoapException(exception_0.Message, SoapException.ServerFaultCode);
		}

		// Token: 0x060027FD RID: 10237 RVA: 0x0010D108 File Offset: 0x0010B308
		public static void smethod_4(Exception exception_0, object[] object_0)
		{
			try
			{
				if (exception_0.GetType() == typeof(Exception) && exception_0.Message == Class521.smethod_0(119052))
				{
					exception_0 = exception_0.InnerException;
				}
				else
				{
					Class542.smethod_11(exception_0, object_0);
				}
				Class544.Handler.method_4(exception_0, true, true);
			}
			catch
			{
			}
		}

		// Token: 0x060027FE RID: 10238 RVA: 0x0010D174 File Offset: 0x0010B374
		private void method_0(object sender, ThreadExceptionEventArgs e)
		{
			try
			{
				Exception ex = e.Exception;
				Type type = ex.GetType();
				if (type.Name == Class521.smethod_0(119065) && type.Namespace == Class521.smethod_0(119090))
				{
					ex = (Exception)type.GetField(Class521.smethod_0(119135)).GetValue(ex);
				}
				if (!(ex is SecurityException) || !(Class544.string_2 == Class521.smethod_0(4933)) || !this.method_3(ex as SecurityException))
				{
					this.method_4(ex, true, false);
				}
			}
			catch
			{
			}
		}

		// Token: 0x060027FF RID: 10239 RVA: 0x0010D228 File Offset: 0x0010B428
		private void method_1(object sender, UnhandledExceptionEventArgs e)
		{
			try
			{
				if (!(e.ExceptionObject is SecurityException) || !(Class544.string_2 == Class521.smethod_0(4933)) || !this.method_3(e.ExceptionObject as SecurityException))
				{
					if (e.ExceptionObject is Exception)
					{
						this.method_4((Exception)e.ExceptionObject, !e.IsTerminating, false);
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002800 RID: 10240 RVA: 0x0000F7C1 File Offset: 0x0000D9C1
		public void method_2(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x06002801 RID: 10241 RVA: 0x0000F7CA File Offset: 0x0000D9CA
		protected virtual Guid vmethod_3()
		{
			return Guid.Empty;
		}

		// Token: 0x06002802 RID: 10242 RVA: 0x0010D2AC File Offset: 0x0010B4AC
		private bool method_3(SecurityException securityException_0)
		{
			EventArgs36 eventArgs = new EventArgs36(securityException_0);
			this.vmethod_2(eventArgs);
			if (eventArgs.ReportException)
			{
				return false;
			}
			if (!eventArgs.TryToContinue)
			{
				Application.Exit();
			}
			return true;
		}

		// Token: 0x06002803 RID: 10243 RVA: 0x0010D2E0 File Offset: 0x0010B4E0
		private void method_4(Exception exception_0, bool bool_1, bool bool_2)
		{
			Type type = exception_0.GetType();
			if (type.Name == Class521.smethod_0(119065) && type.Namespace == Class521.smethod_0(119090))
			{
				exception_0 = (Exception)type.GetField(Class521.smethod_0(119135)).GetValue(exception_0);
			}
			bool flag = true;
			if (exception_0 != null && !(exception_0 is ThreadAbortException))
			{
				try
				{
					Class536 @class = new Class536(this.vmethod_3(), exception_0, this.iwebProxy_0);
					@class.SendingReportFeedback += this.method_7;
					@class.DebuggerLaunched += this.method_6;
					@class.FatalException += this.method_5;
					EventArgs35 eventArgs = new EventArgs35(@class, exception_0);
					if (Class513.smethod_0() != null)
					{
						eventArgs.method_1();
					}
					if (!bool_1)
					{
						eventArgs.method_0(false);
						eventArgs.TryToContinue = false;
					}
					else if (bool_2 || Class544.bool_0)
					{
						eventArgs.method_0(false);
						eventArgs.TryToContinue = true;
					}
					this.vmethod_0(eventArgs);
					flag = !eventArgs.TryToContinue;
				}
				catch (ThreadAbortException)
				{
				}
				catch (Exception exception_)
				{
					this.vmethod_1(new EventArgs34(exception_));
				}
				if (flag)
				{
					foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
					{
						try
						{
							string fullName = assembly.FullName;
							if (fullName.EndsWith(Class521.smethod_0(119160)) && fullName.StartsWith(Class521.smethod_0(119185)))
							{
								object obj = assembly.GetType(Class521.smethod_0(119218)).GetProperty(Class521.smethod_0(119255)).GetGetMethod().Invoke(null, null);
								obj.GetType().GetMethod(Class521.smethod_0(119268), new Type[0]).Invoke(obj, null);
							}
						}
						catch
						{
						}
					}
					try
					{
						Environment.ExitCode = -532462766;
						Application.Exit();
					}
					catch
					{
						try
						{
							Environment.Exit(-532462766);
						}
						catch
						{
						}
					}
				}
				return;
			}
		}

		// Token: 0x06002804 RID: 10244 RVA: 0x0000F7D1 File Offset: 0x0000D9D1
		private void method_5(object sender, EventArgs34 e)
		{
			this.vmethod_1(e);
		}

		// Token: 0x06002805 RID: 10245 RVA: 0x0010D520 File Offset: 0x0010B720
		private void method_6(object sender, EventArgs e)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(sender, e);
			}
		}

		// Token: 0x06002806 RID: 10246 RVA: 0x0010D540 File Offset: 0x0010B740
		private void method_7(object sender, EventArgs37 e)
		{
			Delegate38 @delegate = this.delegate38_0;
			if (@delegate != null)
			{
				@delegate(sender, e);
			}
		}

		// Token: 0x040013D6 RID: 5078
		public const string string_0 = "{1fe9e38e-05cc-46a3-ae48-6cda8fb62056}";

		// Token: 0x040013D7 RID: 5079
		public const string string_1 = "{395edd3b-130e-4160-bb08-6931086cea46}";

		// Token: 0x040013D8 RID: 5080
		private static readonly bool bool_0 = Convert.ToBoolean(Class521.smethod_0(119281));

		// Token: 0x040013D9 RID: 5081
		private static readonly string string_2 = Class521.smethod_0(4933);

		// Token: 0x040013DA RID: 5082
		private static Class544 class544_0;

		// Token: 0x040013DB RID: 5083
		private IWebProxy iwebProxy_0;

		// Token: 0x040013DC RID: 5084
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040013DD RID: 5085
		[CompilerGenerated]
		private Delegate38 delegate38_0;
	}
}
