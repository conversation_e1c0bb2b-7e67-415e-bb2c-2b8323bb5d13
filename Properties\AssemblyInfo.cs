﻿using System;
using System.Configuration.Assemblies;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyAlgorithmId(AssemblyHashAlgorithm.None)]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCopyright("Copyright © TEx Studio, 2025. All rights reserved.")]
[assembly: AssemblyProduct("交易练习者")]
[assembly: AssemblyFileVersion("*******")]
[assembly: Guid("6fecd38d-8f42-433f-9163-09177d3e02b0")]
[assembly: ComVisible(false)]
[assembly: AssemblyCompany("TEx Studio")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyDescription("交易练习者")]
[assembly: AssemblyTitle("TEx")]
