﻿using System;
using System.Drawing;

namespace ns4
{
	// Token: 0x0200012A RID: 298
	internal static class ThemeColors
	{
		// Token: 0x04000509 RID: 1289
		public static Color DarkestGray = Color.FromArgb(22, 22, 22);

		// Token: 0x0400050A RID: 1290
		public static Color DarkGray = Color.FromArgb(35, 38, 42);

		// Token: 0x0400050B RID: 1291
		public static Color MediumDarkGray = Color.FromArgb(49, 49, 49);

		// Token: 0x0400050C RID: 1292
		public static Color MediumGray = Color.FromArgb(59, 59, 59);

		// Token: 0x0400050D RID: 1293
		public static Color LightMediumGray = Color.FromArgb(72, 72, 72);

		// Token: 0x0400050E RID: 1294
		public static Color LightGray = Color.FromArgb(100, 100, 100);

		// Token: 0x0400050F RID: 1295
		public static Color LighterGray = Color.FromArgb(144, 144, 144);

		// Token: 0x04000510 RID: 1296
		public static Color VeryLightGray = Color.FromArgb(184, 184, 184);

		// Token: 0x04000511 RID: 1297
		public static Color AlmostWhite = Color.FromArgb(225, 225, 225);

		// Token: 0x04000512 RID: 1298
		public static Color NearWhite = Color.FromArgb(245, 245, 245);

		// Token: 0x04000513 RID: 1299
		public static Color AlmostPureWhite = Color.FromArgb(254, 254, 254);

		// Token: 0x04000514 RID: 1300
		public static Color Orange = Color.FromArgb(252, 188, 14);

		// Token: 0x04000515 RID: 1301
		public static Color LightOrange = Color.FromArgb(255, 228, 181);

		// Token: 0x04000516 RID: 1302
		public static Color LightYellow = Color.FromArgb(255, 255, 236);

		// Token: 0x04000517 RID: 1303
		public static Color LightBlue = Color.FromArgb(238, 238, 255);

		// Token: 0x04000518 RID: 1304
		public static Color VeryLightBlue = Color.FromArgb(240, 240, 255);

		// Token: 0x04000519 RID: 1305
		public static Color Blue = Color.FromArgb(58, 123, 213);

		// Token: 0x0400051A RID: 1306
		public static Color LightBlue2 = Color.FromArgb(93, 153, 232);

		// Token: 0x0400051B RID: 1307
		public static Color DarkRed = Color.FromArgb(141, 13, 13);

		// Token: 0x0400051C RID: 1308
		public static Color Red = Color.FromArgb(233, 53, 18);

		// Token: 0x0400051D RID: 1309
		public static Color BrightRed = Color.FromArgb(255, 87, 22);

		// Token: 0x0400051E RID: 1310
		public static Color OrangeRed = Color.FromArgb(234, 85, 4);

		// Token: 0x0400051F RID: 1311
		public static Color LightRed = Color.FromArgb(239, 110, 89);

		// Token: 0x04000520 RID: 1312
		public static Color Brown = Color.FromArgb(165, 134, 79);

		// Token: 0x04000521 RID: 1313
		public static Color Green = Color.FromArgb(0, 192, 0);
	}
}
