﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000338 RID: 824
	public sealed class UserDefineIndScript : IComparable<UserDefineIndScript>, ICloneable
	{
		// Token: 0x170005EF RID: 1519
		// (get) Token: 0x060022AE RID: 8878 RVA: 0x000F519C File Offset: 0x000F339C
		// (set) Token: 0x060022AF RID: 8879 RVA: 0x0000DBD5 File Offset: 0x0000BDD5
		public List<UserDefineParam> UserDefineParams { get; set; }

		// Token: 0x170005F0 RID: 1520
		// (get) Token: 0x060022B0 RID: 8880 RVA: 0x000F51B4 File Offset: 0x000F33B4
		// (set) Token: 0x060022B1 RID: 8881 RVA: 0x0000DBE0 File Offset: 0x0000BDE0
		public string Name { get; private set; }

		// Token: 0x060022B2 RID: 8882 RVA: 0x0000DBEB File Offset: 0x0000BDEB
		public void method_0(string string_5)
		{
			this.Name = string_5;
		}

		// Token: 0x170005F1 RID: 1521
		// (get) Token: 0x060022B3 RID: 8883 RVA: 0x000F51CC File Offset: 0x000F33CC
		// (set) Token: 0x060022B4 RID: 8884 RVA: 0x0000DBF6 File Offset: 0x0000BDF6
		public bool UserIndFlag { get; set; }

		// Token: 0x060022B5 RID: 8885 RVA: 0x000F51E4 File Offset: 0x000F33E4
		public bool method_1(UserDefineIndScript userDefineIndScript_0)
		{
			bool result;
			if (userDefineIndScript_0 != null)
			{
				if (!(this.Code.ToUpper().Trim() != userDefineIndScript_0.Code.ToUpper().Trim()) && !(this.Instruction.Trim() != userDefineIndScript_0.Instruction.Trim()) && !(this.Script.Trim() != userDefineIndScript_0.Script.Trim()))
				{
					if (this.MainK != userDefineIndScript_0.MainK)
					{
						result = false;
					}
					else if (this.YLine != userDefineIndScript_0.YLine)
					{
						result = false;
					}
					else if (this.UserDefineParams.Count != userDefineIndScript_0.UserDefineParams.Count)
					{
						result = false;
					}
					else
					{
						for (int i = 0; i < this.UserDefineParams.Count; i++)
						{
							if (!this.UserDefineParams[i].CheckEqual(userDefineIndScript_0.UserDefineParams[i]))
							{
								return false;
							}
						}
						result = true;
					}
				}
				else
				{
					result = false;
				}
			}
			else if (!(this.Code.ToUpper() != Class521.smethod_0(1449)) && !(this.Instruction != Class521.smethod_0(1449)) && !(this.Script != Class521.smethod_0(1449)))
			{
				if (this.YLine != Class521.smethod_0(1449))
				{
					result = false;
				}
				else if (this.UserDefineParams.Count != 0)
				{
					result = false;
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060022B6 RID: 8886 RVA: 0x000F5378 File Offset: 0x000F3578
		public int CompareTo(UserDefineIndScript other)
		{
			return string.Compare(this.Name, other.Name);
		}

		// Token: 0x170005F2 RID: 1522
		// (get) Token: 0x060022B7 RID: 8887 RVA: 0x000F539C File Offset: 0x000F359C
		// (set) Token: 0x060022B8 RID: 8888 RVA: 0x0000DC01 File Offset: 0x0000BE01
		public string Code { get; set; }

		// Token: 0x170005F3 RID: 1523
		// (get) Token: 0x060022B9 RID: 8889 RVA: 0x000F53B4 File Offset: 0x000F35B4
		// (set) Token: 0x060022BA RID: 8890 RVA: 0x0000DC0C File Offset: 0x0000BE0C
		public bool MainK { get; private set; }

		// Token: 0x170005F4 RID: 1524
		// (get) Token: 0x060022BB RID: 8891 RVA: 0x000F53CC File Offset: 0x000F35CC
		// (set) Token: 0x060022BC RID: 8892 RVA: 0x0000DC17 File Offset: 0x0000BE17
		public double Ver { get; set; }

		// Token: 0x170005F5 RID: 1525
		// (get) Token: 0x060022BD RID: 8893 RVA: 0x000F53E4 File Offset: 0x000F35E4
		// (set) Token: 0x060022BE RID: 8894 RVA: 0x0000DC22 File Offset: 0x0000BE22
		public bool Check { get; set; }

		// Token: 0x170005F6 RID: 1526
		// (get) Token: 0x060022BF RID: 8895 RVA: 0x000F53FC File Offset: 0x000F35FC
		// (set) Token: 0x060022C0 RID: 8896 RVA: 0x0000DC2D File Offset: 0x0000BE2D
		public string YLine { get; set; }

		// Token: 0x170005F7 RID: 1527
		// (get) Token: 0x060022C1 RID: 8897 RVA: 0x000F5414 File Offset: 0x000F3614
		public string NameAndScript
		{
			get
			{
				string text = Class521.smethod_0(1449);
				if (this.MainK)
				{
					text = this.Name + Class521.smethod_0(5036) + this.Script;
				}
				else
				{
					text = this.Name + Class521.smethod_0(5036) + this.Script;
				}
				if (!this.Check)
				{
					text += Class521.smethod_0(103763);
				}
				return text;
			}
		}

		// Token: 0x170005F8 RID: 1528
		// (get) Token: 0x060022C2 RID: 8898 RVA: 0x000F548C File Offset: 0x000F368C
		// (set) Token: 0x060022C3 RID: 8899 RVA: 0x0000DC38 File Offset: 0x0000BE38
		public string Script { get; private set; }

		// Token: 0x170005F9 RID: 1529
		// (get) Token: 0x060022C4 RID: 8900 RVA: 0x000F54A4 File Offset: 0x000F36A4
		// (set) Token: 0x060022C5 RID: 8901 RVA: 0x0000DC43 File Offset: 0x0000BE43
		public string Instruction { get; set; }

		// Token: 0x060022C6 RID: 8902 RVA: 0x000F54BC File Offset: 0x000F36BC
		public static bool smethod_0(string string_5, out string string_6)
		{
			bool result;
			if (string.IsNullOrEmpty(string_5))
			{
				string_6 = Class521.smethod_0(103780);
				result = false;
			}
			else if (!char.IsLetter(string_5, 0))
			{
				string_6 = Class521.smethod_0(103841);
				result = false;
			}
			else if (string_5.Trim().ToCharArray().Contains(' '))
			{
				string_6 = Class521.smethod_0(103914);
				result = false;
			}
			else
			{
				for (int i = 0; i < string_5.Length; i++)
				{
					UserDefineIndScript.Class447 @class = new UserDefineIndScript.Class447();
					@class.char_0 = string_5[i];
					if (Path.GetInvalidFileNameChars().Any(new Func<char, bool>(@class.method_0)))
					{
						string_6 = Class521.smethod_0(103580) + (i + 1).ToString() + Class521.smethod_0(103975);
						return false;
					}
				}
				string_6 = string.Empty;
				result = true;
			}
			return result;
		}

		// Token: 0x060022C7 RID: 8903 RVA: 0x000F5598 File Offset: 0x000F3798
		public UserDefineIndScript(string name, string code, List<UserDefineParam> Uparams, bool mainK, string descript, bool check, string yLine, string instruction = "")
		{
			this.Name = name;
			this.Code = code;
			this.UserDefineParams = Uparams;
			this.MainK = mainK;
			this.YLine = yLine;
			this.Check = check;
			this.Script = descript;
			this.Ver = 4.5;
			this.Instruction = instruction;
		}

		// Token: 0x060022C8 RID: 8904 RVA: 0x0000DC4E File Offset: 0x0000BE4E
		public void method_2(UserDefineIndScript userDefineIndScript_0)
		{
			this.Code = userDefineIndScript_0.Code;
			this.UserDefineParams = userDefineIndScript_0.UserDefineParams;
			this.MainK = userDefineIndScript_0.MainK;
			this.Script = userDefineIndScript_0.Script;
			this.UserIndFlag = userDefineIndScript_0.UserIndFlag;
		}

		// Token: 0x060022C9 RID: 8905 RVA: 0x000F55FC File Offset: 0x000F37FC
		public object Clone()
		{
			return new UserDefineIndScript(this.Name, this.Code, this.method_3(this.UserDefineParams), this.MainK, this.Script, this.Check, this.YLine, this.Instruction);
		}

		// Token: 0x060022CA RID: 8906 RVA: 0x000F5648 File Offset: 0x000F3848
		private List<UserDefineParam> method_3(List<UserDefineParam> list_1)
		{
			List<UserDefineParam> list = new List<UserDefineParam>();
			foreach (UserDefineParam userDefineParam in list_1)
			{
				UserDefineParam userDefineParam2 = userDefineParam.Clone() as UserDefineParam;
				if (userDefineParam2 != null)
				{
					list.Add(userDefineParam2);
				}
			}
			return list;
		}

		// Token: 0x060022CB RID: 8907 RVA: 0x000F56B0 File Offset: 0x000F38B0
		public UserDefineIndScript()
		{
			this.Name = Class521.smethod_0(9268);
			this.Code = Class521.smethod_0(1449);
			this.UserDefineParams = new List<UserDefineParam>();
			this.MainK = true;
			this.Script = Class521.smethod_0(1449);
		}

		// Token: 0x040010D8 RID: 4312
		[CompilerGenerated]
		private List<UserDefineParam> list_0;

		// Token: 0x040010D9 RID: 4313
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040010DA RID: 4314
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040010DB RID: 4315
		[CompilerGenerated]
		private string string_1;

		// Token: 0x040010DC RID: 4316
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x040010DD RID: 4317
		[CompilerGenerated]
		private double double_0;

		// Token: 0x040010DE RID: 4318
		[CompilerGenerated]
		private bool bool_2;

		// Token: 0x040010DF RID: 4319
		[CompilerGenerated]
		private string string_2;

		// Token: 0x040010E0 RID: 4320
		[CompilerGenerated]
		private string string_3;

		// Token: 0x040010E1 RID: 4321
		[CompilerGenerated]
		private string string_4;

		// Token: 0x02000339 RID: 825
		[CompilerGenerated]
		private sealed class Class447
		{
			// Token: 0x060022CD RID: 8909 RVA: 0x000F5708 File Offset: 0x000F3908
			internal bool method_0(char char_1)
			{
				return char_1 == this.char_0;
			}

			// Token: 0x040010E2 RID: 4322
			public char char_0;
		}
	}
}
