﻿using System;
using ns18;

namespace TEx.Trading
{
	// Token: 0x020003AC RID: 940
	[Serializable]
	internal sealed class ShownCondOrder : CondOrder
	{
		// Token: 0x060025ED RID: 9709 RVA: 0x0000E7D0 File Offset: 0x0000C9D0
		public ShownCondOrder()
		{
		}

		// Token: 0x060025EE RID: 9710 RVA: 0x0000E7D8 File Offset: 0x0000C9D8
		public ShownCondOrder(CondOrder co)
		{
			base.method_0(co);
			if (base.StkSymbol != null)
			{
				this.SymbCode = base.StkSymbol.Code;
			}
		}

		// Token: 0x17000657 RID: 1623
		// (get) Token: 0x060025EF RID: 9711 RVA: 0x00103BD4 File Offset: 0x00101DD4
		// (set) Token: 0x060025F0 RID: 9712 RVA: 0x0000E802 File Offset: 0x0000CA02
		public string SymbCode
		{
			get
			{
				return this._SymbCode;
			}
			set
			{
				this._SymbCode = value;
			}
		}

		// Token: 0x17000658 RID: 1624
		// (get) Token: 0x060025F1 RID: 9713 RVA: 0x00103BEC File Offset: 0x00101DEC
		public string CondDesc
		{
			get
			{
				string result;
				if (base.TrailingStopPts != null)
				{
					if (base.CondPrice == 0m)
					{
						result = base.TrailingStopPts.Value / 1.00000000000000000m + Class521.smethod_0(109905);
					}
					else
					{
						result = this.method_2() + Class521.smethod_0(109926);
					}
				}
				else
				{
					result = this.method_2();
				}
				return result;
			}
		}

		// Token: 0x060025F2 RID: 9714 RVA: 0x00103C7C File Offset: 0x00101E7C
		private string method_2()
		{
			string arg = Class521.smethod_0(24877);
			if (base.ComparisonOpt == ComparisonOpt.BiggerOrEqual)
			{
				arg = Class521.smethod_0(24882);
			}
			else if (base.ComparisonOpt == ComparisonOpt.Less)
			{
				arg = Class521.smethod_0(24887);
			}
			else if (base.ComparisonOpt == ComparisonOpt.LessOrEqual)
			{
				arg = Class521.smethod_0(24892);
			}
			return Class521.smethod_0(24982) + arg + base.CondPrice / 1.00000000000000000m;
		}

		// Token: 0x17000659 RID: 1625
		// (get) Token: 0x060025F3 RID: 9715 RVA: 0x00103D0C File Offset: 0x00101F0C
		public string StatusDesc
		{
			get
			{
				string result = Class521.smethod_0(109947);
				if (base.OrderStatus == OrderStatus.Executed)
				{
					result = Class521.smethod_0(109960);
				}
				else if (base.OrderStatus == OrderStatus.Canceled)
				{
					result = Class521.smethod_0(109973);
				}
				return result;
			}
		}

		// Token: 0x1700065A RID: 1626
		// (get) Token: 0x060025F4 RID: 9716 RVA: 0x00103D54 File Offset: 0x00101F54
		public string LongShortDesc
		{
			get
			{
				string result = Class521.smethod_0(3210);
				if (base.OrderType != OrderType.Order_CloseLong && base.OrderType != OrderType.Order_OpenShort)
				{
					if (base.OrderType != OrderType.Order_CloseLongRevOpen)
					{
						if (base.OrderType == OrderType.Order_OpenLong || base.OrderType == OrderType.Order_CloseShort || base.OrderType == OrderType.Order_CloseShortRevOpen)
						{
							result = Class521.smethod_0(18676);
							goto IL_5A;
						}
						goto IL_5A;
					}
				}
				result = Class521.smethod_0(18671);
				IL_5A:
				return result;
			}
		}

		// Token: 0x1700065B RID: 1627
		// (get) Token: 0x060025F5 RID: 9717 RVA: 0x00103DC0 File Offset: 0x00101FC0
		public string OpenCloseDesc
		{
			get
			{
				string result = Class521.smethod_0(3210);
				if (base.OrderType == OrderType.Order_OpenLong || base.OrderType == OrderType.Order_OpenShort)
				{
					result = Class521.smethod_0(25064);
				}
				if (base.OrderType != OrderType.Order_CloseLong)
				{
					if (base.OrderType != OrderType.Order_CloseShort)
					{
						if (base.OrderType == OrderType.Order_CloseLongRevOpen || base.OrderType == OrderType.Order_CloseShortRevOpen)
						{
							result = Class521.smethod_0(24753);
							goto IL_65;
						}
						goto IL_65;
					}
				}
				result = Class521.smethod_0(25073);
				IL_65:
				return result;
			}
		}

		// Token: 0x0400124E RID: 4686
		private string _SymbCode;
	}
}
