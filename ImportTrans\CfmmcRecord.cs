﻿using System;
using System.Collections.Generic;
using System.Linq;
using ns18;

namespace TEx.ImportTrans
{
	// Token: 0x02000368 RID: 872
	public sealed class CfmmcRecord : IStoreElement
	{
		// Token: 0x0600247F RID: 9343 RVA: 0x000FE168 File Offset: 0x000FC368
		private DateTime method_0(List<string> list_1)
		{
			return Convert.ToDateTime(list_1[12]);
		}

		// Token: 0x06002480 RID: 9344 RVA: 0x000FE188 File Offset: 0x000FC388
		public DateTime method_1()
		{
			if (!this.list_0.Any<List<string>>())
			{
				throw new Exception(Class521.smethod_0(108379));
			}
			return this.method_0(this.list_0.First<List<string>>());
		}

		// Token: 0x06002481 RID: 9345 RVA: 0x000FE1C8 File Offset: 0x000FC3C8
		public DateTime method_2()
		{
			if (!this.list_0.Any<List<string>>())
			{
				throw new Exception(Class521.smethod_0(108448));
			}
			return this.method_0(this.list_0.Last<List<string>>());
		}

		// Token: 0x17000637 RID: 1591
		// (get) Token: 0x06002482 RID: 9346 RVA: 0x000FE208 File Offset: 0x000FC408
		public List<List<string>> RecordList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x17000638 RID: 1592
		// (get) Token: 0x06002483 RID: 9347 RVA: 0x000FE220 File Offset: 0x000FC420
		public string ID
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x06002484 RID: 9348 RVA: 0x0000E3A7 File Offset: 0x0000C5A7
		public void method_3(string string_1, List<List<string>> list_1)
		{
			this.string_0 = string_1;
			this.list_0 = list_1;
		}

		// Token: 0x040011A3 RID: 4515
		private List<List<string>> list_0;

		// Token: 0x040011A4 RID: 4516
		private string string_0;
	}
}
