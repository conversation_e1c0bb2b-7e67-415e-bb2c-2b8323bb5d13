﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns12;
using ns18;
using TEx;

namespace ns26
{
	// Token: 0x0200023D RID: 573
	[DesignerCategory("Code")]
	internal sealed class Class310 : SplitContainer
	{
		// Token: 0x06001878 RID: 6264 RVA: 0x000A8EB0 File Offset: 0x000A70B0
		public Class310()
		{
			base.Orientation = Orientation.Horizontal;
			base.Panel2Collapsed = true;
			base.Dock = DockStyle.Fill;
			base.Panel1.BackColor = Color.Beige;
			this.SplitContainerList = new List<SplitContainer>();
			base.Tag = Class521.smethod_0(2606);
			this.label_0 = new Label();
			base.Panel1.Controls.Add(this.label_0);
			this.label_0.Size = new Size(180, 50);
			this.label_0.BorderStyle = BorderStyle.FixedSingle;
			this.label_0.ForeColor = Color.Black;
			this.label_0.Text = Class521.smethod_0(64291);
			this.label_0.TextAlign = ContentAlignment.MiddleCenter;
			this.label_0.Location = new Point(base.Width - 190, 10);
			this.contextMenu_0 = new ContextMenu();
			MenuItem menuItem = new MenuItem();
			menuItem.Text = Class521.smethod_0(64332);
			menuItem.Popup += this.method_9;
			MenuItem menuItem2 = new MenuItem();
			menuItem2.Text = Class521.smethod_0(64349);
			menuItem2.Click += this.method_5;
			menuItem.MenuItems.Add(menuItem2);
			MenuItem menuItem3 = new MenuItem();
			menuItem3.Text = Class521.smethod_0(64362);
			menuItem3.Click += this.method_6;
			menuItem.MenuItems.Add(menuItem3);
			MenuItem menuItem4 = new MenuItem();
			menuItem4.Text = Class521.smethod_0(64375);
			menuItem4.Click += this.method_4;
			menuItem.MenuItems.Add(menuItem4);
			MenuItem menuItem5 = new MenuItem();
			menuItem5.Text = Class521.smethod_0(64388);
			MenuItem menuItem6 = new MenuItem();
			menuItem6.Text = Class521.smethod_0(64405);
			menuItem6.Click += this.method_7;
			MenuItem menuItem7 = new MenuItem();
			menuItem7.Text = Class521.smethod_0(64414);
			menuItem7.Click += this.method_8;
			menuItem5.MenuItems.Add(menuItem6);
			menuItem5.MenuItems.Add(menuItem7);
			MenuItem menuItem8 = new MenuItem();
			menuItem8.Text = Class521.smethod_0(64423);
			menuItem8.Click += this.method_2;
			this.contextMenu_0.MenuItems.Add(menuItem);
			this.contextMenu_0.MenuItems.Add(menuItem5);
			this.contextMenu_0.MenuItems.Add(menuItem8);
			this.ContextMenu = this.contextMenu_0;
			base.MouseClick += this.Class310_MouseClick;
			base.SizeChanged += this.Class310_SizeChanged;
			Base.UI.NewSwitchAcctTabAdding += this.method_3;
			this.method_11(this);
		}

		// Token: 0x06001879 RID: 6265 RVA: 0x000A91A4 File Offset: 0x000A73A4
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Graphics graphics = Graphics.FromHwnd(base.Panel1.Handle);
			Rectangle rect = new Rectangle(270, 200, 230, 140);
			Pen pen = new Pen(Color.Black, 10f);
			graphics.DrawRectangle(pen, rect);
		}

		// Token: 0x0600187A RID: 6266 RVA: 0x000041B9 File Offset: 0x000023B9
		private void Class310_MouseClick(object sender, MouseEventArgs e)
		{
		}

		// Token: 0x0600187B RID: 6267 RVA: 0x0000A0CD File Offset: 0x000082CD
		private void method_0(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Right)
			{
				this.splitterPanel_0 = (SplitterPanel)sender;
			}
		}

		// Token: 0x0600187C RID: 6268 RVA: 0x0000A0EA File Offset: 0x000082EA
		private void method_1(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Right)
			{
				this.splitContainer_0 = (SplitContainer)sender;
			}
		}

		// Token: 0x0600187D RID: 6269 RVA: 0x000A91FC File Offset: 0x000A73FC
		private void method_2(object sender, EventArgs e)
		{
			if (this.splitterPanel_0 != null)
			{
				SplitContainer splitContainer = (SplitContainer)this.splitterPanel_0.Parent;
				if (splitContainer != null && !splitContainer.Panel2Collapsed)
				{
					SplitterPanel splitterPanel;
					if (this.splitterPanel_0 == splitContainer.Panel1)
					{
						splitterPanel = splitContainer.Panel2;
					}
					else
					{
						splitterPanel = splitContainer.Panel1;
					}
					if (splitterPanel.Controls.Count > 0)
					{
						Control value = splitterPanel.Controls[0];
						SplitterPanel splitterPanel2 = (SplitterPanel)splitContainer.Parent;
						splitterPanel2.Controls.Remove(splitContainer);
						splitterPanel2.Controls.Add(value);
					}
					splitContainer.Dispose();
					this.list_0.Remove(splitContainer);
				}
			}
		}

		// Token: 0x0600187E RID: 6270 RVA: 0x0000A107 File Offset: 0x00008307
		private void Class310_SizeChanged(object sender, EventArgs e)
		{
			this.label_0.Location = new Point(base.Width - 190, 10);
		}

		// Token: 0x0600187F RID: 6271 RVA: 0x0000A129 File Offset: 0x00008329
		private void method_3(EventArgs19 eventArgs19_0)
		{
			if (this.TransTabCtrl != null)
			{
				MessageBox.Show(Class521.smethod_0(64440), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				eventArgs19_0.Cancel = true;
			}
		}

		// Token: 0x06001880 RID: 6272 RVA: 0x000A92A0 File Offset: 0x000A74A0
		private void method_4(object sender, EventArgs e)
		{
			if (this.label_0.Visible)
			{
				this.label_0.Visible = false;
			}
			if (this.TransTabCtrl == null)
			{
				new TransTabCtrl(this.splitterPanel_0).vmethod_0();
			}
			else
			{
				MessageBox.Show(Class521.smethod_0(64440), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06001881 RID: 6273 RVA: 0x000A9300 File Offset: 0x000A7500
		private void method_5(object sender, EventArgs e)
		{
			if (this.label_0.Visible)
			{
				this.label_0.Visible = false;
			}
			ChtCtrl_KLine chtCtrl_KLine = Base.UI.smethod_142(null);
			if (this.splitterPanel_0 != null)
			{
				this.splitterPanel_0.Controls.Add(chtCtrl_KLine);
				chtCtrl_KLine.ContainerSpltPanel = this.splitterPanel_0;
			}
		}

		// Token: 0x06001882 RID: 6274 RVA: 0x000A935C File Offset: 0x000A755C
		private void method_6(object sender, EventArgs e)
		{
			if (this.label_0.Visible)
			{
				this.label_0.Visible = false;
			}
			if (this.splitterPanel_0 != null)
			{
				ChtCtrl_Tick chtCtrl_Tick = Base.UI.smethod_144(null);
				this.splitterPanel_0.Controls.Add(chtCtrl_Tick);
				chtCtrl_Tick.ContainerSpltPanel = this.splitterPanel_0;
			}
		}

		// Token: 0x06001883 RID: 6275 RVA: 0x0000A159 File Offset: 0x00008359
		private void method_7(object sender, EventArgs e)
		{
			this.method_10(Orientation.Vertical);
		}

		// Token: 0x06001884 RID: 6276 RVA: 0x0000A164 File Offset: 0x00008364
		private void method_8(object sender, EventArgs e)
		{
			this.method_10(Orientation.Horizontal);
		}

		// Token: 0x06001885 RID: 6277 RVA: 0x0000A16F File Offset: 0x0000836F
		private void method_9(object sender, EventArgs e)
		{
			if (this.TransTabCtrl != null)
			{
				((MenuItem)sender).MenuItems[2].Enabled = false;
			}
		}

		// Token: 0x06001886 RID: 6278 RVA: 0x000A93B8 File Offset: 0x000A75B8
		private void method_10(Orientation orientation_0)
		{
			if (this.label_0.Visible)
			{
				this.label_0.Visible = false;
			}
			SplitContainer splitContainer = new SplitContainer();
			splitContainer.Orientation = orientation_0;
			splitContainer.Dock = DockStyle.Fill;
			splitContainer.BorderStyle = BorderStyle.FixedSingle;
			splitContainer.SplitterWidth = 1;
			splitContainer.MouseClick += this.method_1;
			splitContainer.Tag = Guid.NewGuid().ToString().Substring(0, 23);
			this.method_11(splitContainer);
			if (this.splitterPanel_0 != null)
			{
				this.splitterPanel_0.Controls.Add(splitContainer);
				int value;
				if (orientation_0 == Orientation.Vertical)
				{
					value = splitContainer.Width;
				}
				else
				{
					value = this.splitterPanel_0.Height;
				}
				splitContainer.SplitterDistance = (int)Math.Round(value / 2m, 0);
				this.list_0.Add(splitContainer);
			}
		}

		// Token: 0x06001887 RID: 6279 RVA: 0x0000A192 File Offset: 0x00008392
		private void method_11(SplitContainer splitContainer_1)
		{
			splitContainer_1.Panel1.MouseClick += this.method_0;
			splitContainer_1.Panel2.MouseClick += this.method_0;
		}

		// Token: 0x06001888 RID: 6280 RVA: 0x0000A1C4 File Offset: 0x000083C4
		protected void Dispose(bool disposing)
		{
			Base.UI.NewSwitchAcctTabAdding -= this.method_3;
			base.Dispose(disposing);
		}

		// Token: 0x06001889 RID: 6281 RVA: 0x000A949C File Offset: 0x000A769C
		private TransTabCtrl method_12(SplitterPanel splitterPanel_1)
		{
			if (splitterPanel_1.Controls.Count > 0)
			{
				TransTabCtrl result;
				using (IEnumerator enumerator = splitterPanel_1.Controls.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						object obj = enumerator.Current;
						if (obj.GetType() == typeof(TransTabCtrl))
						{
							TransTabCtrl transTabCtrl = obj as TransTabCtrl;
							if (!transTabCtrl.IsSwitchedBehind)
							{
								result = transTabCtrl;
								goto IL_67;
							}
						}
					}
					goto IL_6C;
				}
				IL_67:
				return result;
			}
			IL_6C:
			return null;
		}

		// Token: 0x0600188A RID: 6282 RVA: 0x000A952C File Offset: 0x000A772C
		private List<ChtCtrl> method_13(SplitterPanel splitterPanel_1)
		{
			List<ChtCtrl> list = new List<ChtCtrl>();
			if (splitterPanel_1.Controls.Count > 0)
			{
				foreach (object obj in splitterPanel_1.Controls)
				{
					if (obj is ChtCtrl)
					{
						ChtCtrl chtCtrl = obj as ChtCtrl;
						if (!chtCtrl.IsSwitchedBehind)
						{
							list.Add(chtCtrl);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x17000418 RID: 1048
		// (get) Token: 0x0600188B RID: 6283 RVA: 0x000A95B8 File Offset: 0x000A77B8
		public TransTabCtrl TransTabCtrl
		{
			get
			{
				TransTabCtrl result;
				foreach (SplitContainer splitContainer in this.SplitContainerList)
				{
					TransTabCtrl transTabCtrl = this.method_12(splitContainer.Panel1);
					if (transTabCtrl != null)
					{
						result = transTabCtrl;
						goto IL_5D;
					}
					transTabCtrl = this.method_12(splitContainer.Panel2);
					if (transTabCtrl != null)
					{
						result = transTabCtrl;
						goto IL_5D;
					}
				}
				return null;
				IL_5D:
				return result;
			}
		}

		// Token: 0x17000419 RID: 1049
		// (get) Token: 0x0600188C RID: 6284 RVA: 0x000A963C File Offset: 0x000A783C
		public List<ChtCtrl> TExChtCtrlList
		{
			get
			{
				List<ChtCtrl> list = new List<ChtCtrl>();
				foreach (SplitContainer splitContainer in this.SplitContainerList)
				{
					list.AddRange(this.method_13(splitContainer.Panel1));
					list.AddRange(this.method_13(splitContainer.Panel2));
				}
				return list;
			}
		}

		// Token: 0x1700041A RID: 1050
		// (get) Token: 0x0600188D RID: 6285 RVA: 0x000A96B8 File Offset: 0x000A78B8
		public int TotalCtrls
		{
			get
			{
				int result;
				if (this.IfBlankPanelExist)
				{
					result = 0;
				}
				else
				{
					result = ((this.TransTabCtrl == null) ? 0 : 1) + this.TExChtCtrlList.Count;
				}
				return result;
			}
		}

		// Token: 0x1700041B RID: 1051
		// (get) Token: 0x0600188E RID: 6286 RVA: 0x000A96F0 File Offset: 0x000A78F0
		public bool IfChanged
		{
			get
			{
				return !this.label_0.Visible;
			}
		}

		// Token: 0x1700041C RID: 1052
		// (get) Token: 0x0600188F RID: 6287 RVA: 0x000A9710 File Offset: 0x000A7910
		// (set) Token: 0x06001890 RID: 6288 RVA: 0x0000A1E0 File Offset: 0x000083E0
		public List<SplitContainer> SplitContainerList
		{
			get
			{
				if (this.list_0.Count == 0 || this.list_0[0].GetType() != typeof(Class310))
				{
					this.list_0.Insert(0, this);
				}
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x1700041D RID: 1053
		// (get) Token: 0x06001891 RID: 6289 RVA: 0x000A9760 File Offset: 0x000A7960
		public bool IfBlankPanelExist
		{
			get
			{
				bool result;
				foreach (SplitContainer splitContainer in this.SplitContainerList)
				{
					if (splitContainer.Panel1.Controls.Count == 0)
					{
						result = true;
						goto IL_68;
					}
					if (!splitContainer.Panel2Collapsed && splitContainer.Panel2.Controls.Count == 0)
					{
						result = true;
						goto IL_68;
					}
				}
				return false;
				IL_68:
				return result;
			}
		}

		// Token: 0x1700041E RID: 1054
		// (get) Token: 0x06001892 RID: 6290 RVA: 0x000A97EC File Offset: 0x000A79EC
		public ChartUISettings ChartUI
		{
			get
			{
				ChartUISettings result = default(ChartUISettings);
				result.SetSplitContainerParamList(this.SplitContainerList);
				result.SetChtCtrlParamList(this.TExChtCtrlList);
				result.SetTransTabCtrlParam(this.TransTabCtrl);
				if (this.TotalCtrls == 1)
				{
					string singleFixedContent;
					if (this.TransTabCtrl != null)
					{
						singleFixedContent = typeof(TransTabCtrl).ToString();
					}
					else
					{
						singleFixedContent = this.TExChtCtrlList.First<ChtCtrl>().GetType().ToString();
					}
					result.SingleFixedContent = singleFixedContent;
				}
				return result;
			}
		}

		// Token: 0x04000C43 RID: 3139
		private Label label_0;

		// Token: 0x04000C44 RID: 3140
		private ContextMenu contextMenu_0;

		// Token: 0x04000C45 RID: 3141
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000C46 RID: 3142
		private SplitContainer splitContainer_0;

		// Token: 0x04000C47 RID: 3143
		private List<SplitContainer> list_0;
	}
}
