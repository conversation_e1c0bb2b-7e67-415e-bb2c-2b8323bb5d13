﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns18;
using ns28;
using ns3;

namespace TEx.ImportTrans
{
	// Token: 0x0200036E RID: 878
	public sealed class CCfmmcRecorderStore : Class474, Interface4
	{
		// Token: 0x060024A1 RID: 9377 RVA: 0x0000E44E File Offset: 0x0000C64E
		public CCfmmcRecorderStore()
		{
			this.string_0 = CfmmcRecImporter.CfmmcRecFileLocalPath;
		}

		// Token: 0x060024A2 RID: 9378 RVA: 0x000FE494 File Offset: 0x000FC694
		private XElement method_2(CfmmcRecord cfmmcRecord_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(105808));
			xelement.SetAttributeValue(Class521.smethod_0(105821), cfmmcRecord_0.ID);
			for (int i = 0; i < cfmmcRecord_0.RecordList.Count; i++)
			{
				List<string> list = cfmmcRecord_0.RecordList[i];
				XElement xelement2 = new XElement(Class521.smethod_0(105912));
				for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
				{
					xelement2.SetAttributeValue(cfmmcRecFieldsEnum.ToString(), list[(int)cfmmcRecFieldsEnum]);
				}
				xelement.Add(xelement2);
			}
			return xelement;
		}

		// Token: 0x060024A3 RID: 9379 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_3(CfmmcRecord cfmmcRecord_0)
		{
		}

		// Token: 0x060024A4 RID: 9380 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_4(CfmmcRecord cfmmcRecord_0)
		{
		}

		// Token: 0x060024A5 RID: 9381 RVA: 0x000FE168 File Offset: 0x000FC368
		private DateTime method_5(List<string> list_0)
		{
			return Convert.ToDateTime(list_0[12]);
		}

		// Token: 0x060024A6 RID: 9382 RVA: 0x000FE548 File Offset: 0x000FC748
		private CfmmcRecord method_6(CfmmcRecord cfmmcRecord_0, CfmmcRecord cfmmcRecord_1)
		{
			List<string> list_ = cfmmcRecord_0.RecordList.Last<List<string>>();
			DateTime t = this.method_5(list_);
			for (int i = 0; i < cfmmcRecord_1.RecordList.Count; i++)
			{
				List<string> list = cfmmcRecord_1.RecordList[i];
				if (this.method_5(list) > t)
				{
					cfmmcRecord_0.RecordList.Add(list);
				}
			}
			return cfmmcRecord_0;
		}

		// Token: 0x060024A7 RID: 9383 RVA: 0x000FE5B0 File Offset: 0x000FC7B0
		private IStoreElement method_7(IStoreElement istoreElement_0, IStoreElement istoreElement_1)
		{
			return this.method_6(istoreElement_0 as CfmmcRecord, istoreElement_1 as CfmmcRecord);
		}

		// Token: 0x060024A8 RID: 9384 RVA: 0x000FE5D4 File Offset: 0x000FC7D4
		private void method_8(IStoreElement istoreElement_0)
		{
			XElement xelement = this.method_2(istoreElement_0 as CfmmcRecord);
			if (!File.Exists(this.string_0))
			{
				XDocument xdocument = new XDocument();
				XElement xelement2 = new XElement(Class521.smethod_0(105723));
				xdocument.Add(xelement2);
				xelement2.Add(xelement);
				xdocument.Save(this.string_0);
			}
			else
			{
				XDocument xdocument2 = XDocument.Load(this.string_0);
				IEnumerable<XElement> source = xdocument2.Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(105808));
				for (int i = 0; i < source.Count<XElement>(); i++)
				{
					XElement xelement3 = source.ElementAt(i);
					if (xelement3.Attribute(Class521.smethod_0(105821)).Value == xelement.Attribute(Class521.smethod_0(105821)).Value)
					{
						xelement3.ReplaceWith(xelement);
						xdocument2.Save(this.string_0);
						return;
					}
				}
				xdocument2.Element(Class521.smethod_0(105723)).Add(xelement);
				xdocument2.Save(this.string_0);
			}
		}

		// Token: 0x060024A9 RID: 9385 RVA: 0x000FE704 File Offset: 0x000FC904
		public void imethod_0(IStoreElement istoreElement_0)
		{
			if (istoreElement_0.GetType() == typeof(CfmmcRecord))
			{
				if ((istoreElement_0 as CfmmcRecord).RecordList != null)
				{
					IStoreElement storeElement = this.imethod_4(istoreElement_0.ID);
					IStoreElement istoreElement_;
					if (storeElement != null)
					{
						istoreElement_ = this.method_7(storeElement, istoreElement_0);
					}
					else
					{
						istoreElement_ = istoreElement_0;
					}
					this.method_8(istoreElement_);
				}
			}
		}

		// Token: 0x060024AA RID: 9386 RVA: 0x000041B9 File Offset: 0x000023B9
		public void imethod_1(IStoreElement istoreElement_0)
		{
		}

		// Token: 0x060024AB RID: 9387 RVA: 0x000041B9 File Offset: 0x000023B9
		public void imethod_2(IStoreElement istoreElement_0)
		{
		}

		// Token: 0x060024AC RID: 9388 RVA: 0x000FE75C File Offset: 0x000FC95C
		private CfmmcRecord method_9(XElement xelement_0)
		{
			CfmmcRecord cfmmcRecord = new CfmmcRecord();
			string value = xelement_0.Attribute(Class521.smethod_0(105821)).Value;
			List<List<string>> list = new List<List<string>>();
			foreach (XElement xelement in xelement_0.Elements(Class521.smethod_0(105912)))
			{
				List<string> list2 = new List<string>();
				for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
				{
					XAttribute xattribute = xelement.Attribute(cfmmcRecFieldsEnum.ToString());
					list2.Add(xattribute.Value);
				}
				list.Add(list2);
			}
			cfmmcRecord.method_3(value, list);
			return cfmmcRecord;
		}

		// Token: 0x060024AD RID: 9389 RVA: 0x000FE834 File Offset: 0x000FCA34
		protected override List<IStoreElement> vmethod_0()
		{
			List<CfmmcRecord> list = new List<CfmmcRecord>();
			foreach (XElement xelement_ in XDocument.Load(this.string_0).Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(105808)))
			{
				CfmmcRecord item = this.method_9(xelement_);
				list.Add(item);
			}
			return list.Select(new Func<CfmmcRecord, IStoreElement>(CCfmmcRecorderStore.<>c.<>9.method_0)).ToList<IStoreElement>();
		}

		// Token: 0x060024AE RID: 9390 RVA: 0x000FE8F0 File Offset: 0x000FCAF0
		public IStoreElement imethod_4(string string_1)
		{
			CCfmmcRecorderStore.Class476 @class = new CCfmmcRecorderStore.Class476();
			@class.string_0 = string_1;
			IStoreElement result;
			if (!File.Exists(this.string_0))
			{
				result = null;
			}
			else
			{
				List<IStoreElement> source = base.imethod_3();
				IStoreElement storeElement;
				try
				{
					storeElement = source.Single(new Func<IStoreElement, bool>(@class.method_0));
				}
				catch
				{
					storeElement = null;
				}
				result = storeElement;
			}
			return result;
		}

		// Token: 0x02000370 RID: 880
		[CompilerGenerated]
		private sealed class Class476
		{
			// Token: 0x060024B3 RID: 9395 RVA: 0x000FE954 File Offset: 0x000FCB54
			internal bool method_0(IStoreElement istoreElement_0)
			{
				return istoreElement_0.ID == this.string_0;
			}

			// Token: 0x040011B0 RID: 4528
			public string string_0;
		}
	}
}
