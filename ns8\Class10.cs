﻿using System;
using ns18;
using TEx;
using TEx.Util;

namespace ns8
{
	// Token: 0x0200001F RID: 31
	internal class Class10 : WebApiBgWorker
	{
		// Token: 0x060000B1 RID: 177 RVA: 0x00002D2D File Offset: 0x00000F2D
		public Class10() : base(TApp.FULLHOST + Class521.smethod_0(3606), 30000)
		{
		}

		// Token: 0x060000B2 RID: 178 RVA: 0x000129AC File Offset: 0x00010BAC
		protected string method_0(string string_0)
		{
			int length = string_0.IndexOf(Class521.smethod_0(1733));
			return string_0.Substring(0, length);
		}

		// Token: 0x04000042 RID: 66
		private const int int_0 = 30000;
	}
}
