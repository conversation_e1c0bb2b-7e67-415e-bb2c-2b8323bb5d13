﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.CompilerServices;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020000AC RID: 172
	internal abstract class TOdrLine : IDisposable
	{
		// Token: 0x060005EC RID: 1516 RVA: 0x000047B7 File Offset: 0x000029B7
		public TOdrLine(ChartCS chart, decimal price)
		{
			this.chartCS_0 = chart;
			this.decimal_0 = price;
		}

		// Token: 0x060005ED RID: 1517 RVA: 0x000047CF File Offset: 0x000029CF
		public void Dispose()
		{
			this.vmethod_0(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x060005EE RID: 1518 RVA: 0x0002D4F0 File Offset: 0x0002B6F0
		~TOdrLine()
		{
			this.vmethod_0(false);
		}

		// Token: 0x060005EF RID: 1519 RVA: 0x000047E0 File Offset: 0x000029E0
		protected virtual void vmethod_0(bool bool_1)
		{
			if (!this.bool_0)
			{
				this.bool_0 = true;
			}
		}

		// Token: 0x060005F0 RID: 1520 RVA: 0x0002D520 File Offset: 0x0002B720
		protected void method_0()
		{
			LineObj lineObj = this.vmethod_5(this.Price);
			if (lineObj != null)
			{
				this.lineObj_0 = lineObj;
				this.method_1(lineObj);
			}
		}

		// Token: 0x060005F1 RID: 1521 RVA: 0x0002D550 File Offset: 0x0002B750
		private void method_1(LineObj lineObj_1)
		{
			this.Chart.GraphPane.GraphObjList.Add(lineObj_1);
			if (this.Chart.TOdrLineList == null)
			{
				this.Chart.TOdrLineList = new List<TOdrLine>();
			}
			this.Chart.TOdrLineList.Add(this);
			this.method_2();
			this.Chart.ZedGraphControl.Refresh();
			if (Base.UI.TOdrLineList == null)
			{
				Base.UI.TOdrLineList = new List<TOdrLine>();
			}
			Base.UI.TOdrLineList.Add(this);
		}

		// Token: 0x060005F2 RID: 1522 RVA: 0x0002D5D8 File Offset: 0x0002B7D8
		private void method_2()
		{
			TextObj textObj = this.vmethod_6();
			this.TextBox = textObj;
			this.Chart.method_60(this.TextBox);
			this.Chart.GraphPane.GraphObjList.Add(textObj);
		}

		// Token: 0x060005F3 RID: 1523
		public abstract void vmethod_1();

		// Token: 0x060005F4 RID: 1524 RVA: 0x000047F5 File Offset: 0x000029F5
		public virtual void vmethod_2()
		{
			this.method_3(true);
		}

		// Token: 0x060005F5 RID: 1525 RVA: 0x0002D61C File Offset: 0x0002B81C
		private void method_3(bool bool_1)
		{
			if (bool_1 && !this.IsCurrent)
			{
				this.method_4();
			}
			else if (this.Chart.GraphPane != null)
			{
				double min = this.Chart.GraphPane.XAxis.Scale.Min;
				double max = this.Chart.GraphPane.XAxis.Scale.Max;
				double num = this.vmethod_3();
				if (this.Line.Location.X1 != min || this.Line.Location.X2 != max || this.Line.Location.Y != num)
				{
					this.vmethod_4(num);
					this.Chart.ZedGraphControl.Refresh();
				}
			}
		}

		// Token: 0x060005F6 RID: 1526
		protected abstract double vmethod_3();

		// Token: 0x060005F7 RID: 1527 RVA: 0x0002D6E0 File Offset: 0x0002B8E0
		public virtual void vmethod_4(double double_0)
		{
			double min = this.Chart.GraphPane.XAxis.Scale.Min;
			double width = this.Chart.GraphPane.XAxis.Scale.Max - min;
			this.Line.Location = new Location(min, double_0, width, 0.0, CoordType.AxisXYScale, AlignH.Center, AlignV.Center);
			this.vmethod_1();
			this.TextBox.Location = new Location(0.0, double_0, this.TextBox.Location.CoordinateFrame);
			this.TextBox.Location.AlignH = AlignH.Left;
			this.TextBox.Location.AlignV = AlignV.Bottom;
		}

		// Token: 0x060005F8 RID: 1528 RVA: 0x0002D798 File Offset: 0x0002B998
		public void method_4()
		{
			try
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_6));
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			try
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_7));
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
			this.Chart.ZedGraphControl.Refresh();
			if (Base.UI.TOdrLineList != null)
			{
				Base.UI.TOdrLineList.Remove(this);
			}
			this.System.IDisposable.Dispose();
		}

		// Token: 0x060005F9 RID: 1529 RVA: 0x0002D844 File Offset: 0x0002BA44
		protected virtual LineObj vmethod_5(decimal decimal_1)
		{
			LineObj result;
			try
			{
				Color color = this.vmethod_7();
				double num = Convert.ToDouble(decimal_1);
				double min = this.Chart.GraphPane.XAxis.Scale.Min;
				double max = this.Chart.GraphPane.XAxis.Scale.Max;
				result = new LineObj(color, min, num, max, num)
				{
					IsClippedToChartRect = true,
					Line = 
					{
						Style = this.vmethod_8()
					},
					Tag = TOdrLine.string_1 + Class521.smethod_0(2712) + Guid.NewGuid().ToString().Substring(0, 19),
					ZOrder = ZOrder.B_BehindLegend
				};
				goto IL_B0;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return null;
			IL_B0:
			return result;
		}

		// Token: 0x060005FA RID: 1530 RVA: 0x0002D91C File Offset: 0x0002BB1C
		protected virtual TextObj vmethod_6()
		{
			return new TextObj(this.Text, 0.0, Convert.ToDouble(this.Price))
			{
				FontSpec = 
				{
					Fill = 
					{
						IsVisible = false
					},
					FontColor = this.Line.Line.Color,
					Border = 
					{
						IsVisible = false
					},
					StringAlignment = StringAlignment.Near,
					Size = 10f
				},
				Location = 
				{
					AlignH = AlignH.Left,
					AlignV = AlignV.Bottom
				},
				ZOrder = ZOrder.B_BehindLegend,
				Tag = this.Line.Tag
			};
		}

		// Token: 0x060005FB RID: 1531 RVA: 0x0002D9D8 File Offset: 0x0002BBD8
		protected string method_5(decimal decimal_1)
		{
			int decimals = 2;
			TradingSymbol tradingSymbol = null;
			if (this.Chart != null && this.Chart.Symbol != null)
			{
				tradingSymbol = this.Chart.Symbol.MstSymbol;
			}
			if (tradingSymbol != null)
			{
				decimals = tradingSymbol.DigitNb;
			}
			string result;
			if (decimal_1 == 0m)
			{
				result = Class521.smethod_0(59526);
			}
			else
			{
				result = (Math.Round(decimal_1, decimals) / 1.000000000000000000m).ToString();
			}
			return result;
		}

		// Token: 0x060005FC RID: 1532
		protected abstract Color vmethod_7();

		// Token: 0x060005FD RID: 1533
		protected abstract DashStyle vmethod_8();

		// Token: 0x17000129 RID: 297
		// (get) Token: 0x060005FE RID: 1534 RVA: 0x0002DA60 File Offset: 0x0002BC60
		// (set) Token: 0x060005FF RID: 1535 RVA: 0x00004800 File Offset: 0x00002A00
		public LineObj Line
		{
			get
			{
				return this.lineObj_0;
			}
			set
			{
				this.lineObj_0 = value;
			}
		}

		// Token: 0x1700012A RID: 298
		// (get) Token: 0x06000600 RID: 1536 RVA: 0x0002DA78 File Offset: 0x0002BC78
		// (set) Token: 0x06000601 RID: 1537 RVA: 0x0000480B File Offset: 0x00002A0B
		public ChartCS Chart
		{
			get
			{
				return this.chartCS_0;
			}
			set
			{
				this.chartCS_0 = value;
			}
		}

		// Token: 0x1700012B RID: 299
		// (get) Token: 0x06000602 RID: 1538 RVA: 0x0002DA90 File Offset: 0x0002BC90
		// (set) Token: 0x06000603 RID: 1539 RVA: 0x00004816 File Offset: 0x00002A16
		public TextObj TextBox
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x1700012C RID: 300
		// (get) Token: 0x06000604 RID: 1540 RVA: 0x0002DAA8 File Offset: 0x0002BCA8
		// (set) Token: 0x06000605 RID: 1541 RVA: 0x00004821 File Offset: 0x00002A21
		public decimal Price
		{
			get
			{
				return this.decimal_0;
			}
			set
			{
				this.decimal_0 = value;
			}
		}

		// Token: 0x1700012D RID: 301
		// (get) Token: 0x06000606 RID: 1542 RVA: 0x0002DAC0 File Offset: 0x0002BCC0
		// (set) Token: 0x06000607 RID: 1543 RVA: 0x0000482C File Offset: 0x00002A2C
		public string Text
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x1700012E RID: 302
		// (get) Token: 0x06000608 RID: 1544
		public abstract bool IsCurrent { get; }

		// Token: 0x1700012F RID: 303
		// (get) Token: 0x06000609 RID: 1545
		// (set) Token: 0x0600060A RID: 1546
		public abstract bool HighLighted { get; set; }

		// Token: 0x0600060C RID: 1548 RVA: 0x0002DAD8 File Offset: 0x0002BCD8
		[CompilerGenerated]
		private bool method_6(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is LineObj)
			{
				result = (graphObj_0.Tag == this.Line.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600060D RID: 1549 RVA: 0x0002DB08 File Offset: 0x0002BD08
		[CompilerGenerated]
		private bool method_7(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is TextObj)
			{
				result = (graphObj_0.Tag == this.TextBox.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0400029C RID: 668
		private LineObj lineObj_0;

		// Token: 0x0400029D RID: 669
		private ChartCS chartCS_0;

		// Token: 0x0400029E RID: 670
		private TextObj textObj_0;

		// Token: 0x0400029F RID: 671
		private string string_0;

		// Token: 0x040002A0 RID: 672
		private decimal decimal_0;

		// Token: 0x040002A1 RID: 673
		public static readonly string string_1 = Class521.smethod_0(59535);

		// Token: 0x040002A2 RID: 674
		private bool bool_0;
	}
}
