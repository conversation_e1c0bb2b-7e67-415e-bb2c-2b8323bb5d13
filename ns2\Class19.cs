﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns18;
using ns26;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns2
{
	// Token: 0x0200003B RID: 59
	internal sealed class Class19
	{
		// Token: 0x17000071 RID: 113
		// (get) Token: 0x060001AB RID: 427 RVA: 0x00017F64 File Offset: 0x00016164
		// (set) Token: 0x060001AC RID: 428 RVA: 0x00003131 File Offset: 0x00001331
		public string tscode { get; set; }

		// Token: 0x17000072 RID: 114
		// (get) Token: 0x060001AD RID: 429 RVA: 0x00017F7C File Offset: 0x0001617C
		// (set) Token: 0x060001AE RID: 430 RVA: 0x0000313C File Offset: 0x0000133C
		public List<Forecast> forecasts { get; set; }

		// Token: 0x17000073 RID: 115
		// (get) Token: 0x060001AF RID: 431 RVA: 0x00017F94 File Offset: 0x00016194
		// (set) Token: 0x060001B0 RID: 432 RVA: 0x00003147 File Offset: 0x00001347
		public List<Express> expresses { get; set; }

		// Token: 0x17000074 RID: 116
		// (get) Token: 0x060001B1 RID: 433 RVA: 0x00017FAC File Offset: 0x000161AC
		// (set) Token: 0x060001B2 RID: 434 RVA: 0x00003152 File Offset: 0x00001352
		public DateTime FetchDate { get; set; }

		// Token: 0x060001B3 RID: 435 RVA: 0x00017FC4 File Offset: 0x000161C4
		public SortedDictionary<DateTime, string> method_0()
		{
			SortedDictionary<DateTime, string> sortedDictionary = new SortedDictionary<DateTime, string>();
			if (this.forecasts != null)
			{
				foreach (Forecast forecast in this.forecasts)
				{
					string value = string.Concat(new string[]
					{
						Class521.smethod_0(3445),
						Environment.NewLine,
						forecast.summary,
						Environment.NewLine,
						Class521.smethod_0(3470),
						forecast.end_date,
						Environment.NewLine,
						Class521.smethod_0(3487),
						forecast.ann_date
					});
					try
					{
						sortedDictionary[this.method_1(forecast.ann_date)] = value;
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
				foreach (Express express in this.expresses)
				{
					string text = string.Concat(new object[]
					{
						Class521.smethod_0(3508),
						Environment.NewLine,
						Class521.smethod_0(3533),
						express.diluted_eps,
						Class521.smethod_0(3554),
						Environment.NewLine
					});
					if (express.total_profit != null)
					{
						text = text + Class521.smethod_0(3559) + Utility.GetUnitAbbrNbString(express.total_profit.Value) + Environment.NewLine;
					}
					if (express.n_income != null)
					{
						text = text + Class521.smethod_0(3580) + Utility.GetUnitAbbrNbString(express.n_income.Value) + Environment.NewLine;
					}
					text = string.Concat(new string[]
					{
						text,
						Class521.smethod_0(3470),
						express.end_date,
						Environment.NewLine,
						Class521.smethod_0(3487),
						express.ann_date
					});
					try
					{
						sortedDictionary[this.method_1(express.ann_date)] = text;
					}
					catch (Exception exception_2)
					{
						Class184.smethod_0(exception_2);
					}
				}
			}
			return sortedDictionary;
		}

		// Token: 0x060001B4 RID: 436 RVA: 0x00018278 File Offset: 0x00016478
		private DateTime method_1(string string_1)
		{
			return new DateTime(Convert.ToInt32(string_1.Substring(0, 4)), Convert.ToInt32(string_1.Substring(4, 2)), Convert.ToInt32(string_1.Substring(6, 2)), 15, 0, 0);
		}

		// Token: 0x060001B5 RID: 437 RVA: 0x000182BC File Offset: 0x000164BC
		public void method_2()
		{
			string filePath = Path.Combine(TApp.string_7, this.tscode + Class521.smethod_0(3597));
			try
			{
				string content = JsonConvert.SerializeObject(this);
				Utility.SaveFile(filePath, content, null);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x060001B6 RID: 438 RVA: 0x00018314 File Offset: 0x00016514
		public bool method_3()
		{
			int num = 15;
			int month = DateTime.Now.Month;
			if (month > 2 && month < 5)
			{
				num = 5;
			}
			bool result;
			if (DateTime.Now - this.FetchDate > TimeSpan.FromDays((double)num))
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x040000AB RID: 171
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040000AC RID: 172
		[CompilerGenerated]
		private List<Forecast> list_0;

		// Token: 0x040000AD RID: 173
		[CompilerGenerated]
		private List<Express> list_1;

		// Token: 0x040000AE RID: 174
		[CompilerGenerated]
		private DateTime dateTime_0;
	}
}
