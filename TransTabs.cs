﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using DevComponents.AdvTree;
using DevComponents.DotNetBar;
using Newtonsoft.Json;
using ns10;
using ns13;
using ns15;
using ns18;
using ns19;
using ns20;
using ns21;
using ns23;
using ns26;
using ns31;
using ns4;
using ns6;
using ns7;
using ns9;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000255 RID: 597
	internal sealed class TransTabs : UserControl
	{
		// Token: 0x14000092 RID: 146
		// (add) Token: 0x06001976 RID: 6518 RVA: 0x000B0E50 File Offset: 0x000AF050
		// (remove) Token: 0x06001977 RID: 6519 RVA: 0x000B0E88 File Offset: 0x000AF088
		public event EventHandler DateTimePickerEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001978 RID: 6520 RVA: 0x000B0EC0 File Offset: 0x000AF0C0
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000093 RID: 147
		// (add) Token: 0x06001979 RID: 6521 RVA: 0x000B0EE8 File Offset: 0x000AF0E8
		// (remove) Token: 0x0600197A RID: 6522 RVA: 0x000B0F20 File Offset: 0x000AF120
		public event EventHandler DateTimePickerLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600197B RID: 6523 RVA: 0x000B0F58 File Offset: 0x000AF158
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000094 RID: 148
		// (add) Token: 0x0600197C RID: 6524 RVA: 0x000B0F80 File Offset: 0x000AF180
		// (remove) Token: 0x0600197D RID: 6525 RVA: 0x000B0FB8 File Offset: 0x000AF1B8
		public event EventHandler TUnitsInputChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600197E RID: 6526 RVA: 0x000B0FF0 File Offset: 0x000AF1F0
		protected void method_2()
		{
			EventHandler eventHandler = this.eventHandler_2;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000095 RID: 149
		// (add) Token: 0x0600197F RID: 6527 RVA: 0x000B1018 File Offset: 0x000AF218
		// (remove) Token: 0x06001980 RID: 6528 RVA: 0x000B1050 File Offset: 0x000AF250
		public event EventHandler TPriceInputChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001981 RID: 6529 RVA: 0x000B1088 File Offset: 0x000AF288
		protected void method_3()
		{
			EventHandler eventHandler = this.eventHandler_3;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000096 RID: 150
		// (add) Token: 0x06001982 RID: 6530 RVA: 0x000B10B0 File Offset: 0x000AF2B0
		// (remove) Token: 0x06001983 RID: 6531 RVA: 0x000B10E8 File Offset: 0x000AF2E8
		public event Delegate22 ChangeToHisTransDTRequested
		{
			[CompilerGenerated]
			add
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001984 RID: 6532 RVA: 0x000B1120 File Offset: 0x000AF320
		protected void method_4(ShownHisTrans shownHisTrans_0)
		{
			EventArgs16 e = new EventArgs16(shownHisTrans_0);
			Delegate22 @delegate = this.delegate22_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x14000097 RID: 151
		// (add) Token: 0x06001985 RID: 6533 RVA: 0x000B1148 File Offset: 0x000AF348
		// (remove) Token: 0x06001986 RID: 6534 RVA: 0x000B1180 File Offset: 0x000AF380
		public event MsgEventHandler MsgNotifyNeeded
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x06001987 RID: 6535 RVA: 0x0000AA4B File Offset: 0x00008C4B
		protected void method_5(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x06001988 RID: 6536 RVA: 0x000B11B8 File Offset: 0x000AF3B8
		public TransTabs(SplitterPanel panel)
		{
			this.IsInitiating = true;
			this.method_141();
			this.method_6();
			if (panel != null)
			{
				panel.Controls.Add(this);
				this.ParentSplitPanel = panel;
			}
			this.trdAnalysisPanel_0 = new TrdAnalysisPanel();
			this.trdAnalysisPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_0.Controls.Add(this.trdAnalysisPanel_0);
			this.method_8();
			this.IsInitiating = false;
		}

		// Token: 0x06001989 RID: 6537 RVA: 0x000B1238 File Offset: 0x000AF438
		private void method_6()
		{
			float emSize = TApp.smethod_4(9f, false);
			this.font_0 = new Font(Class521.smethod_0(7183), emSize, FontStyle.Bold);
			this.font_1 = new Font(Class521.smethod_0(7183), emSize, FontStyle.Regular);
			this.tabControl_1.Font = this.font_1;
			this.tabControl_1.SelectedTabFont = this.font_0;
			this.tabControl_4.Font = this.font_1;
			this.tabControl_4.SelectedTabFont = this.font_1;
			emSize = TApp.smethod_4(9f, false);
			Font font = new Font(Class521.smethod_0(7183), emSize, FontStyle.Regular);
			this.label_6.Font = font;
			this.label_5.Font = font;
			this.label_7.Font = font;
			this.comboBox_0.Font = font;
			this.checkBox_0.Font = font;
			this.checkBox_1.Font = font;
			this.class289_0.Font = font;
			this.class289_1.Font = font;
			Font font2 = new Font(Class521.smethod_0(24023), emSize);
			this.numericUpDown_1.Font = font2;
			this.numericUpDown_0.Font = font2;
			Font font3 = new Font(Class521.smethod_0(24023), TApp.smethod_4(8f, false));
			this.label_4.Font = font3;
			foreach (object obj in this.panel_2.Controls)
			{
				((RadioButton)obj).Font = font3;
			}
			Font font4 = new Font(Class521.smethod_0(67439), TApp.smethod_4(11f, false));
			this.button_2.Font = font4;
			this.button_3.Font = font4;
		}

		// Token: 0x0600198A RID: 6538 RVA: 0x000B1420 File Offset: 0x000AF620
		public void method_7(SplitterPanel splitterPanel_2)
		{
			if (splitterPanel_2 != null)
			{
				if (this.ParentSplitPanel != null)
				{
					this.ParentSplitPanel.Controls.Remove(this);
				}
				splitterPanel_2.Controls.Clear();
				splitterPanel_2.Controls.Add(this);
				this.ParentSplitPanel = splitterPanel_2;
				base.Enabled = true;
				base.Visible = true;
			}
		}

		// Token: 0x0600198B RID: 6539 RVA: 0x000B1478 File Offset: 0x000AF678
		public void method_8()
		{
			this.fnDataApiWorker_0 = new FnDataApiWorker();
			this.fnDataApiWorker_0.ResultReceived += this.fnDataApiWorker_0_ResultReceived;
			this.fnDataApiWorker_0.RequestError += this.fnDataApiWorker_0_RequestError;
			this.smethod_0();
			this.Dock = DockStyle.Fill;
			this.tabControl_1.ThemeAware = false;
			this.tabControl_1.Style = eTabStripStyle.OneNote;
			Base.UI.ChartThemeChanged += this.method_10;
			this.tabControlPanel_0.ThemeAware = false;
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_8.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_12.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_13.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_11.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_9.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_4.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_5.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_6.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_7.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_2.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_3.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_10.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_14.Padding = new System.Windows.Forms.Padding(0);
			this.method_19();
			this.method_96();
			this.method_97();
			this.method_67();
			this.method_86();
			this.method_93();
			this.method_35();
			this.method_26(true, null);
			this.tabControl_1.SelectedTabChanging += this.tabControl_1_SelectedTabChanging;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			base.Disposed += this.TransTabs_Disposed;
			if (!TApp.IsStIncluded)
			{
				this.tabItem_12.Visible = false;
				this.tabItem_13.Visible = false;
			}
			this.tabItem_14.Visible = false;
			this.method_12();
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet_ in Base.Data.SymbDataSets)
				{
					this.method_17(symbDataSet_);
				}
			}
			Base.Data.CurrSymblChanged += this.method_107;
			Base.Data.SymbDataSetAdded += this.method_108;
			Base.Data.SymbDataSetRemoved += this.method_109;
			Base.Data.DateSelectionChanged += this.method_110;
			Base.Acct.AccountChanging += this.method_105;
			Base.Acct.AccountChanged += this.method_106;
			Base.Trading.TransCreated += this.method_112;
			Base.Trading.OrderCanceled += this.method_113;
			Base.Trading.OrderCreated += this.method_114;
			Base.Trading.OrderPriceUnitsUpdated += this.method_115;
			Base.Trading.CondOrderCreated += this.method_116;
			Base.Trading.CondOrderExecuted += this.method_117;
			Base.Trading.CondOrderUpdated += this.method_118;
			Base.Trading.CondOrderStatusUpdated += this.method_119;
			Base.Trading.OpenTransListUpdated += this.method_120;
			Base.UI.CurrTradingSymbChanged += this.method_128;
			Base.UI.Form.StockShortSettingChanged += this.method_121;
			Base.UI.Form.IfShowSymbCNNameInOpenTransGridViewChanged += this.method_122;
			this.smethod_1();
		}

		// Token: 0x0600198C RID: 6540 RVA: 0x000B1834 File Offset: 0x000AFA34
		private void fnDataApiWorker_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				try
				{
					Dictionary<string, object> requestDict = e.RequestDict;
					if (requestDict.ContainsKey(Class521.smethod_0(1594)))
					{
						DataGridViewMkt dataGridViewMkt = requestDict[Class521.smethod_0(1594)] as DataGridViewMkt;
						if (requestDict.ContainsKey(Class521.smethod_0(1724)))
						{
							this.method_9(dataGridViewMkt, requestDict[Class521.smethod_0(1724)] as SortableBindingList<ShowMktSymb>);
							DateTime dateTime = Convert.ToDateTime(requestDict[Class521.smethod_0(1603)]);
							dataGridViewMkt.Tag = dateTime;
						}
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x0600198D RID: 6541 RVA: 0x000B18E8 File Offset: 0x000AFAE8
		private void method_9(DataGridViewMkt dataGridViewMkt_1, SortableBindingList<ShowMktSymb> sortableBindingList_0)
		{
			TransTabs.Class321 @class = new TransTabs.Class321();
			@class.dataGridViewMkt_0 = dataGridViewMkt_1;
			@class.sortableBindingList_0 = sortableBindingList_0;
			DataGridViewColumn sortedColumn = @class.dataGridViewMkt_0.SortedColumn;
			@class.string_0 = null;
			@class.listSortDirection_0 = ListSortDirection.Ascending;
			if (sortedColumn != null)
			{
				@class.string_0 = sortedColumn.Name;
				if (sortedColumn.HeaderCell.SortGlyphDirection == SortOrder.Ascending)
				{
					@class.listSortDirection_0 = ListSortDirection.Ascending;
				}
				else
				{
					@class.listSortDirection_0 = ListSortDirection.Descending;
				}
			}
			@class.int_0 = -1;
			if (@class.dataGridViewMkt_0.SelectedRows != null && @class.dataGridViewMkt_0.SelectedRows.Count > 0)
			{
				@class.int_0 = @class.dataGridViewMkt_0.SelectedRows[0].Index;
			}
			if (base.IsHandleCreated)
			{
				base.Invoke(new Action(@class.method_0));
			}
		}

		// Token: 0x0600198E RID: 6542 RVA: 0x0000AA68 File Offset: 0x00008C68
		private void fnDataApiWorker_0_RequestError(object sender, TEx.Comn.ErrorEventArgs e)
		{
			if (e.Exception is WebException)
			{
				e.Exception.Message == Class521.smethod_0(67448);
			}
			Class184.smethod_0(e.Exception);
		}

		// Token: 0x0600198F RID: 6543 RVA: 0x0000AA9F File Offset: 0x00008C9F
		private void method_10(object sender, EventArgs e)
		{
			this.method_11();
		}

		// Token: 0x06001990 RID: 6544 RVA: 0x0000AAA9 File Offset: 0x00008CA9
		private void method_11()
		{
			this.method_12();
			this.method_66();
		}

		// Token: 0x06001991 RID: 6545 RVA: 0x000B19B0 File Offset: 0x000AFBB0
		private void method_12()
		{
			TabColorScheme colorScheme;
			TabColorScheme tabColorScheme_;
			TabColorScheme tabColorScheme_2;
			ColorScheme colorScheme_;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				colorScheme = Base.UI.smethod_58();
				tabColorScheme_ = Base.UI.smethod_64();
				tabColorScheme_2 = Base.UI.smethod_59();
				colorScheme_ = Base.UI.smethod_56();
			}
			else
			{
				colorScheme = Base.UI.smethod_60();
				tabColorScheme_ = Base.UI.smethod_65();
				tabColorScheme_2 = Base.UI.smethod_66();
				colorScheme_ = Base.UI.smethod_57();
			}
			this.tabControl_1.ColorScheme = colorScheme;
			this.method_15(colorScheme_);
			this.method_13(tabColorScheme_);
			this.method_14(tabColorScheme_2);
		}

		// Token: 0x06001992 RID: 6546 RVA: 0x0000AAB9 File Offset: 0x00008CB9
		private void method_13(TabColorScheme tabColorScheme_0)
		{
			this.tabControl_0.ColorScheme = tabColorScheme_0;
			this.tabControl_4.ColorScheme = tabColorScheme_0;
			this.trdAnalysisPanel_0.ColorScheme = tabColorScheme_0;
		}

		// Token: 0x06001993 RID: 6547 RVA: 0x0000AAE1 File Offset: 0x00008CE1
		private void method_14(TabColorScheme tabColorScheme_0)
		{
			this.tabControl_2.ColorScheme = tabColorScheme_0;
			this.tabControl_3.ColorScheme = tabColorScheme_0;
		}

		// Token: 0x06001994 RID: 6548 RVA: 0x000B1A20 File Offset: 0x000AFC20
		private void method_15(ColorScheme colorScheme_0)
		{
			this.method_16(this.tabControlPanel_8, colorScheme_0);
			this.method_16(this.tabControlPanel_0, colorScheme_0);
			this.method_16(this.tabControlPanel_9, colorScheme_0);
			this.method_16(this.tabControlPanel_11, colorScheme_0);
			this.method_16(this.tabControlPanel_12, colorScheme_0);
			this.method_16(this.tabControlPanel_13, colorScheme_0);
		}

		// Token: 0x06001995 RID: 6549 RVA: 0x0000AAFD File Offset: 0x00008CFD
		private void method_16(TabControlPanel tabControlPanel_15, ColorScheme colorScheme_0)
		{
			tabControlPanel_15.ColorScheme = colorScheme_0;
			tabControlPanel_15.ApplyPanelStyle();
		}

		// Token: 0x06001996 RID: 6550 RVA: 0x0000AB0E File Offset: 0x00008D0E
		private void method_17(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged += this.method_101;
			symbDataSet_0.CurrDateChanged += this.method_102;
		}

		// Token: 0x06001997 RID: 6551 RVA: 0x0000AB36 File Offset: 0x00008D36
		private void method_18(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged -= this.method_101;
			symbDataSet_0.CurrDateChanged -= this.method_102;
		}

		// Token: 0x06001998 RID: 6552 RVA: 0x000B1A80 File Offset: 0x000AFC80
		private void method_19()
		{
			this.tabControlPanel_8.ThemeAware = false;
			this.tabControl_0 = new DevComponents.DotNetBar.TabControl();
			Base.UI.smethod_69(this.tabControl_0);
			this.tabControlPanel_8.Controls.Add(this.tabControl_0);
			this.tabControl_0.Font = this.font_1;
			this.tabControl_0.SelectedTabFont = this.font_1;
			this.tabControl_0.SelectedTabChanged += this.tabControl_0_SelectedTabChanged;
			this.method_28();
			if (this.ParentSplitPanel != null)
			{
				foreach (DataGridViewMkt dataGridView_ in this.list_0)
				{
					this.method_36(dataGridView_);
				}
				this.method_36(this.dataGridViewMkt_0);
			}
			this.tabControl_0.RecalcLayout();
			this.CurrShowMktSymbList = this.method_53();
			this.dataGridViewMkt_0.Paint += this.dataGridViewMkt_0_Paint;
			this.dataGridViewMkt_0.MouseMove += this.dataGridViewMkt_0_MouseMove;
			this.dataGridViewMkt_0.MouseDown += this.dataGridViewMkt_0_MouseDown;
			this.dataGridViewMkt_0.DragOver += this.dataGridViewMkt_0_DragOver;
			this.dataGridViewMkt_0.DragDrop += this.dataGridViewMkt_0_DragDrop;
			this.method_25();
			try
			{
				this.method_51();
			}
			catch
			{
			}
		}

		// Token: 0x06001999 RID: 6553 RVA: 0x000B1C08 File Offset: 0x000AFE08
		private ToolStripMenuItem method_20()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(67465);
			toolStripMenuItem.Text = Class521.smethod_0(27236);
			toolStripMenuItem.Click += this.method_38;
			return toolStripMenuItem;
		}

		// Token: 0x0600199A RID: 6554 RVA: 0x000B1C50 File Offset: 0x000AFE50
		private ToolStripMenuItem method_21()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(67482);
			toolStripMenuItem.Text = Class521.smethod_0(67499);
			toolStripMenuItem.Click += this.method_40;
			return toolStripMenuItem;
		}

		// Token: 0x0600199B RID: 6555 RVA: 0x000B1C98 File Offset: 0x000AFE98
		private ToolStripMenuItem method_22()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(67516);
			toolStripMenuItem.Text = Class521.smethod_0(67537);
			toolStripMenuItem.Click += this.method_42;
			return toolStripMenuItem;
		}

		// Token: 0x0600199C RID: 6556 RVA: 0x000B1CE0 File Offset: 0x000AFEE0
		private ToolStripMenuItem method_23()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(67554);
			toolStripMenuItem.Click += this.method_43;
			return toolStripMenuItem;
		}

		// Token: 0x0600199D RID: 6557 RVA: 0x000B1D18 File Offset: 0x000AFF18
		private ToolStripMenuItem method_24()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(67575);
			toolStripMenuItem.Click += this.method_44;
			return toolStripMenuItem;
		}

		// Token: 0x0600199E RID: 6558 RVA: 0x000B1D50 File Offset: 0x000AFF50
		private void method_25()
		{
			this.dataGridViewMkt_0.ContextMenuStrip = new ContextMenuStrip();
			this.dataGridViewMkt_0.ContextMenuStrip.Items.Add(this.method_23());
			this.dataGridViewMkt_0.ContextMenuStrip.Items.Add(this.method_24());
			Base.UI.smethod_73(this.dataGridViewMkt_0.ContextMenuStrip);
		}

		// Token: 0x0600199F RID: 6559 RVA: 0x0000AB5E File Offset: 0x00008D5E
		private void tabControl_0_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			if (!this.IsInitiating)
			{
				this.method_26(true, e.NewTab);
			}
		}

		// Token: 0x060019A0 RID: 6560 RVA: 0x000B1DB8 File Offset: 0x000AFFB8
		private void method_26(bool bool_2 = false, TabItem tabItem_15 = null)
		{
			DataGridViewMkt dataGridViewMkt = this.method_27(tabItem_15);
			if (dataGridViewMkt != null)
			{
				bool flag = true;
				if (bool_2)
				{
					bool flag2 = false;
					SortableBindingList<ShowMktSymb> sortableBindingList = dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>;
					if (sortableBindingList != null && sortableBindingList.Count > 0)
					{
						int num = 15;
						if (15 > sortableBindingList.Count)
						{
							num = sortableBindingList.Count;
						}
						int num2 = sortableBindingList.Take(num).Where(new Func<ShowMktSymb, bool>(TransTabs.<>c.<>9.method_0)).Count<ShowMktSymb>();
						if ((double)num * 0.8 < (double)num2)
						{
							flag2 = true;
						}
					}
					if (flag2 && dataGridViewMkt.Tag != null)
					{
						DateTime? dateTime = Base.Data.smethod_54();
						if (dateTime != null && dateTime.Value.AddHours(4.0).Date == ((DateTime)dataGridViewMkt.Tag).AddHours(4.0).Date)
						{
							flag = false;
						}
					}
				}
				if (flag)
				{
					this.fnDataApiWorker_0.method_1(dataGridViewMkt, this.list_1);
				}
			}
		}

		// Token: 0x060019A1 RID: 6561 RVA: 0x000B1ED8 File Offset: 0x000B00D8
		private DataGridViewMkt method_27(TabItem tabItem_15 = null)
		{
			if (tabItem_15 == null)
			{
				tabItem_15 = this.tabControl_0.SelectedTab;
			}
			Control attachedControl = tabItem_15.AttachedControl;
			DataGridViewMkt result;
			if (attachedControl != null && attachedControl.Controls.Count > 0)
			{
				result = (attachedControl.Controls[0] as DataGridViewMkt);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019A2 RID: 6562 RVA: 0x000B1F28 File Offset: 0x000B0128
		private void method_28()
		{
			this.method_46();
			this.list_0 = new List<DataGridViewMkt>();
			bool flag = true;
			List<ShowMktSymb> list = new List<ShowMktSymb>();
			List<ShowMktSymb> list2 = new List<ShowMktSymb>();
			List<ShowMktSymb> list3 = new List<ShowMktSymb>();
			List<ShowMktSymb> list4 = new List<ShowMktSymb>();
			List<ShowMktSymb> list5 = new List<ShowMktSymb>();
			List<ShowMktSymb> list6 = new List<ShowMktSymb>();
			List<ShowMktSymb> list7 = new List<ShowMktSymb>();
			List<ShowMktSymb> list8 = new List<ShowMktSymb>();
			List<ShowMktSymb> list9 = new List<ShowMktSymb>();
			List<ShowMktSymb> list10 = new List<ShowMktSymb>();
			List<ShowMktSymb> list11 = new List<ShowMktSymb>();
			List<ShowMktSymb> list12 = new List<ShowMktSymb>();
			List<ShowMktSymb> list13 = new List<ShowMktSymb>();
			List<ShowMktSymb> list14 = new List<ShowMktSymb>();
			List<ShowMktSymb> list15 = new List<ShowMktSymb>();
			foreach (ShowMktSymb showMktSymb in this.list_1)
			{
				if (showMktSymb.ExchgId == -1)
				{
					list6.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 0)
				{
					list2.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 1)
				{
					list.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 2)
				{
					list3.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 3)
				{
					list5.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 4)
				{
					list4.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(67596)))
				{
					list7.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith(Class521.smethod_0(67601)) || (showMktSymb.StkCode.StartsWith(Class521.smethod_0(67606)) && !showMktSymb.StkCode.StartsWith(Class521.smethod_0(67611))) || showMktSymb.StkCode.StartsWith(Class521.smethod_0(67620))))
				{
					list8.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(67601))) || (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(67625))))
				{
					list9.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(67630)))
				{
					list10.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith(Class521.smethod_0(67635)) || showMktSymb.StkCode.StartsWith(Class521.smethod_0(67640))))
				{
					list11.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(67645)))
				{
					list12.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(12122))) || (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith(Class521.smethod_0(67650)) || showMktSymb.StkCode.StartsWith(Class521.smethod_0(67655)))))
				{
					list13.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(50245))) || (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith(Class521.smethod_0(50250))))
				{
					list14.Add(showMktSymb);
				}
				if (showMktSymb.IsInZiXuan)
				{
					list15.Add(showMktSymb);
				}
			}
			if (flag)
			{
				if (list7.Count > 0)
				{
					this.method_31(Class521.smethod_0(67660), Class521.smethod_0(67677), Enum1.const_0, list7, null);
				}
				if (list8.Count > 0)
				{
					this.method_31(Class521.smethod_0(67730), Class521.smethod_0(67747), Enum1.const_0, list8, null);
				}
				if (list10.Count > 0)
				{
					this.method_31(Class521.smethod_0(23135), Class521.smethod_0(67800), Enum1.const_0, list10, null);
				}
				if (list11.Count > 0)
				{
					this.method_31(Class521.smethod_0(23177), Class521.smethod_0(67829), Enum1.const_0, list11, null);
				}
				if (list12.Count > 0)
				{
					this.method_31(Class521.smethod_0(23093), Class521.smethod_0(67858), Enum1.const_0, list12, null);
				}
				if (list9.Count > 0)
				{
					this.method_31(Class521.smethod_0(23271), Class521.smethod_0(67887), Enum1.const_1, list9, null);
				}
				if (list13.Count > 0)
				{
					this.method_31(Class521.smethod_0(23245), Class521.smethod_0(67920), Enum1.const_1, list13, null);
				}
			}
			if (list14.Count > 0)
			{
				this.method_31(Class521.smethod_0(23063), Class521.smethod_0(67937), Enum1.const_4, list14, null);
			}
			if (list.Count > 0)
			{
				list = list.OrderBy(new Func<ShowMktSymb, string>(TransTabs.<>c.<>9.method_1)).ToList<ShowMktSymb>();
				this.method_31(Class521.smethod_0(67958), Class521.smethod_0(67971), Enum1.const_2, list, null);
			}
			if (list2.Count > 0)
			{
				this.method_31(Class521.smethod_0(68016), Class521.smethod_0(68033), Enum1.const_2, list2, null);
			}
			if (list3.Count > 0)
			{
				this.method_31(Class521.smethod_0(68082), Class521.smethod_0(68095), Enum1.const_2, list3, null);
			}
			if (list4.Count > 0)
			{
				this.method_31(Class521.smethod_0(68132), Class521.smethod_0(68145), Enum1.const_2, list4, null);
			}
			if (list5.Count > 0)
			{
				this.method_31(Class521.smethod_0(68182), Class521.smethod_0(68195), Enum1.const_2, list5, null);
			}
			if (list6.Count > 0)
			{
				this.method_31(Class521.smethod_0(68232), Class521.smethod_0(68245), Enum1.const_2, list6, null);
			}
			List<ShowMktSymb> list16 = this.method_30(list15);
			this.dataGridViewMkt_0 = this.method_31(Class521.smethod_0(22944), Class521.smethod_0(22944), this.method_29(list16), list16, null);
			this.dataGridViewMkt_0.KeyUp += this.dataGridViewMkt_0_KeyUp;
			this.dataGridViewMkt_0.MultiSelect = true;
			this.dataGridViewMkt_0.AllowDrop = true;
			this.dataGridViewMkt_0.Refresh();
		}

		// Token: 0x060019A3 RID: 6563 RVA: 0x000B25D0 File Offset: 0x000B07D0
		private void dataGridViewMkt_0_KeyUp(object sender, KeyEventArgs e)
		{
			DataGridViewMkt dataGridViewMkt = sender as DataGridViewMkt;
			if (e.KeyCode == Keys.Delete && dataGridViewMkt.SelectedRows.Count > 0 && MessageBox.Show(Class521.smethod_0(68274), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				foreach (object obj in dataGridViewMkt.SelectedRows)
				{
					DataGridViewRow dataGridViewRow_ = (DataGridViewRow)obj;
					this.method_41(dataGridViewRow_);
				}
			}
		}

		// Token: 0x060019A4 RID: 6564 RVA: 0x000B266C File Offset: 0x000B086C
		private Enum1 method_29(IList<ShowMktSymb> ilist_0)
		{
			Enum1 result = Enum1.const_0;
			if (TApp.IsStIncluded)
			{
				if (TApp.IsFtIncluded)
				{
					result = Enum1.const_1;
				}
			}
			else
			{
				result = Enum1.const_2;
			}
			return result;
		}

		// Token: 0x060019A5 RID: 6565 RVA: 0x000B2694 File Offset: 0x000B0894
		private List<ShowMktSymb> method_30(List<ShowMktSymb> list_4 = null)
		{
			if (list_4 == null)
			{
				list_4 = this.list_1.Where(new Func<ShowMktSymb, bool>(TransTabs.<>c.<>9.method_2)).ToList<ShowMktSymb>();
			}
			if (list_4.Count > 0)
			{
				try
				{
					list_4 = list_4.OrderBy(new Func<ShowMktSymb, int?>(TransTabs.<>c.<>9.method_3)).ToList<ShowMktSymb>();
				}
				catch
				{
				}
				this.method_33(list_4);
			}
			return list_4;
		}

		// Token: 0x060019A6 RID: 6566 RVA: 0x000B272C File Offset: 0x000B092C
		private DataGridViewMkt method_31(string string_1, string string_2, Enum1 enum1_0, List<ShowMktSymb> list_4, string[] string_3 = null)
		{
			TabItem tabItem = new TabItem(this.icontainer_0);
			this.tabControl_0.Tabs.Add(tabItem);
			tabItem.Text = string_1;
			tabItem.Tooltip = string_2;
			tabItem.Name = string_1;
			TabControlPanel tabControlPanel = new TabControlPanel();
			this.tabControl_0.Controls.Add(tabControlPanel);
			tabControlPanel.Dock = DockStyle.Fill;
			tabControlPanel.Padding = new System.Windows.Forms.Padding(0, 0, 0, 0);
			tabControlPanel.TabItem = tabItem;
			tabItem.AttachedControl = tabControlPanel;
			DataGridViewMkt dataGridViewMkt = new DataGridViewMkt(1350);
			tabControlPanel.Controls.Add(dataGridViewMkt);
			dataGridViewMkt.CellDoubleClick += this.method_49;
			this.list_0.Add(dataGridViewMkt);
			dataGridViewMkt.RowContextMenuStripNeeded += this.method_45;
			dataGridViewMkt.CellToolTipTextNeeded += this.method_34;
			dataGridViewMkt.ShowCellToolTips = true;
			dataGridViewMkt.Name = string_1;
			dataGridViewMkt.MktDgvType = enum1_0;
			dataGridViewMkt.SourceMktSymbLst = list_4;
			dataGridViewMkt.IdxClassAry = string_3;
			if (list_4 != null)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList_ = new SortableBindingList<ShowMktSymb>(list_4);
				dataGridViewMkt.method_8(sortableBindingList_);
			}
			if (this.class326_0 != null)
			{
				dataGridViewMkt.Tag = this.class326_0.CurrDate;
			}
			return dataGridViewMkt;
		}

		// Token: 0x060019A7 RID: 6567 RVA: 0x000B2860 File Offset: 0x000B0A60
		private void dataGridViewMkt_0_MouseMove(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.Button == MouseButtons.Left && this.rectangle_0 != Rectangle.Empty && !this.rectangle_0.Contains(e.X, e.Y))
			{
				dataGridView.DoDragDrop(dataGridView.Rows[this.int_1], DragDropEffects.Move);
			}
		}

		// Token: 0x060019A8 RID: 6568 RVA: 0x000B28C8 File Offset: 0x000B0AC8
		private void dataGridViewMkt_0_MouseDown(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			this.int_1 = dataGridView.HitTest(e.X, e.Y).RowIndex;
			if (this.int_1 != -1)
			{
				Size dragSize = SystemInformation.DragSize;
				this.rectangle_0 = new Rectangle(new Point(e.X - dragSize.Width / 2, e.Y - dragSize.Height / 2), dragSize);
			}
			else
			{
				this.rectangle_0 = Rectangle.Empty;
				if (e.Button == MouseButtons.Right)
				{
					this.method_25();
				}
			}
		}

		// Token: 0x060019A9 RID: 6569 RVA: 0x0000AB77 File Offset: 0x00008D77
		private void dataGridViewMkt_0_DragOver(object sender, DragEventArgs e)
		{
			e.Effect = DragDropEffects.Move;
		}

		// Token: 0x060019AA RID: 6570 RVA: 0x000B295C File Offset: 0x000B0B5C
		private void dataGridViewMkt_0_DragDrop(object sender, DragEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			Point point = dataGridView.PointToClient(new Point(e.X, e.Y));
			this.int_2 = dataGridView.HitTest(point.X, point.Y).RowIndex;
			if (e.Effect == DragDropEffects.Move && this.int_2 >= 0)
			{
				ShowMktSymb item = ((DataGridViewRow)e.Data.GetData(typeof(DataGridViewRow))).DataBoundItem as ShowMktSymb;
				SortableBindingList<ShowMktSymb> sortableBindingList = dataGridView.DataSource as SortableBindingList<ShowMktSymb>;
				int num = this.int_2;
				if (num < 0)
				{
					num = 0;
				}
				sortableBindingList.Remove(item);
				sortableBindingList.Insert(num, item);
				dataGridView.Rows[num].Selected = true;
				this.method_33(sortableBindingList);
			}
		}

		// Token: 0x060019AB RID: 6571 RVA: 0x0000AB82 File Offset: 0x00008D82
		private void method_32()
		{
			if (this.dataGridViewMkt_0.DataSource != null)
			{
				this.method_33(this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>);
			}
		}

		// Token: 0x060019AC RID: 6572 RVA: 0x000B2A28 File Offset: 0x000B0C28
		private void method_33(IList<ShowMktSymb> ilist_0)
		{
			if (ilist_0 != null)
			{
				for (int i = 0; i < ilist_0.Count; i++)
				{
					ilist_0[i].IdxInZixuanDGV = new int?(i);
				}
			}
		}

		// Token: 0x060019AD RID: 6573 RVA: 0x000B2A60 File Offset: 0x000B0C60
		private void method_34(object sender, DataGridViewCellToolTipTextNeededEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.RowIndex >= 0 && !Base.UI.IsInCreateNewPageState)
			{
				DataGridViewRow dataGridViewRow = dataGridView.Rows[e.RowIndex];
				try
				{
					if ((dataGridViewRow.DataBoundItem as ShowMktSymb).StkId != Base.Data.CurrSelectedSymbol.ID)
					{
						e.ToolTipText = Class521.smethod_0(68319);
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x060019AE RID: 6574 RVA: 0x000B2ADC File Offset: 0x000B0CDC
		private void method_35()
		{
			try
			{
				if (Base.UI.Form.LastFuncTabsIdx != null)
				{
					this.SelectedTabIndex = Base.UI.Form.LastFuncTabsIdx.Value;
				}
				if (Base.UI.Form.LastMktSymbTabsIdx != null)
				{
					this.MktSelectedTabIndex = Base.UI.Form.LastMktSymbTabsIdx.Value;
				}
				if (this.SelectedTab.Text == Class521.smethod_0(68360) || this.SelectedTab.Text == Class521.smethod_0(68377))
				{
					this.trdAnalysisPanel_0.method_6();
				}
			}
			catch
			{
			}
		}

		// Token: 0x060019AF RID: 6575 RVA: 0x0000ABA9 File Offset: 0x00008DA9
		private void method_36(DataGridView dataGridView_0)
		{
			dataGridView_0.ContextMenuStrip = new ContextMenuStrip();
			this.method_87(dataGridView_0.ContextMenuStrip);
		}

		// Token: 0x060019B0 RID: 6576 RVA: 0x000B2B9C File Offset: 0x000B0D9C
		public void method_37(int int_3)
		{
			ShowMktSymb showMktSymb = this.method_54(int_3);
			if (showMktSymb != null)
			{
				this.method_57(showMktSymb);
				this.method_39(showMktSymb);
			}
		}

		// Token: 0x060019B1 RID: 6577 RVA: 0x000B2BC8 File Offset: 0x000B0DC8
		private void method_38(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null && toolStripMenuItem.Tag != null)
			{
				ShowMktSymb showMktSymb_ = (toolStripMenuItem.Tag as DataGridViewRow).DataBoundItem as ShowMktSymb;
				this.method_39(showMktSymb_);
			}
		}

		// Token: 0x060019B2 RID: 6578 RVA: 0x000B2C08 File Offset: 0x000B0E08
		private void method_39(ShowMktSymb showMktSymb_0)
		{
			if (showMktSymb_0 != null && !showMktSymb_0.IsInZiXuan)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
				if (sortableBindingList == null)
				{
					sortableBindingList = new SortableBindingList<ShowMktSymb>();
				}
				showMktSymb_0.IsInZiXuan = true;
				sortableBindingList.Add(showMktSymb_0);
				if (sortableBindingList.Count == 1)
				{
					this.dataGridViewMkt_0.MktDgvType = this.method_29(sortableBindingList);
					this.dataGridViewMkt_0.Refresh();
				}
				this.method_32();
				this.method_47();
			}
		}

		// Token: 0x060019B3 RID: 6579 RVA: 0x000B2C7C File Offset: 0x000B0E7C
		private void method_40(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null && toolStripMenuItem.Tag != null)
			{
				DataGridViewRow dataGridViewRow = toolStripMenuItem.Tag as DataGridViewRow;
				if (dataGridViewRow != null)
				{
					this.method_41(dataGridViewRow);
				}
			}
		}

		// Token: 0x060019B4 RID: 6580 RVA: 0x000B2CB4 File Offset: 0x000B0EB4
		private void method_41(DataGridViewRow dataGridViewRow_0)
		{
			ShowMktSymb showMktSymb = dataGridViewRow_0.DataBoundItem as ShowMktSymb;
			if (showMktSymb != null && showMktSymb.IsInZiXuan)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
				showMktSymb.IsInZiXuan = false;
				sortableBindingList.Remove(showMktSymb);
				this.method_33(sortableBindingList);
			}
		}

		// Token: 0x060019B5 RID: 6581 RVA: 0x000B2D04 File Offset: 0x000B0F04
		private void method_42(object sender, EventArgs e)
		{
			ShowMktSymb showMktSymb_ = ((sender as ToolStripMenuItem).Tag as DataGridViewRow).DataBoundItem as ShowMktSymb;
			this.method_50(showMktSymb_, true);
		}

		// Token: 0x060019B6 RID: 6582 RVA: 0x000B2D38 File Offset: 0x000B0F38
		private void method_43(object sender, EventArgs e)
		{
			SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
			if (sortableBindingList != null && sortableBindingList.Count > 0)
			{
				string text = Utility.SaveFile(Class521.smethod_0(68394), Class521.smethod_0(68419), Environment.GetFolderPath(Environment.SpecialFolder.Personal), Class521.smethod_0(68532));
				if (!string.IsNullOrEmpty(text))
				{
					StringBuilder stringBuilder = new StringBuilder();
					bool flag = text.EndsWith(Class521.smethod_0(68557));
					try
					{
						foreach (ShowMktSymb showMktSymb in sortableBindingList)
						{
							if (stringBuilder.Length > 0)
							{
								stringBuilder.Append(Environment.NewLine);
							}
							string value;
							if (flag)
							{
								value = showMktSymb.StkCode;
							}
							else
							{
								value = showMktSymb.method_0();
							}
							stringBuilder.Append(value);
						}
						string content = stringBuilder.ToString();
						Utility.SaveFile(text, content, null);
						MessageBox.Show(Class521.smethod_0(68562), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060019B7 RID: 6583 RVA: 0x000B2E6C File Offset: 0x000B106C
		private void method_44(object sender, EventArgs e)
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Title = Class521.smethod_0(68583);
			openFileDialog.Filter = Class521.smethod_0(68419);
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = new SortableBindingList<ShowMktSymb>();
				try
				{
					Dictionary<string, StkSymbol> dictionary = null;
					foreach (string text in File.ReadAllLines(openFileDialog.FileName))
					{
						try
						{
							if (!string.IsNullOrEmpty(text))
							{
								if (text.Contains(Class521.smethod_0(4736)))
								{
									ShowMktSymb showMktSymb = new ShowMktSymb();
									showMktSymb.method_1(text);
									showMktSymb.IsInZiXuan = true;
									sortableBindingList.Add(showMktSymb);
								}
								else
								{
									if (dictionary == null)
									{
										dictionary = Base.Data.UsrStkSymbols.Values.ToDictionary(new Func<StkSymbol, string>(TransTabs.<>c.<>9.method_4), new Func<StkSymbol, StkSymbol>(TransTabs.<>c.<>9.method_5));
									}
									string value = null;
									if (text.IndexOf(Class521.smethod_0(3636)) > 0)
									{
										value = Class521.smethod_0(3636);
									}
									else if (text.IndexOf(Class521.smethod_0(68616)) > 0)
									{
										value = Class521.smethod_0(68616);
									}
									int startIndex = 0;
									int length = text.Length;
									if (!string.IsNullOrEmpty(value))
									{
										length = text.IndexOf(value);
									}
									string text2 = text.Substring(startIndex, length).Trim();
									string text3 = text2.Substring(0, 1);
									bool flag = false;
									bool flag2 = false;
									if (text2.Length > 6)
									{
										if (text2.Length == 7 && (text3 == Class521.smethod_0(2841) || text3 == Class521.smethod_0(4933)))
										{
											flag2 = true;
										}
									}
									else
									{
										if (text2.Length <= 2)
										{
											break;
										}
										if (!Utility.IsDigitChars(text3, false, false))
										{
											string text4 = text2.Substring(0, 2);
											bool flag3 = text4.Equals(Class521.smethod_0(49887), StringComparison.InvariantCultureIgnoreCase);
											bool flag4 = text4.Equals(Class521.smethod_0(49882), StringComparison.InvariantCultureIgnoreCase);
											if (flag3 || flag4)
											{
												text2 = (flag3 ? Class521.smethod_0(4933) : (Class521.smethod_0(2841) + text2.Substring(2)));
												flag2 = true;
											}
											else
											{
												flag = true;
											}
										}
									}
									if (!flag && !flag2)
									{
										text2 = Class521.smethod_0(2841) + text2;
									}
									StkSymbol stkSymbol = null;
									text2 = text2.ToUpper();
									dictionary.TryGetValue(text2, out stkSymbol);
									if (stkSymbol == null)
									{
										if (flag)
										{
											if (text2.Length > 4 && Utility.IsDigitChars(text2.Substring(text2.Length - 3), false, false))
											{
												text2 = text2.Substring(0, text2.Length - 4) + text2.Substring(text2.Length - 2);
												dictionary.TryGetValue(text2, out stkSymbol);
											}
										}
										else if (!flag2)
										{
											text2 = Class521.smethod_0(4933) + text2.Substring(1);
											dictionary.TryGetValue(text2, out stkSymbol);
										}
									}
									if (stkSymbol != null)
									{
										sortableBindingList.Add(new ShowMktSymb(stkSymbol)
										{
											IsInZiXuan = true
										});
									}
								}
							}
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
						}
					}
					if (sortableBindingList.Count > 0)
					{
						SortableBindingList<ShowMktSymb> sortableBindingList2 = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
						if (sortableBindingList2 != null && sortableBindingList2.Count != 0)
						{
							try
							{
								Dictionary<int, ShowMktSymb> dictionary2 = sortableBindingList2.ToDictionary(new Func<ShowMktSymb, int>(TransTabs.<>c.<>9.method_6), new Func<ShowMktSymb, ShowMktSymb>(TransTabs.<>c.<>9.method_7));
								foreach (ShowMktSymb showMktSymb2 in sortableBindingList)
								{
									if (!dictionary2.ContainsKey(showMktSymb2.StkId))
									{
										sortableBindingList2.Add(showMktSymb2);
									}
								}
								goto IL_3FD;
							}
							catch (Exception exception_2)
							{
								Class184.smethod_0(exception_2);
								goto IL_3FD;
							}
						}
						this.dataGridViewMkt_0.method_8(sortableBindingList);
						IL_3FD:
						this.method_47();
						MessageBox.Show(string.Format(Class521.smethod_0(68621), sortableBindingList.Count), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					else
					{
						MessageBox.Show(Class521.smethod_0(68674), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
				}
				catch
				{
					MessageBox.Show(Class521.smethod_0(68719), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019B8 RID: 6584 RVA: 0x000B3350 File Offset: 0x000B1550
		private void method_45(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewMkt dataGridViewMkt = sender as DataGridView;
			DataGridViewRow dataGridViewRow = (sender as DataGridView).Rows[e.RowIndex];
			ShowMktSymb showMktSymb = dataGridViewRow.DataBoundItem as ShowMktSymb;
			dataGridViewRow.Selected = true;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			StkSymbol stkSymbol = SymbMgr.smethod_3(showMktSymb.StkId);
			if (!Base.UI.IsInCreateNewPageState && (Base.Data.CurrSelectedSymbol == null || stkSymbol.ID != Base.Data.CurrSelectedSymbol.ID))
			{
				contextMenuStrip.Items.Add(this.method_22());
			}
			if (dataGridViewMkt == this.dataGridViewMkt_0)
			{
				ToolStripMenuItem toolStripMenuItem = this.method_21();
				toolStripMenuItem.Tag = dataGridViewRow;
				contextMenuStrip.Items.Add(toolStripMenuItem);
				contextMenuStrip.Items.Add(this.method_23());
				contextMenuStrip.Items.Add(this.method_24());
			}
			else
			{
				ToolStripMenuItem toolStripMenuItem2 = this.method_20();
				toolStripMenuItem2.Tag = dataGridViewRow;
				contextMenuStrip.Items.Add(toolStripMenuItem2);
			}
			if (this.ParentSplitPanel != null)
			{
				contextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(contextMenuStrip);
			}
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			e.ContextMenuStrip.Items[0].Tag = dataGridViewRow;
		}

		// Token: 0x060019B9 RID: 6585 RVA: 0x000B347C File Offset: 0x000B167C
		private void method_46()
		{
			this.class326_0 = TransTabs.smethod_0();
			Dictionary<int, ShowMktSymb> dictionary = null;
			if (this.class326_0 != null)
			{
				dictionary = this.class326_0.ShowMktSymbDict;
			}
			this.list_1 = new List<ShowMktSymb>();
			foreach (KeyValuePair<int, StkSymbol> keyValuePair in Base.Data.UsrStkSymbols)
			{
				ShowMktSymb showMktSymb = null;
				if (dictionary != null)
				{
					dictionary.TryGetValue(keyValuePair.Key, out showMktSymb);
				}
				if (showMktSymb == null)
				{
					showMktSymb = new ShowMktSymb(keyValuePair.Value);
				}
				this.list_1.Add(showMktSymb);
			}
		}

		// Token: 0x060019BA RID: 6586 RVA: 0x0000ABC4 File Offset: 0x00008DC4
		private void dataGridViewMkt_0_Paint(object sender, PaintEventArgs e)
		{
			if (this.dataGridViewMkt_0.Columns[0].Visible)
			{
				this.dataGridViewMkt_0.Columns[0].Visible = false;
				this.dataGridViewMkt_0.Refresh();
			}
		}

		// Token: 0x060019BB RID: 6587 RVA: 0x000B3528 File Offset: 0x000B1728
		private static Class326 smethod_0()
		{
			string filePath = TransTabs.string_0;
			if (Utility.FileExists(filePath))
			{
				Class326 result;
				try
				{
					string value = null;
					try
					{
						value = Utility.GetStringFromCompressedFile(filePath, CompressAlgm.LZMA, null);
					}
					catch
					{
					}
					if (!string.IsNullOrEmpty(value))
					{
						Class326 @class = JsonConvert.DeserializeObject<Class326>(value);
						DateTime? dateTime = Base.Data.smethod_54();
						if (@class != null && dateTime != null && @class.UserName == TApp.UserName && @class.CurrDate == dateTime.Value.Date)
						{
							TExPackage? texPkg = @class.TExPkg;
							TExPackage? texPkg2 = TApp.SrvParams.TExPkg;
							if (texPkg.GetValueOrDefault() == texPkg2.GetValueOrDefault() & texPkg != null == (texPkg2 != null))
							{
								if (!string.IsNullOrEmpty(@class.ShowMktSymbCsv))
								{
									string[] array = @class.ShowMktSymbCsv.Split(new string[]
									{
										Environment.NewLine
									}, StringSplitOptions.None);
									@class.ShowMktSymbDict = new Dictionary<int, ShowMktSymb>();
									foreach (string text in array)
									{
										ShowMktSymb showMktSymb = new ShowMktSymb();
										showMktSymb.method_1(text);
										@class.ShowMktSymbDict[showMktSymb.StkId] = showMktSymb;
									}
								}
								result = @class;
								goto IL_137;
							}
						}
					}
					goto IL_13D;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					goto IL_13D;
				}
				IL_137:
				return result;
			}
			IL_13D:
			return null;
		}

		// Token: 0x060019BC RID: 6588 RVA: 0x000B36B0 File Offset: 0x000B18B0
		public void method_47()
		{
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null && this.list_0 != null)
			{
				Class326 @class = new Class326();
				@class.UserName = TApp.UserName;
				@class.TExPkg = TApp.SrvParams.TExPkg;
				@class.CurrDate = dateTime.Value.Date;
				Dictionary<int, ShowMktSymb> dictionary = new Dictionary<int, ShowMktSymb>();
				int num = 0;
				foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
				{
					if (dataGridViewMkt.DataSource != null)
					{
						bool flag = dataGridViewMkt.Tag != null && ((DateTime)dataGridViewMkt.Tag).AddHours(4.0).Date == dateTime.Value.AddHours(4.0).Date;
						foreach (ShowMktSymb showMktSymb in (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>))
						{
							if (showMktSymb.IsInZiXuan || showMktSymb.close != null || (flag && (!string.IsNullOrEmpty(showMktSymb.cb_stk_code) || showMktSymb.total_share != null)))
							{
								dictionary[showMktSymb.StkId] = showMktSymb;
								num++;
							}
						}
					}
				}
				try
				{
					StringBuilder stringBuilder = new StringBuilder();
					foreach (KeyValuePair<int, ShowMktSymb> keyValuePair in dictionary)
					{
						if (stringBuilder.Length > 0)
						{
							stringBuilder.Append(Environment.NewLine);
						}
						stringBuilder.Append(keyValuePair.Value.method_0());
					}
					@class.ShowMktSymbCsv = stringBuilder.ToString();
					string str = JsonConvert.SerializeObject(@class);
					string filePath = TransTabs.string_0;
					Utility.GenCompressedFile(str, filePath, CompressAlgm.LZMA, null);
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060019BD RID: 6589 RVA: 0x000B3934 File Offset: 0x000B1B34
		public void method_48()
		{
			List<StkSymbol> list = Base.Data.smethod_115();
			foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
			{
				BindingList<ShowMktSymb> bindingList = dataGridViewMkt.DataSource as BindingList<ShowMktSymb>;
				if (bindingList != null)
				{
					using (IEnumerator<ShowMktSymb> enumerator2 = bindingList.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							TransTabs.Class322 @class = new TransTabs.Class322();
							@class.showMktSymb_0 = enumerator2.Current;
							if (list.Exists(new Predicate<StkSymbol>(@class.method_0)))
							{
								@class.showMktSymb_0.IsDownloaded = true;
							}
						}
					}
				}
			}
		}

		// Token: 0x060019BE RID: 6590 RVA: 0x000B39F8 File Offset: 0x000B1BF8
		private void method_49(object sender, DataGridViewCellEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.RowIndex < dataGridView.Rows.Count && e.RowIndex >= 0)
			{
				ShowMktSymb showMktSymb_ = dataGridView.Rows[e.RowIndex].DataBoundItem as ShowMktSymb;
				this.method_50(showMktSymb_, true);
			}
		}

		// Token: 0x060019BF RID: 6591 RVA: 0x000B3A50 File Offset: 0x000B1C50
		private void method_50(ShowMktSymb showMktSymb_0, bool bool_2 = true)
		{
			StkSymbol stkSymbol = SymbMgr.smethod_3(showMktSymb_0.StkId);
			if (Base.Data.CurrSelectedSymbol == null || stkSymbol.ID != Base.Data.CurrSelectedSymbol.ID)
			{
				Base.UI.smethod_176(Base.Data.string_1);
				Base.Data.smethod_69(stkSymbol, false, false, false, null);
				Base.UI.smethod_178();
			}
			if (bool_2 && Base.UI.Chart.IsSingleFixedContent && Base.UI.VisibleChtCtrlList == null)
			{
				this.method_88();
			}
		}

		// Token: 0x060019C0 RID: 6592 RVA: 0x000B3AC8 File Offset: 0x000B1CC8
		private void method_51()
		{
			foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
			{
				this.method_52(symbDataSet.SymblID);
			}
		}

		// Token: 0x060019C1 RID: 6593 RVA: 0x000B3B24 File Offset: 0x000B1D24
		private void method_52(int int_3)
		{
			this.method_56(int_3);
			foreach (KeyValuePair<DataGridView, int> keyValuePair in this.DataGridViewListWithCurrMktSymb)
			{
				keyValuePair.Key.Refresh();
			}
		}

		// Token: 0x060019C2 RID: 6594 RVA: 0x000B3B88 File Offset: 0x000B1D88
		private List<ShowMktSymb> method_53()
		{
			List<ShowMktSymb> list = null;
			if (Base.Data.SymbDataSets != null)
			{
				list = new List<ShowMktSymb>();
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					ShowMktSymb showMktSymb = this.method_54(symbDataSet.SymblID);
					if (showMktSymb != null)
					{
						list.Add(showMktSymb);
					}
				}
			}
			return list;
		}

		// Token: 0x060019C3 RID: 6595 RVA: 0x000B3C04 File Offset: 0x000B1E04
		private ShowMktSymb method_54(int int_3)
		{
			ShowMktSymb result = null;
			this.list_3 = new List<KeyValuePair<DataGridView, int>>();
			foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
			{
				ShowMktSymb showMktSymb = this.method_55(int_3, dataGridViewMkt);
				if (showMktSymb != null)
				{
					int num = (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>).IndexOf(showMktSymb);
					if (num >= 0 && num < dataGridViewMkt.Rows.Count)
					{
						KeyValuePair<DataGridView, int> item = new KeyValuePair<DataGridView, int>(dataGridViewMkt, num);
						this.list_3.Add(item);
					}
					result = showMktSymb;
				}
			}
			return result;
		}

		// Token: 0x060019C4 RID: 6596 RVA: 0x000B3CB4 File Offset: 0x000B1EB4
		private ShowMktSymb method_55(int int_3, DataGridView dataGridView_0)
		{
			TransTabs.Class323 @class = new TransTabs.Class323();
			@class.int_0 = int_3;
			ShowMktSymb result = null;
			SortableBindingList<ShowMktSymb> sortableBindingList = dataGridView_0.DataSource as SortableBindingList<ShowMktSymb>;
			if (sortableBindingList != null)
			{
				result = sortableBindingList.FirstOrDefault(new Func<ShowMktSymb, bool>(@class.method_0));
			}
			return result;
		}

		// Token: 0x060019C5 RID: 6597 RVA: 0x000B3CF8 File Offset: 0x000B1EF8
		private ShowMktSymb method_56(int int_3)
		{
			ShowMktSymb showMktSymb = this.method_54(int_3);
			ShowMktSymb result;
			if (showMktSymb != null)
			{
				result = this.method_57(showMktSymb);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C6 RID: 6598 RVA: 0x000B3D20 File Offset: 0x000B1F20
		private ShowMktSymb method_57(ShowMktSymb showMktSymb_0)
		{
			SymbDataSet symbDataSet = Base.Data.smethod_49(showMktSymb_0.StkId, false);
			ShowMktSymb result;
			if (symbDataSet != null && symbDataSet.HasValidDataSet)
			{
				showMktSymb_0.LastDT = new DateTime?(symbDataSet.CurrHisDataSet.CurrHisData.Date);
				double? lastDayClose = symbDataSet.CurrHisDataSet.LastDayClose;
				if (lastDayClose != null)
				{
					showMktSymb_0.LastDayClose = new decimal?(Convert.ToDecimal(lastDayClose));
				}
				showMktSymb_0.Price = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close));
				bool flag = (Base.UI.Form.IsSpanMoveNext || Base.UI.Form.IsSpanMovePrev) && Base.UI.Form.LastSpanMoveDT != null;
				try
				{
					if (flag)
					{
						List<HisData> source = Base.Data.smethod_56(showMktSymb_0.StkId, null);
						showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(source.Sum(new Func<HisData, double?>(TransTabs.<>c.<>9.method_8))));
						showMktSymb_0.Open = new decimal?(Convert.ToDecimal(source.First<HisData>().Open));
						showMktSymb_0.High = new decimal?(Convert.ToDecimal(source.Max(new Func<HisData, double>(TransTabs.<>c.<>9.method_9))));
						showMktSymb_0.Low = new decimal?(Convert.ToDecimal(source.Min(new Func<HisData, double>(TransTabs.<>c.<>9.method_10))));
					}
					else
					{
						List<HisData> list = symbDataSet.method_106(symbDataSet.CurrHisDataSet.FetchedHisDataList, symbDataSet.CurrHisDataSet.CurrDayBeginDT, symbDataSet.CurrHisDataSet.CurrHisData.Date, false, true, new int?(1));
						if (list != null && list.Any<HisData>())
						{
							showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(list.Sum(new Func<HisData, double?>(TransTabs.<>c.<>9.method_11))));
							showMktSymb_0.Open = new decimal?(Convert.ToDecimal(list.First<HisData>().Open));
							showMktSymb_0.High = new decimal?(Convert.ToDecimal(list.Max(new Func<HisData, double>(TransTabs.<>c.<>9.method_12))));
							showMktSymb_0.Low = new decimal?(Convert.ToDecimal(list.Min(new Func<HisData, double>(TransTabs.<>c.<>9.method_13))));
						}
						else
						{
							showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Volume));
							showMktSymb_0.Open = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Open));
							showMktSymb_0.High = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.High));
							showMktSymb_0.Low = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Low));
						}
					}
					if (symbDataSet.CurrHisDataSet.CurrHisData != null && symbDataSet.CurrHisDataSet.CurrHisData.Amount != null && !symbDataSet.CurrSymbol.IsStock)
					{
						showMktSymb_0.Amount = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Amount));
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				showMktSymb_0 = this.method_58(showMktSymb_0);
				showMktSymb_0.IsDownloaded = true;
				result = showMktSymb_0;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C7 RID: 6599 RVA: 0x000B40EC File Offset: 0x000B22EC
		private ShowMktSymb method_58(ShowMktSymb showMktSymb_0)
		{
			StkSymbol stkSymbol = Base.Data.smethod_49(showMktSymb_0.StkId, false).CurrSymbol;
			if (stkSymbol.ID != showMktSymb_0.StkId)
			{
				stkSymbol = SymbMgr.smethod_3(showMktSymb_0.StkId);
			}
			int digitNb = stkSymbol.DigitNb;
			showMktSymb_0.Price = new decimal?(Math.Round(showMktSymb_0.Price.Value, digitNb));
			if (showMktSymb_0.Vol != null)
			{
				showMktSymb_0.Vol = new decimal?(Math.Round(showMktSymb_0.Vol.Value) / 1.0000000000000000000000m);
			}
			if (showMktSymb_0.Open != null)
			{
				showMktSymb_0.Open = new decimal?(Math.Round(showMktSymb_0.Open.Value, digitNb));
			}
			if (showMktSymb_0.High != null)
			{
				showMktSymb_0.High = new decimal?(Math.Round(showMktSymb_0.High.Value, digitNb));
			}
			if (showMktSymb_0.Low != null)
			{
				showMktSymb_0.Low = new decimal?(Math.Round(showMktSymb_0.Low.Value, digitNb));
			}
			if (showMktSymb_0.LastDayClose != null)
			{
				showMktSymb_0.LastDayClose = new decimal?(Math.Round(showMktSymb_0.LastDayClose.Value, digitNb));
			}
			return showMktSymb_0;
		}

		// Token: 0x060019C8 RID: 6600 RVA: 0x000B4258 File Offset: 0x000B2458
		private object method_59(DataGridViewRow dataGridViewRow_0)
		{
			object result;
			if (dataGridViewRow_0 != null)
			{
				result = dataGridViewRow_0.DataBoundItem;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C9 RID: 6601 RVA: 0x000B4278 File Offset: 0x000B2478
		private DataGridViewRow method_60()
		{
			DataGridView dataGridView_ = this.method_62();
			return this.method_61(dataGridView_);
		}

		// Token: 0x060019CA RID: 6602 RVA: 0x000B4298 File Offset: 0x000B2498
		private DataGridViewRow method_61(DataGridView dataGridView_0)
		{
			DataGridViewRow result;
			if (dataGridView_0 == null)
			{
				result = null;
			}
			else if (dataGridView_0.Rows.GetRowCount(DataGridViewElementStates.Selected) > 0)
			{
				result = dataGridView_0.SelectedRows[0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019CB RID: 6603 RVA: 0x000B42D0 File Offset: 0x000B24D0
		private DataGridView method_62()
		{
			Control control = this.method_64(this.SelectedTab);
			if (control is DevComponents.DotNetBar.TabControl)
			{
				DevComponents.DotNetBar.TabControl tabControl = control as DevComponents.DotNetBar.TabControl;
				Control control2 = this.method_65(tabControl.SelectedPanel);
				if (control2 is DataGridView)
				{
					return control2 as DataGridView;
				}
			}
			return null;
		}

		// Token: 0x060019CC RID: 6604 RVA: 0x000B431C File Offset: 0x000B251C
		public bool method_63()
		{
			if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrSymbol != null)
			{
				StkSymbol currSymbol = Base.Data.CurrSymbDataSet.CurrSymbol;
				for (int i = 0; i < this.list_0.Count; i++)
				{
					DataGridViewMkt dataGridViewMkt = this.list_0[i];
					ShowMktSymb showMktSymb = this.method_55(currSymbol.ID, dataGridViewMkt);
					if (showMktSymb != null)
					{
						int num = (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>).IndexOf(showMktSymb);
						if (num >= 0 && num < dataGridViewMkt.Rows.Count)
						{
							DataGridViewRow dataGridViewRow = dataGridViewMkt.Rows[num];
							bool result;
							try
							{
								DataGridViewCell dataGridViewCell = dataGridViewRow.Cells[1];
								if (dataGridViewCell.Visible)
								{
									dataGridViewMkt.CurrentCell = dataGridViewCell;
								}
								dataGridViewRow.Selected = true;
								this.tabControl_0.SelectedTabIndex = i;
								result = true;
								goto IL_D6;
							}
							catch (Exception exception_)
							{
								Class184.smethod_0(exception_);
							}
							goto IL_BF;
							IL_D6:
							return result;
						}
					}
					IL_BF:;
				}
			}
			return false;
		}

		// Token: 0x060019CD RID: 6605 RVA: 0x000B441C File Offset: 0x000B261C
		private Control method_64(TabItem tabItem_15)
		{
			TabControlPanel tabControlPanel_ = tabItem_15.AttachedControl as TabControlPanel;
			return this.method_65(tabControlPanel_);
		}

		// Token: 0x060019CE RID: 6606 RVA: 0x000B4440 File Offset: 0x000B2640
		private Control method_65(TabControlPanel tabControlPanel_15)
		{
			return tabControlPanel_15.Controls[0];
		}

		// Token: 0x060019CF RID: 6607 RVA: 0x000B4460 File Offset: 0x000B2660
		private void method_66()
		{
			this.button_4.ForeColor = Class181.color_1;
			this.button_0.ForeColor = Class181.color_1;
			this.button_1.ForeColor = Class181.color_1;
			this.button_5.ForeColor = Class181.color_1;
			this.button_6.ForeColor = Class181.color_1;
			this.button_7.ForeColor = Class181.color_1;
			this.button_8.ForeColor = Class181.color_1;
			this.button_3.ForeColor = Color.Green;
			this.button_2.ForeColor = Color.Red;
			Base.UI.smethod_74(this.expandableSplitter_0);
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.tabControlPanel_9.BackColor = Class181.color_3;
				this.panel_1.BackColor = Class181.color_3;
				this.tabControlPanel_4.BackColor = Class181.color_3;
				this.panel_0.BackColor = Class181.color_3;
				this.splitContainer_2.Panel1.BackColor = Class181.color_3;
				this.splitContainer_2.Panel2.BackColor = Class181.color_3;
				this.splitContainer_0.Panel1.BackColor = Class181.color_3;
				this.splitContainer_1.Panel1.BackColor = Class181.color_3;
				this.label_5.ForeColor = Class181.color_9;
				this.label_6.ForeColor = Class181.color_9;
				this.label_7.ForeColor = Class181.color_9;
				this.label_4.ForeColor = Class181.color_9;
				this.checkBox_0.ForeColor = Class181.color_9;
				this.numericUpDown_1.ForeColor = Class181.color_10;
				this.numericUpDown_0.ForeColor = Class181.color_10;
				this.numericUpDown_1.BackColor = Class181.color_3;
				this.numericUpDown_0.BackColor = Class181.color_3;
				this.panel_2.BackColor = Class181.color_3;
				this.radioButton_4.ForeColor = Class181.color_9;
				this.radioButton_3.ForeColor = Class181.color_9;
				this.radioButton_0.ForeColor = Class181.color_9;
				this.radioButton_1.ForeColor = Class181.color_9;
				this.radioButton_2.ForeColor = Class181.color_9;
				this.radioButton_4.BackColor = Class181.color_3;
				this.radioButton_3.BackColor = Class181.color_3;
				this.radioButton_0.BackColor = Class181.color_3;
				this.radioButton_1.BackColor = Class181.color_3;
				this.radioButton_2.BackColor = Class181.color_3;
				this.panel_3.BackColor = Class181.color_3;
				this.checkBox_1.ForeColor = Class181.color_9;
				this.checkBox_1.BackColor = Class181.color_3;
				this.tableLayoutPanel_0.BackColor = Class181.color_3;
				this.tableLayoutPanel_1.BackColor = Class181.color_3;
				this.tableLayoutPanel_2.BackColor = Class181.color_3;
				this.radioButton_6.ForeColor = Class181.color_9;
				this.radioButton_5.ForeColor = Class181.color_9;
				this.radioButton_6.BackColor = Class181.color_3;
				this.radioButton_5.BackColor = Class181.color_3;
			}
			else
			{
				this.tabControlPanel_9.BackColor = Class181.color_9;
				this.panel_1.BackColor = Class181.color_9;
				this.tabControlPanel_4.BackColor = Class181.color_9;
				this.panel_0.BackColor = Class181.color_9;
				this.splitContainer_2.Panel1.BackColor = Class181.color_9;
				this.splitContainer_2.Panel2.BackColor = Class181.color_9;
				this.splitContainer_0.Panel1.BackColor = Class181.color_9;
				this.splitContainer_1.Panel1.BackColor = Class181.color_9;
				this.label_5.ForeColor = Class181.color_1;
				this.label_6.ForeColor = Class181.color_1;
				this.label_7.ForeColor = Class181.color_1;
				this.label_4.ForeColor = Class181.color_1;
				this.checkBox_0.ForeColor = Class181.color_1;
				this.numericUpDown_1.ForeColor = Class181.color_1;
				this.numericUpDown_0.ForeColor = Class181.color_1;
				this.numericUpDown_1.BackColor = Color.White;
				this.numericUpDown_0.BackColor = Color.White;
				this.panel_2.BackColor = Class181.color_9;
				this.radioButton_4.ForeColor = Class181.color_1;
				this.radioButton_3.ForeColor = Class181.color_1;
				this.radioButton_0.ForeColor = Class181.color_1;
				this.radioButton_1.ForeColor = Class181.color_1;
				this.radioButton_2.ForeColor = Class181.color_1;
				this.radioButton_4.BackColor = Class181.color_9;
				this.radioButton_3.BackColor = Class181.color_9;
				this.radioButton_0.BackColor = Class181.color_9;
				this.radioButton_1.BackColor = Class181.color_9;
				this.radioButton_2.BackColor = Class181.color_9;
				this.panel_3.BackColor = Class181.color_9;
				this.checkBox_1.ForeColor = Class181.color_1;
				this.checkBox_1.BackColor = Class181.color_9;
				this.tableLayoutPanel_0.BackColor = Class181.color_9;
				this.tableLayoutPanel_1.BackColor = Class181.color_9;
				this.tableLayoutPanel_2.BackColor = Class181.color_9;
				this.radioButton_6.ForeColor = Class181.color_1;
				this.radioButton_5.ForeColor = Class181.color_1;
				this.radioButton_6.BackColor = Class181.color_9;
				this.radioButton_5.BackColor = Class181.color_9;
			}
		}

		// Token: 0x060019D0 RID: 6608 RVA: 0x000B4A08 File Offset: 0x000B2C08
		private void method_67()
		{
			this.method_66();
			this.numericUpDown_0.Maximum = 9999999m;
			this.numericUpDown_0.Minimum = 1m;
			this.numericUpDown_0.Increment = 1m;
			this.numericUpDown_1.Maximum = 9999999m;
			this.numericUpDown_1.Minimum = 0m;
			this.method_73();
			this.numericUpDown_1.ValueChanged += this.numericUpDown_1_ValueChanged;
			this.numericUpDown_0.ValueChanged += this.numericUpDown_0_ValueChanged;
			this.numericUpDown_0.KeyPress += this.numericUpDown_0_KeyPress;
			this.checkBox_0.Click += this.checkBox_0_Click;
			this.checkBox_0.CheckedChanged += this.checkBox_0_CheckedChanged;
			this.radioButton_2.Click += this.radioButton_2_Click;
			this.radioButton_1.Click += this.radioButton_1_Click;
			this.radioButton_0.Click += this.radioButton_0_Click;
			this.radioButton_3.Click += this.radioButton_3_Click;
			this.radioButton_4.Click += this.radioButton_4_Click;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Click += this.button_3_Click;
			this.button_4.Click += this.button_4_Click;
			this.class289_1.Checked = true;
			this.class289_1.CheckedChanged += this.class289_1_CheckedChanged;
			this.class289_0.CheckedChanged += this.class289_0_CheckedChanged;
			this.method_111();
			this.checkBox_1.CheckedChanged += this.checkBox_1_CheckedChanged;
			this.radioButton_2.Tag = 0.15m;
			this.radioButton_1.Tag = 0.3m;
			this.radioButton_0.Tag = 0.5m;
			this.radioButton_3.Tag = 0.8m;
			this.radioButton_4.Tag = 1m;
			Base.UI.Form.FollowPrcInTradingInputChanged += this.method_80;
			Base.UI.smethod_69(this.tabControl_4);
			if (TApp.DpiScaleMulti > 1f)
			{
				this.splitContainer_2.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_2.SplitterDistance * TApp.DpiScaleMulti) * 1.1));
				this.splitContainer_1.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_1.SplitterDistance * TApp.DpiScaleMulti)));
				this.splitContainer_0.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_0.SplitterDistance * TApp.DpiScaleMulti)));
			}
			this.tabControl_4.SelectedTabChanged += this.tabControl_4_SelectedTabChanged;
			this.button_5.Click += this.button_5_Click;
			this.button_6.Click += this.button_6_Click;
			this.button_7.Click += this.button_7_Click;
			this.button_8.Click += this.button_8_Click;
			if (!TApp.IsFtIncluded)
			{
				this.button_7.Visible = false;
			}
			else
			{
				this.button_7.Visible = true;
			}
			this.tabControlPanel_9.ThemeAware = false;
			this.dataGridViewOpenTrans_0 = new DataGridViewOpenTrans();
			this.tabControlPanel_4.Controls.Add(this.dataGridViewOpenTrans_0);
			this.dataGridViewOpenTrans_0.method_5();
			if (Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				this.radioButton_5.Checked = true;
			}
			else
			{
				this.radioButton_6.Checked = true;
			}
			this.radioButton_5.CheckedChanged += this.radioButton_5_CheckedChanged;
			this.radioButton_6.CheckedChanged += this.radioButton_6_CheckedChanged;
			this.dataGridViewHisTrans_0 = new DataGridViewHisTrans();
			this.dataGridViewHisTrans_0.ChangeToHisTransDTRequested += this.method_84;
			this.tabControlPanel_2.Controls.Add(this.dataGridViewHisTrans_0);
			this.dataGridViewHisTrans_0.method_6();
			this.tabItem_1.Click += this.tabItem_1_Click;
			this.dateTimePicker_1.Enter += this.dateTimePicker_2_Enter;
			this.dateTimePicker_0.Enter += this.dateTimePicker_2_Enter;
			this.dateTimePicker_1.Leave += this.dateTimePicker_3_Leave;
			this.dateTimePicker_0.Leave += this.dateTimePicker_3_Leave;
			this.class293_0 = new Class293();
			this.tabControlPanel_6.Controls.Add(this.class293_0);
			this.class293_0.method_5();
			this.button_1.Click += this.button_1_Click;
			this.tabItem_5.Click += this.tabItem_5_Click;
			this.dateTimePicker_3.Enter += this.dateTimePicker_2_Enter;
			this.dateTimePicker_2.Enter += this.dateTimePicker_2_Enter;
			this.dateTimePicker_2.Leave += this.dateTimePicker_3_Leave;
			this.dateTimePicker_3.Leave += this.dateTimePicker_3_Leave;
			this.class294_0 = new Class294();
			this.class294_0.CreateCondOrderRequested += this.method_83;
			this.tabControlPanel_10.Controls.Add(this.class294_0);
			this.class294_0.method_6();
			if (this.ParentSplitPanel != null)
			{
				this.dataGridViewOpenTrans_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.dataGridViewOpenTrans_0.ContextMenuStrip);
				this.dataGridViewHisTrans_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.dataGridViewHisTrans_0.ContextMenuStrip);
				if (this.dataGridViewHisTrans_1 != null && this.dataGridViewHisTrans_1.ContextMenuStrip != null)
				{
					this.dataGridViewHisTrans_1.ContextMenuStrip.Items.Add(new ToolStripSeparator());
					this.method_87(this.dataGridViewHisTrans_1.ContextMenuStrip);
				}
				this.class293_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.class293_0.ContextMenuStrip);
			}
		}

		// Token: 0x060019D1 RID: 6609 RVA: 0x0000AC02 File Offset: 0x00008E02
		public void method_68()
		{
			this.method_69();
			this.method_70();
			this.method_72();
		}

		// Token: 0x060019D2 RID: 6610 RVA: 0x000B50D0 File Offset: 0x000B32D0
		public void method_69()
		{
			this.comboBox_0.Items.Clear();
			if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
			{
				this.comboBox_0.Text = Class521.smethod_0(24382);
			}
			else if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					if (!symbDataSet.IsCurrDateNotListedYet)
					{
						TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
						comboBoxItem.Text = symbDataSet.CurrSymbol.CNName + Class521.smethod_0(24872) + symbDataSet.CurrSymbol.Code.Trim() + Class521.smethod_0(5046);
						comboBoxItem.Value = symbDataSet.CurrSymbol;
						this.comboBox_0.Items.Add(comboBoxItem);
						if (symbDataSet.CurrSymbol.ID == Base.UI.CurrTradingSymbol.ID)
						{
							this.comboBox_0.SelectedIndexChanged -= this.comboBox_0_SelectedIndexChanged;
							this.comboBox_0.SelectedIndex = this.comboBox_0.Items.Count - 1;
							this.comboBox_0.SelectedValue = Base.UI.CurrTradingSymbol;
							this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
						}
					}
				}
			}
		}

		// Token: 0x060019D3 RID: 6611 RVA: 0x0000AC18 File Offset: 0x00008E18
		private void method_70()
		{
			this.method_71();
			this.numericUpDown_0.Value = Base.UI.smethod_180();
			this.method_77();
		}

		// Token: 0x060019D4 RID: 6612 RVA: 0x000B524C File Offset: 0x000B344C
		private void method_71()
		{
			this.label_4.Text = Class521.smethod_0(68776) + Base.Trading.smethod_207(Base.UI.CurrTradingSymbol).ToString();
		}

		// Token: 0x060019D5 RID: 6613 RVA: 0x000B5288 File Offset: 0x000B3488
		private void method_72()
		{
			if (Base.UI.CurrTradingSymbol != null)
			{
				this.numericUpDown_1.DecimalPlaces = Base.UI.CurrTradingSymbol.DigitNb;
				this.numericUpDown_1.Increment = Base.UI.CurrTradingSymbol.LeastPriceVar.Value;
				try
				{
					SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
					if (symbDataSet != null && symbDataSet.CurrHisData != null)
					{
						decimal value = Convert.ToDecimal(symbDataSet.CurrHisData.Close);
						this.numericUpDown_1.Value = value;
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x060019D6 RID: 6614 RVA: 0x0000AC3D File Offset: 0x00008E3D
		private void method_73()
		{
			this.method_74();
			this.method_75();
		}

		// Token: 0x060019D7 RID: 6615 RVA: 0x000B5320 File Offset: 0x000B3520
		private void method_74()
		{
			if (this.label_8.Visible)
			{
				this.label_8.Text = string.Format(Class521.smethod_0(68785), Base.Acct.smethod_19() / 1.000000000000000000000m);
			}
		}

		// Token: 0x060019D8 RID: 6616 RVA: 0x000B537C File Offset: 0x000B357C
		private void method_75()
		{
			if (this.label_10.Visible)
			{
				decimal? num = Base.Acct.smethod_23();
				if (num != null)
				{
					decimal num2 = Math.Round(num.Value, 0) / 1.00000000000000000000000m;
					this.label_10.Text = num2.ToString() + Class521.smethod_0(5356);
				}
				else
				{
					this.label_10.Text = Class521.smethod_0(18686);
				}
			}
		}

		// Token: 0x060019D9 RID: 6617 RVA: 0x0000AC4D File Offset: 0x00008E4D
		private void button_2_Click(object sender, EventArgs e)
		{
			this.method_76(true);
		}

		// Token: 0x060019DA RID: 6618 RVA: 0x0000AC58 File Offset: 0x00008E58
		private void button_3_Click(object sender, EventArgs e)
		{
			this.method_76(false);
		}

		// Token: 0x060019DB RID: 6619 RVA: 0x000B540C File Offset: 0x000B360C
		private void method_76(bool bool_2)
		{
			bool? nullable_ = new bool?(this.class289_1.Checked);
			if (this.checkBox_1.Checked)
			{
				nullable_ = null;
			}
			if (!Base.UI.Form.EnableShortForStock && Base.UI.CurrTradingSymbol.IsStock)
			{
				if (bool_2)
				{
					nullable_ = new bool?(true);
				}
				else
				{
					nullable_ = new bool?(false);
				}
			}
			decimal value = this.numericUpDown_1.Value;
			decimal value2 = this.numericUpDown_0.Value;
			Base.Trading.smethod_222(Base.UI.CurrTradingSymbol.ID, bool_2, nullable_, value, value2);
		}

		// Token: 0x060019DC RID: 6620 RVA: 0x000B549C File Offset: 0x000B369C
		private void checkBox_0_Click(object sender, EventArgs e)
		{
			if (this.checkBox_0.Checked && Base.UI.CurrTradingSymbol != null)
			{
				SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					double close = symbDataSet.CurrHisDataSet.CurrHisData.Close;
					try
					{
						this.numericUpDown_1.Value = Convert.ToDecimal(close);
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060019DD RID: 6621 RVA: 0x0000AC63 File Offset: 0x00008E63
		private void checkBox_0_CheckedChanged(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfFollowPrcInTradingTab != this.checkBox_0.Checked)
			{
				Base.UI.Form.IfFollowPrcInTradingTab = this.checkBox_0.Checked;
			}
		}

		// Token: 0x060019DE RID: 6622 RVA: 0x0000AC93 File Offset: 0x00008E93
		private void radioButton_2_Click(object sender, EventArgs e)
		{
			this.method_78(0.15m);
		}

		// Token: 0x060019DF RID: 6623 RVA: 0x0000ACA8 File Offset: 0x00008EA8
		private void radioButton_1_Click(object sender, EventArgs e)
		{
			this.method_78(0.3m);
		}

		// Token: 0x060019E0 RID: 6624 RVA: 0x0000ACBC File Offset: 0x00008EBC
		private void radioButton_0_Click(object sender, EventArgs e)
		{
			this.method_78(0.5m);
		}

		// Token: 0x060019E1 RID: 6625 RVA: 0x0000ACD0 File Offset: 0x00008ED0
		private void radioButton_3_Click(object sender, EventArgs e)
		{
			this.method_78(0.8m);
		}

		// Token: 0x060019E2 RID: 6626 RVA: 0x0000ACE4 File Offset: 0x00008EE4
		private void radioButton_4_Click(object sender, EventArgs e)
		{
			this.method_78(1m);
		}

		// Token: 0x060019E3 RID: 6627 RVA: 0x0000ACF3 File Offset: 0x00008EF3
		private void class289_1_CheckedChanged(object sender, EventArgs e)
		{
			this.method_77();
		}

		// Token: 0x060019E4 RID: 6628 RVA: 0x0000ACF3 File Offset: 0x00008EF3
		private void class289_0_CheckedChanged(object sender, EventArgs e)
		{
			this.method_77();
		}

		// Token: 0x060019E5 RID: 6629 RVA: 0x000B5518 File Offset: 0x000B3718
		private void method_77()
		{
			decimal? num = this.method_79();
			if (num != null)
			{
				this.method_78(num.Value);
			}
		}

		// Token: 0x060019E6 RID: 6630 RVA: 0x000B5544 File Offset: 0x000B3744
		private void method_78(decimal decimal_0)
		{
			long value = Base.Trading.smethod_207(Base.UI.CurrTradingSymbol);
			if (this.class289_0.Enabled && this.class289_0.Visible && this.class289_0.Checked)
			{
				long? num = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_14)).Sum(new Func<ShownOpenTrans, long?>(TransTabs.<>c.<>9.method_15));
				if (num != null)
				{
					long? num2 = num;
					if (num2.GetValueOrDefault() > 0L & num2 != null)
					{
						value = num.Value;
						goto IL_152;
					}
				}
				long? num3 = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_16)).Sum(new Func<ShownOpenTrans, long?>(TransTabs.<>c.<>9.method_17));
				if (num3 != null)
				{
					long? num2 = num3;
					if (num2.GetValueOrDefault() > 0L & num2 != null)
					{
						value = num3.Value;
					}
				}
			}
			IL_152:
			decimal num4 = Math.Floor(value * decimal_0);
			if (num4 >= 1m)
			{
				try
				{
					this.numericUpDown_0.Value = num4;
					return;
				}
				catch
				{
					return;
				}
			}
			this.numericUpDown_0.Value = 1m;
		}

		// Token: 0x060019E7 RID: 6631 RVA: 0x000B56F8 File Offset: 0x000B38F8
		private decimal? method_79()
		{
			IEnumerable<RadioButton> source = this.panel_2.Controls.OfType<RadioButton>().Where(new Func<RadioButton, bool>(TransTabs.<>c.<>9.method_18));
			decimal? result;
			if (source.Any<RadioButton>())
			{
				result = new decimal?(Convert.ToDecimal(source.First<RadioButton>().Tag));
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019E8 RID: 6632 RVA: 0x000B5768 File Offset: 0x000B3968
		private void checkBox_1_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_1.Checked)
			{
				this.class289_1.Enabled = false;
				this.class289_0.Enabled = false;
			}
			else
			{
				this.class289_1.Enabled = true;
				this.class289_0.Enabled = true;
			}
		}

		// Token: 0x060019E9 RID: 6633 RVA: 0x0000ACFD File Offset: 0x00008EFD
		private void method_80(object sender, EventArgs e)
		{
			if (this.checkBox_0.Checked != Base.UI.Form.IfFollowPrcInTradingTab)
			{
				this.checkBox_0.Checked = Base.UI.Form.IfFollowPrcInTradingTab;
			}
		}

		// Token: 0x060019EA RID: 6634 RVA: 0x0000AD2D File Offset: 0x00008F2D
		private void numericUpDown_1_ValueChanged(object sender, EventArgs e)
		{
			Base.UI.Form.TradingPrice = this.numericUpDown_1.Value;
			this.method_3();
		}

		// Token: 0x060019EB RID: 6635 RVA: 0x0000AD4C File Offset: 0x00008F4C
		private void numericUpDown_0_ValueChanged(object sender, EventArgs e)
		{
			if (this.numericUpDown_0.Focused)
			{
				this.method_81();
			}
			Base.UI.Form.TradingUnits = Convert.ToInt32(this.numericUpDown_0.Value);
			this.method_2();
		}

		// Token: 0x060019EC RID: 6636 RVA: 0x0000AD83 File Offset: 0x00008F83
		private void numericUpDown_0_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (char.IsDigit(e.KeyChar))
			{
				this.method_81();
			}
		}

		// Token: 0x060019ED RID: 6637 RVA: 0x000B57B8 File Offset: 0x000B39B8
		private void method_81()
		{
			IEnumerable<RadioButton> source = this.panel_2.Controls.OfType<RadioButton>().Where(new Func<RadioButton, bool>(TransTabs.<>c.<>9.method_19));
			if (source.Any<RadioButton>())
			{
				source.First<RadioButton>().Checked = false;
			}
		}

		// Token: 0x060019EE RID: 6638 RVA: 0x000B5810 File Offset: 0x000B3A10
		private void button_4_Click(object sender, EventArgs e)
		{
			TransTabs.Class324 @class = new TransTabs.Class324();
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else
			{
				int num2;
				int num = this.class289_1.Enabled ? (num2 = (this.class289_1.Checked ? 1 : 0)) : (num2 = 1);
				bool flag = true;
				List<ShownOpenTrans> list = null;
				int num3;
				if (Base.Trading.CurrOpenTransList != null)
				{
					list = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_20)).ToList<ShownOpenTrans>();
					if ((num3 = num2) != 0)
					{
						goto IL_B2;
					}
				}
				else if ((num3 = num) != 0)
				{
					goto IL_B2;
				}
				if (list != null && list.Any<ShownOpenTrans>())
				{
					if (list.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_21)).Any<ShownOpenTrans>())
					{
						flag = false;
					}
				}
				IL_B2:
				OrderType orderType_ = Base.Trading.smethod_76(num3 != 0, flag);
				Base.UI.smethod_125();
				@class.int_0 = Base.UI.CurrTradingSymbol.ID;
				SymbDataSet symbDataSet = Base.Data.smethod_49(@class.int_0, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					List<Transaction> list2 = Base.Trading.Transactions.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
					new Form12(null, Base.UI.CurrTradingSymbol, orderType_, null, Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close), 0m, this.numericUpDown_0.Value, null, list2)
					{
						ShowInTaskbar = false
					}.ShowDialog();
				}
				else
				{
					Class184.smethod_0(new Exception(Class521.smethod_0(68798)));
				}
			}
		}

		// Token: 0x060019EF RID: 6639 RVA: 0x000B599C File Offset: 0x000B3B9C
		private void tabControl_4_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			string a = e.NewTab.Text.Trim();
			if (a == Class521.smethod_0(68875))
			{
				this.button_6.Visible = true;
				if (!TApp.IsFtIncluded)
				{
					this.button_7.Visible = false;
				}
				else
				{
					this.button_7.Visible = true;
				}
				this.button_8.Visible = true;
				this.button_5.Text = Class521.smethod_0(68892);
				this.button_6.Text = Class521.smethod_0(52717);
				this.button_7.Text = Class521.smethod_0(68909);
				this.radioButton_5.Visible = true;
				this.radioButton_6.Visible = true;
			}
			else if (a == Class521.smethod_0(68926))
			{
				this.button_6.Visible = false;
				this.button_7.Visible = false;
				this.button_8.Visible = false;
				this.button_5.Text = Class521.smethod_0(68943);
				this.radioButton_5.Visible = false;
				this.radioButton_6.Visible = false;
			}
			else if (a == Class521.smethod_0(68960))
			{
				this.button_6.Visible = true;
				this.button_7.Visible = false;
				this.button_8.Visible = false;
				this.button_5.Text = Class521.smethod_0(47057);
				this.button_6.Text = Class521.smethod_0(52504);
				this.radioButton_5.Visible = false;
				this.radioButton_6.Visible = false;
			}
			else if (a == Class521.smethod_0(68977))
			{
				this.button_6.Visible = true;
				this.button_7.Visible = true;
				this.button_8.Visible = false;
				this.button_5.Text = Class521.smethod_0(5783);
				this.button_6.Text = Class521.smethod_0(52956);
				this.button_7.Text = Class521.smethod_0(68990);
				this.radioButton_5.Visible = false;
				this.radioButton_6.Visible = false;
			}
		}

		// Token: 0x060019F0 RID: 6640 RVA: 0x000B5BD8 File Offset: 0x000B3DD8
		private void button_5_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == Class521.smethod_0(68892))
			{
				if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
				{
					this.dataGridViewOpenTrans_0.method_14();
				}
				else if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					MessageBox.Show(Class521.smethod_0(69003), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == Class521.smethod_0(68943))
			{
				if (this.tabControl_2.SelectedTab.Text.Trim() == Class521.smethod_0(69040))
				{
					if (this.dataGridViewHisTrans_0.DataSource != null)
					{
						SortableBindingList<ShownHisTrans> sortableBindingList = this.dataGridViewHisTrans_0.DataSource as SortableBindingList<ShownHisTrans>;
						if (sortableBindingList.Count > 0)
						{
							this.dataGridViewHisTrans_0.method_10(sortableBindingList);
						}
					}
				}
				else if (this.dataGridViewHisTrans_1 != null && this.dataGridViewHisTrans_1.DataSource != null)
				{
					SortableBindingList<ShownHisTrans> sortableBindingList2 = this.dataGridViewHisTrans_1.DataSource as SortableBindingList<ShownHisTrans>;
					if (sortableBindingList2.Count > 0)
					{
						this.dataGridViewHisTrans_0.method_10(sortableBindingList2);
					}
				}
			}
			else if (button.Text == Class521.smethod_0(47057))
			{
				if (this.class293_0.SelectedRows.Count == 1)
				{
					Base.Trading.smethod_73((this.class293_0.SelectedRows[0].DataBoundItem as ShownOrder).ID);
					this.class293_0.method_5();
				}
				else if (this.class293_0.RowCount > 0)
				{
					MessageBox.Show(Class521.smethod_0(69057), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == Class521.smethod_0(5783))
			{
				if (this.class294_0.SelectedRows.Count == 1)
				{
					Base.Trading.smethod_94((this.class294_0.SelectedRows[0].DataBoundItem as ShownCondOrder).ID, OrderStatus.Canceled);
					this.class294_0.Refresh();
				}
				else if (this.class294_0.RowCount > 0)
				{
					MessageBox.Show(Class521.smethod_0(69094), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019F1 RID: 6641 RVA: 0x000B5E3C File Offset: 0x000B403C
		private void button_6_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == Class521.smethod_0(52717))
			{
				if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					if (Base.Acct.CurrAccount.IsReadOnly)
					{
						Base.Acct.smethod_50();
					}
					else if (Base.Trading.smethod_217() && Base.Trading.smethod_55())
					{
						this.dataGridViewOpenTrans_0.method_5();
					}
				}
			}
			else if (button.Text == Class521.smethod_0(52504))
			{
				if (this.class293_0.RowCount > 0)
				{
					Base.Trading.smethod_74();
					this.class293_0.method_5();
				}
			}
			else if (button.Text == Class521.smethod_0(52956) && this.class294_0.RowCount > 0)
			{
				Base.Trading.smethod_111();
				this.class294_0.Refresh();
			}
		}

		// Token: 0x060019F2 RID: 6642 RVA: 0x000B5F1C File Offset: 0x000B411C
		private void button_7_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == Class521.smethod_0(68909))
			{
				if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
				{
					ShownOpenTrans shownOpenTrans = this.dataGridViewOpenTrans_0.SelectedRows[0].DataBoundItem as ShownOpenTrans;
					if (Base.Trading.smethod_57(shownOpenTrans))
					{
						OrderType orderType_ = OrderType.Order_OpenLong;
						if (shownOpenTrans.TransType == 1)
						{
							orderType_ = OrderType.Order_OpenShort;
						}
						Base.Trading.smethod_197(shownOpenTrans.SymbolID, orderType_, shownOpenTrans.Units, 0m);
						this.dataGridViewOpenTrans_0.method_5();
					}
				}
				else if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					MessageBox.Show(Class521.smethod_0(69003), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == Class521.smethod_0(68990))
			{
				this.button_4_Click(this, null);
			}
		}

		// Token: 0x060019F3 RID: 6643 RVA: 0x000B6004 File Offset: 0x000B4204
		private void button_8_Click(object sender, EventArgs e)
		{
			if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
			{
				ShownOpenTrans shownOpenTrans = this.dataGridViewOpenTrans_0.SelectedRows[0].DataBoundItem as ShownOpenTrans;
				SymbDataSet symbDataSet = Base.Data.smethod_49(shownOpenTrans.SymbolID, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet && symbDataSet.CurrHisData.Close > 0.0)
				{
					this.dataGridViewOpenTrans_0.method_12(shownOpenTrans);
				}
				else
				{
					MessageBox.Show(Class521.smethod_0(69135), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019F4 RID: 6644 RVA: 0x0000AD9A File Offset: 0x00008F9A
		public void method_82()
		{
			this.dataGridViewOpenTrans_0.method_5();
			this.dataGridViewHisTrans_0.method_6();
			this.class293_0.method_5();
			this.class294_0.method_6();
		}

		// Token: 0x060019F5 RID: 6645 RVA: 0x000B609C File Offset: 0x000B429C
		private void radioButton_6_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_6.Checked && Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				Base.UI.Form.IfShowIndividualShownOpenTrans = false;
				Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
			}
		}

		// Token: 0x060019F6 RID: 6646 RVA: 0x000B60F0 File Offset: 0x000B42F0
		private void radioButton_5_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_5.Checked && !Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				Base.UI.Form.IfShowIndividualShownOpenTrans = true;
				Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
			}
		}

		// Token: 0x060019F7 RID: 6647 RVA: 0x0000ADCA File Offset: 0x00008FCA
		private void method_83(object sender, EventArgs e)
		{
			this.button_4_Click(this, new EventArgs());
		}

		// Token: 0x060019F8 RID: 6648 RVA: 0x0000ADDA File Offset: 0x00008FDA
		private void dateTimePicker_2_Enter(object sender, EventArgs e)
		{
			this.method_0();
		}

		// Token: 0x060019F9 RID: 6649 RVA: 0x0000ADE4 File Offset: 0x00008FE4
		private void dateTimePicker_3_Leave(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x060019FA RID: 6650 RVA: 0x0000ADEE File Offset: 0x00008FEE
		private void method_84(EventArgs16 eventArgs16_0)
		{
			this.method_4(eventArgs16_0.ShownHisTrans);
		}

		// Token: 0x060019FB RID: 6651 RVA: 0x000041B9 File Offset: 0x000023B9
		public void method_85()
		{
		}

		// Token: 0x060019FC RID: 6652 RVA: 0x000B6144 File Offset: 0x000B4344
		public void method_86()
		{
			if (this.ParentSplitPanel != null)
			{
				ContextMenuStrip contextMenuStrip_ = new ContextMenuStrip();
				this.method_87(contextMenuStrip_);
				this.trdAnalysisPanel_0.method_1(contextMenuStrip_);
			}
		}

		// Token: 0x060019FD RID: 6653 RVA: 0x000B6174 File Offset: 0x000B4374
		public void method_87(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(69248);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(64362);
			toolStripMenuItem2.Click += this.method_90;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(64349);
			toolStripMenuItem3.Click += this.method_92;
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(64375);
			toolStripMenuItem4.Checked = true;
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
		}

		// Token: 0x060019FE RID: 6654 RVA: 0x000B6230 File Offset: 0x000B4430
		public ChtCtrl_Tick method_88()
		{
			SplitterPanel parentSplitPanel = this.ParentSplitPanel;
			if (this.ParentTransTabCtrl != null)
			{
				parentSplitPanel = this.ParentTransTabCtrl.ParentSplitPanel;
			}
			ChtCtrl_Tick result;
			if (parentSplitPanel != null)
			{
				this.method_89();
				result = Base.UI.smethod_150(this, parentSplitPanel);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019FF RID: 6655 RVA: 0x000B6270 File Offset: 0x000B4470
		private void method_89()
		{
			Base.UI.Form.LastFuncTabsIdx = new int?(this.SelectedTabIndex);
			Base.UI.Form.LastMktSymbTabsIdx = new int?(this.MktSelectedTabIndex);
			Base.UI.TransTabs = null;
			base.Enabled = false;
			base.Visible = false;
			if (this.ParentTransTabCtrl != null)
			{
				this.ParentTransTabCtrl.IsSwitchedBehind = true;
			}
		}

		// Token: 0x06001A00 RID: 6656 RVA: 0x0000ADFE File Offset: 0x00008FFE
		private void method_90(object sender, EventArgs e)
		{
			this.method_88();
		}

		// Token: 0x06001A01 RID: 6657 RVA: 0x000B62D4 File Offset: 0x000B44D4
		public ChtCtrl_KLine method_91()
		{
			ChtCtrl_KLine result;
			if (this.ParentTransTabCtrl != null && this.ParentTransTabCtrl.ParentSplitPanel != null)
			{
				this.method_89();
				result = Base.UI.smethod_152(this, this.ParentTransTabCtrl.ParentSplitPanel);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001A02 RID: 6658 RVA: 0x0000AE09 File Offset: 0x00009009
		private void method_92(object sender, EventArgs e)
		{
			this.method_91();
		}

		// Token: 0x06001A03 RID: 6659 RVA: 0x0000AE14 File Offset: 0x00009014
		private void method_93()
		{
			this.control1_0.method_0();
		}

		// Token: 0x06001A04 RID: 6660 RVA: 0x000B6318 File Offset: 0x000B4518
		public List<string> method_94()
		{
			return this.control1_0.method_26();
		}

		// Token: 0x06001A05 RID: 6661 RVA: 0x000B6334 File Offset: 0x000B4534
		public AdvTree method_95()
		{
			return this.control1_0.method_27();
		}

		// Token: 0x06001A06 RID: 6662 RVA: 0x0000AE23 File Offset: 0x00009023
		private void method_96()
		{
			this.control2_0.method_0();
		}

		// Token: 0x06001A07 RID: 6663 RVA: 0x0000AE32 File Offset: 0x00009032
		private void method_97()
		{
			this.symbFilterPanel_0.method_1();
			this.symbFilterPanel_0.MsgNotifyNeeded += this.method_98;
		}

		// Token: 0x06001A08 RID: 6664 RVA: 0x0000AE58 File Offset: 0x00009058
		private void method_98(object sender, MsgEventArgs e)
		{
			this.method_5(e.Msg);
		}

		// Token: 0x06001A09 RID: 6665 RVA: 0x0000AE68 File Offset: 0x00009068
		private void method_99()
		{
			if (this.tabControlPanel_14.Controls.Count == 0)
			{
				this.method_100();
			}
		}

		// Token: 0x06001A0A RID: 6666 RVA: 0x000B6350 File Offset: 0x000B4550
		private void method_100()
		{
			Control12 control = new Control12();
			control.Dock = DockStyle.Fill;
			this.tabControlPanel_14.Controls.Add(control);
		}

		// Token: 0x06001A0B RID: 6667 RVA: 0x0000AE84 File Offset: 0x00009084
		private void method_101(object sender, EventArgs e)
		{
			this.method_103();
			this.method_104();
			this.method_75();
		}

		// Token: 0x06001A0C RID: 6668 RVA: 0x0000AE9A File Offset: 0x0000909A
		private void method_102(object sender, EventArgs e)
		{
			this.method_74();
			this.method_26(false, null);
		}

		// Token: 0x06001A0D RID: 6669 RVA: 0x000B6380 File Offset: 0x000B4580
		private void method_103()
		{
			if (this.CurrShowMktSymbList != null)
			{
				for (int i = 0; i < this.CurrShowMktSymbList.Count; i++)
				{
					ShowMktSymb showMktSymb = this.CurrShowMktSymbList[i];
					SymbDataSet symbDataSet = Base.Data.smethod_49(showMktSymb.StkId, false);
					if (symbDataSet != null && symbDataSet.HasValidDataSet)
					{
						DateTime? lastDT = showMktSymb.LastDT;
						DateTime date = symbDataSet.CurrHisDataSet.CurrHisData.Date;
						if (lastDT == null || (lastDT != null && !(lastDT.GetValueOrDefault() == date)))
						{
							DateTime? lastDT2 = showMktSymb.LastDT;
							if (showMktSymb.StkId != symbDataSet.CurrSymbol.ID || (lastDT2 == null || symbDataSet.CurrHisDataSet.CurrExchgOBT == null || !(lastDT2.Value >= symbDataSet.CurrHisDataSet.CurrDayBeginDT)) || !(lastDT2.Value <= symbDataSet.CurrHisDataSet.CurrHisData.Date))
							{
								this.method_57(showMktSymb);
							}
							else
							{
								showMktSymb.method_17(symbDataSet.CurrHisDataSet.CurrHisData);
								showMktSymb = this.method_58(showMktSymb);
							}
							if (!Base.UI.Form.IsInBlindTestMode)
							{
								if (this.DataGridViewListWithCurrMktSymb != null)
								{
									using (List<KeyValuePair<DataGridView, int>>.Enumerator enumerator = this.DataGridViewListWithCurrMktSymb.GetEnumerator())
									{
										while (enumerator.MoveNext())
										{
											KeyValuePair<DataGridView, int> keyValuePair = enumerator.Current;
											if (keyValuePair.Key.Visible)
											{
												keyValuePair.Key.Refresh();
											}
										}
										goto IL_194;
									}
								}
								if (showMktSymb.IsInZiXuan && this.dataGridViewMkt_0.Visible)
								{
									this.dataGridViewMkt_0.Refresh();
								}
							}
						}
					}
					IL_194:;
				}
			}
		}

		// Token: 0x06001A0E RID: 6670 RVA: 0x000B6548 File Offset: 0x000B4748
		private void method_104()
		{
			if (this.IfFollowPrice)
			{
				SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
				try
				{
					if (symbDataSet != null && symbDataSet.HasValidDataSet)
					{
						this.numericUpDown_1.Value = Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close);
					}
				}
				catch
				{
				}
			}
			this.method_77();
			this.label_4.Text = Class521.smethod_0(68776) + Base.Trading.smethod_207(Base.UI.CurrTradingSymbol).ToString();
		}

		// Token: 0x06001A0F RID: 6671 RVA: 0x0000AEAC File Offset: 0x000090AC
		private void method_105(object sender, EventArgs e)
		{
			BaoDianMgr.smethod_8();
		}

		// Token: 0x06001A10 RID: 6672 RVA: 0x0000AEB5 File Offset: 0x000090B5
		private void method_106(object sender, EventArgs e)
		{
			this.CurrShowMktSymbList = this.method_53();
			this.method_51();
			this.method_123();
			this.method_124();
			this.method_68();
			this.method_73();
		}

		// Token: 0x06001A11 RID: 6673 RVA: 0x000B65E4 File Offset: 0x000B47E4
		private void method_107(EventArgs1 eventArgs1_0)
		{
			int newSymbID = eventArgs1_0.NewSymbID;
			this.CurrShowMktSymbList = this.method_53();
			this.method_52(newSymbID);
			this.method_68();
			this.method_111();
		}

		// Token: 0x06001A12 RID: 6674 RVA: 0x000B661C File Offset: 0x000B481C
		private void method_108(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_17(symbDataSet_);
		}

		// Token: 0x06001A13 RID: 6675 RVA: 0x000B663C File Offset: 0x000B483C
		private void method_109(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_18(symbDataSet_);
		}

		// Token: 0x06001A14 RID: 6676 RVA: 0x0000AEE3 File Offset: 0x000090E3
		private void method_110(object sender, EventArgs e)
		{
			if (this.tabControl_1.Name == Class521.smethod_0(69265))
			{
				this.method_26(false, null);
			}
		}

		// Token: 0x06001A15 RID: 6677 RVA: 0x000B665C File Offset: 0x000B485C
		private void method_111()
		{
			if (Base.UI.CurrTradingSymbol.IsStock && !Base.UI.Form.EnableShortForStock)
			{
				this.class289_1.Visible = false;
				this.class289_0.Visible = false;
				this.checkBox_1.Visible = false;
			}
			else
			{
				this.class289_1.Visible = true;
				this.class289_0.Visible = true;
				this.checkBox_1.Visible = true;
			}
		}

		// Token: 0x06001A16 RID: 6678 RVA: 0x000B66D0 File Offset: 0x000B48D0
		private void method_112(EventArgs17 eventArgs17_0)
		{
			if (!Base.UI.Form.IfDisableAutoShowCurrTransTab)
			{
				Transaction transaction = eventArgs17_0.Transaction;
				if ((transaction.TransType == 1 || transaction.TransType == 3) && this.SelectedTabIndex != 1)
				{
					this.SelectedTabIndex = 1;
					this.TransSelectedTabIndex = 0;
				}
				this.method_82();
				this.method_70();
				this.method_73();
			}
		}

		// Token: 0x06001A17 RID: 6679 RVA: 0x0000AF0B File Offset: 0x0000910B
		private void method_113(object sender, EventArgs e)
		{
			this.method_82();
			this.method_70();
			this.method_73();
		}

		// Token: 0x06001A18 RID: 6680 RVA: 0x0000AF0B File Offset: 0x0000910B
		private void method_114(EventArgs14 eventArgs14_0)
		{
			this.method_82();
			this.method_70();
			this.method_73();
		}

		// Token: 0x06001A19 RID: 6681 RVA: 0x0000AF21 File Offset: 0x00009121
		private void method_115(EventArgs14 eventArgs14_0)
		{
			this.class293_0.Refresh();
		}

		// Token: 0x06001A1A RID: 6682 RVA: 0x0000AF30 File Offset: 0x00009130
		private void method_116(EventArgs15 eventArgs15_0)
		{
			this.class294_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1B RID: 6683 RVA: 0x0000AF30 File Offset: 0x00009130
		private void method_117(object sender, EventArgs e)
		{
			this.class294_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1C RID: 6684 RVA: 0x0000AF4B File Offset: 0x0000914B
		private void method_118(EventArgs15 eventArgs15_0)
		{
			this.class294_0.Refresh();
		}

		// Token: 0x06001A1D RID: 6685 RVA: 0x0000AF30 File Offset: 0x00009130
		private void method_119(EventArgs15 eventArgs15_0)
		{
			this.class294_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1E RID: 6686 RVA: 0x0000AF5A File Offset: 0x0000915A
		private void method_120(object sender, EventArgs e)
		{
			this.method_129();
		}

		// Token: 0x06001A1F RID: 6687 RVA: 0x0000AF64 File Offset: 0x00009164
		private void method_121(object sender, EventArgs e)
		{
			this.method_111();
		}

		// Token: 0x06001A20 RID: 6688 RVA: 0x0000AF6E File Offset: 0x0000916E
		private void method_122(object sender, EventArgs e)
		{
			Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
		}

		// Token: 0x06001A21 RID: 6689 RVA: 0x000B6730 File Offset: 0x000B4930
		private void tabControl_1_SelectedTabChanging(object sender, TabStripTabChangingEventArgs e)
		{
			Base.UI.MainForm.AcceptButton = null;
			if (!(e.NewTab.Name == Class521.smethod_0(69290)))
			{
				this.method_134(false);
				this.method_135();
				if (e.NewTab.Name == Class521.smethod_0(69265))
				{
					this.method_26(true, null);
				}
				else if (e.NewTab.Name == Class521.smethod_0(69307))
				{
					this.trdAnalysisPanel_0.method_7();
				}
				else if (e.NewTab.Name == Class521.smethod_0(69332))
				{
					Base.UI.MainForm.AcceptButton = this.symbFilterPanel_0.AcceptButton;
				}
			}
		}

		// Token: 0x06001A22 RID: 6690 RVA: 0x000B67F8 File Offset: 0x000B49F8
		private void tabItem_1_Click(object sender, EventArgs e)
		{
			if (this.dataGridViewHisTrans_1 == null)
			{
				this.splitContainer_0.Panel2.SuspendLayout();
				this.dataGridViewHisTrans_1 = new DataGridViewHisTrans(new SortableBindingList<ShownHisTrans>());
				this.dataGridViewHisTrans_1.ChangeToHisTransDTRequested += this.method_84;
				this.splitContainer_0.Panel2.Controls.Add(this.dataGridViewHisTrans_1);
				this.dataGridViewHisTrans_1.method_6();
				this.splitContainer_0.Panel2.ResumeLayout();
				this.button_0.Click += this.button_0_Click;
				if ((this.dateTimePicker_1.Value - DateTime.Now).TotalMinutes < 1.0)
				{
					try
					{
						this.dateTimePicker_1.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date.AddDays(-7.0);
						this.dateTimePicker_0.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date;
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001A23 RID: 6691 RVA: 0x000B692C File Offset: 0x000B4B2C
		private void button_0_Click(object sender, EventArgs e)
		{
			DateTime value = this.dateTimePicker_1.Value;
			DateTime value2 = this.dateTimePicker_0.Value;
			SortableBindingList<ShownHisTrans> dataSource = Base.Trading.smethod_146(value, value2);
			this.dataGridViewHisTrans_1.DataSource = dataSource;
			this.dataGridViewHisTrans_1.Refresh();
		}

		// Token: 0x06001A24 RID: 6692 RVA: 0x000B6970 File Offset: 0x000B4B70
		private void method_123()
		{
			if (this.dataGridViewHisTrans_0 != null)
			{
				this.dataGridViewHisTrans_0.DataSource = null;
				this.dataGridViewHisTrans_0.Refresh();
			}
			if (this.dataGridViewHisTrans_1 != null)
			{
				this.dataGridViewHisTrans_1.DataSource = null;
				this.dataGridViewHisTrans_1.Refresh();
			}
		}

		// Token: 0x06001A25 RID: 6693 RVA: 0x000B69C0 File Offset: 0x000B4BC0
		private void method_124()
		{
			if (this.class293_0 != null)
			{
				this.class293_0.DataSource = null;
				this.class293_0.Refresh();
			}
			if (this.class293_1 != null)
			{
				this.class293_1.DataSource = null;
				this.class293_1.Refresh();
			}
		}

		// Token: 0x06001A26 RID: 6694 RVA: 0x0000AF90 File Offset: 0x00009190
		private void tabItem_5_Click(object sender, EventArgs e)
		{
			if (this.class293_1 == null)
			{
				this.method_125();
			}
		}

		// Token: 0x06001A27 RID: 6695 RVA: 0x000B6A10 File Offset: 0x000B4C10
		private void method_125()
		{
			this.class293_1 = new Class293(new SortableBindingList<ShownOrder>());
			this.splitContainer_1.Panel2.Controls.Add(this.class293_1);
			this.class293_1.method_5();
			if ((this.dateTimePicker_3.Value - DateTime.Now).TotalMinutes < 1.0 && Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrHisData != null)
			{
				this.dateTimePicker_3.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date.AddDays(-7.0);
				this.dateTimePicker_2.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date;
			}
		}

		// Token: 0x06001A28 RID: 6696 RVA: 0x000B6AE8 File Offset: 0x000B4CE8
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.class293_1 == null)
			{
				this.method_125();
			}
			DateTime value = this.dateTimePicker_3.Value;
			DateTime value2 = this.dateTimePicker_2.Value;
			this.class293_1.DataSource = null;
			SortableBindingList<ShownOrder> dataSource = Base.Trading.smethod_42(value, value2);
			this.class293_1.DataSource = dataSource;
			this.class293_1.method_9();
			this.class293_1.Refresh();
		}

		// Token: 0x06001A29 RID: 6697 RVA: 0x0000AFA2 File Offset: 0x000091A2
		private void method_126(object sender, ScrollEventArgs e)
		{
			((TabControlPanel)sender).Invalidate();
		}

		// Token: 0x06001A2A RID: 6698 RVA: 0x000B6B54 File Offset: 0x000B4D54
		private bool method_127(ShowMktSymb showMktSymb_0)
		{
			bool result;
			if (this.dataGridViewMkt_0.DataSource != null)
			{
				result = (this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>).Contains(showMktSymb_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001A2B RID: 6699 RVA: 0x0000AFB1 File Offset: 0x000091B1
		private void method_128(object sender, EventArgs e)
		{
			this.method_69();
			this.method_70();
			this.method_72();
			this.method_111();
		}

		// Token: 0x06001A2C RID: 6700 RVA: 0x000B6B8C File Offset: 0x000B4D8C
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			TEx.Util.ComboBoxItem comboBoxItem = (sender as ComboBox).SelectedItem as TEx.Util.ComboBoxItem;
			if (comboBoxItem != null && comboBoxItem.Value != null)
			{
				StkSymbol stkSymbol = comboBoxItem.Value as StkSymbol;
				if (Base.UI.CurrTradingSymbol != null && Base.UI.CurrTradingSymbol.ID != stkSymbol.ID)
				{
					Base.UI.CurrTradingSymbol = stkSymbol;
					this.method_70();
					this.method_72();
				}
			}
		}

		// Token: 0x06001A2D RID: 6701 RVA: 0x000B6BF0 File Offset: 0x000B4DF0
		private void TransTabs_Disposed(object sender, EventArgs e)
		{
			try
			{
				Base.Data.CurrSymblChanged -= this.method_107;
				Base.Acct.AccountChanging -= this.method_105;
				Base.Acct.AccountChanged -= this.method_106;
				Base.Trading.TransCreated -= this.method_112;
				Base.Trading.OrderCanceled -= this.method_113;
				Base.Trading.OrderCreated -= this.method_114;
				Base.Trading.OrderPriceUnitsUpdated -= this.method_115;
				Base.Trading.CondOrderCreated -= this.method_116;
				Base.Trading.CondOrderExecuted -= this.method_117;
				Base.Trading.CondOrderUpdated -= this.method_118;
				Base.Trading.CondOrderStatusUpdated -= this.method_119;
				Base.Trading.OpenTransListUpdated -= this.method_120;
				Base.UI.Form.StockShortSettingChanged -= this.method_121;
				Base.UI.CurrTradingSymbChanged -= this.method_128;
				this.dataGridViewMkt_0.Dispose();
				if (this.ParentSplitPanel != null)
				{
					this.ParentSplitPanel.Controls.Remove(this);
				}
				if (Base.Data.SymbDataSets != null)
				{
					foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
					{
						symbDataSet.CurrHisDataChanged -= this.method_101;
						symbDataSet.CurrDateChanged -= this.method_102;
					}
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001A2E RID: 6702 RVA: 0x0000AFCD File Offset: 0x000091CD
		public void method_129()
		{
			if (this.dataGridViewOpenTrans_0 != null)
			{
				if (this.dataGridViewOpenTrans_0.DataSource != Base.Trading.CurrOpenTransList)
				{
					this.dataGridViewOpenTrans_0.method_5();
				}
				this.dataGridViewOpenTrans_0.Refresh();
			}
		}

		// Token: 0x06001A2F RID: 6703 RVA: 0x0000B001 File Offset: 0x00009201
		public void method_130()
		{
			if (this.symbFilterPanel_0 != null)
			{
				this.symbFilterPanel_0.method_26();
			}
		}

		// Token: 0x06001A30 RID: 6704 RVA: 0x000B6DAC File Offset: 0x000B4FAC
		public void method_131()
		{
			Control12 control = this.method_132();
			if (control != null)
			{
				control.method_32();
			}
			this.method_47();
		}

		// Token: 0x06001A31 RID: 6705 RVA: 0x000B6DD4 File Offset: 0x000B4FD4
		private Control12 method_132()
		{
			Control12 result = null;
			if (this.tabControlPanel_14.Controls.Count > 0)
			{
				result = (this.tabControlPanel_14.Controls[0] as Control12);
			}
			return result;
		}

		// Token: 0x06001A32 RID: 6706 RVA: 0x000B6E14 File Offset: 0x000B5014
		public void method_133()
		{
			if (this.IsInVideoPage)
			{
				Control12 control = this.method_132();
				if (control != null)
				{
					control.method_23();
				}
			}
		}

		// Token: 0x06001A33 RID: 6707 RVA: 0x000B6E3C File Offset: 0x000B503C
		private void method_134(bool bool_2)
		{
			Control12 control = this.method_132();
			if (control != null)
			{
				control.method_25(bool_2);
			}
		}

		// Token: 0x06001A34 RID: 6708 RVA: 0x000B6E5C File Offset: 0x000B505C
		public void method_135()
		{
			Control12 control = this.method_132();
			if (control != null)
			{
				control.method_24();
			}
		}

		// Token: 0x06001A35 RID: 6709 RVA: 0x0000B018 File Offset: 0x00009218
		public void method_136(string string_1)
		{
			this.method_140(this.tabControl_1, string_1);
		}

		// Token: 0x06001A36 RID: 6710 RVA: 0x0000B029 File Offset: 0x00009229
		public void method_137(string string_1)
		{
			this.method_140(this.tabControl_0, string_1);
		}

		// Token: 0x06001A37 RID: 6711 RVA: 0x0000B03A File Offset: 0x0000923A
		public void method_138(string string_1)
		{
			this.method_140(this.tabControl_4, string_1);
		}

		// Token: 0x06001A38 RID: 6712 RVA: 0x0000B04B File Offset: 0x0000924B
		public void method_139(string string_1)
		{
			this.trdAnalysisPanel_0.method_40(string_1);
		}

		// Token: 0x06001A39 RID: 6713 RVA: 0x000B6E7C File Offset: 0x000B507C
		private void method_140(DevComponents.DotNetBar.TabControl tabControl_5, string string_1)
		{
			TransTabs.Class325 @class = new TransTabs.Class325();
			@class.string_0 = string_1;
			TabItem tabItem = tabControl_5.Tabs.Cast<TabItem>().ToList<TabItem>().SingleOrDefault(new Func<TabItem, bool>(@class.method_0));
			if (tabItem != null)
			{
				tabControl_5.SelectedTab = tabItem;
			}
		}

		// Token: 0x1700043D RID: 1085
		// (get) Token: 0x06001A3A RID: 6714 RVA: 0x000B6EC4 File Offset: 0x000B50C4
		public SplitterPanel SplitterPanel
		{
			get
			{
				return this.splitterPanel_1;
			}
		}

		// Token: 0x1700043E RID: 1086
		// (get) Token: 0x06001A3B RID: 6715 RVA: 0x000B6EDC File Offset: 0x000B50DC
		// (set) Token: 0x06001A3C RID: 6716 RVA: 0x0000B05B File Offset: 0x0000925B
		public TransTabCtrl ParentTransTabCtrl
		{
			get
			{
				return this.transTabCtrl_0;
			}
			set
			{
				this.transTabCtrl_0 = value;
			}
		}

		// Token: 0x1700043F RID: 1087
		// (get) Token: 0x06001A3D RID: 6717 RVA: 0x000B6EF4 File Offset: 0x000B50F4
		// (set) Token: 0x06001A3E RID: 6718 RVA: 0x0000B066 File Offset: 0x00009266
		public bool IsMaximized
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x17000440 RID: 1088
		// (get) Token: 0x06001A3F RID: 6719 RVA: 0x000B6F0C File Offset: 0x000B510C
		// (set) Token: 0x06001A40 RID: 6720 RVA: 0x0000B071 File Offset: 0x00009271
		public int OriContainerBarHeight
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x17000441 RID: 1089
		// (get) Token: 0x06001A41 RID: 6721 RVA: 0x000B6F24 File Offset: 0x000B5124
		// (set) Token: 0x06001A42 RID: 6722 RVA: 0x000B6F40 File Offset: 0x000B5140
		public int SelectedTabIndex
		{
			get
			{
				return this.tabControl_1.SelectedTabIndex;
			}
			set
			{
				try
				{
					if (this.tabControl_1 != null)
					{
						this.tabControl_1.SelectedTabIndex = value;
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x17000442 RID: 1090
		// (get) Token: 0x06001A43 RID: 6723 RVA: 0x000B6F7C File Offset: 0x000B517C
		// (set) Token: 0x06001A44 RID: 6724 RVA: 0x0000B07C File Offset: 0x0000927C
		public int MktSelectedTabIndex
		{
			get
			{
				return this.tabControl_0.SelectedTabIndex;
			}
			set
			{
				this.tabControl_0.SelectedTabIndex = value;
			}
		}

		// Token: 0x17000443 RID: 1091
		// (get) Token: 0x06001A45 RID: 6725 RVA: 0x000B6F98 File Offset: 0x000B5198
		// (set) Token: 0x06001A46 RID: 6726 RVA: 0x0000B08C File Offset: 0x0000928C
		public TabItem MktSelectedTab
		{
			get
			{
				return this.tabControl_0.SelectedTab;
			}
			set
			{
				this.tabControl_0.SelectedTab = value;
			}
		}

		// Token: 0x17000444 RID: 1092
		// (get) Token: 0x06001A47 RID: 6727 RVA: 0x000B6FB4 File Offset: 0x000B51B4
		// (set) Token: 0x06001A48 RID: 6728 RVA: 0x0000B09C File Offset: 0x0000929C
		public int TransSelectedTabIndex
		{
			get
			{
				return this.tabControl_4.SelectedTabIndex;
			}
			set
			{
				this.tabControl_4.SelectedTabIndex = value;
			}
		}

		// Token: 0x17000445 RID: 1093
		// (get) Token: 0x06001A49 RID: 6729 RVA: 0x000B6FD0 File Offset: 0x000B51D0
		// (set) Token: 0x06001A4A RID: 6730 RVA: 0x0000B0AC File Offset: 0x000092AC
		public TabItem TransSelectedTab
		{
			get
			{
				return this.tabControl_4.SelectedTab;
			}
			set
			{
				this.tabControl_4.SelectedTab = value;
			}
		}

		// Token: 0x17000446 RID: 1094
		// (get) Token: 0x06001A4B RID: 6731 RVA: 0x000B6FEC File Offset: 0x000B51EC
		// (set) Token: 0x06001A4C RID: 6732 RVA: 0x0000B0BC File Offset: 0x000092BC
		public TabItem SelectedTab
		{
			get
			{
				return this.tabControl_1.SelectedTab;
			}
			set
			{
				this.tabControl_1.SelectedTab = value;
			}
		}

		// Token: 0x17000447 RID: 1095
		// (get) Token: 0x06001A4D RID: 6733 RVA: 0x000B7008 File Offset: 0x000B5208
		public TabsCollection TabsCollection
		{
			get
			{
				return this.tabControl_1.Tabs;
			}
		}

		// Token: 0x17000448 RID: 1096
		// (get) Token: 0x06001A4E RID: 6734 RVA: 0x000B7024 File Offset: 0x000B5224
		public TabsCollection MarketTabsCollection
		{
			get
			{
				return this.tabControl_0.Tabs;
			}
		}

		// Token: 0x17000449 RID: 1097
		// (get) Token: 0x06001A4F RID: 6735 RVA: 0x000B7040 File Offset: 0x000B5240
		public TabsCollection TransTabsCollection
		{
			get
			{
				return this.tabControl_4.Tabs;
			}
		}

		// Token: 0x1700044A RID: 1098
		// (get) Token: 0x06001A50 RID: 6736 RVA: 0x000B705C File Offset: 0x000B525C
		// (set) Token: 0x06001A51 RID: 6737 RVA: 0x0000B0CC File Offset: 0x000092CC
		public SplitterPanel ParentSplitPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
			}
		}

		// Token: 0x1700044B RID: 1099
		// (get) Token: 0x06001A52 RID: 6738 RVA: 0x000B7074 File Offset: 0x000B5274
		// (set) Token: 0x06001A53 RID: 6739 RVA: 0x0000B0D7 File Offset: 0x000092D7
		public List<ShowMktSymb> CurrShowMktSymbList
		{
			get
			{
				return this.list_2;
			}
			private set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x1700044C RID: 1100
		// (get) Token: 0x06001A54 RID: 6740 RVA: 0x000B708C File Offset: 0x000B528C
		public List<KeyValuePair<DataGridView, int>> DataGridViewListWithCurrMktSymb
		{
			get
			{
				return this.list_3;
			}
		}

		// Token: 0x1700044D RID: 1101
		// (get) Token: 0x06001A55 RID: 6741 RVA: 0x000B70A4 File Offset: 0x000B52A4
		public bool IsDateTimePickersFocused
		{
			get
			{
				bool result;
				if (!this.dateTimePicker_0.Focused && !this.dateTimePicker_2.Focused && !this.dateTimePicker_1.Focused)
				{
					result = this.dateTimePicker_3.Focused;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x1700044E RID: 1102
		// (get) Token: 0x06001A56 RID: 6742 RVA: 0x000B70EC File Offset: 0x000B52EC
		public List<StkSymbol> ZixuanStkSymbList
		{
			get
			{
				List<StkSymbol> list = null;
				if (this.dataGridViewMkt_0.DataSource != null)
				{
					Collection<ShowMktSymb> collection = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
					list = new List<StkSymbol>();
					foreach (ShowMktSymb showMktSymb in collection)
					{
						list.Add(SymbMgr.smethod_3(showMktSymb.StkId));
					}
				}
				return list;
			}
		}

		// Token: 0x1700044F RID: 1103
		// (get) Token: 0x06001A57 RID: 6743 RVA: 0x000B7168 File Offset: 0x000B5368
		public bool IsInInputState
		{
			get
			{
				bool result;
				if (!this.numericUpDown_0.Focused && !this.numericUpDown_1.Focused && !this.control1_0.IsInInputState)
				{
					result = this.symbFilterPanel_0.IsInInputState;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000450 RID: 1104
		// (get) Token: 0x06001A58 RID: 6744 RVA: 0x000B71B0 File Offset: 0x000B53B0
		// (set) Token: 0x06001A59 RID: 6745 RVA: 0x0000B0E2 File Offset: 0x000092E2
		public bool IfAutoOpenClose
		{
			get
			{
				return this.checkBox_1.Checked;
			}
			set
			{
				if (this.checkBox_1.Checked != value)
				{
					this.checkBox_1.Checked = value;
				}
			}
		}

		// Token: 0x17000451 RID: 1105
		// (get) Token: 0x06001A5A RID: 6746 RVA: 0x000B71CC File Offset: 0x000B53CC
		// (set) Token: 0x06001A5B RID: 6747 RVA: 0x0000B100 File Offset: 0x00009300
		public bool IfFollowPrice
		{
			get
			{
				return this.checkBox_0.Checked;
			}
			set
			{
				if (this.checkBox_0.Checked != value)
				{
					this.checkBox_0.Checked = value;
				}
			}
		}

		// Token: 0x17000452 RID: 1106
		// (get) Token: 0x06001A5C RID: 6748 RVA: 0x000B71E8 File Offset: 0x000B53E8
		// (set) Token: 0x06001A5D RID: 6749 RVA: 0x0000B11E File Offset: 0x0000931E
		public decimal InputTradingUnits
		{
			get
			{
				return this.numericUpDown_0.Value;
			}
			set
			{
				if (this.numericUpDown_0.Value != value)
				{
					this.numericUpDown_0.Value = value;
				}
			}
		}

		// Token: 0x17000453 RID: 1107
		// (get) Token: 0x06001A5E RID: 6750 RVA: 0x000B7204 File Offset: 0x000B5404
		// (set) Token: 0x06001A5F RID: 6751 RVA: 0x0000B141 File Offset: 0x00009341
		public decimal InputTradingPrice
		{
			get
			{
				return this.numericUpDown_1.Value;
			}
			set
			{
				if (this.numericUpDown_1.Value != value)
				{
					this.numericUpDown_1.Value = value;
				}
			}
		}

		// Token: 0x17000454 RID: 1108
		// (get) Token: 0x06001A60 RID: 6752 RVA: 0x000B7220 File Offset: 0x000B5420
		public bool IsScrollableCtrlFocused
		{
			get
			{
				return this.control1_0.IsScrollableCtrlFocused;
			}
		}

		// Token: 0x17000455 RID: 1109
		// (get) Token: 0x06001A61 RID: 6753 RVA: 0x000B723C File Offset: 0x000B543C
		public bool IsInVideoPage
		{
			get
			{
				return this.tabControl_1.SelectedTab.Name.Contains(Class521.smethod_0(69353));
			}
		}

		// Token: 0x17000456 RID: 1110
		// (get) Token: 0x06001A62 RID: 6754 RVA: 0x000B726C File Offset: 0x000B546C
		public bool IsPlayingVideo
		{
			get
			{
				Control12 control = this.method_132();
				bool result;
				if (control != null && control.IsPlaying)
				{
					result = true;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000457 RID: 1111
		// (get) Token: 0x06001A63 RID: 6755 RVA: 0x000B7294 File Offset: 0x000B5494
		// (set) Token: 0x06001A64 RID: 6756 RVA: 0x0000B164 File Offset: 0x00009364
		public bool IsInitiating { get; private set; }

		// Token: 0x06001A65 RID: 6757 RVA: 0x0000B16F File Offset: 0x0000936F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001A66 RID: 6758 RVA: 0x000B72AC File Offset: 0x000B54AC
		private void method_141()
		{
			this.icontainer_0 = new Container();
			this.tabControl_1 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_9 = new TabControlPanel();
			this.panel_0 = new Panel();
			this.splitContainer_2 = new SplitContainer();
			this.tableLayoutPanel_0 = new TableLayoutPanel();
			this.button_5 = new Button();
			this.button_6 = new Button();
			this.button_7 = new Button();
			this.button_8 = new Button();
			this.radioButton_5 = new RadioButton();
			this.radioButton_6 = new RadioButton();
			this.tabControl_4 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_5 = new TabControlPanel();
			this.tabControl_3 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_7 = new TabControlPanel();
			this.splitContainer_1 = new SplitContainer();
			this.tableLayoutPanel_2 = new TableLayoutPanel();
			this.button_1 = new Button();
			this.label_3 = new Label();
			this.dateTimePicker_2 = new DateTimePicker();
			this.dateTimePicker_3 = new DateTimePicker();
			this.label_2 = new Label();
			this.tabItem_5 = new TabItem(this.icontainer_0);
			this.tabControlPanel_6 = new TabControlPanel();
			this.tabItem_4 = new TabItem(this.icontainer_0);
			this.tabItem_6 = new TabItem(this.icontainer_0);
			this.tabControlPanel_1 = new TabControlPanel();
			this.tabControl_2 = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_3 = new TabControlPanel();
			this.splitContainer_0 = new SplitContainer();
			this.tableLayoutPanel_1 = new TableLayoutPanel();
			this.button_0 = new Button();
			this.label_1 = new Label();
			this.dateTimePicker_0 = new DateTimePicker();
			this.dateTimePicker_1 = new DateTimePicker();
			this.label_0 = new Label();
			this.tabItem_1 = new TabItem(this.icontainer_0);
			this.tabControlPanel_2 = new TabControlPanel();
			this.tabItem_0 = new TabItem(this.icontainer_0);
			this.tabItem_2 = new TabItem(this.icontainer_0);
			this.tabControlPanel_4 = new TabControlPanel();
			this.tabItem_3 = new TabItem(this.icontainer_0);
			this.tabControlPanel_10 = new TabControlPanel();
			this.tabItem_10 = new TabItem(this.icontainer_0);
			this.expandableSplitter_0 = new ExpandableSplitter();
			this.panel_1 = new Panel();
			this.comboBox_0 = new ComboBox();
			this.label_10 = new Label();
			this.label_11 = new Label();
			this.label_8 = new Label();
			this.label_9 = new Label();
			this.checkBox_1 = new CheckBox();
			this.panel_3 = new Panel();
			this.class289_0 = new Class289();
			this.class289_1 = new Class289();
			this.checkBox_0 = new CheckBox();
			this.button_4 = new Button();
			this.button_3 = new Button();
			this.button_2 = new Button();
			this.panel_2 = new Panel();
			this.radioButton_4 = new RadioButton();
			this.radioButton_3 = new RadioButton();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.radioButton_2 = new RadioButton();
			this.numericUpDown_1 = new NumericUpDown();
			this.label_7 = new Label();
			this.label_4 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.label_5 = new Label();
			this.label_6 = new Label();
			this.tabItem_9 = new TabItem(this.icontainer_0);
			this.tabControlPanel_8 = new TabControlPanel();
			this.tabItem_8 = new TabItem(this.icontainer_0);
			this.tabControlPanel_0 = new TabControlPanel();
			this.tabItem_7 = new TabItem(this.icontainer_0);
			this.tabControlPanel_13 = new TabControlPanel();
			this.symbFilterPanel_0 = new SymbFilterPanel();
			this.tabItem_13 = new TabItem(this.icontainer_0);
			this.tabControlPanel_14 = new TabControlPanel();
			this.tabItem_14 = new TabItem(this.icontainer_0);
			this.tabControlPanel_12 = new TabControlPanel();
			this.control2_0 = new Control2();
			this.tabItem_12 = new TabItem(this.icontainer_0);
			this.tabControlPanel_11 = new TabControlPanel();
			this.control1_0 = new Control1();
			this.tabItem_11 = new TabItem(this.icontainer_0);
			((ISupportInitialize)this.tabControl_1).BeginInit();
			this.tabControl_1.SuspendLayout();
			this.tabControlPanel_9.SuspendLayout();
			this.panel_0.SuspendLayout();
			this.splitContainer_2.Panel1.SuspendLayout();
			this.splitContainer_2.Panel2.SuspendLayout();
			this.splitContainer_2.SuspendLayout();
			this.tableLayoutPanel_0.SuspendLayout();
			((ISupportInitialize)this.tabControl_4).BeginInit();
			this.tabControl_4.SuspendLayout();
			this.tabControlPanel_5.SuspendLayout();
			((ISupportInitialize)this.tabControl_3).BeginInit();
			this.tabControl_3.SuspendLayout();
			this.tabControlPanel_7.SuspendLayout();
			this.splitContainer_1.Panel1.SuspendLayout();
			this.splitContainer_1.SuspendLayout();
			this.tableLayoutPanel_2.SuspendLayout();
			this.tabControlPanel_1.SuspendLayout();
			((ISupportInitialize)this.tabControl_2).BeginInit();
			this.tabControl_2.SuspendLayout();
			this.tabControlPanel_3.SuspendLayout();
			this.splitContainer_0.Panel1.SuspendLayout();
			this.splitContainer_0.SuspendLayout();
			this.tableLayoutPanel_1.SuspendLayout();
			this.panel_1.SuspendLayout();
			this.panel_3.SuspendLayout();
			this.panel_2.SuspendLayout();
			((ISupportInitialize)this.numericUpDown_1).BeginInit();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			this.tabControlPanel_13.SuspendLayout();
			this.tabControlPanel_12.SuspendLayout();
			this.tabControlPanel_11.SuspendLayout();
			base.SuspendLayout();
			this.tabControl_1.CanReorderTabs = false;
			this.tabControl_1.CloseButtonOnTabsAlwaysDisplayed = false;
			this.tabControl_1.ColorScheme.TabItemBorder = Color.Empty;
			this.tabControl_1.ColorScheme.TabPanelBorder = Color.Empty;
			this.tabControl_1.Controls.Add(this.tabControlPanel_9);
			this.tabControl_1.Controls.Add(this.tabControlPanel_8);
			this.tabControl_1.Controls.Add(this.tabControlPanel_0);
			this.tabControl_1.Controls.Add(this.tabControlPanel_13);
			this.tabControl_1.Controls.Add(this.tabControlPanel_14);
			this.tabControl_1.Controls.Add(this.tabControlPanel_12);
			this.tabControl_1.Controls.Add(this.tabControlPanel_11);
			this.tabControl_1.Dock = DockStyle.Fill;
			this.tabControl_1.Location = new Point(0, 0);
			this.tabControl_1.Name = Class521.smethod_0(69362);
			this.tabControl_1.SelectedTabIndex = 0;
			this.tabControl_1.Size = new Size(1116, 528);
			this.tabControl_1.Style = eTabStripStyle.Metro;
			this.tabControl_1.TabIndex = 0;
			this.tabControl_1.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_1.Tabs.Add(this.tabItem_8);
			this.tabControl_1.Tabs.Add(this.tabItem_9);
			this.tabControl_1.Tabs.Add(this.tabItem_7);
			this.tabControl_1.Tabs.Add(this.tabItem_13);
			this.tabControl_1.Tabs.Add(this.tabItem_12);
			this.tabControl_1.Tabs.Add(this.tabItem_11);
			this.tabControl_1.Tabs.Add(this.tabItem_14);
			this.tabControlPanel_9.Controls.Add(this.panel_0);
			this.tabControlPanel_9.Controls.Add(this.expandableSplitter_0);
			this.tabControlPanel_9.Controls.Add(this.panel_1);
			this.tabControlPanel_9.Dock = DockStyle.Fill;
			this.tabControlPanel_9.Location = new Point(0, 30);
			this.tabControlPanel_9.Name = Class521.smethod_0(69387);
			this.tabControlPanel_9.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_9.Size = new Size(1116, 498);
			this.tabControlPanel_9.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_9.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_9.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_9.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_9.Style.GradientAngle = 90;
			this.tabControlPanel_9.TabIndex = 3;
			this.tabControlPanel_9.TabItem = this.tabItem_9;
			this.panel_0.Controls.Add(this.splitContainer_2);
			this.panel_0.Dock = DockStyle.Fill;
			this.panel_0.Location = new Point(317, 1);
			this.panel_0.Name = Class521.smethod_0(69420);
			this.panel_0.Size = new Size(798, 496);
			this.panel_0.TabIndex = 2;
			this.splitContainer_2.Dock = DockStyle.Fill;
			this.splitContainer_2.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_2.IsSplitterFixed = true;
			this.splitContainer_2.Location = new Point(0, 0);
			this.splitContainer_2.Name = Class521.smethod_0(69437);
			this.splitContainer_2.Orientation = Orientation.Horizontal;
			this.splitContainer_2.Panel1.Controls.Add(this.tableLayoutPanel_0);
			this.splitContainer_2.Panel2.Controls.Add(this.tabControl_4);
			this.splitContainer_2.Size = new Size(798, 496);
			this.splitContainer_2.SplitterDistance = 35;
			this.splitContainer_2.SplitterWidth = 1;
			this.splitContainer_2.TabIndex = 1;
			this.tableLayoutPanel_0.ColumnCount = 7;
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle());
			this.tableLayoutPanel_0.Controls.Add(this.button_5, 0, 0);
			this.tableLayoutPanel_0.Controls.Add(this.button_6, 1, 0);
			this.tableLayoutPanel_0.Controls.Add(this.button_7, 2, 0);
			this.tableLayoutPanel_0.Controls.Add(this.button_8, 3, 0);
			this.tableLayoutPanel_0.Controls.Add(this.radioButton_5, 5, 0);
			this.tableLayoutPanel_0.Controls.Add(this.radioButton_6, 4, 0);
			this.tableLayoutPanel_0.Dock = DockStyle.Fill;
			this.tableLayoutPanel_0.Location = new Point(0, 0);
			this.tableLayoutPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_0.Name = Class521.smethod_0(69478);
			this.tableLayoutPanel_0.RowCount = 1;
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Size = new Size(798, 35);
			this.tableLayoutPanel_0.TabIndex = 0;
			this.button_5.Anchor = AnchorStyles.None;
			this.button_5.BackColor = Color.Transparent;
			this.button_5.Location = new Point(6, 5);
			this.button_5.Margin = new System.Windows.Forms.Padding(0);
			this.button_5.Name = Class521.smethod_0(69511);
			this.button_5.Size = new Size(88, 25);
			this.button_5.TabIndex = 0;
			this.button_5.Text = Class521.smethod_0(68892);
			this.button_5.UseVisualStyleBackColor = false;
			this.button_6.Anchor = AnchorStyles.None;
			this.button_6.BackColor = Color.Transparent;
			this.button_6.Location = new Point(106, 4);
			this.button_6.Margin = new System.Windows.Forms.Padding(0);
			this.button_6.Name = Class521.smethod_0(69532);
			this.button_6.Size = new Size(88, 26);
			this.button_6.TabIndex = 1;
			this.button_6.Text = Class521.smethod_0(52717);
			this.button_6.UseVisualStyleBackColor = false;
			this.button_7.Anchor = AnchorStyles.None;
			this.button_7.BackColor = Color.Transparent;
			this.button_7.Location = new Point(206, 4);
			this.button_7.Margin = new System.Windows.Forms.Padding(0);
			this.button_7.Name = Class521.smethod_0(69553);
			this.button_7.Size = new Size(88, 26);
			this.button_7.TabIndex = 2;
			this.button_7.Text = Class521.smethod_0(68909);
			this.button_7.UseVisualStyleBackColor = false;
			this.button_8.Anchor = AnchorStyles.None;
			this.button_8.BackColor = Color.Transparent;
			this.button_8.Location = new Point(306, 4);
			this.button_8.Margin = new System.Windows.Forms.Padding(0);
			this.button_8.Name = Class521.smethod_0(69574);
			this.button_8.Size = new Size(88, 26);
			this.button_8.TabIndex = 3;
			this.button_8.Text = Class521.smethod_0(23756);
			this.button_8.UseVisualStyleBackColor = false;
			this.radioButton_5.Anchor = AnchorStyles.None;
			this.radioButton_5.AutoSize = true;
			this.radioButton_5.FlatStyle = FlatStyle.Popup;
			this.radioButton_5.Location = new Point(506, 8);
			this.radioButton_5.Margin = new System.Windows.Forms.Padding(0);
			this.radioButton_5.Name = Class521.smethod_0(69595);
			this.radioButton_5.Size = new Size(87, 19);
			this.radioButton_5.TabIndex = 1;
			this.radioButton_5.TabStop = true;
			this.radioButton_5.Text = Class521.smethod_0(69628);
			this.radioButton_5.UseVisualStyleBackColor = false;
			this.radioButton_6.Anchor = AnchorStyles.None;
			this.radioButton_6.AutoSize = true;
			this.radioButton_6.FlatStyle = FlatStyle.Popup;
			this.radioButton_6.Location = new Point(406, 8);
			this.radioButton_6.Margin = new System.Windows.Forms.Padding(0);
			this.radioButton_6.Name = Class521.smethod_0(69645);
			this.radioButton_6.Size = new Size(87, 19);
			this.radioButton_6.TabIndex = 0;
			this.radioButton_6.TabStop = true;
			this.radioButton_6.Text = Class521.smethod_0(69674);
			this.radioButton_6.UseVisualStyleBackColor = false;
			this.tabControl_4.BackColor = Color.FromArgb(194, 217, 247);
			this.tabControl_4.CanReorderTabs = true;
			this.tabControl_4.ColorScheme.TabBackground = SystemColors.Control;
			this.tabControl_4.ColorScheme.TabBackground2 = SystemColors.Control;
			this.tabControl_4.Controls.Add(this.tabControlPanel_5);
			this.tabControl_4.Controls.Add(this.tabControlPanel_1);
			this.tabControl_4.Controls.Add(this.tabControlPanel_4);
			this.tabControl_4.Controls.Add(this.tabControlPanel_10);
			this.tabControl_4.Dock = DockStyle.Fill;
			this.tabControl_4.Location = new Point(0, 0);
			this.tabControl_4.Name = Class521.smethod_0(69691);
			this.tabControl_4.SelectedTabIndex = 0;
			this.tabControl_4.Size = new Size(798, 460);
			this.tabControl_4.Style = eTabStripStyle.Flat;
			this.tabControl_4.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_4.TabIndex = 0;
			this.tabControl_4.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_4.Tabs.Add(this.tabItem_3);
			this.tabControl_4.Tabs.Add(this.tabItem_2);
			this.tabControl_4.Tabs.Add(this.tabItem_6);
			this.tabControl_4.Tabs.Add(this.tabItem_10);
			this.tabControl_4.Text = Class521.smethod_0(69712);
			this.tabControlPanel_5.AutoScroll = true;
			this.tabControlPanel_5.Controls.Add(this.tabControl_3);
			this.tabControlPanel_5.Dock = DockStyle.Fill;
			this.tabControlPanel_5.Location = new Point(0, 0);
			this.tabControlPanel_5.Name = Class521.smethod_0(69733);
			this.tabControlPanel_5.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_5.Size = new Size(798, 432);
			this.tabControlPanel_5.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_5.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_5.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_5.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_5.Style.GradientAngle = -90;
			this.tabControlPanel_5.TabIndex = 3;
			this.tabControlPanel_5.TabItem = this.tabItem_6;
			this.tabControl_3.CanReorderTabs = true;
			this.tabControl_3.ColorScheme.TabBorder = Color.Transparent;
			this.tabControl_3.ColorScheme.TabItemBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(215, 230, 249), 0f),
				new BackgroundColorBlend(Color.FromArgb(199, 220, 248), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(179, 208, 245), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(215, 229, 247), 1f)
			});
			this.tabControl_3.ColorScheme.TabItemBorder = Color.Transparent;
			this.tabControl_3.ColorScheme.TabItemHotBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(255, 253, 235), 0f),
				new BackgroundColorBlend(Color.FromArgb(255, 236, 168), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 218, 89), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 230, 141), 1f)
			});
			this.tabControl_3.ColorScheme.TabItemSelectedBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.White, 0f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 1f)
			});
			this.tabControl_3.ColorScheme.TabPanelBorder = Color.Transparent;
			this.tabControl_3.Controls.Add(this.tabControlPanel_7);
			this.tabControl_3.Controls.Add(this.tabControlPanel_6);
			this.tabControl_3.Dock = DockStyle.Fill;
			this.tabControl_3.Location = new Point(1, 1);
			this.tabControl_3.Name = Class521.smethod_0(69762);
			this.tabControl_3.SelectedTabIndex = 0;
			this.tabControl_3.Size = new Size(796, 430);
			this.tabControl_3.Style = eTabStripStyle.Office2007Dock;
			this.tabControl_3.TabAlignment = eTabStripAlignment.Right;
			this.tabControl_3.TabIndex = 1;
			this.tabControl_3.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_3.Tabs.Add(this.tabItem_4);
			this.tabControl_3.Tabs.Add(this.tabItem_5);
			this.tabControl_3.Text = Class521.smethod_0(20184);
			this.tabControl_3.ThemeAware = true;
			this.tabControlPanel_7.Controls.Add(this.splitContainer_1);
			this.tabControlPanel_7.Dock = DockStyle.Fill;
			this.tabControlPanel_7.Location = new Point(0, 0);
			this.tabControlPanel_7.Name = Class521.smethod_0(69787);
			this.tabControlPanel_7.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_7.Size = new Size(769, 430);
			this.tabControlPanel_7.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_7.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_7.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_7.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_7.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_7.Style.GradientAngle = 180;
			this.tabControlPanel_7.TabIndex = 2;
			this.tabControlPanel_7.TabItem = this.tabItem_5;
			this.splitContainer_1.Dock = DockStyle.Fill;
			this.splitContainer_1.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_1.IsSplitterFixed = true;
			this.splitContainer_1.Location = new Point(1, 1);
			this.splitContainer_1.Name = Class521.smethod_0(69824);
			this.splitContainer_1.Orientation = Orientation.Horizontal;
			this.splitContainer_1.Panel1.BackColor = Color.Transparent;
			this.splitContainer_1.Panel1.Controls.Add(this.tableLayoutPanel_2);
			this.splitContainer_1.Panel1MinSize = 28;
			this.splitContainer_1.Size = new Size(767, 428);
			this.splitContainer_1.SplitterDistance = 32;
			this.splitContainer_1.SplitterWidth = 1;
			this.splitContainer_1.TabIndex = 0;
			this.tableLayoutPanel_2.ColumnCount = 6;
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle());
			this.tableLayoutPanel_2.Controls.Add(this.button_1, 4, 0);
			this.tableLayoutPanel_2.Controls.Add(this.label_3, 0, 0);
			this.tableLayoutPanel_2.Controls.Add(this.dateTimePicker_2, 3, 0);
			this.tableLayoutPanel_2.Controls.Add(this.dateTimePicker_3, 1, 0);
			this.tableLayoutPanel_2.Controls.Add(this.label_2, 2, 0);
			this.tableLayoutPanel_2.Dock = DockStyle.Fill;
			this.tableLayoutPanel_2.Location = new Point(0, 0);
			this.tableLayoutPanel_2.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_2.Name = Class521.smethod_0(69861);
			this.tableLayoutPanel_2.RowCount = 1;
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_2.Size = new Size(767, 32);
			this.tableLayoutPanel_2.TabIndex = 5;
			this.button_1.Anchor = AnchorStyles.None;
			this.button_1.BackColor = Color.Transparent;
			this.button_1.Location = new Point(489, 3);
			this.button_1.Margin = new System.Windows.Forms.Padding(0);
			this.button_1.Name = Class521.smethod_0(69898);
			this.button_1.Size = new Size(62, 25);
			this.button_1.TabIndex = 4;
			this.button_1.Text = Class521.smethod_0(69919);
			this.button_1.UseVisualStyleBackColor = false;
			this.label_3.Anchor = AnchorStyles.Right;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(18, 8);
			this.label_3.Margin = new System.Windows.Forms.Padding(0);
			this.label_3.Name = Class521.smethod_0(69928);
			this.label_3.Size = new Size(52, 15);
			this.label_3.TabIndex = 1;
			this.label_3.Text = Class521.smethod_0(69941);
			this.label_3.BackColor = Color.Transparent;
			this.dateTimePicker_2.Anchor = AnchorStyles.None;
			this.dateTimePicker_2.Location = new Point(313, 3);
			this.dateTimePicker_2.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_2.Name = Class521.smethod_0(69954);
			this.dateTimePicker_2.Size = new Size(163, 25);
			this.dateTimePicker_2.TabIndex = 3;
			this.dateTimePicker_3.Anchor = AnchorStyles.None;
			this.dateTimePicker_3.Location = new Point(73, 3);
			this.dateTimePicker_3.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_3.Name = Class521.smethod_0(69987);
			this.dateTimePicker_3.Size = new Size(163, 25);
			this.dateTimePicker_3.TabIndex = 0;
			this.label_2.Anchor = AnchorStyles.Right;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(258, 8);
			this.label_2.Margin = new System.Windows.Forms.Padding(0);
			this.label_2.Name = Class521.smethod_0(5893);
			this.label_2.Size = new Size(52, 15);
			this.label_2.TabIndex = 2;
			this.label_2.Text = Class521.smethod_0(70024);
			this.label_2.BackColor = Color.Transparent;
			this.tabItem_5.AttachedControl = this.tabControlPanel_7;
			this.tabItem_5.Name = Class521.smethod_0(70037);
			this.tabItem_5.Text = Class521.smethod_0(70058);
			this.tabControlPanel_6.Dock = DockStyle.Fill;
			this.tabControlPanel_6.Location = new Point(0, 0);
			this.tabControlPanel_6.Name = Class521.smethod_0(70075);
			this.tabControlPanel_6.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_6.Size = new Size(769, 430);
			this.tabControlPanel_6.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_6.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_6.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_6.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_6.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_6.Style.GradientAngle = 180;
			this.tabControlPanel_6.TabIndex = 1;
			this.tabControlPanel_6.TabItem = this.tabItem_4;
			this.tabItem_4.AttachedControl = this.tabControlPanel_6;
			this.tabItem_4.Name = Class521.smethod_0(70112);
			this.tabItem_4.Text = Class521.smethod_0(69040);
			this.tabItem_6.AttachedControl = this.tabControlPanel_5;
			this.tabItem_6.Name = Class521.smethod_0(70133);
			this.tabItem_6.Text = Class521.smethod_0(68960);
			this.tabControlPanel_1.AutoScroll = true;
			this.tabControlPanel_1.Controls.Add(this.tabControl_2);
			this.tabControlPanel_1.Dock = DockStyle.Fill;
			this.tabControlPanel_1.Location = new Point(0, 0);
			this.tabControlPanel_1.Name = Class521.smethod_0(70150);
			this.tabControlPanel_1.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_1.Size = new Size(798, 432);
			this.tabControlPanel_1.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_1.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_1.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_1.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_1.Style.GradientAngle = -90;
			this.tabControlPanel_1.TabIndex = 2;
			this.tabControlPanel_1.TabItem = this.tabItem_2;
			this.tabControl_2.CanReorderTabs = true;
			this.tabControl_2.ColorScheme.TabBorder = Color.Transparent;
			this.tabControl_2.ColorScheme.TabItemBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(215, 230, 249), 0f),
				new BackgroundColorBlend(Color.FromArgb(199, 220, 248), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(179, 208, 245), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(215, 229, 247), 1f)
			});
			this.tabControl_2.ColorScheme.TabItemBorder = Color.Transparent;
			this.tabControl_2.ColorScheme.TabItemHotBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(255, 253, 235), 0f),
				new BackgroundColorBlend(Color.FromArgb(255, 236, 168), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 218, 89), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 230, 141), 1f)
			});
			this.tabControl_2.ColorScheme.TabItemSelectedBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.White, 0f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 1f)
			});
			this.tabControl_2.ColorScheme.TabPanelBorder = Color.Transparent;
			this.tabControl_2.Controls.Add(this.tabControlPanel_3);
			this.tabControl_2.Controls.Add(this.tabControlPanel_2);
			this.tabControl_2.Dock = DockStyle.Fill;
			this.tabControl_2.Location = new Point(1, 1);
			this.tabControl_2.Name = Class521.smethod_0(70179);
			this.tabControl_2.SelectedTabIndex = 0;
			this.tabControl_2.Size = new Size(796, 430);
			this.tabControl_2.Style = eTabStripStyle.Office2007Dock;
			this.tabControl_2.TabAlignment = eTabStripAlignment.Right;
			this.tabControl_2.TabIndex = 0;
			this.tabControl_2.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_2.Tabs.Add(this.tabItem_0);
			this.tabControl_2.Tabs.Add(this.tabItem_1);
			this.tabControl_2.Text = Class521.smethod_0(20184);
			this.tabControl_2.ThemeAware = true;
			this.tabControlPanel_3.Controls.Add(this.splitContainer_0);
			this.tabControlPanel_3.Dock = DockStyle.Fill;
			this.tabControlPanel_3.Location = new Point(0, 0);
			this.tabControlPanel_3.Name = Class521.smethod_0(70204);
			this.tabControlPanel_3.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_3.Size = new Size(769, 430);
			this.tabControlPanel_3.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_3.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_3.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_3.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_3.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_3.Style.GradientAngle = 180;
			this.tabControlPanel_3.TabIndex = 2;
			this.tabControlPanel_3.TabItem = this.tabItem_1;
			this.splitContainer_0.Dock = DockStyle.Fill;
			this.splitContainer_0.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_0.IsSplitterFixed = true;
			this.splitContainer_0.Location = new Point(1, 1);
			this.splitContainer_0.Name = Class521.smethod_0(70241);
			this.splitContainer_0.Orientation = Orientation.Horizontal;
			this.splitContainer_0.Panel1.BackColor = Color.Transparent;
			this.splitContainer_0.Panel1.Controls.Add(this.tableLayoutPanel_1);
			this.splitContainer_0.Panel1MinSize = 28;
			this.splitContainer_0.Size = new Size(767, 428);
			this.splitContainer_0.SplitterDistance = 32;
			this.splitContainer_0.SplitterWidth = 1;
			this.splitContainer_0.TabIndex = 0;
			this.tableLayoutPanel_1.ColumnCount = 6;
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle());
			this.tableLayoutPanel_1.Controls.Add(this.button_0, 4, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_1, 0, 0);
			this.tableLayoutPanel_1.Controls.Add(this.dateTimePicker_0, 3, 0);
			this.tableLayoutPanel_1.Controls.Add(this.dateTimePicker_1, 1, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_0, 2, 0);
			this.tableLayoutPanel_1.Dock = DockStyle.Fill;
			this.tableLayoutPanel_1.Location = new Point(0, 0);
			this.tableLayoutPanel_1.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_1.Name = Class521.smethod_0(70278);
			this.tableLayoutPanel_1.RowCount = 1;
			this.tableLayoutPanel_1.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Size = new Size(767, 32);
			this.tableLayoutPanel_1.TabIndex = 0;
			this.button_0.Anchor = AnchorStyles.None;
			this.button_0.BackColor = Color.Transparent;
			this.button_0.Location = new Point(489, 3);
			this.button_0.Margin = new System.Windows.Forms.Padding(0);
			this.button_0.Name = Class521.smethod_0(70315);
			this.button_0.Size = new Size(62, 26);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(69919);
			this.button_0.UseVisualStyleBackColor = false;
			this.label_1.Anchor = AnchorStyles.Right;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(18, 8);
			this.label_1.Margin = new System.Windows.Forms.Padding(0);
			this.label_1.Name = Class521.smethod_0(70340);
			this.label_1.Size = new Size(52, 15);
			this.label_1.TabIndex = 1;
			this.label_1.Text = Class521.smethod_0(69941);
			this.label_1.BackColor = Color.Transparent;
			this.dateTimePicker_0.Anchor = AnchorStyles.None;
			this.dateTimePicker_0.Location = new Point(313, 3);
			this.dateTimePicker_0.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_0.Name = Class521.smethod_0(70361);
			this.dateTimePicker_0.Size = new Size(163, 25);
			this.dateTimePicker_0.TabIndex = 3;
			this.dateTimePicker_1.Anchor = AnchorStyles.None;
			this.dateTimePicker_1.Location = new Point(73, 3);
			this.dateTimePicker_1.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_1.Name = Class521.smethod_0(70398);
			this.dateTimePicker_1.Size = new Size(163, 25);
			this.dateTimePicker_1.TabIndex = 0;
			this.label_0.Anchor = AnchorStyles.Right;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(258, 8);
			this.label_0.Margin = new System.Windows.Forms.Padding(0);
			this.label_0.Name = Class521.smethod_0(70439);
			this.label_0.Size = new Size(52, 15);
			this.label_0.TabIndex = 2;
			this.label_0.Text = Class521.smethod_0(70024);
			this.label_0.BackColor = Color.Transparent;
			this.tabItem_1.AttachedControl = this.tabControlPanel_3;
			this.tabItem_1.Name = Class521.smethod_0(70460);
			this.tabItem_1.Text = Class521.smethod_0(70058);
			this.tabControlPanel_2.Dock = DockStyle.Fill;
			this.tabControlPanel_2.Location = new Point(0, 0);
			this.tabControlPanel_2.Name = Class521.smethod_0(70481);
			this.tabControlPanel_2.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_2.Size = new Size(769, 430);
			this.tabControlPanel_2.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_2.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_2.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_2.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_2.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_2.Style.GradientAngle = 180;
			this.tabControlPanel_2.TabIndex = 1;
			this.tabControlPanel_2.TabItem = this.tabItem_0;
			this.tabItem_0.AttachedControl = this.tabControlPanel_2;
			this.tabItem_0.Name = Class521.smethod_0(70518);
			this.tabItem_0.Text = Class521.smethod_0(69040);
			this.tabItem_2.AttachedControl = this.tabControlPanel_1;
			this.tabItem_2.Name = Class521.smethod_0(70539);
			this.tabItem_2.Text = Class521.smethod_0(68926);
			this.tabControlPanel_4.Dock = DockStyle.Fill;
			this.tabControlPanel_4.Location = new Point(0, 0);
			this.tabControlPanel_4.Name = Class521.smethod_0(70556);
			this.tabControlPanel_4.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_4.Size = new Size(798, 432);
			this.tabControlPanel_4.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_4.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_4.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_4.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_4.Style.GradientAngle = -90;
			this.tabControlPanel_4.TabIndex = 1;
			this.tabControlPanel_4.TabItem = this.tabItem_3;
			this.tabItem_3.AttachedControl = this.tabControlPanel_4;
			this.tabItem_3.Name = Class521.smethod_0(70589);
			this.tabItem_3.Text = Class521.smethod_0(70610);
			this.tabControlPanel_10.Dock = DockStyle.Fill;
			this.tabControlPanel_10.Location = new Point(0, 0);
			this.tabControlPanel_10.Name = Class521.smethod_0(70631);
			this.tabControlPanel_10.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_10.Size = new Size(798, 432);
			this.tabControlPanel_10.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_10.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_10.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_10.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_10.Style.GradientAngle = -90;
			this.tabControlPanel_10.TabIndex = 1;
			this.tabControlPanel_10.TabItem = this.tabItem_10;
			this.tabItem_10.AttachedControl = this.tabControlPanel_10;
			this.tabItem_10.Name = Class521.smethod_0(70668);
			this.tabItem_10.Text = Class521.smethod_0(68977);
			this.expandableSplitter_0.BackColor = SystemColors.ControlLight;
			this.expandableSplitter_0.BackColor2 = Color.Empty;
			this.expandableSplitter_0.BackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.BackColorSchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.ExpandableControl = this.panel_1;
			this.expandableSplitter_0.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expandableSplitter_0.HotBackColor2 = Color.Empty;
			this.expandableSplitter_0.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expandableSplitter_0.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.Location = new Point(313, 1);
			this.expandableSplitter_0.Name = Class521.smethod_0(70689);
			this.expandableSplitter_0.Size = new Size(4, 496);
			this.expandableSplitter_0.Style = eSplitterStyle.Mozilla;
			this.expandableSplitter_0.TabIndex = 1;
			this.expandableSplitter_0.TabStop = false;
			this.panel_1.Controls.Add(this.comboBox_0);
			this.panel_1.Controls.Add(this.label_10);
			this.panel_1.Controls.Add(this.label_11);
			this.panel_1.Controls.Add(this.label_8);
			this.panel_1.Controls.Add(this.label_9);
			this.panel_1.Controls.Add(this.checkBox_1);
			this.panel_1.Controls.Add(this.panel_3);
			this.panel_1.Controls.Add(this.checkBox_0);
			this.panel_1.Controls.Add(this.button_4);
			this.panel_1.Controls.Add(this.button_3);
			this.panel_1.Controls.Add(this.button_2);
			this.panel_1.Controls.Add(this.panel_2);
			this.panel_1.Controls.Add(this.numericUpDown_1);
			this.panel_1.Controls.Add(this.label_7);
			this.panel_1.Controls.Add(this.label_4);
			this.panel_1.Controls.Add(this.numericUpDown_0);
			this.panel_1.Controls.Add(this.label_5);
			this.panel_1.Controls.Add(this.label_6);
			this.panel_1.Dock = DockStyle.Left;
			this.panel_1.Location = new Point(1, 1);
			this.panel_1.Name = Class521.smethod_0(70714);
			this.panel_1.Size = new Size(312, 496);
			this.panel_1.TabIndex = 0;
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(58, 16);
			this.comboBox_0.MaxDropDownItems = 80;
			this.comboBox_0.Name = Class521.smethod_0(70735);
			this.comboBox_0.Size = new Size(152, 23);
			this.comboBox_0.TabIndex = 21;
			this.label_10.AutoSize = true;
			this.label_10.Font = new Font(Class521.smethod_0(24023), 8f);
			this.label_10.Location = new Point(246, 224);
			this.label_10.Name = Class521.smethod_0(70764);
			this.label_10.Size = new Size(36, 17);
			this.label_10.TabIndex = 20;
			this.label_10.Text = Class521.smethod_0(70785);
			this.label_10.Visible = false;
			this.label_11.AutoSize = true;
			this.label_11.Location = new Point(199, 225);
			this.label_11.Name = Class521.smethod_0(70790);
			this.label_11.Size = new Size(52, 15);
			this.label_11.TabIndex = 19;
			this.label_11.Text = Class521.smethod_0(70807);
			this.label_11.Visible = false;
			this.label_8.AutoSize = true;
			this.label_8.Font = new Font(Class521.smethod_0(24023), 8f);
			this.label_8.Location = new Point(91, 224);
			this.label_8.Name = Class521.smethod_0(70820);
			this.label_8.Size = new Size(56, 17);
			this.label_8.TabIndex = 18;
			this.label_8.Text = Class521.smethod_0(70841);
			this.label_8.Visible = false;
			this.label_9.AutoSize = true;
			this.label_9.Location = new Point(12, 225);
			this.label_9.Name = Class521.smethod_0(70850);
			this.label_9.Size = new Size(82, 15);
			this.label_9.TabIndex = 17;
			this.label_9.Text = Class521.smethod_0(70863);
			this.label_9.Visible = false;
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.FlatStyle = FlatStyle.Popup;
			this.checkBox_1.Location = new Point(77, 174);
			this.checkBox_1.Name = Class521.smethod_0(70884);
			this.checkBox_1.Size = new Size(56, 19);
			this.checkBox_1.TabIndex = 15;
			this.checkBox_1.Text = Class521.smethod_0(70913);
			this.checkBox_1.UseVisualStyleBackColor = false;
			this.panel_3.Controls.Add(this.class289_0);
			this.panel_3.Controls.Add(this.class289_1);
			this.panel_3.Location = new Point(11, 159);
			this.panel_3.Name = Class521.smethod_0(70922);
			this.panel_3.Size = new Size(64, 48);
			this.panel_3.TabIndex = 14;
			this.class289_0.AutoSize = true;
			this.class289_0.FlatStyle = FlatStyle.Popup;
			this.class289_0.Location = new Point(4, 27);
			this.class289_0.Name = Class521.smethod_0(70943);
			this.class289_0.Size = new Size(57, 19);
			this.class289_0.TabIndex = 1;
			this.class289_0.TabStop = true;
			this.class289_0.Text = Class521.smethod_0(25073);
			this.class289_0.UseVisualStyleBackColor = false;
			this.class289_1.AutoSize = true;
			this.class289_1.FlatStyle = FlatStyle.Popup;
			this.class289_1.Location = new Point(4, 5);
			this.class289_1.Name = Class521.smethod_0(70964);
			this.class289_1.Size = new Size(57, 19);
			this.class289_1.TabIndex = 0;
			this.class289_1.TabStop = true;
			this.class289_1.Text = Class521.smethod_0(25064);
			this.class289_1.UseVisualStyleBackColor = false;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.BackColor = Color.Transparent;
			this.checkBox_0.FlatStyle = FlatStyle.Popup;
			this.checkBox_0.Location = new Point(228, 52);
			this.checkBox_0.Name = Class521.smethod_0(70985);
			this.checkBox_0.Size = new Size(56, 19);
			this.checkBox_0.TabIndex = 13;
			this.checkBox_0.Text = Class521.smethod_0(71010);
			this.checkBox_0.UseVisualStyleBackColor = false;
			this.button_4.BackColor = Color.Transparent;
			this.button_4.Location = new Point(216, 14);
			this.button_4.Name = Class521.smethod_0(71019);
			this.button_4.Size = new Size(80, 25);
			this.button_4.TabIndex = 11;
			this.button_4.Text = Class521.smethod_0(68977);
			this.button_4.UseVisualStyleBackColor = false;
			this.button_3.BackColor = Color.Transparent;
			this.button_3.Font = new Font(Class521.smethod_0(67439), 13f);
			this.button_3.ForeColor = Color.Green;
			this.button_3.Location = new Point(222, 159);
			this.button_3.Name = Class521.smethod_0(71040);
			this.button_3.Size = new Size(76, 48);
			this.button_3.TabIndex = 9;
			this.button_3.Text = Class521.smethod_0(25000);
			this.button_3.UseVisualStyleBackColor = false;
			this.button_2.BackColor = Color.Transparent;
			this.button_2.Font = new Font(Class521.smethod_0(67439), 13f);
			this.button_2.ForeColor = Color.Red;
			this.button_2.Location = new Point(141, 159);
			this.button_2.Name = Class521.smethod_0(71061);
			this.button_2.Size = new Size(76, 48);
			this.button_2.TabIndex = 8;
			this.button_2.Text = Class521.smethod_0(24991);
			this.button_2.UseVisualStyleBackColor = false;
			this.panel_2.Controls.Add(this.radioButton_4);
			this.panel_2.Controls.Add(this.radioButton_3);
			this.panel_2.Controls.Add(this.radioButton_0);
			this.panel_2.Controls.Add(this.radioButton_1);
			this.panel_2.Controls.Add(this.radioButton_2);
			this.panel_2.Font = new Font(Class521.smethod_0(71082), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.panel_2.Location = new Point(14, 111);
			this.panel_2.Name = Class521.smethod_0(71099);
			this.panel_2.Size = new Size(285, 28);
			this.panel_2.TabIndex = 7;
			this.radioButton_4.AutoSize = true;
			this.radioButton_4.BackColor = Color.Transparent;
			this.radioButton_4.FlatStyle = FlatStyle.Popup;
			this.radioButton_4.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioButton_4.Location = new Point(218, 5);
			this.radioButton_4.Name = Class521.smethod_0(71128);
			this.radioButton_4.Size = new Size(64, 21);
			this.radioButton_4.TabIndex = 9;
			this.radioButton_4.TabStop = true;
			this.radioButton_4.Text = Class521.smethod_0(71153);
			this.radioButton_4.UseVisualStyleBackColor = false;
			this.radioButton_3.AutoSize = true;
			this.radioButton_3.BackColor = Color.Transparent;
			this.radioButton_3.FlatStyle = FlatStyle.Popup;
			this.radioButton_3.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioButton_3.Location = new Point(165, 5);
			this.radioButton_3.Name = Class521.smethod_0(71162);
			this.radioButton_3.Size = new Size(56, 21);
			this.radioButton_3.TabIndex = 8;
			this.radioButton_3.TabStop = true;
			this.radioButton_3.Text = Class521.smethod_0(71183);
			this.radioButton_3.UseVisualStyleBackColor = false;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.BackColor = Color.Transparent;
			this.radioButton_0.FlatStyle = FlatStyle.Popup;
			this.radioButton_0.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioButton_0.Location = new Point(111, 5);
			this.radioButton_0.Name = Class521.smethod_0(71188);
			this.radioButton_0.Size = new Size(56, 21);
			this.radioButton_0.TabIndex = 2;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(71209);
			this.radioButton_0.UseVisualStyleBackColor = false;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.BackColor = Color.Transparent;
			this.radioButton_1.FlatStyle = FlatStyle.Popup;
			this.radioButton_1.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioButton_1.Location = new Point(55, 5);
			this.radioButton_1.Name = Class521.smethod_0(71214);
			this.radioButton_1.Size = new Size(56, 21);
			this.radioButton_1.TabIndex = 1;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(70785);
			this.radioButton_1.UseVisualStyleBackColor = false;
			this.radioButton_2.AutoSize = true;
			this.radioButton_2.BackColor = Color.Transparent;
			this.radioButton_2.FlatStyle = FlatStyle.Popup;
			this.radioButton_2.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioButton_2.Location = new Point(1, 5);
			this.radioButton_2.Name = Class521.smethod_0(71235);
			this.radioButton_2.Size = new Size(56, 21);
			this.radioButton_2.TabIndex = 0;
			this.radioButton_2.TabStop = true;
			this.radioButton_2.Text = Class521.smethod_0(71256);
			this.radioButton_2.UseVisualStyleBackColor = false;
			this.numericUpDown_1.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_1.Location = new Point(58, 48);
			this.numericUpDown_1.Name = Class521.smethod_0(71261);
			this.numericUpDown_1.Size = new Size(152, 24);
			this.numericUpDown_1.TabIndex = 6;
			this.label_7.AutoSize = true;
			this.label_7.BackColor = Color.Transparent;
			this.label_7.Location = new Point(11, 53);
			this.label_7.Name = Class521.smethod_0(71290);
			this.label_7.Size = new Size(37, 15);
			this.label_7.TabIndex = 5;
			this.label_7.Text = Class521.smethod_0(24982);
			this.label_4.AutoSize = true;
			this.label_4.BackColor = Color.Transparent;
			this.label_4.Font = new Font(Class521.smethod_0(24023), 8f);
			this.label_4.Location = new Point(225, 84);
			this.label_4.Name = Class521.smethod_0(71307);
			this.label_4.Size = new Size(40, 17);
			this.label_4.TabIndex = 4;
			this.label_4.Text = Class521.smethod_0(71332);
			this.numericUpDown_0.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_0.Location = new Point(58, 81);
			this.numericUpDown_0.Name = Class521.smethod_0(71341);
			this.numericUpDown_0.Size = new Size(152, 24);
			this.numericUpDown_0.TabIndex = 3;
			this.label_5.AutoSize = true;
			this.label_5.BackColor = Color.Transparent;
			this.label_5.Location = new Point(11, 85);
			this.label_5.Name = Class521.smethod_0(71370);
			this.label_5.Size = new Size(37, 15);
			this.label_5.TabIndex = 1;
			this.label_5.Text = Class521.smethod_0(52539);
			this.label_6.AutoSize = true;
			this.label_6.BackColor = Color.Transparent;
			this.label_6.Location = new Point(11, 20);
			this.label_6.Name = Class521.smethod_0(71387);
			this.label_6.Size = new Size(37, 15);
			this.label_6.TabIndex = 0;
			this.label_6.Text = Class521.smethod_0(51577);
			this.tabItem_9.AttachedControl = this.tabControlPanel_9;
			this.tabItem_9.Name = Class521.smethod_0(71404);
			this.tabItem_9.Text = Class521.smethod_0(71429);
			this.tabControlPanel_8.Dock = DockStyle.Fill;
			this.tabControlPanel_8.Location = new Point(0, 30);
			this.tabControlPanel_8.Name = Class521.smethod_0(71446);
			this.tabControlPanel_8.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_8.Size = new Size(1116, 498);
			this.tabControlPanel_8.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_8.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_8.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_8.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_8.Style.GradientAngle = 90;
			this.tabControlPanel_8.TabIndex = 2;
			this.tabControlPanel_8.TabItem = this.tabItem_8;
			this.tabItem_8.AttachedControl = this.tabControlPanel_8;
			this.tabItem_8.Name = Class521.smethod_0(69265);
			this.tabItem_8.Text = Class521.smethod_0(71479);
			this.tabControlPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_0.Location = new Point(0, 30);
			this.tabControlPanel_0.Name = Class521.smethod_0(71496);
			this.tabControlPanel_0.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_0.Size = new Size(1116, 498);
			this.tabControlPanel_0.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_0.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_0.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_0.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_0.Style.GradientAngle = 90;
			this.tabControlPanel_0.TabIndex = 1;
			this.tabControlPanel_0.TabItem = this.tabItem_7;
			this.tabItem_7.AttachedControl = this.tabControlPanel_0;
			this.tabItem_7.Name = Class521.smethod_0(69307);
			this.tabItem_7.Text = Class521.smethod_0(68360);
			this.tabControlPanel_13.Controls.Add(this.symbFilterPanel_0);
			this.tabControlPanel_13.Dock = DockStyle.Fill;
			this.tabControlPanel_13.Location = new Point(0, 30);
			this.tabControlPanel_13.Name = Class521.smethod_0(71533);
			this.tabControlPanel_13.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_13.Size = new Size(1116, 498);
			this.tabControlPanel_13.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_13.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_13.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_13.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_13.Style.GradientAngle = 90;
			this.tabControlPanel_13.TabIndex = 6;
			this.tabControlPanel_13.TabItem = this.tabItem_13;
			this.symbFilterPanel_0.CurrUserCfgCondGrpName = null;
			this.symbFilterPanel_0.Dock = DockStyle.Fill;
			this.symbFilterPanel_0.Location = new Point(1, 1);
			this.symbFilterPanel_0.Name = Class521.smethod_0(71566);
			this.symbFilterPanel_0.Size = new Size(1114, 496);
			this.symbFilterPanel_0.TabIndex = 0;
			this.tabItem_13.AttachedControl = this.tabControlPanel_13;
			this.tabItem_13.Name = Class521.smethod_0(69332);
			this.tabItem_13.Text = Class521.smethod_0(71587);
			this.tabControlPanel_14.Dock = DockStyle.Fill;
			this.tabControlPanel_14.Location = new Point(0, 30);
			this.tabControlPanel_14.Name = Class521.smethod_0(71604);
			this.tabControlPanel_14.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_14.Size = new Size(1116, 498);
			this.tabControlPanel_14.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_14.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_14.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_14.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_14.Style.GradientAngle = 90;
			this.tabControlPanel_14.TabIndex = 7;
			this.tabControlPanel_14.TabItem = this.tabItem_14;
			this.tabItem_14.AttachedControl = this.tabControlPanel_14;
			this.tabItem_14.Name = Class521.smethod_0(69290);
			this.tabItem_14.Text = Class521.smethod_0(71633);
			this.tabControlPanel_12.Controls.Add(this.control2_0);
			this.tabControlPanel_12.Dock = DockStyle.Fill;
			this.tabControlPanel_12.Location = new Point(0, 30);
			this.tabControlPanel_12.Name = Class521.smethod_0(71650);
			this.tabControlPanel_12.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_12.Size = new Size(1116, 498);
			this.tabControlPanel_12.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_12.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_12.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_12.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_12.Style.GradientAngle = 90;
			this.tabControlPanel_12.TabIndex = 5;
			this.tabControlPanel_12.TabItem = this.tabItem_12;
			this.control2_0.Dock = DockStyle.Fill;
			this.control2_0.Location = new Point(1, 1);
			this.control2_0.Name = Class521.smethod_0(71679);
			this.control2_0.Size = new Size(1114, 496);
			this.control2_0.TabIndex = 0;
			this.tabItem_12.AttachedControl = this.tabControlPanel_12;
			this.tabItem_12.Name = Class521.smethod_0(71700);
			this.tabItem_12.Text = Class521.smethod_0(71717);
			this.tabControlPanel_11.Controls.Add(this.control1_0);
			this.tabControlPanel_11.Dock = DockStyle.Fill;
			this.tabControlPanel_11.Location = new Point(0, 30);
			this.tabControlPanel_11.Name = Class521.smethod_0(71734);
			this.tabControlPanel_11.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_11.Size = new Size(1116, 498);
			this.tabControlPanel_11.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_11.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_11.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_11.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_11.Style.GradientAngle = 90;
			this.tabControlPanel_11.TabIndex = 4;
			this.tabControlPanel_11.TabItem = this.tabItem_11;
			this.control1_0.Dock = DockStyle.Fill;
			this.control1_0.Location = new Point(1, 1);
			this.control1_0.Name = Class521.smethod_0(71775);
			this.control1_0.Size = new Size(1114, 496);
			this.control1_0.TabIndex = 0;
			this.tabItem_11.AttachedControl = this.tabControlPanel_11;
			this.tabItem_11.Name = Class521.smethod_0(71792);
			this.tabItem_11.Text = Class521.smethod_0(71813);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tabControl_1);
			base.Name = Class521.smethod_0(71830);
			base.Size = new Size(1116, 528);
			((ISupportInitialize)this.tabControl_1).EndInit();
			this.tabControl_1.ResumeLayout(false);
			this.tabControlPanel_9.ResumeLayout(false);
			this.panel_0.ResumeLayout(false);
			this.splitContainer_2.Panel1.ResumeLayout(false);
			this.splitContainer_2.Panel2.ResumeLayout(false);
			this.splitContainer_2.ResumeLayout(false);
			this.tableLayoutPanel_0.ResumeLayout(false);
			this.tableLayoutPanel_0.PerformLayout();
			((ISupportInitialize)this.tabControl_4).EndInit();
			this.tabControl_4.ResumeLayout(false);
			this.tabControlPanel_5.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_3).EndInit();
			this.tabControl_3.ResumeLayout(false);
			this.tabControlPanel_7.ResumeLayout(false);
			this.splitContainer_1.Panel1.ResumeLayout(false);
			this.splitContainer_1.ResumeLayout(false);
			this.tableLayoutPanel_2.ResumeLayout(false);
			this.tableLayoutPanel_2.PerformLayout();
			this.tabControlPanel_1.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_2).EndInit();
			this.tabControl_2.ResumeLayout(false);
			this.tabControlPanel_3.ResumeLayout(false);
			this.splitContainer_0.Panel1.ResumeLayout(false);
			this.splitContainer_0.ResumeLayout(false);
			this.tableLayoutPanel_1.ResumeLayout(false);
			this.tableLayoutPanel_1.PerformLayout();
			this.panel_1.ResumeLayout(false);
			this.panel_1.PerformLayout();
			this.panel_3.ResumeLayout(false);
			this.panel_3.PerformLayout();
			this.panel_2.ResumeLayout(false);
			this.panel_2.PerformLayout();
			((ISupportInitialize)this.numericUpDown_1).EndInit();
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			this.tabControlPanel_13.ResumeLayout(false);
			this.tabControlPanel_12.ResumeLayout(false);
			this.tabControlPanel_11.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000CCA RID: 3274
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000CCB RID: 3275
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000CCC RID: 3276
		[CompilerGenerated]
		private EventHandler eventHandler_2;

		// Token: 0x04000CCD RID: 3277
		[CompilerGenerated]
		private EventHandler eventHandler_3;

		// Token: 0x04000CCE RID: 3278
		[CompilerGenerated]
		private Delegate22 delegate22_0;

		// Token: 0x04000CCF RID: 3279
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x04000CD0 RID: 3280
		private DataGridViewOpenTrans dataGridViewOpenTrans_0;

		// Token: 0x04000CD1 RID: 3281
		private DataGridViewHisTrans dataGridViewHisTrans_0;

		// Token: 0x04000CD2 RID: 3282
		private DataGridViewHisTrans dataGridViewHisTrans_1;

		// Token: 0x04000CD3 RID: 3283
		private Class293 class293_0;

		// Token: 0x04000CD4 RID: 3284
		private Class293 class293_1;

		// Token: 0x04000CD5 RID: 3285
		private Class294 class294_0;

		// Token: 0x04000CD6 RID: 3286
		private static string string_0 = TApp.UserAcctFolder + Class521.smethod_0(71843);

		// Token: 0x04000CD7 RID: 3287
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000CD8 RID: 3288
		private SplitterPanel splitterPanel_1;

		// Token: 0x04000CD9 RID: 3289
		private Class58 class58_0;

		// Token: 0x04000CDA RID: 3290
		private TransTabCtrl transTabCtrl_0;

		// Token: 0x04000CDB RID: 3291
		private bool bool_0;

		// Token: 0x04000CDC RID: 3292
		private int int_0;

		// Token: 0x04000CDD RID: 3293
		private DevComponents.DotNetBar.TabControl tabControl_0;

		// Token: 0x04000CDE RID: 3294
		private List<DataGridViewMkt> list_0;

		// Token: 0x04000CDF RID: 3295
		private FnDataApiWorker fnDataApiWorker_0;

		// Token: 0x04000CE0 RID: 3296
		private Font font_0;

		// Token: 0x04000CE1 RID: 3297
		private Font font_1;

		// Token: 0x04000CE2 RID: 3298
		private TrdAnalysisPanel trdAnalysisPanel_0;

		// Token: 0x04000CE3 RID: 3299
		private Class326 class326_0;

		// Token: 0x04000CE4 RID: 3300
		private List<ShowMktSymb> list_1;

		// Token: 0x04000CE5 RID: 3301
		private List<ShowMktSymb> list_2;

		// Token: 0x04000CE6 RID: 3302
		private List<KeyValuePair<DataGridView, int>> list_3;

		// Token: 0x04000CE7 RID: 3303
		private DataGridViewMkt dataGridViewMkt_0;

		// Token: 0x04000CE8 RID: 3304
		private Rectangle rectangle_0;

		// Token: 0x04000CE9 RID: 3305
		private int int_1;

		// Token: 0x04000CEA RID: 3306
		private int int_2 = -1;

		// Token: 0x04000CEB RID: 3307
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x04000CEC RID: 3308
		private IContainer icontainer_0;

		// Token: 0x04000CED RID: 3309
		private DevComponents.DotNetBar.TabControl tabControl_1;

		// Token: 0x04000CEE RID: 3310
		private TabControlPanel tabControlPanel_0;

		// Token: 0x04000CEF RID: 3311
		private TabControlPanel tabControlPanel_1;

		// Token: 0x04000CF0 RID: 3312
		private DevComponents.DotNetBar.TabControl tabControl_2;

		// Token: 0x04000CF1 RID: 3313
		private TabControlPanel tabControlPanel_2;

		// Token: 0x04000CF2 RID: 3314
		private TabItem tabItem_0;

		// Token: 0x04000CF3 RID: 3315
		private TabControlPanel tabControlPanel_3;

		// Token: 0x04000CF4 RID: 3316
		private SplitContainer splitContainer_0;

		// Token: 0x04000CF5 RID: 3317
		private Button button_0;

		// Token: 0x04000CF6 RID: 3318
		private DateTimePicker dateTimePicker_0;

		// Token: 0x04000CF7 RID: 3319
		private Label label_0;

		// Token: 0x04000CF8 RID: 3320
		private Label label_1;

		// Token: 0x04000CF9 RID: 3321
		private DateTimePicker dateTimePicker_1;

		// Token: 0x04000CFA RID: 3322
		private TabItem tabItem_1;

		// Token: 0x04000CFB RID: 3323
		private TabItem tabItem_2;

		// Token: 0x04000CFC RID: 3324
		private TabControlPanel tabControlPanel_4;

		// Token: 0x04000CFD RID: 3325
		private TabItem tabItem_3;

		// Token: 0x04000CFE RID: 3326
		private TabControlPanel tabControlPanel_5;

		// Token: 0x04000CFF RID: 3327
		private DevComponents.DotNetBar.TabControl tabControl_3;

		// Token: 0x04000D00 RID: 3328
		private TabControlPanel tabControlPanel_6;

		// Token: 0x04000D01 RID: 3329
		private TabItem tabItem_4;

		// Token: 0x04000D02 RID: 3330
		private TabControlPanel tabControlPanel_7;

		// Token: 0x04000D03 RID: 3331
		private SplitContainer splitContainer_1;

		// Token: 0x04000D04 RID: 3332
		private Button button_1;

		// Token: 0x04000D05 RID: 3333
		private DateTimePicker dateTimePicker_2;

		// Token: 0x04000D06 RID: 3334
		private Label label_2;

		// Token: 0x04000D07 RID: 3335
		private Label label_3;

		// Token: 0x04000D08 RID: 3336
		private DateTimePicker dateTimePicker_3;

		// Token: 0x04000D09 RID: 3337
		private TabItem tabItem_5;

		// Token: 0x04000D0A RID: 3338
		private TabItem tabItem_6;

		// Token: 0x04000D0B RID: 3339
		private TabItem tabItem_7;

		// Token: 0x04000D0C RID: 3340
		private TabControlPanel tabControlPanel_8;

		// Token: 0x04000D0D RID: 3341
		private TabItem tabItem_8;

		// Token: 0x04000D0E RID: 3342
		private TabControlPanel tabControlPanel_9;

		// Token: 0x04000D0F RID: 3343
		private TabItem tabItem_9;

		// Token: 0x04000D10 RID: 3344
		private Panel panel_0;

		// Token: 0x04000D11 RID: 3345
		private ExpandableSplitter expandableSplitter_0;

		// Token: 0x04000D12 RID: 3346
		private Panel panel_1;

		// Token: 0x04000D13 RID: 3347
		private Label label_4;

		// Token: 0x04000D14 RID: 3348
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000D15 RID: 3349
		private Label label_5;

		// Token: 0x04000D16 RID: 3350
		private Label label_6;

		// Token: 0x04000D17 RID: 3351
		private Panel panel_2;

		// Token: 0x04000D18 RID: 3352
		private RadioButton radioButton_0;

		// Token: 0x04000D19 RID: 3353
		private RadioButton radioButton_1;

		// Token: 0x04000D1A RID: 3354
		private RadioButton radioButton_2;

		// Token: 0x04000D1B RID: 3355
		private NumericUpDown numericUpDown_1;

		// Token: 0x04000D1C RID: 3356
		private Label label_7;

		// Token: 0x04000D1D RID: 3357
		private RadioButton radioButton_3;

		// Token: 0x04000D1E RID: 3358
		private RadioButton radioButton_4;

		// Token: 0x04000D1F RID: 3359
		private Button button_2;

		// Token: 0x04000D20 RID: 3360
		private Button button_3;

		// Token: 0x04000D21 RID: 3361
		private Button button_4;

		// Token: 0x04000D22 RID: 3362
		private CheckBox checkBox_0;

		// Token: 0x04000D23 RID: 3363
		private Panel panel_3;

		// Token: 0x04000D24 RID: 3364
		private Class289 class289_0;

		// Token: 0x04000D25 RID: 3365
		private Class289 class289_1;

		// Token: 0x04000D26 RID: 3366
		private CheckBox checkBox_1;

		// Token: 0x04000D27 RID: 3367
		private DevComponents.DotNetBar.TabControl tabControl_4;

		// Token: 0x04000D28 RID: 3368
		private TabControlPanel tabControlPanel_10;

		// Token: 0x04000D29 RID: 3369
		private TabItem tabItem_10;

		// Token: 0x04000D2A RID: 3370
		private Label label_8;

		// Token: 0x04000D2B RID: 3371
		private Label label_9;

		// Token: 0x04000D2C RID: 3372
		private Label label_10;

		// Token: 0x04000D2D RID: 3373
		private Label label_11;

		// Token: 0x04000D2E RID: 3374
		private ComboBox comboBox_0;

		// Token: 0x04000D2F RID: 3375
		private SplitContainer splitContainer_2;

		// Token: 0x04000D30 RID: 3376
		private Button button_5;

		// Token: 0x04000D31 RID: 3377
		private Button button_6;

		// Token: 0x04000D32 RID: 3378
		private Button button_7;

		// Token: 0x04000D33 RID: 3379
		private Button button_8;

		// Token: 0x04000D34 RID: 3380
		private RadioButton radioButton_5;

		// Token: 0x04000D35 RID: 3381
		private RadioButton radioButton_6;

		// Token: 0x04000D36 RID: 3382
		private TabControlPanel tabControlPanel_11;

		// Token: 0x04000D37 RID: 3383
		private TabItem tabItem_11;

		// Token: 0x04000D38 RID: 3384
		private TabControlPanel tabControlPanel_12;

		// Token: 0x04000D39 RID: 3385
		private TabItem tabItem_12;

		// Token: 0x04000D3A RID: 3386
		private TabControlPanel tabControlPanel_13;

		// Token: 0x04000D3B RID: 3387
		private TabItem tabItem_13;

		// Token: 0x04000D3C RID: 3388
		private Control2 control2_0;

		// Token: 0x04000D3D RID: 3389
		private SymbFilterPanel symbFilterPanel_0;

		// Token: 0x04000D3E RID: 3390
		private Control1 control1_0;

		// Token: 0x04000D3F RID: 3391
		private TabControlPanel tabControlPanel_14;

		// Token: 0x04000D40 RID: 3392
		private TabItem tabItem_14;

		// Token: 0x04000D41 RID: 3393
		private TableLayoutPanel tableLayoutPanel_0;

		// Token: 0x04000D42 RID: 3394
		private TableLayoutPanel tableLayoutPanel_1;

		// Token: 0x04000D43 RID: 3395
		private TableLayoutPanel tableLayoutPanel_2;

		// Token: 0x02000256 RID: 598
		[CompilerGenerated]
		private sealed class Class321
		{
			// Token: 0x06001A69 RID: 6761 RVA: 0x000BBCE0 File Offset: 0x000B9EE0
			internal void method_0()
			{
				this.dataGridViewMkt_0.smethod_0();
				this.dataGridViewMkt_0.SuspendLayout();
				this.dataGridViewMkt_0.method_8(this.sortableBindingList_0);
				if (!string.IsNullOrEmpty(this.string_0))
				{
					this.dataGridViewMkt_0.Sort(this.dataGridViewMkt_0.Columns[this.string_0], this.listSortDirection_0);
				}
				if (this.int_0 > -1 && this.int_0 < this.sortableBindingList_0.Count)
				{
					this.dataGridViewMkt_0.Rows[this.int_0].Selected = true;
				}
				this.dataGridViewMkt_0.ResumeLayout();
				this.dataGridViewMkt_0.smethod_1();
			}

			// Token: 0x04000D44 RID: 3396
			public DataGridViewMkt dataGridViewMkt_0;

			// Token: 0x04000D45 RID: 3397
			public SortableBindingList<ShowMktSymb> sortableBindingList_0;

			// Token: 0x04000D46 RID: 3398
			public string string_0;

			// Token: 0x04000D47 RID: 3399
			public ListSortDirection listSortDirection_0;

			// Token: 0x04000D48 RID: 3400
			public int int_0;
		}

		// Token: 0x02000258 RID: 600
		[CompilerGenerated]
		private sealed class Class322
		{
			// Token: 0x06001A83 RID: 6787 RVA: 0x000BBF34 File Offset: 0x000BA134
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ID == this.showMktSymb_0.StkId;
			}

			// Token: 0x04000D60 RID: 3424
			public ShowMktSymb showMktSymb_0;
		}

		// Token: 0x02000259 RID: 601
		[CompilerGenerated]
		private sealed class Class323
		{
			// Token: 0x06001A85 RID: 6789 RVA: 0x000BBF58 File Offset: 0x000BA158
			internal bool method_0(ShowMktSymb showMktSymb_0)
			{
				return showMktSymb_0.StkId == this.int_0;
			}

			// Token: 0x04000D61 RID: 3425
			public int int_0;
		}

		// Token: 0x0200025A RID: 602
		[CompilerGenerated]
		private sealed class Class324
		{
			// Token: 0x06001A87 RID: 6791 RVA: 0x000BBF78 File Offset: 0x000BA178
			internal bool method_0(Transaction transaction_0)
			{
				if (transaction_0.AcctID == Base.Acct.CurrAccount.ID && transaction_0.SymbolID == this.int_0)
				{
					long? openUnits = transaction_0.OpenUnits;
					if (openUnits.GetValueOrDefault() > 0L & openUnits != null)
					{
						if (transaction_0.TransType != 1)
						{
							return transaction_0.TransType == 3;
						}
						return true;
					}
				}
				return false;
			}

			// Token: 0x04000D62 RID: 3426
			public int int_0;
		}

		// Token: 0x0200025B RID: 603
		[CompilerGenerated]
		private sealed class Class325
		{
			// Token: 0x06001A89 RID: 6793 RVA: 0x000BBFF0 File Offset: 0x000BA1F0
			internal bool method_0(TabItem tabItem_0)
			{
				return tabItem_0.Text.Contains(this.string_0);
			}

			// Token: 0x04000D63 RID: 3427
			public string string_0;
		}
	}
}
