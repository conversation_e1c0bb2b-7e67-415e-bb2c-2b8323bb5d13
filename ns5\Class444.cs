﻿using System;
using System.Runtime.CompilerServices;
using ns12;
using ns14;
using ns28;
using ns7;
using TEx.SIndicator;

namespace ns5
{
	// Token: 0x0200032B RID: 811
	internal sealed class Class444
	{
		// Token: 0x170005E5 RID: 1509
		// (get) Token: 0x06002261 RID: 8801 RVA: 0x000F3448 File Offset: 0x000F1648
		// (set) Token: 0x06002262 RID: 8802 RVA: 0x0000DB1C File Offset: 0x0000BD1C
		private ParserEnvironment PE { get; set; }

		// Token: 0x06002263 RID: 8803 RVA: 0x0000DB27 File Offset: 0x0000BD27
		public Class444(ParserEnvironment parserEnvironment_1)
		{
			this.PE = parserEnvironment_1;
		}

		// Token: 0x06002264 RID: 8804 RVA: 0x000F3460 File Offset: 0x000F1660
		public Class411 method_0(Tokenes tokenes_0)
		{
			tokenes_0.method_4();
			return Class422.smethod_0(tokenes_0, this.PE);
		}

		// Token: 0x06002265 RID: 8805 RVA: 0x000F3484 File Offset: 0x000F1684
		public Class411 method_1(Tokenes tokenes_0)
		{
			tokenes_0.method_4();
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = null;
			}
			else
			{
				result = Class417.smethod_0(tokenes_0);
			}
			return result;
		}

		// Token: 0x040010C1 RID: 4289
		[CompilerGenerated]
		private ParserEnvironment parserEnvironment_0;
	}
}
