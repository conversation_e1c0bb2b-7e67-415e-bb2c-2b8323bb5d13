﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns23;

namespace ns28
{
	// Token: 0x02000410 RID: 1040
	[DesignerCategory("Code")]
	internal sealed class Class549 : Label
	{
		// Token: 0x06002828 RID: 10280 RVA: 0x0010D930 File Offset: 0x0010BB30
		private void method_0()
		{
			try
			{
				using (Graphics graphics = base.CreateGraphics())
				{
					int num = Class547.smethod_2(graphics, this.Text, this.Font, base.Width);
					if (num > 0)
					{
						base.Height = num;
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002829 RID: 10281 RVA: 0x0000F94F File Offset: 0x0000DB4F
		protected void OnFontChanged(EventArgs e)
		{
			base.OnFontChanged(e);
			this.method_0();
		}

		// Token: 0x0600282A RID: 10282 RVA: 0x0000F95E File Offset: 0x0000DB5E
		protected void OnResize(EventArgs e)
		{
			base.OnResize(e);
			this.method_0();
		}

		// Token: 0x0600282B RID: 10283 RVA: 0x0000F96D File Offset: 0x0000DB6D
		protected void OnTextChanged(EventArgs e)
		{
			base.OnTextChanged(e);
			this.method_0();
		}

		// Token: 0x0600282C RID: 10284 RVA: 0x0000F97C File Offset: 0x0000DB7C
		public Class549()
		{
			base.FlatStyle = FlatStyle.System;
			base.UseMnemonic = false;
		}
	}
}
