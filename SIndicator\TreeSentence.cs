﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns10;
using ns11;
using ns12;
using ns18;
using ns26;
using ns28;
using ns7;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000330 RID: 816
	public sealed class TreeSentence : Class415
	{
		// Token: 0x0600227A RID: 8826 RVA: 0x0000D993 File Offset: 0x0000BB93
		public TreeSentence(HToken token, Class411 left, Class411 right) : base(token, left, right)
		{
		}

		// Token: 0x0600227B RID: 8827 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x0600227C RID: 8828 RVA: 0x000F39CC File Offset: 0x000F1BCC
		public static Class411 smethod_0(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			string string_ = Class521.smethod_0(50733);
			HToken htoken;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_4)
			{
				htoken = tokenes_0.Current;
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_10 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_11)
				{
					throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103012)));
				}
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_11)
				{
					string_ = Class521.smethod_0(99492);
				}
				tokenes_0.method_1();
			}
			else
			{
				if (tokenes_0.Current.Symbol.Name != Class521.smethod_0(1449) && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_4)
				{
					HToken htoken2 = tokenes_0.Current;
					tokenes_0.method_1();
					if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_10)
					{
						if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_11)
						{
							tokenes_0.method_2();
							htoken = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_4, Class521.smethod_0(1449)));
							goto IL_18F;
						}
					}
					throw new Exception(htoken2.method_0(Class521.smethod_0(103033)));
				}
				htoken = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_4, Class521.smethod_0(1449)));
			}
			IL_18F:
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103062)));
			}
			Class411 @class = Class416.smethod_0(tokenes_0);
			if (@class.Token.Symbol.HSymbolType == Enum26.const_4 && !parserEnvironment_0.NewVarList.Contains(@class.Token.Symbol.Name))
			{
				throw new Exception(@class.Token.method_0(Class521.smethod_0(101363)));
			}
			tokenes_0.method_1();
			Class411 class411_;
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30)
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_22)
				{
					if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
					{
						class411_ = TreeSentence.smethod_2(tokenes_0);
						goto IL_2B2;
					}
					throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103103)));
				}
			}
			class411_ = new Class412(new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_30, Class521.smethod_0(1449))));
			tokenes_0.method_2();
			IL_2B2:
			TreeSentence.smethod_1(htoken, parserEnvironment_0);
			Class412 class411_2 = new Class412(new HToken(htoken.Col, htoken.Line, new Class442(Enum26.const_30, Class521.smethod_0(1449))));
			Class411 left = new Class421(htoken, class411_, class411_2);
			if (!string.IsNullOrEmpty(htoken.Symbol.Name))
			{
				parserEnvironment_0.method_2(htoken.Symbol.Name);
			}
			return new TreeSentence(new HToken(htoken.Col, htoken.Line, new Class442(Enum26.const_3, string_)), left, @class);
		}

		// Token: 0x0600227D RID: 8829 RVA: 0x000F3D10 File Offset: 0x000F1F10
		private static void smethod_1(HToken htoken_1, ParserEnvironment parserEnvironment_0)
		{
			TreeSentence.Class445 @class = new TreeSentence.Class445();
			@class.string_0 = htoken_1.Symbol.Name;
			if (!parserEnvironment_0.Properties.Any(new Func<PropertyInfo, bool>(@class.method_0)) && !parserEnvironment_0.Functions.Any(new Func<MethodInfo, bool>(@class.method_1)) && !parserEnvironment_0.NewVarList.Any(new Func<string, bool>(@class.method_2)))
			{
				return;
			}
			throw new Exception(htoken_1.method_0(Class521.smethod_0(103136)));
		}

		// Token: 0x0600227E RID: 8830 RVA: 0x000F3D98 File Offset: 0x000F1F98
		public static Class411 smethod_2(Tokenes tokenes_0)
		{
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_34 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_35 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_36)
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_37)
				{
					throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103169)));
				}
			}
			Class411 @class = new Class412(tokenes_0.Current);
			tokenes_0.method_1();
			Class411 class411_;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
			{
				class411_ = TreeSentence.smethod_2(tokenes_0);
			}
			else
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_22)
				{
					throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103103)));
				}
				tokenes_0.method_2();
				class411_ = new Class412(new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class442(Enum26.const_30, Class521.smethod_0(1449))));
			}
			return new Class415(new HToken(@class.Token.Col, @class.Token.Col, @class.Token.Symbol), @class, class411_);
		}

		// Token: 0x0600227F RID: 8831 RVA: 0x000F3EE8 File Offset: 0x000F20E8
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_3)
			{
				throw new Exception(this.Token.method_0(Class521.smethod_0(103326)));
			}
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			if (obj.GetType() != typeof(List<string>))
			{
				throw new Exception(this.Left.Token.method_0(Class521.smethod_0(103186)));
			}
			List<string> list = (List<string>)obj;
			if (!list.Any<string>())
			{
				throw new Exception(this.Left.Token.method_0(Class521.smethod_0(103215)));
			}
			object obj2 = this.Right.vmethod_1(parserEnvironment_0);
			string name = this.Token.Symbol.Name;
			DataArray dataArray;
			if (obj2.GetType() == typeof(DataArray))
			{
				dataArray = (obj2 as DataArray);
			}
			else
			{
				if (obj2.GetType() != typeof(double))
				{
					throw new Exception(this.Right.Token.method_0(Class521.smethod_0(103186)));
				}
				if (name == Class521.smethod_0(99492))
				{
					return new NameDoubleValue(list[0], (double)obj2);
				}
				PropertyInfo propertyInfo = parserEnvironment_0.Properties.SingleOrDefault(new Func<PropertyInfo, bool>(TreeSentence.<>c.<>9.method_0));
				if (propertyInfo == null)
				{
					throw new Exception(Class521.smethod_0(103248));
				}
				DataArray dataArray2 = propertyInfo.GetValue(parserEnvironment_0.UserDefineIns, null) as DataArray;
				if (dataArray2 == null)
				{
					throw new Exception(Class521.smethod_0(103281));
				}
				dataArray = new DataArray(dataArray2.Data.Length, (double)obj2);
			}
			dataArray.Visible = (name == Class521.smethod_0(50733));
			dataArray.NumVisibleLineNot = false;
			if (list.Any(new Func<string, bool>(TreeSentence.<>c.<>9.method_1)))
			{
				dataArray.Visible = false;
				dataArray.NumVisibleLineNot = true;
			}
			return this.method_2(dataArray, list);
		}

		// Token: 0x06002280 RID: 8832 RVA: 0x000F410C File Offset: 0x000F230C
		private DataArray method_2(object object_0, List<string> list_0)
		{
			if (object_0.GetType() != typeof(DataArray))
			{
				throw new Exception(this.Right.Token.method_0(Class521.smethod_0(103355)));
			}
			DataArray dataArray = (DataArray)object_0;
			if (list_0.Count <= 0)
			{
				throw new Exception(Class521.smethod_0(103392));
			}
			dataArray.Name = list_0[0];
			for (int i = 1; i < list_0.Count; i++)
			{
				TreeSentence.Class446 @class = new TreeSentence.Class446();
				@class.string_0 = list_0[i].ToUpper();
				if (ParserEnvironment.smethod_4(@class.string_0))
				{
					dataArray.ColorStr = @class.string_0;
				}
				else if (ParserEnvironment.smethod_9(@class.string_0))
				{
					dataArray.LineTypeStr = @class.string_0;
				}
				else if (ParserEnvironment.smethod_10(@class.string_0))
				{
					dataArray.LineWithStr = @class.string_0;
				}
				else if (ParserEnvironment.string_0.Any(new Func<string, bool>(@class.method_0)))
				{
					dataArray.ShapeStr = @class.string_0;
				}
			}
			return dataArray;
		}

		// Token: 0x02000331 RID: 817
		[CompilerGenerated]
		private sealed class Class445
		{
			// Token: 0x06002282 RID: 8834 RVA: 0x000F4228 File Offset: 0x000F2428
			internal bool method_0(PropertyInfo propertyInfo_0)
			{
				return propertyInfo_0.Name == this.string_0;
			}

			// Token: 0x06002283 RID: 8835 RVA: 0x000F4228 File Offset: 0x000F2428
			internal bool method_1(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.string_0;
			}

			// Token: 0x06002284 RID: 8836 RVA: 0x000F424C File Offset: 0x000F244C
			internal bool method_2(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x040010C7 RID: 4295
			public string string_0;
		}

		// Token: 0x02000333 RID: 819
		[CompilerGenerated]
		private sealed class Class446
		{
			// Token: 0x0600228A RID: 8842 RVA: 0x000F42B8 File Offset: 0x000F24B8
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x040010CB RID: 4299
			public string string_0;
		}
	}
}
