﻿using System;

namespace ns3
{
	// Token: 0x020001A6 RID: 422
	internal sealed class EventArgs9 : EventArgs
	{
		// Token: 0x06001045 RID: 4165 RVA: 0x00006E74 File Offset: 0x00005074
		public EventArgs9(string string_1)
		{
			this.string_0 = string_1;
		}

		// Token: 0x17000265 RID: 613
		// (get) Token: 0x06001046 RID: 4166 RVA: 0x0006DAAC File Offset: 0x0006BCAC
		// (set) Token: 0x06001047 RID: 4167 RVA: 0x00006E85 File Offset: 0x00005085
		public string AppUpdFeed
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x04000823 RID: 2083
		private string string_0;
	}
}
