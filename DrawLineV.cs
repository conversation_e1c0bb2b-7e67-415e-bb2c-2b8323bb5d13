﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006B RID: 107
	[Serializable]
	internal class DrawLineV : DrawObj, ISerializable
	{
		// Token: 0x060003EB RID: 1003 RVA: 0x00003742 File Offset: 0x00001942
		public DrawLineV()
		{
		}

		// Token: 0x060003EC RID: 1004 RVA: 0x00003A65 File Offset: 0x00001C65
		public DrawLineV(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(28568);
			base.CanChgColor = true;
			base.IsOneClickLoc = true;
		}

		// Token: 0x060003ED RID: 1005 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawLineV(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003EE RID: 1006 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003EF RID: 1007 RVA: 0x000227FC File Offset: 0x000209FC
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_3, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x060003F0 RID: 1008 RVA: 0x00003796 File Offset: 0x00001996
		public override void vmethod_17(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			base.vmethod_17(chartCS_1, double_3, double_4, double_3, double_4);
		}

		// Token: 0x060003F1 RID: 1009 RVA: 0x000037A9 File Offset: 0x000019A9
		public override void vmethod_18(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, bool bool_6, bool bool_7)
		{
			base.vmethod_18(chartCS_1, double_3, double_4, double_3, double_4, bool_6, bool_7);
		}
	}
}
