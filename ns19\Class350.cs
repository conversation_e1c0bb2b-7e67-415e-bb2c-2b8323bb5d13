﻿using System;
using ns22;
using TEx.SIndicator;

namespace ns19
{
	// Token: 0x020002E1 RID: 737
	internal sealed class Class350 : IndEx
	{
		// Token: 0x060020CD RID: 8397 RVA: 0x0000D47A File Offset: 0x0000B67A
		public Class350(UserDefineInd userDefineInd_2) : base(userDefineInd_2)
		{
		}

		// Token: 0x060020CE RID: 8398 RVA: 0x000E991C File Offset: 0x000E7B1C
		protected override void vmethod_0()
		{
			base.vmethod_0();
			foreach (ShapeCurve shapeCurve in this.list_0)
			{
				if (shapeCurve is Class390)
				{
					shapeCurve.Curve.IsY2Axis = true;
				}
			}
		}

		// Token: 0x060020CF RID: 8399 RVA: 0x0000D483 File Offset: 0x0000B683
		public override void RescaleAxis()
		{
			base.GraphControl.GraphPane.YAxis.Scale.MagAuto = false;
		}
	}
}
