﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using ns0;
using ns18;
using ns23;
using ns26;
using ns28;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020000A3 RID: 163
	internal sealed partial class BkupSyncCnfmWnd : Form
	{
		// Token: 0x14000024 RID: 36
		// (add) Token: 0x06000563 RID: 1379 RVA: 0x0002AC34 File Offset: 0x00028E34
		// (remove) Token: 0x06000564 RID: 1380 RVA: 0x0002AC6C File Offset: 0x00028E6C
		public event Delegate27 SyncParamsConfirmed
		{
			[CompilerGenerated]
			add
			{
				Delegate27 @delegate = this.delegate27_0;
				Delegate27 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate27 value2 = (Delegate27)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate27>(ref this.delegate27_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate27 @delegate = this.delegate27_0;
				Delegate27 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate27 value2 = (Delegate27)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate27>(ref this.delegate27_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000565 RID: 1381 RVA: 0x0002ACA4 File Offset: 0x00028EA4
		protected void method_0(EventArgs23 eventArgs23_0)
		{
			Delegate27 @delegate = this.delegate27_0;
			if (@delegate != null)
			{
				@delegate(eventArgs23_0);
			}
		}

		// Token: 0x06000566 RID: 1382 RVA: 0x000045D5 File Offset: 0x000027D5
		public BkupSyncCnfmWnd()
		{
			this.method_5();
		}

		// Token: 0x06000567 RID: 1383 RVA: 0x000045E5 File Offset: 0x000027E5
		public BkupSyncCnfmWnd(List<SyncParam> syncPmLst) : this()
		{
			this.list_0 = syncPmLst;
			this.method_2();
		}

		// Token: 0x06000568 RID: 1384 RVA: 0x0002ACC4 File Offset: 0x00028EC4
		private void BkupSyncCnfmWnd_Load(object sender, EventArgs e)
		{
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			base.Deactivate += this.BkupSyncCnfmWnd_Deactivate;
			base.Activated += this.BkupSyncCnfmWnd_Activated;
		}

		// Token: 0x06000569 RID: 1385
		[DllImport("user32")]
		public static extern int SetForegroundWindow(IntPtr intptr_0);

		// Token: 0x0600056A RID: 1386 RVA: 0x000045FC File Offset: 0x000027FC
		private void method_1(object sender, EventArgs e)
		{
			BkupSyncCnfmWnd.SetForegroundWindow(base.Handle);
		}

		// Token: 0x0600056B RID: 1387 RVA: 0x000041B9 File Offset: 0x000023B9
		private void BkupSyncCnfmWnd_Deactivate(object sender, EventArgs e)
		{
		}

		// Token: 0x0600056C RID: 1388 RVA: 0x000041B9 File Offset: 0x000023B9
		private void BkupSyncCnfmWnd_Activated(object sender, EventArgs e)
		{
		}

		// Token: 0x0600056D RID: 1389 RVA: 0x0002AD28 File Offset: 0x00028F28
		private void method_2()
		{
			foreach (SyncParam syncParam in this.list_0)
			{
				switch (syncParam.enum8_0)
				{
				case Enum8.const_0:
					this.method_4(syncParam, this.checkBox_1, this.checkBox_2);
					break;
				case Enum8.const_1:
					this.method_4(syncParam, this.checkBox_3, this.checkBox_4);
					break;
				case Enum8.const_2:
					this.method_4(syncParam, this.checkBox_5, this.checkBox_6);
					break;
				case Enum8.const_3:
					this.method_4(syncParam, this.checkBox_7, this.checkBox_8);
					break;
				case Enum8.const_4:
					this.method_4(syncParam, this.checkBox_9, this.checkBox_10);
					break;
				case Enum8.const_5:
					this.method_4(syncParam, this.checkBox_11, this.checkBox_12);
					break;
				}
			}
			if (this.list_0.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_0)).Any<SyncParam>())
			{
				this.checkBox_0.Visible = false;
			}
		}

		// Token: 0x0600056E RID: 1390 RVA: 0x0002AE60 File Offset: 0x00029060
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.checkBox_1.Enabled)
			{
				SyncParam syncParam = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_1));
				if (!this.checkBox_1.Checked)
				{
					syncParam.bool_2 = true;
				}
			}
			if (this.checkBox_2.Enabled)
			{
				SyncParam syncParam2 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_2));
				if (!this.checkBox_2.Checked)
				{
					syncParam2.bool_2 = true;
				}
			}
			if (this.checkBox_3.Enabled)
			{
				SyncParam syncParam3 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_3));
				if (!this.checkBox_3.Checked)
				{
					syncParam3.bool_2 = true;
				}
			}
			if (this.checkBox_4.Enabled)
			{
				SyncParam syncParam4 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_4));
				if (!this.checkBox_4.Checked)
				{
					syncParam4.bool_2 = true;
				}
			}
			if (this.checkBox_5.Enabled)
			{
				SyncParam syncParam5 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_5));
				if (!this.checkBox_5.Checked)
				{
					syncParam5.bool_2 = true;
				}
			}
			if (this.checkBox_6.Enabled)
			{
				SyncParam syncParam6 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_6));
				if (!this.checkBox_6.Checked)
				{
					syncParam6.bool_2 = true;
				}
			}
			if (this.checkBox_7.Enabled)
			{
				SyncParam syncParam7 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_7));
				SyncParam syncParam8 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_8));
				if (!this.checkBox_7.Checked)
				{
					syncParam7.bool_2 = true;
					syncParam8.bool_2 = true;
				}
			}
			if (this.checkBox_8.Enabled)
			{
				SyncParam syncParam9 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_9));
				SyncParam syncParam10 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_10));
				if (!this.checkBox_8.Checked)
				{
					syncParam9.bool_2 = true;
					syncParam10.bool_2 = true;
				}
			}
			if (this.checkBox_9.Enabled)
			{
				SyncParam syncParam11 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_11));
				if (!this.checkBox_9.Checked)
				{
					syncParam11.bool_2 = true;
				}
			}
			if (this.checkBox_10.Enabled)
			{
				SyncParam syncParam12 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_12));
				if (!this.checkBox_10.Checked)
				{
					syncParam12.bool_2 = true;
				}
			}
			if (this.checkBox_11.Enabled)
			{
				SyncParam syncParam13 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_13));
				if (!this.checkBox_11.Checked)
				{
					syncParam13.bool_2 = true;
				}
			}
			if (this.checkBox_12.Enabled)
			{
				SyncParam syncParam14 = this.list_0.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_14));
				if (!this.checkBox_12.Checked)
				{
					syncParam14.bool_2 = true;
				}
			}
			bool bool_ = false;
			bool bool_2 = false;
			if (this.checkBox_0.Checked)
			{
				if (this.list_0.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_15)).Any<SyncParam>())
				{
					bool_ = true;
				}
				if (this.list_0.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_16)).Any<SyncParam>())
				{
					bool_2 = true;
				}
			}
			EventArgs23 eventArgs23_ = new EventArgs23(this.list_0, bool_, bool_2);
			this.method_3(eventArgs23_);
			this.method_0(eventArgs23_);
			base.DialogResult = DialogResult.OK;
			base.Close();
		}

		// Token: 0x0600056F RID: 1391 RVA: 0x0002B320 File Offset: 0x00029520
		private void method_3(EventArgs23 eventArgs23_0)
		{
			IEnumerable<SyncParam> enumerable = eventArgs23_0.SyncParamList.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_17));
			if (enumerable.Any<SyncParam>())
			{
				using (IEnumerator<SyncParam> enumerator = enumerable.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						BkupSyncCnfmWnd.Class64 @class = new BkupSyncCnfmWnd.Class64();
						@class.syncParam_0 = enumerator.Current;
						switch (@class.syncParam_0.enum8_0)
						{
						case Enum8.const_0:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_0));
							break;
						case Enum8.const_1:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_1));
							break;
						case Enum8.const_2:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_2));
							break;
						case Enum8.const_3:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_3));
							break;
						case Enum8.const_4:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_4));
							break;
						case Enum8.const_5:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_5));
							break;
						}
					}
				}
			}
			if (eventArgs23_0.IfNoConfirmCopyToLocal)
			{
				Base.UI.Form.BackupSyncAutoOverwritingLocalFile = true;
			}
			if (eventArgs23_0.IfNoConfirmCopytoSrvNewer)
			{
				Base.UI.Form.BackupSyncConflictTreatmt = new BackupSyncConflictTreatmt?(BackupSyncConflictTreatmt.AutoSyncNoPrompt);
			}
		}

		// Token: 0x06000570 RID: 1392 RVA: 0x0000460C File Offset: 0x0000280C
		private void button_0_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x06000571 RID: 1393 RVA: 0x0000461D File Offset: 0x0000281D
		private void method_4(SyncParam syncParam_0, CheckBox checkBox_13, CheckBox checkBox_14)
		{
			if (syncParam_0.bool_0)
			{
				checkBox_13.Enabled = true;
				checkBox_13.Checked = true;
			}
			else
			{
				checkBox_14.Enabled = true;
				checkBox_14.Checked = true;
			}
		}

		// Token: 0x1700010A RID: 266
		// (get) Token: 0x06000572 RID: 1394 RVA: 0x0002B49C File Offset: 0x0002969C
		// (set) Token: 0x06000573 RID: 1395 RVA: 0x00004647 File Offset: 0x00002847
		public List<SyncParam> SyncPmLst
		{
			get
			{
				return this.list_0;
			}
			set
			{
				if (this.list_0 == null || this.list_0 != value)
				{
					this.list_0 = value;
					this.method_2();
				}
			}
		}

		// Token: 0x06000574 RID: 1396 RVA: 0x00004669 File Offset: 0x00002869
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000575 RID: 1397 RVA: 0x0002B4B4 File Offset: 0x000296B4
		private void method_5()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(BkupSyncCnfmWnd));
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.pictureBox_0 = new PictureBox();
			this.pictureBox_1 = new PictureBox();
			this.pictureBox_2 = new PictureBox();
			this.pictureBox_3 = new PictureBox();
			this.label_0 = new Label();
			this.checkBox_0 = new CheckBox();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.checkBox_1 = new CheckBox();
			this.groupBox_0 = new GroupBox();
			this.checkBox_2 = new CheckBox();
			this.label_5 = new Label();
			this.label_6 = new Label();
			this.label_7 = new Label();
			this.label_8 = new Label();
			this.label_9 = new Label();
			this.checkBox_3 = new CheckBox();
			this.checkBox_4 = new CheckBox();
			this.checkBox_5 = new CheckBox();
			this.checkBox_6 = new CheckBox();
			this.checkBox_7 = new CheckBox();
			this.checkBox_8 = new CheckBox();
			this.checkBox_9 = new CheckBox();
			this.checkBox_10 = new CheckBox();
			this.checkBox_11 = new CheckBox();
			this.checkBox_12 = new CheckBox();
			this.groupBox_1 = new GroupBox();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			((ISupportInitialize)this.pictureBox_1).BeginInit();
			((ISupportInitialize)this.pictureBox_2).BeginInit();
			((ISupportInitialize)this.pictureBox_3).BeginInit();
			base.SuspendLayout();
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(365, 304);
			this.button_0.Name = Class521.smethod_0(10825);
			this.button_0.Size = new Size(110, 30);
			this.button_0.TabIndex = 7;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(241, 304);
			this.button_1.Name = Class521.smethod_0(10838);
			this.button_1.Size = new Size(110, 30);
			this.button_1.TabIndex = 6;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.pictureBox_0.Image = (Image)componentResourceManager.GetObject(Class521.smethod_0(10847));
			this.pictureBox_0.Location = new Point(370, 13);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(56, 56);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 8;
			this.pictureBox_0.TabStop = false;
			this.pictureBox_1.Image = Class375.notebook;
			this.pictureBox_1.Location = new Point(101, 18);
			this.pictureBox_1.Name = Class521.smethod_0(6913);
			this.pictureBox_1.Size = new Size(48, 48);
			this.pictureBox_1.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_1.TabIndex = 9;
			this.pictureBox_1.TabStop = false;
			this.pictureBox_2.Image = Class375.biArrow;
			this.pictureBox_2.Location = new Point(155, 40);
			this.pictureBox_2.Name = Class521.smethod_0(6947);
			this.pictureBox_2.Size = new Size(210, 10);
			this.pictureBox_2.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_2.TabIndex = 11;
			this.pictureBox_2.TabStop = false;
			this.pictureBox_3.Image = Class375.files;
			this.pictureBox_3.Location = new Point(244, 29);
			this.pictureBox_3.Name = Class521.smethod_0(6930);
			this.pictureBox_3.Size = new Size(28, 28);
			this.pictureBox_3.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_3.TabIndex = 12;
			this.pictureBox_3.TabStop = false;
			this.label_0.Location = new Point(113, 269);
			this.label_0.Name = Class521.smethod_0(10872);
			this.label_0.Size = new Size(318, 23);
			this.label_0.TabIndex = 14;
			this.label_0.Text = Class521.smethod_0(10885);
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(64, 311);
			this.checkBox_0.Name = Class521.smethod_0(10958);
			this.checkBox_0.Size = new Size(119, 19);
			this.checkBox_0.TabIndex = 15;
			this.checkBox_0.Text = Class521.smethod_0(10987);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(224, 75);
			this.label_1.Name = Class521.smethod_0(5871);
			this.label_1.Size = new Size(67, 15);
			this.label_1.TabIndex = 16;
			this.label_1.Text = Class521.smethod_0(11012);
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(358, 75);
			this.label_2.Name = Class521.smethod_0(5827);
			this.label_2.Size = new Size(82, 15);
			this.label_2.TabIndex = 17;
			this.label_2.Text = Class521.smethod_0(11029);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(84, 75);
			this.label_3.Name = Class521.smethod_0(5849);
			this.label_3.Size = new Size(82, 15);
			this.label_3.TabIndex = 18;
			this.label_3.Text = Class521.smethod_0(11050);
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(202, 108);
			this.label_4.Name = Class521.smethod_0(7019);
			this.label_4.Size = new Size(112, 15);
			this.label_4.TabIndex = 19;
			this.label_4.Text = Class521.smethod_0(11071);
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.Enabled = false;
			this.checkBox_1.Location = new Point(391, 108);
			this.checkBox_1.Name = Class521.smethod_0(11100);
			this.checkBox_1.Size = new Size(18, 17);
			this.checkBox_1.TabIndex = 20;
			this.checkBox_1.UseVisualStyleBackColor = true;
			this.groupBox_0.Location = new Point(87, 90);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(350, 10);
			this.groupBox_0.TabIndex = 21;
			this.groupBox_0.TabStop = false;
			this.checkBox_2.AutoSize = true;
			this.checkBox_2.Enabled = false;
			this.checkBox_2.Location = new Point(116, 107);
			this.checkBox_2.Name = Class521.smethod_0(11129);
			this.checkBox_2.Size = new Size(18, 17);
			this.checkBox_2.TabIndex = 22;
			this.checkBox_2.UseVisualStyleBackColor = true;
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(225, 132);
			this.label_5.Name = Class521.smethod_0(5893);
			this.label_5.Size = new Size(67, 15);
			this.label_5.TabIndex = 23;
			this.label_5.Text = Class521.smethod_0(11158);
			this.label_6.AutoSize = true;
			this.label_6.Location = new Point(225, 156);
			this.label_6.Name = Class521.smethod_0(5915);
			this.label_6.Size = new Size(67, 15);
			this.label_6.TabIndex = 24;
			this.label_6.Text = Class521.smethod_0(11175);
			this.label_7.AutoSize = true;
			this.label_7.Location = new Point(185, 180);
			this.label_7.Name = Class521.smethod_0(7268);
			this.label_7.Size = new Size(150, 15);
			this.label_7.TabIndex = 25;
			this.label_7.Text = Class521.smethod_0(11192);
			this.label_8.AutoSize = true;
			this.label_8.Location = new Point(210, 204);
			this.label_8.Name = Class521.smethod_0(11233);
			this.label_8.Size = new Size(97, 15);
			this.label_8.TabIndex = 26;
			this.label_8.Text = Class521.smethod_0(11242);
			this.label_9.AutoSize = true;
			this.label_9.Location = new Point(240, 228);
			this.label_9.Name = Class521.smethod_0(11267);
			this.label_9.Size = new Size(37, 15);
			this.label_9.TabIndex = 27;
			this.label_9.Text = Class521.smethod_0(11276);
			this.checkBox_3.AutoSize = true;
			this.checkBox_3.Enabled = false;
			this.checkBox_3.Location = new Point(391, 132);
			this.checkBox_3.Name = Class521.smethod_0(11285);
			this.checkBox_3.Size = new Size(18, 17);
			this.checkBox_3.TabIndex = 28;
			this.checkBox_3.UseVisualStyleBackColor = true;
			this.checkBox_4.AutoSize = true;
			this.checkBox_4.Enabled = false;
			this.checkBox_4.Location = new Point(116, 131);
			this.checkBox_4.Name = Class521.smethod_0(11318);
			this.checkBox_4.Size = new Size(18, 17);
			this.checkBox_4.TabIndex = 29;
			this.checkBox_4.UseVisualStyleBackColor = true;
			this.checkBox_5.AutoSize = true;
			this.checkBox_5.Enabled = false;
			this.checkBox_5.Location = new Point(391, 156);
			this.checkBox_5.Name = Class521.smethod_0(11351);
			this.checkBox_5.Size = new Size(18, 17);
			this.checkBox_5.TabIndex = 30;
			this.checkBox_5.UseVisualStyleBackColor = true;
			this.checkBox_6.AutoSize = true;
			this.checkBox_6.Enabled = false;
			this.checkBox_6.Location = new Point(116, 155);
			this.checkBox_6.Name = Class521.smethod_0(11384);
			this.checkBox_6.Size = new Size(18, 17);
			this.checkBox_6.TabIndex = 31;
			this.checkBox_6.UseVisualStyleBackColor = true;
			this.checkBox_7.AutoSize = true;
			this.checkBox_7.Enabled = false;
			this.checkBox_7.Location = new Point(391, 180);
			this.checkBox_7.Name = Class521.smethod_0(11417);
			this.checkBox_7.Size = new Size(18, 17);
			this.checkBox_7.TabIndex = 32;
			this.checkBox_7.UseVisualStyleBackColor = true;
			this.checkBox_8.AutoSize = true;
			this.checkBox_8.Enabled = false;
			this.checkBox_8.Location = new Point(116, 179);
			this.checkBox_8.Name = Class521.smethod_0(11442);
			this.checkBox_8.Size = new Size(18, 17);
			this.checkBox_8.TabIndex = 33;
			this.checkBox_8.UseVisualStyleBackColor = true;
			this.checkBox_9.AutoSize = true;
			this.checkBox_9.Enabled = false;
			this.checkBox_9.Location = new Point(391, 204);
			this.checkBox_9.Name = Class521.smethod_0(11467);
			this.checkBox_9.Size = new Size(18, 17);
			this.checkBox_9.TabIndex = 34;
			this.checkBox_9.UseVisualStyleBackColor = true;
			this.checkBox_10.AutoSize = true;
			this.checkBox_10.Enabled = false;
			this.checkBox_10.Location = new Point(116, 203);
			this.checkBox_10.Name = Class521.smethod_0(11492);
			this.checkBox_10.Size = new Size(18, 17);
			this.checkBox_10.TabIndex = 35;
			this.checkBox_10.UseVisualStyleBackColor = true;
			this.checkBox_11.AutoSize = true;
			this.checkBox_11.Enabled = false;
			this.checkBox_11.Location = new Point(391, 228);
			this.checkBox_11.Name = Class521.smethod_0(11517);
			this.checkBox_11.Size = new Size(18, 17);
			this.checkBox_11.TabIndex = 36;
			this.checkBox_11.UseVisualStyleBackColor = true;
			this.checkBox_12.AutoSize = true;
			this.checkBox_12.Enabled = false;
			this.checkBox_12.Location = new Point(116, 227);
			this.checkBox_12.Name = Class521.smethod_0(11542);
			this.checkBox_12.Size = new Size(18, 17);
			this.checkBox_12.TabIndex = 37;
			this.checkBox_12.UseVisualStyleBackColor = true;
			this.groupBox_1.Location = new Point(87, 247);
			this.groupBox_1.Name = Class521.smethod_0(10647);
			this.groupBox_1.Size = new Size(350, 10);
			this.groupBox_1.TabIndex = 38;
			this.groupBox_1.TabStop = false;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(528, 346);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.checkBox_12);
			base.Controls.Add(this.checkBox_11);
			base.Controls.Add(this.checkBox_10);
			base.Controls.Add(this.checkBox_9);
			base.Controls.Add(this.checkBox_8);
			base.Controls.Add(this.checkBox_7);
			base.Controls.Add(this.checkBox_6);
			base.Controls.Add(this.checkBox_5);
			base.Controls.Add(this.checkBox_4);
			base.Controls.Add(this.checkBox_3);
			base.Controls.Add(this.label_9);
			base.Controls.Add(this.label_8);
			base.Controls.Add(this.label_7);
			base.Controls.Add(this.label_6);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.checkBox_2);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.checkBox_1);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.checkBox_0);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.pictureBox_3);
			base.Controls.Add(this.pictureBox_2);
			base.Controls.Add(this.pictureBox_1);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(11567);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(11588);
			base.TopMost = true;
			base.Load += this.BkupSyncCnfmWnd_Load;
			((ISupportInitialize)this.pictureBox_0).EndInit();
			((ISupportInitialize)this.pictureBox_1).EndInit();
			((ISupportInitialize)this.pictureBox_2).EndInit();
			((ISupportInitialize)this.pictureBox_3).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000256 RID: 598
		[CompilerGenerated]
		private Delegate27 delegate27_0;

		// Token: 0x04000257 RID: 599
		private List<SyncParam> list_0;

		// Token: 0x04000258 RID: 600
		private IContainer icontainer_0;

		// Token: 0x04000259 RID: 601
		private Button button_0;

		// Token: 0x0400025A RID: 602
		private Button button_1;

		// Token: 0x0400025B RID: 603
		private PictureBox pictureBox_0;

		// Token: 0x0400025C RID: 604
		private PictureBox pictureBox_1;

		// Token: 0x0400025D RID: 605
		private PictureBox pictureBox_2;

		// Token: 0x0400025E RID: 606
		private PictureBox pictureBox_3;

		// Token: 0x0400025F RID: 607
		private Label label_0;

		// Token: 0x04000260 RID: 608
		private CheckBox checkBox_0;

		// Token: 0x04000261 RID: 609
		private Label label_1;

		// Token: 0x04000262 RID: 610
		private Label label_2;

		// Token: 0x04000263 RID: 611
		private Label label_3;

		// Token: 0x04000264 RID: 612
		private Label label_4;

		// Token: 0x04000265 RID: 613
		private CheckBox checkBox_1;

		// Token: 0x04000266 RID: 614
		private GroupBox groupBox_0;

		// Token: 0x04000267 RID: 615
		private CheckBox checkBox_2;

		// Token: 0x04000268 RID: 616
		private Label label_5;

		// Token: 0x04000269 RID: 617
		private Label label_6;

		// Token: 0x0400026A RID: 618
		private Label label_7;

		// Token: 0x0400026B RID: 619
		private Label label_8;

		// Token: 0x0400026C RID: 620
		private Label label_9;

		// Token: 0x0400026D RID: 621
		private CheckBox checkBox_3;

		// Token: 0x0400026E RID: 622
		private CheckBox checkBox_4;

		// Token: 0x0400026F RID: 623
		private CheckBox checkBox_5;

		// Token: 0x04000270 RID: 624
		private CheckBox checkBox_6;

		// Token: 0x04000271 RID: 625
		private CheckBox checkBox_7;

		// Token: 0x04000272 RID: 626
		private CheckBox checkBox_8;

		// Token: 0x04000273 RID: 627
		private CheckBox checkBox_9;

		// Token: 0x04000274 RID: 628
		private CheckBox checkBox_10;

		// Token: 0x04000275 RID: 629
		private CheckBox checkBox_11;

		// Token: 0x04000276 RID: 630
		private CheckBox checkBox_12;

		// Token: 0x04000277 RID: 631
		private GroupBox groupBox_1;

		// Token: 0x020000A5 RID: 165
		[CompilerGenerated]
		private sealed class Class64
		{
			// Token: 0x0600058B RID: 1419 RVA: 0x0002C9A0 File Offset: 0x0002ABA0
			internal bool method_0(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals(Class521.smethod_0(9444)) && !srvParam_0.FileName.Equals(Class521.smethod_0(9444)) && !srvParam_0.Note.Contains(Class521.smethod_0(9495)))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058C RID: 1420 RVA: 0x0002CA10 File Offset: 0x0002AC10
			internal bool method_1(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals(Class521.smethod_0(9316)))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0600058D RID: 1421 RVA: 0x0002CA54 File Offset: 0x0002AC54
			internal bool method_2(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals(Class521.smethod_0(9329)) && !srvParam_0.FileName.Equals(Class521.smethod_0(9346)))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058E RID: 1422 RVA: 0x0002CAB0 File Offset: 0x0002ACB0
			internal bool method_3(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals(Class521.smethod_0(9363)) && !srvParam_0.FileName.Equals(Class521.smethod_0(9376)))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058F RID: 1423 RVA: 0x0002CB0C File Offset: 0x0002AD0C
			internal bool method_4(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals(Class521.smethod_0(9410)))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06000590 RID: 1424 RVA: 0x0002CB50 File Offset: 0x0002AD50
			internal bool method_5(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals(Class521.smethod_0(9427)))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400028B RID: 651
			public SyncParam syncParam_0;
		}
	}
}
