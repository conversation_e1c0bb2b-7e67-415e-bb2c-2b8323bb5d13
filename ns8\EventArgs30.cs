﻿using System;
using System.Runtime.CompilerServices;
using TEx.SIndicator;

namespace ns8
{
	// Token: 0x02000301 RID: 769
	internal sealed class EventArgs30 : EventArgs
	{
		// Token: 0x170005C0 RID: 1472
		// (get) Token: 0x0600214F RID: 8527 RVA: 0x000EC9B8 File Offset: 0x000EABB8
		// (set) Token: 0x06002150 RID: 8528 RVA: 0x0000D6C3 File Offset: 0x0000B8C3
		public UserDefineIndScript UDS { get; private set; }

		// Token: 0x06002151 RID: 8529 RVA: 0x0000D6CE File Offset: 0x0000B8CE
		public EventArgs30(UserDefineIndScript userDefineIndScript_1)
		{
			this.UDS = (userDefineIndScript_1.System.ICloneable.Clone() as UserDefineIndScript);
		}

		// Token: 0x04001035 RID: 4149
		[CompilerGenerated]
		private UserDefineIndScript userDefineIndScript_0;
	}
}
