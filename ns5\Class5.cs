﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using ns18;

namespace ns5
{
	// Token: 0x02000007 RID: 7
	[CompilerGenerated]
	internal sealed class Class5<T, U>
	{
		// Token: 0x17000011 RID: 17
		// (get) Token: 0x06000025 RID: 37 RVA: 0x000108B4 File Offset: 0x0000EAB4
		public T codes
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x17000012 RID: 18
		// (get) Token: 0x06000026 RID: 38 RVA: 0x000108CC File Offset: 0x0000EACC
		public U date
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x06000027 RID: 39 RVA: 0x00002AF9 File Offset: 0x00000CF9
		[DebuggerHidden]
		public Class5(T gparam_2, U gparam_3)
		{
			this.gparam_0 = gparam_2;
			this.gparam_1 = gparam_3;
		}

		// Token: 0x06000028 RID: 40 RVA: 0x000108E4 File Offset: 0x0000EAE4
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class5<T, U> @class = obj as Class5<T, U>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0))
			{
				result = EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000029 RID: 41 RVA: 0x00010934 File Offset: 0x0000EB34
		[DebuggerHidden]
		public int GetHashCode()
		{
			return (-********** + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1);
		}

		// Token: 0x0600002A RID: 42 RVA: 0x00010974 File Offset: 0x0000EB74
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = Class521.smethod_0(422);
			object[] array = new object[2];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_4B;
				}
			}
			obj = ptr.ToString();
			IL_4B:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_86;
				}
			}
			obj2 = ptr2.ToString();
			IL_86:
			array[num2] = obj2;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000011 RID: 17
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x04000012 RID: 18
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;
	}
}
