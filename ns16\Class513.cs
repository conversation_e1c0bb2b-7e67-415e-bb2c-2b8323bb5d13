﻿using System;
using ns13;
using ns15;
using ns17;
using ns18;
using ns20;

namespace ns16
{
	// Token: 0x020003C4 RID: 964
	internal sealed class Class513
	{
		// Token: 0x060026D8 RID: 9944 RVA: 0x00105F88 File Offset: 0x00104188
		public static string smethod_0()
		{
			string result;
			try
			{
				object obj;
				Enum33 @enum = Class513.smethod_1(Class521.smethod_0(116109), Class514.SubkeyApplication, out obj);
				if (@enum == Enum33.const_1)
				{
					result = null;
				}
				else
				{
					if (@enum == Enum33.const_0)
					{
						@enum = Class513.smethod_1(Class521.smethod_0(116109), Class514.WowSubkeyApplication, out obj);
					}
					result = (string)obj;
				}
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060026D9 RID: 9945 RVA: 0x00105FF0 File Offset: 0x001041F0
		public static Enum33 smethod_1(string string_0, string string_1, out object object_0)
		{
			object_0 = null;
			try
			{
				Enum33 @enum;
				using (Class516 @class = Class518.smethod_0(Class515.uintptr_2, Enum34.const_0, Enum32.const_0, string_1, out @enum))
				{
					if (@enum == Enum33.const_1)
					{
						return Enum33.const_1;
					}
					if (@enum != Enum33.const_2)
					{
						return Enum33.const_0;
					}
					object_0 = @class.vmethod_0(string_0);
				}
			}
			catch
			{
				return Enum33.const_0;
			}
			return Enum33.const_2;
		}
	}
}
