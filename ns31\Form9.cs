﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Rendering;
using ns11;
using ns18;
using ns26;
using ns4;
using ns9;
using TEx;

namespace ns31
{
	// Token: 0x02000163 RID: 355
	internal sealed partial class Form9 : Form
	{
		// Token: 0x06000D79 RID: 3449 RVA: 0x00005FC9 File Offset: 0x000041C9
		public Form9()
		{
			this.method_7();
			base.Load += this.Form9_Load;
			base.Shown += this.Form9_Shown;
		}

		// Token: 0x06000D7A RID: 3450 RVA: 0x00056B30 File Offset: 0x00054D30
		private void Form9_Load(object sender, EventArgs e)
		{
			this.method_0(this.buttonX_3);
			this.method_0(this.buttonX_4);
			this.method_0(this.buttonX_0);
			this.method_0(this.buttonX_1);
			this.method_0(this.buttonX_2);
			this.method_0(this.buttonX_5);
			this.buttonX_3.AutoCheckOnClick = true;
			this.buttonX_4.AutoCheckOnClick = true;
			this.buttonX_0.AutoCheckOnClick = true;
			this.buttonX_1.AutoCheckOnClick = true;
			this.buttonX_2.AutoCheckOnClick = true;
			this.buttonX_5.AutoCheckOnClick = true;
			this.buttonX_3.Click += this.buttonX_3_Click;
			this.buttonX_4.Click += this.buttonX_4_Click;
			this.buttonX_0.Click += this.buttonX_0_Click;
			this.buttonX_1.Click += this.buttonX_1_Click;
			this.buttonX_2.Click += this.buttonX_2_Click;
			this.buttonX_5.Click += this.buttonX_5_Click;
			Base.UI.DrawModeSetOff += this.method_5;
			Base.UI.DrawOdrWnd = this;
			base.FormClosed += this.Form9_FormClosed;
			base.Deactivate += this.Form9_Deactivate;
			base.Activated += this.Form9_Activated;
			base.Disposed += this.Form9_Disposed;
			this.method_2();
			Base.Data.CurrSymblChanged += this.method_1;
			base.Focus();
		}

		// Token: 0x06000D7B RID: 3451 RVA: 0x00005FFD File Offset: 0x000041FD
		private void method_0(ButtonX buttonX_6)
		{
			buttonX_6.RenderMode = eRenderMode.Custom;
			buttonX_6.Renderer = new Office2007Renderer();
			buttonX_6.BackColor = Class181.color_10;
		}

		// Token: 0x06000D7C RID: 3452 RVA: 0x00056CD8 File Offset: 0x00054ED8
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (msg.WParam.ToInt32() == 27)
			{
				Base.UI.smethod_156();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000D7D RID: 3453 RVA: 0x0000601E File Offset: 0x0000421E
		private void method_1(EventArgs1 eventArgs1_0)
		{
			this.method_2();
		}

		// Token: 0x06000D7E RID: 3454 RVA: 0x00056D08 File Offset: 0x00054F08
		private void method_2()
		{
			bool enabled = true;
			if ((Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null && Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol != null && !Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymblSupportsShort) || (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrSymbol != null && !Base.Data.CurrSymbDataSet.CurrSymblSupportsShort))
			{
				enabled = false;
			}
			this.buttonX_1.Enabled = enabled;
		}

		// Token: 0x06000D7F RID: 3455 RVA: 0x00006028 File Offset: 0x00004228
		private void Form9_Shown(object sender, EventArgs e)
		{
			this.Text = Class521.smethod_0(23658);
		}

		// Token: 0x06000D80 RID: 3456 RVA: 0x0000603C File Offset: 0x0000423C
		private void Form9_Deactivate(object sender, EventArgs e)
		{
			base.TopMost = false;
		}

		// Token: 0x06000D81 RID: 3457 RVA: 0x00006047 File Offset: 0x00004247
		private void Form9_Activated(object sender, EventArgs e)
		{
			base.TopMost = true;
		}

		// Token: 0x06000D82 RID: 3458 RVA: 0x00056D7C File Offset: 0x00054F7C
		private void buttonX_3_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.RevTransOdr);
		}

		// Token: 0x06000D83 RID: 3459 RVA: 0x00056D9C File Offset: 0x00054F9C
		private void buttonX_4_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.EraseAllDrawOdr);
		}

		// Token: 0x06000D84 RID: 3460 RVA: 0x00056DBC File Offset: 0x00054FBC
		private void buttonX_0_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.OpenLongOdr);
		}

		// Token: 0x06000D85 RID: 3461 RVA: 0x00056DDC File Offset: 0x00054FDC
		private void buttonX_1_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.OpenShrtOdr);
		}

		// Token: 0x06000D86 RID: 3462 RVA: 0x00056DFC File Offset: 0x00054FFC
		private void buttonX_2_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.StopLimitOdr);
		}

		// Token: 0x06000D87 RID: 3463 RVA: 0x00056E1C File Offset: 0x0005501C
		private void buttonX_5_Click(object sender, EventArgs e)
		{
			ButtonX buttonX = (ButtonX)sender;
			if (Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.method_3(buttonX, TEx.DrawMode.ROpenOdr);
			}
			else if (new Form19().ShowDialog() == DialogResult.OK)
			{
				this.method_3(buttonX, TEx.DrawMode.ROpenOdr);
			}
			else
			{
				buttonX.Checked = false;
			}
		}

		// Token: 0x06000D88 RID: 3464 RVA: 0x00056E68 File Offset: 0x00055068
		private void method_3(ButtonX buttonX_6, TEx.DrawMode drawMode_0)
		{
			if (buttonX_6.Checked)
			{
				this.method_4(buttonX_6);
				if (Base.Acct.CurrAccount.IsReadOnly)
				{
					Base.Acct.smethod_50();
					buttonX_6.Checked = false;
					Base.UI.DrawMode = TEx.DrawMode.Off;
					return;
				}
				Base.UI.DrawMode = drawMode_0;
				if (drawMode_0 == TEx.DrawMode.EraseAllDrawOdr || Base.UI.Form.StockRestorationMethod == null || Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.Later)
				{
					return;
				}
				using (List<ChtCtrl>.Enumerator enumerator = Base.UI.ChtCtrlList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						ChtCtrl chtCtrl = enumerator.Current;
						SymbDataSet symbDataSet = chtCtrl.SymbDataSet;
						if (symbDataSet.CurrSymbol.IsStock && symbDataSet.CurrSymbStSpltList != null)
						{
							chtCtrl.method_31();
						}
					}
					return;
				}
			}
			Base.UI.DrawMode = TEx.DrawMode.Off;
		}

		// Token: 0x06000D89 RID: 3465 RVA: 0x00056F4C File Offset: 0x0005514C
		private void method_4(ButtonX buttonX_6)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX && obj != buttonX_6)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000D8A RID: 3466 RVA: 0x00006052 File Offset: 0x00004252
		private void Form9_FormClosed(object sender, FormClosedEventArgs e)
		{
			Base.UI.DrawMode = TEx.DrawMode.Off;
			Base.UI.DrawOdrWnd = null;
			Base.UI.smethod_156();
		}

		// Token: 0x06000D8B RID: 3467 RVA: 0x00006068 File Offset: 0x00004268
		private void method_5(object sender, EventArgs e)
		{
			this.method_6();
		}

		// Token: 0x06000D8C RID: 3468 RVA: 0x00056FC0 File Offset: 0x000551C0
		private void method_6()
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000D8D RID: 3469 RVA: 0x00006072 File Offset: 0x00004272
		private void Form9_Disposed(object sender, EventArgs e)
		{
			Base.UI.DrawModeSetOff -= this.method_5;
			Base.Data.CurrSymblChanged -= this.method_1;
		}

		// Token: 0x06000D8E RID: 3470 RVA: 0x00006098 File Offset: 0x00004298
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000D8F RID: 3471 RVA: 0x00057030 File Offset: 0x00055230
		private void method_7()
		{
			this.buttonX_0 = new ButtonX();
			this.buttonX_1 = new ButtonX();
			this.buttonX_2 = new ButtonX();
			this.buttonX_3 = new ButtonX();
			this.buttonX_4 = new ButtonX();
			this.buttonX_5 = new ButtonX();
			base.SuspendLayout();
			this.buttonX_0.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_0.ColorTable = eButtonColor.Flat;
			this.buttonX_0.Image = Class375.DrawOpenLongOdr;
			this.buttonX_0.Location = new Point(15, 8);
			this.buttonX_0.Name = Class521.smethod_0(23675);
			this.buttonX_0.Size = new Size(32, 30);
			this.buttonX_0.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_0.TabIndex = 0;
			this.buttonX_0.Tooltip = Class521.smethod_0(23688);
			this.buttonX_1.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_1.ColorTable = eButtonColor.Flat;
			this.buttonX_1.Image = Class375.DrawOpenShrtOdr;
			this.buttonX_1.Location = new Point(63, 8);
			this.buttonX_1.Name = Class521.smethod_0(23705);
			this.buttonX_1.Size = new Size(32, 30);
			this.buttonX_1.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_1.TabIndex = 1;
			this.buttonX_1.Tooltip = Class521.smethod_0(23718);
			this.buttonX_2.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_2.ColorTable = eButtonColor.Flat;
			this.buttonX_2.Image = Class375.DrawCondClose;
			this.buttonX_2.Location = new Point(111, 8);
			this.buttonX_2.Name = Class521.smethod_0(23735);
			this.buttonX_2.Size = new Size(32, 30);
			this.buttonX_2.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_2.TabIndex = 2;
			this.buttonX_2.Tooltip = Class521.smethod_0(23756);
			this.buttonX_3.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_3.ColorTable = eButtonColor.Flat;
			this.buttonX_3.Image = Class375.DrawClsRevOpen;
			this.buttonX_3.Location = new Point(159, 8);
			this.buttonX_3.Name = Class521.smethod_0(23773);
			this.buttonX_3.Size = new Size(32, 30);
			this.buttonX_3.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_3.TabIndex = 3;
			this.buttonX_3.Tooltip = Class521.smethod_0(23794);
			this.buttonX_4.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_4.ColorTable = eButtonColor.Flat;
			this.buttonX_4.Image = Class375.DelDraw;
			this.buttonX_4.Location = new Point(264, 8);
			this.buttonX_4.Name = Class521.smethod_0(23811);
			this.buttonX_4.Size = new Size(32, 30);
			this.buttonX_4.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_4.TabIndex = 5;
			this.buttonX_4.Tooltip = Class521.smethod_0(22208);
			this.buttonX_5.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_5.ColorTable = eButtonColor.Flat;
			this.buttonX_5.Image = Class375.DrawROpen;
			this.buttonX_5.Location = new Point(207, 8);
			this.buttonX_5.Name = Class521.smethod_0(23832);
			this.buttonX_5.Size = new Size(32, 30);
			this.buttonX_5.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_5.TabIndex = 4;
			this.buttonX_5.Tooltip = Class521.smethod_0(23849);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(310, 46);
			base.Controls.Add(this.buttonX_5);
			base.Controls.Add(this.buttonX_4);
			base.Controls.Add(this.buttonX_3);
			base.Controls.Add(this.buttonX_2);
			base.Controls.Add(this.buttonX_1);
			base.Controls.Add(this.buttonX_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(23874);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(23658);
			base.TopMost = true;
			base.ResumeLayout(false);
		}

		// Token: 0x040006DD RID: 1757
		private IContainer icontainer_0;

		// Token: 0x040006DE RID: 1758
		private ButtonX buttonX_0;

		// Token: 0x040006DF RID: 1759
		private ButtonX buttonX_1;

		// Token: 0x040006E0 RID: 1760
		private ButtonX buttonX_2;

		// Token: 0x040006E1 RID: 1761
		private ButtonX buttonX_3;

		// Token: 0x040006E2 RID: 1762
		private ButtonX buttonX_4;

		// Token: 0x040006E3 RID: 1763
		private ButtonX buttonX_5;
	}
}
