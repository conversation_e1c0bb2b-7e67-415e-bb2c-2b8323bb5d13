﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns9
{
	// Token: 0x020002CF RID: 719
	internal sealed partial class Form22 : Form
	{
		// Token: 0x06002032 RID: 8242 RVA: 0x000E4BF8 File Offset: 0x000E2DF8
		public Form22()
		{
			this.method_0();
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			Base.UI.smethod_54(this);
		}

		// Token: 0x06002033 RID: 8243 RVA: 0x000E4C54 File Offset: 0x000E2E54
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.textBox_0.Text.Trim() == Class521.smethod_0(1449))
			{
				MessageBox.Show(Class521.smethod_0(95361));
				this.textBox_0.Focus();
			}
			else if (this.list_0.Contains(this.textBox_0.Text.Trim()))
			{
				MessageBox.Show(Class521.smethod_0(95394));
				this.textBox_0.Focus();
			}
			else
			{
				this.bool_0 = true;
				base.Close();
			}
		}

		// Token: 0x06002034 RID: 8244 RVA: 0x0000D180 File Offset: 0x0000B380
		private void button_1_Click(object sender, EventArgs e)
		{
			this.bool_0 = false;
			base.Close();
		}

		// Token: 0x170005B0 RID: 1456
		// (get) Token: 0x06002035 RID: 8245 RVA: 0x000E4CEC File Offset: 0x000E2EEC
		public string GroupName
		{
			get
			{
				return this.textBox_0.Text.Trim();
			}
		}

		// Token: 0x06002036 RID: 8246 RVA: 0x0000D191 File Offset: 0x0000B391
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002037 RID: 8247 RVA: 0x000E4D10 File Offset: 0x000E2F10
		private void method_0()
		{
			this.label_0 = new Label();
			this.textBox_0 = new TextBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(22, 27);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(127, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(95459);
			this.textBox_0.Location = new Point(25, 53);
			this.textBox_0.Margin = new Padding(3, 2, 3, 2);
			this.textBox_0.Name = Class521.smethod_0(95492);
			this.textBox_0.Size = new Size(321, 25);
			this.textBox_0.TabIndex = 1;
			this.button_0.Location = new Point(142, 108);
			this.button_0.Margin = new Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(95505);
			this.button_0.Size = new Size(99, 30);
			this.button_0.TabIndex = 2;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(247, 108);
			this.button_1.Margin = new Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(95518);
			this.button_1.Size = new Size(99, 30);
			this.button_1.TabIndex = 2;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.ButtonFace;
			base.ClientSize = new Size(377, 149);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.Margin = new Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(95535);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(95556);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000FC6 RID: 4038
		public bool bool_0;

		// Token: 0x04000FC7 RID: 4039
		public List<string> list_0 = new List<string>();

		// Token: 0x04000FC8 RID: 4040
		private IContainer icontainer_0;

		// Token: 0x04000FC9 RID: 4041
		private Label label_0;

		// Token: 0x04000FCA RID: 4042
		private TextBox textBox_0;

		// Token: 0x04000FCB RID: 4043
		private Button button_0;

		// Token: 0x04000FCC RID: 4044
		private Button button_1;
	}
}
