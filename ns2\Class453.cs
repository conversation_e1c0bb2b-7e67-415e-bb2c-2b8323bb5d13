﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using NPOI.HSSF.UserModel;
using NPOI.POIFS.FileSystem;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using ns18;

namespace ns2
{
	// Token: 0x02000344 RID: 836
	internal sealed class Class453
	{
		// Token: 0x0600232A RID: 9002 RVA: 0x000F7228 File Offset: 0x000F5428
		public Class453(string string_5)
		{
			this.string_2 = Path.GetExtension(string_5).ToLower().Substring(1);
			if (this.string_2 != Class521.smethod_0(104525) && this.string_2 != Class521.smethod_0(104530))
			{
				throw new Exception(Class521.smethod_0(104539));
			}
			this.FilePath = string_5;
		}

		// Token: 0x0600232B RID: 9003 RVA: 0x000F729C File Offset: 0x000F549C
		private IWorkbook method_0(FileStream fileStream_1, string string_5)
		{
			IWorkbook result = null;
			try
			{
				if (this.string_2 == Class521.smethod_0(104525))
				{
					result = new HSSFWorkbook(fileStream_1);
				}
				else
				{
					result = new XSSFWorkbook(fileStream_1);
				}
			}
			catch (Exception ex)
			{
				this.method_4();
				if (ex is NotOLE2FileException)
				{
					throw ex;
				}
			}
			return result;
		}

		// Token: 0x0600232C RID: 9004 RVA: 0x000F72FC File Offset: 0x000F54FC
		public string[] method_1()
		{
			return this.method_2(this.FilePath);
		}

		// Token: 0x0600232D RID: 9005 RVA: 0x000F731C File Offset: 0x000F551C
		public string[] method_2(string string_5)
		{
			List<List<string>> list = this.method_3(string_5);
			List<string> list2 = new List<string>();
			foreach (List<string> list3 in list)
			{
				Class453.Class454 @class = new Class453.Class454();
				@class.string_0 = Class521.smethod_0(1449);
				list3.ForEach(new Action<string>(@class.method_0));
				if (@class.string_0.Length > 0)
				{
					@class.string_0 = @class.string_0.Substring(0, @class.string_0.Length - 1);
				}
				list2.Add(@class.string_0);
			}
			return list2.ToArray();
		}

		// Token: 0x0600232E RID: 9006 RVA: 0x000F73DC File Offset: 0x000F55DC
		private List<List<string>> method_3(string string_5)
		{
			List<List<string>> list = null;
			List<List<string>> result;
			if (!File.Exists(string_5))
			{
				result = null;
			}
			else
			{
				List<List<string>> list2;
				using (this.fileStream_0 = File.OpenRead(string_5))
				{
					IWorkbook workbook = this.method_0(this.fileStream_0, string_5);
					if (workbook == null)
					{
						list2 = null;
						goto IL_1F9;
					}
					this.iworkbook_0 = workbook;
					list = new List<List<string>>();
					if (workbook.NumberOfSheets > 0)
					{
						ISheet sheetAt = workbook.GetSheetAt(0);
						int? num = null;
						int? num2 = null;
						for (int i = 0; i < sheetAt.PhysicalNumberOfRows; i++)
						{
							IRow row = sheetAt.GetRow(i);
							if (row != null)
							{
								if (num == null)
								{
									num = new int?((int)row.FirstCellNum);
								}
								else if (num2 == null)
								{
									num2 = new int?((int)row.FirstCellNum);
								}
								else if ((int)row.FirstCellNum < num2.Value)
								{
									num2 = new int?((int)row.FirstCellNum);
								}
								List<string> list3 = new List<string>();
								for (int j = 0; j < row.Cells.Count; j++)
								{
									if (row.Cells[j].CellType == CellType.Numeric && DateUtil.IsCellDateFormatted(row.Cells[j]))
									{
										DateTime dateCellValue = row.Cells[j].DateCellValue;
										if (dateCellValue.Year < 2000)
										{
											list3.Add(dateCellValue.ToString(Class521.smethod_0(104592)));
										}
										else
										{
											list3.Add(dateCellValue.ToString(Class521.smethod_0(104605)));
										}
									}
									else
									{
										list3.Add(row.Cells[j].ToString());
									}
								}
								list.Add(list3);
							}
						}
						if (num != null && num2 != null && num.Value > num2.Value)
						{
							throw new Exception0(Class521.smethod_0(104634));
						}
					}
				}
				return list;
				IL_1F9:
				result = list2;
			}
			return result;
		}

		// Token: 0x0600232F RID: 9007 RVA: 0x0000DE69 File Offset: 0x0000C069
		public void method_4()
		{
			this.vmethod_0(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x06002330 RID: 9008 RVA: 0x000F7608 File Offset: 0x000F5808
		~Class453()
		{
			this.vmethod_0(false);
		}

		// Token: 0x06002331 RID: 9009 RVA: 0x000F7638 File Offset: 0x000F5838
		protected void vmethod_0(bool bool_2)
		{
			if (!this.bool_1)
			{
				if (bool_2)
				{
					this.bool_0 = true;
					if (this.fileStream_0 != null)
					{
						this.fileStream_0.Dispose();
					}
					if (this.iworkbook_0 != null)
					{
						this.iworkbook_0 = null;
					}
				}
				this.bool_1 = true;
				this.bool_0 = false;
			}
		}

		// Token: 0x17000608 RID: 1544
		// (get) Token: 0x06002332 RID: 9010 RVA: 0x000F768C File Offset: 0x000F588C
		// (set) Token: 0x06002333 RID: 9011 RVA: 0x0000DE7A File Offset: 0x0000C07A
		public string FilePath { get; set; }

		// Token: 0x17000609 RID: 1545
		// (get) Token: 0x06002334 RID: 9012 RVA: 0x000F76A4 File Offset: 0x000F58A4
		// (set) Token: 0x06002335 RID: 9013 RVA: 0x0000DE85 File Offset: 0x0000C085
		public string[] LinesInCSVFormat
		{
			get
			{
				if (this.string_4 == null)
				{
					this.string_4 = this.method_1();
				}
				return this.string_4;
			}
			set
			{
				this.string_4 = value;
			}
		}

		// Token: 0x040010FB RID: 4347
		private const string string_0 = "xls";

		// Token: 0x040010FC RID: 4348
		private const string string_1 = "xlsx";

		// Token: 0x040010FD RID: 4349
		private string string_2;

		// Token: 0x040010FE RID: 4350
		private FileStream fileStream_0;

		// Token: 0x040010FF RID: 4351
		private IWorkbook iworkbook_0;

		// Token: 0x04001100 RID: 4352
		private bool bool_0;

		// Token: 0x04001101 RID: 4353
		private bool bool_1;

		// Token: 0x04001102 RID: 4354
		[CompilerGenerated]
		private string string_3;

		// Token: 0x04001103 RID: 4355
		private string[] string_4;

		// Token: 0x02000345 RID: 837
		[CompilerGenerated]
		private sealed class Class454
		{
			// Token: 0x06002337 RID: 9015 RVA: 0x0000DE90 File Offset: 0x0000C090
			internal void method_0(string string_1)
			{
				this.string_0 = this.string_0 + string_1 + Class521.smethod_0(4736);
			}

			// Token: 0x04001104 RID: 4356
			public string string_0;
		}
	}
}
