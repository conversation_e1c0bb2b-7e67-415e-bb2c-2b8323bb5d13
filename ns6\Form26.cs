﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using mshtml;
using ns18;
using ns2;
using ns20;
using ns25;
using SHDocVw;

namespace ns6
{
	// Token: 0x0200037A RID: 890
	internal sealed partial class Form26 : Form
	{
		// Token: 0x060024D1 RID: 9425 RVA: 0x0000E4BA File Offset: 0x0000C6BA
		public Form26()
		{
			this.method_17();
			this.webBrowser_1.AllowWebBrowserDrop = false;
			(this.webBrowser_1.ActiveXInstance as SHDocVw.WebBrowser).NewWindow2 += new DWebBrowserEvents2_NewWindow2EventHandler(this, (UIntPtr)ldftn(method_1));
		}

		// Token: 0x060024D2 RID: 9426 RVA: 0x000FEF34 File Offset: 0x000FD134
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.icontainer_0 != null)
				{
					this.icontainer_0.Dispose();
				}
				try
				{
					(this.webBrowser_1.ActiveXInstance as SHDocVw.WebBrowser).NewWindow2 -= new DWebBrowserEvents2_NewWindow2EventHandler(this, (UIntPtr)ldftn(method_1));
				}
				catch
				{
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x060024D3 RID: 9427 RVA: 0x000FEF98 File Offset: 0x000FD198
		public string method_0()
		{
			string result;
			try
			{
				HtmlElementCollection elementsByTagName = this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108620));
				string text = Class521.smethod_0(1449);
				foreach (object obj in elementsByTagName)
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					text = text + htmlElement.GetAttribute(Class521.smethod_0(9713)) + Class521.smethod_0(3636);
					if (htmlElement.GetAttribute(Class521.smethod_0(9713)) == Class521.smethod_0(108629) && htmlElement.GetAttribute(Class521.smethod_0(108638)) == Class521.smethod_0(108647))
					{
						htmlElement.InvokeMember(Class521.smethod_0(108664));
						result = string.Empty;
						goto IL_EF;
					}
				}
				result = string.Format(Class521.smethod_0(108673), text);
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_EF:
			return result;
		}

		// Token: 0x060024D4 RID: 9428 RVA: 0x0000E4F7 File Offset: 0x0000C6F7
		private void method_1(ref object object_0, ref bool bool_0)
		{
			object_0 = this.webBrowser_0.ActiveXInstance;
		}

		// Token: 0x060024D5 RID: 9429 RVA: 0x0000E508 File Offset: 0x0000C708
		[Obsolete]
		public void method_2()
		{
			this.webBrowser_1.Refresh();
		}

		// Token: 0x060024D6 RID: 9430 RVA: 0x0000E517 File Offset: 0x0000C717
		public void method_3(string string_0)
		{
			this.webBrowser_1.Navigate(string_0);
		}

		// Token: 0x060024D7 RID: 9431 RVA: 0x000FF0B8 File Offset: 0x000FD2B8
		private HtmlElement method_4()
		{
			HtmlElement result;
			foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108734)))
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				if (htmlElement.GetAttribute(Class521.smethod_0(108739)).Contains(Class521.smethod_0(108744)))
				{
					result = htmlElement;
					goto IL_74;
				}
			}
			return null;
			IL_74:
			return result;
		}

		// Token: 0x060024D8 RID: 9432 RVA: 0x000FF150 File Offset: 0x000FD350
		public Bitmap method_5(HtmlElement htmlElement_0)
		{
			IHTMLImgElement ihtmlimgElement = (IHTMLImgElement)htmlElement_0.DomElement;
			Form26.Interface5 @interface = (Form26.Interface5)ihtmlimgElement;
			Bitmap bitmap = new Bitmap(ihtmlimgElement.width, ihtmlimgElement.height);
			Graphics graphics = Graphics.FromImage(bitmap);
			IntPtr hdc = graphics.GetHdc();
			@interface.imethod_0(hdc);
			graphics.ReleaseHdc(hdc);
			return bitmap;
		}

		// Token: 0x060024D9 RID: 9433
		[DllImport("urlmon.dll")]
		[return: MarshalAs(UnmanagedType.Error)]
		private static extern int CoInternetSetFeatureEnabled(int int_2, [MarshalAs(UnmanagedType.U4)] int int_3, bool bool_0);

		// Token: 0x060024DA RID: 9434 RVA: 0x0000E527 File Offset: 0x0000C727
		private static void smethod_0()
		{
			Form26.CoInternetSetFeatureEnabled(21, 2, true);
		}

		// Token: 0x060024DB RID: 9435 RVA: 0x000FF1A0 File Offset: 0x000FD3A0
		public string method_6(string string_0, string string_1)
		{
			string result;
			try
			{
				Form26.smethod_0();
				bool flag = false;
				bool flag2 = false;
				bool flag3 = false;
				HtmlElement htmlElement_ = this.method_4();
				foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108620)))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if (htmlElement.GetAttribute(Class521.smethod_0(9713)) == Class521.smethod_0(108765))
					{
						Thread.Sleep(200);
						htmlElement.InnerText = string_0;
						flag = true;
					}
					else if (htmlElement.GetAttribute(Class521.smethod_0(9713)) == Class521.smethod_0(108774))
					{
						Thread.Sleep(200);
						htmlElement.InnerText = string_1;
						flag2 = true;
					}
					else if (htmlElement.GetAttribute(Class521.smethod_0(9713)) == Class521.smethod_0(108787))
					{
						Thread.Sleep(200);
						Bitmap bitmap_ = this.method_5(htmlElement_);
						Class485 @class = Class486.smethod_0(Class521.smethod_0(108800));
						string levenFeature = Class491.levenFeature;
						File.WriteAllText(Class521.smethod_0(108809), levenFeature);
						@class.vmethod_1(Class521.smethod_0(108809));
						string innerText = @class.vmethod_5(bitmap_);
						htmlElement.InnerText = innerText;
						flag3 = true;
					}
					else if (htmlElement.GetAttribute(Class521.smethod_0(108638)) == Class521.smethod_0(108822))
					{
						if (flag && flag2 && flag3)
						{
							Thread.Sleep(200);
							htmlElement.InvokeMember(Class521.smethod_0(108664));
							result = string.Empty;
							goto IL_1D5;
						}
						result = Class521.smethod_0(108843);
						goto IL_1D5;
					}
				}
				result = Class521.smethod_0(108904);
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_1D5:
			return result;
		}

		// Token: 0x060024DC RID: 9436 RVA: 0x000FF3C0 File Offset: 0x000FD5C0
		public int method_7(HtmlElement htmlElement_0)
		{
			int num = htmlElement_0.OffsetRectangle.Left;
			HtmlElement offsetParent = htmlElement_0.OffsetParent;
			while (offsetParent != null)
			{
				num += offsetParent.OffsetRectangle.Left;
				offsetParent = offsetParent.OffsetParent;
			}
			return num;
		}

		// Token: 0x060024DD RID: 9437 RVA: 0x000FF40C File Offset: 0x000FD60C
		public int method_8(HtmlElement htmlElement_0)
		{
			int num = htmlElement_0.OffsetRectangle.Top;
			HtmlElement offsetParent = htmlElement_0.OffsetParent;
			while (offsetParent != null)
			{
				num += offsetParent.OffsetRectangle.Top;
				offsetParent = offsetParent.OffsetParent;
			}
			return num;
		}

		// Token: 0x060024DE RID: 9438 RVA: 0x000FF458 File Offset: 0x000FD658
		public string method_9()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108949)))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if (htmlElement.InnerText.Contains(Class521.smethod_0(108958)))
					{
						result = Class521.smethod_0(108958);
						goto IL_A2;
					}
					if (htmlElement.InnerText.Contains(Class521.smethod_0(108991)))
					{
						result = Class521.smethod_0(109008);
						goto IL_A2;
					}
				}
				result = string.Empty;
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_A2:
			return result;
		}

		// Token: 0x060024DF RID: 9439 RVA: 0x000FF52C File Offset: 0x000FD72C
		public string method_10()
		{
			string result;
			try
			{
				using (IEnumerator enumerator = this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108949)).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (((HtmlElement)enumerator.Current).InnerText.Contains(Class521.smethod_0(109065)))
						{
							result = Class521.smethod_0(109065);
							goto IL_7C;
						}
					}
				}
				result = string.Empty;
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_7C:
			return result;
		}

		// Token: 0x060024E0 RID: 9440 RVA: 0x000FF5D8 File Offset: 0x000FD7D8
		public WebBrowserReadyState method_11(int int_2)
		{
			WebBrowserReadyState readyState;
			if (int_2 == 1)
			{
				readyState = this.webBrowser_1.ReadyState;
			}
			else
			{
				if (int_2 != 2)
				{
					throw new Exception(Class521.smethod_0(109086));
				}
				readyState = this.webBrowser_0.ReadyState;
			}
			return readyState;
		}

		// Token: 0x060024E1 RID: 9441 RVA: 0x000FF61C File Offset: 0x000FD81C
		public string method_12()
		{
			string result;
			foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(109131)))
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				if (htmlElement.InnerText.Contains(Class521.smethod_0(109140)))
				{
					htmlElement.InvokeMember(Class521.smethod_0(108664));
					result = Class521.smethod_0(1449);
					goto IL_8D;
				}
			}
			return Class521.smethod_0(109173);
			IL_8D:
			return result;
		}

		// Token: 0x060024E2 RID: 9442 RVA: 0x000FF6D0 File Offset: 0x000FD8D0
		public bool method_13(object object_0)
		{
			bool result;
			if ((object_0 as System.Windows.Forms.WebBrowser).ReadyState == WebBrowserReadyState.Complete)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060024E3 RID: 9443 RVA: 0x000FF6F4 File Offset: 0x000FD8F4
		public string method_14(DateTime dateTime_0)
		{
			HtmlElementCollection elementsByTagName = this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108620));
			bool flag = false;
			string value = dateTime_0.ToString(Class521.smethod_0(1702));
			string text = Class521.smethod_0(1449);
			foreach (object obj in elementsByTagName)
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				text = text + htmlElement.GetAttribute(Class521.smethod_0(9713)) + Class521.smethod_0(3636);
				if (htmlElement.GetAttribute(Class521.smethod_0(9713)) == Class521.smethod_0(109202))
				{
					Thread.Sleep(200);
					htmlElement.SetAttribute(Class521.smethod_0(108638), value);
					flag = true;
					break;
				}
			}
			string result;
			if (!flag)
			{
				result = string.Format(Class521.smethod_0(109215), text);
			}
			else
			{
				string text2;
				try
				{
					foreach (object obj2 in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(108620)))
					{
						HtmlElement htmlElement2 = (HtmlElement)obj2;
						if (htmlElement2.GetAttribute(Class521.smethod_0(108638)) == Class521.smethod_0(109292))
						{
							Thread.Sleep(200);
							htmlElement2.InvokeMember(Class521.smethod_0(108664));
							text2 = Class521.smethod_0(1449);
							goto IL_1C0;
						}
					}
					text2 = Class521.smethod_0(109301);
				}
				catch (Exception ex)
				{
					MessageBox.Show(string.Format(Class521.smethod_0(109334), ex.Message));
					text2 = Class521.smethod_0(109301);
				}
				IL_1C0:
				result = text2;
			}
			return result;
		}

		// Token: 0x060024E4 RID: 9444 RVA: 0x000FF8F4 File Offset: 0x000FDAF4
		public string method_15()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(91172)))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if ((htmlElement.InnerText ?? Class521.smethod_0(1449)).Contains(Class521.smethod_0(109371)))
					{
						htmlElement.InvokeMember(Class521.smethod_0(108664));
						result = Class521.smethod_0(1449);
						goto IL_C5;
					}
				}
				result = Class521.smethod_0(109388);
			}
			catch (Exception ex)
			{
				MessageBox.Show(string.Format(Class521.smethod_0(109421), ex.Message));
				result = Class521.smethod_0(109388);
			}
			IL_C5:
			return result;
		}

		// Token: 0x060024E5 RID: 9445 RVA: 0x000FF9EC File Offset: 0x000FDBEC
		public string method_16()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowser_1.Document.GetElementsByTagName(Class521.smethod_0(91172)))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if ((htmlElement.InnerText ?? Class521.smethod_0(1449)).Contains(Class521.smethod_0(109458)))
					{
						htmlElement.InvokeMember(Class521.smethod_0(108664));
						result = Class521.smethod_0(1449);
						goto IL_C5;
					}
				}
				result = Class521.smethod_0(109475);
			}
			catch (Exception ex)
			{
				MessageBox.Show(string.Format(Class521.smethod_0(109421), ex.Message));
				result = Class521.smethod_0(109475);
			}
			IL_C5:
			return result;
		}

		// Token: 0x17000641 RID: 1601
		// (get) Token: 0x060024E6 RID: 9446 RVA: 0x000FFAE4 File Offset: 0x000FDCE4
		public System.Windows.Forms.WebBrowser WebBrowserMain
		{
			get
			{
				return this.webBrowser_1;
			}
		}

		// Token: 0x17000642 RID: 1602
		// (get) Token: 0x060024E7 RID: 9447 RVA: 0x000FFAFC File Offset: 0x000FDCFC
		public System.Windows.Forms.WebBrowser WebBrowserQryRslt
		{
			get
			{
				return this.webBrowser_0;
			}
		}

		// Token: 0x060024E8 RID: 9448 RVA: 0x000FFB14 File Offset: 0x000FDD14
		private void method_17()
		{
			this.toolStrip_0 = new ToolStrip();
			this.splitContainer_0 = new SplitContainer();
			this.webBrowser_0 = new System.Windows.Forms.WebBrowser();
			this.webBrowser_1 = new System.Windows.Forms.WebBrowser();
			this.splitContainer_0.Panel1.SuspendLayout();
			this.splitContainer_0.Panel2.SuspendLayout();
			this.splitContainer_0.SuspendLayout();
			base.SuspendLayout();
			this.toolStrip_0.ImageScalingSize = new Size(20, 20);
			this.toolStrip_0.Location = new Point(0, 0);
			this.toolStrip_0.Name = Class521.smethod_0(96280);
			this.toolStrip_0.Size = new Size(1390, 25);
			this.toolStrip_0.TabIndex = 0;
			this.toolStrip_0.Text = Class521.smethod_0(96280);
			this.splitContainer_0.Dock = DockStyle.Fill;
			this.splitContainer_0.Location = new Point(0, 25);
			this.splitContainer_0.Margin = new Padding(4, 4, 4, 4);
			this.splitContainer_0.Name = Class521.smethod_0(109508);
			this.splitContainer_0.Panel1.Controls.Add(this.webBrowser_0);
			this.splitContainer_0.Panel2.Controls.Add(this.webBrowser_1);
			this.splitContainer_0.Size = new Size(1390, 787);
			this.splitContainer_0.SplitterDistance = 653;
			this.splitContainer_0.SplitterWidth = 5;
			this.splitContainer_0.TabIndex = 1;
			this.webBrowser_0.Dock = DockStyle.Fill;
			this.webBrowser_0.Location = new Point(0, 0);
			this.webBrowser_0.Margin = new Padding(4, 4, 4, 4);
			this.webBrowser_0.MinimumSize = new Size(27, 25);
			this.webBrowser_0.Name = Class521.smethod_0(109529);
			this.webBrowser_0.Size = new Size(653, 787);
			this.webBrowser_0.TabIndex = 0;
			this.webBrowser_1.Dock = DockStyle.Fill;
			this.webBrowser_1.Location = new Point(0, 0);
			this.webBrowser_1.Margin = new Padding(4, 4, 4, 4);
			this.webBrowser_1.MinimumSize = new Size(27, 25);
			this.webBrowser_1.Name = Class521.smethod_0(109554);
			this.webBrowser_1.Size = new Size(732, 787);
			this.webBrowser_1.TabIndex = 0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(1390, 812);
			base.Controls.Add(this.splitContainer_0);
			base.Controls.Add(this.toolStrip_0);
			base.Margin = new Padding(4, 4, 4, 4);
			base.Name = Class521.smethod_0(109575);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(109575);
			this.splitContainer_0.Panel1.ResumeLayout(false);
			this.splitContainer_0.Panel2.ResumeLayout(false);
			this.splitContainer_0.ResumeLayout(false);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040011C5 RID: 4549
		private const int int_0 = 21;

		// Token: 0x040011C6 RID: 4550
		private const int int_1 = 2;

		// Token: 0x040011C7 RID: 4551
		private IContainer icontainer_0;

		// Token: 0x040011C8 RID: 4552
		private ToolStrip toolStrip_0;

		// Token: 0x040011C9 RID: 4553
		private SplitContainer splitContainer_0;

		// Token: 0x040011CA RID: 4554
		private System.Windows.Forms.WebBrowser webBrowser_0;

		// Token: 0x040011CB RID: 4555
		private System.Windows.Forms.WebBrowser webBrowser_1;

		// Token: 0x0200037B RID: 891
		[InterfaceType(1)]
		[Guid("3050F669-98B5-11CF-BB82-00AA00BDCE0B")]
		[ComImport]
		private interface Interface5
		{
			// Token: 0x060024E9 RID: 9449
			void imethod_0(IntPtr intptr_0);

			// Token: 0x060024EA RID: 9450
			void imethod_1(string string_0, IntPtr intptr_0);
		}
	}
}
