﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.AdvTree;
using ns18;
using ns26;
using TEx;
using TEx.Util;

namespace ns11
{
	// Token: 0x0200008C RID: 140
	internal sealed partial class Form0 : Form
	{
		// Token: 0x06000492 RID: 1170 RVA: 0x00003F6F File Offset: 0x0000216F
		public Form0()
		{
			this.method_2();
			base.Icon = Class375.BookIcon;
			base.Shown += this.Form0_Shown;
		}

		// Token: 0x06000493 RID: 1171 RVA: 0x00003F9C File Offset: 0x0000219C
		private void Form0_Shown(object sender, EventArgs e)
		{
			this.textBox_0.SelectAll();
			this.textBox_0.Focus();
		}

		// Token: 0x06000494 RID: 1172 RVA: 0x000249DC File Offset: 0x00022BDC
		internal void method_0(ChtCtrl chtCtrl_1, List<string> list_1, Bitmap bitmap_0, string string_1 = null)
		{
			this.ChtCtrl = chtCtrl_1;
			this.Groups = list_1;
			this.CurrGroupPath = string_1;
			this.comboBox_0.DataSource = list_1;
			this.comboBox_0.SelectedIndex = 0;
			if (!string.IsNullOrEmpty(string_1))
			{
				this.comboBox_0.SelectedIndex = list_1.IndexOf(string_1);
			}
			this.pictureBox_0.Image = bitmap_0;
			DateTime date = this.ChtCtrl.SymbDataSet.CurrHisData.Date;
			this.label_5.Text = this.ChtCtrl.Symbol.CNName;
			this.label_6.Text = date.ToString(Class521.smethod_0(5644));
			this.textBox_0.Text = this.ChtCtrl.Symbol.CNName + date.ToString(Class521.smethod_0(5669));
		}

		// Token: 0x06000495 RID: 1173 RVA: 0x00024AC0 File Offset: 0x00022CC0
		internal void method_1(Node node_1, List<string> list_1)
		{
			this.Text = Class521.smethod_0(5686);
			this.BaoDianNodeToEdit = node_1;
			this.Groups = list_1;
			this.CurrGroupPath = node_1.Parent.FullPath;
			this.comboBox_0.DataSource = list_1;
			this.comboBox_0.SelectedIndex = 0;
			if (!string.IsNullOrEmpty(this.CurrGroupPath))
			{
				this.comboBox_0.SelectedIndex = list_1.IndexOf(this.CurrGroupPath);
			}
			BaoDian baoDian = node_1.Tag as BaoDian;
			this.pictureBox_0.Image = baoDian.ScreenShot;
			this.textBox_0.Text = baoDian.Name;
			DateTime symbolTime = baoDian.SymbolTime;
			this.label_6.Text = symbolTime.ToString(Class521.smethod_0(5644));
			StkSymbol stkSymbol = SymbMgr.smethod_3(baoDian.SymbolID);
			if (stkSymbol != null)
			{
				this.label_5.Text = stkSymbol.CNName;
			}
			this.textBox_1.Text = baoDian.Note;
		}

		// Token: 0x06000496 RID: 1174 RVA: 0x00024BBC File Offset: 0x00022DBC
		private void button_1_Click(object sender, EventArgs e)
		{
			string group = this.comboBox_0.Text.Trim();
			string text = this.textBox_0.Text.Trim();
			if (string.IsNullOrEmpty(text))
			{
				MessageBox.Show(Class521.smethod_0(5703));
				this.textBox_0.Focus();
			}
			else
			{
				string note = this.textBox_1.Text.Trim();
				if (this.BaoDianNodeToEdit == null)
				{
					BaoDian baoDian = new BaoDian();
					baoDian.Name = text;
					baoDian.UID = Utility.GetUniqueString(20);
					baoDian.ScreenShot = (this.pictureBox_0.Image as Bitmap);
					baoDian.Group = this.comboBox_0.Text.Trim();
					baoDian.Note = this.textBox_1.Text.Trim();
					baoDian.SymbolID = this.ChtCtrl.Symbol.ID;
					baoDian.PeriodType = this.ChtCtrl.PeriodType;
					baoDian.PeriodUnit = this.ChtCtrl.PeriodUnits;
					baoDian.SymbolTime = this.ChtCtrl.SymbDataSet.CurrHisData.Date;
					baoDian.CreateTime = DateTime.Now;
					Action<BaoDian> action_ = BaoDianMgr.action_0;
					if (action_ != null)
					{
						action_(baoDian);
					}
				}
				else
				{
					this.BaoDianNodeToEdit.Text = text;
					BaoDian baoDian2 = this.BaoDianNodeToEdit.Tag as BaoDian;
					baoDian2.Name = text;
					baoDian2.Group = group;
					baoDian2.Note = note;
					Action<Node> action_2 = BaoDianMgr.action_1;
					if (action_2 != null)
					{
						action_2(this.BaoDianNodeToEdit);
					}
				}
				BaoDianMgr.smethod_8();
				base.Close();
			}
		}

		// Token: 0x170000F2 RID: 242
		// (get) Token: 0x06000497 RID: 1175 RVA: 0x00024D50 File Offset: 0x00022F50
		// (set) Token: 0x06000498 RID: 1176 RVA: 0x00003FB7 File Offset: 0x000021B7
		internal ChtCtrl ChtCtrl { get; set; }

		// Token: 0x170000F3 RID: 243
		// (get) Token: 0x06000499 RID: 1177 RVA: 0x00024D68 File Offset: 0x00022F68
		// (set) Token: 0x0600049A RID: 1178 RVA: 0x00003FC2 File Offset: 0x000021C2
		public List<string> Groups { get; set; }

		// Token: 0x170000F4 RID: 244
		// (get) Token: 0x0600049B RID: 1179 RVA: 0x00024D80 File Offset: 0x00022F80
		// (set) Token: 0x0600049C RID: 1180 RVA: 0x00003FCD File Offset: 0x000021CD
		public string CurrGroupPath { get; set; }

		// Token: 0x170000F5 RID: 245
		// (get) Token: 0x0600049D RID: 1181 RVA: 0x00024D98 File Offset: 0x00022F98
		// (set) Token: 0x0600049E RID: 1182 RVA: 0x00003FD8 File Offset: 0x000021D8
		public Node BaoDianNodeToEdit { get; private set; }

		// Token: 0x0600049F RID: 1183 RVA: 0x00003FE3 File Offset: 0x000021E3
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060004A0 RID: 1184 RVA: 0x00024DB0 File Offset: 0x00022FB0
		private void method_2()
		{
			this.pictureBox_0 = new PictureBox();
			this.textBox_0 = new TextBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.textBox_1 = new TextBox();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.label_5 = new Label();
			this.label_6 = new Label();
			this.groupBox_0 = new GroupBox();
			this.comboBox_0 = new ComboBox();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			this.groupBox_0.SuspendLayout();
			base.SuspendLayout();
			this.pictureBox_0.Dock = DockStyle.Fill;
			this.pictureBox_0.Location = new Point(3, 21);
			this.pictureBox_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(367, 263);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.Zoom;
			this.pictureBox_0.TabIndex = 5;
			this.pictureBox_0.TabStop = false;
			this.textBox_0.Location = new Point(83, 111);
			this.textBox_0.Margin = new System.Windows.Forms.Padding(4);
			this.textBox_0.Name = Class521.smethod_0(5749);
			this.textBox_0.Size = new Size(264, 25);
			this.textBox_0.TabIndex = 1;
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(675, 403);
			this.button_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(5766);
			this.button_0.Size = new Size(117, 34);
			this.button_0.TabIndex = 6;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(543, 403);
			this.button_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(5792);
			this.button_1.Size = new Size(117, 34);
			this.button_1.TabIndex = 5;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.textBox_1.Location = new Point(83, 153);
			this.textBox_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_1.Multiline = true;
			this.textBox_1.Name = Class521.smethod_0(5810);
			this.textBox_1.ScrollBars = ScrollBars.Vertical;
			this.textBox_1.Size = new Size(316, 211);
			this.textBox_1.TabIndex = 2;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(25, 154);
			this.label_0.Name = Class521.smethod_0(5827);
			this.label_0.Size = new Size(52, 15);
			this.label_0.TabIndex = 6;
			this.label_0.Text = Class521.smethod_0(5836);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(25, 115);
			this.label_1.Name = Class521.smethod_0(5849);
			this.label_1.Size = new Size(52, 15);
			this.label_1.TabIndex = 7;
			this.label_1.Text = Class521.smethod_0(5858);
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(25, 75);
			this.label_2.Name = Class521.smethod_0(5871);
			this.label_2.Size = new Size(52, 15);
			this.label_2.TabIndex = 8;
			this.label_2.Text = Class521.smethod_0(5880);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(25, 36);
			this.label_3.Name = Class521.smethod_0(5893);
			this.label_3.Size = new Size(52, 15);
			this.label_3.TabIndex = 15;
			this.label_3.Text = Class521.smethod_0(5902);
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(422, 36);
			this.label_4.Name = Class521.smethod_0(5915);
			this.label_4.Size = new Size(82, 15);
			this.label_4.TabIndex = 17;
			this.label_4.Text = Class521.smethod_0(5924);
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(80, 36);
			this.label_5.Name = Class521.smethod_0(5945);
			this.label_5.Size = new Size(95, 15);
			this.label_5.TabIndex = 18;
			this.label_5.Text = Class521.smethod_0(5945);
			this.label_6.AutoSize = true;
			this.label_6.Location = new Point(510, 36);
			this.label_6.Name = Class521.smethod_0(5962);
			this.label_6.Size = new Size(87, 15);
			this.label_6.TabIndex = 19;
			this.label_6.Text = Class521.smethod_0(5962);
			this.groupBox_0.Controls.Add(this.pictureBox_0);
			this.groupBox_0.Location = new Point(422, 77);
			this.groupBox_0.Name = Class521.smethod_0(5979);
			this.groupBox_0.Size = new Size(373, 287);
			this.groupBox_0.TabIndex = 20;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(6004);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(83, 72);
			this.comboBox_0.Name = Class521.smethod_0(6017);
			this.comboBox_0.Size = new Size(264, 23);
			this.comboBox_0.TabIndex = 21;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(822, 454);
			base.Controls.Add(this.comboBox_0);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.label_6);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.textBox_1);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_2);
			this.DoubleBuffered = true;
			base.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(6038);
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(6059);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			this.groupBox_0.ResumeLayout(false);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040001C0 RID: 448
		[CompilerGenerated]
		private ChtCtrl chtCtrl_0;

		// Token: 0x040001C1 RID: 449
		[CompilerGenerated]
		private List<string> list_0;

		// Token: 0x040001C2 RID: 450
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040001C3 RID: 451
		[CompilerGenerated]
		private Node node_0;

		// Token: 0x040001C4 RID: 452
		private IContainer icontainer_0;

		// Token: 0x040001C5 RID: 453
		private PictureBox pictureBox_0;

		// Token: 0x040001C6 RID: 454
		private TextBox textBox_0;

		// Token: 0x040001C7 RID: 455
		private Button button_0;

		// Token: 0x040001C8 RID: 456
		private Button button_1;

		// Token: 0x040001C9 RID: 457
		private TextBox textBox_1;

		// Token: 0x040001CA RID: 458
		private Label label_0;

		// Token: 0x040001CB RID: 459
		private Label label_1;

		// Token: 0x040001CC RID: 460
		private Label label_2;

		// Token: 0x040001CD RID: 461
		private Label label_3;

		// Token: 0x040001CE RID: 462
		private Label label_4;

		// Token: 0x040001CF RID: 463
		private Label label_5;

		// Token: 0x040001D0 RID: 464
		private Label label_6;

		// Token: 0x040001D1 RID: 465
		private GroupBox groupBox_0;

		// Token: 0x040001D2 RID: 466
		private ComboBox comboBox_0;
	}
}
