﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns13;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000073 RID: 115
	[Serializable]
	internal sealed class DrawLineDExt : DrawLine, ISerializable
	{
		// Token: 0x06000428 RID: 1064 RVA: 0x00003C6B File Offset: 0x00001E6B
		public DrawLineDExt()
		{
		}

		// Token: 0x06000429 RID: 1065 RVA: 0x00003C73 File Offset: 0x00001E73
		public DrawLineDExt(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5246);
			base.IsOneClickLoc = false;
		}

		// Token: 0x0600042A RID: 1066 RVA: 0x00003C9B File Offset: 0x00001E9B
		protected DrawLineDExt(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600042B RID: 1067 RVA: 0x00003CAC File Offset: 0x00001EAC
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600042C RID: 1068 RVA: 0x000235A4 File Offset: 0x000217A4
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			Class60 @class = DrawLine.smethod_0(chartCS_1, double_1, double_2, double_3, double_4);
			LineObj item = base.method_23(double_1, double_2, @class.X2, @class.Y2, string_5);
			list.Add(item);
			return list;
		}
	}
}
