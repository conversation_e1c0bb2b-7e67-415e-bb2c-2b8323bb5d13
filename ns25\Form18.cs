﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns28;
using ns7;
using TEx;

namespace ns25
{
	// Token: 0x020001F3 RID: 499
	internal sealed partial class Form18 : Form
	{
		// Token: 0x1400007C RID: 124
		// (add) Token: 0x06001392 RID: 5010 RVA: 0x00087F74 File Offset: 0x00086174
		// (remove) Token: 0x06001393 RID: 5011 RVA: 0x00087FAC File Offset: 0x000861AC
		public event Delegate13 QuickWndItemSelected
		{
			[CompilerGenerated]
			add
			{
				Delegate13 @delegate = this.delegate13_0;
				Delegate13 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate13 value2 = (Delegate13)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate13>(ref this.delegate13_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate13 @delegate = this.delegate13_0;
				Delegate13 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate13 value2 = (Delegate13)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate13>(ref this.delegate13_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001394 RID: 5012 RVA: 0x00087FE4 File Offset: 0x000861E4
		protected void method_0(EventArgs10 eventArgs10_0)
		{
			Delegate13 @delegate = this.delegate13_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs10_0);
			}
		}

		// Token: 0x06001395 RID: 5013 RVA: 0x00007ECB File Offset: 0x000060CB
		public Form18()
		{
			this.method_5();
			base.Load += this.Form18_Load;
			base.Shown += this.Form18_Shown;
		}

		// Token: 0x06001396 RID: 5014 RVA: 0x00007EFF File Offset: 0x000060FF
		public Form18(List<QuickWndItem> list_1, string string_1) : this(list_1, null, string_1)
		{
		}

		// Token: 0x06001397 RID: 5015 RVA: 0x00088008 File Offset: 0x00086208
		public Form18(List<QuickWndItem> list_1, IEnumerable<QuickWndItem> ienumerable_0, string string_1) : this()
		{
			this.method_3(list_1, ienumerable_0, string_1);
			this.dataGridView_0.ColumnHeadersVisible = false;
			this.dataGridView_0.RowHeadersVisible = false;
			this.dataGridView_0.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridView_0.Columns[0].Width = (int)Convert.ToInt16(Math.Ceiling(this.dataGridView_0.Width * 0.35m));
			this.dataGridView_0.Columns[1].Width = this.dataGridView_0.Width - this.dataGridView_0.Columns[0].Width;
			this.dataGridView_0.Columns[2].Visible = false;
			this.dataGridView_0.Columns[3].Visible = false;
			this.dataGridView_0.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.BottomLeft;
			this.dataGridView_0.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			this.dataGridView_0.AllowUserToAddRows = false;
			this.dataGridView_0.AllowUserToDeleteRows = false;
			this.dataGridView_0.MultiSelect = false;
			this.dataGridView_0.ReadOnly = true;
			this.dataGridView_0.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView_0.DefaultCellStyle.Font = new Font(Class521.smethod_0(7183), 8.6f, FontStyle.Regular);
			this.dataGridView_0.Rows[0].Selected = true;
			this.dataGridView_0.ScrollBars = ScrollBars.Vertical;
			this.dataGridView_0.DoubleClick += this.dataGridView_0_DoubleClick;
			this.textBox_0.TextChanged += this.textBox_0_TextChanged;
		}

		// Token: 0x06001398 RID: 5016 RVA: 0x000041B9 File Offset: 0x000023B9
		private void Form18_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x06001399 RID: 5017 RVA: 0x00007F0A File Offset: 0x0000610A
		private void Form18_Shown(object sender, EventArgs e)
		{
			this.textBox_0.Focus();
			this.textBox_0.ImeMode = ImeMode.Disable;
			this.method_1();
		}

		// Token: 0x0600139A RID: 5018 RVA: 0x00007F2C File Offset: 0x0000612C
		private void method_1()
		{
			this.textBox_0.SelectionStart = this.textBox_0.Text.Length;
			this.textBox_0.SelectionLength = 0;
		}

		// Token: 0x0600139B RID: 5019 RVA: 0x000881C4 File Offset: 0x000863C4
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (keyData == Keys.Escape)
			{
				base.Close();
			}
			else if (keyData == Keys.Return)
			{
				this.method_4();
			}
			else if (keyData == Keys.Up)
			{
				if (this.dataGridView_0.RowCount > 1)
				{
					int index = this.dataGridView_0.SelectedRows[0].Index;
					if (index > 0)
					{
						this.dataGridView_0.Rows[index - 1].Selected = true;
						this.dataGridView_0.DisplayedRowCount(false);
						if (this.dataGridView_0.FirstDisplayedScrollingRowIndex > 0 && index - 1 < this.dataGridView_0.FirstDisplayedScrollingRowIndex)
						{
							this.dataGridView_0.FirstDisplayedScrollingRowIndex--;
						}
						return true;
					}
				}
			}
			else if (keyData == Keys.Down && this.dataGridView_0.RowCount > 1)
			{
				int index2 = this.dataGridView_0.SelectedRows[0].Index;
				if (index2 < this.dataGridView_0.RowCount - 1)
				{
					this.dataGridView_0.Rows[index2 + 1].Selected = true;
					int num = this.dataGridView_0.DisplayedRowCount(false);
					if (index2 + 1 >= num)
					{
						this.dataGridView_0.FirstDisplayedScrollingRowIndex = index2 + 2 - num;
					}
					return true;
				}
				return true;
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x0600139C RID: 5020 RVA: 0x00088310 File Offset: 0x00086510
		private void textBox_0_TextChanged(object sender, EventArgs e)
		{
			if (string.IsNullOrEmpty(this.textBox_0.Text))
			{
				int count = 200;
				if (200 > this.list_0.Count)
				{
					count = this.list_0.Count;
				}
				this.bindingSource_0.DataSource = this.list_0.Take(count);
			}
			else
			{
				Form18.Class281 @class = new Form18.Class281();
				@class.string_0 = this.textBox_0.Text.Trim().ToLower();
				IEnumerable<QuickWndItem> dataSource = this.list_0.Where(new Func<QuickWndItem, bool>(@class.method_0));
				this.bindingSource_0.DataSource = dataSource;
			}
		}

		// Token: 0x0600139D RID: 5021 RVA: 0x00007F57 File Offset: 0x00006157
		public void method_2(List<QuickWndItem> list_1, string string_1)
		{
			this.method_3(list_1, null, string_1);
		}

		// Token: 0x0600139E RID: 5022 RVA: 0x000883B4 File Offset: 0x000865B4
		public void method_3(List<QuickWndItem> list_1, IEnumerable<QuickWndItem> ienumerable_0, string string_1)
		{
			Form18.Class282 @class = new Form18.Class282();
			@class.string_0 = string_1;
			this.list_0 = list_1;
			this.string_0 = @class.string_0;
			this.bindingSource_0 = new BindingSource();
			if (ienumerable_0 != null && ienumerable_0.Any<QuickWndItem>())
			{
				this.bindingSource_0.DataSource = ienumerable_0;
			}
			else
			{
				IEnumerable<QuickWndItem> dataSource = list_1.Where(new Func<QuickWndItem, bool>(@class.method_0));
				this.bindingSource_0.DataSource = dataSource;
			}
			this.dataGridView_0.DataSource = this.bindingSource_0;
			this.textBox_0.Text = @class.string_0;
		}

		// Token: 0x0600139F RID: 5023 RVA: 0x00007F64 File Offset: 0x00006164
		private void dataGridView_0_DoubleClick(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060013A0 RID: 5024 RVA: 0x00088448 File Offset: 0x00086648
		private void method_4()
		{
			if (this.dataGridView_0.SelectedRows != null && this.dataGridView_0.SelectedRows.Count >= 1)
			{
				base.Owner = null;
				base.DialogResult = DialogResult.OK;
				QuickWndItem quickWndItem_ = this.dataGridView_0.SelectedRows[0].DataBoundItem as QuickWndItem;
				this.method_0(new EventArgs10(quickWndItem_));
			}
		}

		// Token: 0x170002F0 RID: 752
		// (get) Token: 0x060013A1 RID: 5025 RVA: 0x00058AD0 File Offset: 0x00056CD0
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				createParams.ExStyle |= 33554432;
				return createParams;
			}
		}

		// Token: 0x170002F1 RID: 753
		// (get) Token: 0x060013A2 RID: 5026 RVA: 0x000239B0 File Offset: 0x00021BB0
		protected bool ShowWithoutActivation
		{
			get
			{
				return true;
			}
		}

		// Token: 0x170002F2 RID: 754
		// (get) Token: 0x060013A3 RID: 5027 RVA: 0x000884B0 File Offset: 0x000866B0
		// (set) Token: 0x060013A4 RID: 5028 RVA: 0x00007F6E File Offset: 0x0000616E
		public List<QuickWndItem> Items
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170002F3 RID: 755
		// (get) Token: 0x060013A5 RID: 5029 RVA: 0x000884C8 File Offset: 0x000866C8
		// (set) Token: 0x060013A6 RID: 5030 RVA: 0x00007F79 File Offset: 0x00006179
		public string KeyInStr
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x060013A7 RID: 5031 RVA: 0x00007F84 File Offset: 0x00006184
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060013A8 RID: 5032 RVA: 0x000884E0 File Offset: 0x000866E0
		private void method_5()
		{
			this.textBox_0 = new TextBox();
			this.dataGridView_0 = new DataGridView();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			base.SuspendLayout();
			this.textBox_0.Location = new Point(-2, 1);
			this.textBox_0.Name = Class521.smethod_0(7596);
			this.textBox_0.Size = new Size(230, 25);
			this.textBox_0.TabIndex = 0;
			this.dataGridView_0.BackgroundColor = SystemColors.Window;
			this.dataGridView_0.BorderStyle = BorderStyle.None;
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.Location = new Point(0, 28);
			this.dataGridView_0.Name = Class521.smethod_0(49789);
			this.dataGridView_0.RowTemplate.Height = 27;
			this.dataGridView_0.Size = new Size(228, 182);
			this.dataGridView_0.TabIndex = 1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(229, 211);
			base.Controls.Add(this.dataGridView_0);
			base.Controls.Add(this.textBox_0);
			base.FormBorderStyle = FormBorderStyle.FixedToolWindow;
			base.Name = Class521.smethod_0(49806);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(49819);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000A35 RID: 2613
		[CompilerGenerated]
		private Delegate13 delegate13_0;

		// Token: 0x04000A36 RID: 2614
		private List<QuickWndItem> list_0;

		// Token: 0x04000A37 RID: 2615
		private string string_0;

		// Token: 0x04000A38 RID: 2616
		private BindingSource bindingSource_0;

		// Token: 0x04000A39 RID: 2617
		private IContainer icontainer_0;

		// Token: 0x04000A3A RID: 2618
		private TextBox textBox_0;

		// Token: 0x04000A3B RID: 2619
		private DataGridView dataGridView_0;

		// Token: 0x020001F4 RID: 500
		[CompilerGenerated]
		private sealed class Class281
		{
			// Token: 0x060013AA RID: 5034 RVA: 0x0008868C File Offset: 0x0008688C
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000A3C RID: 2620
			public string string_0;
		}

		// Token: 0x020001F5 RID: 501
		[CompilerGenerated]
		private sealed class Class282
		{
			// Token: 0x060013AC RID: 5036 RVA: 0x000886DC File Offset: 0x000868DC
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0.ToLower()))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000A3D RID: 2621
			public string string_0;
		}
	}
}
