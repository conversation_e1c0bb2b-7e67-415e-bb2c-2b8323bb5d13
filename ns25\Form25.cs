﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns11;
using ns17;
using ns18;
using TEx;
using TEx.Inds;
using TEx.SIndicator;

namespace ns25
{
	// Token: 0x020002D9 RID: 729
	internal sealed partial class Form25 : Form
	{
		// Token: 0x06002084 RID: 8324 RVA: 0x000E7C0C File Offset: 0x000E5E0C
		public Form25()
		{
			this.method_3();
			base.StartPosition = FormStartPosition.CenterScreen;
			base.Load += this.Form25_Load;
			base.FormClosed += this.Form25_FormClosed;
			Base.UI.smethod_54(this);
			Base.UI.smethod_55(this.dataGridView_0);
			this.control13_0 = new Control13();
			this.control13_0.Parent = this.groupBox_0;
			this.control13_0.Dock = DockStyle.Fill;
			this.control13_0.OnEndEdit += this.method_0;
			this.dataGridView_0.Dock = DockStyle.Fill;
			this.dataGridView_0.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridView_0.Click += this.dataGridView_0_Click;
			this.dataGridView_0.BackgroundColor = Color.White;
			this.dataGridView_0.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView_0.ColumnHeadersVisible = false;
			this.dataGridView_0.RowHeadersVisible = false;
			this.dataGridView_0.BorderStyle = BorderStyle.None;
			this.dataGridView_0.DataSource = this.bindingList_0;
			this.dataGridView_0.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
		}

		// Token: 0x06002085 RID: 8325 RVA: 0x0000D318 File Offset: 0x0000B518
		private void Form25_FormClosed(object sender, FormClosedEventArgs e)
		{
			this.control13_0.OnEndEdit -= this.method_0;
		}

		// Token: 0x06002086 RID: 8326 RVA: 0x000E7D48 File Offset: 0x000E5F48
		private void method_0(object sender, EventArgs e)
		{
			if (this.dataGridView_0.CurrentRow != null)
			{
				int index = this.dataGridView_0.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				List<UserDefineParam> showList = this.control13_0.ShowList;
				if (!FormIndEditer.smethod_0(showList))
				{
					MessageBox.Show(Class521.smethod_0(96622));
				}
				else if (indEx != null && showList != null && indEx.UDInd.UDS.UserDefineParams.Count == this.control13_0.ShowList.Count)
				{
					indEx.UDInd.UDS.UserDefineParams = this.control13_0.ShowList;
				}
			}
		}

		// Token: 0x06002087 RID: 8327 RVA: 0x000E7DF0 File Offset: 0x000E5FF0
		private void dataGridView_0_Click(object sender, EventArgs e)
		{
			if (this.dataGridView_0.CurrentRow != null)
			{
				this.dataGridView_0.CurrentRow.Selected = true;
				int index = this.dataGridView_0.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				if (indEx == null)
				{
					this.control13_0.method_2(null, Enum25.flag_1, Control13.Enum27.const_1);
				}
				else if (indEx.UDInd.UDS.UserDefineParams.Any<UserDefineParam>())
				{
					this.control13_0.method_2(indEx.UDInd.UDS.UserDefineParams, Enum25.flag_1, Control13.Enum27.const_1);
				}
				else
				{
					this.control13_0.method_2(null, Enum25.flag_1, Control13.Enum27.const_1);
				}
			}
		}

		// Token: 0x06002088 RID: 8328 RVA: 0x000E7E94 File Offset: 0x000E6094
		private IndEx method_1(int int_0)
		{
			IndEx result;
			if (int_0 < this.list_0.Count)
			{
				result = this.list_0[int_0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002089 RID: 8329 RVA: 0x000041B9 File Offset: 0x000023B9
		private void Form25_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x0600208A RID: 8330 RVA: 0x000E7EC4 File Offset: 0x000E60C4
		public void method_2(List<IndEx> list_1)
		{
			if (list_1 != null)
			{
				this.bindingList_0.Clear();
				this.list_0 = list_1;
				foreach (IndEx indEx in this.list_0)
				{
					this.bindingList_0.Add(new NameScript(indEx.EnName, indEx.UDInd.UDS.Script));
					indEx.UDInd.method_0(indEx.UDInd.UDS.System.ICloneable.Clone() as UserDefineIndScript);
				}
				if (this.dataGridView_0.ColumnCount == 2)
				{
					this.dataGridView_0.Columns[Class521.smethod_0(1858)].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					this.dataGridView_0.Columns[Class521.smethod_0(95687)].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
				}
				if (this.dataGridView_0.RowCount > 0)
				{
					this.dataGridView_0.Rows[0].Selected = true;
				}
				if (this.dataGridView_0.CurrentRow != null)
				{
					int index = this.dataGridView_0.CurrentRow.Index;
					if (index < this.list_0.Count)
					{
						IndEx indEx2 = this.list_0[index];
						this.control13_0.method_2(indEx2.UDInd.UDS.UserDefineParams, Enum25.flag_1, Control13.Enum27.const_1);
						return;
					}
				}
				this.control13_0.method_2(null, Enum25.flag_1, Control13.Enum27.const_1);
			}
		}

		// Token: 0x170005B2 RID: 1458
		// (get) Token: 0x0600208B RID: 8331 RVA: 0x000E8050 File Offset: 0x000E6250
		public List<UserDefineParam> UdPList
		{
			get
			{
				return this.control13_0.ShowList.ToList<UserDefineParam>();
			}
		}

		// Token: 0x0600208C RID: 8332 RVA: 0x00004273 File Offset: 0x00002473
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x0600208D RID: 8333 RVA: 0x000E8074 File Offset: 0x000E6274
		private void button_1_Click(object sender, EventArgs e)
		{
			foreach (IndEx indEx in this.list_0)
			{
				if (indEx.InitInd(indEx.Chart))
				{
					indEx.Chart.method_123(indEx, indEx.Chart);
				}
			}
			base.Close();
		}

		// Token: 0x0600208E RID: 8334 RVA: 0x0000D333 File Offset: 0x0000B533
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600208F RID: 8335 RVA: 0x000E80EC File Offset: 0x000E62EC
		private void method_3()
		{
			this.groupBox_0 = new GroupBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.groupBox_1 = new GroupBox();
			this.dataGridView_0 = new DataGridView();
			this.groupBox_1.SuspendLayout();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			base.SuspendLayout();
			this.groupBox_0.Location = new Point(269, 24);
			this.groupBox_0.Margin = new Padding(3, 2, 3, 2);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Padding = new Padding(3, 2, 3, 2);
			this.groupBox_0.Size = new Size(390, 225);
			this.groupBox_0.TabIndex = 0;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(96667);
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(548, 264);
			this.button_0.Margin = new Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(95518);
			this.button_0.Size = new Size(110, 30);
			this.button_0.TabIndex = 2;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Location = new Point(420, 264);
			this.button_1.Margin = new Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(95505);
			this.button_1.Size = new Size(110, 30);
			this.button_1.TabIndex = 2;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.groupBox_1.Controls.Add(this.dataGridView_0);
			this.groupBox_1.Location = new Point(22, 24);
			this.groupBox_1.Name = Class521.smethod_0(10647);
			this.groupBox_1.Size = new Size(232, 225);
			this.groupBox_1.TabIndex = 3;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(95268);
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.GridColor = SystemColors.Control;
			this.dataGridView_0.Location = new Point(6, 23);
			this.dataGridView_0.Name = Class521.smethod_0(95285);
			this.dataGridView_0.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
			this.dataGridView_0.RowTemplate.Height = 20;
			this.dataGridView_0.Size = new Size(220, 196);
			this.dataGridView_0.TabIndex = 0;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(684, 308);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.groupBox_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.Margin = new Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(96684);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(96701);
			this.groupBox_1.ResumeLayout(false);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000FFD RID: 4093
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FFE RID: 4094
		private Control13 control13_0;

		// Token: 0x04000FFF RID: 4095
		private List<IndEx> list_0 = new List<IndEx>();

		// Token: 0x04001000 RID: 4096
		private IContainer icontainer_0;

		// Token: 0x04001001 RID: 4097
		private GroupBox groupBox_0;

		// Token: 0x04001002 RID: 4098
		private Button button_0;

		// Token: 0x04001003 RID: 4099
		private Button button_1;

		// Token: 0x04001004 RID: 4100
		private GroupBox groupBox_1;

		// Token: 0x04001005 RID: 4101
		private DataGridView dataGridView_0;
	}
}
