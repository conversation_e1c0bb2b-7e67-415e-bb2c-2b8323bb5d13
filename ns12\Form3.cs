﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns1;
using ns18;
using ns21;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns12
{
	// Token: 0x02000093 RID: 147
	internal sealed partial class Form3 : Form
	{
		// Token: 0x1400001E RID: 30
		// (add) Token: 0x060004D7 RID: 1239 RVA: 0x00026D1C File Offset: 0x00024F1C
		// (remove) Token: 0x060004D8 RID: 1240 RVA: 0x00026D54 File Offset: 0x00024F54
		public event MsgEventHandler CondGroupSelected
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004D9 RID: 1241 RVA: 0x00004245 File Offset: 0x00002445
		protected void method_0(string string_0, List<FilterCond> list_0)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_0, list_0));
			}
		}

		// Token: 0x060004DA RID: 1242 RVA: 0x00026D8C File Offset: 0x00024F8C
		public Form3()
		{
			this.method_1();
			base.Load += this.Form3_Load;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
		}

		// Token: 0x060004DB RID: 1243 RVA: 0x00004262 File Offset: 0x00002462
		public Form3(Class302 class302_1) : this()
		{
			this.FilterCondsUserCfg = class302_1;
		}

		// Token: 0x060004DC RID: 1244 RVA: 0x00026DE8 File Offset: 0x00024FE8
		private void Form3_Load(object sender, EventArgs e)
		{
			if (this.FilterCondsUserCfg != null)
			{
				List<Class303> condGroups = this.FilterCondsUserCfg.CondGroups;
				if (condGroups != null && condGroups.Count > 0)
				{
					List<ComboBoxItem> list = new List<ComboBoxItem>();
					foreach (Class303 @class in condGroups)
					{
						ComboBoxItem item = new ComboBoxItem(@class.Name, @class.FilterConds);
						list.Add(item);
					}
					this.listBox_0.DataSource = list;
					this.listBox_0.SelectedIndex = 0;
				}
			}
		}

		// Token: 0x060004DD RID: 1245 RVA: 0x00026E8C File Offset: 0x0002508C
		private void button_1_Click(object sender, EventArgs e)
		{
			ComboBoxItem comboBoxItem = this.listBox_0.SelectedItem as ComboBoxItem;
			this.method_0(comboBoxItem.Text, comboBoxItem.Value as List<FilterCond>);
			base.Close();
		}

		// Token: 0x060004DE RID: 1246 RVA: 0x00004273 File Offset: 0x00002473
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000100 RID: 256
		// (get) Token: 0x060004DF RID: 1247 RVA: 0x00026ECC File Offset: 0x000250CC
		// (set) Token: 0x060004E0 RID: 1248 RVA: 0x0000427D File Offset: 0x0000247D
		public Class302 FilterCondsUserCfg { get; set; }

		// Token: 0x060004E1 RID: 1249 RVA: 0x00004288 File Offset: 0x00002488
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060004E2 RID: 1250 RVA: 0x00026EE4 File Offset: 0x000250E4
		private void method_1()
		{
			this.listBox_0 = new ListBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			base.SuspendLayout();
			this.listBox_0.FormattingEnabled = true;
			this.listBox_0.ItemHeight = 15;
			this.listBox_0.Location = new Point(33, 32);
			this.listBox_0.Name = Class521.smethod_0(7408);
			this.listBox_0.Size = new Size(338, 184);
			this.listBox_0.TabIndex = 0;
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(267, 244);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(104, 32);
			this.button_0.TabIndex = 12;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(145, 244);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(104, 32);
			this.button_1.TabIndex = 11;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(408, 288);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.listBox_0);
			this.DoubleBuffered = true;
			base.Name = Class521.smethod_0(7455);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(7484);
			base.ResumeLayout(false);
		}

		// Token: 0x040001F6 RID: 502
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001F7 RID: 503
		[CompilerGenerated]
		private Class302 class302_0;

		// Token: 0x040001F8 RID: 504
		private IContainer icontainer_0;

		// Token: 0x040001F9 RID: 505
		private ListBox listBox_0;

		// Token: 0x040001FA RID: 506
		private Button button_0;

		// Token: 0x040001FB RID: 507
		private Button button_1;
	}
}
