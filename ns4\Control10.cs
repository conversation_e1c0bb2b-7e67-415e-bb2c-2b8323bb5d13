﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using ns21;
using ns26;
using TEx;

namespace ns4
{
	// Token: 0x02000251 RID: 593
	[Docking(DockingBehavior.AutoDock)]
	internal class Control10 : UserControl
	{
		// Token: 0x06001959 RID: 6489 RVA: 0x0000A8E5 File Offset: 0x00008AE5
		public Control10()
		{
			this.method_1();
			this.PanelHeaderFont = new Font(Class521.smethod_0(24023), TApp.smethod_4(8.4f, false));
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x0600195A RID: 6490 RVA: 0x0000A91C File Offset: 0x00008B1C
		public Control10(SplitterPanel splitterPanel_1) : this()
		{
			if (splitterPanel_1 != null)
			{
				splitterPanel_1.Controls.Add(this);
				this.ParentSplitPanel = splitterPanel_1;
			}
		}

		// Token: 0x0600195B RID: 6491 RVA: 0x0000A93C File Offset: 0x00008B3C
		protected virtual void button_Max_Click(object sender, EventArgs e)
		{
			this.SetTransTabsMaximization();
		}

		// Token: 0x0600195C RID: 6492 RVA: 0x000B04D4 File Offset: 0x000AE6D4
		public virtual void SetTransTabsMaximization()
		{
			if (!this.bool_0)
			{
				string name = Class521.smethod_0(67276);
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					name = Class521.smethod_0(67309);
				}
				this.button_0.Image = (Image)Class351.Resources.GetObject(name);
				if (Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else
			{
				string name2 = Class521.smethod_0(67338);
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					name2 = Class521.smethod_0(67363);
				}
				this.button_0.Image = (Image)Class351.Resources.GetObject(name2);
			}
			SplitterPanel splitterPanel = (SplitterPanel)base.Parent;
			this.method_0(splitterPanel);
			SplitContainer splitContainer = (SplitContainer)splitterPanel.Parent;
			while (splitContainer.Parent is SplitterPanel)
			{
				splitterPanel = (SplitterPanel)splitContainer.Parent;
				this.method_0(splitterPanel);
				splitContainer = (SplitContainer)splitterPanel.Parent;
			}
			this.bool_0 = !this.bool_0;
		}

		// Token: 0x0600195D RID: 6493 RVA: 0x000B05D4 File Offset: 0x000AE7D4
		private void method_0(SplitterPanel splitterPanel_1)
		{
			if (splitterPanel_1 != null)
			{
				SplitContainer splitContainer = (SplitContainer)splitterPanel_1.Parent;
				if (splitterPanel_1 == splitContainer.Panel1)
				{
					if (this.bool_0)
					{
						if (splitContainer.Tag == null || (splitContainer.Tag != null && splitContainer.Tag.ToString() != Class521.smethod_0(2606)))
						{
							splitContainer.Panel2Collapsed = !this.bool_0;
						}
					}
					else
					{
						splitContainer.Panel2Collapsed = !this.bool_0;
					}
				}
				else
				{
					splitContainer.Panel1Collapsed = !this.bool_0;
				}
			}
		}

		// Token: 0x0600195E RID: 6494 RVA: 0x000B0660 File Offset: 0x000AE860
		public virtual void vmethod_0()
		{
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.splitContainer_0.BackColor = Class181.color_3;
				this.splitContainer_0.Panel1.BackColor = Class181.color_3;
				this.panelEx_0.CanvasColor = Class181.color_3;
				this.panelEx_0.Style.BackColor1.Color = Class181.color_2;
				this.panelEx_0.Style.BackColor2.Color = Class181.color_2;
				this.panelEx_0.Style.BorderColor.Color = Class181.color_2;
				this.panelEx_0.Style.ForeColor.ColorSchemePart = eColorSchemePart.ItemText;
				if (this.bool_0)
				{
					this.button_0.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(67309));
				}
				else
				{
					this.button_0.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(67363));
				}
			}
			else
			{
				this.splitContainer_0.BackColor = Class181.color_9;
				this.splitContainer_0.Panel1.BackColor = Class181.color_9;
				this.panelEx_0.CanvasColor = Class181.color_9;
				this.panelEx_0.Style.BackColor1.Color = Class181.color_10;
				this.panelEx_0.Style.BackColor2.Color = Class181.color_10;
				this.panelEx_0.Style.BorderColor.Color = Class181.color_9;
				this.panelEx_0.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
				if (this.bool_0)
				{
					this.button_0.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(67276));
				}
				else
				{
					this.button_0.Image = (Image)Class351.Resources.GetObject(Class521.smethod_0(67338));
				}
			}
		}

		// Token: 0x17000435 RID: 1077
		// (get) Token: 0x0600195F RID: 6495 RVA: 0x000B0860 File Offset: 0x000AEA60
		public bool IsMaximized
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x17000436 RID: 1078
		// (get) Token: 0x06001960 RID: 6496 RVA: 0x000B0878 File Offset: 0x000AEA78
		// (set) Token: 0x06001961 RID: 6497 RVA: 0x0000A946 File Offset: 0x00008B46
		public bool IsSwitchedBehind
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				if (this.bool_1 != value)
				{
					this.bool_1 = value;
				}
			}
		}

		// Token: 0x17000437 RID: 1079
		// (get) Token: 0x06001962 RID: 6498 RVA: 0x000B0890 File Offset: 0x000AEA90
		// (set) Token: 0x06001963 RID: 6499 RVA: 0x0000A95A File Offset: 0x00008B5A
		public SplitterPanel ParentSplitPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
			}
		}

		// Token: 0x17000438 RID: 1080
		// (get) Token: 0x06001964 RID: 6500 RVA: 0x000B08A8 File Offset: 0x000AEAA8
		public SplitterPanel ContainerPanel
		{
			get
			{
				return this.splitContainer_0.Panel2;
			}
		}

		// Token: 0x17000439 RID: 1081
		// (get) Token: 0x06001965 RID: 6501 RVA: 0x000B08C4 File Offset: 0x000AEAC4
		// (set) Token: 0x06001966 RID: 6502 RVA: 0x0000A965 File Offset: 0x00008B65
		public string PanelHeaderText
		{
			get
			{
				return this.panelEx_0.Text;
			}
			set
			{
				this.panelEx_0.Text = value;
			}
		}

		// Token: 0x1700043A RID: 1082
		// (get) Token: 0x06001967 RID: 6503 RVA: 0x000B08E0 File Offset: 0x000AEAE0
		// (set) Token: 0x06001968 RID: 6504 RVA: 0x0000A975 File Offset: 0x00008B75
		public Font PanelHeaderFont
		{
			get
			{
				return this.panelEx_0.Font;
			}
			set
			{
				this.panelEx_0.Font = value;
			}
		}

		// Token: 0x1700043B RID: 1083
		// (get) Token: 0x06001969 RID: 6505 RVA: 0x000B08FC File Offset: 0x000AEAFC
		// (set) Token: 0x0600196A RID: 6506 RVA: 0x0000A985 File Offset: 0x00008B85
		public bool MaxButtonVisible
		{
			get
			{
				return this.button_0.Visible;
			}
			set
			{
				this.button_0.Visible = value;
			}
		}

		// Token: 0x0600196B RID: 6507 RVA: 0x0000A995 File Offset: 0x00008B95
		private void splitContainer_0_Resize(object sender, EventArgs e)
		{
			this.button_0.Location = new Point(this.splitContainer_0.Width - 23, 2);
		}

		// Token: 0x0600196C RID: 6508 RVA: 0x0000A9B8 File Offset: 0x00008BB8
		protected override void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600196D RID: 6509 RVA: 0x000B0918 File Offset: 0x000AEB18
		private void method_1()
		{
			this.splitContainer_0 = new SplitContainer();
			this.panelEx_0 = new PanelEx();
			this.button_0 = new Button();
			this.splitContainer_0.Panel1.SuspendLayout();
			this.splitContainer_0.SuspendLayout();
			this.panelEx_0.SuspendLayout();
			base.SuspendLayout();
			this.panelEx_0.CanvasColor = SystemColors.Control;
			this.panelEx_0.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx_0.Controls.Add(this.button_0);
			this.panelEx_0.Dock = DockStyle.Fill;
			this.panelEx_0.Font = new Font(Class521.smethod_0(24023), 8.4f);
			this.panelEx_0.Location = new Point(0, 0);
			this.panelEx_0.Margin = new System.Windows.Forms.Padding(0);
			this.panelEx_0.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
			this.panelEx_0.Name = Class521.smethod_0(60851);
			this.panelEx_0.Size = new Size(606, 22);
			this.panelEx_0.Style.BackColor1.Color = Color.White;
			this.panelEx_0.Style.BackColor2.Color = Color.FromArgb(224, 224, 224);
			this.panelEx_0.Style.BorderColor.Color = Color.Silver;
			this.panelEx_0.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx_0.Style.GradientAngle = 90;
			this.panelEx_0.Style.LineAlignment = StringAlignment.Center;
			this.panelEx_0.TabIndex = 1;
			this.panelEx_0.Text = Class521.smethod_0(1449);
			this.splitContainer_0.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_0.IsSplitterFixed = true;
			this.splitContainer_0.Location = new Point(0, 0);
			this.splitContainer_0.MinimumSize = new Size(200, 120);
			this.splitContainer_0.Name = Class521.smethod_0(67384);
			this.splitContainer_0.Dock = DockStyle.Fill;
			this.splitContainer_0.Orientation = Orientation.Horizontal;
			this.splitContainer_0.Panel1.Controls.Add(this.panelEx_0);
			this.splitContainer_0.Panel1MinSize = 20;
			this.splitContainer_0.Size = new Size(606, 397);
			this.splitContainer_0.SplitterDistance = 22;
			this.splitContainer_0.SplitterWidth = 1;
			this.splitContainer_0.TabIndex = 1;
			this.splitContainer_0.Resize += this.splitContainer_0_Resize;
			this.button_0.BackColor = Color.Transparent;
			this.button_0.FlatAppearance.BorderColor = Color.FromArgb(49, 106, 197);
			this.button_0.FlatAppearance.BorderSize = 0;
			this.button_0.FlatAppearance.MouseOverBackColor = Color.FromArgb(193, 210, 238);
			this.button_0.FlatStyle = FlatStyle.Flat;
			this.button_0.Image = Class375.max_small;
			this.button_0.Location = new Point(583, 2);
			this.button_0.Name = Class521.smethod_0(67405);
			this.button_0.Size = new Size(16, 16);
			this.button_0.TabIndex = 2;
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_Max_Click;
			base.AutoScaleDimensions = new SizeF(7f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.AutoSize = true;
			this.BackColor = Color.Transparent;
			base.Controls.Add(this.splitContainer_0);
			base.Name = Class521.smethod_0(3083);
			base.Size = new Size(609, 400);
			this.splitContainer_0.Panel1.ResumeLayout(false);
			this.splitContainer_0.ResumeLayout(false);
			this.panelEx_0.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000CC1 RID: 3265
		private bool bool_0;

		// Token: 0x04000CC2 RID: 3266
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000CC3 RID: 3267
		private bool bool_1;

		// Token: 0x04000CC4 RID: 3268
		private IContainer icontainer_0;

		// Token: 0x04000CC5 RID: 3269
		private SplitContainer splitContainer_0;

		// Token: 0x04000CC6 RID: 3270
		private PanelEx panelEx_0;

		// Token: 0x04000CC7 RID: 3271
		private Button button_0;
	}
}
