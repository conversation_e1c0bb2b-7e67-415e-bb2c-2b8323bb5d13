﻿using System;

namespace TEx
{
	// Token: 0x020000A7 RID: 167
	[Serializable]
	public sealed class StSplit
	{
		// Token: 0x1700010B RID: 267
		// (get) Token: 0x06000593 RID: 1427 RVA: 0x0002CB94 File Offset: 0x0002AD94
		// (set) Token: 0x06000594 RID: 1428 RVA: 0x00004698 File Offset: 0x00002898
		public int StockId
		{
			get
			{
				return this._StockId;
			}
			set
			{
				if (this._StockId != value)
				{
					this._StockId = value;
				}
			}
		}

		// Token: 0x1700010C RID: 268
		// (get) Token: 0x06000595 RID: 1429 RVA: 0x0002CBAC File Offset: 0x0002ADAC
		// (set) Token: 0x06000596 RID: 1430 RVA: 0x000046AC File Offset: 0x000028AC
		public DateTime Date
		{
			get
			{
				return this._Date;
			}
			set
			{
				if (this._Date != value)
				{
					this._Date = value;
				}
			}
		}

		// Token: 0x1700010D RID: 269
		// (get) Token: 0x06000597 RID: 1431 RVA: 0x0002CBC4 File Offset: 0x0002ADC4
		// (set) Token: 0x06000598 RID: 1432 RVA: 0x0002CBDC File Offset: 0x0002ADDC
		public decimal? BonusShares
		{
			get
			{
				return this._BonusShares;
			}
			set
			{
				decimal? bonusShares = this._BonusShares;
				decimal? num = value;
				if (!(bonusShares.GetValueOrDefault() == num.GetValueOrDefault() & bonusShares != null == (num != null)))
				{
					this._BonusShares = value;
				}
			}
		}

		// Token: 0x1700010E RID: 270
		// (get) Token: 0x06000599 RID: 1433 RVA: 0x0002CC24 File Offset: 0x0002AE24
		// (set) Token: 0x0600059A RID: 1434 RVA: 0x0002CC3C File Offset: 0x0002AE3C
		public decimal? RationedShares
		{
			get
			{
				return this._RationedShares;
			}
			set
			{
				decimal? rationedShares = this._RationedShares;
				decimal? num = value;
				if (!(rationedShares.GetValueOrDefault() == num.GetValueOrDefault() & rationedShares != null == (num != null)))
				{
					this._RationedShares = value;
				}
			}
		}

		// Token: 0x1700010F RID: 271
		// (get) Token: 0x0600059B RID: 1435 RVA: 0x0002CC84 File Offset: 0x0002AE84
		// (set) Token: 0x0600059C RID: 1436 RVA: 0x0002CC9C File Offset: 0x0002AE9C
		public decimal? RationedSharePrice
		{
			get
			{
				return this._RationedSharePrice;
			}
			set
			{
				decimal? rationedSharePrice = this._RationedSharePrice;
				decimal? num = value;
				if (!(rationedSharePrice.GetValueOrDefault() == num.GetValueOrDefault() & rationedSharePrice != null == (num != null)))
				{
					this._RationedSharePrice = value;
				}
			}
		}

		// Token: 0x17000110 RID: 272
		// (get) Token: 0x0600059D RID: 1437 RVA: 0x0002CCE4 File Offset: 0x0002AEE4
		// (set) Token: 0x0600059E RID: 1438 RVA: 0x0002CCFC File Offset: 0x0002AEFC
		public decimal? Divident
		{
			get
			{
				return this._Divident;
			}
			set
			{
				decimal? divident = this._Divident;
				decimal? num = value;
				if (!(divident.GetValueOrDefault() == num.GetValueOrDefault() & divident != null == (num != null)))
				{
					this._Divident = value;
				}
			}
		}

		// Token: 0x04000290 RID: 656
		private int _StockId;

		// Token: 0x04000291 RID: 657
		private DateTime _Date;

		// Token: 0x04000292 RID: 658
		private decimal? _BonusShares;

		// Token: 0x04000293 RID: 659
		private decimal? _RationedShares;

		// Token: 0x04000294 RID: 660
		private decimal? _RationedSharePrice;

		// Token: 0x04000295 RID: 661
		private decimal? _Divident;
	}
}
