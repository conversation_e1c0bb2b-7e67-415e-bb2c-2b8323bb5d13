﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns20;
using ns24;
using ns27;
using ns9;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000165 RID: 357
	internal sealed partial class DrawParamWnd : Form
	{
		// Token: 0x14000071 RID: 113
		// (add) Token: 0x06000D94 RID: 3476 RVA: 0x000574F0 File Offset: 0x000556F0
		// (remove) Token: 0x06000D95 RID: 3477 RVA: 0x00057528 File Offset: 0x00055728
		public event Delegate8 DrawObjParamSet
		{
			[CompilerGenerated]
			add
			{
				Delegate8 @delegate = this.delegate8_0;
				Delegate8 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate8 value2 = (Delegate8)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate8>(ref this.delegate8_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate8 @delegate = this.delegate8_0;
				Delegate8 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate8 value2 = (Delegate8)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate8>(ref this.delegate8_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000D96 RID: 3478 RVA: 0x000060B9 File Offset: 0x000042B9
		protected void method_0(EventArgs7 eventArgs7_0)
		{
			Delegate8 @delegate = this.delegate8_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs7_0);
			}
		}

		// Token: 0x06000D97 RID: 3479 RVA: 0x000060D0 File Offset: 0x000042D0
		public DrawParamWnd()
		{
			this.method_4();
		}

		// Token: 0x06000D98 RID: 3480 RVA: 0x00057560 File Offset: 0x00055760
		public DrawParamWnd(DrawObj drawObj) : this()
		{
			this.drawObj_0 = drawObj;
			DTValLocation dtvalLocation = drawObj.DTValLocation;
			this.control6_0 = new Control6(drawObj, dtvalLocation.X1DateTime, dtvalLocation.Y1Value);
			this.control6_0.Location = new Point(15, 15);
			this.nullable_0 = drawObj.LineColor;
			this.tabPage_2.Controls.Add(this.control6_0);
			this.tabPage_2.BackColor = Color.FromKnownColor(KnownColor.Control);
			int num = 115;
			if (!drawObj.IsOneClickLoc)
			{
				this.control6_1 = new Control6(drawObj, dtvalLocation.X2DateTime, dtvalLocation.Y2Value, 2);
				this.control6_1.Location = new Point(15, num + 32);
				this.tabPage_2.Controls.Add(this.control6_1);
			}
			this.tabControl_0.Height += num;
			base.Height += num;
			this.button_0.Location = new Point(this.button_0.Location.X, this.button_0.Location.Y + num + 1);
			this.button_2.Location = new Point(this.button_2.Location.X, this.button_2.Location.Y + num + 1);
			this.button_1.Location = new Point(this.button_1.Location.X, this.button_1.Location.Y + num + 1);
			DrawLineStyle lineStyle = drawObj.LineStyle;
			if (lineStyle != null)
			{
				this.tabPage_0 = new TabPage();
				this.tabPage_0.Location = new Point(4, 25);
				this.tabPage_0.Name = Class521.smethod_0(23891);
				this.tabPage_0.Padding = new Padding(3);
				this.tabPage_0.Size = new Size(385, 170);
				this.tabPage_0.Text = Class521.smethod_0(23916);
				this.tabPage_0.BackColor = Color.FromKnownColor(KnownColor.Control);
				this.tabControl_0.SuspendLayout();
				base.SuspendLayout();
				this.tabControl_0.Controls.Add(this.tabPage_0);
				this.label_0 = new Label();
				this.label_0.Text = Class521.smethod_0(23925);
				this.label_0.TextAlign = ContentAlignment.MiddleLeft;
				this.label_1 = new Label();
				this.label_1.Text = Class521.smethod_0(23938);
				this.label_1.TextAlign = ContentAlignment.MiddleLeft;
				int width = this.tabPage_0.Width / 2;
				int height = 36;
				this.class66_0 = new Class66(null, null);
				this.class66_0.Width = width;
				this.class66_0.Height = 36;
				for (int i = 0; i < Enum.GetNames(typeof(DrawLineType)).Length; i++)
				{
					this.class66_0.Items.Add(i);
				}
				this.class66_0.SelectedIndex = (int)lineStyle.LineType;
				this.class67_0 = new Class67(null, null);
				this.class67_0.Width = width;
				this.class67_0.Height = height;
				for (int j = 0; j < Enum.GetNames(typeof(DrawLineWidth)).Length; j++)
				{
					this.class67_0.Items.Add(j);
				}
				this.class67_0.SelectedIndex = (int)lineStyle.LineWidth;
				this.tabPage_0.Controls.Add(this.label_0);
				this.tabPage_0.Controls.Add(this.label_1);
				this.tabPage_0.Controls.Add(this.class66_0);
				this.tabPage_0.Controls.Add(this.class67_0);
				this.tabControl_0.ResumeLayout(false);
				base.ResumeLayout(false);
			}
			this.list_0 = drawObj.SublineParamList;
			if (this.list_0 != null)
			{
				this.tabPage_1 = new TabPage();
				this.tabPage_1.Location = new Point(4, 25);
				this.tabPage_1.Name = Class521.smethod_0(23951);
				this.tabPage_1.Padding = new Padding(3);
				this.tabPage_1.Size = new Size(385, 170);
				this.tabPage_1.Text = Class521.smethod_0(23972);
				this.tabPage_1.BackColor = Color.FromKnownColor(KnownColor.Control);
				this.tabControl_0.SuspendLayout();
				base.SuspendLayout();
				this.tabControl_0.Controls.Add(this.tabPage_1);
				this.dataGridView_0 = this.method_1(this.list_0);
				this.tabPage_1.Controls.Add(this.dataGridView_0);
				this.checkBox_0 = new CheckBox();
				this.checkBox_0.Checked = false;
				this.checkBox_0.Text = Class521.smethod_0(23981);
				this.tabPage_1.Controls.Add(this.checkBox_0);
				this.tabControl_0.ResumeLayout(false);
				base.ResumeLayout(false);
			}
			if (!drawObj.CanChgColor)
			{
				this.button_0.Visible = false;
			}
			if (!string.IsNullOrEmpty(drawObj.Name))
			{
				this.Text = this.Text + Class521.smethod_0(24018) + drawObj.Name;
			}
			base.Shown += this.DrawParamWnd_Shown;
		}

		// Token: 0x06000D99 RID: 3481 RVA: 0x00057B04 File Offset: 0x00055D04
		private void DrawParamWnd_Shown(object sender, EventArgs e)
		{
			if (this.tabPage_0 != null)
			{
				int num = this.tabPage_0.Height / 3;
				int num2 = num + this.tabPage_0.Height / 5;
				int width = base.Width / 8;
				int num3 = base.Width / 7;
				this.label_0.Location = new Point(num3, num);
				this.label_1.Location = new Point(num3, num2);
				this.label_0.Width = width;
				this.label_1.Width = width;
				int width2 = Convert.ToInt32(Math.Round((double)base.Width / 2.4));
				int x = num3 + this.label_0.Width;
				this.class66_0.Location = new Point(x, num - 1);
				this.class67_0.Location = new Point(x, num2 - 1);
				this.class66_0.Width = width2;
				this.class67_0.Width = width2;
			}
			if (this.tabPage_1 != null && this.dataGridView_0 != null)
			{
				DataGridView dataGridView = this.dataGridView_0;
				DataGridViewColumnCollection columns = dataGridView.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(4835));
				columns[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_DigitNb())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Visible = false;
				DataGridViewColumnCollection columns2 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(4835));
				columns2[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_MaxValue())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Visible = false;
				DataGridViewColumnCollection columns3 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(4835));
				columns3[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_MinValue())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Visible = false;
				DataGridViewColumnCollection columns4 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(5338));
				columns4[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_Name())), new ParameterExpression[]
				{
					parameterExpression
				}))].FillWeight = 30f;
				DataGridViewColumnCollection columns5 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(5338));
				columns5[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_Name())), new ParameterExpression[]
				{
					parameterExpression
				}))].ReadOnly = true;
				DataGridViewColumnCollection columns6 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(5338));
				columns6[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_Enabled())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].FillWeight = 50f;
				DataGridViewColumnCollection columns7 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(5338));
				columns7[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_Value())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight;
				DataGridViewColumnCollection columns8 = dataGridView.Columns;
				parameterExpression = Expression.Parameter(typeof(DrawSublineParam), Class521.smethod_0(5338));
				columns8[Utility.GetPropertyName<DrawSublineParam>(Expression.Lambda<Func<DrawSublineParam, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(DrawSublineParam.get_Value())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
				dataGridView.Width = Convert.ToInt32(Math.Round((double)this.tabPage_1.Width / 2.2));
				dataGridView.Height = this.tabPage_1.Height - 24;
				this.checkBox_0.Location = new Point(Convert.ToInt32(this.tabPage_1.Width / 2 + 16), Convert.ToInt32(this.tabPage_1.Height - 30));
				this.checkBox_0.Width = this.tabPage_1.Width / 2 - 16;
			}
		}

		// Token: 0x06000D9A RID: 3482 RVA: 0x00057FDC File Offset: 0x000561DC
		private DataGridView method_1(List<DrawSublineParam> list_1)
		{
			DataGridView dataGridView = new DataGridView();
			dataGridView.MultiSelect = false;
			dataGridView.BorderStyle = BorderStyle.None;
			dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
			dataGridView.BackgroundColor = Color.White;
			dataGridView.GridColor = Color.FromArgb(217, 217, 217);
			dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			dataGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			dataGridView.RowHeadersVisible = false;
			dataGridView.DefaultCellStyle.Font = new Font(Class521.smethod_0(24023), 8.5f);
			dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 120, 215);
			dataGridView.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			dataGridView.AllowUserToAddRows = false;
			dataGridView.AllowUserToDeleteRows = false;
			dataGridView.Location = new Point(10, 14);
			dataGridView.CellToolTipTextNeeded += this.method_2;
			dataGridView.CellValidating += this.method_3;
			dataGridView.DataSource = new BindingSource
			{
				DataSource = list_1
			};
			return dataGridView;
		}

		// Token: 0x06000D9B RID: 3483 RVA: 0x000060E0 File Offset: 0x000042E0
		private void method_2(object sender, DataGridViewCellToolTipTextNeededEventArgs e)
		{
			if (e.ColumnIndex == 2)
			{
				e.ToolTipText = Class521.smethod_0(24052);
			}
		}

		// Token: 0x06000D9C RID: 3484 RVA: 0x000580D8 File Offset: 0x000562D8
		private void method_3(object sender, DataGridViewCellValidatingEventArgs e)
		{
			object formattedValue = e.FormattedValue;
			if (e.ColumnIndex == 2)
			{
				DrawSublineParam drawSublineParam = this.list_0[e.RowIndex];
				try
				{
					double num = Convert.ToDouble(e.FormattedValue);
					if (!double.IsNaN(num))
					{
						if (num < drawSublineParam.MinValue)
						{
							MessageBox.Show(Class521.smethod_0(24077) + drawSublineParam.MinValue.ToString() + Class521.smethod_0(24110), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							e.Cancel = true;
						}
						else
						{
							double num2 = num;
							double? maxValue = drawSublineParam.MaxValue;
							if (num2 > maxValue.GetValueOrDefault() & maxValue != null)
							{
								MessageBox.Show(Class521.smethod_0(24115) + drawSublineParam.MaxValue.ToString() + Class521.smethod_0(24110), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
								e.Cancel = true;
							}
						}
					}
					else
					{
						MessageBox.Show(Class521.smethod_0(24148), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						e.Cancel = true;
					}
				}
				catch
				{
					MessageBox.Show(Class521.smethod_0(24148), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					e.Cancel = true;
				}
			}
		}

		// Token: 0x06000D9D RID: 3485 RVA: 0x00058234 File Offset: 0x00056434
		private void button_2_Click(object sender, EventArgs e)
		{
			DTValLocation dtvalLocation_;
			if (this.control6_1 == null)
			{
				dtvalLocation_ = new DTValLocation(this.control6_0.XDateTime, this.control6_0.YValue, this.control6_0.XDateTime, this.control6_0.YValue);
			}
			else
			{
				dtvalLocation_ = new DTValLocation(this.control6_0.XDateTime, this.control6_0.YValue, this.control6_1.XDateTime, this.control6_1.YValue);
			}
			DrawLineStyle drawLineStyle = null;
			if (this.tabPage_0 != null)
			{
				drawLineStyle = this.drawObj_0.LineStyle;
				if (drawLineStyle.LineType != (DrawLineType)this.class66_0.SelectedIndex)
				{
					drawLineStyle.LineType = (DrawLineType)this.class66_0.SelectedIndex;
				}
				if (drawLineStyle.LineWidth != (DrawLineWidth)this.class67_0.SelectedIndex)
				{
					drawLineStyle.LineWidth = (DrawLineWidth)this.class67_0.SelectedIndex;
				}
			}
			if (this.checkBox_0 != null && this.checkBox_0.Checked && this.list_0 != null)
			{
				if (Base.UI.Form.UserDrawObjParamsDict == null)
				{
					Base.UI.Form.UserDrawObjParamsDict = new Dictionary<string, Dictionary<string, object>>();
				}
				string key = this.drawObj_0.GetType().ToString();
				if (!Base.UI.Form.UserDrawObjParamsDict.ContainsKey(key))
				{
					Base.UI.Form.UserDrawObjParamsDict[key] = new Dictionary<string, object>();
				}
				Base.UI.Form.UserDrawObjParamsDict[key][DrawObjParamType.SublineParam.ToString()] = this.list_0;
				Base.UI.smethod_47();
			}
			this.method_0(new EventArgs7(dtvalLocation_, this.nullable_0, drawLineStyle, this.list_0));
			base.Close();
		}

		// Token: 0x06000D9E RID: 3486 RVA: 0x000583D8 File Offset: 0x000565D8
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.nullable_0 != null)
			{
				this.colorDialog_0.Color = this.nullable_0.Value;
			}
			else
			{
				this.colorDialog_0.Color = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black);
			}
			if (this.colorDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.nullable_0 = new Color?(this.colorDialog_0.Color);
				Base.UI.Form.LastSelectedLineColor = this.nullable_0;
			}
		}

		// Token: 0x06000D9F RID: 3487 RVA: 0x000060FD File Offset: 0x000042FD
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000DA0 RID: 3488 RVA: 0x00058464 File Offset: 0x00056664
		private void method_4()
		{
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.button_2 = new Button();
			this.colorDialog_0 = new ColorDialog();
			this.tabControl_0 = new TabControl();
			this.tabPage_2 = new TabPage();
			this.tabControl_0.SuspendLayout();
			base.SuspendLayout();
			this.button_0.Location = new Point(22, 243);
			this.button_0.Name = Class521.smethod_0(24185);
			this.button_0.Size = new Size(100, 32);
			this.button_0.TabIndex = 7;
			this.button_0.Text = Class521.smethod_0(24198);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Location = new Point(339, 243);
			this.button_1.Name = Class521.smethod_0(24207);
			this.button_1.Size = new Size(100, 32);
			this.button_1.TabIndex = 6;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_2.Location = new Point(230, 243);
			this.button_2.Name = Class521.smethod_0(24224);
			this.button_2.Size = new Size(100, 32);
			this.button_2.TabIndex = 5;
			this.button_2.Text = Class521.smethod_0(5801);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_2.Click += this.button_2_Click;
			this.tabControl_0.Controls.Add(this.tabPage_2);
			this.tabControl_0.ItemSize = new Size(64, 21);
			this.tabControl_0.Location = new Point(22, 18);
			this.tabControl_0.Name = Class521.smethod_0(24233);
			this.tabControl_0.SelectedIndex = 0;
			this.tabControl_0.Size = new Size(419, 210);
			this.tabControl_0.TabIndex = 8;
			this.tabPage_2.Location = new Point(4, 25);
			this.tabPage_2.Name = Class521.smethod_0(24250);
			this.tabPage_2.Padding = new Padding(3);
			this.tabPage_2.Size = new Size(411, 181);
			this.tabPage_2.TabIndex = 0;
			this.tabPage_2.Text = Class521.smethod_0(24271);
			this.tabPage_2.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_2;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(460, 289);
			base.Controls.Add(this.tabControl_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_2);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(24280);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(24297);
			this.tabControl_0.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x040006E4 RID: 1764
		[CompilerGenerated]
		private Delegate8 delegate8_0;

		// Token: 0x040006E5 RID: 1765
		private DrawObj drawObj_0;

		// Token: 0x040006E6 RID: 1766
		private Color? nullable_0;

		// Token: 0x040006E7 RID: 1767
		private Control6 control6_0;

		// Token: 0x040006E8 RID: 1768
		private Control6 control6_1;

		// Token: 0x040006E9 RID: 1769
		private List<DrawSublineParam> list_0;

		// Token: 0x040006EA RID: 1770
		private TabPage tabPage_0;

		// Token: 0x040006EB RID: 1771
		private Label label_0;

		// Token: 0x040006EC RID: 1772
		private Label label_1;

		// Token: 0x040006ED RID: 1773
		private Class66 class66_0;

		// Token: 0x040006EE RID: 1774
		private Class67 class67_0;

		// Token: 0x040006EF RID: 1775
		private TabPage tabPage_1;

		// Token: 0x040006F0 RID: 1776
		private DataGridView dataGridView_0;

		// Token: 0x040006F1 RID: 1777
		private CheckBox checkBox_0;

		// Token: 0x040006F2 RID: 1778
		private IContainer icontainer_0;

		// Token: 0x040006F3 RID: 1779
		private Button button_0;

		// Token: 0x040006F4 RID: 1780
		private Button button_1;

		// Token: 0x040006F5 RID: 1781
		private Button button_2;

		// Token: 0x040006F6 RID: 1782
		private ColorDialog colorDialog_0;

		// Token: 0x040006F7 RID: 1783
		private TabControl tabControl_0;

		// Token: 0x040006F8 RID: 1784
		private TabPage tabPage_2;
	}
}
