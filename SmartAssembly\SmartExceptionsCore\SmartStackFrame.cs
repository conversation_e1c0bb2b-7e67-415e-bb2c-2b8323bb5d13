﻿using System;
using System.Runtime.Serialization;
using System.Security;
using ns18;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000403 RID: 1027
	[Serializable]
	public sealed class SmartStackFrame : ISerializable
	{
		// Token: 0x060027DC RID: 10204 RVA: 0x0010CB38 File Offset: 0x0010AD38
		[SecurityCritical]
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(118790), this.MethodID, typeof(int));
			info.AddValue(Class521.smethod_0(118827), this.ILOffset, typeof(int));
			info.AddValue(Class521.smethod_0(118864), this.ExceptionStackDepth, typeof(int));
			int num = (this.Objects == null) ? 0 : this.Objects.Length;
			info.AddValue(Class521.smethod_0(118917), num, typeof(int));
			for (int i = 0; i < num; i++)
			{
				string name = string.Format(Class521.smethod_0(118962), i);
				try
				{
					if (this.Objects[i] == null)
					{
						info.AddValue(name, null, typeof(object));
					}
					else
					{
						info.AddValue(name, this.Objects[i].GetType() + Class521.smethod_0(24018) + this.Objects[i], typeof(string));
					}
				}
				catch (Exception)
				{
				}
			}
		}

		// Token: 0x060027DD RID: 10205 RVA: 0x0010CC74 File Offset: 0x0010AE74
		internal SmartStackFrame(SerializationInfo info, StreamingContext context)
		{
			this.MethodID = info.GetInt32(Class521.smethod_0(118790));
			this.ILOffset = info.GetInt32(Class521.smethod_0(118827));
			this.ExceptionStackDepth = info.GetInt32(Class521.smethod_0(118864));
			int @int = info.GetInt32(Class521.smethod_0(118917));
			this.Objects = new object[@int];
			for (int i = 0; i < @int; i++)
			{
				try
				{
					this.Objects[i] = info.GetValue(string.Format(Class521.smethod_0(118962), i), typeof(string));
				}
				catch (Exception)
				{
					this.Objects[i] = Class521.smethod_0(119007);
				}
			}
		}

		// Token: 0x060027DE RID: 10206 RVA: 0x0000F5D5 File Offset: 0x0000D7D5
		internal SmartStackFrame(int methodID, object[] objects, int ilOffset, int exceptionStackDepth)
		{
			this.MethodID = methodID;
			this.ExceptionStackDepth = exceptionStackDepth;
			this.ILOffset = ilOffset;
			this.Objects = objects;
		}

		// Token: 0x040013CE RID: 5070
		public readonly int MethodID;

		// Token: 0x040013CF RID: 5071
		public readonly object[] Objects;

		// Token: 0x040013D0 RID: 5072
		public readonly int ILOffset;

		// Token: 0x040013D1 RID: 5073
		public readonly int ExceptionStackDepth;
	}
}
