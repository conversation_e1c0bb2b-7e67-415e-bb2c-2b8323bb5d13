﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using ns18;
using ns2;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x0200039A RID: 922
	internal static class CutBigComponent
	{
		// Token: 0x06002566 RID: 9574 RVA: 0x0000E6EC File Offset: 0x0000C8EC
		private static void smethod_0(List<Point> list_0, List<Rectangle> list_1)
		{
			list_1.Add(ImageProcessor.smethod_16(list_0));
		}

		// Token: 0x06002567 RID: 9575 RVA: 0x00101994 File Offset: 0x000FFB94
		public static Bitmap smethod_1(Bitmap bitmap_0, List<Rectangle> list_0, List<Rectangle> list_1, Class485 class485_0, List<int> list_2)
		{
			list_0.Clear();
			ImageProcessor.smethod_4(bitmap_0, 200);
			bitmap_0 = ImageProcessor.smethod_26(bitmap_0, 2, 4, 10);
			ConnectedComponent connectedComponent = new ConnectedComponent();
			connectedComponent.Roi = new Rectangle(1, 1, bitmap_0.Width - 2, bitmap_0.Height - 2);
			Bitmap result = new Bitmap(bitmap_0);
			List<List<Point>> list;
			for (;;)
			{
				list = connectedComponent.method_0(bitmap_0);
				ImageProcessor.smethod_3(list);
				ImageProcessor.smethod_0(list);
				int num = ImageProcessor.smethod_19(list);
				if (num == -1)
				{
					break;
				}
				CutBigComponent.smethod_0(list[num], list_1);
				int x = CutBigComponent.smethod_2(bitmap_0, list[num], list_2, class485_0);
				for (int i = 0; i < bitmap_0.Height; i++)
				{
					bitmap_0.SetPixel(x, i, Color.FromArgb(255, 255, 255));
				}
			}
			foreach (List<Point> list2 in list)
			{
				if (list2.Count != 0)
				{
					int num2 = list2.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_0));
					int num3 = list2.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_1));
					int num4 = list2.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_2));
					int num5 = list2.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_3));
					Rectangle rectangle = new Rectangle(num5, num4, num3 - num5, num2 - num4);
					list_0.Add(rectangle);
					Graphics graphics = Graphics.FromImage(bitmap_0);
					Pen pen = new Pen(Color.FromArgb(0, 0, 255));
					graphics.DrawRectangle(pen, rectangle);
					Pen pen2 = new Pen(Color.FromArgb(255, 0, 0));
					for (int j = 0; j < list2.Count - 1; j++)
					{
						graphics.DrawLine(pen2, list2[j], list2[j + 1]);
					}
					graphics.Dispose();
				}
			}
			return result;
		}

		// Token: 0x06002568 RID: 9576 RVA: 0x00101BF4 File Offset: 0x000FFDF4
		public static int smethod_2(Bitmap bitmap_0, List<Point> list_0, List<int> list_1, Class485 class485_0)
		{
			int num = list_0.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_4));
			int num2 = list_0.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_5));
			int num3 = list_0.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_6));
			int num4 = list_0.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_7));
			int num5 = num2 - num + 1;
			List<int> list = new List<int>();
			List<int> list2 = new List<int>();
			CutBigComponent.Class498 @class = new CutBigComponent.Class498();
			@class.int_0 = num;
			while (@class.int_0 < num2)
			{
				int item = list_0.Where(new Func<Point, bool>(@class.method_0)).ToArray<Point>().Length;
				list.Add(item);
				int int_ = @class.int_0;
				@class.int_0 = int_ + 1;
			}
			for (int i = 0; i < 5; i++)
			{
				list[i] = 100000;
			}
			for (int j = list.Count - 5; j < list.Count; j++)
			{
				list[j] = 10000;
			}
			int num6 = list.IndexOf(list.Min());
			if (num6 >= 5 && num5 - num6 >= 5)
			{
				list2.Add(num6);
				list_1.Add(num + num6);
			}
			for (int k = num6 - 5; k < num6 + 5; k++)
			{
				if (k >= 0 && k < list.Count)
				{
					list[k] = 100000;
				}
			}
			int num7 = list.IndexOf(list.Min());
			if (num7 >= 5 && num5 - num7 >= 5)
			{
				list2.Add(num7);
				list_1.Add(num + num7);
			}
			for (int l = num7 - 5; l < num7 + 5; l++)
			{
				if (l >= 0 && l < list.Count)
				{
					list[l] = 100000;
				}
			}
			int num8 = list.IndexOf(list.Min());
			if (num8 >= 5 && num5 - num8 >= 5)
			{
				list2.Add(num8);
				list_1.Add(num + num8);
			}
			double[] array = new double[list2.Count];
			for (int m = 0; m < list2.Count; m++)
			{
				Rectangle rectangle_ = new Rectangle(num, num3, list2[m], num4 - num3);
				Rectangle rectangle_2 = new Rectangle(list2[m] + num, num3, num5 - list2[m], num4 - num3);
				List<Point> list3 = ImageProcessor.smethod_1(bitmap_0, rectangle_);
				List<Point> list4 = ImageProcessor.smethod_1(bitmap_0, rectangle_2);
				List<List<Point>> list_2 = new List<List<Point>>
				{
					list3,
					list4
				};
				ImageProcessor.smethod_0(list_2);
				if (list3.Count >= 20 && list4.Count >= 20)
				{
					Reg[] source = class485_0.vmethod_4(list_2);
					array[m] = source.Max(new Func<Reg, double>(CutBigComponent.<>c.<>9.method_8));
				}
				else
				{
					array[m] = 0.0;
				}
			}
			return list2[Array.IndexOf<double>(array, array.Max())] + num;
		}

		// Token: 0x06002569 RID: 9577 RVA: 0x00101F50 File Offset: 0x00100150
		public static bool smethod_3(Bitmap bitmap_0, Rectangle rectangle_0, int int_0, Class485 class485_0)
		{
			double num = 0.8;
			bool result;
			if (int_0 <= rectangle_0.X)
			{
				result = true;
			}
			else if (int_0 >= rectangle_0.X + rectangle_0.Width - 5)
			{
				result = true;
			}
			else
			{
				Rectangle item = new Rectangle(rectangle_0.X, rectangle_0.Y, int_0 - rectangle_0.X, rectangle_0.Height);
				Rectangle item2 = new Rectangle(int_0, rectangle_0.Y, rectangle_0.Width + rectangle_0.X - int_0, rectangle_0.Height);
				List<Rectangle> list_ = new List<Rectangle>
				{
					item,
					item2
				};
				if (class485_0.vmethod_3(bitmap_0, list_)[0].Persent >= num)
				{
					result = true;
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x0600256A RID: 9578 RVA: 0x00102010 File Offset: 0x00100210
		public static int smethod_4(Bitmap bitmap_0, List<Point> list_0, Class485 class485_0)
		{
			double num = 0.8;
			int num2 = ImageProcessor.smethod_11(list_0, bitmap_0);
			int num3 = list_0.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_9));
			int num4 = list_0.Max(new Func<Point, int>(CutBigComponent.<>c.<>9.method_10));
			int num5 = list_0.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_11));
			int num6 = list_0.Min(new Func<Point, int>(CutBigComponent.<>c.<>9.method_12));
			Rectangle item = new Rectangle(num5, num6, num2 - num5, num4 - num6);
			Rectangle item2 = new Rectangle(num2 + 1, num6, num3 - num2 - 1, num4 - num6);
			List<Rectangle> list_ = new List<Rectangle>
			{
				item,
				item2
			};
			Reg[] array = class485_0.vmethod_3(bitmap_0, list_);
			int result;
			if (array.Max(new Func<Reg, double>(CutBigComponent.<>c.<>9.method_13)) >= num && array[0].Name != Class521.smethod_0(109813))
			{
				result = num2;
			}
			else
			{
				if (array[0].Name == Class521.smethod_0(109813))
				{
					num2 += 5;
					while (!CutBigComponent.smethod_3(bitmap_0, new Rectangle(num5, num6, num3 - num5, num4 - num6), num2, class485_0))
					{
						Thread.Sleep(10);
						num2++;
					}
				}
				else
				{
					num2 = num5 + 5;
					while (!CutBigComponent.smethod_3(bitmap_0, new Rectangle(num5, num6, num3 - num5, num4 - num6), num2, class485_0))
					{
						Thread.Sleep(10);
						num2++;
					}
				}
				result = num2;
			}
			return result;
		}

		// Token: 0x0200039C RID: 924
		[CompilerGenerated]
		private sealed class Class498
		{
			// Token: 0x0600257C RID: 9596 RVA: 0x00102220 File Offset: 0x00100420
			internal bool method_0(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x04001213 RID: 4627
			public int int_0;
		}
	}
}
