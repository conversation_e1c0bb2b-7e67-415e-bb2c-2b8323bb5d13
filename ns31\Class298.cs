﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ns31
{
	// Token: 0x0200021B RID: 539
	[DesignerCategory("Code")]
	internal sealed class Class298 : Panel
	{
		// Token: 0x06001643 RID: 5699 RVA: 0x0000900E File Offset: 0x0000720E
		public Class298()
		{
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, true);
		}

		// Token: 0x06001644 RID: 5700 RVA: 0x0009A910 File Offset: 0x00098B10
		protected void OnPaint(PaintEventArgs e)
		{
			if (this.DrawCustomBorder)
			{
				using (SolidBrush solidBrush = new SolidBrush(this.BackColor))
				{
					e.Graphics.FillRectangle(solidBrush, base.ClientRectangle);
				}
				Pen pen = new Pen(this.BorderColor);
				e.Graphics.DrawRectangle(pen, 0, 0, base.ClientSize.Width - 1, base.ClientSize.Height - 1);
			}
		}

		// Token: 0x17000393 RID: 915
		// (get) Token: 0x06001645 RID: 5701 RVA: 0x0009A99C File Offset: 0x00098B9C
		// (set) Token: 0x06001646 RID: 5702 RVA: 0x00009024 File Offset: 0x00007224
		public bool DrawCustomBorder { get; set; }

		// Token: 0x17000394 RID: 916
		// (get) Token: 0x06001647 RID: 5703 RVA: 0x0009A9B4 File Offset: 0x00098BB4
		// (set) Token: 0x06001648 RID: 5704 RVA: 0x0000902F File Offset: 0x0000722F
		public Color BorderColor { get; set; }

		// Token: 0x04000B42 RID: 2882
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000B43 RID: 2883
		[CompilerGenerated]
		private Color color_0;
	}
}
