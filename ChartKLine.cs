﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns1;
using ns13;
using ns17;
using ns18;
using ns20;
using ns21;
using ns22;
using ns25;
using ns26;
using ns27;
using ns7;
using ns8;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200017C RID: 380
	internal abstract class ChartKLine : ChartBase
	{
		// Token: 0x06000E51 RID: 3665 RVA: 0x000065C6 File Offset: 0x000047C6
		public ChartKLine(ChtCtrl_KLine dChtCtrl_KLine, SplitterPanel panel) : base(dChtCtrl_KLine, panel)
		{
		}

		// Token: 0x1700023E RID: 574
		// (get) Token: 0x06000E52 RID: 3666 RVA: 0x0005D96C File Offset: 0x0005BB6C
		public List<IndEx> IndExList
		{
			get
			{
				return this.list_0.Where(new Func<Indicator, bool>(ChartKLine.<>c.<>9.method_0)).Select(new Func<Indicator, IndEx>(ChartKLine.<>c.<>9.method_1)).ToList<IndEx>();
			}
		}

		// Token: 0x1700023F RID: 575
		// (get) Token: 0x06000E53 RID: 3667 RVA: 0x0005D9D0 File Offset: 0x0005BBD0
		public List<UserDefineIndScript> UDSList
		{
			get
			{
				return this.IndExList.Select(new Func<IndEx, UserDefineIndScript>(ChartKLine.<>c.<>9.method_2)).ToList<UserDefineIndScript>();
			}
		}

		// Token: 0x17000240 RID: 576
		// (get) Token: 0x06000E54 RID: 3668 RVA: 0x0005DA10 File Offset: 0x0005BC10
		public DataProvider DP
		{
			get
			{
				return new DataProvider(base.HisDataPeriodSet.PeriodHisDataList, base.Symbol);
			}
		}

		// Token: 0x06000E55 RID: 3669 RVA: 0x0005DA38 File Offset: 0x0005BC38
		protected void method_73(ContextMenuStrip contextMenuStrip_0)
		{
			string text = Class521.smethod_0(47752);
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_78;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E56 RID: 3670 RVA: 0x0005DA80 File Offset: 0x0005BC80
		protected void method_74(ContextMenuStrip contextMenuStrip_0)
		{
			string text = Class521.smethod_0(47769);
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_75;
		}

		// Token: 0x06000E57 RID: 3671 RVA: 0x000065D8 File Offset: 0x000047D8
		private void method_75(object sender, EventArgs e)
		{
			Form21 form = new Form21();
			form.method_0(this.ChtCtrl_KLine.method_72());
			form.ShowDialog();
		}

		// Token: 0x06000E58 RID: 3672 RVA: 0x0005DAC8 File Offset: 0x0005BCC8
		protected void method_76(ContextMenuStrip contextMenuStrip_0)
		{
			string text = Class521.smethod_0(47794);
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_77;
		}

		// Token: 0x06000E59 RID: 3673 RVA: 0x000065F8 File Offset: 0x000047F8
		private void method_77(object sender, EventArgs e)
		{
			Form25 form = new Form25();
			form.method_2(this.ChtCtrl_KLine.method_71());
			form.ShowDialog();
		}

		// Token: 0x06000E5A RID: 3674 RVA: 0x00006618 File Offset: 0x00004818
		private void method_78(object sender, EventArgs e)
		{
			FormIndMgr formIndMgr = new FormIndMgr();
			formIndMgr.ShownIndEditer += this.method_79;
			formIndMgr.ShowDialog();
			formIndMgr.ShownIndEditer -= this.method_79;
		}

		// Token: 0x06000E5B RID: 3675 RVA: 0x0005DB10 File Offset: 0x0005BD10
		private void method_79(object sender, EventArgs e)
		{
			EventArgs32 eventArgs = e as EventArgs32;
			if (eventArgs != null)
			{
				FormIndEditer indEditer = eventArgs.IndEditer;
				if (eventArgs.DoType == Enum25.flag_0)
				{
					indEditer.ReadyToLoadNewIndToChart += this.method_81;
				}
				else if (eventArgs.DoType == Enum25.flag_1)
				{
					indEditer.ReadyToReloadModifiedIndToChart += this.method_83;
				}
			}
		}

		// Token: 0x06000E5C RID: 3676 RVA: 0x0005DB6C File Offset: 0x0005BD6C
		private IndEx method_80(string string_10)
		{
			ChartKLine.Class214 @class = new ChartKLine.Class214();
			@class.string_0 = string_10;
			IndEx result;
			if (!this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_1));
				if (userDefineIndScript == null)
				{
					result = null;
				}
				else
				{
					IndEx indEx = this.method_86(userDefineIndScript);
					if (this.vmethod_29(indEx, this, null))
					{
						result = indEx;
					}
					else
					{
						result = null;
					}
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000E5D RID: 3677 RVA: 0x0005DBE0 File Offset: 0x0005BDE0
		protected void method_81(object sender, EventArgs e)
		{
			EventArgs30 eventArgs = e as EventArgs30;
			if (eventArgs == null)
			{
				MessageBox.Show(Class521.smethod_0(47811), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			else
			{
				this.method_82(eventArgs.UDS);
			}
		}

		// Token: 0x06000E5E RID: 3678 RVA: 0x0005DC24 File Offset: 0x0005BE24
		public void method_82(UserDefineIndScript userDefineIndScript_0)
		{
			ChartKLine.Class215 @class = new ChartKLine.Class215();
			@class.userDefineIndScript_0 = userDefineIndScript_0;
			if (this.list_0.SingleOrDefault(new Func<Indicator, bool>(@class.method_0)) == null)
			{
				IndEx indicator_ = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indicator_, this, null);
			}
		}

		// Token: 0x06000E5F RID: 3679 RVA: 0x0005DC70 File Offset: 0x0005BE70
		protected void method_83(object sender, EventArgs e)
		{
			EventArgs30 eventArgs = e as EventArgs30;
			if (eventArgs == null)
			{
				MessageBox.Show(Class521.smethod_0(47840));
			}
			else
			{
				UserDefineIndScript uds = eventArgs.UDS;
				this.method_84(uds);
			}
		}

		// Token: 0x06000E60 RID: 3680 RVA: 0x0005DCAC File Offset: 0x0005BEAC
		public void method_84(UserDefineIndScript userDefineIndScript_0)
		{
			ChartKLine.Class216 @class = new ChartKLine.Class216();
			@class.userDefineIndScript_0 = userDefineIndScript_0;
			Indicator indicator = this.list_0.FirstOrDefault(new Func<Indicator, bool>(@class.method_0));
			if (indicator != null)
			{
				IndEx indEx = indicator as IndEx;
				if (indEx != null)
				{
					indEx.RemoveFromChart();
				}
				indEx = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indEx, this, null);
			}
			else if (MessageBox.Show(Class521.smethod_0(47869), Class521.smethod_0(7587), MessageBoxButtons.OKCancel) == DialogResult.OK)
			{
				IndEx indicator_ = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indicator_, this, null);
			}
		}

		// Token: 0x06000E61 RID: 3681 RVA: 0x0005DD40 File Offset: 0x0005BF40
		protected void method_85(object sender, EventArgs e)
		{
			ChartKLine.Class217 @class = new ChartKLine.Class217();
			@class.eventArgs30_0 = (e as EventArgs30);
			if (@class.eventArgs30_0 == null)
			{
				MessageBox.Show(Class521.smethod_0(47811), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			else if (this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
			{
				MessageBox.Show(Class521.smethod_0(47922), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				IndEx indEx = this.method_86(@class.eventArgs30_0.UDS);
				if (indEx.IsMainChartInd)
				{
					if (indEx.InitInd(this))
					{
						this.method_123(indEx, this);
					}
				}
				else
				{
					this.ChtCtrl_KLine.method_73(indEx);
				}
			}
		}

		// Token: 0x06000E62 RID: 3682 RVA: 0x0005DDFC File Offset: 0x0005BFFC
		private IndEx method_86(UserDefineIndScript userDefineIndScript_0)
		{
			return IndEx.smethod_0(new UserDefineInd(this.DP, userDefineIndScript_0));
		}

		// Token: 0x06000E63 RID: 3683 RVA: 0x0000664B File Offset: 0x0000484B
		private void method_87(object sender, EventArgs e)
		{
			this.method_89(Class521.smethod_0(1449), Enum25.flag_0, null);
		}

		// Token: 0x06000E64 RID: 3684 RVA: 0x0005DE20 File Offset: 0x0005C020
		private void method_88(object sender, EventArgs e)
		{
			IndEx indEx = this.ChtCtrl_KLine.SelectedInd as IndEx;
			if (indEx != null)
			{
				this.method_89(Class521.smethod_0(1449), Enum25.flag_1, indEx.UDInd.UDS);
			}
		}

		// Token: 0x06000E65 RID: 3685 RVA: 0x0005DE60 File Offset: 0x0005C060
		private void method_89(string string_10, Enum25 enum25_0, UserDefineIndScript userDefineIndScript_0)
		{
			switch (enum25_0)
			{
			case Enum25.flag_0:
			{
				FormIndEditer formIndEditer = new FormIndEditer();
				formIndEditer.ReadyToLoadNewIndToChart += this.method_85;
				formIndEditer.Group = string_10;
				formIndEditer.ShowDialog();
				formIndEditer.ReadyToLoadNewIndToChart -= this.method_85;
				break;
			}
			case Enum25.flag_1:
			{
				FormIndEditer formIndEditer2 = new FormIndEditer();
				formIndEditer2.ReadyToReloadModifiedIndToChart += this.method_83;
				if (formIndEditer2.method_8(userDefineIndScript_0))
				{
					formIndEditer2.ShowDialog();
				}
				formIndEditer2.ReadyToReloadModifiedIndToChart -= this.method_83;
				break;
			}
			}
		}

		// Token: 0x06000E66 RID: 3686 RVA: 0x0005DEFC File Offset: 0x0005C0FC
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.ZedGraphControl.ContextMenuBuilder += this.vmethod_27;
			base.ZedGraphControl.MouseClick += this.vmethod_31;
			base.ZedGraphControl.MouseDownEvent += this.method_142;
			base.ZedGraphControl.MouseUpEvent += this.method_141;
		}

		// Token: 0x06000E67 RID: 3687 RVA: 0x0005DF70 File Offset: 0x0005C170
		protected virtual void vmethod_24()
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.InitItem();
			}
		}

		// Token: 0x06000E68 RID: 3688 RVA: 0x0005DFC4 File Offset: 0x0005C1C4
		public override void vmethod_3()
		{
			base.vmethod_3();
			GraphPane graphPane = base.GraphPane;
			double num = Convert.ToDouble(this.ChtCtrl_KLine.MaxSticksPerChart);
			double num2 = num / 12.0;
			graphPane.XAxis.Scale.Max = num + num2;
			if (base.IsXAxisVisible)
			{
				this.vmethod_26();
			}
		}

		// Token: 0x06000E69 RID: 3689 RVA: 0x0005E01C File Offset: 0x0005C21C
		public override void ApplyTheme(ChartTheme theme)
		{
			foreach (Indicator indicator in this.IndList)
			{
				if (indicator != null)
				{
					indicator.ApplyTheme(theme);
				}
			}
			if (base.SupportHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				foreach (GraphObj graphObj in base.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartKLine.<>c.<>9.method_3)))
				{
					((HiLowMarkTextObj)graphObj).method_2();
				}
			}
			this.method_147();
			base.ApplyTheme(theme);
		}

		// Token: 0x06000E6A RID: 3690 RVA: 0x0005E108 File Offset: 0x0005C308
		public override void vmethod_4(int int_0)
		{
			int num = base.method_4(int_0);
			foreach (Indicator indicator in this.IndList)
			{
				bool isSelected;
				if (isSelected = indicator.IsSelected)
				{
					indicator.IsSelected = false;
				}
				indicator.StartIdx = num;
				indicator.EndIdx = int_0;
				for (int i = num; i <= int_0; i++)
				{
					indicator.AddNewItem(i);
				}
				if (isSelected)
				{
					indicator.IsSelected = true;
				}
			}
			base.vmethod_4(int_0);
		}

		// Token: 0x06000E6B RID: 3691 RVA: 0x0005E1A8 File Offset: 0x0005C3A8
		public override void vmethod_6(HDTick hdtick_0)
		{
			foreach (Indicator indicator in this.IndList)
			{
				int itemIdx = this.ChtCtrl_KLine.IndexOfLastItemShown + 1;
				indicator.AddNewItem(itemIdx, hdtick_0);
			}
			if (base.IsXAxisVisible)
			{
				base.GraphPane.XAxis.Scale.BaseTic = (double)this.method_95();
			}
			base.vmethod_6(hdtick_0);
		}

		// Token: 0x06000E6C RID: 3692 RVA: 0x0005E238 File Offset: 0x0005C438
		public override void vmethod_5(HisData hisData_0)
		{
			int num = this.ChtCtrl_KLine.IndexOfLastItemShown + 1;
			if (num >= base.HisDataPeriodSet.PeriodHisDataList.Count)
			{
				Class184.smethod_0(new ArgumentOutOfRangeException(Class521.smethod_0(47983) + num));
			}
			else
			{
				foreach (Indicator indicator in this.IndList)
				{
					indicator.AddNewItem(num, hisData_0);
				}
			}
			if (base.IsXAxisVisible)
			{
				base.GraphPane.XAxis.Scale.BaseTic = (double)this.method_95();
			}
			base.vmethod_5(hisData_0);
		}

		// Token: 0x06000E6D RID: 3693 RVA: 0x0005E2FC File Offset: 0x0005C4FC
		public override void vmethod_7(int int_0, HisData hisData_0, bool bool_4)
		{
			HisData hisData = hisData_0.Clone();
			if (bool_4)
			{
				hisData = this.method_90(int_0, hisData_0);
				if (hisData == null)
				{
					return;
				}
				using (List<Indicator>.Enumerator enumerator = this.IndList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Indicator indicator = enumerator.Current;
						indicator.UpdateLastItem(int_0, hisData);
					}
					goto IL_84;
				}
			}
			foreach (Indicator indicator2 in this.IndList)
			{
				indicator2.UpdateLastItem(int_0, hisData_0);
			}
			IL_84:
			base.vmethod_7(int_0, hisData_0, bool_4);
		}

		// Token: 0x06000E6E RID: 3694 RVA: 0x0005E3B4 File Offset: 0x0005C5B4
		public HisData method_90(int int_0, HisData hisData_0)
		{
			HisData hisData = null;
			bool flag = false;
			List<HisData> list = this.method_91(int_0, hisData_0.Date, ref flag);
			if (list != null && list.Any<HisData>())
			{
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
				{
					hisData = hisData_0;
				}
				else
				{
					bool flag2 = false;
					if (!flag && base.Symbol.IsStock && Base.UI.Form.StockRestorationMethod != null)
					{
						stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
						if (!(stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null) && base.SymbDataSet.method_102(list.First<HisData>().Date, hisData_0.Date) != null)
						{
							flag2 = true;
						}
					}
					hisData = list.First<HisData>().Clone();
					if (flag2)
					{
						hisData = base.SymbDataSet.method_91(hisData);
					}
					double num = hisData.High;
					double num2 = hisData.Low;
					foreach (HisData hisData2 in list)
					{
						HisData hisData3 = hisData2;
						if (flag2)
						{
							hisData3 = base.SymbDataSet.method_91(hisData2);
						}
						if (hisData3.High > num)
						{
							num = hisData3.High;
						}
						if (hisData3.Low < num2)
						{
							num2 = hisData3.Low;
						}
					}
					hisData.High = num;
					hisData.Low = num2;
					hisData.Date = hisData_0.Date;
					hisData.Close = hisData_0.Close;
					hisData = base.SymbDataSet.method_91(hisData);
				}
			}
			return hisData;
		}

		// Token: 0x06000E6F RID: 3695 RVA: 0x0005E560 File Offset: 0x0005C760
		public List<HisData> method_91(int int_0, DateTime dateTime_0, ref bool bool_4)
		{
			DateTime dateTime_ = DateTime.MinValue;
			List<HisData> result;
			if (int_0 > 0 && int_0 <= base.HisDataPeriodSet.PeriodHisDataList.Count)
			{
				dateTime_ = base.HisDataPeriodSet.PeriodHisDataList.Keys[int_0 - 1];
				ChtCtrl chtCtrl = this.method_92();
				SortedList<DateTime, HisData> sortedList_ = chtCtrl.method_40(dateTime_0, ref bool_4);
				int? nullable_ = chtCtrl.method_41();
				result = base.SymbDataSet.method_105(sortedList_, dateTime_, dateTime_0, nullable_);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000E70 RID: 3696 RVA: 0x0005E5D0 File Offset: 0x0005C7D0
		public ChtCtrl method_92()
		{
			ChartKLine.Class218 @class = new ChartKLine.Class218();
			@class.chartKLine_0 = this;
			@class.nullable_0 = null;
			@class.nullable_1 = null;
			if (Base.UI.Form.IsJustSpanMoved)
			{
				if (Base.UI.Form.SpanMoveChtCtrl != null)
				{
					@class.nullable_0 = new PeriodType?(Base.UI.Form.SpanMoveChtCtrl.PeriodType);
					@class.nullable_1 = Base.UI.Form.SpanMoveChtCtrl.PeriodUnits;
				}
			}
			else if (Base.UI.Form.AutoPlayPeriodType != null && Base.UI.Form.AutoPlayPeriodUnits != null)
			{
				@class.nullable_0 = new PeriodType?(Base.UI.Form.AutoPlayPeriodType.Value);
				@class.nullable_1 = new int?(Base.UI.Form.AutoPlayPeriodUnits.Value);
			}
			if (base.ChtCtrl.method_36(@class.nullable_0, @class.nullable_1))
			{
				ChtCtrl chtCtrl = Base.UI.ChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(@class.method_0));
				if (chtCtrl != null)
				{
					return chtCtrl;
				}
			}
			return base.ChtCtrl;
		}

		// Token: 0x06000E71 RID: 3697 RVA: 0x0005E6F4 File Offset: 0x0005C8F4
		public override void vmethod_9(int int_0, HDTick hdtick_0, bool bool_4)
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.UpdateLastItem(int_0, hdtick_0);
			}
			base.vmethod_9(int_0, hdtick_0, bool_4);
		}

		// Token: 0x06000E72 RID: 3698 RVA: 0x00006661 File Offset: 0x00004861
		public override void vmethod_11(HisData hisData_0)
		{
			this.method_147();
			this.vmethod_25();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x06000E73 RID: 3699 RVA: 0x0005E754 File Offset: 0x0005C954
		public virtual void vmethod_25()
		{
			this.method_93();
			if (base.SupportHighLowMark && !Base.UI.Form.NoShowHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				new HiLowMarkTextObj(this, true);
				new HiLowMarkTextObj(this, false);
			}
		}

		// Token: 0x06000E74 RID: 3700 RVA: 0x0005E7A4 File Offset: 0x0005C9A4
		public void method_93()
		{
			if (base.SupportHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				base.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(ChartKLine.<>c.<>9.method_4));
			}
		}

		// Token: 0x06000E75 RID: 3701 RVA: 0x0005E800 File Offset: 0x0005CA00
		public double method_94(DateTime dateTime_0)
		{
			ChartKLine.Class219 @class = new ChartKLine.Class219();
			@class.chartKLine_0 = this;
			@class.dateTime_0 = dateTime_0;
			double result = 0.0;
			@class.int_0 = base.ChtCtrl.HisDataPeriodSet.method_37(@class.dateTime_0, true);
			try
			{
				if (@class.int_0 > 0)
				{
					result = base.HisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Sum(new Func<KeyValuePair<DateTime, HisData>, double?>(ChartKLine.<>c.<>9.method_5)).Value;
				}
				else
				{
					result = base.HisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).Sum(new Func<KeyValuePair<DateTime, HisData>, double?>(ChartKLine.<>c.<>9.method_6)).Value;
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x06000E76 RID: 3702 RVA: 0x0005E8FC File Offset: 0x0005CAFC
		public override void vmethod_12()
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.ResetCurve();
			}
			base.vmethod_12();
		}

		// Token: 0x06000E77 RID: 3703 RVA: 0x0005E958 File Offset: 0x0005CB58
		public virtual void vmethod_26()
		{
			GraphPane graphPane = base.GraphPane;
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 15)
			{
				graphPane.XAxis.Scale.Format = Class521.smethod_0(48020);
				graphPane.XAxis.Scale.MajorStep = (double)this.XAxisTimeUnit;
			}
			else if ((base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value >= 15) || base.HisDataPeriodSet.PeriodType != PeriodType.ByMins)
			{
				if (base.HisDataPeriodSet.PeriodType != PeriodType.ByWeek && base.HisDataPeriodSet.PeriodType != PeriodType.ByMonth && (base.HisDataPeriodSet.PeriodType != PeriodType.ByDay || base.HisDataPeriodSet.PeriodUnits == null || base.HisDataPeriodSet.PeriodUnits.Value <= 1))
				{
					graphPane.XAxis.Scale.Format = Class521.smethod_0(48038);
				}
				else
				{
					graphPane.XAxis.Scale.Format = Class521.smethod_0(48029);
				}
				if (Base.UI.Form.IsInBlindTestMode)
				{
					graphPane.XAxis.Scale.Format = Class521.smethod_0(48047);
				}
				if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
				{
					int? numberOfStickItemsPerDay = base.HisDataPeriodSet.NumberOfStickItemsPerDay;
					if (numberOfStickItemsPerDay == null)
					{
						if (base.SymbDataSet.CurrDate != null)
						{
							DateTime date = base.SymbDataSet.CurrDate.Value.Date;
						}
						else
						{
							DateTime date2 = base.HisDataPeriodSet.PeriodHisDataList.Keys.Last<DateTime>().Date;
						}
						base.HisDataPeriodSet.NumberOfStickItemsPerDay = base.HisDataPeriodSet.method_44(base.SymbDataSet.CurrDate.Value.Date);
						numberOfStickItemsPerDay = base.HisDataPeriodSet.NumberOfStickItemsPerDay;
					}
					if (numberOfStickItemsPerDay != null)
					{
						graphPane.XAxis.Scale.MajorStep = (double)((int)Math.Ceiling((double)this.XAxisTimeUnit / (double)numberOfStickItemsPerDay.Value) * numberOfStickItemsPerDay.Value);
					}
				}
			}
			try
			{
				graphPane.XAxis.Scale.BaseTic = (double)this.method_96();
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06000E78 RID: 3704 RVA: 0x0005EBD8 File Offset: 0x0005CDD8
		private int method_95()
		{
			int num = 1;
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			int indexOfLastItemShown = this.ChtCtrl_KLine.IndexOfLastItemShown;
			int indexOfFirstItemShown = this.ChtCtrl_KLine.IndexOfFirstItemShown;
			int maxSticksPerChart = this.ChtCtrl_KLine.MaxSticksPerChart;
			int num2 = (int)base.GraphPane.XAxis.Scale.BaseTic;
			int xaxisTimeUnit = (int)this.XAxisTimeUnit;
			int result;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 30)
			{
				if (indexOfLastItemShown + 2 > xaxisTimeUnit)
				{
					if (num2 == 1)
					{
						if (indexOfFirstItemShown + xaxisTimeUnit < base.HisDataPeriodSet.PeriodHisDataList.Count && Utility.CanExactDiv(base.HisDataPeriodSet.PeriodHisDataList.Keys[indexOfFirstItemShown + xaxisTimeUnit].Minute, xaxisTimeUnit))
						{
							return xaxisTimeUnit;
						}
						int num3 = this.method_96() - 1;
						if (num3 != 0)
						{
							return num3;
						}
					}
					else
					{
						if (indexOfLastItemShown < maxSticksPerChart - 1)
						{
							return num2;
						}
						return num2 - 1;
					}
				}
				result = num;
			}
			else
			{
				int num4 = (int)base.GraphPane.XAxis.Scale.MajorStep;
				if (num2 == 1)
				{
					bool flag = false;
					if (base.PeriodHisDataList.Count > indexOfFirstItemShown + num4)
					{
						flag = base.method_69(indexOfFirstItemShown + num4 - 1);
					}
					if (indexOfLastItemShown + 2 > num4 && flag)
					{
						result = num4;
					}
					else
					{
						result = this.method_96();
					}
				}
				else if (indexOfLastItemShown < maxSticksPerChart - 1)
				{
					result = num2;
				}
				else
				{
					result = num2 - 1;
				}
			}
			return result;
		}

		// Token: 0x06000E79 RID: 3705 RVA: 0x0005ED5C File Offset: 0x0005CF5C
		private int method_96()
		{
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			int xaxisTimeUnit = (int)this.XAxisTimeUnit;
			int maxSticksPerChart = this.ChtCtrl_KLine.MaxSticksPerChart;
			int num;
			int num2;
			if (this.ChtCtrl_KLine.IsInRetroMode)
			{
				num = this.ChtCtrl_KLine.RetroModeFirstItemIdx;
				num2 = this.ChtCtrl_KLine.RetroModeLastItemIdx;
			}
			else
			{
				num = this.ChtCtrl_KLine.IndexOfFirstItemShown;
				num2 = this.ChtCtrl_KLine.IndexOfLastItemShown;
			}
			bool flag = false;
			int i;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 15)
			{
				int result;
				try
				{
					if (num < base.HisDataPeriodSet.PeriodHisDataList.Count)
					{
						DateTime dateTime = base.HisDataPeriodSet.PeriodHisDataList.Keys[num];
						if (periodUnits.Value < xaxisTimeUnit && num2 + 2 > xaxisTimeUnit)
						{
							i = 0;
							while (i + 1 < maxSticksPerChart)
							{
								if (!Utility.CanExactDiv(periodUnits.Value * i + dateTime.Minute, xaxisTimeUnit))
								{
									i++;
								}
								else
								{
									flag = true;
									IL_FA:
									if (flag)
									{
										result = i + 1;
										goto IL_10B;
									}
									goto IL_106;
								}
							}
							goto IL_FA;
						}
					}
					IL_106:
					goto IL_14D;
				}
				catch
				{
					goto IL_14D;
				}
				IL_10B:
				return result;
			}
			int num3 = maxSticksPerChart;
			if (num2 < maxSticksPerChart)
			{
				num3 = num2 + 1;
			}
			i = 0;
			while (i < num3)
			{
				if (!base.method_69(i + num))
				{
					i++;
				}
				else
				{
					flag = true;
					IL_141:
					if (flag)
					{
						return i + 2;
					}
					goto IL_14D;
				}
			}
			goto IL_141;
			IL_14D:
			return 1;
		}

		// Token: 0x06000E7A RID: 3706 RVA: 0x0005EED0 File Offset: 0x0005D0D0
		protected virtual void vmethod_27(ZedGraphControl zedGraphControl_1, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			contextMenuStrip_0.Items.Clear();
			Indicator indicator = this.method_143(point_0);
			if (indicator != null && indicator.IsSelected)
			{
				this.method_117(contextMenuStrip_0, indicator);
			}
			else
			{
				if (!Base.UI.IsInCreateNewPageState)
				{
					base.method_10(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
					this.method_97(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
				}
				this.method_99(contextMenuStrip_0);
				this.method_73(contextMenuStrip_0);
				this.method_76(contextMenuStrip_0);
				base.method_38(contextMenuStrip_0);
				this.vmethod_28(contextMenuStrip_0);
				base.method_13(contextMenuStrip_0);
				base.method_14(contextMenuStrip_0);
				base.method_15(contextMenuStrip_0);
				this.method_105(contextMenuStrip_0);
				this.method_112(contextMenuStrip_0);
				this.method_104(contextMenuStrip_0);
				if (base.Symbol.IsStock)
				{
					this.method_118(contextMenuStrip_0);
				}
				base.method_26(contextMenuStrip_0);
				if (!Base.UI.IsInCreateNewPageState)
				{
					base.method_38(contextMenuStrip_0);
					base.method_11(contextMenuStrip_0);
					this.method_114(contextMenuStrip_0);
					this.method_115(contextMenuStrip_0);
					base.method_31(contextMenuStrip_0);
					this.method_113(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
					base.method_32(contextMenuStrip_0);
					if (base.ChtCtrl.SymbDataSet != null && base.ChtCtrl.SymbDataSet.CurrHisData != null)
					{
						base.method_33(contextMenuStrip_0);
					}
					base.method_12(contextMenuStrip_0);
				}
				Base.UI.smethod_73(contextMenuStrip_0);
			}
		}

		// Token: 0x06000E7B RID: 3707 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_28(ContextMenuStrip contextMenuStrip_0)
		{
		}

		// Token: 0x06000E7C RID: 3708 RVA: 0x0005F00C File Offset: 0x0005D20C
		protected void method_97(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(23658);
			if (Base.UI.DrawOdrWnd != null)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_21);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Image = Class375.DrawOdrLineBtnIcon;
			toolStripMenuItem.Click += this.method_98;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E7D RID: 3709 RVA: 0x00006678 File Offset: 0x00004878
		private void method_98(object sender, EventArgs e)
		{
			Base.UI.smethod_116();
		}

		// Token: 0x06000E7E RID: 3710 RVA: 0x0005F0A0 File Offset: 0x0005D2A0
		protected void method_99(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48068);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			toolStripMenuItem.MouseEnter += this.method_102;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(48085);
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			toolStripMenuItem2.DropDownItems.Add(new ToolStripMenuItem());
			toolStripMenuItem2.MouseEnter += this.method_100;
		}

		// Token: 0x06000E7F RID: 3711 RVA: 0x0005F13C File Offset: 0x0005D33C
		private void method_100(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				this.method_101(toolStripMenuItem, ChartType.MACD);
			}
		}

		// Token: 0x06000E80 RID: 3712 RVA: 0x0005F160 File Offset: 0x0005D360
		private void method_101(ToolStripMenuItem toolStripMenuItem_0, ChartType chartType_1)
		{
			toolStripMenuItem_0.DropDownItems.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				UserDefineIndScript[] array;
				if (chartType_1 == ChartType.CandleStick)
				{
					array = userDefineIndGroup.UDSList.Where(new Func<UserDefineIndScript, bool>(ChartKLine.<>c.<>9.method_7)).ToArray<UserDefineIndScript>();
				}
				else
				{
					if (chartType_1 != ChartType.MACD)
					{
						break;
					}
					array = userDefineIndGroup.UDSList.Where(new Func<UserDefineIndScript, bool>(ChartKLine.<>c.<>9.method_8)).ToArray<UserDefineIndScript>();
				}
				if (array == null)
				{
					break;
				}
				if (array.Any<UserDefineIndScript>())
				{
					ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
					toolStripMenuItem.Text = userDefineIndGroup.Group;
					toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem);
					foreach (UserDefineIndScript userDefineIndScript in array)
					{
						ChartKLine.Class220 @class = new ChartKLine.Class220();
						@class.toolStripMenuItem_0 = Base.UI.smethod_77(userDefineIndScript.NameAndScript);
						@class.toolStripMenuItem_0.Name = userDefineIndScript.Name;
						@class.toolStripMenuItem_0.Click += this.method_103;
						toolStripMenuItem.DropDownItems.Add(@class.toolStripMenuItem_0);
						if (this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
						{
							@class.toolStripMenuItem_0.Checked = true;
						}
					}
				}
			}
		}

		// Token: 0x06000E81 RID: 3713 RVA: 0x0005F310 File Offset: 0x0005D510
		private void method_102(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				this.method_101(toolStripMenuItem, ChartType.CandleStick);
			}
		}

		// Token: 0x06000E82 RID: 3714 RVA: 0x0005F334 File Offset: 0x0005D534
		private void method_103(object sender, EventArgs e)
		{
			ChartKLine.Class221 @class = new ChartKLine.Class221();
			@class.toolStripMenuItem_0 = (sender as ToolStripMenuItem);
			if (@class.toolStripMenuItem_0 != null)
			{
				if (!@class.toolStripMenuItem_0.Checked)
				{
					string name = @class.toolStripMenuItem_0.Name;
					this.method_80(name);
				}
				else
				{
					Indicator indicator = this.ChtCtrl_KLine.IndList.FirstOrDefault(new Func<Indicator, bool>(@class.method_0));
					if (indicator != null)
					{
						indicator.RemoveFromChart();
					}
				}
				return;
			}
			throw new Exception(Class521.smethod_0(48102));
		}

		// Token: 0x06000E83 RID: 3715 RVA: 0x0005F3B8 File Offset: 0x0005D5B8
		protected void method_104(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48143);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(16325);
			toolStripMenuItem2.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_25) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(16338);
			toolStripMenuItem3.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_26) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(16351);
			toolStripMenuItem4.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_27) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = Class521.smethod_0(48169);
			toolStripMenuItem5.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_36) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem6 = Base.UI.smethod_76();
			toolStripMenuItem6.Text = Class521.smethod_0(16364);
			toolStripMenuItem6.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_28) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem7 = Base.UI.smethod_76();
			toolStripMenuItem7.Text = Class521.smethod_0(16377);
			toolStripMenuItem7.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_29) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem8 = Base.UI.smethod_76();
			toolStripMenuItem8.Text = Class521.smethod_0(37487);
			toolStripMenuItem8.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_30) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem9 = Base.UI.smethod_76();
			toolStripMenuItem9.Text = Class521.smethod_0(37500);
			toolStripMenuItem9.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_31) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem10 = Base.UI.smethod_76();
			toolStripMenuItem10.Text = Class521.smethod_0(48182);
			toolStripMenuItem10.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_32) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem11 = Base.UI.smethod_76();
			toolStripMenuItem11.Text = Class521.smethod_0(21727);
			toolStripMenuItem11.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_33) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem12 = Base.UI.smethod_76();
			toolStripMenuItem12.Text = Class521.smethod_0(48195);
			toolStripMenuItem12.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_34) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem13 = Base.UI.smethod_76();
			toolStripMenuItem13.Text = Class521.smethod_0(48200);
			toolStripMenuItem13.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_35) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem14 = Base.UI.smethod_76();
			toolStripMenuItem14.Text = Class521.smethod_0(48205);
			toolStripMenuItem14.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_37) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem15 = Base.UI.smethod_76();
			toolStripMenuItem15.Text = Class521.smethod_0(48226);
			toolStripMenuItem15.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_38) + Class521.smethod_0(48160);
			ToolStripMenuItem toolStripMenuItem16 = Base.UI.smethod_76();
			toolStripMenuItem16.Text = Class521.smethod_0(48247);
			toolStripMenuItem16.ShortcutKeyDisplayString = Class186.smethod_5(Enum3.const_39) + Class521.smethod_0(48160);
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4,
				toolStripMenuItem5,
				toolStripMenuItem6,
				toolStripMenuItem7,
				toolStripMenuItem8,
				toolStripMenuItem9,
				toolStripMenuItem10,
				toolStripMenuItem11,
				toolStripMenuItem12,
				toolStripMenuItem13,
				toolStripMenuItem14,
				toolStripMenuItem15,
				toolStripMenuItem16
			});
			ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
			if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
			{
				if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits != null)
				{
					int? periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
					if (!(periodUnits.GetValueOrDefault() == 1 & periodUnits != null))
					{
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 3 & periodUnits != null)
						{
							toolStripMenuItem3.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 5 & periodUnits != null)
						{
							toolStripMenuItem4.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 10 & periodUnits != null)
						{
							toolStripMenuItem5.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 15 & periodUnits != null)
						{
							toolStripMenuItem6.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 30 & periodUnits != null)
						{
							toolStripMenuItem7.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 60 & periodUnits != null)
						{
							toolStripMenuItem8.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 120 & periodUnits != null)
						{
							toolStripMenuItem9.Checked = true;
							goto IL_69C;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 240 & periodUnits != null)
						{
							toolStripMenuItem10.Checked = true;
							goto IL_69C;
						}
						if (Utility.CanExactDiv(chtCtrl_KLine.HisDataPeriodSet.PeriodUnits.Value, 60))
						{
							toolStripMenuItem15.Checked = true;
							goto IL_69C;
						}
						toolStripMenuItem14.Checked = true;
						goto IL_69C;
					}
				}
				toolStripMenuItem2.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByDay)
			{
				if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits != null)
				{
					if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits == null || chtCtrl_KLine.HisDataPeriodSet.PeriodUnits.Value != 1)
					{
						toolStripMenuItem16.Checked = true;
						goto IL_69C;
					}
				}
				toolStripMenuItem11.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByWeek)
			{
				toolStripMenuItem12.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByMonth)
			{
				toolStripMenuItem13.Checked = true;
			}
			IL_69C:
			foreach (object obj in toolStripMenuItem.DropDownItems)
			{
				if (obj is ToolStripMenuItem)
				{
					((ToolStripMenuItem)obj).Click += this.method_129;
				}
			}
		}

		// Token: 0x06000E84 RID: 3716 RVA: 0x0005FAD0 File Offset: 0x0005DCD0
		public void method_105(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48264);
			toolStripMenuItem.DropDownOpening += this.method_106;
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E85 RID: 3717 RVA: 0x0005FB28 File Offset: 0x0005DD28
		private void method_106(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			toolStripMenuItem.DropDownItems.Clear();
			this.method_107(toolStripMenuItem);
		}

		// Token: 0x06000E86 RID: 3718 RVA: 0x0005FB50 File Offset: 0x0005DD50
		private void method_107(ToolStripMenuItem toolStripMenuItem_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48281);
			toolStripMenuItem.Click += this.method_108;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(48318);
			toolStripMenuItem2.Click += this.method_109;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(48355);
			toolStripMenuItem3.Click += this.method_110;
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(48384);
			toolStripMenuItem4.Click += this.method_111;
			toolStripMenuItem_0.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem,
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				if (Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
				{
					toolStripMenuItem2.Checked = true;
				}
				else if (Base.UI.Form.KLineType.Value == KLineType.OHLCBar)
				{
					toolStripMenuItem3.Checked = true;
				}
				else
				{
					toolStripMenuItem4.Checked = true;
				}
			}
			else
			{
				toolStripMenuItem.Checked = true;
			}
		}

		// Token: 0x06000E87 RID: 3719 RVA: 0x0005FC98 File Offset: 0x0005DE98
		private void method_108(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_EmptyUpK);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E88 RID: 3720 RVA: 0x0005FCE4 File Offset: 0x0005DEE4
		private void method_109(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.CandleStick_SolidUpK)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_SolidUpK);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E89 RID: 3721 RVA: 0x0005FD34 File Offset: 0x0005DF34
		private void method_110(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.OHLCBar)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.OHLCBar);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E8A RID: 3722 RVA: 0x0005FD84 File Offset: 0x0005DF84
		private void method_111(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.HLCBar)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.HLCBar);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E8B RID: 3723 RVA: 0x0005FDD4 File Offset: 0x0005DFD4
		protected void method_112(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48409);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(48426);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(48435);
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(48444);
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = Class521.smethod_0(48453);
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4,
				toolStripMenuItem5
			});
			switch (this.ChtCtrl_KLine.NumberOfCharts)
			{
			case 1:
				toolStripMenuItem2.Checked = true;
				break;
			case 2:
				toolStripMenuItem3.Checked = true;
				break;
			case 3:
				toolStripMenuItem4.Checked = true;
				break;
			case 4:
				toolStripMenuItem5.Checked = true;
				break;
			}
			toolStripMenuItem2.Click += this.method_134;
			toolStripMenuItem3.Click += this.method_135;
			toolStripMenuItem4.Click += this.method_136;
			toolStripMenuItem5.Click += this.method_137;
		}

		// Token: 0x06000E8C RID: 3724 RVA: 0x0005FF18 File Offset: 0x0005E118
		protected void method_113(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48462);
			if (Base.UI.DrawToolWnd != null)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			try
			{
				Keys keys = Class210.smethod_3(Enum3.const_19);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Image = Class375.LineD;
			toolStripMenuItem.Click += this.method_139;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8D RID: 3725 RVA: 0x0005FFAC File Offset: 0x0005E1AC
		protected void method_114(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48479);
			toolStripMenuItem.Click += this.method_132;
			if (Base.UI.Form.IfDispDayDivLine)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8E RID: 3726 RVA: 0x0006000C File Offset: 0x0005E20C
		protected void method_115(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48504);
			toolStripMenuItem.Click += this.method_133;
			if (Base.UI.Form.IfDisableTsOdrLine)
			{
				toolStripMenuItem.Checked = false;
			}
			else
			{
				toolStripMenuItem.Checked = true;
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8F RID: 3727 RVA: 0x0006006C File Offset: 0x0005E26C
		protected void method_116(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48525);
			toolStripMenuItem.Click += this.method_138;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(48542);
			toolStripMenuItem2.Click += this.method_88;
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(46831);
			toolStripMenuItem3.Click += this.method_140;
			contextMenuStrip_0.Items.Add(toolStripMenuItem3);
		}

		// Token: 0x06000E90 RID: 3728 RVA: 0x0006013C File Offset: 0x0005E33C
		protected void method_117(ContextMenuStrip contextMenuStrip_0, Indicator indicator_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48525);
			toolStripMenuItem.Click += this.method_138;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(46831);
			toolStripMenuItem2.Click += this.method_140;
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(48542);
			toolStripMenuItem3.Click += this.method_88;
			contextMenuStrip_0.Items.Add(toolStripMenuItem3);
		}

		// Token: 0x06000E91 RID: 3729 RVA: 0x0006020C File Offset: 0x0005E40C
		protected void method_118(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = Class521.smethod_0(48559);
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = Class521.smethod_0(42323);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = Class521.smethod_0(42336);
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = Class521.smethod_0(42349);
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
			if (Base.UI.Form.StockRestorationMethod == null)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
			}
			StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
			if (stockRestorationMethod != null)
			{
				switch (stockRestorationMethod.GetValueOrDefault())
				{
				case StockRestorationMethod.Prior:
					toolStripMenuItem2.Checked = true;
					break;
				case StockRestorationMethod.Later:
					toolStripMenuItem3.Checked = true;
					break;
				case StockRestorationMethod.None:
					toolStripMenuItem4.Checked = true;
					break;
				}
			}
			toolStripMenuItem2.Click += this.method_119;
			toolStripMenuItem3.Click += this.method_120;
			toolStripMenuItem4.Click += this.method_121;
		}

		// Token: 0x06000E92 RID: 3730 RVA: 0x00060344 File Offset: 0x0005E544
		private void method_119(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (!(stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Prior & stockRestorationMethod != null))
				{
					Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
				}
			}
		}

		// Token: 0x06000E93 RID: 3731 RVA: 0x00060398 File Offset: 0x0005E598
		private void method_120(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				if (Base.UI.Form.StockRestorationMethod == null)
				{
					return;
				}
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
				{
					return;
				}
			}
			Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Later);
		}

		// Token: 0x06000E94 RID: 3732 RVA: 0x00060400 File Offset: 0x0005E600
		private void method_121(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				if (Base.UI.Form.StockRestorationMethod == null)
				{
					return;
				}
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null)
				{
					return;
				}
			}
			Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.None);
		}

		// Token: 0x06000E95 RID: 3733 RVA: 0x00060468 File Offset: 0x0005E668
		protected virtual bool vmethod_29(Indicator indicator_0, ChartKLine chartKLine_0, ToolStripMenuItem toolStripMenuItem_0)
		{
			if (toolStripMenuItem_0 != null)
			{
				this.method_127(toolStripMenuItem_0, indicator_0.Chart);
			}
			if (indicator_0.IsMainChartInd)
			{
				ChartCS chartCS;
				if (chartKLine_0.ChartType == ChartType.CandleStick)
				{
					chartCS = (chartKLine_0 as ChartCS);
				}
				else
				{
					chartCS = (this.ChtCtrl_KLine.ChartList.FirstOrDefault(new Func<ChartBase, bool>(ChartKLine.<>c.<>9.method_9)) as ChartCS);
				}
				if (chartCS == null)
				{
					return false;
				}
				if (!indicator_0.InitInd(chartCS))
				{
					return false;
				}
				chartCS.method_123(indicator_0, chartCS);
			}
			else
			{
				Class225 @class;
				if (chartKLine_0.ChartType == ChartType.MACD)
				{
					@class = (chartKLine_0 as Class225);
				}
				else
				{
					@class = (this.ChtCtrl_KLine.ChartList.FirstOrDefault(new Func<ChartBase, bool>(ChartKLine.<>c.<>9.method_10)) as Class225);
				}
				if (@class == null)
				{
					return false;
				}
				this.ChtCtrl_KLine.method_74(@class, indicator_0);
			}
			return true;
		}

		// Token: 0x06000E96 RID: 3734 RVA: 0x00006681 File Offset: 0x00004881
		public void method_122(Indicator indicator_0)
		{
			this.method_124(indicator_0, this, false);
		}

		// Token: 0x06000E97 RID: 3735 RVA: 0x0000668E File Offset: 0x0000488E
		public void method_123(Indicator indicator_0, ChartKLine chartKLine_0)
		{
			this.method_124(indicator_0, chartKLine_0, true);
		}

		// Token: 0x06000E98 RID: 3736 RVA: 0x00060558 File Offset: 0x0005E758
		public void method_124(Indicator indicator_0, ChartKLine chartKLine_0, bool bool_4)
		{
			if (!this.method_126(indicator_0) || bool_4)
			{
				bool isSelected;
				if (isSelected = indicator_0.IsSelected)
				{
					indicator_0.IsSelected = false;
				}
				if (this.method_126(indicator_0))
				{
					indicator_0.RemoveFromChart();
				}
				indicator_0.InitItem();
				int num = this.IsInRetroMode ? this.ChtCtrl_KLine.RetroModeLastItemIdx : this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.IsInRetroMode)
				{
					num = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				indicator_0.StartIdx = 0;
				indicator_0.EndIdx = num;
				for (int i = 0; i <= num; i++)
				{
					indicator_0.AddNewItem(i);
				}
				if (!this.method_126(indicator_0))
				{
					chartKLine_0.IndList.Add(indicator_0);
				}
				this.vmethod_19();
				indicator_0.IndicatorRemoved += this.vmethod_30;
				if (isSelected)
				{
					indicator_0.IsSelected = true;
				}
				chartKLine_0.vmethod_11(base.SymbDataSet.LastHisData);
				chartKLine_0.vmethod_15();
			}
		}

		// Token: 0x06000E99 RID: 3737 RVA: 0x00060644 File Offset: 0x0005E844
		public void method_125(Indicator indicator_0)
		{
			if (this.IndList.Contains(indicator_0))
			{
				if (indicator_0.IsSelected)
				{
					indicator_0.IsSelected = false;
				}
				indicator_0.InitItem();
				int num = this.IsInRetroMode ? this.ChtCtrl_KLine.RetroModeLastItemIdx : this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.IsInRetroMode)
				{
					num = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				indicator_0.StartIdx = 0;
				indicator_0.EndIdx = num;
				for (int i = 0; i <= num; i++)
				{
					indicator_0.AddNewItem(i);
				}
			}
		}

		// Token: 0x06000E9A RID: 3738 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_30(object sender, EventArgs27 e)
		{
		}

		// Token: 0x06000E9B RID: 3739 RVA: 0x000606D0 File Offset: 0x0005E8D0
		public bool method_126(Indicator indicator_0)
		{
			ChartKLine.Class222 @class = new ChartKLine.Class222();
			@class.indicator_0 = indicator_0;
			@class.chartKLine_0 = this;
			bool result;
			if (this.IndList != null)
			{
				result = this.IndList.Exists(new Predicate<Indicator>(@class.method_0));
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000E9C RID: 3740 RVA: 0x00060718 File Offset: 0x0005E918
		private bool method_127(ToolStripMenuItem toolStripMenuItem_0, ChartKLine chartKLine_0)
		{
			ChartKLine.Class223 @class = new ChartKLine.Class223();
			@class.chartKLine_0 = chartKLine_0;
			bool result;
			using (IEnumerator enumerator = toolStripMenuItem_0.DropDownItems.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					ChartKLine.Class224 class2 = new ChartKLine.Class224();
					class2.class223_0 = @class;
					class2.toolStripMenuItem_0 = (ToolStripMenuItem)enumerator.Current;
					if (class2.toolStripMenuItem_0.Checked)
					{
						try
						{
							this.ChtCtrl_KLine.IndList.Single(new Func<Indicator, bool>(class2.method_0)).RemoveFromChart();
							class2.toolStripMenuItem_0.Checked = false;
							result = true;
							goto IL_9E;
						}
						catch
						{
						}
					}
				}
			}
			return false;
			IL_9E:
			return result;
		}

		// Token: 0x06000E9D RID: 3741 RVA: 0x000607E8 File Offset: 0x0005E9E8
		private void method_128(object sender, EventArgs3 e)
		{
			Indicator ind = e.Ind;
			ChartKLine chart = e.Chart;
			ind.SetIndParams(e.IndParamList);
			if (e.OwnerItem != null)
			{
				this.method_127(e.OwnerItem, chart);
			}
			if (ind.IsMainChartInd)
			{
				if (ind.InitInd(chart))
				{
					this.method_123(ind, chart);
				}
			}
			else
			{
				this.ChtCtrl_KLine.method_73(ind);
			}
		}

		// Token: 0x06000E9E RID: 3742 RVA: 0x00060850 File Offset: 0x0005EA50
		private void method_129(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = (ToolStripMenuItem)sender;
			ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
			if (toolStripMenuItem.Text == Class521.smethod_0(16325))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(1));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48576))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(2));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(16338))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(3));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(16351))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(5));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48169))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(10));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(16364))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(15));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(16377))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(30));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(37487))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(60));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(37500))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(120));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48182))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(240));
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(21727))
			{
				chtCtrl_KLine.method_12(PeriodType.ByDay, null);
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48195))
			{
				chtCtrl_KLine.method_12(PeriodType.ByWeek, null);
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48200))
			{
				chtCtrl_KLine.method_12(PeriodType.ByMonth, null);
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48205))
			{
				this.method_130(Enum4.const_0);
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48226))
			{
				this.method_130(Enum4.const_1);
			}
			else if (toolStripMenuItem.Text == Class521.smethod_0(48247))
			{
				this.method_130(Enum4.const_2);
			}
		}

		// Token: 0x06000E9F RID: 3743 RVA: 0x00060AF0 File Offset: 0x0005ECF0
		private void method_130(Enum4 enum4_0)
		{
			int int_ = 10;
			if (enum4_0 == Enum4.const_0)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					int_ = Base.UI.Form.NMinsPeriodUnits.Value;
				}
			}
			else if (enum4_0 == Enum4.const_1)
			{
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					int_ = Base.UI.Form.NHoursPeriodUnits.Value;
				}
			}
			else if (Base.UI.Form.NDaysPeriodUnits != null)
			{
				int_ = Base.UI.Form.NDaysPeriodUnits.Value;
			}
			Form6 form = new Form6(enum4_0, int_);
			form.NPeriodSet += this.method_131;
			form.ShowDialog();
		}

		// Token: 0x06000EA0 RID: 3744 RVA: 0x00060BA4 File Offset: 0x0005EDA4
		private void method_131(EventArgs4 eventArgs4_0)
		{
			ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
			if (chtCtrl_KLine != null)
			{
				switch (eventArgs4_0.NPeriodType)
				{
				case Enum4.const_0:
					chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(eventArgs4_0.Period));
					Base.UI.Form.NMinsPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				case Enum4.const_1:
					chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(eventArgs4_0.Period * 60));
					Base.UI.Form.NHoursPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				case Enum4.const_2:
					chtCtrl_KLine.method_12(PeriodType.ByDay, new int?(eventArgs4_0.Period));
					Base.UI.Form.NDaysPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				}
			}
		}

		// Token: 0x06000EA1 RID: 3745 RVA: 0x0000669B File Offset: 0x0000489B
		private void method_132(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfDispDayDivLine)
			{
				Base.UI.Form.IfDispDayDivLine = false;
			}
			else
			{
				Base.UI.Form.IfDispDayDivLine = true;
			}
			Base.UI.smethod_31();
		}

		// Token: 0x06000EA2 RID: 3746 RVA: 0x000066C8 File Offset: 0x000048C8
		private void method_133(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfDisableTsOdrLine)
			{
				Base.UI.Form.IfDisableTsOdrLine = false;
			}
			else
			{
				Base.UI.Form.IfDisableTsOdrLine = true;
			}
			Base.UI.smethod_31();
		}

		// Token: 0x06000EA3 RID: 3747 RVA: 0x000066F5 File Offset: 0x000048F5
		private void method_134(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_80();
		}

		// Token: 0x06000EA4 RID: 3748 RVA: 0x00006704 File Offset: 0x00004904
		private void method_135(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_81();
		}

		// Token: 0x06000EA5 RID: 3749 RVA: 0x00006713 File Offset: 0x00004913
		private void method_136(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_83();
		}

		// Token: 0x06000EA6 RID: 3750 RVA: 0x00006722 File Offset: 0x00004922
		private void method_137(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_85();
		}

		// Token: 0x06000EA7 RID: 3751 RVA: 0x00060C5C File Offset: 0x0005EE5C
		private void method_138(object sender, EventArgs e)
		{
			Indicator selectedInd = this.ChtCtrl_KLine.SelectedInd;
			if (selectedInd != null)
			{
				selectedInd.IsSelected = false;
				selectedInd.RemoveFromChart();
			}
		}

		// Token: 0x06000EA8 RID: 3752 RVA: 0x00006731 File Offset: 0x00004931
		private void method_139(object sender, EventArgs e)
		{
			Base.UI.smethod_115();
		}

		// Token: 0x06000EA9 RID: 3753 RVA: 0x00060C88 File Offset: 0x0005EE88
		private void method_140(object sender, EventArgs e)
		{
			Form25 form = new Form25();
			List<IndEx> list = this.ChtCtrl_KLine.method_71();
			IndEx indEx = this.ChtCtrl_KLine.SelectedInd as IndEx;
			if (indEx != null)
			{
				list.Remove(indEx);
				list.Insert(0, indEx);
			}
			form.method_2(list);
			form.ShowDialog();
		}

		// Token: 0x06000EAA RID: 3754 RVA: 0x00060CDC File Offset: 0x0005EEDC
		protected virtual void vmethod_31(object sender, MouseEventArgs e)
		{
			if (Base.UI.DrawMode == DrawMode.Off)
			{
				GraphPane graphPane = base.GraphPane;
				PointF pointF_ = new PointF((float)e.X, (float)e.Y);
				Indicator indicator = this.method_143(pointF_);
				if (indicator != null)
				{
					if (!indicator.IsSelected)
					{
						indicator.IsSelected = true;
					}
					else
					{
						indicator.IsSelected = false;
					}
				}
				else if (this.ChtCtrl_KLine.SelectedInd != null)
				{
					this.ChtCtrl_KLine.SelectedInd.IsSelected = false;
				}
			}
		}

		// Token: 0x06000EAB RID: 3755 RVA: 0x00060D54 File Offset: 0x0005EF54
		private bool method_141(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			this.nullable_2 = null;
			this.ChtCtrl_KLine.Cursor = Cursors.Default;
			return false;
		}

		// Token: 0x06000EAC RID: 3756 RVA: 0x00060D84 File Offset: 0x0005EF84
		private bool method_142(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			PointD pointD = base.method_50(mouseEventArgs_0.X, mouseEventArgs_0.Y);
			this.nullable_2 = new double?(pointD.X);
			return false;
		}

		// Token: 0x06000EAD RID: 3757 RVA: 0x00060DBC File Offset: 0x0005EFBC
		protected override bool zedGraphControl_0_MouseMoveEvent(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if (mouseEventArgs_0.Button == MouseButtons.Left && this.nullable_2 != null && Base.UI.DrawingObj == null)
			{
				ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
				chtCtrl_KLine.Cursor = Cursors.NoMoveHoriz;
				PointD pointD = base.method_50(mouseEventArgs_0.X, mouseEventArgs_0.Y);
				if (!pointD.Equals(default(PointD)))
				{
					int num = Convert.ToInt32(Math.Round(pointD.X - this.nullable_2.Value));
					if (num != 0)
					{
						if (num > 0)
						{
							chtCtrl_KLine.method_120(num);
						}
						else
						{
							chtCtrl_KLine.method_122(-num);
						}
						this.nullable_2 = new double?(pointD.X);
						this.vmethod_25();
					}
				}
				result = true;
			}
			else
			{
				result = base.zedGraphControl_0_MouseMoveEvent(zedGraphControl_1, mouseEventArgs_0);
			}
			return result;
		}

		// Token: 0x06000EAE RID: 3758 RVA: 0x00060E94 File Offset: 0x0005F094
		private Indicator method_143(PointF pointF_0)
		{
			CurveItem curve;
			int num;
			if (base.GraphPane.FindNearestPoint(pointF_0, out curve, out num))
			{
				Indicator result;
				try
				{
					foreach (Indicator indicator in this.list_0)
					{
						if (indicator.GetIndNameByCurve(curve) != null)
						{
							result = indicator;
							goto IL_58;
						}
					}
					goto IL_5E;
				}
				catch (Exception)
				{
					result = null;
				}
				IL_58:
				return result;
			}
			IL_5E:
			return null;
		}

		// Token: 0x06000EAF RID: 3759 RVA: 0x0000673A File Offset: 0x0000493A
		private void method_144()
		{
			base.GraphPane.YAxis.ScaleFormatEvent += this.method_146;
		}

		// Token: 0x06000EB0 RID: 3760 RVA: 0x0000675A File Offset: 0x0000495A
		private void method_145()
		{
			base.GraphPane.YAxis.ScaleFormatEvent -= this.method_146;
		}

		// Token: 0x06000EB1 RID: 3761 RVA: 0x00060F24 File Offset: 0x0005F124
		private string method_146(GraphPane graphPane_0, Axis axis_0, double double_0, int int_0)
		{
			string result;
			if (double_0 == 0.0)
			{
				result = Class521.smethod_0(2841);
			}
			else
			{
				result = Class521.smethod_0(1449);
			}
			return result;
		}

		// Token: 0x06000EB2 RID: 3762 RVA: 0x00060F58 File Offset: 0x0005F158
		protected void method_147()
		{
			GraphPane graphPane = base.GraphPane;
			if (graphPane == null)
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(48589)));
			}
			else
			{
				List<GraphObj> list = graphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartKLine.<>c.<>9.method_11)).ToList<GraphObj>();
				for (int i = 0; i < list.Count; i++)
				{
					graphPane.GraphObjList.Remove(list[i]);
					list[i] = null;
				}
				if (Base.UI.Form.IfDispDayDivLine && base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
				{
					int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
					int periodOfChartDispDayDivLine = (int)Base.UI.Form.PeriodOfChartDispDayDivLine;
					if (periodUnits.GetValueOrDefault() <= periodOfChartDispDayDivLine & periodUnits != null)
					{
						List<int> list2 = this.ChtCtrl_KLine.method_107();
						if (list2 != null)
						{
							foreach (int num in list2)
							{
								Color color = Color.Silver;
								if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
								{
									color = Color.DarkRed;
								}
								LineObj lineObj = new LineObj(color, (double)num, graphPane.YAxis.Scale.Min, (double)num, graphPane.YAxis.Scale.Max);
								lineObj.IsClippedToChartRect = false;
								lineObj.Line.Style = DashStyle.Dash;
								lineObj.Tag = ChartKLine.string_9;
								lineObj.ZOrder = ZOrder.E_BehindCurves;
								graphPane.GraphObjList.Add(lineObj);
							}
						}
					}
				}
			}
		}

		// Token: 0x17000241 RID: 577
		// (get) Token: 0x06000EB3 RID: 3763 RVA: 0x00061110 File Offset: 0x0005F310
		// (set) Token: 0x06000EB4 RID: 3764 RVA: 0x0000677A File Offset: 0x0000497A
		public List<Indicator> IndList
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x17000242 RID: 578
		// (get) Token: 0x06000EB5 RID: 3765 RVA: 0x00061128 File Offset: 0x0005F328
		// (set) Token: 0x06000EB6 RID: 3766 RVA: 0x00006785 File Offset: 0x00004985
		public List<Indicator> SelectedIndList
		{
			get
			{
				return this.list_1;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x17000243 RID: 579
		// (get) Token: 0x06000EB7 RID: 3767 RVA: 0x00061140 File Offset: 0x0005F340
		public ChtCtrl_KLine ChtCtrl_KLine
		{
			get
			{
				return (ChtCtrl_KLine)base.ChtCtrl;
			}
		}

		// Token: 0x17000244 RID: 580
		// (get) Token: 0x06000EB8 RID: 3768 RVA: 0x0006115C File Offset: 0x0005F35C
		// (set) Token: 0x06000EB9 RID: 3769 RVA: 0x00006790 File Offset: 0x00004990
		public TimeUnit XAxisTimeUnit
		{
			get
			{
				return this.timeUnit_0;
			}
			set
			{
				this.timeUnit_0 = value;
			}
		}

		// Token: 0x17000245 RID: 581
		// (get) Token: 0x06000EBA RID: 3770 RVA: 0x00061174 File Offset: 0x0005F374
		// (set) Token: 0x06000EBB RID: 3771 RVA: 0x0006118C File Offset: 0x0005F38C
		public bool IfYAxisOnlyDispZeroLabel
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				if (this.bool_3 != value)
				{
					this.bool_3 = value;
					if (this.bool_3)
					{
						base.GraphPane.YAxis.ScaleFormatEvent += this.method_146;
					}
					else
					{
						base.GraphPane.YAxis.ScaleFormatEvent -= this.method_146;
					}
				}
			}
		}

		// Token: 0x17000246 RID: 582
		// (get) Token: 0x06000EBC RID: 3772 RVA: 0x000611F0 File Offset: 0x0005F3F0
		public bool IsInRetroMode
		{
			get
			{
				return this.ChtCtrl_KLine.IsInRetroMode;
			}
		}

		// Token: 0x17000247 RID: 583
		// (get) Token: 0x06000EBD RID: 3773 RVA: 0x0006120C File Offset: 0x0005F40C
		public int FirstItemIndex
		{
			get
			{
				int result;
				if (this.ChtCtrl_KLine.IsInRetroMode)
				{
					result = this.ChtCtrl_KLine.RetroModeFirstItemIdx;
				}
				else
				{
					result = this.ChtCtrl_KLine.IndexOfFirstItemShown;
				}
				return result;
			}
		}

		// Token: 0x17000248 RID: 584
		// (get) Token: 0x06000EBE RID: 3774 RVA: 0x00061244 File Offset: 0x0005F444
		public int LastItemIndex
		{
			get
			{
				int result = this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.ChtCtrl_KLine.IsInRetroMode)
				{
					result = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				return result;
			}
		}

		// Token: 0x17000249 RID: 585
		// (get) Token: 0x06000EBF RID: 3775 RVA: 0x0006127C File Offset: 0x0005F47C
		public PeriodType PeriodType
		{
			get
			{
				return base.ChtCtrl.PeriodType;
			}
		}

		// Token: 0x1700024A RID: 586
		// (get) Token: 0x06000EC0 RID: 3776 RVA: 0x00061298 File Offset: 0x0005F498
		public int? PeriodUnits
		{
			get
			{
				return base.ChtCtrl.PeriodUnits;
			}
		}

		// Token: 0x04000773 RID: 1907
		private List<Indicator> list_0;

		// Token: 0x04000774 RID: 1908
		private List<Indicator> list_1;

		// Token: 0x04000775 RID: 1909
		private TimeUnit timeUnit_0 = TimeUnit.ThirtyMins;

		// Token: 0x04000776 RID: 1910
		private bool bool_3;

		// Token: 0x04000777 RID: 1911
		protected static readonly string string_9 = Class521.smethod_0(48610);

		// Token: 0x04000778 RID: 1912
		private double? nullable_2;

		// Token: 0x0200017D RID: 381
		// (Invoke) Token: 0x06000EC3 RID: 3779
		public delegate void Delegate11(ZedGraphControl control, ContextMenuStrip menuStrip, Point mousePt, ZedGraphControl.ContextMenuObjectState objState);

		// Token: 0x0200017F RID: 383
		[CompilerGenerated]
		private sealed class Class214
		{
			// Token: 0x06000ED5 RID: 3797 RVA: 0x00061400 File Offset: 0x0005F600
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.UDInd.UDS.Name == this.string_0;
			}

			// Token: 0x06000ED6 RID: 3798 RVA: 0x0006142C File Offset: 0x0005F62C
			internal bool method_1(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x04000786 RID: 1926
			public string string_0;
		}

		// Token: 0x02000180 RID: 384
		[CompilerGenerated]
		private sealed class Class215
		{
			// Token: 0x06000ED8 RID: 3800 RVA: 0x00061450 File Offset: 0x0005F650
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.userDefineIndScript_0.Name;
			}

			// Token: 0x04000787 RID: 1927
			public UserDefineIndScript userDefineIndScript_0;
		}

		// Token: 0x02000181 RID: 385
		[CompilerGenerated]
		private sealed class Class216
		{
			// Token: 0x06000EDA RID: 3802 RVA: 0x00061478 File Offset: 0x0005F678
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.userDefineIndScript_0.Name;
			}

			// Token: 0x04000788 RID: 1928
			public UserDefineIndScript userDefineIndScript_0;
		}

		// Token: 0x02000182 RID: 386
		[CompilerGenerated]
		private sealed class Class217
		{
			// Token: 0x06000EDC RID: 3804 RVA: 0x000614A0 File Offset: 0x0005F6A0
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.UDInd.UDS.Name == this.eventArgs30_0.UDS.Name;
			}

			// Token: 0x04000789 RID: 1929
			public EventArgs30 eventArgs30_0;
		}

		// Token: 0x02000183 RID: 387
		[CompilerGenerated]
		private sealed class Class218
		{
			// Token: 0x06000EDE RID: 3806 RVA: 0x000614D8 File Offset: 0x0005F6D8
			internal bool method_0(ChtCtrl chtCtrl_0)
			{
				bool result;
				if (chtCtrl_0.Symbol == this.chartKLine_0.Symbol && chtCtrl_0.PeriodType == this.nullable_0.Value)
				{
					int? periodUnits = chtCtrl_0.PeriodUnits;
					int? num = this.nullable_1;
					result = (periodUnits.GetValueOrDefault() == num.GetValueOrDefault() & periodUnits != null == (num != null));
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400078A RID: 1930
			public ChartKLine chartKLine_0;

			// Token: 0x0400078B RID: 1931
			public PeriodType? nullable_0;

			// Token: 0x0400078C RID: 1932
			public int? nullable_1;
		}

		// Token: 0x02000184 RID: 388
		[CompilerGenerated]
		private sealed class Class219
		{
			// Token: 0x06000EE0 RID: 3808 RVA: 0x00061544 File Offset: 0x0005F744
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key > this.chartKLine_0.HisDataPeriodSet.PeriodHisDataList.Keys[this.int_0 - 1] && keyValuePair_0.Key <= this.dateTime_0)
				{
					result = (keyValuePair_0.Value.Volume != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06000EE1 RID: 3809 RVA: 0x000615B4 File Offset: 0x0005F7B4
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key <= this.dateTime_0)
				{
					result = (keyValuePair_0.Value.Volume != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400078D RID: 1933
			public ChartKLine chartKLine_0;

			// Token: 0x0400078E RID: 1934
			public int int_0;

			// Token: 0x0400078F RID: 1935
			public DateTime dateTime_0;
		}

		// Token: 0x02000185 RID: 389
		[CompilerGenerated]
		private sealed class Class220
		{
			// Token: 0x06000EE3 RID: 3811 RVA: 0x000615F4 File Offset: 0x0005F7F4
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.EnName == this.toolStripMenuItem_0.Name;
			}

			// Token: 0x04000790 RID: 1936
			public ToolStripMenuItem toolStripMenuItem_0;
		}

		// Token: 0x02000186 RID: 390
		[CompilerGenerated]
		private sealed class Class221
		{
			// Token: 0x06000EE5 RID: 3813 RVA: 0x0006161C File Offset: 0x0005F81C
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.toolStripMenuItem_0.Name;
			}

			// Token: 0x04000791 RID: 1937
			public ToolStripMenuItem toolStripMenuItem_0;
		}

		// Token: 0x02000187 RID: 391
		[CompilerGenerated]
		private sealed class Class222
		{
			// Token: 0x06000EE7 RID: 3815 RVA: 0x00061644 File Offset: 0x0005F844
			internal bool method_0(Indicator indicator_1)
			{
				bool result;
				if (indicator_1.EnName == this.indicator_0.EnName)
				{
					result = (indicator_1.Chart == this.chartKLine_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000792 RID: 1938
			public Indicator indicator_0;

			// Token: 0x04000793 RID: 1939
			public ChartKLine chartKLine_0;
		}

		// Token: 0x02000188 RID: 392
		[CompilerGenerated]
		private sealed class Class223
		{
			// Token: 0x04000794 RID: 1940
			public ChartKLine chartKLine_0;
		}

		// Token: 0x02000189 RID: 393
		[CompilerGenerated]
		private sealed class Class224
		{
			// Token: 0x06000EEA RID: 3818 RVA: 0x00061680 File Offset: 0x0005F880
			internal bool method_0(Indicator indicator_0)
			{
				bool result;
				if (indicator_0.EnName == this.toolStripMenuItem_0.Text)
				{
					result = (indicator_0.Chart == this.class223_0.chartKLine_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000795 RID: 1941
			public ToolStripMenuItem toolStripMenuItem_0;

			// Token: 0x04000796 RID: 1942
			public ChartKLine.Class223 class223_0;
		}
	}
}
