﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using ns26;
using ns4;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200008D RID: 141
	public sealed partial class PageSelWnd : Form1
	{
		// Token: 0x1400001C RID: 28
		// (add) Token: 0x060004A1 RID: 1185 RVA: 0x0002568C File Offset: 0x0002388C
		// (remove) Token: 0x060004A2 RID: 1186 RVA: 0x000256C4 File Offset: 0x000238C4
		public event MsgEventHandler PageSelected
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004A3 RID: 1187 RVA: 0x00004004 File Offset: 0x00002204
		protected void method_1(string string_0)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_0, null));
			}
		}

		// Token: 0x060004A4 RID: 1188 RVA: 0x000256FC File Offset: 0x000238FC
		public PageSelWnd()
		{
			this.method_5();
			base.Icon = Class375.TExIcoBlue;
			this.BackColor = Color.White;
			base.ShowIcon = false;
			this.dictionary_0 = new Dictionary<string, string>();
			this.dictionary_0.Add(Class521.smethod_0(1984), Class521.smethod_0(6076));
			this.dictionary_0.Add(Class521.smethod_0(1993), Class521.smethod_0(6230));
			this.dictionary_0.Add(Class521.smethod_0(2002), Class521.smethod_0(6339));
			this.dictionary_0.Add(Class521.smethod_0(6452), Class521.smethod_0(6461));
			this.dictionary_0.Add(Class521.smethod_0(6599), Class521.smethod_0(6608));
			this.dictionary_0.Add(Class521.smethod_0(6746), Class521.smethod_0(6755));
			this.scrShotPictureBox_0.IsSelected = true;
			this.labelX_0.Text = this.dictionary_0[this.scrShotPictureBox_0.Tag as string];
			this.linkLabel_0.LinkColor = Class181.color_22;
			foreach (ScrShotPictureBox scrShotPictureBox in this.PictureBoxList)
			{
				scrShotPictureBox.Selected += this.method_4;
				scrShotPictureBox.MouseEnter += this.method_2;
				scrShotPictureBox.MouseLeave += this.method_3;
			}
			this.linkLabel_0.LinkClicked += this.linkLabel_0_LinkClicked;
		}

		// Token: 0x060004A5 RID: 1189 RVA: 0x000258C8 File Offset: 0x00023AC8
		private void method_2(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = sender as ScrShotPictureBox;
			this.labelX_0.Text = this.dictionary_0[scrShotPictureBox.Tag as string];
		}

		// Token: 0x060004A6 RID: 1190 RVA: 0x00025900 File Offset: 0x00023B00
		private void method_3(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = this.PictureBoxList.SingleOrDefault(new Func<ScrShotPictureBox, bool>(PageSelWnd.<>c.<>9.method_0));
			this.labelX_0.Text = this.dictionary_0[scrShotPictureBox.Tag as string];
		}

		// Token: 0x060004A7 RID: 1191 RVA: 0x0002595C File Offset: 0x00023B5C
		private void linkLabel_0_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = this.PictureBoxList.SingleOrDefault(new Func<ScrShotPictureBox, bool>(PageSelWnd.<>c.<>9.method_1));
			this.method_1(scrShotPictureBox.Tag as string);
			base.Close();
		}

		// Token: 0x060004A8 RID: 1192 RVA: 0x000259B0 File Offset: 0x00023BB0
		private void method_4(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = sender as ScrShotPictureBox;
			foreach (ScrShotPictureBox scrShotPictureBox2 in this.PictureBoxList)
			{
				if (scrShotPictureBox2 != scrShotPictureBox)
				{
					scrShotPictureBox2.IsSelected = false;
				}
			}
		}

		// Token: 0x170000F6 RID: 246
		// (get) Token: 0x060004A9 RID: 1193 RVA: 0x00025A14 File Offset: 0x00023C14
		public List<ScrShotPictureBox> PictureBoxList
		{
			get
			{
				if (this.list_0 == null)
				{
					List<ScrShotPictureBox> list = new List<ScrShotPictureBox>();
					foreach (object obj in base.Controls)
					{
						if (obj is ScrShotPictureBox)
						{
							list.Add(obj as ScrShotPictureBox);
						}
					}
					this.list_0 = list;
				}
				return this.list_0;
			}
		}

		// Token: 0x060004AA RID: 1194 RVA: 0x00004021 File Offset: 0x00002221
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060004AB RID: 1195 RVA: 0x00025A98 File Offset: 0x00023C98
		private void method_5()
		{
			this.scrShotPictureBox_0 = new ScrShotPictureBox();
			this.scrShotPictureBox_1 = new ScrShotPictureBox();
			this.scrShotPictureBox_2 = new ScrShotPictureBox();
			this.scrShotPictureBox_3 = new ScrShotPictureBox();
			this.scrShotPictureBox_4 = new ScrShotPictureBox();
			this.scrShotPictureBox_5 = new ScrShotPictureBox();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.label_5 = new Label();
			this.labelX_0 = new LabelX();
			this.linkLabel_0 = new LinkLabel();
			this.label_6 = new Label();
			this.label_7 = new Label();
			((ISupportInitialize)this.scrShotPictureBox_0).BeginInit();
			((ISupportInitialize)this.scrShotPictureBox_1).BeginInit();
			((ISupportInitialize)this.scrShotPictureBox_2).BeginInit();
			((ISupportInitialize)this.scrShotPictureBox_3).BeginInit();
			((ISupportInitialize)this.scrShotPictureBox_4).BeginInit();
			((ISupportInitialize)this.scrShotPictureBox_5).BeginInit();
			base.SuspendLayout();
			this.scrShotPictureBox_0.Image = Class375.page_symbs;
			this.scrShotPictureBox_0.IsSelected = false;
			this.scrShotPictureBox_0.Location = new Point(82, 116);
			this.scrShotPictureBox_0.Name = Class521.smethod_0(5732);
			this.scrShotPictureBox_0.Size = new Size(315, 200);
			this.scrShotPictureBox_0.TabIndex = 0;
			this.scrShotPictureBox_0.TabStop = false;
			this.scrShotPictureBox_0.Tag = Class521.smethod_0(1984);
			this.scrShotPictureBox_1.Image = Class375.page_tick_cht;
			this.scrShotPictureBox_1.IsSelected = false;
			this.scrShotPictureBox_1.Location = new Point(424, 116);
			this.scrShotPictureBox_1.Name = Class521.smethod_0(6913);
			this.scrShotPictureBox_1.Size = new Size(315, 200);
			this.scrShotPictureBox_1.TabIndex = 1;
			this.scrShotPictureBox_1.TabStop = false;
			this.scrShotPictureBox_1.Tag = Class521.smethod_0(1993);
			this.scrShotPictureBox_2.Image = Class375.page_simple_cht;
			this.scrShotPictureBox_2.IsSelected = false;
			this.scrShotPictureBox_2.Location = new Point(766, 116);
			this.scrShotPictureBox_2.Name = Class521.smethod_0(6930);
			this.scrShotPictureBox_2.Size = new Size(315, 200);
			this.scrShotPictureBox_2.TabIndex = 2;
			this.scrShotPictureBox_2.TabStop = false;
			this.scrShotPictureBox_2.Tag = Class521.smethod_0(2002);
			this.scrShotPictureBox_3.Image = Class375.page_two_cht;
			this.scrShotPictureBox_3.IsSelected = false;
			this.scrShotPictureBox_3.Location = new Point(82, 373);
			this.scrShotPictureBox_3.Name = Class521.smethod_0(6947);
			this.scrShotPictureBox_3.Size = new Size(315, 200);
			this.scrShotPictureBox_3.TabIndex = 3;
			this.scrShotPictureBox_3.TabStop = false;
			this.scrShotPictureBox_3.Tag = Class521.smethod_0(6452);
			this.scrShotPictureBox_4.Image = Class375.page_three_cht;
			this.scrShotPictureBox_4.IsSelected = false;
			this.scrShotPictureBox_4.Location = new Point(424, 373);
			this.scrShotPictureBox_4.Name = Class521.smethod_0(6964);
			this.scrShotPictureBox_4.Size = new Size(315, 200);
			this.scrShotPictureBox_4.TabIndex = 4;
			this.scrShotPictureBox_4.TabStop = false;
			this.scrShotPictureBox_4.Tag = Class521.smethod_0(6599);
			this.scrShotPictureBox_5.Image = Class375.page_four_cht;
			this.scrShotPictureBox_5.IsSelected = false;
			this.scrShotPictureBox_5.Location = new Point(766, 373);
			this.scrShotPictureBox_5.Name = Class521.smethod_0(6981);
			this.scrShotPictureBox_5.Size = new Size(315, 200);
			this.scrShotPictureBox_5.TabIndex = 5;
			this.scrShotPictureBox_5.TabStop = false;
			this.scrShotPictureBox_5.Tag = Class521.smethod_0(6746);
			this.label_0.BackColor = Color.Transparent;
			this.label_0.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_0.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_0.Location = new Point(189, 322);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(90, 30);
			this.label_0.TabIndex = 6;
			this.label_0.Text = Class521.smethod_0(1984);
			this.label_0.TextAlign = ContentAlignment.MiddleCenter;
			this.label_1.BackColor = Color.Transparent;
			this.label_1.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_1.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_1.Location = new Point(536, 322);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(90, 30);
			this.label_1.TabIndex = 7;
			this.label_1.Text = Class521.smethod_0(1993);
			this.label_1.TextAlign = ContentAlignment.MiddleCenter;
			this.label_2.BackColor = Color.Transparent;
			this.label_2.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_2.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_2.Location = new Point(879, 322);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(90, 30);
			this.label_2.TabIndex = 8;
			this.label_2.Text = Class521.smethod_0(2002);
			this.label_2.TextAlign = ContentAlignment.MiddleCenter;
			this.label_3.BackColor = Color.Transparent;
			this.label_3.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_3.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_3.Location = new Point(189, 578);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(90, 30);
			this.label_3.TabIndex = 9;
			this.label_3.Text = Class521.smethod_0(6452);
			this.label_3.TextAlign = ContentAlignment.MiddleCenter;
			this.label_4.BackColor = Color.Transparent;
			this.label_4.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_4.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_4.Location = new Point(536, 578);
			this.label_4.Name = Class521.smethod_0(5893);
			this.label_4.Size = new Size(90, 30);
			this.label_4.TabIndex = 10;
			this.label_4.Text = Class521.smethod_0(6599);
			this.label_4.TextAlign = ContentAlignment.MiddleCenter;
			this.label_5.BackColor = Color.Transparent;
			this.label_5.Font = new Font(Class521.smethod_0(6998), 10f);
			this.label_5.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_5.Location = new Point(879, 578);
			this.label_5.Name = Class521.smethod_0(5915);
			this.label_5.Size = new Size(90, 30);
			this.label_5.TabIndex = 11;
			this.label_5.Text = Class521.smethod_0(6746);
			this.label_5.TextAlign = ContentAlignment.MiddleCenter;
			this.labelX_0.BackColor = Color.Transparent;
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Font = new Font(Class521.smethod_0(6998), 10f);
			this.labelX_0.ForeColor = Color.FromArgb(64, 64, 64);
			this.labelX_0.Location = new Point(82, 623);
			this.labelX_0.Name = Class521.smethod_0(7028);
			this.labelX_0.Size = new Size(999, 48);
			this.labelX_0.TabIndex = 12;
			this.labelX_0.Text = Class521.smethod_0(7045);
			this.labelX_0.TextAlignment = StringAlignment.Center;
			this.linkLabel_0.Font = new Font(Class521.smethod_0(7183), 10f, FontStyle.Bold);
			this.linkLabel_0.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_0.LinkColor = Color.FromArgb(255, 128, 0);
			this.linkLabel_0.Location = new Point(547, 687);
			this.linkLabel_0.Name = Class521.smethod_0(7192);
			this.linkLabel_0.Size = new Size(71, 27);
			this.linkLabel_0.TabIndex = 13;
			this.linkLabel_0.TabStop = true;
			this.linkLabel_0.Text = Class521.smethod_0(7213);
			this.linkLabel_0.TextAlign = ContentAlignment.MiddleCenter;
			this.label_6.BackColor = Color.Transparent;
			this.label_6.Font = new Font(Class521.smethod_0(6998), 12f, FontStyle.Bold);
			this.label_6.ForeColor = Color.FromArgb(64, 64, 64);
			this.label_6.Location = new Point(423, 45);
			this.label_6.Name = Class521.smethod_0(7222);
			this.label_6.Size = new Size(315, 39);
			this.label_6.TabIndex = 14;
			this.label_6.Text = Class521.smethod_0(7239);
			this.label_6.TextAlign = ContentAlignment.MiddleCenter;
			this.label_7.BackColor = Color.Transparent;
			this.label_7.Font = new Font(Class521.smethod_0(6998), 8.5f);
			this.label_7.ForeColor = Color.Gray;
			this.label_7.ImageAlign = ContentAlignment.BottomRight;
			this.label_7.Location = new Point(667, 708);
			this.label_7.Name = Class521.smethod_0(7268);
			this.label_7.Size = new Size(492, 26);
			this.label_7.TabIndex = 15;
			this.label_7.Text = Class521.smethod_0(7277);
			this.label_7.TextAlign = ContentAlignment.MiddleRight;
			base.AcceptButton = this.linkLabel_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(1162, 741);
			base.Controls.Add(this.label_7);
			base.Controls.Add(this.label_6);
			base.Controls.Add(this.linkLabel_0);
			base.Controls.Add(this.labelX_0);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.scrShotPictureBox_5);
			base.Controls.Add(this.scrShotPictureBox_4);
			base.Controls.Add(this.scrShotPictureBox_3);
			base.Controls.Add(this.scrShotPictureBox_2);
			base.Controls.Add(this.scrShotPictureBox_1);
			base.Controls.Add(this.scrShotPictureBox_0);
			base.Name = Class521.smethod_0(7370);
			this.Text = Class521.smethod_0(7387);
			((ISupportInitialize)this.scrShotPictureBox_0).EndInit();
			((ISupportInitialize)this.scrShotPictureBox_1).EndInit();
			((ISupportInitialize)this.scrShotPictureBox_2).EndInit();
			((ISupportInitialize)this.scrShotPictureBox_3).EndInit();
			((ISupportInitialize)this.scrShotPictureBox_4).EndInit();
			((ISupportInitialize)this.scrShotPictureBox_5).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x040001D3 RID: 467
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001D4 RID: 468
		private Dictionary<string, string> dictionary_0;

		// Token: 0x040001D5 RID: 469
		private List<ScrShotPictureBox> list_0;

		// Token: 0x040001D6 RID: 470
		private IContainer icontainer_0;

		// Token: 0x040001D7 RID: 471
		private ScrShotPictureBox scrShotPictureBox_0;

		// Token: 0x040001D8 RID: 472
		private ScrShotPictureBox scrShotPictureBox_1;

		// Token: 0x040001D9 RID: 473
		private ScrShotPictureBox scrShotPictureBox_2;

		// Token: 0x040001DA RID: 474
		private ScrShotPictureBox scrShotPictureBox_3;

		// Token: 0x040001DB RID: 475
		private ScrShotPictureBox scrShotPictureBox_4;

		// Token: 0x040001DC RID: 476
		private ScrShotPictureBox scrShotPictureBox_5;

		// Token: 0x040001DD RID: 477
		private Label label_0;

		// Token: 0x040001DE RID: 478
		private Label label_1;

		// Token: 0x040001DF RID: 479
		private Label label_2;

		// Token: 0x040001E0 RID: 480
		private Label label_3;

		// Token: 0x040001E1 RID: 481
		private Label label_4;

		// Token: 0x040001E2 RID: 482
		private Label label_5;

		// Token: 0x040001E3 RID: 483
		private LabelX labelX_0;

		// Token: 0x040001E4 RID: 484
		private LinkLabel linkLabel_0;

		// Token: 0x040001E5 RID: 485
		private Label label_6;

		// Token: 0x040001E6 RID: 486
		private Label label_7;
	}
}
