﻿using System;
using System.ComponentModel;
using ns9;
using TEx;

namespace ns20
{
	// Token: 0x020000AA RID: 170
	[DesignerCategory("Code")]
	internal sealed class Class66 : Class65
	{
		// Token: 0x060005D4 RID: 1492 RVA: 0x00004742 File Offset: 0x00002942
		public Class66(DrawLineStyle drawLineStyle_1 = null, IContainer icontainer_1 = null) : base(typeof(DrawLineType), drawLineStyle_1, icontainer_1)
		{
		}

		// Token: 0x060005D5 RID: 1493 RVA: 0x0002CE74 File Offset: 0x0002B074
		protected override float[] vmethod_2(int int_0)
		{
			return DrawLineStyle.smethod_1((DrawLineType)int_0);
		}

		// Token: 0x060005D6 RID: 1494 RVA: 0x0002CE8C File Offset: 0x0002B08C
		protected override float vmethod_1(int int_0)
		{
			return 2f;
		}
	}
}
