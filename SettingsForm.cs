﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns26;
using ns3;
using ns9;
using TEx.Trading;

namespace TEx
{
	// Token: 0x020001A9 RID: 425
	public sealed partial class SettingsForm : Form
	{
		// Token: 0x1400007B RID: 123
		// (add) Token: 0x06001060 RID: 4192 RVA: 0x0006E588 File Offset: 0x0006C788
		// (remove) Token: 0x06001061 RID: 4193 RVA: 0x0006E5C0 File Offset: 0x0006C7C0
		public event EventHandler UISettingsConfirmed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001062 RID: 4194 RVA: 0x00006F9D File Offset: 0x0000519D
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06001063 RID: 4195 RVA: 0x00006FB8 File Offset: 0x000051B8
		public SettingsForm()
		{
			this.method_17();
		}

		// Token: 0x06001064 RID: 4196 RVA: 0x0006E5F8 File Offset: 0x0006C7F8
		private void SettingsForm_Load(object sender, EventArgs e)
		{
			this.checkBox_27.Checked = Base.UI.Form.IfAlwaysShowTransNoteBox;
			this.checkBox_27.CheckedChanged += this.checkBox_27_CheckedChanged;
			this.method_14();
			this.checkBox_24.Checked = !Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox;
			this.checkBox_26.Checked = !Base.UI.Form.IfHideTransNoteBorder;
			this.checkBox_23.Checked = Base.UI.Form.IfTransNoteFillTransparent;
			this.checkBox_31.Checked = !Base.UI.Form.IfDisableOpenCloseSound;
			this.checkBox_25.Checked = !Base.UI.Form.IfDisableTsOdrLine;
			this.checkBox_5.Checked = !Base.UI.Form.IfDisableAutoShowCurrTransTab;
			this.checkBox_19.Checked = Base.UI.Form.IfFollowPrcInTradingTab;
			this.checkBox_36.Checked = !Base.UI.Form.NoShowHighLowMark;
			this.comboBox_7.SelectedIndex = 0;
			if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
			{
				this.comboBox_7.SelectedIndex = 0;
				this.pictureBox_1.Image = Class375.BigArrwSamples;
			}
			else if (Base.UI.Form.TransArrowType == TransArrowType.SmallUpDn)
			{
				this.comboBox_7.SelectedIndex = 1;
				this.pictureBox_1.Image = Class375.SmallArrwSample;
			}
			this.comboBox_7.SelectedIndexChanged += this.comboBox_7_SelectedIndexChanged;
			if (Base.UI.Form.IfShowDayOfWeek)
			{
				this.checkBox_34.Checked = true;
			}
			this.checkBox_32.Checked = Base.UI.Form.IfDispDayDivLine;
			this.comboBox_9.Items.Add(Class521.smethod_0(16325));
			this.comboBox_9.Items.Add(Class521.smethod_0(16338));
			this.comboBox_9.Items.Add(Class521.smethod_0(16351));
			this.comboBox_9.Items.Add(Class521.smethod_0(16364));
			this.comboBox_9.Items.Add(Class521.smethod_0(16377));
			this.comboBox_9.Items.Add(Class521.smethod_0(37487));
			this.comboBox_9.SelectedItem = this.method_16(Base.UI.Form.PeriodOfChartDispDayDivLine);
			this.method_12();
			this.checkBox_3.Checked = Base.UI.Form.IfPauseAtDayEnd;
			this.checkBox_2.Checked = Base.UI.Form.IfAutoSavePageOnExit;
			this.checkBox_0.Checked = Base.UI.Form.IfSaveSpeedOnQuit;
			this.checkBox_1.Checked = Base.UI.Form.IfSaveWindowOnQuit;
			this.checkBox_4.Checked = Base.UI.Form.IfConfirmQuit;
			this.checkBox_28.Checked = !Base.UI.Form.IfShowNoTransArrow;
			this.checkBox_28.CheckedChanged += this.checkBox_28_CheckedChanged;
			this.method_13();
			if (!Base.UI.Form.IfShowAllTransArrow)
			{
				this.radioButton_7.Checked = true;
			}
			else
			{
				this.radioButton_6.Checked = true;
			}
			this.comboBox_8.Items.Add(Class521.smethod_0(16325));
			this.comboBox_8.Items.Add(Class521.smethod_0(16338));
			this.comboBox_8.Items.Add(Class521.smethod_0(16351));
			this.comboBox_8.Items.Add(Class521.smethod_0(16364));
			this.comboBox_8.Items.Add(Class521.smethod_0(16377));
			this.comboBox_8.Items.Add(Class521.smethod_0(37487));
			this.comboBox_8.Items.Add(Class521.smethod_0(37500));
			this.comboBox_8.Items.Add(Class521.smethod_0(12209));
			this.comboBox_8.Items.Add(Class521.smethod_0(12228));
			this.comboBox_8.Items.Add(Class521.smethod_0(12247));
			TimeUnit timeUnit = Base.UI.Form.PeriodOfChartDispTransArrow;
			if (timeUnit == (TimeUnit)0)
			{
				timeUnit = TimeUnit.Day;
			}
			this.comboBox_8.SelectedItem = this.method_16(timeUnit);
			if (Base.UI.Form.IfSymbSwitchShowCurrDT)
			{
				this.radioButton_10.Checked = true;
			}
			else
			{
				this.radioButton_11.Checked = true;
			}
			if (!Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader)
			{
				this.checkBox_33.Checked = true;
			}
			if (Base.UI.Form.IfShowSymbCNNameInOpenTransGridView)
			{
				this.radioButton_9.Checked = true;
			}
			else
			{
				this.radioButton_8.Checked = true;
			}
			if (Base.UI.CurrTradingSymbol != null)
			{
				this.stkSymbol_0 = Base.UI.CurrTradingSymbol;
			}
			else if (this.stkSymbol_0 == null)
			{
				this.stkSymbol_0 = Base.UI.CurrSymbol;
			}
			if (this.stkSymbol_0 != null)
			{
				this.groupBox_2.Text = Class521.smethod_0(37513);
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				GroupBox groupBox = this.groupBox_2;
				groupBox.Text += Class521.smethod_0(37530);
			}
			else
			{
				GroupBox groupBox2 = this.groupBox_2;
				groupBox2.Text = groupBox2.Text + Class521.smethod_0(37555) + this.stkSymbol_0.MstSymbol.AbbrCNName + Class521.smethod_0(37572);
			}
			this.method_8();
			ToolTip toolTip = new ToolTip();
			string caption = Class521.smethod_0(37577);
			toolTip.SetToolTip(this.linkLabel_0, caption);
			toolTip.SetToolTip(this.linkLabel_1, caption);
			this.linkLabel_0.LinkClicked += this.linkLabel_0_LinkClicked;
			this.linkLabel_1.LinkClicked += this.linkLabel_1_LinkClicked;
			if (this.stkSymbol_0 != null)
			{
				AcctSymbol acctSymbol = Base.Acct.smethod_30(this.stkSymbol_0.ID);
				if (this.stkSymbol_0.AutoStopLossPoints != null)
				{
					this.checkBox_7.Checked = acctSymbol.IfAutoStopLoss;
				}
				else
				{
					this.checkBox_7.Checked = false;
				}
				if (this.stkSymbol_0.AutoLimitTakePoints != null)
				{
					this.checkBox_6.Checked = acctSymbol.IfAutoLimitTake;
				}
				else
				{
					this.checkBox_6.Checked = false;
				}
			}
			this.checkBox_7.CheckedChanged += this.checkBox_7_CheckedChanged;
			this.checkBox_6.CheckedChanged += this.checkBox_6_CheckedChanged;
			this.checkBox_30.Checked = Base.UI.Form.IsOrderConfmNeeded;
			this.checkBox_8.Checked = Base.UI.Form.EnableShortForStock;
			this.checkBox_9.Checked = Base.UI.Form.EnableT0ForStock;
			this.checkBox_29.Checked = Base.UI.Form.IfNoConfClsTransWhenDblClick;
			this.checkBox_20.Checked = !Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits;
			this.checkBox_22.Checked = !Base.UI.Form.IfNotAutoCancelExistingOpenTransOrder;
			this.checkBox_35.Checked = !Base.UI.Form.IfNotAutoCancelExistingCloseTransOrder;
			if (Base.UI.Form.IfCloseNewTransFirst)
			{
				this.radioButton_1.Checked = true;
			}
			else
			{
				this.radioButton_0.Checked = true;
			}
			string caption2 = Class521.smethod_0(37610) + Environment.NewLine + Class521.smethod_0(37691);
			ToolTip toolTip2 = new ToolTip();
			toolTip2.ToolTipIcon = ToolTipIcon.Info;
			toolTip2.ToolTipTitle = Class521.smethod_0(37744);
			toolTip2.SetToolTip(this.groupBox_4, caption2);
			toolTip2.SetToolTip(this.radioButton_1, caption2);
			toolTip2.SetToolTip(this.radioButton_0, caption2);
			if (Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.Prior)
			{
				if (Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value == StockRestorationMethod.Later)
				{
					this.comboBox_0.SelectedIndex = 1;
				}
				else
				{
					this.comboBox_0.SelectedIndex = 2;
				}
			}
			else
			{
				this.comboBox_0.SelectedIndex = 0;
			}
			if (Base.UI.Form.IfNoBonusShare)
			{
				this.comboBox_1.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_1.SelectedIndex = 0;
			}
			if (Base.UI.Form.IfNoDivident)
			{
				this.comboBox_2.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_2.SelectedIndex = 0;
			}
			if (Base.UI.Form.RationedShareTreatmt != null)
			{
				if (Base.UI.Form.RationedShareTreatmt.Value != RationedShareTreatmt.Prompt)
				{
					if (Base.UI.Form.RationedShareTreatmt != null && Base.UI.Form.RationedShareTreatmt.Value == RationedShareTreatmt.Auto)
					{
						this.comboBox_3.SelectedIndex = 0;
						goto IL_8D3;
					}
					this.comboBox_3.SelectedIndex = 2;
					goto IL_8D3;
				}
			}
			this.comboBox_3.SelectedIndex = 1;
			IL_8D3:
			if (Base.UI.Form.IfROpenFixedAmt)
			{
				this.radioButton_4.Checked = true;
				this.numericUpDown_0.Enabled = false;
			}
			else
			{
				this.radioButton_5.Checked = true;
				this.textBox_0.Enabled = false;
			}
			if (Base.UI.Form.ROpenRatio != null)
			{
				this.numericUpDown_0.Value = Base.UI.Form.ROpenRatio.Value;
			}
			else
			{
				this.numericUpDown_0.Value = 3m;
			}
			if (Base.UI.Form.ROpenFixedAmt != null)
			{
				this.textBox_0.Text = Base.UI.Form.ROpenFixedAmt.Value.ToString();
			}
			else
			{
				this.textBox_0.Text = Base.Trading.smethod_213().ToString();
			}
			if (!Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.checkBox_21.Checked = true;
			}
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				if (Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
				{
					this.comboBox_10.SelectedIndex = 1;
				}
				else if (Base.UI.Form.KLineType.Value == KLineType.OHLCBar)
				{
					this.comboBox_10.SelectedIndex = 2;
				}
				else
				{
					this.comboBox_10.SelectedIndex = 3;
				}
			}
			else
			{
				this.comboBox_10.SelectedIndex = 0;
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.comboBox_11.SelectedIndex = 0;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				this.comboBox_11.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_11.SelectedIndex = 2;
			}
			this.method_1();
			this.comboBox_10.SelectedIndexChanged += this.comboBox_10_SelectedIndexChanged;
			this.comboBox_11.SelectedIndexChanged += this.comboBox_11_SelectedIndexChanged;
			this.textBox_0.KeyPress += this.textBox_0_KeyPress;
			this.radioButton_5.CheckedChanged += this.radioButton_5_CheckedChanged;
			this.tabControl_0.Controls.Remove(this.tabPage_2);
		}

		// Token: 0x06001065 RID: 4197 RVA: 0x00006FC8 File Offset: 0x000051C8
		private void comboBox_11_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001066 RID: 4198 RVA: 0x00006FC8 File Offset: 0x000051C8
		private void comboBox_10_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001067 RID: 4199 RVA: 0x0006F114 File Offset: 0x0006D314
		private void method_1()
		{
			if (this.comboBox_10.SelectedIndex == 0)
			{
				if (this.comboBox_11.SelectedIndex == 0)
				{
					this.pictureBox_2.Image = Class375.theme_1_1;
				}
				else if (this.comboBox_11.SelectedIndex == 1)
				{
					this.pictureBox_2.Image = Class375.theme_1_2;
				}
				else
				{
					this.pictureBox_2.Image = Class375.theme_1_3;
				}
			}
			else if (this.comboBox_10.SelectedIndex == 1)
			{
				if (this.comboBox_11.SelectedIndex == 0)
				{
					this.pictureBox_2.Image = Class375.theme_2_1;
				}
				else if (this.comboBox_11.SelectedIndex == 1)
				{
					this.pictureBox_2.Image = Class375.theme_2_2;
				}
				else
				{
					this.pictureBox_2.Image = Class375.theme_2_3;
				}
			}
			else if (this.comboBox_10.SelectedIndex == 2)
			{
				if (this.comboBox_11.SelectedIndex == 0)
				{
					this.pictureBox_2.Image = Class375.theme_3_1;
				}
				else if (this.comboBox_11.SelectedIndex == 1)
				{
					this.pictureBox_2.Image = Class375.theme_3_2;
				}
				else
				{
					this.pictureBox_2.Image = Class375.theme_3_3;
				}
			}
			else if (this.comboBox_11.SelectedIndex == 0)
			{
				this.pictureBox_2.Image = Class375.theme_4_1;
			}
			else if (this.comboBox_11.SelectedIndex == 1)
			{
				this.pictureBox_2.Image = Class375.theme_4_2;
			}
			else
			{
				this.pictureBox_2.Image = Class375.theme_4_3;
			}
		}

		// Token: 0x06001068 RID: 4200 RVA: 0x0006F2A4 File Offset: 0x0006D4A4
		private void comboBox_7_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_7.SelectedIndex == 0)
			{
				this.pictureBox_1.Image = Class375.BigArrwSamples;
			}
			else if (this.comboBox_7.SelectedIndex == 1)
			{
				this.pictureBox_1.Image = Class375.SmallArrwSample;
			}
		}

		// Token: 0x06001069 RID: 4201 RVA: 0x0006F2F0 File Offset: 0x0006D4F0
		private void radioButton_5_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_5.Checked)
			{
				this.numericUpDown_0.Enabled = true;
				this.textBox_0.Enabled = false;
			}
			else
			{
				this.numericUpDown_0.Enabled = false;
				this.textBox_0.Enabled = true;
			}
		}

		// Token: 0x0600106A RID: 4202 RVA: 0x00006FD2 File Offset: 0x000051D2
		private void method_2(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				this.method_4();
			}
			else if (!BkupSyncMgr.smethod_5(null, new bool?(false), false))
			{
				this.method_5();
			}
		}

		// Token: 0x0600106B RID: 4203 RVA: 0x00006FFA File Offset: 0x000051FA
		private void method_3(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				this.method_4();
			}
			else if (!BkupSyncMgr.smethod_5(null, new bool?(true), false))
			{
				this.method_5();
			}
		}

		// Token: 0x0600106C RID: 4204 RVA: 0x00007022 File Offset: 0x00005222
		private void method_4()
		{
			MessageBox.Show(Class521.smethod_0(37769), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x0600106D RID: 4205 RVA: 0x00007043 File Offset: 0x00005243
		private void method_5()
		{
			MessageBox.Show(Class521.smethod_0(37818), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x0600106E RID: 4206 RVA: 0x0006F340 File Offset: 0x0006D540
		private void method_6(object sender, EventArgs e)
		{
			CheckBox checkBox = sender as CheckBox;
			if (TApp.IsTrialUser)
			{
				if (checkBox.Checked)
				{
					checkBox.Checked = false;
					MessageBox.Show(Class521.smethod_0(37769), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
			else if (this.groupBox_7.Controls.OfType<CheckBox>().Count(new Func<CheckBox, bool>(SettingsForm.<>c.<>9.method_0)) == 0)
			{
				this.groupBox_8.Enabled = false;
			}
			else
			{
				this.groupBox_8.Enabled = true;
			}
		}

		// Token: 0x0600106F RID: 4207 RVA: 0x00007064 File Offset: 0x00005264
		private void method_7(object sender, EventArgs e)
		{
			if (this.checkBox_15.Checked)
			{
				this.comboBox_4.Enabled = true;
			}
			else
			{
				this.comboBox_4.Enabled = false;
			}
		}

		// Token: 0x06001070 RID: 4208 RVA: 0x0006F3E0 File Offset: 0x0006D5E0
		private void method_8()
		{
			this.linkLabel_0.Text = Class521.smethod_0(37875);
			string text = Class521.smethod_0(37892);
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints != null)
			{
				text = this.stkSymbol_0.AutoStopLossPoints.Value.ToString();
			}
			LinkLabel linkLabel = this.linkLabel_0;
			linkLabel.Text = linkLabel.Text + text + Class521.smethod_0(37572);
			this.linkLabel_0.LinkArea = new LinkArea(4, text.Length);
			this.linkLabel_1.Text = Class521.smethod_0(37875);
			string text2 = Class521.smethod_0(37892);
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoLimitTakePoints != null)
			{
				text2 = this.stkSymbol_0.AutoLimitTakePoints.Value.ToString();
			}
			LinkLabel linkLabel2 = this.linkLabel_1;
			linkLabel2.Text = linkLabel2.Text + text2 + Class521.smethod_0(37572);
			this.linkLabel_1.LinkArea = new LinkArea(4, text2.Length);
		}

		// Token: 0x06001071 RID: 4209 RVA: 0x0000708F File Offset: 0x0000528F
		private void linkLabel_0_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (this.stkSymbol_0 != null)
			{
				this.method_9(false, false, true, false);
			}
		}

		// Token: 0x06001072 RID: 4210 RVA: 0x000070A5 File Offset: 0x000052A5
		private void linkLabel_1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (this.stkSymbol_0 != null)
			{
				this.method_9(false, false, false, true);
			}
		}

		// Token: 0x06001073 RID: 4211 RVA: 0x0006F514 File Offset: 0x0006D714
		private void checkBox_7_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_7.Checked && this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints == null)
			{
				this.checkBox_7.Checked = false;
				if (MessageBox.Show(Class521.smethod_0(37905), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_9(true, false, true, false);
				}
			}
		}

		// Token: 0x06001074 RID: 4212 RVA: 0x0006F584 File Offset: 0x0006D784
		private void checkBox_6_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_6.Checked)
			{
				StkSymbol currSymbol = Base.UI.CurrSymbol;
				if (currSymbol != null && currSymbol.AutoLimitTakePoints == null)
				{
					this.checkBox_6.Checked = false;
					if (MessageBox.Show(Class521.smethod_0(38010), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_9(false, true, false, true);
					}
				}
			}
		}

		// Token: 0x06001075 RID: 4213 RVA: 0x0006F5F0 File Offset: 0x0006D7F0
		private void method_9(bool bool_2, bool bool_3, bool bool_4, bool bool_5)
		{
			Form13 form = new Form13(new List<StkSymbol>
			{
				this.stkSymbol_0
			}, bool_2, bool_3, true);
			form.SymbParamsUpdated += this.method_10;
			form.Owner = this;
			if (bool_4)
			{
				form.SymbParamCtrl.IfFocusOnAutoStopOnStartup = true;
			}
			else if (bool_5)
			{
				form.SymbParamCtrl.IfFocusOnAutoLimitOnStartup = true;
			}
			form.ShowDialog();
		}

		// Token: 0x06001076 RID: 4214 RVA: 0x0006F65C File Offset: 0x0006D85C
		private void method_10(object sender, EventArgs21 e)
		{
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints != null)
			{
				this.method_8();
				if (e.IfTurnOnAutoStop)
				{
					this.checkBox_7.Checked = true;
				}
			}
			else if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoLimitTakePoints != null)
			{
				this.method_8();
				if (e.IfTurnOnAutoLimit)
				{
					this.checkBox_6.Checked = true;
				}
			}
		}

		// Token: 0x06001077 RID: 4215 RVA: 0x0006F6DC File Offset: 0x0006D8DC
		private void button_1_Click(object sender, EventArgs e)
		{
			Base.UI.Form.IfDispDayDivLine = this.checkBox_32.Checked;
			Base.UI.Form.PeriodOfChartDispDayDivLine = this.method_15(this.comboBox_9.SelectedItem as string);
			Base.UI.Form.IfPauseAtDayEnd = this.checkBox_3.Checked;
			Base.UI.Form.IfAutoSavePageOnExit = this.checkBox_2.Checked;
			Base.UI.Form.IfSaveSpeedOnQuit = this.checkBox_0.Checked;
			Base.UI.Form.IfSaveWindowOnQuit = this.checkBox_1.Checked;
			Base.UI.Form.IfConfirmQuit = this.checkBox_4.Checked;
			Base.UI.Form.IfSymbSwitchShowCurrDT = this.radioButton_10.Checked;
			if (this.checkBox_28.Checked)
			{
				Base.UI.Form.IfShowNoTransArrow = false;
				if (this.radioButton_7.Checked)
				{
					Base.UI.Form.IfShowAllTransArrow = false;
				}
				else
				{
					Base.UI.Form.IfShowAllTransArrow = true;
				}
			}
			else
			{
				Base.UI.Form.IfShowNoTransArrow = true;
			}
			Base.UI.Form.PeriodOfChartDispTransArrow = this.method_15(this.comboBox_8.SelectedItem as string);
			Base.UI.Form.IfAlwaysShowTransNoteBox = this.checkBox_27.Checked;
			Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox = !this.checkBox_24.Checked;
			Base.UI.Form.IfHideTransNoteBorder = !this.checkBox_26.Checked;
			Base.UI.Form.IfTransNoteFillTransparent = this.checkBox_23.Checked;
			Base.UI.Form.IfDisableOpenCloseSound = !this.checkBox_31.Checked;
			Base.UI.Form.IfDisableTsOdrLine = !this.checkBox_25.Checked;
			Base.UI.Form.IfDisableAutoShowCurrTransTab = !this.checkBox_5.Checked;
			Base.UI.Form.IfShowSymbCNNameInOpenTransGridView = this.radioButton_9.Checked;
			Base.UI.Form.IfShowDayOfWeek = this.checkBox_34.Checked;
			Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader = !this.checkBox_33.Checked;
			Base.UI.Form.NoShowHighLowMark = !this.checkBox_36.Checked;
			if (this.comboBox_7.SelectedIndex == 0)
			{
				Base.UI.Form.TransArrowType = TransArrowType.BigOblique;
			}
			else if (this.comboBox_7.SelectedIndex == 1)
			{
				Base.UI.Form.TransArrowType = TransArrowType.SmallUpDn;
			}
			if (this.comboBox_10.SelectedIndex == 0)
			{
				if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_EmptyUpK);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_10.SelectedIndex == 1)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.CandleStick_SolidUpK & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_SolidUpK);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_10.SelectedIndex == 2)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.OHLCBar & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.OHLCBar);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_10.SelectedIndex == 3)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.HLCBar & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.HLCBar);
					this.bool_0 = true;
				}
			}
			if (this.comboBox_11.SelectedIndex == 0)
			{
				if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
				{
					Base.UI.Form.ChartTheme = ChartTheme.Classic;
					this.bool_1 = true;
				}
			}
			else if (this.comboBox_11.SelectedIndex == 1)
			{
				if (Base.UI.Form.ChartTheme != ChartTheme.Yellow)
				{
					Base.UI.Form.ChartTheme = ChartTheme.Yellow;
					this.bool_1 = true;
				}
			}
			else if (Base.UI.Form.ChartTheme != ChartTheme.Modern)
			{
				Base.UI.Form.ChartTheme = ChartTheme.Modern;
				this.bool_1 = true;
			}
			Base.UI.Form.IsOrderConfmNeeded = this.checkBox_30.Checked;
			Base.UI.Form.EnableShortForStock = this.checkBox_8.Checked;
			Base.UI.Form.EnableT0ForStock = this.checkBox_9.Checked;
			Base.UI.Form.IfNoConfClsTransWhenDblClick = this.checkBox_29.Checked;
			Base.UI.Form.IfCloseNewTransFirst = this.radioButton_1.Checked;
			Base.UI.Form.IfNotAutoCancelExistingOpenTransOrder = !this.checkBox_22.Checked;
			Base.UI.Form.IfNotAutoCancelExistingCloseTransOrder = !this.checkBox_35.Checked;
			Base.UI.Form.IfNoBonusShare = (this.comboBox_1.SelectedIndex == 1);
			Base.UI.Form.IfNoDivident = (this.comboBox_2.SelectedIndex == 1);
			Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits = !this.checkBox_20.Checked;
			Base.UI.Form.IfFollowPrcInTradingTab = this.checkBox_19.Checked;
			if (this.comboBox_0.SelectedIndex == 0)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
			}
			else if (this.comboBox_0.SelectedIndex == 1)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Later);
			}
			else
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.None);
			}
			if (this.comboBox_3.SelectedIndex == 0)
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Auto);
			}
			else if (this.comboBox_3.SelectedIndex == 1)
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Prompt);
			}
			else
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.None);
			}
			Base.Trading.smethod_109(this.stkSymbol_0.ID, this.checkBox_7.Checked);
			Base.Trading.smethod_110(this.stkSymbol_0.ID, this.checkBox_6.Checked);
			if (this.radioButton_4.Checked)
			{
				Base.UI.Form.IfROpenFixedAmt = true;
				bool flag = false;
				string value = this.textBox_0.Text.Trim();
				try
				{
					if (string.IsNullOrEmpty(value))
					{
						flag = true;
					}
					else if (Convert.ToInt32(value) < 1)
					{
						flag = true;
					}
				}
				catch
				{
					flag = true;
				}
				if (flag)
				{
					MessageBox.Show(Class521.smethod_0(38115), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.textBox_0.Focus();
					return;
				}
				Base.UI.Form.ROpenFixedAmt = new int?(Convert.ToInt32(value));
			}
			else
			{
				Base.UI.Form.IfROpenFixedAmt = false;
				Base.UI.Form.ROpenRatio = new decimal?(this.numericUpDown_0.Value);
			}
			Base.UI.Form.IfROpenNoShowCnfmDlg = !this.checkBox_21.Checked;
			Base.UI.smethod_47();
			this.method_0();
			base.Dispose();
		}

		// Token: 0x06001078 RID: 4216 RVA: 0x000070BB File Offset: 0x000052BB
		private void method_11(object sender, EventArgs e)
		{
			this.method_12();
		}

		// Token: 0x06001079 RID: 4217 RVA: 0x0006FDA0 File Offset: 0x0006DFA0
		private void method_12()
		{
			if (this.checkBox_32.Checked)
			{
				this.comboBox_9.Enabled = true;
				this.label_11.Enabled = true;
			}
			else
			{
				this.comboBox_9.Enabled = false;
				this.label_11.Enabled = false;
			}
		}

		// Token: 0x0600107A RID: 4218 RVA: 0x000041B9 File Offset: 0x000023B9
		private void button_0_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x0600107B RID: 4219 RVA: 0x000070C5 File Offset: 0x000052C5
		private void checkBox_28_CheckedChanged(object sender, EventArgs e)
		{
			this.method_13();
		}

		// Token: 0x0600107C RID: 4220 RVA: 0x0006FDF0 File Offset: 0x0006DFF0
		private void method_13()
		{
			if (this.checkBox_28.Checked)
			{
				this.groupBox_11.Enabled = true;
				this.radioButton_7.Enabled = true;
				this.radioButton_6.Enabled = true;
				this.comboBox_8.Enabled = true;
				this.label_10.Enabled = true;
				if (Base.UI.Form.IfShowAllTransArrow)
				{
					this.radioButton_7.Checked = false;
				}
				else
				{
					this.radioButton_6.Checked = false;
				}
			}
			else
			{
				this.groupBox_11.Enabled = false;
				this.radioButton_7.Enabled = false;
				this.radioButton_6.Enabled = false;
				this.comboBox_8.Enabled = false;
				this.label_10.Enabled = false;
			}
		}

		// Token: 0x0600107D RID: 4221 RVA: 0x000070CF File Offset: 0x000052CF
		private void checkBox_27_CheckedChanged(object sender, EventArgs e)
		{
			this.method_14();
		}

		// Token: 0x0600107E RID: 4222 RVA: 0x000070D9 File Offset: 0x000052D9
		private void method_14()
		{
			if (this.checkBox_27.Checked)
			{
				this.checkBox_24.Enabled = true;
			}
			else
			{
				this.checkBox_24.Enabled = false;
			}
		}

		// Token: 0x0600107F RID: 4223 RVA: 0x0006FEAC File Offset: 0x0006E0AC
		private TimeUnit method_15(string string_0)
		{
			uint num = Class511.smethod_0(string_0);
			if (num <= 2269934029U)
			{
				if (num <= 1239321821U)
				{
					if (num != 647060167U)
					{
						if (num == 1239321821U)
						{
							if (string_0 == Class521.smethod_0(16377))
							{
								return TimeUnit.ThirtyMins;
							}
						}
					}
					else if (string_0 == Class521.smethod_0(16351))
					{
						return TimeUnit.FiveMins;
					}
				}
				else if (num != 2026632856U)
				{
					if (num != 2123123768U)
					{
						if (num == 2269934029U)
						{
							if (string_0 == Class521.smethod_0(37487))
							{
								return TimeUnit.OneHour;
							}
						}
					}
					else if (string_0 == Class521.smethod_0(12228))
					{
						return TimeUnit.Week;
					}
				}
				else if (string_0 == Class521.smethod_0(12247))
				{
					return TimeUnit.Month;
				}
			}
			else if (num <= 3193763256U)
			{
				if (num != 3092397832U)
				{
					if (num == 3193763256U)
					{
						if (string_0 == Class521.smethod_0(16364))
						{
							return TimeUnit.FifteenMins;
						}
					}
				}
				else if (string_0 == Class521.smethod_0(37500))
				{
					return TimeUnit.TwoHour;
				}
			}
			else if (num != 3462998755U)
			{
				if (num != 3897470057U)
				{
					if (num == 4074130189U)
					{
						if (string_0 == Class521.smethod_0(12209))
						{
							return TimeUnit.Day;
						}
					}
				}
				else if (string_0 == Class521.smethod_0(16338))
				{
					return TimeUnit.ThreeMins;
				}
			}
			else if (string_0 == Class521.smethod_0(16325))
			{
				return TimeUnit.OneMin;
			}
			return TimeUnit.OneMin;
		}

		// Token: 0x06001080 RID: 4224 RVA: 0x0007004C File Offset: 0x0006E24C
		private string method_16(TimeUnit timeUnit_0)
		{
			if (timeUnit_0 <= TimeUnit.OneHour)
			{
				if (timeUnit_0 <= TimeUnit.FifteenMins)
				{
					switch (timeUnit_0)
					{
					case TimeUnit.OneMin:
						return Class521.smethod_0(16325);
					case (TimeUnit)2:
					case (TimeUnit)4:
						break;
					case TimeUnit.ThreeMins:
						return Class521.smethod_0(16338);
					case TimeUnit.FiveMins:
						return Class521.smethod_0(16351);
					default:
						if (timeUnit_0 == TimeUnit.FifteenMins)
						{
							return Class521.smethod_0(16364);
						}
						break;
					}
				}
				else
				{
					if (timeUnit_0 == TimeUnit.ThirtyMins)
					{
						return Class521.smethod_0(16377);
					}
					if (timeUnit_0 == TimeUnit.OneHour)
					{
						return Class521.smethod_0(37487);
					}
				}
			}
			else if (timeUnit_0 <= TimeUnit.Day)
			{
				if (timeUnit_0 == TimeUnit.TwoHour)
				{
					return Class521.smethod_0(37500);
				}
				if (timeUnit_0 == TimeUnit.Day)
				{
					return Class521.smethod_0(12209);
				}
			}
			else
			{
				if (timeUnit_0 == TimeUnit.Week)
				{
					return Class521.smethod_0(12228);
				}
				if (timeUnit_0 == TimeUnit.Month)
				{
					return Class521.smethod_0(12247);
				}
			}
			return string.Empty;
		}

		// Token: 0x06001081 RID: 4225 RVA: 0x00007104 File Offset: 0x00005304
		private void textBox_0_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x17000268 RID: 616
		// (get) Token: 0x06001082 RID: 4226 RVA: 0x00070158 File Offset: 0x0006E358
		public bool KLineTypeChanged
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x17000269 RID: 617
		// (get) Token: 0x06001083 RID: 4227 RVA: 0x00070170 File Offset: 0x0006E370
		public bool ChartThemeChanged
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x06001084 RID: 4228 RVA: 0x00007129 File Offset: 0x00005329
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001085 RID: 4229 RVA: 0x00070188 File Offset: 0x0006E388
		private void method_17()
		{
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.checkBox_0 = new CheckBox();
			this.checkBox_1 = new CheckBox();
			this.checkBox_2 = new CheckBox();
			this.checkBox_3 = new CheckBox();
			this.checkBox_4 = new CheckBox();
			this.groupBox_0 = new GroupBox();
			this.checkBox_19 = new CheckBox();
			this.checkBox_20 = new CheckBox();
			this.checkBox_5 = new CheckBox();
			this.groupBox_1 = new GroupBox();
			this.tabControl_0 = new TabControl();
			this.tabPage_3 = new TabPage();
			this.groupBox_14 = new GroupBox();
			this.pictureBox_2 = new PictureBox();
			this.comboBox_10 = new ComboBox();
			this.comboBox_11 = new ComboBox();
			this.label_12 = new Label();
			this.label_13 = new Label();
			this.groupBox_13 = new GroupBox();
			this.checkBox_34 = new CheckBox();
			this.label_15 = new Label();
			this.label_11 = new Label();
			this.comboBox_9 = new ComboBox();
			this.checkBox_32 = new CheckBox();
			this.groupBox_9 = new GroupBox();
			this.checkBox_36 = new CheckBox();
			this.groupBox_10 = new GroupBox();
			this.pictureBox_1 = new PictureBox();
			this.comboBox_7 = new ComboBox();
			this.groupBox_11 = new GroupBox();
			this.label_9 = new Label();
			this.checkBox_23 = new CheckBox();
			this.label_10 = new Label();
			this.checkBox_24 = new CheckBox();
			this.comboBox_8 = new ComboBox();
			this.checkBox_25 = new CheckBox();
			this.checkBox_26 = new CheckBox();
			this.panel_1 = new Panel();
			this.radioButton_6 = new RadioButton();
			this.radioButton_7 = new RadioButton();
			this.checkBox_27 = new CheckBox();
			this.checkBox_28 = new CheckBox();
			this.tabPage_0 = new TabPage();
			this.groupBox_19 = new GroupBox();
			this.panel_3 = new Panel();
			this.radioButton_10 = new RadioButton();
			this.radioButton_11 = new RadioButton();
			this.label_16 = new Label();
			this.groupBox_16 = new GroupBox();
			this.checkBox_33 = new CheckBox();
			this.groupBox_17 = new GroupBox();
			this.panel_2 = new Panel();
			this.radioButton_8 = new RadioButton();
			this.radioButton_9 = new RadioButton();
			this.label_14 = new Label();
			this.groupBox_12 = new GroupBox();
			this.checkBox_29 = new CheckBox();
			this.checkBox_30 = new CheckBox();
			this.checkBox_31 = new CheckBox();
			this.tabPage_1 = new TabPage();
			this.groupBox_18 = new GroupBox();
			this.panel_0 = new Panel();
			this.radioButton_1 = new RadioButton();
			this.radioButton_0 = new RadioButton();
			this.groupBox_5 = new GroupBox();
			this.label_8 = new Label();
			this.textBox_0 = new TextBox();
			this.label_7 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.checkBox_21 = new CheckBox();
			this.radioButton_4 = new RadioButton();
			this.radioButton_5 = new RadioButton();
			this.groupBox_4 = new GroupBox();
			this.checkBox_35 = new CheckBox();
			this.checkBox_22 = new CheckBox();
			this.groupBox_3 = new GroupBox();
			this.groupBox_15 = new GroupBox();
			this.comboBox_3 = new ComboBox();
			this.comboBox_2 = new ComboBox();
			this.label_3 = new Label();
			this.label_2 = new Label();
			this.comboBox_1 = new ComboBox();
			this.label_1 = new Label();
			this.comboBox_0 = new ComboBox();
			this.label_0 = new Label();
			this.checkBox_9 = new CheckBox();
			this.checkBox_8 = new CheckBox();
			this.groupBox_2 = new GroupBox();
			this.linkLabel_1 = new LinkLabel();
			this.linkLabel_0 = new LinkLabel();
			this.checkBox_6 = new CheckBox();
			this.checkBox_7 = new CheckBox();
			this.tabPage_2 = new TabPage();
			this.label_6 = new Label();
			this.button_3 = new Button();
			this.button_2 = new Button();
			this.pictureBox_0 = new PictureBox();
			this.groupBox_8 = new GroupBox();
			this.comboBox_6 = new ComboBox();
			this.label_5 = new Label();
			this.comboBox_5 = new ComboBox();
			this.radioButton_3 = new RadioButton();
			this.label_4 = new Label();
			this.radioButton_2 = new RadioButton();
			this.groupBox_7 = new GroupBox();
			this.comboBox_4 = new ComboBox();
			this.checkBox_15 = new CheckBox();
			this.checkBox_16 = new CheckBox();
			this.checkBox_17 = new CheckBox();
			this.groupBox_6 = new GroupBox();
			this.checkBox_18 = new CheckBox();
			this.checkBox_10 = new CheckBox();
			this.checkBox_11 = new CheckBox();
			this.checkBox_12 = new CheckBox();
			this.checkBox_13 = new CheckBox();
			this.checkBox_14 = new CheckBox();
			this.groupBox_0.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			this.tabControl_0.SuspendLayout();
			this.tabPage_3.SuspendLayout();
			this.groupBox_14.SuspendLayout();
			((ISupportInitialize)this.pictureBox_2).BeginInit();
			this.groupBox_13.SuspendLayout();
			this.groupBox_9.SuspendLayout();
			((ISupportInitialize)this.pictureBox_1).BeginInit();
			this.panel_1.SuspendLayout();
			this.tabPage_0.SuspendLayout();
			this.groupBox_19.SuspendLayout();
			this.panel_3.SuspendLayout();
			this.groupBox_16.SuspendLayout();
			this.panel_2.SuspendLayout();
			this.groupBox_12.SuspendLayout();
			this.tabPage_1.SuspendLayout();
			this.groupBox_18.SuspendLayout();
			this.panel_0.SuspendLayout();
			this.groupBox_5.SuspendLayout();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			this.groupBox_4.SuspendLayout();
			this.groupBox_3.SuspendLayout();
			this.groupBox_2.SuspendLayout();
			this.tabPage_2.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			this.groupBox_8.SuspendLayout();
			this.groupBox_7.SuspendLayout();
			this.groupBox_6.SuspendLayout();
			base.SuspendLayout();
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(645, 452);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(120, 32);
			this.button_0.TabIndex = 1;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.Location = new Point(513, 452);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(120, 32);
			this.button_1.TabIndex = 0;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(22, 55);
			this.checkBox_0.Name = Class521.smethod_0(38196);
			this.checkBox_0.Size = new Size(194, 19);
			this.checkBox_0.TabIndex = 1;
			this.checkBox_0.Text = Class521.smethod_0(38217);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.Location = new Point(22, 111);
			this.checkBox_1.Name = Class521.smethod_0(38262);
			this.checkBox_1.Size = new Size(224, 19);
			this.checkBox_1.TabIndex = 3;
			this.checkBox_1.Text = Class521.smethod_0(38287);
			this.checkBox_1.UseVisualStyleBackColor = true;
			this.checkBox_2.AutoSize = true;
			this.checkBox_2.Location = new Point(22, 83);
			this.checkBox_2.Name = Class521.smethod_0(38340);
			this.checkBox_2.Size = new Size(194, 19);
			this.checkBox_2.TabIndex = 2;
			this.checkBox_2.Text = Class521.smethod_0(38361);
			this.checkBox_2.UseVisualStyleBackColor = true;
			this.checkBox_3.AutoSize = true;
			this.checkBox_3.Location = new Point(22, 30);
			this.checkBox_3.Name = Class521.smethod_0(38406);
			this.checkBox_3.Size = new Size(194, 19);
			this.checkBox_3.TabIndex = 0;
			this.checkBox_3.Text = Class521.smethod_0(38435);
			this.checkBox_3.UseVisualStyleBackColor = true;
			this.checkBox_4.AutoSize = true;
			this.checkBox_4.Location = new Point(22, 27);
			this.checkBox_4.Name = Class521.smethod_0(38480);
			this.checkBox_4.Size = new Size(164, 19);
			this.checkBox_4.TabIndex = 0;
			this.checkBox_4.Text = Class521.smethod_0(38501);
			this.checkBox_4.UseVisualStyleBackColor = true;
			this.groupBox_0.Controls.Add(this.checkBox_1);
			this.groupBox_0.Controls.Add(this.checkBox_0);
			this.groupBox_0.Controls.Add(this.checkBox_2);
			this.groupBox_0.Controls.Add(this.checkBox_4);
			this.groupBox_0.Location = new Point(22, 16);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(340, 148);
			this.groupBox_0.TabIndex = 0;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(38538);
			this.checkBox_19.AutoSize = true;
			this.checkBox_19.Location = new Point(21, 83);
			this.checkBox_19.Name = Class521.smethod_0(38555);
			this.checkBox_19.Size = new Size(224, 19);
			this.checkBox_19.TabIndex = 2;
			this.checkBox_19.Text = Class521.smethod_0(38600);
			this.checkBox_19.UseVisualStyleBackColor = true;
			this.checkBox_20.AutoSize = true;
			this.checkBox_20.Location = new Point(21, 55);
			this.checkBox_20.Name = Class521.smethod_0(38653);
			this.checkBox_20.Size = new Size(232, 19);
			this.checkBox_20.TabIndex = 1;
			this.checkBox_20.Text = Class521.smethod_0(38706);
			this.checkBox_20.UseVisualStyleBackColor = true;
			this.checkBox_5.AutoSize = true;
			this.checkBox_5.Location = new Point(21, 27);
			this.checkBox_5.Name = Class521.smethod_0(38763);
			this.checkBox_5.Size = new Size(179, 19);
			this.checkBox_5.TabIndex = 0;
			this.checkBox_5.Text = Class521.smethod_0(38804);
			this.checkBox_5.UseVisualStyleBackColor = true;
			this.groupBox_1.Controls.Add(this.checkBox_3);
			this.groupBox_1.Location = new Point(22, 304);
			this.groupBox_1.Name = Class521.smethod_0(20658);
			this.groupBox_1.Size = new Size(340, 70);
			this.groupBox_1.TabIndex = 2;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(38845);
			this.tabControl_0.Controls.Add(this.tabPage_3);
			this.tabControl_0.Controls.Add(this.tabPage_0);
			this.tabControl_0.Controls.Add(this.tabPage_1);
			this.tabControl_0.Controls.Add(this.tabPage_2);
			this.tabControl_0.Location = new Point(12, 12);
			this.tabControl_0.Name = Class521.smethod_0(38862);
			this.tabControl_0.SelectedIndex = 0;
			this.tabControl_0.Size = new Size(755, 426);
			this.tabControl_0.TabIndex = 16;
			this.tabPage_3.BackColor = SystemColors.Control;
			this.tabPage_3.Controls.Add(this.groupBox_14);
			this.tabPage_3.Controls.Add(this.groupBox_13);
			this.tabPage_3.Controls.Add(this.groupBox_9);
			this.tabPage_3.Location = new Point(4, 25);
			this.tabPage_3.Name = Class521.smethod_0(38891);
			this.tabPage_3.Padding = new Padding(3);
			this.tabPage_3.Size = new Size(747, 397);
			this.tabPage_3.TabIndex = 3;
			this.tabPage_3.Text = Class521.smethod_0(38916);
			this.groupBox_14.Controls.Add(this.pictureBox_2);
			this.groupBox_14.Controls.Add(this.comboBox_10);
			this.groupBox_14.Controls.Add(this.comboBox_11);
			this.groupBox_14.Controls.Add(this.label_12);
			this.groupBox_14.Controls.Add(this.label_13);
			this.groupBox_14.Location = new Point(384, 16);
			this.groupBox_14.Name = Class521.smethod_0(38933);
			this.groupBox_14.Size = new Size(340, 228);
			this.groupBox_14.TabIndex = 19;
			this.groupBox_14.TabStop = false;
			this.groupBox_14.Text = Class521.smethod_0(38950);
			this.pictureBox_2.Image = Class375.theme_1_3;
			this.pictureBox_2.Location = new Point(48, 93);
			this.pictureBox_2.Name = Class521.smethod_0(38967);
			this.pictureBox_2.Size = new Size(261, 120);
			this.pictureBox_2.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_2.TabIndex = 19;
			this.pictureBox_2.TabStop = false;
			this.comboBox_10.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_10.FormattingEnabled = true;
			this.comboBox_10.Items.AddRange(new object[]
			{
				Class521.smethod_0(38984),
				Class521.smethod_0(39017),
				Class521.smethod_0(39050),
				Class521.smethod_0(39071)
			});
			this.comboBox_10.Location = new Point(111, 26);
			this.comboBox_10.Name = Class521.smethod_0(39092);
			this.comboBox_10.Size = new Size(198, 23);
			this.comboBox_10.TabIndex = 16;
			this.comboBox_11.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_11.FormattingEnabled = true;
			this.comboBox_11.Items.AddRange(new object[]
			{
				Class521.smethod_0(39117),
				Class521.smethod_0(39134),
				Class521.smethod_0(39151)
			});
			this.comboBox_11.Location = new Point(111, 59);
			this.comboBox_11.Name = Class521.smethod_0(39168);
			this.comboBox_11.Size = new Size(198, 23);
			this.comboBox_11.TabIndex = 18;
			this.label_12.AutoSize = true;
			this.label_12.Location = new Point(23, 30);
			this.label_12.Name = Class521.smethod_0(11267);
			this.label_12.Size = new Size(82, 15);
			this.label_12.TabIndex = 3;
			this.label_12.Text = Class521.smethod_0(39197);
			this.label_13.AutoSize = true;
			this.label_13.Location = new Point(23, 63);
			this.label_13.Name = Class521.smethod_0(39218);
			this.label_13.Size = new Size(82, 15);
			this.label_13.TabIndex = 17;
			this.label_13.Text = Class521.smethod_0(39231);
			this.groupBox_13.Controls.Add(this.checkBox_34);
			this.groupBox_13.Controls.Add(this.label_15);
			this.groupBox_13.Controls.Add(this.label_11);
			this.groupBox_13.Controls.Add(this.comboBox_9);
			this.groupBox_13.Controls.Add(this.checkBox_32);
			this.groupBox_13.Location = new Point(385, 255);
			this.groupBox_13.Name = Class521.smethod_0(39252);
			this.groupBox_13.Size = new Size(340, 121);
			this.groupBox_13.TabIndex = 2;
			this.groupBox_13.TabStop = false;
			this.groupBox_13.Text = Class521.smethod_0(39265);
			this.checkBox_34.AutoSize = true;
			this.checkBox_34.Location = new Point(23, 84);
			this.checkBox_34.Name = Class521.smethod_0(39282);
			this.checkBox_34.Size = new Size(119, 19);
			this.checkBox_34.TabIndex = 17;
			this.checkBox_34.Text = Class521.smethod_0(39315);
			this.checkBox_34.UseVisualStyleBackColor = true;
			this.label_15.AutoSize = true;
			this.label_15.Location = new Point(43, 56);
			this.label_15.Name = Class521.smethod_0(20859);
			this.label_15.Size = new Size(52, 15);
			this.label_15.TabIndex = 16;
			this.label_15.Text = Class521.smethod_0(39340);
			this.label_11.AutoSize = true;
			this.label_11.Location = new Point(187, 56);
			this.label_11.Name = Class521.smethod_0(39353);
			this.label_11.Size = new Size(112, 15);
			this.label_11.TabIndex = 13;
			this.label_11.Text = Class521.smethod_0(39382);
			this.label_11.TextAlign = ContentAlignment.MiddleRight;
			this.comboBox_9.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_9.FormattingEnabled = true;
			this.comboBox_9.Location = new Point(103, 52);
			this.comboBox_9.Name = Class521.smethod_0(39411);
			this.comboBox_9.Size = new Size(76, 23);
			this.comboBox_9.TabIndex = 15;
			this.checkBox_32.AutoSize = true;
			this.checkBox_32.Location = new Point(23, 30);
			this.checkBox_32.Name = Class521.smethod_0(39440);
			this.checkBox_32.Size = new Size(164, 19);
			this.checkBox_32.TabIndex = 14;
			this.checkBox_32.Text = Class521.smethod_0(39473);
			this.checkBox_32.UseVisualStyleBackColor = true;
			this.groupBox_9.Controls.Add(this.checkBox_36);
			this.groupBox_9.Controls.Add(this.groupBox_10);
			this.groupBox_9.Controls.Add(this.pictureBox_1);
			this.groupBox_9.Controls.Add(this.comboBox_7);
			this.groupBox_9.Controls.Add(this.groupBox_11);
			this.groupBox_9.Controls.Add(this.label_9);
			this.groupBox_9.Controls.Add(this.checkBox_23);
			this.groupBox_9.Controls.Add(this.label_10);
			this.groupBox_9.Controls.Add(this.checkBox_24);
			this.groupBox_9.Controls.Add(this.comboBox_8);
			this.groupBox_9.Controls.Add(this.checkBox_25);
			this.groupBox_9.Controls.Add(this.checkBox_26);
			this.groupBox_9.Controls.Add(this.panel_1);
			this.groupBox_9.Controls.Add(this.checkBox_27);
			this.groupBox_9.Controls.Add(this.checkBox_28);
			this.groupBox_9.Location = new Point(22, 16);
			this.groupBox_9.Name = Class521.smethod_0(20273);
			this.groupBox_9.Size = new Size(340, 360);
			this.groupBox_9.TabIndex = 1;
			this.groupBox_9.TabStop = false;
			this.groupBox_9.Text = Class521.smethod_0(39510);
			this.checkBox_36.AutoSize = true;
			this.checkBox_36.Location = new Point(22, 327);
			this.checkBox_36.Name = Class521.smethod_0(39527);
			this.checkBox_36.Size = new Size(164, 19);
			this.checkBox_36.TabIndex = 18;
			this.checkBox_36.Text = Class521.smethod_0(39560);
			this.checkBox_36.UseVisualStyleBackColor = true;
			this.groupBox_10.Location = new Point(44, 106);
			this.groupBox_10.Name = Class521.smethod_0(10647);
			this.groupBox_10.Size = new Size(215, 2);
			this.groupBox_10.TabIndex = 17;
			this.groupBox_10.TabStop = false;
			this.pictureBox_1.Image = Class375.BigArrwSamples;
			this.pictureBox_1.Location = new Point(47, 143);
			this.pictureBox_1.Name = Class521.smethod_0(39597);
			this.pictureBox_1.Size = new Size(174, 39);
			this.pictureBox_1.SizeMode = PictureBoxSizeMode.AutoSize;
			this.pictureBox_1.TabIndex = 16;
			this.pictureBox_1.TabStop = false;
			this.comboBox_7.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_7.FormattingEnabled = true;
			this.comboBox_7.Items.AddRange(new object[]
			{
				Class521.smethod_0(39618),
				Class521.smethod_0(39639)
			});
			this.comboBox_7.Location = new Point(132, 113);
			this.comboBox_7.Name = Class521.smethod_0(39660);
			this.comboBox_7.Size = new Size(110, 23);
			this.comboBox_7.TabIndex = 15;
			this.groupBox_11.Location = new Point(44, 188);
			this.groupBox_11.Name = Class521.smethod_0(39685);
			this.groupBox_11.Size = new Size(215, 2);
			this.groupBox_11.TabIndex = 13;
			this.groupBox_11.TabStop = false;
			this.label_9.AutoSize = true;
			this.label_9.Location = new Point(44, 117);
			this.label_9.Name = Class521.smethod_0(7268);
			this.label_9.Size = new Size(82, 15);
			this.label_9.TabIndex = 14;
			this.label_9.Text = Class521.smethod_0(39710);
			this.checkBox_23.AutoSize = true;
			this.checkBox_23.Location = new Point(44, 270);
			this.checkBox_23.Name = Class521.smethod_0(39731);
			this.checkBox_23.Size = new Size(179, 19);
			this.checkBox_23.TabIndex = 7;
			this.checkBox_23.Text = Class521.smethod_0(39780);
			this.checkBox_23.TextAlign = ContentAlignment.MiddleCenter;
			this.checkBox_23.UseVisualStyleBackColor = true;
			this.label_10.AutoSize = true;
			this.label_10.Location = new Point(123, 81);
			this.label_10.Name = Class521.smethod_0(39821);
			this.label_10.Size = new Size(112, 15);
			this.label_10.TabIndex = 12;
			this.label_10.Text = Class521.smethod_0(39382);
			this.label_10.TextAlign = ContentAlignment.MiddleRight;
			this.checkBox_24.AutoSize = true;
			this.checkBox_24.Location = new Point(66, 220);
			this.checkBox_24.Name = Class521.smethod_0(39858);
			this.checkBox_24.Size = new Size(134, 19);
			this.checkBox_24.TabIndex = 5;
			this.checkBox_24.Text = Class521.smethod_0(39899);
			this.checkBox_24.UseVisualStyleBackColor = true;
			this.comboBox_8.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_8.FormattingEnabled = true;
			this.comboBox_8.Location = new Point(44, 78);
			this.comboBox_8.Name = Class521.smethod_0(39928);
			this.comboBox_8.Size = new Size(76, 23);
			this.comboBox_8.TabIndex = 3;
			this.checkBox_25.AutoSize = true;
			this.checkBox_25.Location = new Point(22, 300);
			this.checkBox_25.Name = Class521.smethod_0(39965);
			this.checkBox_25.Size = new Size(239, 19);
			this.checkBox_25.TabIndex = 0;
			this.checkBox_25.Text = Class521.smethod_0(39994);
			this.checkBox_25.UseVisualStyleBackColor = true;
			this.checkBox_26.AutoSize = true;
			this.checkBox_26.Location = new Point(44, 245);
			this.checkBox_26.Name = Class521.smethod_0(40051);
			this.checkBox_26.Size = new Size(179, 19);
			this.checkBox_26.TabIndex = 6;
			this.checkBox_26.Text = Class521.smethod_0(40092);
			this.checkBox_26.UseVisualStyleBackColor = true;
			this.panel_1.Controls.Add(this.radioButton_6);
			this.panel_1.Controls.Add(this.radioButton_7);
			this.panel_1.Location = new Point(42, 49);
			this.panel_1.Name = Class521.smethod_0(8903);
			this.panel_1.Size = new Size(200, 27);
			this.panel_1.TabIndex = 7;
			this.radioButton_6.AutoSize = true;
			this.radioButton_6.Location = new Point(98, 4);
			this.radioButton_6.Name = Class521.smethod_0(40133);
			this.radioButton_6.Size = new Size(88, 19);
			this.radioButton_6.TabIndex = 1;
			this.radioButton_6.TabStop = true;
			this.radioButton_6.Text = Class521.smethod_0(40166);
			this.radioButton_6.UseVisualStyleBackColor = true;
			this.radioButton_7.AutoSize = true;
			this.radioButton_7.Location = new Point(2, 4);
			this.radioButton_7.Name = Class521.smethod_0(40183);
			this.radioButton_7.Size = new Size(88, 19);
			this.radioButton_7.TabIndex = 0;
			this.radioButton_7.TabStop = true;
			this.radioButton_7.Text = Class521.smethod_0(40216);
			this.radioButton_7.UseVisualStyleBackColor = true;
			this.checkBox_27.AutoSize = true;
			this.checkBox_27.Location = new Point(44, 197);
			this.checkBox_27.Name = Class521.smethod_0(40233);
			this.checkBox_27.Size = new Size(164, 19);
			this.checkBox_27.TabIndex = 4;
			this.checkBox_27.Text = Class521.smethod_0(40278);
			this.checkBox_27.UseVisualStyleBackColor = true;
			this.checkBox_28.AutoSize = true;
			this.checkBox_28.Location = new Point(22, 27);
			this.checkBox_28.Name = Class521.smethod_0(40315);
			this.checkBox_28.Size = new Size(164, 19);
			this.checkBox_28.TabIndex = 0;
			this.checkBox_28.Text = Class521.smethod_0(40348);
			this.checkBox_28.UseVisualStyleBackColor = true;
			this.tabPage_0.BackColor = SystemColors.Control;
			this.tabPage_0.Controls.Add(this.groupBox_19);
			this.tabPage_0.Controls.Add(this.groupBox_16);
			this.tabPage_0.Controls.Add(this.groupBox_12);
			this.tabPage_0.Controls.Add(this.groupBox_0);
			this.tabPage_0.Controls.Add(this.groupBox_1);
			this.tabPage_0.Location = new Point(4, 25);
			this.tabPage_0.Name = Class521.smethod_0(40385);
			this.tabPage_0.Padding = new Padding(3);
			this.tabPage_0.Size = new Size(747, 397);
			this.tabPage_0.TabIndex = 0;
			this.tabPage_0.Text = Class521.smethod_0(40410);
			this.groupBox_19.Controls.Add(this.panel_3);
			this.groupBox_19.Controls.Add(this.label_16);
			this.groupBox_19.Location = new Point(384, 238);
			this.groupBox_19.Name = Class521.smethod_0(40427);
			this.groupBox_19.Size = new Size(340, 136);
			this.groupBox_19.TabIndex = 0;
			this.groupBox_19.TabStop = false;
			this.groupBox_19.Text = Class521.smethod_0(40444);
			this.panel_3.Controls.Add(this.radioButton_10);
			this.panel_3.Controls.Add(this.radioButton_11);
			this.panel_3.Location = new Point(21, 55);
			this.panel_3.Name = Class521.smethod_0(40469);
			this.panel_3.Size = new Size(287, 59);
			this.panel_3.TabIndex = 22;
			this.radioButton_10.AutoSize = true;
			this.radioButton_10.Location = new Point(4, 34);
			this.radioButton_10.Name = Class521.smethod_0(40498);
			this.radioButton_10.Size = new Size(118, 19);
			this.radioButton_10.TabIndex = 1;
			this.radioButton_10.TabStop = true;
			this.radioButton_10.Text = Class521.smethod_0(40535);
			this.radioButton_10.UseVisualStyleBackColor = true;
			this.radioButton_11.AutoSize = true;
			this.radioButton_11.Location = new Point(4, 7);
			this.radioButton_11.Name = Class521.smethod_0(40560);
			this.radioButton_11.Size = new Size(163, 19);
			this.radioButton_11.TabIndex = 0;
			this.radioButton_11.TabStop = true;
			this.radioButton_11.Text = Class521.smethod_0(40597);
			this.radioButton_11.UseVisualStyleBackColor = true;
			this.label_16.AutoSize = true;
			this.label_16.Location = new Point(18, 32);
			this.label_16.Name = Class521.smethod_0(11233);
			this.label_16.Size = new Size(232, 15);
			this.label_16.TabIndex = 0;
			this.label_16.Text = Class521.smethod_0(40634);
			this.groupBox_16.Controls.Add(this.checkBox_33);
			this.groupBox_16.Controls.Add(this.groupBox_17);
			this.groupBox_16.Controls.Add(this.panel_2);
			this.groupBox_16.Controls.Add(this.label_14);
			this.groupBox_16.Controls.Add(this.checkBox_19);
			this.groupBox_16.Controls.Add(this.checkBox_5);
			this.groupBox_16.Controls.Add(this.checkBox_20);
			this.groupBox_16.Location = new Point(384, 16);
			this.groupBox_16.Name = Class521.smethod_0(40695);
			this.groupBox_16.Size = new Size(340, 210);
			this.groupBox_16.TabIndex = 4;
			this.groupBox_16.TabStop = false;
			this.groupBox_16.Text = Class521.smethod_0(40712);
			this.checkBox_33.AutoSize = true;
			this.checkBox_33.Location = new Point(21, 112);
			this.checkBox_33.Name = Class521.smethod_0(40733);
			this.checkBox_33.Size = new Size(239, 19);
			this.checkBox_33.TabIndex = 3;
			this.checkBox_33.Text = Class521.smethod_0(40786);
			this.checkBox_33.UseVisualStyleBackColor = true;
			this.groupBox_17.Location = new Point(20, 147);
			this.groupBox_17.Name = Class521.smethod_0(40843);
			this.groupBox_17.Size = new Size(285, 2);
			this.groupBox_17.TabIndex = 4;
			this.groupBox_17.TabStop = false;
			this.panel_2.Controls.Add(this.radioButton_8);
			this.panel_2.Controls.Add(this.radioButton_9);
			this.panel_2.Location = new Point(110, 158);
			this.panel_2.Name = Class521.smethod_0(40860);
			this.panel_2.Size = new Size(193, 32);
			this.panel_2.TabIndex = 6;
			this.radioButton_8.AutoSize = true;
			this.radioButton_8.Location = new Point(96, 7);
			this.radioButton_8.Name = Class521.smethod_0(40869);
			this.radioButton_8.Size = new Size(88, 19);
			this.radioButton_8.TabIndex = 1;
			this.radioButton_8.TabStop = true;
			this.radioButton_8.Text = Class521.smethod_0(40898);
			this.radioButton_8.UseVisualStyleBackColor = true;
			this.radioButton_9.AutoSize = true;
			this.radioButton_9.Location = new Point(4, 7);
			this.radioButton_9.Name = Class521.smethod_0(40915);
			this.radioButton_9.Size = new Size(88, 19);
			this.radioButton_9.TabIndex = 0;
			this.radioButton_9.TabStop = true;
			this.radioButton_9.Text = Class521.smethod_0(40948);
			this.radioButton_9.UseVisualStyleBackColor = true;
			this.label_14.AutoSize = true;
			this.label_14.Location = new Point(18, 167);
			this.label_14.Name = Class521.smethod_0(40965);
			this.label_14.Size = new Size(97, 15);
			this.label_14.TabIndex = 5;
			this.label_14.Text = Class521.smethod_0(40978);
			this.groupBox_12.Controls.Add(this.checkBox_29);
			this.groupBox_12.Controls.Add(this.checkBox_30);
			this.groupBox_12.Controls.Add(this.checkBox_31);
			this.groupBox_12.Location = new Point(22, 175);
			this.groupBox_12.Name = Class521.smethod_0(21854);
			this.groupBox_12.Size = new Size(340, 118);
			this.groupBox_12.TabIndex = 1;
			this.groupBox_12.TabStop = false;
			this.groupBox_12.Text = Class521.smethod_0(41003);
			this.checkBox_29.AutoSize = true;
			this.checkBox_29.Location = new Point(23, 81);
			this.checkBox_29.Name = Class521.smethod_0(41020);
			this.checkBox_29.Size = new Size(209, 19);
			this.checkBox_29.TabIndex = 2;
			this.checkBox_29.Text = Class521.smethod_0(41057);
			this.checkBox_29.UseVisualStyleBackColor = true;
			this.checkBox_30.AutoSize = true;
			this.checkBox_30.Location = new Point(23, 27);
			this.checkBox_30.Name = Class521.smethod_0(41106);
			this.checkBox_30.Size = new Size(134, 19);
			this.checkBox_30.TabIndex = 0;
			this.checkBox_30.Text = Class521.smethod_0(41127);
			this.checkBox_30.UseVisualStyleBackColor = true;
			this.checkBox_31.AutoSize = true;
			this.checkBox_31.Location = new Point(23, 54);
			this.checkBox_31.Name = Class521.smethod_0(41156);
			this.checkBox_31.Size = new Size(164, 19);
			this.checkBox_31.TabIndex = 1;
			this.checkBox_31.Text = Class521.smethod_0(41185);
			this.checkBox_31.UseVisualStyleBackColor = true;
			this.tabPage_1.BackColor = SystemColors.Control;
			this.tabPage_1.Controls.Add(this.groupBox_18);
			this.tabPage_1.Controls.Add(this.groupBox_5);
			this.tabPage_1.Controls.Add(this.groupBox_4);
			this.tabPage_1.Controls.Add(this.groupBox_3);
			this.tabPage_1.Controls.Add(this.groupBox_2);
			this.tabPage_1.Location = new Point(4, 25);
			this.tabPage_1.Name = Class521.smethod_0(41222);
			this.tabPage_1.Padding = new Padding(3);
			this.tabPage_1.Size = new Size(747, 397);
			this.tabPage_1.TabIndex = 1;
			this.tabPage_1.Text = Class521.smethod_0(41255);
			this.groupBox_18.Controls.Add(this.panel_0);
			this.groupBox_18.Location = new Point(384, 271);
			this.groupBox_18.Name = Class521.smethod_0(20235);
			this.groupBox_18.Size = new Size(340, 99);
			this.groupBox_18.TabIndex = 5;
			this.groupBox_18.TabStop = false;
			this.groupBox_18.Text = Class521.smethod_0(41272);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Location = new Point(17, 21);
			this.panel_0.Name = Class521.smethod_0(41289);
			this.panel_0.Size = new Size(228, 60);
			this.panel_0.TabIndex = 2;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(5, 10);
			this.radioButton_1.Name = Class521.smethod_0(41310);
			this.radioButton_1.Size = new Size(88, 19);
			this.radioButton_1.TabIndex = 0;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(41343);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Location = new Point(5, 36);
			this.radioButton_0.Name = Class521.smethod_0(41360);
			this.radioButton_0.Size = new Size(88, 19);
			this.radioButton_0.TabIndex = 1;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(41393);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.groupBox_5.Controls.Add(this.label_8);
			this.groupBox_5.Controls.Add(this.textBox_0);
			this.groupBox_5.Controls.Add(this.label_7);
			this.groupBox_5.Controls.Add(this.numericUpDown_0);
			this.groupBox_5.Controls.Add(this.checkBox_21);
			this.groupBox_5.Controls.Add(this.radioButton_4);
			this.groupBox_5.Controls.Add(this.radioButton_5);
			this.groupBox_5.Location = new Point(384, 128);
			this.groupBox_5.Name = Class521.smethod_0(41410);
			this.groupBox_5.Size = new Size(340, 132);
			this.groupBox_5.TabIndex = 3;
			this.groupBox_5.TabStop = false;
			this.groupBox_5.Text = Class521.smethod_0(41423);
			this.label_8.AutoSize = true;
			this.label_8.Location = new Point(253, 63);
			this.label_8.Name = Class521.smethod_0(5915);
			this.label_8.Size = new Size(22, 15);
			this.label_8.TabIndex = 20;
			this.label_8.Text = Class521.smethod_0(3554);
			this.textBox_0.Location = new Point(172, 58);
			this.textBox_0.Name = Class521.smethod_0(41440);
			this.textBox_0.Size = new Size(78, 25);
			this.textBox_0.TabIndex = 3;
			this.textBox_0.Text = Class521.smethod_0(41465);
			this.label_7.AutoSize = true;
			this.label_7.Location = new Point(256, 33);
			this.label_7.Name = Class521.smethod_0(41474);
			this.label_7.Size = new Size(15, 15);
			this.label_7.TabIndex = 18;
			this.label_7.Text = Class521.smethod_0(5356);
			this.numericUpDown_0.DecimalPlaces = 1;
			this.numericUpDown_0.Increment = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numericUpDown_0.Location = new Point(172, 29);
			this.numericUpDown_0.Minimum = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numericUpDown_0.Name = Class521.smethod_0(41495);
			this.numericUpDown_0.Size = new Size(78, 25);
			this.numericUpDown_0.TabIndex = 1;
			this.numericUpDown_0.Value = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.checkBox_21.AutoSize = true;
			this.checkBox_21.Location = new Point(22, 92);
			this.checkBox_21.Name = Class521.smethod_0(41524);
			this.checkBox_21.Size = new Size(179, 19);
			this.checkBox_21.TabIndex = 4;
			this.checkBox_21.Text = Class521.smethod_0(41557);
			this.checkBox_21.UseVisualStyleBackColor = true;
			this.radioButton_4.AutoSize = true;
			this.radioButton_4.Location = new Point(22, 60);
			this.radioButton_4.Name = Class521.smethod_0(41598);
			this.radioButton_4.Size = new Size(148, 19);
			this.radioButton_4.TabIndex = 2;
			this.radioButton_4.TabStop = true;
			this.radioButton_4.Text = Class521.smethod_0(41627);
			this.radioButton_4.UseVisualStyleBackColor = true;
			this.radioButton_5.AutoSize = true;
			this.radioButton_5.Location = new Point(22, 31);
			this.radioButton_5.Name = Class521.smethod_0(41660);
			this.radioButton_5.Size = new Size(148, 19);
			this.radioButton_5.TabIndex = 0;
			this.radioButton_5.TabStop = true;
			this.radioButton_5.Text = Class521.smethod_0(41689);
			this.radioButton_5.UseVisualStyleBackColor = true;
			this.groupBox_4.Controls.Add(this.checkBox_35);
			this.groupBox_4.Controls.Add(this.checkBox_22);
			this.groupBox_4.Location = new Point(22, 271);
			this.groupBox_4.Name = Class521.smethod_0(41722);
			this.groupBox_4.Size = new Size(340, 99);
			this.groupBox_4.TabIndex = 4;
			this.groupBox_4.TabStop = false;
			this.groupBox_4.Text = Class521.smethod_0(41743);
			this.checkBox_35.AutoSize = true;
			this.checkBox_35.Location = new Point(21, 58);
			this.checkBox_35.Name = Class521.smethod_0(41760);
			this.checkBox_35.Size = new Size(254, 19);
			this.checkBox_35.TabIndex = 4;
			this.checkBox_35.Text = Class521.smethod_0(41809);
			this.checkBox_35.UseVisualStyleBackColor = true;
			this.checkBox_22.AutoSize = true;
			this.checkBox_22.Location = new Point(21, 30);
			this.checkBox_22.Name = Class521.smethod_0(41870);
			this.checkBox_22.Size = new Size(254, 19);
			this.checkBox_22.TabIndex = 0;
			this.checkBox_22.Text = Class521.smethod_0(41915);
			this.checkBox_22.UseVisualStyleBackColor = true;
			this.groupBox_3.Controls.Add(this.groupBox_15);
			this.groupBox_3.Controls.Add(this.comboBox_3);
			this.groupBox_3.Controls.Add(this.comboBox_2);
			this.groupBox_3.Controls.Add(this.label_3);
			this.groupBox_3.Controls.Add(this.label_2);
			this.groupBox_3.Controls.Add(this.comboBox_1);
			this.groupBox_3.Controls.Add(this.label_1);
			this.groupBox_3.Controls.Add(this.comboBox_0);
			this.groupBox_3.Controls.Add(this.label_0);
			this.groupBox_3.Controls.Add(this.checkBox_9);
			this.groupBox_3.Controls.Add(this.checkBox_8);
			this.groupBox_3.Location = new Point(22, 16);
			this.groupBox_3.Name = Class521.smethod_0(21427);
			this.groupBox_3.Size = new Size(340, 244);
			this.groupBox_3.TabIndex = 1;
			this.groupBox_3.TabStop = false;
			this.groupBox_3.Text = Class521.smethod_0(41976);
			this.groupBox_15.Location = new Point(24, 157);
			this.groupBox_15.Name = Class521.smethod_0(41993);
			this.groupBox_15.Size = new Size(215, 2);
			this.groupBox_15.TabIndex = 18;
			this.groupBox_15.TabStop = false;
			this.comboBox_3.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_3.FormattingEnabled = true;
			this.comboBox_3.Items.AddRange(new object[]
			{
				Class521.smethod_0(42010),
				Class521.smethod_0(42027),
				Class521.smethod_0(42044)
			});
			this.comboBox_3.Location = new Point(135, 88);
			this.comboBox_3.Name = Class521.smethod_0(42057);
			this.comboBox_3.Size = new Size(104, 23);
			this.comboBox_3.TabIndex = 5;
			this.comboBox_2.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_2.FormattingEnabled = true;
			this.comboBox_2.Items.AddRange(new object[]
			{
				Class521.smethod_0(42098),
				Class521.smethod_0(42115)
			});
			this.comboBox_2.Location = new Point(135, 119);
			this.comboBox_2.Name = Class521.smethod_0(42128);
			this.comboBox_2.Size = new Size(104, 23);
			this.comboBox_2.TabIndex = 7;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(20, 92);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(112, 15);
			this.label_3.TabIndex = 4;
			this.label_3.Text = Class521.smethod_0(42165);
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(20, 123);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(112, 15);
			this.label_2.TabIndex = 6;
			this.label_2.Text = Class521.smethod_0(42194);
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Items.AddRange(new object[]
			{
				Class521.smethod_0(42223),
				Class521.smethod_0(42240)
			});
			this.comboBox_1.Location = new Point(135, 57);
			this.comboBox_1.Name = Class521.smethod_0(42253);
			this.comboBox_1.Size = new Size(104, 23);
			this.comboBox_1.TabIndex = 3;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(20, 61);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(112, 15);
			this.label_1.TabIndex = 2;
			this.label_1.Text = Class521.smethod_0(42294);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Items.AddRange(new object[]
			{
				Class521.smethod_0(42323),
				Class521.smethod_0(42336),
				Class521.smethod_0(42349)
			});
			this.comboBox_0.Location = new Point(135, 26);
			this.comboBox_0.Name = Class521.smethod_0(42362);
			this.comboBox_0.Size = new Size(104, 23);
			this.comboBox_0.TabIndex = 1;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(20, 30);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(112, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(42391);
			this.checkBox_9.AutoSize = true;
			this.checkBox_9.Location = new Point(24, 202);
			this.checkBox_9.Name = Class521.smethod_0(42420);
			this.checkBox_9.Size = new Size(203, 19);
			this.checkBox_9.TabIndex = 9;
			this.checkBox_9.Text = Class521.smethod_0(42445);
			this.checkBox_9.UseVisualStyleBackColor = true;
			this.checkBox_8.AutoSize = true;
			this.checkBox_8.Location = new Point(24, 174);
			this.checkBox_8.Name = Class521.smethod_0(42490);
			this.checkBox_8.Size = new Size(179, 19);
			this.checkBox_8.TabIndex = 8;
			this.checkBox_8.Text = Class521.smethod_0(42519);
			this.checkBox_8.UseVisualStyleBackColor = true;
			this.groupBox_2.Controls.Add(this.linkLabel_1);
			this.groupBox_2.Controls.Add(this.linkLabel_0);
			this.groupBox_2.Controls.Add(this.checkBox_6);
			this.groupBox_2.Controls.Add(this.checkBox_7);
			this.groupBox_2.Location = new Point(384, 16);
			this.groupBox_2.Name = Class521.smethod_0(42560);
			this.groupBox_2.Size = new Size(340, 100);
			this.groupBox_2.TabIndex = 2;
			this.groupBox_2.TabStop = false;
			this.groupBox_2.Text = Class521.smethod_0(37513);
			this.linkLabel_1.LinkArea = new LinkArea(4, 3);
			this.linkLabel_1.Location = new Point(116, 61);
			this.linkLabel_1.Name = Class521.smethod_0(42589);
			this.linkLabel_1.Size = new Size(152, 22);
			this.linkLabel_1.TabIndex = 3;
			this.linkLabel_1.TabStop = true;
			this.linkLabel_1.Text = Class521.smethod_0(42618);
			this.linkLabel_1.UseCompatibleTextRendering = true;
			this.linkLabel_0.LinkArea = new LinkArea(4, 3);
			this.linkLabel_0.Location = new Point(116, 35);
			this.linkLabel_0.Name = Class521.smethod_0(42651);
			this.linkLabel_0.Size = new Size(152, 23);
			this.linkLabel_0.TabIndex = 2;
			this.linkLabel_0.TabStop = true;
			this.linkLabel_0.Text = Class521.smethod_0(42618);
			this.linkLabel_0.UseCompatibleTextRendering = true;
			this.checkBox_6.AutoSize = true;
			this.checkBox_6.Location = new Point(22, 60);
			this.checkBox_6.Name = Class521.smethod_0(42680);
			this.checkBox_6.Size = new Size(89, 19);
			this.checkBox_6.TabIndex = 1;
			this.checkBox_6.Text = Class521.smethod_0(42701);
			this.checkBox_6.UseVisualStyleBackColor = true;
			this.checkBox_7.AutoSize = true;
			this.checkBox_7.Location = new Point(22, 33);
			this.checkBox_7.Name = Class521.smethod_0(42718);
			this.checkBox_7.Size = new Size(89, 19);
			this.checkBox_7.TabIndex = 0;
			this.checkBox_7.Text = Class521.smethod_0(42739);
			this.checkBox_7.UseVisualStyleBackColor = true;
			this.tabPage_2.BackColor = SystemColors.Control;
			this.tabPage_2.Controls.Add(this.label_6);
			this.tabPage_2.Controls.Add(this.button_3);
			this.tabPage_2.Controls.Add(this.button_2);
			this.tabPage_2.Controls.Add(this.pictureBox_0);
			this.tabPage_2.Controls.Add(this.groupBox_8);
			this.tabPage_2.Controls.Add(this.groupBox_7);
			this.tabPage_2.Controls.Add(this.groupBox_6);
			this.tabPage_2.Location = new Point(4, 25);
			this.tabPage_2.Name = Class521.smethod_0(42756);
			this.tabPage_2.Padding = new Padding(3);
			this.tabPage_2.Size = new Size(747, 397);
			this.tabPage_2.TabIndex = 2;
			this.tabPage_2.Text = Class521.smethod_0(42789);
			this.label_6.Location = new Point(61, 24);
			this.label_6.Name = Class521.smethod_0(42806);
			this.label_6.Size = new Size(522, 44);
			this.label_6.TabIndex = 25;
			this.label_6.Text = Class521.smethod_0(42831);
			this.button_3.Location = new Point(164, 302);
			this.button_3.Name = Class521.smethod_0(43065);
			this.button_3.Size = new Size(127, 32);
			this.button_3.TabIndex = 24;
			this.button_3.Text = Class521.smethod_0(43086);
			this.button_3.UseVisualStyleBackColor = true;
			this.button_2.ImageAlign = ContentAlignment.MiddleLeft;
			this.button_2.Location = new Point(26, 302);
			this.button_2.Name = Class521.smethod_0(43107);
			this.button_2.Size = new Size(127, 32);
			this.button_2.TabIndex = 23;
			this.button_2.Text = Class521.smethod_0(43128);
			this.button_2.UseVisualStyleBackColor = true;
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(39, 22);
			this.pictureBox_0.Name = Class521.smethod_0(43149);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 22;
			this.pictureBox_0.TabStop = false;
			this.groupBox_8.Controls.Add(this.comboBox_6);
			this.groupBox_8.Controls.Add(this.label_5);
			this.groupBox_8.Controls.Add(this.comboBox_5);
			this.groupBox_8.Controls.Add(this.radioButton_3);
			this.groupBox_8.Controls.Add(this.label_4);
			this.groupBox_8.Controls.Add(this.radioButton_2);
			this.groupBox_8.Location = new Point(314, 206);
			this.groupBox_8.Name = Class521.smethod_0(43166);
			this.groupBox_8.Size = new Size(274, 142);
			this.groupBox_8.TabIndex = 16;
			this.groupBox_8.TabStop = false;
			this.groupBox_8.Text = Class521.smethod_0(43199);
			this.comboBox_6.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_6.FormattingEnabled = true;
			this.comboBox_6.Items.AddRange(new object[]
			{
				Class521.smethod_0(43224),
				Class521.smethod_0(43241),
				Class521.smethod_0(43254)
			});
			this.comboBox_6.Location = new Point(156, 105);
			this.comboBox_6.Name = Class521.smethod_0(43271);
			this.comboBox_6.Size = new Size(96, 23);
			this.comboBox_6.TabIndex = 21;
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(35, 109);
			this.label_5.Name = Class521.smethod_0(5893);
			this.label_5.Size = new Size(112, 15);
			this.label_5.TabIndex = 22;
			this.label_5.Text = Class521.smethod_0(43312);
			this.comboBox_5.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_5.FormattingEnabled = true;
			this.comboBox_5.Items.AddRange(new object[]
			{
				Class521.smethod_0(43224),
				Class521.smethod_0(43241)
			});
			this.comboBox_5.Location = new Point(156, 48);
			this.comboBox_5.Name = Class521.smethod_0(43341);
			this.comboBox_5.Size = new Size(96, 23);
			this.comboBox_5.TabIndex = 19;
			this.radioButton_3.AutoSize = true;
			this.radioButton_3.Location = new Point(16, 83);
			this.radioButton_3.Name = Class521.smethod_0(43390);
			this.radioButton_3.Size = new Size(163, 19);
			this.radioButton_3.TabIndex = 1;
			this.radioButton_3.TabStop = true;
			this.radioButton_3.Text = Class521.smethod_0(43435);
			this.radioButton_3.UseVisualStyleBackColor = true;
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(35, 52);
			this.label_4.Name = Class521.smethod_0(43472);
			this.label_4.Size = new Size(112, 15);
			this.label_4.TabIndex = 20;
			this.label_4.Text = Class521.smethod_0(43505);
			this.radioButton_2.AutoSize = true;
			this.radioButton_2.Location = new Point(16, 26);
			this.radioButton_2.Name = Class521.smethod_0(43534);
			this.radioButton_2.Size = new Size(163, 19);
			this.radioButton_2.TabIndex = 0;
			this.radioButton_2.TabStop = true;
			this.radioButton_2.Text = Class521.smethod_0(43575);
			this.radioButton_2.UseVisualStyleBackColor = true;
			this.groupBox_7.Controls.Add(this.comboBox_4);
			this.groupBox_7.Controls.Add(this.checkBox_15);
			this.groupBox_7.Controls.Add(this.checkBox_16);
			this.groupBox_7.Controls.Add(this.checkBox_17);
			this.groupBox_7.Location = new Point(314, 78);
			this.groupBox_7.Name = Class521.smethod_0(43612);
			this.groupBox_7.Size = new Size(274, 115);
			this.groupBox_7.TabIndex = 3;
			this.groupBox_7.TabStop = false;
			this.groupBox_7.Text = Class521.smethod_0(43637);
			this.comboBox_4.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_4.FormattingEnabled = true;
			this.comboBox_4.Items.AddRange(new object[]
			{
				Class521.smethod_0(16377),
				Class521.smethod_0(37487),
				Class521.smethod_0(37500)
			});
			this.comboBox_4.Location = new Point(156, 79);
			this.comboBox_4.Name = Class521.smethod_0(43662);
			this.comboBox_4.Size = new Size(96, 23);
			this.comboBox_4.TabIndex = 19;
			this.checkBox_15.AutoSize = true;
			this.checkBox_15.Location = new Point(16, 81);
			this.checkBox_15.Name = Class521.smethod_0(43691);
			this.checkBox_15.Size = new Size(134, 19);
			this.checkBox_15.TabIndex = 17;
			this.checkBox_15.Text = Class521.smethod_0(43732);
			this.checkBox_15.UseVisualStyleBackColor = true;
			this.checkBox_16.AutoSize = true;
			this.checkBox_16.Location = new Point(16, 54);
			this.checkBox_16.Name = Class521.smethod_0(43761);
			this.checkBox_16.Size = new Size(104, 19);
			this.checkBox_16.TabIndex = 16;
			this.checkBox_16.Text = Class521.smethod_0(43790);
			this.checkBox_16.UseVisualStyleBackColor = true;
			this.checkBox_17.AutoSize = true;
			this.checkBox_17.Location = new Point(16, 27);
			this.checkBox_17.Name = Class521.smethod_0(43811);
			this.checkBox_17.Size = new Size(104, 19);
			this.checkBox_17.TabIndex = 15;
			this.checkBox_17.Text = Class521.smethod_0(43844);
			this.checkBox_17.UseVisualStyleBackColor = true;
			this.groupBox_6.Controls.Add(this.checkBox_18);
			this.groupBox_6.Controls.Add(this.checkBox_10);
			this.groupBox_6.Controls.Add(this.checkBox_11);
			this.groupBox_6.Controls.Add(this.checkBox_12);
			this.groupBox_6.Controls.Add(this.checkBox_13);
			this.groupBox_6.Controls.Add(this.checkBox_14);
			this.groupBox_6.Location = new Point(21, 78);
			this.groupBox_6.Name = Class521.smethod_0(43865);
			this.groupBox_6.Size = new Size(275, 206);
			this.groupBox_6.TabIndex = 2;
			this.groupBox_6.TabStop = false;
			this.groupBox_6.Text = Class521.smethod_0(43890);
			this.checkBox_18.AutoSize = true;
			this.checkBox_18.Location = new Point(16, 84);
			this.checkBox_18.Name = Class521.smethod_0(43907);
			this.checkBox_18.Size = new Size(89, 19);
			this.checkBox_18.TabIndex = 20;
			this.checkBox_18.Text = Class521.smethod_0(11175);
			this.checkBox_18.UseVisualStyleBackColor = true;
			this.checkBox_10.AutoSize = true;
			this.checkBox_10.Location = new Point(16, 165);
			this.checkBox_10.Name = Class521.smethod_0(43940);
			this.checkBox_10.Size = new Size(59, 19);
			this.checkBox_10.TabIndex = 19;
			this.checkBox_10.Text = Class521.smethod_0(11276);
			this.checkBox_10.UseVisualStyleBackColor = true;
			this.checkBox_11.AutoSize = true;
			this.checkBox_11.Location = new Point(16, 138);
			this.checkBox_11.Name = Class521.smethod_0(43969);
			this.checkBox_11.Size = new Size(119, 19);
			this.checkBox_11.TabIndex = 18;
			this.checkBox_11.Text = Class521.smethod_0(11242);
			this.checkBox_11.UseVisualStyleBackColor = true;
			this.checkBox_12.AutoSize = true;
			this.checkBox_12.Location = new Point(16, 111);
			this.checkBox_12.Name = Class521.smethod_0(43998);
			this.checkBox_12.Size = new Size(89, 19);
			this.checkBox_12.TabIndex = 16;
			this.checkBox_12.Text = Class521.smethod_0(8651);
			this.checkBox_12.UseVisualStyleBackColor = true;
			this.checkBox_13.AutoSize = true;
			this.checkBox_13.Location = new Point(16, 57);
			this.checkBox_13.Name = Class521.smethod_0(44023);
			this.checkBox_13.Size = new Size(134, 19);
			this.checkBox_13.TabIndex = 15;
			this.checkBox_13.Text = Class521.smethod_0(44056);
			this.checkBox_13.UseVisualStyleBackColor = true;
			this.checkBox_14.AutoSize = true;
			this.checkBox_14.Location = new Point(16, 30);
			this.checkBox_14.Name = Class521.smethod_0(44085);
			this.checkBox_14.Size = new Size(134, 19);
			this.checkBox_14.TabIndex = 14;
			this.checkBox_14.Text = Class521.smethod_0(11071);
			this.checkBox_14.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(779, 497);
			base.Controls.Add(this.tabControl_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(44110);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(11158);
			base.Load += this.SettingsForm_Load;
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			this.tabControl_0.ResumeLayout(false);
			this.tabPage_3.ResumeLayout(false);
			this.groupBox_14.ResumeLayout(false);
			this.groupBox_14.PerformLayout();
			((ISupportInitialize)this.pictureBox_2).EndInit();
			this.groupBox_13.ResumeLayout(false);
			this.groupBox_13.PerformLayout();
			this.groupBox_9.ResumeLayout(false);
			this.groupBox_9.PerformLayout();
			((ISupportInitialize)this.pictureBox_1).EndInit();
			this.panel_1.ResumeLayout(false);
			this.panel_1.PerformLayout();
			this.tabPage_0.ResumeLayout(false);
			this.groupBox_19.ResumeLayout(false);
			this.groupBox_19.PerformLayout();
			this.panel_3.ResumeLayout(false);
			this.panel_3.PerformLayout();
			this.groupBox_16.ResumeLayout(false);
			this.groupBox_16.PerformLayout();
			this.panel_2.ResumeLayout(false);
			this.panel_2.PerformLayout();
			this.groupBox_12.ResumeLayout(false);
			this.groupBox_12.PerformLayout();
			this.tabPage_1.ResumeLayout(false);
			this.groupBox_18.ResumeLayout(false);
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			this.groupBox_5.ResumeLayout(false);
			this.groupBox_5.PerformLayout();
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			this.groupBox_4.ResumeLayout(false);
			this.groupBox_4.PerformLayout();
			this.groupBox_3.ResumeLayout(false);
			this.groupBox_3.PerformLayout();
			this.groupBox_2.ResumeLayout(false);
			this.groupBox_2.PerformLayout();
			this.tabPage_2.ResumeLayout(false);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			this.groupBox_8.ResumeLayout(false);
			this.groupBox_8.PerformLayout();
			this.groupBox_7.ResumeLayout(false);
			this.groupBox_7.PerformLayout();
			this.groupBox_6.ResumeLayout(false);
			this.groupBox_6.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000834 RID: 2100
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000835 RID: 2101
		private StkSymbol stkSymbol_0;

		// Token: 0x04000836 RID: 2102
		private bool bool_0;

		// Token: 0x04000837 RID: 2103
		private bool bool_1;

		// Token: 0x04000838 RID: 2104
		private IContainer icontainer_0;

		// Token: 0x04000839 RID: 2105
		private Button button_0;

		// Token: 0x0400083A RID: 2106
		private Button button_1;

		// Token: 0x0400083B RID: 2107
		private CheckBox checkBox_0;

		// Token: 0x0400083C RID: 2108
		private CheckBox checkBox_1;

		// Token: 0x0400083D RID: 2109
		private CheckBox checkBox_2;

		// Token: 0x0400083E RID: 2110
		private CheckBox checkBox_3;

		// Token: 0x0400083F RID: 2111
		private CheckBox checkBox_4;

		// Token: 0x04000840 RID: 2112
		private GroupBox groupBox_0;

		// Token: 0x04000841 RID: 2113
		private GroupBox groupBox_1;

		// Token: 0x04000842 RID: 2114
		private CheckBox checkBox_5;

		// Token: 0x04000843 RID: 2115
		private TabControl tabControl_0;

		// Token: 0x04000844 RID: 2116
		private TabPage tabPage_0;

		// Token: 0x04000845 RID: 2117
		private TabPage tabPage_1;

		// Token: 0x04000846 RID: 2118
		private GroupBox groupBox_2;

		// Token: 0x04000847 RID: 2119
		private CheckBox checkBox_6;

		// Token: 0x04000848 RID: 2120
		private CheckBox checkBox_7;

		// Token: 0x04000849 RID: 2121
		private GroupBox groupBox_3;

		// Token: 0x0400084A RID: 2122
		private CheckBox checkBox_8;

		// Token: 0x0400084B RID: 2123
		private CheckBox checkBox_9;

		// Token: 0x0400084C RID: 2124
		private GroupBox groupBox_4;

		// Token: 0x0400084D RID: 2125
		private RadioButton radioButton_0;

		// Token: 0x0400084E RID: 2126
		private RadioButton radioButton_1;

		// Token: 0x0400084F RID: 2127
		private Label label_0;

		// Token: 0x04000850 RID: 2128
		private ComboBox comboBox_0;

		// Token: 0x04000851 RID: 2129
		private ComboBox comboBox_1;

		// Token: 0x04000852 RID: 2130
		private Label label_1;

		// Token: 0x04000853 RID: 2131
		private ComboBox comboBox_2;

		// Token: 0x04000854 RID: 2132
		private Label label_2;

		// Token: 0x04000855 RID: 2133
		private ComboBox comboBox_3;

		// Token: 0x04000856 RID: 2134
		private Label label_3;

		// Token: 0x04000857 RID: 2135
		private LinkLabel linkLabel_0;

		// Token: 0x04000858 RID: 2136
		private LinkLabel linkLabel_1;

		// Token: 0x04000859 RID: 2137
		private GroupBox groupBox_5;

		// Token: 0x0400085A RID: 2138
		private TabPage tabPage_2;

		// Token: 0x0400085B RID: 2139
		private GroupBox groupBox_6;

		// Token: 0x0400085C RID: 2140
		private CheckBox checkBox_10;

		// Token: 0x0400085D RID: 2141
		private CheckBox checkBox_11;

		// Token: 0x0400085E RID: 2142
		private CheckBox checkBox_12;

		// Token: 0x0400085F RID: 2143
		private CheckBox checkBox_13;

		// Token: 0x04000860 RID: 2144
		private CheckBox checkBox_14;

		// Token: 0x04000861 RID: 2145
		private GroupBox groupBox_7;

		// Token: 0x04000862 RID: 2146
		private CheckBox checkBox_15;

		// Token: 0x04000863 RID: 2147
		private CheckBox checkBox_16;

		// Token: 0x04000864 RID: 2148
		private CheckBox checkBox_17;

		// Token: 0x04000865 RID: 2149
		private ComboBox comboBox_4;

		// Token: 0x04000866 RID: 2150
		private ComboBox comboBox_5;

		// Token: 0x04000867 RID: 2151
		private GroupBox groupBox_8;

		// Token: 0x04000868 RID: 2152
		private RadioButton radioButton_2;

		// Token: 0x04000869 RID: 2153
		private RadioButton radioButton_3;

		// Token: 0x0400086A RID: 2154
		private Label label_4;

		// Token: 0x0400086B RID: 2155
		private PictureBox pictureBox_0;

		// Token: 0x0400086C RID: 2156
		private CheckBox checkBox_18;

		// Token: 0x0400086D RID: 2157
		private Button button_2;

		// Token: 0x0400086E RID: 2158
		private Button button_3;

		// Token: 0x0400086F RID: 2159
		private ComboBox comboBox_6;

		// Token: 0x04000870 RID: 2160
		private Label label_5;

		// Token: 0x04000871 RID: 2161
		private Label label_6;

		// Token: 0x04000872 RID: 2162
		private CheckBox checkBox_19;

		// Token: 0x04000873 RID: 2163
		private CheckBox checkBox_20;

		// Token: 0x04000874 RID: 2164
		private RadioButton radioButton_4;

		// Token: 0x04000875 RID: 2165
		private RadioButton radioButton_5;

		// Token: 0x04000876 RID: 2166
		private CheckBox checkBox_21;

		// Token: 0x04000877 RID: 2167
		private Label label_7;

		// Token: 0x04000878 RID: 2168
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000879 RID: 2169
		private TextBox textBox_0;

		// Token: 0x0400087A RID: 2170
		private Label label_8;

		// Token: 0x0400087B RID: 2171
		private CheckBox checkBox_22;

		// Token: 0x0400087C RID: 2172
		private Panel panel_0;

		// Token: 0x0400087D RID: 2173
		private TabPage tabPage_3;

		// Token: 0x0400087E RID: 2174
		private GroupBox groupBox_9;

		// Token: 0x0400087F RID: 2175
		private GroupBox groupBox_10;

		// Token: 0x04000880 RID: 2176
		private PictureBox pictureBox_1;

		// Token: 0x04000881 RID: 2177
		private ComboBox comboBox_7;

		// Token: 0x04000882 RID: 2178
		private GroupBox groupBox_11;

		// Token: 0x04000883 RID: 2179
		private Label label_9;

		// Token: 0x04000884 RID: 2180
		private CheckBox checkBox_23;

		// Token: 0x04000885 RID: 2181
		private Label label_10;

		// Token: 0x04000886 RID: 2182
		private CheckBox checkBox_24;

		// Token: 0x04000887 RID: 2183
		private ComboBox comboBox_8;

		// Token: 0x04000888 RID: 2184
		private CheckBox checkBox_25;

		// Token: 0x04000889 RID: 2185
		private CheckBox checkBox_26;

		// Token: 0x0400088A RID: 2186
		private Panel panel_1;

		// Token: 0x0400088B RID: 2187
		private RadioButton radioButton_6;

		// Token: 0x0400088C RID: 2188
		private RadioButton radioButton_7;

		// Token: 0x0400088D RID: 2189
		private CheckBox checkBox_27;

		// Token: 0x0400088E RID: 2190
		private CheckBox checkBox_28;

		// Token: 0x0400088F RID: 2191
		private GroupBox groupBox_12;

		// Token: 0x04000890 RID: 2192
		private CheckBox checkBox_29;

		// Token: 0x04000891 RID: 2193
		private CheckBox checkBox_30;

		// Token: 0x04000892 RID: 2194
		private CheckBox checkBox_31;

		// Token: 0x04000893 RID: 2195
		private GroupBox groupBox_13;

		// Token: 0x04000894 RID: 2196
		private Label label_11;

		// Token: 0x04000895 RID: 2197
		private ComboBox comboBox_9;

		// Token: 0x04000896 RID: 2198
		private CheckBox checkBox_32;

		// Token: 0x04000897 RID: 2199
		private ComboBox comboBox_10;

		// Token: 0x04000898 RID: 2200
		private Label label_12;

		// Token: 0x04000899 RID: 2201
		private ComboBox comboBox_11;

		// Token: 0x0400089A RID: 2202
		private Label label_13;

		// Token: 0x0400089B RID: 2203
		private GroupBox groupBox_14;

		// Token: 0x0400089C RID: 2204
		private GroupBox groupBox_15;

		// Token: 0x0400089D RID: 2205
		private PictureBox pictureBox_2;

		// Token: 0x0400089E RID: 2206
		private GroupBox groupBox_16;

		// Token: 0x0400089F RID: 2207
		private GroupBox groupBox_17;

		// Token: 0x040008A0 RID: 2208
		private Panel panel_2;

		// Token: 0x040008A1 RID: 2209
		private RadioButton radioButton_8;

		// Token: 0x040008A2 RID: 2210
		private RadioButton radioButton_9;

		// Token: 0x040008A3 RID: 2211
		private Label label_14;

		// Token: 0x040008A4 RID: 2212
		private CheckBox checkBox_33;

		// Token: 0x040008A5 RID: 2213
		private CheckBox checkBox_34;

		// Token: 0x040008A6 RID: 2214
		private Label label_15;

		// Token: 0x040008A7 RID: 2215
		private GroupBox groupBox_18;

		// Token: 0x040008A8 RID: 2216
		private CheckBox checkBox_35;

		// Token: 0x040008A9 RID: 2217
		private GroupBox groupBox_19;

		// Token: 0x040008AA RID: 2218
		private Label label_16;

		// Token: 0x040008AB RID: 2219
		private Panel panel_3;

		// Token: 0x040008AC RID: 2220
		private RadioButton radioButton_10;

		// Token: 0x040008AD RID: 2221
		private RadioButton radioButton_11;

		// Token: 0x040008AE RID: 2222
		private CheckBox checkBox_36;
	}
}
