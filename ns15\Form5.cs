﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using TEx;

namespace ns15
{
	// Token: 0x02000096 RID: 150
	internal sealed partial class Form5 : Form
	{
		// Token: 0x14000020 RID: 32
		// (add) Token: 0x060004F5 RID: 1269 RVA: 0x0002769C File Offset: 0x0002589C
		// (remove) Token: 0x060004F6 RID: 1270 RVA: 0x000276D4 File Offset: 0x000258D4
		public event EventHandler ResetAllDataConfirmed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060004F7 RID: 1271 RVA: 0x00004308 File Offset: 0x00002508
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, null);
			}
		}

		// Token: 0x060004F8 RID: 1272 RVA: 0x0002770C File Offset: 0x0002590C
		public Form5(string string_1)
		{
			this.method_3();
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_1();
			this.string_0 = string_1;
			this.BackColor = Color.White;
			this.button_1.Click += this.button_1_Click;
			this.radioButton_0.CheckedChanged += this.radioButton_0_CheckedChanged;
			this.radioButton_1.CheckedChanged += this.radioButton_1_CheckedChanged;
			this.radioButton_2.CheckedChanged += this.radioButton_2_CheckedChanged;
			this.radioButton_4.CheckedChanged += this.radioButton_4_CheckedChanged;
			this.radioButton_3.CheckedChanged += this.radioButton_3_CheckedChanged;
		}

		// Token: 0x060004F9 RID: 1273 RVA: 0x000277D8 File Offset: 0x000259D8
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font(Class521.smethod_0(7183), num);
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				if (control.Font.Size != num)
				{
					control.Font = new Font(Class521.smethod_0(7183), num);
				}
			}
		}

		// Token: 0x060004FA RID: 1274 RVA: 0x00027884 File Offset: 0x00025A84
		private void button_1_Click(object sender, EventArgs e)
		{
			if (MessageBox.Show(Class521.smethod_0(7692) + this.method_2() + Class521.smethod_0(7721), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				if (this.radioButton_0.Checked)
				{
					Base.UI.smethod_52();
				}
				else if (this.radioButton_1.Checked)
				{
					Base.UI.smethod_87();
				}
				else if (this.radioButton_2.Checked)
				{
					Base.UI.smethod_140();
				}
				else if (this.radioButton_4.Checked)
				{
					TApp.smethod_3(this.string_0);
				}
				else if (this.radioButton_3.Checked)
				{
					this.method_0();
				}
				base.Close();
			}
		}

		// Token: 0x060004FB RID: 1275 RVA: 0x00027938 File Offset: 0x00025B38
		private string method_2()
		{
			string text;
			foreach (object obj in this.panel_0.Controls)
			{
				RadioButton radioButton = (RadioButton)obj;
				if (radioButton.Checked)
				{
					text = radioButton.Text;
					goto IL_55;
				}
			}
			return string.Empty;
			IL_55:
			return text;
		}

		// Token: 0x060004FC RID: 1276 RVA: 0x0000431F File Offset: 0x0000251F
		private void radioButton_0_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_0.Checked)
			{
				this.labelX_0.Text = Class521.smethod_0(7743);
			}
		}

		// Token: 0x060004FD RID: 1277 RVA: 0x00004345 File Offset: 0x00002545
		private void radioButton_1_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_1.Checked)
			{
				this.labelX_0.Text = Class521.smethod_0(7885);
			}
		}

		// Token: 0x060004FE RID: 1278 RVA: 0x0000436B File Offset: 0x0000256B
		private void radioButton_2_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_2.Checked)
			{
				this.labelX_0.Text = Class521.smethod_0(8115);
			}
		}

		// Token: 0x060004FF RID: 1279 RVA: 0x00004391 File Offset: 0x00002591
		private void radioButton_4_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_4.Checked)
			{
				this.labelX_0.Text = Class521.smethod_0(8257);
			}
		}

		// Token: 0x06000500 RID: 1280 RVA: 0x000043B7 File Offset: 0x000025B7
		private void radioButton_3_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_3.Checked)
			{
				this.labelX_0.Text = Class521.smethod_0(8334);
			}
		}

		// Token: 0x06000501 RID: 1281 RVA: 0x000043DD File Offset: 0x000025DD
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000502 RID: 1282 RVA: 0x000279B4 File Offset: 0x00025BB4
		private void method_3()
		{
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.radioButton_2 = new RadioButton();
			this.radioButton_3 = new RadioButton();
			this.labelX_0 = new LabelX();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.panel_0 = new Panel();
			this.radioButton_4 = new RadioButton();
			this.panel_0.SuspendLayout();
			base.SuspendLayout();
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Checked = true;
			this.radioButton_0.Location = new Point(16, 15);
			this.radioButton_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioButton_0.Name = Class521.smethod_0(8584);
			this.radioButton_0.Size = new Size(88, 19);
			this.radioButton_0.TabIndex = 0;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(8613);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(119, 15);
			this.radioButton_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioButton_1.Name = Class521.smethod_0(8630);
			this.radioButton_1.Size = new Size(88, 19);
			this.radioButton_1.TabIndex = 1;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(8651);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.radioButton_2.AutoSize = true;
			this.radioButton_2.Location = new Point(222, 15);
			this.radioButton_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioButton_2.Name = Class521.smethod_0(8668);
			this.radioButton_2.Size = new Size(88, 19);
			this.radioButton_2.TabIndex = 2;
			this.radioButton_2.TabStop = true;
			this.radioButton_2.Text = Class521.smethod_0(8693);
			this.radioButton_2.UseVisualStyleBackColor = true;
			this.radioButton_3.AutoSize = true;
			this.radioButton_3.Location = new Point(428, 15);
			this.radioButton_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioButton_3.Name = Class521.smethod_0(8710);
			this.radioButton_3.Size = new Size(88, 19);
			this.radioButton_3.TabIndex = 4;
			this.radioButton_3.TabStop = true;
			this.radioButton_3.Text = Class521.smethod_0(8735);
			this.radioButton_3.UseVisualStyleBackColor = true;
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.ForeColor = Color.Black;
			this.labelX_0.Location = new Point(60, 140);
			this.labelX_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.labelX_0.Name = Class521.smethod_0(8752);
			this.labelX_0.PaddingLeft = 2;
			this.labelX_0.Size = new Size(549, 112);
			this.labelX_0.TabIndex = 3;
			this.labelX_0.Text = Class521.smethod_0(8769);
			this.labelX_0.TextLineAlignment = StringAlignment.Near;
			this.labelX_0.WordWrap = true;
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(468, 279);
			this.button_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(116, 33);
			this.button_0.TabIndex = 1;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(339, 279);
			this.button_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(116, 33);
			this.button_1.TabIndex = 0;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.panel_0.Controls.Add(this.radioButton_4);
			this.panel_0.Controls.Add(this.radioButton_3);
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Controls.Add(this.radioButton_2);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Location = new Point(42, 46);
			this.panel_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(547, 49);
			this.panel_0.TabIndex = 2;
			this.radioButton_4.AutoSize = true;
			this.radioButton_4.Location = new Point(325, 15);
			this.radioButton_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioButton_4.Name = Class521.smethod_0(8912);
			this.radioButton_4.Size = new Size(88, 19);
			this.radioButton_4.TabIndex = 3;
			this.radioButton_4.TabStop = true;
			this.radioButton_4.Text = Class521.smethod_0(8941);
			this.radioButton_4.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(617, 329);
			base.Controls.Add(this.labelX_0);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(8958);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(8975);
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000206 RID: 518
		private string string_0;

		// Token: 0x04000207 RID: 519
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000208 RID: 520
		private IContainer icontainer_0;

		// Token: 0x04000209 RID: 521
		private RadioButton radioButton_0;

		// Token: 0x0400020A RID: 522
		private RadioButton radioButton_1;

		// Token: 0x0400020B RID: 523
		private RadioButton radioButton_2;

		// Token: 0x0400020C RID: 524
		private RadioButton radioButton_3;

		// Token: 0x0400020D RID: 525
		private Button button_0;

		// Token: 0x0400020E RID: 526
		private Button button_1;

		// Token: 0x0400020F RID: 527
		private LabelX labelX_0;

		// Token: 0x04000210 RID: 528
		private Panel panel_0;

		// Token: 0x04000211 RID: 529
		private RadioButton radioButton_4;
	}
}
