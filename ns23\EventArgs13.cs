﻿using System;

namespace ns23
{
	// Token: 0x02000248 RID: 584
	internal sealed class EventArgs13 : EventArgs
	{
		// Token: 0x060018D0 RID: 6352 RVA: 0x0000A3DA File Offset: 0x000085DA
		public EventArgs13(int int_1, bool bool_1)
		{
			this.int_0 = int_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x17000426 RID: 1062
		// (get) Token: 0x060018D1 RID: 6353 RVA: 0x000AAFB8 File Offset: 0x000A91B8
		public int NewAcctID
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x17000427 RID: 1063
		// (get) Token: 0x060018D2 RID: 6354 RVA: 0x000AAFD0 File Offset: 0x000A91D0
		public bool IfSwitchToNewAcct
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x04000C67 RID: 3175
		private int int_0;

		// Token: 0x04000C68 RID: 3176
		private bool bool_0;
	}
}
