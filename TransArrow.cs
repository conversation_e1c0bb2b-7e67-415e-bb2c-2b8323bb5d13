﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns18;
using ns24;
using ns26;
using TEx.Chart;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200022E RID: 558
	internal sealed class TransArrow
	{
		// Token: 0x0600171F RID: 5919 RVA: 0x00002D25 File Offset: 0x00000F25
		public TransArrow()
		{
		}

		// Token: 0x06001720 RID: 5920 RVA: 0x000A0840 File Offset: 0x0009EA40
		public TransArrow(ChartCS chart, Transaction trans)
		{
			this.Chart = chart;
			this.Transaction = trans;
			DateTime? dateTime = this.Chart.HisDataPeriodSet.method_38(this.Transaction.CreateTime);
			if (dateTime != null)
			{
				this.nullable_0 = new DateTime?(dateTime.Value);
				this.periodType_0 = chart.HisDataPeriodSet.PeriodType;
				this.nullable_1 = chart.HisDataPeriodSet.PeriodUnits;
				this.method_0();
			}
			if ((Base.UI.Form.IfAlwaysShowTransNoteBox && Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox) || (Base.UI.Form.IfAlwaysShowTransNoteBox && !Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox && this.HasUserNotes))
			{
				this.ShowNoteBox = true;
			}
		}

		// Token: 0x170003C6 RID: 966
		// (get) Token: 0x06001721 RID: 5921 RVA: 0x000A0904 File Offset: 0x0009EB04
		// (set) Token: 0x06001722 RID: 5922 RVA: 0x000097C5 File Offset: 0x000079C5
		public ImageObj ArrowImgObj
		{
			get
			{
				return this.imageObj_0;
			}
			set
			{
				this.imageObj_0 = value;
			}
		}

		// Token: 0x170003C7 RID: 967
		// (get) Token: 0x06001723 RID: 5923 RVA: 0x000A091C File Offset: 0x0009EB1C
		// (set) Token: 0x06001724 RID: 5924 RVA: 0x000097D0 File Offset: 0x000079D0
		public Transaction Transaction
		{
			get
			{
				return this.transaction_0;
			}
			set
			{
				this.transaction_0 = value;
			}
		}

		// Token: 0x170003C8 RID: 968
		// (get) Token: 0x06001725 RID: 5925 RVA: 0x000A0934 File Offset: 0x0009EB34
		// (set) Token: 0x06001726 RID: 5926 RVA: 0x000097DB File Offset: 0x000079DB
		public ChartCS Chart
		{
			get
			{
				return this.chartCS_0;
			}
			set
			{
				this.chartCS_0 = value;
			}
		}

		// Token: 0x170003C9 RID: 969
		// (get) Token: 0x06001727 RID: 5927 RVA: 0x000A094C File Offset: 0x0009EB4C
		public DateTime? ItemEndDT
		{
			get
			{
				return this.nullable_0;
			}
		}

		// Token: 0x170003CA RID: 970
		// (get) Token: 0x06001728 RID: 5928 RVA: 0x000A0964 File Offset: 0x0009EB64
		// (set) Token: 0x06001729 RID: 5929 RVA: 0x000097E6 File Offset: 0x000079E6
		public bool LightColor
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				if (this.bool_0 != value)
				{
					this.ArrowImgObj.Image = this.method_5(value);
					this.bool_0 = value;
				}
			}
		}

		// Token: 0x170003CB RID: 971
		// (get) Token: 0x0600172A RID: 5930 RVA: 0x000A097C File Offset: 0x0009EB7C
		public Location Location
		{
			get
			{
				Location result;
				if (this.ArrowImgObj != null)
				{
					result = this.ArrowImgObj.Location;
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x170003CC RID: 972
		// (get) Token: 0x0600172B RID: 5931 RVA: 0x000A09A4 File Offset: 0x0009EBA4
		// (set) Token: 0x0600172C RID: 5932 RVA: 0x0000980C File Offset: 0x00007A0C
		public TextObj NoteBox
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x170003CD RID: 973
		// (get) Token: 0x0600172D RID: 5933 RVA: 0x000A09BC File Offset: 0x0009EBBC
		// (set) Token: 0x0600172E RID: 5934 RVA: 0x00009817 File Offset: 0x00007A17
		public bool ShowNoteBox
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				if (this.bool_1 != value)
				{
					if (value)
					{
						this.method_14();
					}
					else
					{
						this.method_21();
					}
					this.bool_1 = value;
				}
			}
		}

		// Token: 0x170003CE RID: 974
		// (get) Token: 0x0600172F RID: 5935 RVA: 0x000A09D4 File Offset: 0x0009EBD4
		// (set) Token: 0x06001730 RID: 5936 RVA: 0x0000983C File Offset: 0x00007A3C
		public List<LineObj> OCLines
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170003CF RID: 975
		// (get) Token: 0x06001731 RID: 5937 RVA: 0x000A09EC File Offset: 0x0009EBEC
		// (set) Token: 0x06001732 RID: 5938 RVA: 0x00009847 File Offset: 0x00007A47
		public bool ShowOCLines
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				if (this.bool_2 != value)
				{
					if (value)
					{
						this.method_35();
					}
					else
					{
						this.method_39();
					}
					this.bool_2 = value;
				}
			}
		}

		// Token: 0x170003D0 RID: 976
		// (get) Token: 0x06001733 RID: 5939 RVA: 0x000A0A04 File Offset: 0x0009EC04
		// (set) Token: 0x06001734 RID: 5940 RVA: 0x0000986C File Offset: 0x00007A6C
		public PeriodType ItemPeriodType
		{
			get
			{
				return this.periodType_0;
			}
			set
			{
				this.periodType_0 = value;
			}
		}

		// Token: 0x170003D1 RID: 977
		// (get) Token: 0x06001735 RID: 5941 RVA: 0x000A0A1C File Offset: 0x0009EC1C
		// (set) Token: 0x06001736 RID: 5942 RVA: 0x00009877 File Offset: 0x00007A77
		public int? ItemPeriodUnits
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x170003D2 RID: 978
		// (get) Token: 0x06001737 RID: 5943 RVA: 0x000A0A34 File Offset: 0x0009EC34
		// (set) Token: 0x06001738 RID: 5944 RVA: 0x00009882 File Offset: 0x00007A82
		public string Notes
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170003D3 RID: 979
		// (get) Token: 0x06001739 RID: 5945 RVA: 0x000A0A4C File Offset: 0x0009EC4C
		public bool HasUserNotes
		{
			get
			{
				return !string.IsNullOrEmpty(this.UserNote);
			}
		}

		// Token: 0x170003D4 RID: 980
		// (get) Token: 0x0600173A RID: 5946 RVA: 0x000A0A6C File Offset: 0x0009EC6C
		public string UserNote
		{
			get
			{
				return this.method_28(this.Transaction.Notes);
			}
		}

		// Token: 0x170003D5 RID: 981
		// (get) Token: 0x0600173B RID: 5947 RVA: 0x000A0A90 File Offset: 0x0009EC90
		public Color? NoteBoxFontColor
		{
			get
			{
				return this.method_29();
			}
		}

		// Token: 0x170003D6 RID: 982
		// (get) Token: 0x0600173C RID: 5948 RVA: 0x000A0AA8 File Offset: 0x0009ECA8
		public object NoteBoxFont
		{
			get
			{
				return this.method_31();
			}
		}

		// Token: 0x170003D7 RID: 983
		// (get) Token: 0x0600173D RID: 5949 RVA: 0x000A0AC0 File Offset: 0x0009ECC0
		// (set) Token: 0x0600173E RID: 5950 RVA: 0x0000988D File Offset: 0x00007A8D
		public double? RationedPrice
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x170003D8 RID: 984
		// (get) Token: 0x0600173F RID: 5951 RVA: 0x000A0AD8 File Offset: 0x0009ECD8
		public bool ShouldChkRationedPrice
		{
			get
			{
				return this.Chart.SymbDataSet.ShouldChkRationedPrice;
			}
		}

		// Token: 0x170003D9 RID: 985
		// (get) Token: 0x06001740 RID: 5952 RVA: 0x000A0AFC File Offset: 0x0009ECFC
		// (set) Token: 0x06001741 RID: 5953 RVA: 0x00009898 File Offset: 0x00007A98
		public TransArrowType Type
		{
			get
			{
				return this.transArrowType_0;
			}
			set
			{
				this.transArrowType_0 = value;
			}
		}

		// Token: 0x170003DA RID: 986
		// (get) Token: 0x06001742 RID: 5954 RVA: 0x000A0B14 File Offset: 0x0009ED14
		// (set) Token: 0x06001743 RID: 5955 RVA: 0x000098A3 File Offset: 0x00007AA3
		public bool? IsPtLeft { get; set; }

		// Token: 0x170003DB RID: 987
		// (get) Token: 0x06001744 RID: 5956 RVA: 0x000A0B2C File Offset: 0x0009ED2C
		// (set) Token: 0x06001745 RID: 5957 RVA: 0x000098AE File Offset: 0x00007AAE
		public bool IsPtUp { get; set; }

		// Token: 0x06001746 RID: 5958 RVA: 0x000098B9 File Offset: 0x00007AB9
		private void method_0()
		{
			this.method_1(this.Chart, this.Transaction);
		}

		// Token: 0x06001747 RID: 5959 RVA: 0x000A0B44 File Offset: 0x0009ED44
		private void method_1(ChartCS chartCS_1, Transaction transaction_1)
		{
			ImageObj imageObj = this.method_2(chartCS_1, transaction_1);
			if (imageObj != null)
			{
				this.ArrowImgObj = imageObj;
				this.Chart = chartCS_1;
				this.Chart.GraphPane.GraphObjList.Add(this.ArrowImgObj);
				this.Chart.TransArrowList.Add(this);
			}
		}

		// Token: 0x06001748 RID: 5960 RVA: 0x000A0B9C File Offset: 0x0009ED9C
		private ImageObj method_2(ChartCS chartCS_1, Transaction transaction_1)
		{
			Enum17 transType = (Enum17)transaction_1.TransType;
			bool? flag = this.method_3(chartCS_1, transaction_1);
			this.IsPtLeft = flag;
			Image image = this.method_7(transType, flag, false);
			Location location = this.method_13(image, flag);
			ImageObj result;
			if (location != null)
			{
				RectangleF rect = new RectangleF((float)location.X, (float)location.Y, (float)location.Width, (float)location.Height);
				ImageObj imageObj = new ImageObj(image, rect);
				imageObj = new ImageObj(image, rect);
				imageObj.Location.AlignH = location.AlignH;
				imageObj.Location.AlignV = location.AlignV;
				imageObj.IsClippedToChartRect = true;
				imageObj.Tag = TransArrow.string_1 + Class521.smethod_0(2712) + transaction_1.ID.ToString();
				imageObj.ZOrder = ZOrder.A_InFront;
				if (Math.Round(location.X) > (double)chartCS_1.ChtCtrl.MaxSticksPerChart)
				{
					imageObj.IsVisible = false;
				}
				else
				{
					imageObj.IsVisible = true;
				}
				result = imageObj;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001749 RID: 5961 RVA: 0x000A0CA8 File Offset: 0x0009EEA8
		private bool? method_3(ChartCS chartCS_1, Transaction transaction_1)
		{
			TransArrow.Class305 @class = new TransArrow.Class305();
			@class.transArrow_0 = this;
			@class.transaction_0 = transaction_1;
			bool? result = null;
			if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
			{
				Enum17 transType = (Enum17)@class.transaction_0.TransType;
				bool flag = false;
				if (this.Chart != null && chartCS_1.PeriodHisDataList != null && chartCS_1.PeriodHisDataList.Any<KeyValuePair<DateTime, HisData>>())
				{
					List<Transaction> list = this.method_36();
					int num;
					if (list != null && list.Any<Transaction>() && this.ItemEndDT != null)
					{
						TransArrow.Class306 class2 = new TransArrow.Class306();
						class2.class305_0 = @class;
						num = 6;
						int num2 = chartCS_1.PeriodHisDataList.IndexOfKey(this.ItemEndDT.Value);
						int num3 = num2 - 6;
						if (num3 < 0)
						{
							num3 = 0;
						}
						class2.dateTime_0 = chartCS_1.PeriodHisDataList.Keys[num3];
						int num4 = num2 + num;
						if (num4 >= chartCS_1.PeriodHisDataList.Count)
						{
							num4 = chartCS_1.PeriodHisDataList.Count - 1;
						}
						class2.dateTime_1 = chartCS_1.PeriodHisDataList.Keys[num4];
						if (this.Transaction.ClosedTransID != null)
						{
							if (list.Exists(new Predicate<Transaction>(class2.method_0)))
							{
								return new bool?(true);
							}
						}
						else if ((this.Transaction.TransType == 1 || this.Transaction.TransType == 3) && list.Exists(new Predicate<Transaction>(class2.method_1)))
						{
							return new bool?(false);
						}
					}
					num = 10;
					int count = chartCS_1.PeriodHisDataList.Count;
					if (count < 10)
					{
						num = count;
					}
					IEnumerable<HisData> source = chartCS_1.PeriodHisDataList.Values.Where(new Func<HisData, bool>(@class.method_0));
					List<HisData> source2 = source.Skip(Math.Max(0, source.Count<HisData>() - num)).ToList<HisData>();
					if (source2.Any<HisData>())
					{
						HisData hisData = source2.Last<HisData>();
						double num5 = source2.Sum(new Func<HisData, double>(TransArrow.<>c.<>9.method_0)) / (double)num;
						double num6 = hisData.Open + hisData.Close;
						if (num5 < num6)
						{
							flag = true;
						}
					}
				}
				if (transType != Enum17.const_1)
				{
					if (transType != Enum17.const_4)
					{
						if (transType != Enum17.const_3 && transType != Enum17.const_2)
						{
							goto IL_27C;
						}
						if (flag)
						{
							result = new bool?(false);
							goto IL_27C;
						}
						result = new bool?(true);
						goto IL_27C;
					}
				}
				if (flag)
				{
					result = new bool?(true);
				}
				else
				{
					result = new bool?(false);
				}
			}
			IL_27C:
			return result;
		}

		// Token: 0x0600174A RID: 5962 RVA: 0x000A0F38 File Offset: 0x0009F138
		private Image method_4()
		{
			return this.method_6((Enum17)this.Transaction.TransType, false);
		}

		// Token: 0x0600174B RID: 5963 RVA: 0x000A0F5C File Offset: 0x0009F15C
		private Image method_5(bool bool_4)
		{
			return this.method_7((Enum17)this.Transaction.TransType, this.IsPtLeft, bool_4);
		}

		// Token: 0x0600174C RID: 5964 RVA: 0x000A0F88 File Offset: 0x0009F188
		private Image method_6(Enum17 enum17_0, bool bool_4)
		{
			return this.method_7(enum17_0, new bool?(false), bool_4);
		}

		// Token: 0x0600174D RID: 5965 RVA: 0x000A0FA8 File Offset: 0x0009F1A8
		private Image method_7(Enum17 enum17_0, bool? nullable_4, bool bool_4)
		{
			TransArrowType transArrowType = Base.UI.Form.TransArrowType;
			return this.method_8(enum17_0, transArrowType, nullable_4, bool_4);
		}

		// Token: 0x0600174E RID: 5966 RVA: 0x000A0FD0 File Offset: 0x0009F1D0
		private Image method_8(Enum17 enum17_0, TransArrowType transArrowType_1, bool? nullable_4, bool bool_4)
		{
			Image result = null;
			if (transArrowType_1 != TransArrowType.BigOblique)
			{
				if (transArrowType_1 == TransArrowType.SmallUpDn)
				{
					switch (enum17_0)
					{
					case Enum17.const_1:
						result = (bool_4 ? Class375.OpenLongArrow_RedLt : Class375.OpenLongArrow_Red);
						break;
					case Enum17.const_2:
						result = (bool_4 ? Class375.CloseLongArrow_GreenLt : Class375.CloseLongArrow_Green);
						break;
					case Enum17.const_3:
						result = (bool_4 ? Class375.OpenShortArrow_GreenLt : Class375.OpenShortArrow_Green);
						break;
					case Enum17.const_4:
						result = (bool_4 ? Class375.CloseShortArrow_RedLt : Class375.CloseShortArrow_Red);
						break;
					}
				}
			}
			else
			{
				Color color = Color.FromArgb(250, 71, 17);
				Color color2 = Color.FromArgb(6, 205, 6);
				Color color3 = Color.FromArgb(254, 155, 125);
				Color color4 = Color.FromArgb(94, 253, 94);
				Color color_ = color;
				bool bool_5 = true;
				bool bool_6 = false;
				switch (enum17_0)
				{
				case Enum17.const_1:
					color_ = (bool_4 ? color3 : color);
					bool_5 = true;
					bool_6 = false;
					break;
				case Enum17.const_2:
					color_ = (bool_4 ? color4 : color2);
					bool_5 = false;
					bool_6 = true;
					break;
				case Enum17.const_3:
					color_ = (bool_4 ? color4 : color2);
					bool_5 = false;
					bool_6 = false;
					break;
				case Enum17.const_4:
					color_ = (bool_4 ? color3 : color);
					bool_5 = true;
					bool_6 = true;
					break;
				}
				result = this.method_9(32, 32, 2.0, color_, bool_5, nullable_4.Value, bool_6);
			}
			return result;
		}

		// Token: 0x0600174F RID: 5967 RVA: 0x000A113C File Offset: 0x0009F33C
		private Bitmap method_9(int int_0, int int_1, double double_0, Color color_0, bool bool_4, bool bool_5, bool bool_6)
		{
			Bitmap bitmap = new Bitmap(int_0, int_1, PixelFormat.Format16bppRgb555);
			bitmap.MakeTransparent(Color.Black);
			Graphics graphics = Graphics.FromImage(bitmap);
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			Pen pen = new Pen(color_0, (float)double_0);
			pen.StartCap = LineCap.Round;
			pen.EndCap = LineCap.Round;
			pen.LineJoin = LineJoin.Round;
			double num = Math.Sqrt((double)(int_0 * int_1));
			double num2 = num * 0.949999988079071;
			double num3 = num * 0.949999988079071;
			double num4 = Math.Cos(0.6981317007977318) * num2;
			double num5 = Math.Sin(0.6981317007977318) * num2;
			double num6 = Math.Cos(0.5235987755982988) * num3;
			double num7 = Math.Sin(0.5235987755982988) * num3;
			PointF[] array = new PointF[7];
			if (bool_4)
			{
				if (bool_5)
				{
					array[0] = new Point(int_0 - 1, int_1 - 1);
					array[1] = new Point((int)((double)int_0 - num4), (int)((double)int_1 - num5));
					array[2] = new Point((int)((double)int_0 - num6), (int)((double)int_1 - num7));
					array[3] = new Point(0, 0);
					array[4] = new Point((int)((double)int_0 - num7), (int)((double)int_1 - num6));
					array[5] = new Point((int)((double)int_0 - num5), (int)((double)int_1 - num4));
					array[6] = new Point(int_0 - 1, int_1 - 1);
				}
				else
				{
					array[0] = new Point(0, int_1 - 1);
					array[1] = new Point((int)num4, (int)((double)int_1 - num5));
					array[2] = new Point((int)num6, (int)((double)int_1 - num7));
					array[3] = new Point(int_0 - 1, 0);
					array[4] = new Point((int)num7, (int)((double)int_1 - num6));
					array[5] = new Point((int)num5, (int)((double)int_1 - num4));
					array[6] = new Point(0, int_1 - 1);
				}
			}
			else if (bool_5)
			{
				array[0] = new Point(int_0 - 1, 0);
				array[1] = new Point((int)((double)int_0 - num4), (int)num5);
				array[2] = new Point((int)((double)int_0 - num6), (int)num7);
				array[3] = new Point(0, int_1 - 1);
				array[4] = new Point((int)((double)int_0 - num7), (int)num6);
				array[5] = new Point((int)((double)int_0 - num5), (int)num4);
				array[6] = new Point(int_0 - 1, 0);
			}
			else
			{
				array[0] = new Point(0, 0);
				array[1] = new Point((int)num4, (int)num5);
				array[2] = new Point((int)num6, (int)num7);
				array[3] = new Point(int_0 - 1, int_1 - 1);
				array[4] = new Point((int)num7, (int)num6);
				array[5] = new Point((int)num5, (int)num4);
				array[6] = new Point(0, 0);
			}
			graphics.DrawLines(pen, array);
			if (!bool_6)
			{
				SolidBrush brush = new SolidBrush(color_0);
				graphics.FillPolygon(brush, array);
			}
			return bitmap;
		}

		// Token: 0x06001750 RID: 5968 RVA: 0x000A1500 File Offset: 0x0009F700
		public void method_10()
		{
			if (this.ArrowImgObj != null)
			{
				Location location = this.method_12();
				if (location != null && this.ArrowImgObj.Location != location)
				{
					this.ArrowImgObj.Location = location;
					int num = this.Chart.ChtCtrl.IndexOfLastItemShown + 1;
					if (num > this.Chart.ChtCtrl.MaxSticksPerChart)
					{
						num = this.Chart.ChtCtrl.MaxSticksPerChart;
					}
					double num2 = Math.Round(location.X);
					if (num2 >= 0.0 && num2 <= (double)num)
					{
						this.ArrowImgObj.IsVisible = true;
						this.method_11();
					}
					else
					{
						this.ArrowImgObj.IsVisible = false;
						this.method_21();
					}
				}
			}
		}

		// Token: 0x06001751 RID: 5969 RVA: 0x000A15C0 File Offset: 0x0009F7C0
		public void method_11()
		{
			if ((Base.UI.Form.IfAlwaysShowTransNoteBox && Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox) || (Base.UI.Form.IfAlwaysShowTransNoteBox && !Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox && this.HasUserNotes))
			{
				this.method_14();
			}
			else
			{
				this.method_21();
			}
		}

		// Token: 0x06001752 RID: 5970 RVA: 0x000A1618 File Offset: 0x0009F818
		private Location method_12()
		{
			return this.method_13(this.imageObj_0.Image, this.IsPtLeft);
		}

		// Token: 0x06001753 RID: 5971 RVA: 0x000A1640 File Offset: 0x0009F840
		private Location method_13(Image image_0, bool? nullable_4)
		{
			Location result = null;
			DateTime? dateTime = this.ItemEndDT;
			if (dateTime != null && this.periodType_0 == this.Chart.HisDataPeriodSet.PeriodType)
			{
				int? itemPeriodUnits = this.ItemPeriodUnits;
				int? periodUnits = this.Chart.HisDataPeriodSet.PeriodUnits;
				if (itemPeriodUnits.GetValueOrDefault() == periodUnits.GetValueOrDefault() & itemPeriodUnits != null == (periodUnits != null))
				{
					goto IL_B4;
				}
			}
			dateTime = this.Chart.HisDataPeriodSet.method_38(this.Transaction.CreateTime);
			this.nullable_0 = dateTime;
			this.ItemPeriodType = this.Chart.HisDataPeriodSet.PeriodType;
			this.ItemPeriodUnits = this.Chart.HisDataPeriodSet.PeriodUnits;
			IL_B4:
			if (dateTime != null)
			{
				int? num = this.Chart.method_184(dateTime.Value);
				if (num != null)
				{
					try
					{
						GraphPane graphPane = this.Chart.GraphPane;
						double num2 = (double)image_0.Width;
						double num3 = (double)image_0.Height;
						double num4 = (graphPane.XAxis.Scale.Max - graphPane.XAxis.Scale.Min) / (double)graphPane.Rect.Width * num2;
						double num5 = (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) / (double)graphPane.Rect.Height * num3;
						double num6;
						if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
						{
							num6 = (double)num.Value + 1.3;
						}
						else
						{
							num6 = (double)(num.Value + 1) - num4 / 2.0;
						}
						double num7 = Convert.ToDouble(this.Transaction.Price);
						if (this.ShouldChkRationedPrice)
						{
							if (this.RationedPrice == null)
							{
								this.Chart.method_219(this);
							}
							if (this.RationedPrice != null)
							{
								num7 = this.RationedPrice.Value;
							}
						}
						else if (this.Transaction is TranStock)
						{
							TranStock tranStock = this.Transaction as TranStock;
							if (tranStock != null)
							{
								if (tranStock.IsOpen)
								{
									num7 = Convert.ToDouble(tranStock.OriPrice);
								}
								else
								{
									num7 = Convert.ToDouble(tranStock.Price);
								}
							}
						}
						double num8 = num7;
						AlignV alignV = AlignV.Top;
						this.IsPtUp = true;
						Enum17 transType = (Enum17)this.Transaction.TransType;
						if (transType == Enum17.const_3 || transType == Enum17.const_2)
						{
							alignV = AlignV.Bottom;
							num8 += num5;
							this.IsPtUp = false;
						}
						AlignH alignH = AlignH.Center;
						if (nullable_4 != null)
						{
							alignH = AlignH.Left;
							if (!nullable_4.Value)
							{
								num6 -= num4 + 0.6;
							}
						}
						result = new Location(num6, num8, num4, num5, CoordType.AxisXYScale, alignH, alignV);
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
				}
			}
			return result;
		}

		// Token: 0x06001754 RID: 5972 RVA: 0x000A193C File Offset: 0x0009FB3C
		public void method_14()
		{
			if (this.Location == null)
			{
				Class184.smethod_0(new InvalidOperationException(Class521.smethod_0(59362)));
			}
			else
			{
				if (this.NoteBox == null)
				{
					this.NoteBox = this.method_15();
					this.Chart.GraphPane.GraphObjList.Add(this.NoteBox);
				}
				else
				{
					this.NoteBox.Location = this.method_23(this.NoteBox);
					this.method_17();
					this.method_19();
					if (!this.Chart.GraphPane.GraphObjList.Contains(this.NoteBox))
					{
						this.Chart.GraphPane.GraphObjList.Add(this.NoteBox);
					}
				}
				this.bool_1 = true;
			}
		}

		// Token: 0x06001755 RID: 5973 RVA: 0x000A1A00 File Offset: 0x0009FC00
		private TextObj method_15()
		{
			TextObj textObj;
			if (string.IsNullOrEmpty(this.Notes) && !this.HasUserNotes)
			{
				textObj = new TextObj();
				textObj.Text = this.method_16();
				textObj.FontSpec.Size = 12f * TApp.DpiScaleMulti;
				this.method_18(textObj);
			}
			else
			{
				textObj = this.method_27(this.Transaction.Notes);
			}
			textObj.ZOrder = ZOrder.A_InFront;
			textObj.FontSpec.StringAlignment = StringAlignment.Near;
			textObj.Tag = this.ArrowImgObj.Tag + Class521.smethod_0(3641);
			textObj.Location = this.method_23(textObj);
			this.method_20(textObj);
			return textObj;
		}

		// Token: 0x06001756 RID: 5974 RVA: 0x000A1AB4 File Offset: 0x0009FCB4
		private string method_16()
		{
			return string.Concat(new object[]
			{
				Base.Trading.smethod_149((Enum17)this.Transaction.TransType),
				this.Transaction.Units,
				this.Chart.Symbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734),
				(this.Transaction.Profit != null) ? (Class521.smethod_0(59395) + Utility.GetStringWithoutEndZero(this.Transaction.Profit)) : string.Empty
			});
		}

		// Token: 0x06001757 RID: 5975 RVA: 0x000098CF File Offset: 0x00007ACF
		private void method_17()
		{
			this.method_18(this.NoteBox);
		}

		// Token: 0x06001758 RID: 5976 RVA: 0x000A1B60 File Offset: 0x0009FD60
		private void method_18(TextObj textObj_1)
		{
			if (Base.UI.Form.IfTransNoteFillTransparent)
			{
				textObj_1.FontSpec.Fill.IsVisible = false;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic && textObj_1.FontSpec.FontColor == Color.Black)
				{
					textObj_1.FontSpec.FontColor = Color.White;
				}
				else if (Base.UI.Form.ChartTheme != ChartTheme.Classic && textObj_1.FontSpec.FontColor == Color.White)
				{
					textObj_1.FontSpec.FontColor = Color.Black;
				}
			}
			else
			{
				textObj_1.FontSpec.Fill.IsVisible = true;
				Color? color = this.method_29();
				if (color != null)
				{
					textObj_1.FontSpec.FontColor = color.Value;
				}
				else
				{
					textObj_1.FontSpec.FontColor = Color.Black;
				}
			}
		}

		// Token: 0x06001759 RID: 5977 RVA: 0x000098DF File Offset: 0x00007ADF
		private void method_19()
		{
			this.method_20(this.NoteBox);
		}

		// Token: 0x0600175A RID: 5978 RVA: 0x000098EF File Offset: 0x00007AEF
		private void method_20(TextObj textObj_1)
		{
			if (Base.UI.Form.IfHideTransNoteBorder)
			{
				textObj_1.FontSpec.Border.IsVisible = false;
			}
			else
			{
				textObj_1.FontSpec.Border.IsVisible = true;
			}
		}

		// Token: 0x0600175B RID: 5979 RVA: 0x00009923 File Offset: 0x00007B23
		public void method_21()
		{
			if (this.NoteBox != null)
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_41));
			}
			this.bool_1 = false;
		}

		// Token: 0x0600175C RID: 5980 RVA: 0x000A1C3C File Offset: 0x0009FE3C
		private void method_22()
		{
			Location location = this.method_23(this.NoteBox);
			this.NoteBox.Location = location;
		}

		// Token: 0x0600175D RID: 5981 RVA: 0x000A1C64 File Offset: 0x0009FE64
		private Location method_23(TextObj textObj_1)
		{
			Enum17 transType = (Enum17)this.Transaction.TransType;
			bool flag = true;
			if (transType == Enum17.const_1 || transType == Enum17.const_4)
			{
				flag = false;
			}
			double num;
			AlignV alignV;
			if (flag)
			{
				num = this.Location.Y;
				if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
				{
					alignV = AlignV.Top;
				}
				else
				{
					alignV = AlignV.Bottom;
				}
			}
			else
			{
				num = this.Location.Y - this.Location.Height;
				if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
				{
					alignV = AlignV.Bottom;
				}
				else
				{
					alignV = AlignV.Top;
				}
			}
			double num2;
			AlignH alignH;
			if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
			{
				if (this.IsPtLeft.Value)
				{
					num2 = this.Location.X2;
					alignH = AlignH.Left;
					if (num2 + this.Location.Width * 2.0 > this.Chart.GraphPane.XAxis.Scale.Max)
					{
						alignH = AlignH.Right;
						if (this.IsPtUp && alignV == AlignV.Bottom)
						{
							alignV = AlignV.Top;
						}
						else if (!this.IsPtUp && alignV == AlignV.Top)
						{
							alignV = AlignV.Bottom;
						}
					}
				}
				else
				{
					num2 = this.Location.X;
					alignH = AlignH.Right;
					if (num2 - this.Location.Width * 2.0 < this.Chart.GraphPane.XAxis.Scale.Min)
					{
						alignH = AlignH.Left;
						if (this.IsPtUp && alignV == AlignV.Bottom)
						{
							alignV = AlignV.Top;
						}
						else if (!this.IsPtUp && alignV == AlignV.Top)
						{
							alignV = AlignV.Bottom;
						}
					}
				}
			}
			else
			{
				bool flag2 = true;
				if (this.Location != null)
				{
					flag2 = (this.Location.X < (double)(this.Chart.MaxSticksPerChart / 2));
				}
				double num3 = this.Chart.GraphPane.YAxis.Scale.Max - this.Chart.GraphPane.YAxis.Scale.Min;
				if (this.Location != null)
				{
					double y = this.Location.Y;
					double min = this.Chart.GraphPane.YAxis.Scale.Min;
				}
				num2 = (flag2 ? (this.Location.X + 1.0) : this.Location.X);
				alignH = (flag2 ? AlignH.Left : AlignH.Right);
			}
			if (num2 > this.Chart.GraphPane.XAxis.Scale.Max)
			{
				num2 = this.Chart.GraphPane.XAxis.Scale.Max;
			}
			else if (num2 < this.Chart.GraphPane.XAxis.Scale.Min)
			{
				num2 = this.Chart.GraphPane.XAxis.Scale.Min;
			}
			if (alignV == AlignV.Top)
			{
				if (num - this.Location.Height * 1.5 < this.Chart.GraphPane.YAxis.Scale.Min)
				{
					alignV = AlignV.Bottom;
				}
			}
			else if (alignV == AlignV.Bottom && num + this.Location.Height * 1.5 > this.Chart.GraphPane.YAxis.Scale.Max)
			{
				alignV = AlignV.Top;
			}
			if (num > this.Chart.GraphPane.YAxis.Scale.Max)
			{
				num = this.Chart.GraphPane.YAxis.Scale.Max;
			}
			else if (num < this.Chart.GraphPane.YAxis.Scale.Min)
			{
				num = this.Chart.GraphPane.YAxis.Scale.Min;
			}
			return new Location(num2, num, CoordType.AxisXYScale, alignH, alignV);
		}

		// Token: 0x0600175E RID: 5982 RVA: 0x000A2010 File Offset: 0x000A0210
		private XElement method_24(XElement xelement_0, string string_2, Color? nullable_4, object object_0)
		{
			XElement xelement = xelement_0.Element(Class521.smethod_0(59412));
			if (xelement == null)
			{
				xelement = new XElement(Class521.smethod_0(59412), string_2);
				xelement_0.Add(xelement);
			}
			else
			{
				xelement.Value = string_2;
			}
			if (nullable_4 != null)
			{
				XElement xelement2 = xelement_0.Element(Class521.smethod_0(59421));
				if (xelement2 != null)
				{
					xelement2.Value = nullable_4.Value.Name;
				}
				else
				{
					xelement2 = new XElement(Class521.smethod_0(59421), nullable_4.Value.Name);
					xelement_0.Add(xelement2);
				}
			}
			if (object_0 != null)
			{
				Font font = object_0 as Font;
				XElement xelement3 = xelement_0.Element(Class521.smethod_0(49314));
				if (xelement3 == null)
				{
					xelement3 = new XElement(Class521.smethod_0(49314));
					xelement_0.Add(xelement3);
				}
				xelement3.SetAttributeValue(Class521.smethod_0(2972), font.Size);
				xelement3.SetAttributeValue(Class521.smethod_0(59430), Convert.ToInt32(font.Bold));
				xelement3.SetAttributeValue(Class521.smethod_0(59439), Convert.ToInt32(font.Italic));
				xelement3.SetAttributeValue(Class521.smethod_0(59452), Convert.ToInt32(font.Underline));
				xelement3.SetAttributeValue(Class521.smethod_0(59469), font.FontFamily.Name);
			}
			return xelement_0;
		}

		// Token: 0x0600175F RID: 5983 RVA: 0x000A21C4 File Offset: 0x000A03C4
		public void method_25(string string_2, Color? nullable_4, object object_0)
		{
			this.method_26();
			if (!string.IsNullOrEmpty(string_2))
			{
				XDocument xdocument = null;
				XElement xelement = null;
				string notes = this.Transaction.Notes;
				if (!string.IsNullOrEmpty(notes))
				{
					try
					{
						xdocument = XDocument.Parse(notes);
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
					if (xdocument != null)
					{
						try
						{
							xelement = xdocument.Element(Class521.smethod_0(59486));
						}
						catch (Exception exception_2)
						{
							Class184.smethod_0(exception_2);
						}
					}
				}
				if (xdocument == null)
				{
					xdocument = new XDocument();
					xelement = new XElement(Class521.smethod_0(59486));
					xdocument.Add(xelement);
				}
				else if (xelement == null)
				{
					xelement = new XElement(Class521.smethod_0(59486));
					xdocument.Add(xelement);
				}
				this.method_24(xelement, string_2, nullable_4, object_0);
				this.Transaction.Notes = xdocument.ToString();
				this.string_0 = string_2;
				this.NoteBox = this.method_15();
				this.Chart.GraphPane.GraphObjList.Add(this.NoteBox);
				if (object_0 != null)
				{
					Font font = object_0 as Font;
					this.NoteBox.FontSpec.Size = font.Size;
					this.NoteBox.FontSpec.IsBold = font.Bold;
					this.NoteBox.FontSpec.IsItalic = font.Italic;
					this.NoteBox.FontSpec.Family = font.FontFamily.Name;
					this.NoteBox.FontSpec.IsUnderline = font.Underline;
				}
				if (nullable_4 != null)
				{
					this.NoteBox.FontSpec.FontColor = nullable_4.Value;
				}
			}
		}

		// Token: 0x06001760 RID: 5984 RVA: 0x000A2378 File Offset: 0x000A0578
		private void method_26()
		{
			string notes = this.Transaction.Notes;
			if (!string.IsNullOrEmpty(notes))
			{
				try
				{
					XDocument xdocument = XDocument.Parse(notes);
					XElement xelement = xdocument.Element(Class521.smethod_0(59486)).Element(Class521.smethod_0(59412));
					if (xelement != null)
					{
						xelement.Remove();
						this.Transaction.Notes = xdocument.ToString();
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
			this.string_0 = string.Empty;
			this.method_21();
			this.NoteBox = null;
		}

		// Token: 0x06001761 RID: 5985 RVA: 0x000A2418 File Offset: 0x000A0618
		private TextObj method_27(string string_2)
		{
			TextObj result;
			if (string.IsNullOrEmpty(string_2))
			{
				result = null;
			}
			else
			{
				TextObj textObj = null;
				try
				{
					XElement xelement = XDocument.Parse(string_2).Element(Class521.smethod_0(59486));
					XElement xelement2 = xelement.Element(Class521.smethod_0(59412));
					if (xelement2 != null)
					{
						string value = xelement2.Value;
						textObj = new TextObj();
						textObj.Text = this.method_33(value);
						textObj.FontSpec.Size = 12f * TApp.DpiScaleMulti;
						this.string_0 = value;
						this.method_18(textObj);
						try
						{
							XElement xelement3 = xelement.Element(Class521.smethod_0(59421));
							if (xelement3 != null)
							{
								Color? color = new Color?(Color.FromName(xelement3.Value));
								if (color != null)
								{
									textObj.FontSpec.FontColor = color.Value;
								}
							}
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
						}
						try
						{
							XElement xelement4 = xelement.Element(Class521.smethod_0(49314));
							if (xelement4 != null)
							{
								XAttribute xattribute = xelement4.Attribute(Class521.smethod_0(2972));
								if (xattribute != null)
								{
									textObj.FontSpec.Size = Convert.ToSingle(xattribute.Value) * TApp.DpiScaleMulti;
								}
								XAttribute xattribute2 = xelement4.Attribute(Class521.smethod_0(59430));
								if (xattribute2 != null)
								{
									textObj.FontSpec.IsBold = Convert.ToBoolean(Convert.ToInt32(xattribute2.Value));
								}
								XAttribute xattribute3 = xelement4.Attribute(Class521.smethod_0(59439));
								if (xattribute3 != null)
								{
									textObj.FontSpec.IsItalic = Convert.ToBoolean(Convert.ToInt32(xattribute3.Value));
								}
								XAttribute xattribute4 = xelement4.Attribute(Class521.smethod_0(59452));
								if (xattribute4 != null)
								{
									textObj.FontSpec.IsUnderline = Convert.ToBoolean(Convert.ToInt32(xattribute4.Value));
								}
								XAttribute xattribute5 = xelement4.Attribute(Class521.smethod_0(59469));
								if (xattribute5 != null)
								{
									textObj.FontSpec.Family = xattribute5.Value;
								}
							}
						}
						catch (Exception exception_2)
						{
							Class184.smethod_0(exception_2);
						}
					}
				}
				catch (Exception exception_3)
				{
					Class184.smethod_0(exception_3);
				}
				result = textObj;
			}
			return result;
		}

		// Token: 0x06001762 RID: 5986 RVA: 0x000A2694 File Offset: 0x000A0894
		private string method_28(string string_2)
		{
			string result;
			if (string.IsNullOrEmpty(string_2))
			{
				result = null;
			}
			else
			{
				string value;
				try
				{
					XElement xelement = XDocument.Parse(string_2).Element(Class521.smethod_0(59486));
					if (xelement != null)
					{
						XElement xelement2 = xelement.Element(Class521.smethod_0(59412));
						if (xelement2 != null)
						{
							value = xelement2.Value;
							goto IL_5E;
						}
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				return string.Empty;
				IL_5E:
				result = value;
			}
			return result;
		}

		// Token: 0x06001763 RID: 5987 RVA: 0x000A2714 File Offset: 0x000A0914
		private Color? method_29()
		{
			return this.method_30(this.Transaction.Notes);
		}

		// Token: 0x06001764 RID: 5988 RVA: 0x000A2738 File Offset: 0x000A0938
		private Color? method_30(string string_2)
		{
			Color? result;
			if (string.IsNullOrEmpty(string_2))
			{
				Color? color = null;
				result = color;
			}
			else
			{
				Color? color;
				try
				{
					XElement xelement = XDocument.Parse(string_2).Element(Class521.smethod_0(59486));
					if (xelement != null)
					{
						XElement xelement2 = xelement.Element(Class521.smethod_0(59421));
						if (xelement2 != null)
						{
							color = new Color?(Color.FromName(xelement2.Value));
							goto IL_76;
						}
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				return null;
				IL_76:
				result = color;
			}
			return result;
		}

		// Token: 0x06001765 RID: 5989 RVA: 0x000A27D4 File Offset: 0x000A09D4
		private object method_31()
		{
			return this.method_32(this.Transaction.Notes);
		}

		// Token: 0x06001766 RID: 5990 RVA: 0x000A27F8 File Offset: 0x000A09F8
		private object method_32(string string_2)
		{
			object result;
			if (string.IsNullOrEmpty(string_2))
			{
				result = null;
			}
			else
			{
				object obj;
				try
				{
					XElement xelement = XDocument.Parse(string_2).Element(Class521.smethod_0(59486)).Element(Class521.smethod_0(49314));
					if (xelement != null)
					{
						float emSize = Convert.ToSingle(xelement.Attribute(Class521.smethod_0(2972)).Value);
						bool flag = Convert.ToBoolean(Convert.ToInt32(xelement.Attribute(Class521.smethod_0(59430)).Value));
						bool flag2 = Convert.ToBoolean(Convert.ToInt32(xelement.Attribute(Class521.smethod_0(59439)).Value));
						bool flag3 = Convert.ToBoolean(Convert.ToInt32(xelement.Attribute(Class521.smethod_0(59452)).Value));
						string value = xelement.Attribute(Class521.smethod_0(59469)).Value;
						FontStyle style = FontStyle.Regular;
						if (flag)
						{
							style = FontStyle.Bold;
						}
						else if (flag2)
						{
							style = FontStyle.Italic;
						}
						else if (flag3)
						{
							style = FontStyle.Underline;
						}
						obj = new Font(value, emSize, style);
						goto IL_124;
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				return null;
				IL_124:
				result = obj;
			}
			return result;
		}

		// Token: 0x06001767 RID: 5991 RVA: 0x000A2950 File Offset: 0x000A0B50
		private string method_33(string string_2)
		{
			int length = string_2.Length;
			string result;
			if (length <= 10)
			{
				result = string_2;
			}
			else
			{
				string text = Class521.smethod_0(1449);
				int num;
				if ((double)length < Math.Pow(10.0, 2.0) * 0.6)
				{
					num = 10;
				}
				else
				{
					num = Convert.ToInt32(Math.Round(Math.Sqrt((double)length / 0.6)));
					if (num > 20)
					{
						num = 20;
					}
				}
				for (int i = 0; i < length; i += num)
				{
					string str;
					if (i + num > length)
					{
						str = string_2.Substring(i, length - i);
					}
					else
					{
						str = string_2.Substring(i, num);
					}
					text = text.Insert(text.Length, str + Class521.smethod_0(59499));
				}
				result = text.TrimEnd(Class521.smethod_0(59499).ToCharArray());
			}
			return result;
		}

		// Token: 0x06001768 RID: 5992 RVA: 0x00009958 File Offset: 0x00007B58
		public void method_34()
		{
			this.LightColor = true;
			this.ShowNoteBox = true;
			this.ShowOCLines = true;
		}

		// Token: 0x06001769 RID: 5993 RVA: 0x000A2A30 File Offset: 0x000A0C30
		public void method_35()
		{
			List<Transaction> list = this.method_37(this.Transaction);
			if (list != null && list.Any<Transaction>())
			{
				Enum17 transType = (Enum17)this.Transaction.TransType;
				bool flag = true;
				if (transType == Enum17.const_1 || transType == Enum17.const_4)
				{
					flag = false;
				}
				this.list_0 = new List<LineObj>();
				using (List<Transaction>.Enumerator enumerator = list.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						TransArrow.Class307 @class = new TransArrow.Class307();
						@class.transaction_0 = enumerator.Current;
						try
						{
							TransArrow transArrow = this.Chart.TransArrowList.FirstOrDefault(new Func<TransArrow, bool>(@class.method_0));
							if (transArrow != null)
							{
								transArrow.LightColor = true;
								Color color_ = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black;
								double num = Convert.ToDouble(@class.transaction_0.Price);
								if (this.ShouldChkRationedPrice)
								{
									TranStock tranStock = @class.transaction_0 as TranStock;
									double double_;
									if (tranStock.OriPrice > 0m)
									{
										double_ = Convert.ToDouble(tranStock.OriPrice);
									}
									else
									{
										double_ = num;
									}
									DateTime createTime = @class.transaction_0.CreateTime;
									num = this.Chart.SymbDataSet.method_98(double_, createTime);
								}
								double double_2 = this.method_38(this);
								double double_3 = flag ? (this.Location.Y - this.Location.Height) : this.Location.Y;
								double double_4 = this.method_38(transArrow);
								double double_5 = num;
								LineObj lineObj = this.method_40(color_, double_2, double_3, double_4, double_5);
								lineObj.Line.Style = DashStyle.Solid;
								lineObj.ZOrder = ZOrder.D_BehindAxis;
								lineObj.Tag = Class521.smethod_0(59504);
								this.Chart.GraphPane.GraphObjList.Add(lineObj);
								this.list_0.Add(lineObj);
							}
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
						}
					}
				}
			}
		}

		// Token: 0x0600176A RID: 5994 RVA: 0x000A2C54 File Offset: 0x000A0E54
		private List<Transaction> method_36()
		{
			return this.method_37(this.Transaction);
		}

		// Token: 0x0600176B RID: 5995 RVA: 0x000A2C74 File Offset: 0x000A0E74
		private List<Transaction> method_37(Transaction transaction_1)
		{
			List<Transaction> result = null;
			List<Transaction> list = Base.Trading.Transactions.Where(new Func<Transaction, bool>(this.method_42)).ToList<Transaction>();
			if (list != null && list.Any<Transaction>())
			{
				Enum17 transType = (Enum17)transaction_1.TransType;
				if (transType != Enum17.const_1)
				{
					if (transType != Enum17.const_3)
					{
						result = list.Where(new Func<Transaction, bool>(this.method_44)).ToList<Transaction>();
						goto IL_6C;
					}
				}
				result = list.Where(new Func<Transaction, bool>(this.method_43)).ToList<Transaction>();
			}
			IL_6C:
			return result;
		}

		// Token: 0x0600176C RID: 5996 RVA: 0x000A2CF4 File Offset: 0x000A0EF4
		private double method_38(TransArrow transArrow_0)
		{
			GraphPane graphPane = transArrow_0.Chart.GraphPane;
			double num = (graphPane.XAxis.Scale.Max - graphPane.XAxis.Scale.Min) / (double)graphPane.Rect.Width * (double)transArrow_0.ArrowImgObj.Image.Width / 2.0;
			double result;
			if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
			{
				if (transArrow_0.IsPtLeft.Value)
				{
					result = transArrow_0.Location.X;
				}
				else
				{
					result = transArrow_0.Location.X + transArrow_0.Location.Width;
				}
			}
			else
			{
				result = transArrow_0.Location.X + num;
			}
			return result;
		}

		// Token: 0x0600176D RID: 5997 RVA: 0x000A2DB8 File Offset: 0x000A0FB8
		public void method_39()
		{
			if (this.OCLines != null && this.OCLines.Count > 0)
			{
				foreach (LineObj item in this.OCLines)
				{
					this.Chart.GraphPane.GraphObjList.Remove(item);
				}
				this.OCLines = null;
			}
		}

		// Token: 0x0600176E RID: 5998 RVA: 0x000A2E3C File Offset: 0x000A103C
		private LineObj method_40(Color color_0, double double_0, double double_1, double double_2, double double_3)
		{
			ChartCS chart = this.Chart;
			double num;
			double num2;
			if (double_2 >= chart.GraphPane.XAxis.Scale.Min && double_2 <= chart.GraphPane.XAxis.Scale.Max && double_3 >= chart.GraphPane.YAxis.Scale.Min && double_3 <= chart.GraphPane.YAxis.Scale.Max)
			{
				num = double_2;
				num2 = double_3;
			}
			else if (double_0 == double_2)
			{
				num = double_2;
				num2 = chart.GraphPane.YAxis.Scale.Max;
			}
			else if (double_1 != double_3)
			{
				if (double_3 > double_1)
				{
					num2 = chart.GraphPane.YAxis.Scale.Max;
				}
				else
				{
					num2 = chart.GraphPane.YAxis.Scale.Min;
				}
				num = (num2 - double_1) / (double_3 - double_1) * (double_2 - double_0) + double_0;
				if (num < chart.GraphPane.XAxis.Scale.Min)
				{
					num = chart.GraphPane.XAxis.Scale.Min;
					num2 = (num - double_0) / (double_2 - double_0) * (double_3 - double_1) + double_1;
				}
				else if (num > chart.GraphPane.XAxis.Scale.Max)
				{
					num = chart.GraphPane.XAxis.Scale.Max;
					num2 = (num - double_0) / (double_2 - double_0) * (double_3 - double_1) + double_1;
				}
			}
			else
			{
				num = chart.GraphPane.XAxis.Scale.Max;
				num2 = double_3;
			}
			return new LineObj(color_0, double_0, double_1, num, num2);
		}

		// Token: 0x06001770 RID: 6000 RVA: 0x000A2FD4 File Offset: 0x000A11D4
		[CompilerGenerated]
		private bool method_41(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is TextObj)
			{
				result = (graphObj_0.Tag == this.NoteBox.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001771 RID: 6001 RVA: 0x000A3004 File Offset: 0x000A1204
		[CompilerGenerated]
		private bool method_42(Transaction transaction_1)
		{
			return transaction_1.SymbolID == this.Chart.Symbol.ID;
		}

		// Token: 0x06001772 RID: 6002 RVA: 0x000A3030 File Offset: 0x000A1230
		[CompilerGenerated]
		private bool method_43(Transaction transaction_1)
		{
			int? closedTransID = transaction_1.ClosedTransID;
			int id = this.Transaction.ID;
			return closedTransID.GetValueOrDefault() == id & closedTransID != null;
		}

		// Token: 0x06001773 RID: 6003 RVA: 0x000A3068 File Offset: 0x000A1268
		[CompilerGenerated]
		private bool method_44(Transaction transaction_1)
		{
			int id = transaction_1.ID;
			int? closedTransID = this.Transaction.ClosedTransID;
			return id == closedTransID.GetValueOrDefault() & closedTransID != null;
		}

		// Token: 0x04000BD1 RID: 3025
		private ImageObj imageObj_0;

		// Token: 0x04000BD2 RID: 3026
		private Transaction transaction_0;

		// Token: 0x04000BD3 RID: 3027
		private ChartCS chartCS_0;

		// Token: 0x04000BD4 RID: 3028
		private DateTime? nullable_0;

		// Token: 0x04000BD5 RID: 3029
		private PeriodType periodType_0;

		// Token: 0x04000BD6 RID: 3030
		private int? nullable_1;

		// Token: 0x04000BD7 RID: 3031
		private bool bool_0;

		// Token: 0x04000BD8 RID: 3032
		private TextObj textObj_0;

		// Token: 0x04000BD9 RID: 3033
		private bool bool_1;

		// Token: 0x04000BDA RID: 3034
		private List<LineObj> list_0;

		// Token: 0x04000BDB RID: 3035
		private bool bool_2;

		// Token: 0x04000BDC RID: 3036
		private string string_0;

		// Token: 0x04000BDD RID: 3037
		public static readonly string string_1 = Class521.smethod_0(59513);

		// Token: 0x04000BDE RID: 3038
		private double? nullable_2;

		// Token: 0x04000BDF RID: 3039
		private TransArrowType transArrowType_0;

		// Token: 0x04000BE0 RID: 3040
		[CompilerGenerated]
		private bool? nullable_3;

		// Token: 0x04000BE1 RID: 3041
		[CompilerGenerated]
		private bool bool_3;

		// Token: 0x0200022F RID: 559
		[CompilerGenerated]
		private sealed class Class305
		{
			// Token: 0x06001775 RID: 6005 RVA: 0x000A309C File Offset: 0x000A129C
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date <= this.transaction_0.CreateTime;
			}

			// Token: 0x04000BE2 RID: 3042
			public TransArrow transArrow_0;

			// Token: 0x04000BE3 RID: 3043
			public Transaction transaction_0;
		}

		// Token: 0x02000230 RID: 560
		[CompilerGenerated]
		private sealed class Class306
		{
			// Token: 0x06001777 RID: 6007 RVA: 0x000A30C4 File Offset: 0x000A12C4
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.CreateTime >= this.dateTime_0)
				{
					result = (transaction_0.CreateTime < this.class305_0.transArrow_0.Transaction.CreateTime);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001778 RID: 6008 RVA: 0x000A310C File Offset: 0x000A130C
			internal bool method_1(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.CreateTime <= this.dateTime_1)
				{
					result = (transaction_0.CreateTime > this.class305_0.transArrow_0.Transaction.CreateTime);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000BE4 RID: 3044
			public DateTime dateTime_0;

			// Token: 0x04000BE5 RID: 3045
			public DateTime dateTime_1;

			// Token: 0x04000BE6 RID: 3046
			public TransArrow.Class305 class305_0;
		}

		// Token: 0x02000232 RID: 562
		[CompilerGenerated]
		private sealed class Class307
		{
			// Token: 0x0600177D RID: 6013 RVA: 0x000A3174 File Offset: 0x000A1374
			internal bool method_0(TransArrow transArrow_0)
			{
				return transArrow_0.Transaction.ID == this.transaction_0.ID;
			}

			// Token: 0x04000BE9 RID: 3049
			public Transaction transaction_0;
		}
	}
}
