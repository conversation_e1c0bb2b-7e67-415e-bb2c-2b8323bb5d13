﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns17;
using ns18;
using TEx;
using TEx.Inds;

namespace ns11
{
	// Token: 0x02000335 RID: 821
	internal sealed class Control13 : UserControl
	{
		// Token: 0x06002299 RID: 8857 RVA: 0x000F4554 File Offset: 0x000F2754
		public Control13()
		{
			this.method_4();
			Base.UI.smethod_55(this.dataGridView_0);
			this.dataGridView_0.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView_0.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
			this.dataGridView_0.SelectionMode = DataGridViewSelectionMode.CellSelect;
			this.dataGridView_0.AutoGenerateColumns = true;
			this.dataGridView_0.Enabled = true;
			this.dataGridView_0.EditingControlShowing += this.dataGridView_0_EditingControlShowing;
			this.dataGridView_0.DataError += this.dataGridView_0_DataError;
			this.dataGridView_0.BackgroundColor = Color.White;
			this.dataGridView_0.Leave += this.dataGridView_0_Leave;
			this.dataGridView_0.Refresh();
			this.dataGridView_0.Invalidated += this.dataGridView_0_Invalidated;
			this.dataGridView_0.AllowUserToAddRows = false;
			int index = this.dataGridView_0.Columns.Add(Class521.smethod_0(1858), Class521.smethod_0(1472));
			this.dataGridView_0.Columns[index].ValueType = typeof(string);
			index = this.dataGridView_0.Columns.Add(Class521.smethod_0(99576), Class521.smethod_0(103508));
			this.dataGridView_0.Columns[index].ValueType = typeof(double);
			index = this.dataGridView_0.Columns.Add(Class521.smethod_0(99571), Class521.smethod_0(103521));
			this.dataGridView_0.Columns[index].ValueType = typeof(double);
			index = this.dataGridView_0.Columns.Add(Class521.smethod_0(48861), Class521.smethod_0(103534));
			this.dataGridView_0.Columns[index].ValueType = typeof(double);
			for (int i = 0; i < 16; i++)
			{
				this.dataGridView_0.Rows.Add();
			}
			for (int j = 0; j < this.dataGridView_0.ColumnCount; j++)
			{
				this.dataGridView_0.Columns[j].SortMode = DataGridViewColumnSortMode.NotSortable;
			}
		}

		// Token: 0x0600229A RID: 8858 RVA: 0x000F47A8 File Offset: 0x000F29A8
		private void dataGridView_0_DataError(object sender, DataGridViewDataErrorEventArgs e)
		{
			string text = Class521.smethod_0(1449);
			if (e.ColumnIndex != 0)
			{
				text = Class521.smethod_0(103547);
			}
			else
			{
				text = e.Exception.Message;
			}
			MessageBox.Show(text, Class521.smethod_0(17781));
			e.Cancel = true;
		}

		// Token: 0x140000A5 RID: 165
		// (add) Token: 0x0600229B RID: 8859 RVA: 0x000F47FC File Offset: 0x000F29FC
		// (remove) Token: 0x0600229C RID: 8860 RVA: 0x000F4834 File Offset: 0x000F2A34
		public event EventHandler OnEndEdit
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600229D RID: 8861 RVA: 0x000F486C File Offset: 0x000F2A6C
		private void dataGridView_0_Leave(object sender, EventArgs e)
		{
			if (this.dataGridView_0.IsCurrentCellInEditMode)
			{
				try
				{
					this.dataGridView_0.CurrentCell = null;
				}
				catch (Exception)
				{
				}
			}
			this.method_0();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x0600229E RID: 8862 RVA: 0x000F48C4 File Offset: 0x000F2AC4
		private void dataGridView_0_EditingControlShowing(object sender, DataGridViewEditingControlShowingEventArgs e)
		{
			DataGridView dataGridView = (DataGridView)sender;
			if (e.Control is DataGridViewTextBoxEditingControl)
			{
				DataGridViewTextBoxEditingControl dataGridViewTextBoxEditingControl = (DataGridViewTextBoxEditingControl)e.Control;
				if (dataGridView.CurrentCell.OwningColumn.Name == Class521.smethod_0(1858))
				{
					dataGridViewTextBoxEditingControl.CharacterCasing = CharacterCasing.Upper;
				}
				else
				{
					dataGridViewTextBoxEditingControl.CharacterCasing = CharacterCasing.Normal;
				}
			}
		}

		// Token: 0x170005EB RID: 1515
		// (get) Token: 0x0600229F RID: 8863 RVA: 0x000F4924 File Offset: 0x000F2B24
		public List<UserDefineParam> ShowList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x060022A0 RID: 8864 RVA: 0x000F493C File Offset: 0x000F2B3C
		private void method_0()
		{
			this.list_0 = new List<UserDefineParam>();
			for (int i = 0; i < this.dataGridView_0.RowCount; i++)
			{
				UserDefineParam userDefineParam = this.method_1(i);
				if (userDefineParam != null)
				{
					this.list_0.Add(userDefineParam);
				}
			}
		}

		// Token: 0x060022A1 RID: 8865 RVA: 0x000F4984 File Offset: 0x000F2B84
		private UserDefineParam method_1(int int_1)
		{
			UserDefineParam result;
			if (this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(1858)].Value == null)
			{
				result = null;
			}
			else
			{
				string name = this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(1858)].Value as string;
				object value = this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(99576)].Value;
				if (!(value is double))
				{
					this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(99576)].Selected = true;
					MessageBox.Show(Class521.smethod_0(103580) + (int_1 + 1).ToString() + Class521.smethod_0(103585), Class521.smethod_0(17781));
					result = null;
				}
				else
				{
					double min = (double)value;
					value = this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(99571)].Value;
					if (!(value is double))
					{
						this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(99571)].Selected = true;
						MessageBox.Show(Class521.smethod_0(103580) + (int_1 + 1).ToString() + Class521.smethod_0(103618), Class521.smethod_0(17781));
						result = null;
					}
					else
					{
						double max = (double)value;
						value = this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(48861)].Value;
						if (!(value is double))
						{
							this.dataGridView_0.Rows[int_1].Cells[Class521.smethod_0(48861)].Selected = true;
							MessageBox.Show(Class521.smethod_0(103580) + (int_1 + 1).ToString() + Class521.smethod_0(103651), Class521.smethod_0(17781));
							result = null;
						}
						else
						{
							double value2 = (double)value;
							result = new UserDefineParam(name, max, min, value2, 1.0);
						}
					}
				}
			}
			return result;
		}

		// Token: 0x170005EC RID: 1516
		// (get) Token: 0x060022A2 RID: 8866 RVA: 0x000F4C14 File Offset: 0x000F2E14
		// (set) Token: 0x060022A3 RID: 8867 RVA: 0x0000DB7B File Offset: 0x0000BD7B
		private Enum25 DoType { get; set; }

		// Token: 0x060022A4 RID: 8868 RVA: 0x000F4C2C File Offset: 0x000F2E2C
		public void method_2(List<UserDefineParam> list_1, Enum25 enum25_1, Control13.Enum27 enum27_1)
		{
			this.dataGridView_0.Rows.Clear();
			if (enum27_1 == Control13.Enum27.const_1)
			{
				if (list_1 != null)
				{
					for (int i = 0; i < list_1.Count; i++)
					{
						this.dataGridView_0.Rows.Add();
					}
				}
				if (this.dataGridView_0.ColumnCount >= 4)
				{
					this.dataGridView_0.Columns[3].HeaderText = Class521.smethod_0(103534);
				}
			}
			else if (enum27_1 == Control13.Enum27.const_0)
			{
				for (int j = 0; j < 16; j++)
				{
					this.dataGridView_0.Rows.Add();
				}
				if (this.dataGridView_0.ColumnCount >= 4)
				{
					this.dataGridView_0.Columns[3].HeaderText = Class521.smethod_0(103680);
				}
			}
			this.enum27_0 = enum27_1;
			if (list_1 != null && list_1.Any<UserDefineParam>())
			{
				for (int k = 0; k < list_1.Count; k++)
				{
					if (k >= 16)
					{
						break;
					}
					this.dataGridView_0.Rows[k].Cells[Class521.smethod_0(1858)].Value = list_1[k].Name;
					this.dataGridView_0.Rows[k].Cells[Class521.smethod_0(99576)].Value = list_1[k].Min;
					this.dataGridView_0.Rows[k].Cells[Class521.smethod_0(99571)].Value = list_1[k].Max;
					this.dataGridView_0.Rows[k].Cells[Class521.smethod_0(48861)].Value = list_1[k].Value;
				}
			}
			else if (list_1 != null && !list_1.Any<UserDefineParam>())
			{
				this.dataGridView_0.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCells;
			}
			this.DoType = enum25_1;
			this.method_3();
			this.list_0 = list_1;
		}

		// Token: 0x060022A5 RID: 8869 RVA: 0x000F4E40 File Offset: 0x000F3040
		private void method_3()
		{
			for (int i = 0; i < this.dataGridView_0.Columns.Count; i++)
			{
				DataGridViewColumn dataGridViewColumn = this.dataGridView_0.Columns[i];
				if (dataGridViewColumn.Name == Class521.smethod_0(1858))
				{
					if (this.dataGridView_0.RowCount > 0 && this.enum27_0 == Control13.Enum27.const_0)
					{
						this.dataGridView_0.Rows[0].Cells[dataGridViewColumn.Name].Selected = true;
					}
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else if (dataGridViewColumn.Name == Class521.smethod_0(48861))
				{
					if (this.dataGridView_0.RowCount > 0 && this.enum27_0 == Control13.Enum27.const_1)
					{
						this.dataGridView_0.Rows[0].Cells[dataGridViewColumn.Name].Selected = true;
					}
				}
				else if (dataGridViewColumn.Name == Class521.smethod_0(99571))
				{
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else if (dataGridViewColumn.Name == Class521.smethod_0(99576))
				{
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else
				{
					if (!(dataGridViewColumn.Name == Class521.smethod_0(99581)))
					{
						throw new Exception(string.Format(Class521.smethod_0(103693), dataGridViewColumn.Name));
					}
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
					}
					dataGridViewColumn.Visible = false;
				}
			}
		}

		// Token: 0x060022A6 RID: 8870 RVA: 0x000041B9 File Offset: 0x000023B9
		private void dataGridView_0_Invalidated(object sender, InvalidateEventArgs e)
		{
		}

		// Token: 0x060022A7 RID: 8871 RVA: 0x0000DB86 File Offset: 0x0000BD86
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060022A8 RID: 8872 RVA: 0x000F5024 File Offset: 0x000F3224
		private void method_4()
		{
			this.dataGridView_0 = new DataGridView();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			base.SuspendLayout();
			this.dataGridView_0.BorderStyle = BorderStyle.None;
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.Dock = DockStyle.Fill;
			this.dataGridView_0.GridColor = SystemColors.ControlLight;
			this.dataGridView_0.Location = new Point(0, 0);
			this.dataGridView_0.Margin = new Padding(3, 2, 3, 2);
			this.dataGridView_0.Name = Class521.smethod_0(95285);
			this.dataGridView_0.RowTemplate.Height = 20;
			this.dataGridView_0.Size = new Size(385, 220);
			this.dataGridView_0.TabIndex = 0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.dataGridView_0);
			base.Margin = new Padding(3, 2, 3, 2);
			base.Name = Class521.smethod_0(103734);
			base.Size = new Size(385, 220);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x040010CC RID: 4300
		private const int int_0 = 16;

		// Token: 0x040010CD RID: 4301
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040010CE RID: 4302
		private List<UserDefineParam> list_0 = new List<UserDefineParam>();

		// Token: 0x040010CF RID: 4303
		[CompilerGenerated]
		private Enum25 enum25_0;

		// Token: 0x040010D0 RID: 4304
		private Control13.Enum27 enum27_0;

		// Token: 0x040010D1 RID: 4305
		private IContainer icontainer_0;

		// Token: 0x040010D2 RID: 4306
		private DataGridView dataGridView_0;

		// Token: 0x02000336 RID: 822
		public enum Enum27
		{
			// Token: 0x040010D4 RID: 4308
			const_0,
			// Token: 0x040010D5 RID: 4309
			const_1
		}
	}
}
