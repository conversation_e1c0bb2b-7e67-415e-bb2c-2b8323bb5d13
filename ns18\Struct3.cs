﻿using System;

namespace ns18
{
	// Token: 0x020001C8 RID: 456
	internal struct Struct3
	{
		// Token: 0x060011E4 RID: 4580 RVA: 0x000077DF File Offset: 0x000059DF
		public Struct3(int int_1, DateTime dateTime_1, string string_1, bool bool_3, bool bool_4, bool bool_5)
		{
			this.int_0 = int_1;
			this.string_0 = string_1;
			this.bool_0 = bool_3;
			this.bool_1 = bool_4;
			this.bool_2 = bool_5;
			this.dateTime_0 = dateTime_1;
		}

		// Token: 0x060011E5 RID: 4581 RVA: 0x00007810 File Offset: 0x00005A10
		public Struct3(int int_1, DateTime dateTime_1, string string_1, bool bool_3, bool bool_4)
		{
			this = new Struct3(int_1, dateTime_1, string_1, bool_3, bool_4, false);
		}

		// Token: 0x060011E6 RID: 4582 RVA: 0x00007820 File Offset: 0x00005A20
		public Struct3(int int_1, DateTime dateTime_1, string string_1, bool bool_3)
		{
			this = new Struct3(int_1, dateTime_1, string_1, bool_3, true, false);
		}

		// Token: 0x0400094B RID: 2379
		public int int_0;

		// Token: 0x0400094C RID: 2380
		public string string_0;

		// Token: 0x0400094D RID: 2381
		public bool bool_0;

		// Token: 0x0400094E RID: 2382
		public bool bool_1;

		// Token: 0x0400094F RID: 2383
		public bool bool_2;

		// Token: 0x04000950 RID: 2384
		public DateTime dateTime_0;
	}
}
