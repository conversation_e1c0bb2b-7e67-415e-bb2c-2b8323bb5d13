﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using Newtonsoft.Json;
using ns13;
using ns18;
using ns19;
using ns2;
using ns26;
using ns9;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200005E RID: 94
	internal static class InfoMineMgr
	{
		// Token: 0x14000019 RID: 25
		// (add) Token: 0x0600033A RID: 826 RVA: 0x0001FC98 File Offset: 0x0001DE98
		// (remove) Token: 0x0600033B RID: 827 RVA: 0x0001FCD0 File Offset: 0x0001DED0
		public static event Delegate0 InfoMineRetrieved
		{
			[CompilerGenerated]
			add
			{
				Delegate0 @delegate = InfoMineMgr.delegate0_0;
				Delegate0 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate0 value2 = (Delegate0)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate0>(ref InfoMineMgr.delegate0_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate0 @delegate = InfoMineMgr.delegate0_0;
				Delegate0 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate0 value2 = (Delegate0)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate0>(ref InfoMineMgr.delegate0_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600033C RID: 828 RVA: 0x0000361C File Offset: 0x0000181C
		private static void smethod_0(Class19 class19_0)
		{
			InfoMineMgr.smethod_1(new List<Class19>
			{
				class19_0
			});
		}

		// Token: 0x0600033D RID: 829 RVA: 0x00003631 File Offset: 0x00001831
		private static void smethod_1(List<Class19> list_0)
		{
			Delegate0 @delegate = InfoMineMgr.delegate0_0;
			if (@delegate != null)
			{
				@delegate(null, new EventArgs0(list_0));
			}
		}

		// Token: 0x0600033E RID: 830 RVA: 0x0000364C File Offset: 0x0000184C
		static InfoMineMgr()
		{
			InfoMineMgr.infoMineApiWorker_0.ResultReceived += InfoMineMgr.smethod_3;
			Base.Data.CurrSymblChanged += InfoMineMgr.smethod_2;
		}

		// Token: 0x0600033F RID: 831 RVA: 0x00003681 File Offset: 0x00001881
		private static void smethod_2(EventArgs1 eventArgs1_0)
		{
			InfoMineMgr.smethod_5(null);
		}

		// Token: 0x06000340 RID: 832 RVA: 0x0001FD08 File Offset: 0x0001DF08
		private static void smethod_3(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				Dictionary<string, object> requestDict = e.RequestDict;
				if (requestDict.ContainsKey(Class521.smethod_0(1389)))
				{
					List<Class19> list = requestDict[Class521.smethod_0(1389)] as List<Class19>;
					if (list.Count > 0)
					{
						foreach (Class19 @class in list)
						{
							StkSymbol stkSymbol = SymbMgr.smethod_8(@class.tscode);
							if (stkSymbol != null)
							{
								InfoMineMgr.InfoMineDict[stkSymbol.ID] = @class;
							}
						}
						InfoMineMgr.smethod_1(list);
					}
				}
			}
		}

		// Token: 0x06000341 RID: 833 RVA: 0x0000368B File Offset: 0x0000188B
		public static void smethod_4(StkSymbol stkSymbol_0)
		{
			InfoMineMgr.smethod_5(new List<StkSymbol>
			{
				stkSymbol_0
			});
		}

		// Token: 0x06000342 RID: 834 RVA: 0x0001FDC0 File Offset: 0x0001DFC0
		public static void smethod_5(List<StkSymbol> list_0 = null)
		{
			if (list_0 == null)
			{
				list_0 = Base.Data.SymbDataSets.Select(new Func<SymbDataSet, StkSymbol>(InfoMineMgr.<>c.<>9.method_0)).ToList<StkSymbol>();
			}
			List<Class19> list = new List<Class19>();
			List<StkSymbol> list2 = new List<StkSymbol>();
			foreach (StkSymbol stkSymbol in list_0)
			{
				Class19 @class = null;
				InfoMineMgr.InfoMineDict.TryGetValue(stkSymbol.ID, out @class);
				if (@class != null)
				{
					goto IL_D3;
				}
				string text = stkSymbol.method_1();
				if (!string.IsNullOrEmpty(text))
				{
					string text2 = Path.Combine(TApp.string_7, text + Class521.smethod_0(3597));
					string value = null;
					if (!Utility.FileExists(text2))
					{
						goto IL_FC;
					}
					try
					{
						value = File.ReadAllText(text2);
						goto IL_FC;
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
						goto IL_FC;
					}
					try
					{
						IL_C1:
						@class = JsonConvert.DeserializeObject<Class19>(value);
						goto IL_DF;
					}
					catch (Exception exception_2)
					{
						Class184.smethod_0(exception_2);
						goto IL_DF;
					}
					goto IL_D3;
					IL_FC:
					if (!string.IsNullOrEmpty(value))
					{
						goto IL_C1;
					}
				}
				IL_DF:
				if (@class != null)
				{
					list.Add(@class);
					continue;
				}
				list2.Add(stkSymbol);
				continue;
				IL_D3:
				if (@class.method_3())
				{
					@class = null;
					goto IL_DF;
				}
				goto IL_DF;
			}
			if (list2.Count > 0)
			{
				InfoMineMgr.infoMineApiWorker_0.method_2(list2);
			}
			if (list.Count > 0)
			{
				InfoMineMgr.smethod_1(list);
			}
		}

		// Token: 0x170000C3 RID: 195
		// (get) Token: 0x06000343 RID: 835 RVA: 0x0001FF34 File Offset: 0x0001E134
		// (set) Token: 0x06000344 RID: 836 RVA: 0x000036A0 File Offset: 0x000018A0
		public static Dictionary<int, Class19> InfoMineDict
		{
			get
			{
				if (InfoMineMgr.dictionary_0 == null)
				{
					InfoMineMgr.dictionary_0 = new Dictionary<int, Class19>();
				}
				return InfoMineMgr.dictionary_0;
			}
			private set
			{
				InfoMineMgr.dictionary_0 = value;
			}
		}

		// Token: 0x04000126 RID: 294
		[CompilerGenerated]
		private static Delegate0 delegate0_0;

		// Token: 0x04000127 RID: 295
		private static InfoMineApiWorker infoMineApiWorker_0 = new InfoMineApiWorker();

		// Token: 0x04000128 RID: 296
		private static Dictionary<int, Class19> dictionary_0;
	}
}
