﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;
using ns18;

namespace ns20
{
	// Token: 0x02000390 RID: 912
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal sealed class Class491
	{
		// Token: 0x06002541 RID: 9537 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class491()
		{
		}

		// Token: 0x17000646 RID: 1606
		// (get) Token: 0x06002542 RID: 9538 RVA: 0x00101240 File Offset: 0x000FF440
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class491.resourceManager_0 == null)
				{
					Class491.resourceManager_0 = new ResourceManager(Class521.smethod_0(109791), typeof(Class491).Assembly);
				}
				return Class491.resourceManager_0;
			}
		}

		// Token: 0x17000647 RID: 1607
		// (get) Token: 0x06002543 RID: 9539 RVA: 0x00101280 File Offset: 0x000FF480
		// (set) Token: 0x06002544 RID: 9540 RVA: 0x0000E699 File Offset: 0x0000C899
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class491.cultureInfo_0;
			}
			set
			{
				Class491.cultureInfo_0 = value;
			}
		}

		// Token: 0x17000648 RID: 1608
		// (get) Token: 0x06002545 RID: 9541 RVA: 0x00101298 File Offset: 0x000FF498
		internal static string levenFeature
		{
			get
			{
				return Class491.ResourceManager.GetString(Class521.smethod_0(109796), Class491.cultureInfo_0);
			}
		}

		// Token: 0x040011F4 RID: 4596
		private static ResourceManager resourceManager_0;

		// Token: 0x040011F5 RID: 4597
		private static CultureInfo cultureInfo_0;
	}
}
