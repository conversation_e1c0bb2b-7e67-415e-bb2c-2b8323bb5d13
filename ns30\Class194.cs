﻿using System;
using System.Runtime.CompilerServices;

namespace ns30
{
	// Token: 0x0200013B RID: 315
	internal sealed class Class194
	{
		// Token: 0x06000CCA RID: 3274 RVA: 0x00005BA5 File Offset: 0x00003DA5
		public Class194(int int_1, bool bool_2, int? nullable_4, int? nullable_5, bool bool_3, int? nullable_6, int? nullable_7)
		{
			this.stkId = int_1;
			this.getMin = bool_2;
			this.minBegYr = nullable_4;
			this.minEndYr = nullable_5;
			this.getHour = bool_3;
			this.hourBegYr = nullable_6;
			this.hourEndYr = nullable_7;
		}

		// Token: 0x17000208 RID: 520
		// (get) Token: 0x06000CCB RID: 3275 RVA: 0x0004C35C File Offset: 0x0004A55C
		// (set) Token: 0x06000CCC RID: 3276 RVA: 0x00005BE4 File Offset: 0x00003DE4
		public int stkId { get; set; }

		// Token: 0x17000209 RID: 521
		// (get) Token: 0x06000CCD RID: 3277 RVA: 0x0004C374 File Offset: 0x0004A574
		// (set) Token: 0x06000CCE RID: 3278 RVA: 0x00005BEF File Offset: 0x00003DEF
		public bool getMin { get; set; }

		// Token: 0x1700020A RID: 522
		// (get) Token: 0x06000CCF RID: 3279 RVA: 0x0004C38C File Offset: 0x0004A58C
		// (set) Token: 0x06000CD0 RID: 3280 RVA: 0x00005BFA File Offset: 0x00003DFA
		public int? minBegYr { get; set; }

		// Token: 0x1700020B RID: 523
		// (get) Token: 0x06000CD1 RID: 3281 RVA: 0x0004C3A4 File Offset: 0x0004A5A4
		// (set) Token: 0x06000CD2 RID: 3282 RVA: 0x00005C05 File Offset: 0x00003E05
		public int? minEndYr { get; set; }

		// Token: 0x1700020C RID: 524
		// (get) Token: 0x06000CD3 RID: 3283 RVA: 0x0004C3BC File Offset: 0x0004A5BC
		// (set) Token: 0x06000CD4 RID: 3284 RVA: 0x00005C10 File Offset: 0x00003E10
		public bool getHour { get; set; }

		// Token: 0x1700020D RID: 525
		// (get) Token: 0x06000CD5 RID: 3285 RVA: 0x0004C3D4 File Offset: 0x0004A5D4
		// (set) Token: 0x06000CD6 RID: 3286 RVA: 0x00005C1B File Offset: 0x00003E1B
		public int? hourBegYr { get; set; }

		// Token: 0x1700020E RID: 526
		// (get) Token: 0x06000CD7 RID: 3287 RVA: 0x0004C3EC File Offset: 0x0004A5EC
		// (set) Token: 0x06000CD8 RID: 3288 RVA: 0x00005C26 File Offset: 0x00003E26
		public int? hourEndYr { get; set; }

		// Token: 0x04000545 RID: 1349
		[CompilerGenerated]
		private int int_0;

		// Token: 0x04000546 RID: 1350
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000547 RID: 1351
		[CompilerGenerated]
		private int? nullable_0;

		// Token: 0x04000548 RID: 1352
		[CompilerGenerated]
		private int? nullable_1;

		// Token: 0x04000549 RID: 1353
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x0400054A RID: 1354
		[CompilerGenerated]
		private int? nullable_2;

		// Token: 0x0400054B RID: 1355
		[CompilerGenerated]
		private int? nullable_3;
	}
}
