﻿using System;
using System.Drawing;
using System.Runtime.CompilerServices;

namespace ns12
{
	// Token: 0x020002B9 RID: 697
	internal sealed class Class359
	{
		// Token: 0x06001EE1 RID: 7905 RVA: 0x0000D058 File Offset: 0x0000B258
		public Class359(string string_2, Image image_1, string string_3, bool bool_1)
		{
			this.Title = string_2;
			this.Thumbnail = image_1;
			this.VideoUrl = string_3;
			this.IsFree = bool_1;
		}

		// Token: 0x170004CD RID: 1229
		// (get) Token: 0x06001EE2 RID: 7906 RVA: 0x000E0674 File Offset: 0x000DE874
		// (set) Token: 0x06001EE3 RID: 7907 RVA: 0x0000D07F File Offset: 0x0000B27F
		public string Title { get; set; }

		// Token: 0x170004CE RID: 1230
		// (get) Token: 0x06001EE4 RID: 7908 RVA: 0x000E068C File Offset: 0x000DE88C
		// (set) Token: 0x06001EE5 RID: 7909 RVA: 0x0000D08A File Offset: 0x0000B28A
		public Image Thumbnail { get; set; }

		// Token: 0x170004CF RID: 1231
		// (get) Token: 0x06001EE6 RID: 7910 RVA: 0x000E06A4 File Offset: 0x000DE8A4
		// (set) Token: 0x06001EE7 RID: 7911 RVA: 0x0000D095 File Offset: 0x0000B295
		public string VideoUrl { get; set; }

		// Token: 0x170004D0 RID: 1232
		// (get) Token: 0x06001EE8 RID: 7912 RVA: 0x000E06BC File Offset: 0x000DE8BC
		// (set) Token: 0x06001EE9 RID: 7913 RVA: 0x0000D0A0 File Offset: 0x0000B2A0
		public bool IsFree { get; set; }

		// Token: 0x04000F8F RID: 3983
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000F90 RID: 3984
		[CompilerGenerated]
		private Image image_0;

		// Token: 0x04000F91 RID: 3985
		[CompilerGenerated]
		private string string_1;

		// Token: 0x04000F92 RID: 3986
		[CompilerGenerated]
		private bool bool_0;
	}
}
