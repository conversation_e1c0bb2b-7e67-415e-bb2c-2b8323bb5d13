﻿using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ns26
{
	// Token: 0x0200008F RID: 143
	internal partial class Form1 : Form
	{
		// Token: 0x060004B0 RID: 1200
		[DllImport("user32.dll")]
		public static extern int SendMessage(IntPtr intptr_0, int int_0, int int_1, int int_2);

		// Token: 0x060004B1 RID: 1201
		[DllImport("user32.dll")]
		public static extern bool ReleaseCapture();

		// Token: 0x060004B2 RID: 1202 RVA: 0x00004050 File Offset: 0x00002250
		public Form1()
		{
			base.FormBorderStyle = FormBorderStyle.None;
			base.StartPosition = FormStartPosition.CenterScreen;
			base.MouseDown += this.Form1_MouseDown;
		}

		// Token: 0x060004B3 RID: 1203 RVA: 0x0000407A File Offset: 0x0000227A
		private void Form1_MouseDown(object sender, MouseEventArgs e)
		{
			this.method_0(e);
		}

		// Token: 0x060004B4 RID: 1204 RVA: 0x00004085 File Offset: 0x00002285
		protected void method_0(MouseEventArgs mouseEventArgs_0)
		{
			if (mouseEventArgs_0.Button == MouseButtons.Left)
			{
				Form1.ReleaseCapture();
				Form1.SendMessage(base.Handle, 161, 2, 0);
			}
		}
	}
}
