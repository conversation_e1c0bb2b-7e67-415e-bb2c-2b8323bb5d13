﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Xml.Linq;
using ns18;
using ns26;
using ns7;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x02000028 RID: 40
	[Serializable]
	internal struct ChartUISettings
	{
		// Token: 0x060000F0 RID: 240 RVA: 0x000146A0 File Offset: 0x000128A0
		public static ChartUISettings GetNewFromXElement(XElement pageEl, string texVer = null)
		{
			ChartUISettings result = default(ChartUISettings);
			string value = pageEl.Element(Class521.smethod_0(1858)).Value;
			XElement xelement = pageEl.Element(Class521.smethod_0(1867));
			IEnumerable<XElement> enumerable = xelement.Element(Class521.smethod_0(1897)).Elements(Class521.smethod_0(1930));
			List<SplitContainerParam> list = new List<SplitContainerParam>();
			foreach (XElement el in enumerable)
			{
				SplitContainerParam item6 = new SplitContainerParam(el);
				list.Add(item6);
			}
			result.SplitContainerParamList = list;
			XElement xelement2 = xelement.Element(Class521.smethod_0(1959));
			if (xelement2 != null)
			{
				result.SingleFixedContent = xelement2.Value;
			}
			else if (texVer == null)
			{
				if (value == Class521.smethod_0(1984))
				{
					result.SingleFixedContent = typeof(TransTabCtrl).ToString();
				}
				else if (value == Class521.smethod_0(1993))
				{
					result.SingleFixedContent = typeof(ChtCtrl_Tick).ToString();
				}
				else if (value == Class521.smethod_0(2002))
				{
					result.SingleFixedContent = typeof(ChtCtrl_KLine).ToString();
				}
			}
			IEnumerable<XElement> enumerable2 = xelement.Element(Class521.smethod_0(2011)).Elements(Class521.smethod_0(2036));
			List<ChtCtrlParam> list2 = new List<ChtCtrlParam>();
			foreach (XElement xelement3 in enumerable2)
			{
				bool flag = Convert.ToBoolean(Convert.ToInt32(xelement3.Attribute(Class521.smethod_0(2053)).Value));
				ChtCtrlParam chtCtrlParam;
				if (flag)
				{
					chtCtrlParam = new ChtCtrlParam_KLine();
				}
				else
				{
					chtCtrlParam = new ChtCtrlParam();
				}
				XAttribute xattribute = xelement3.Attribute(Class521.smethod_0(2082));
				if (xattribute != null)
				{
					chtCtrlParam.IfNoSync = Convert.ToBoolean(Convert.ToInt32(xattribute.Value));
				}
				XAttribute xattribute2 = xelement3.Attribute(Class521.smethod_0(2095));
				if (xattribute2 != null)
				{
					chtCtrlParam.LinkedSymbolId = new int?(Convert.ToInt32(xattribute2.Value));
				}
				chtCtrlParam.SpCParam_this = new SplitContainerParam(xelement3.Element(Class521.smethod_0(2116)));
				chtCtrlParam.HasParentSpContainer = Convert.ToBoolean(Convert.ToInt32(xelement3.Element(Class521.smethod_0(2137)).Value));
				chtCtrlParam.ParentSpContainerTag = xelement3.Element(Class521.smethod_0(2166)).Value;
				chtCtrlParam.IsInParentSpContainerPanel1 = Convert.ToBoolean(Convert.ToInt32(xelement3.Element(Class521.smethod_0(2195)).Value));
				XElement xelement4 = xelement3.Element(Class521.smethod_0(2232));
				if (xelement4 != null)
				{
					chtCtrlParam.IfShowTickBidPanel = Convert.ToBoolean(Convert.ToInt32(xelement4.Value));
				}
				chtCtrlParam.Tag = xelement3.Element(Class521.smethod_0(2257)).Value;
				chtCtrlParam.PeriodType = (PeriodType)Convert.ToInt32(xelement3.Element(Class521.smethod_0(2262)).Value);
				if (string.IsNullOrEmpty(xelement3.Element(Class521.smethod_0(2279)).Value))
				{
					chtCtrlParam.PeriodUnits = null;
				}
				else
				{
					chtCtrlParam.PeriodUnits = new int?(Convert.ToInt32(xelement3.Element(Class521.smethod_0(2279)).Value));
				}
				chtCtrlParam.MaxSticksPerChart = Convert.ToInt32(xelement3.Element(Class521.smethod_0(2296)).Value);
				xelement4 = xelement3.Element(Class521.smethod_0(2321));
				if (xelement4 != null)
				{
					chtCtrlParam.IsSwitchedBehind = Convert.ToBoolean(Convert.ToInt32(xelement4.Value));
				}
				if (flag)
				{
					XElement xelement5 = xelement3.Element(Class521.smethod_0(2346));
					if (xelement5 != null)
					{
						((ChtCtrlParam_KLine)chtCtrlParam).SpCParam_VolMACD = new SplitContainerParam(xelement5);
					}
					XElement xelement6 = xelement3.Element(Class521.smethod_0(2371));
					if (xelement6 != null)
					{
						((ChtCtrlParam_KLine)chtCtrlParam).SpCParam_VolMACD_Sub = new SplitContainerParam(xelement6);
					}
					if (xelement3.Element(Class521.smethod_0(2400)) != null)
					{
						List<UserDefineIndScript> indListFromXEls = ChartUISettings.GetIndListFromXEls(xelement3.Element(Class521.smethod_0(2400)).Elements(Class521.smethod_0(2413)));
						if (indListFromXEls.Any<UserDefineIndScript>())
						{
							List<ChartParam> list3 = new List<ChartParam>();
							IEnumerable<UserDefineIndScript> source = from i in indListFromXEls
							where i.MainK
							select i;
							ChartParam item2 = new ChartParam(ChartType.CandleStick, null, source.Any<UserDefineIndScript>() ? source.ToList<UserDefineIndScript>() : null, false);
							list3.Add(item2);
							if (!((ChtCtrlParam_KLine)chtCtrlParam).SpCParam_this.Panel2Collapsed)
							{
								ChartParam item3 = new ChartParam(ChartType.MACD, null, null, false);
								list3.Add(item3);
								IEnumerable<UserDefineIndScript> source2 = from i in indListFromXEls
								where !i.MainK
								select i;
								if (source2.Any<UserDefineIndScript>())
								{
									ChartParam item4 = new ChartParam(ChartType.MACD, null, source2.ToList<UserDefineIndScript>(), false);
									list3.Add(item4);
								}
							}
							((ChtCtrlParam_KLine)chtCtrlParam).ChartParamList = list3;
						}
					}
					else if (xelement3.Element(Class521.smethod_0(2426)) != null)
					{
						List<ChartParam> list4 = new List<ChartParam>();
						foreach (XElement xelement7 in xelement3.Element(Class521.smethod_0(2426)).Elements(Class521.smethod_0(2447)))
						{
							ChartType chartType = (ChartType)Convert.ToInt32(xelement7.Attribute(Class521.smethod_0(2464)).Value);
							bool isYAxisLogType = false;
							if (xelement7.Attribute(Class521.smethod_0(2477)) == null)
							{
								isYAxisLogType = false;
								goto IL_898;
							}
							try
							{
								isYAxisLogType = Convert.ToBoolean(xelement7.Attribute(Class521.smethod_0(2477)).Value);
								goto IL_898;
							}
							catch (Exception)
							{
								isYAxisLogType = false;
								goto IL_898;
							}
							goto IL_680;
							IL_809:
							List<UserDefineIndScript> list5;
							if (chartType == ChartType.Vol)
							{
								chartType = ChartType.MACD;
								UserDefineIndScript uds = UserDefineFileMgr.UDSListChecked.SingleOrDefault((UserDefineIndScript item) => item.Name == Class521.smethod_0(2652));
								if (uds != null)
								{
									list5.Clear();
									if (!list5.Any((UserDefineIndScript item) => item.Name == uds.Name))
									{
										list5.Add(uds);
									}
								}
							}
							ChartParam item5 = new ChartParam(chartType, null, list5, isYAxisLogType);
							list4.Add(item5);
							continue;
							IL_680:
							XElement xelement8;
							list5 = ChartUISettings.GetIndListFromXEls(xelement8.Elements(Class521.smethod_0(2413)));
							goto IL_809;
							IL_898:
							list5 = new List<UserDefineIndScript>();
							xelement8 = xelement7.Element(Class521.smethod_0(2400));
							if (xelement8 != null)
							{
								goto IL_680;
							}
							XElement xelement9 = xelement7.Element(Class521.smethod_0(2498));
							if (xelement9 != null)
							{
								foreach (XElement xelement_ in xelement9.Elements(Class521.smethod_0(2507)))
								{
									IndParamsValue paramsValue = UserDefineFileMgr.smethod_27(xelement_);
									if (!list5.Any((UserDefineIndScript item) => item.Name == paramsValue.IndName))
									{
										UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault((UserDefineIndScript item) => item.Name == paramsValue.IndName);
										if (userDefineIndScript != null)
										{
											userDefineIndScript = (userDefineIndScript.System.ICloneable.Clone() as UserDefineIndScript);
											using (List<UserDefineParam>.Enumerator enumerator4 = userDefineIndScript.UserDefineParams.GetEnumerator())
											{
												while (enumerator4.MoveNext())
												{
													UserDefineParam ap = enumerator4.Current;
													NameDoubleValue nameDoubleValue = paramsValue.Values.SingleOrDefault((NameDoubleValue item) => item.Name == ap.Name);
													if (nameDoubleValue != null)
													{
														ap.Value = nameDoubleValue.Value;
													}
												}
											}
											list5.Add(userDefineIndScript);
										}
										else
										{
											Class184.smethod_0(new Exception(paramsValue.IndName + Class521.smethod_0(2520)));
										}
									}
								}
								goto IL_809;
							}
							goto IL_809;
						}
						((ChtCtrlParam_KLine)chtCtrlParam).ChartParamList = list4;
					}
				}
				list2.Add(chtCtrlParam);
			}
			if (!string.IsNullOrEmpty(result.SingleFixedContent))
			{
				if (result.SingleFixedContent == typeof(ChtCtrl_Tick).ToString())
				{
					foreach (ChtCtrlParam chtCtrlParam2 in list2)
					{
						if (chtCtrlParam2 is ChtCtrlParam_KLine)
						{
							chtCtrlParam2.IsSwitchedBehind = true;
						}
					}
					if (list2.SingleOrDefault((ChtCtrlParam p) => !(p is ChtCtrlParam_KLine)) == null)
					{
						ChtCtrlParam singleChtTickParam = ChartUISettings.GetSingleChtTickParam();
						if (list.SingleOrDefault((SplitContainerParam s) => s.Tag == Class521.smethod_0(2606)) == null)
						{
							SplitContainerParam singleChtParentSpcParam = ChartUISettings.GetSingleChtParentSpcParam(typeof(ChtCtrl_Tick));
							list.Clear();
							list.Add(singleChtParentSpcParam);
						}
						list2.Add(singleChtTickParam);
					}
				}
				else if (result.SingleFixedContent == typeof(ChtCtrl_KLine).ToString())
				{
					list2.RemoveAll((ChtCtrlParam p) => !(p is ChtCtrlParam_KLine));
					if (list2.SingleOrDefault((ChtCtrlParam p) => p is ChtCtrlParam_KLine) == null)
					{
						ChtCtrlParam singleKLineChtParam = ChartUISettings.GetSingleKLineChtParam();
						if (list.SingleOrDefault((SplitContainerParam s) => s.Tag == Class521.smethod_0(2606)) == null)
						{
							SplitContainerParam singleChtParentSpcParam2 = ChartUISettings.GetSingleChtParentSpcParam(typeof(ChtCtrl_KLine));
							list.Clear();
							list.Add(singleChtParentSpcParam2);
						}
						list2.Add(singleKLineChtParam);
					}
				}
			}
			result.ChtCtrlParamList = list2;
			XElement xelement10 = xelement.Element(Class521.smethod_0(2581));
			if (xelement10 != null)
			{
				result.TransTabCtrlParam = new TransTabCtrlParam
				{
					HasParentSpContainer = Convert.ToBoolean(Convert.ToInt32(xelement10.Attribute(Class521.smethod_0(2137)).Value)),
					IsInParentSpContainerPanel1 = Convert.ToBoolean(Convert.ToInt32(xelement10.Attribute(Class521.smethod_0(2195)).Value)),
					ParentSpContainerTag = xelement10.Attribute(Class521.smethod_0(2166)).Value
				};
			}
			return result;
		}

		// Token: 0x060000F1 RID: 241 RVA: 0x000152E0 File Offset: 0x000134E0
		public static ChtCtrlParam GetSingleChtTickParam()
		{
			return new ChtCtrlParam
			{
				HasParentSpContainer = true,
				ParentSpContainerTag = Class521.smethod_0(2606),
				IsInParentSpContainerPanel1 = true,
				MaxSticksPerChart = 270,
				PeriodType = PeriodType.ByMins,
				PeriodUnits = new int?(1),
				Tag = Class521.smethod_0(2619),
				SpCParam_this = new SplitContainerParam
				{
					Panel1Collapsed = false,
					Panel2Collapsed = false,
					SplitterDistance = 260,
					Size = new Size(1200, 400)
				}
			};
		}

		// Token: 0x060000F2 RID: 242 RVA: 0x00015380 File Offset: 0x00013580
		private static SplitContainerParam GetSingleChtParentSpcParam(Type ctrlType)
		{
			return new SplitContainerParam
			{
				Panel2Collapsed = true,
				SplitterDistance = 57,
				Panel1CtrlType = ctrlType,
				Panel2CtrlType = ctrlType,
				Orientation = Orientation.Horizontal,
				HasParentSpContainer = false,
				Tag = Class521.smethod_0(2606),
				Size = new Size(1200, 400)
			};
		}

		// Token: 0x060000F3 RID: 243 RVA: 0x000153E8 File Offset: 0x000135E8
		public static ChartUISettings GetSingleTransTabCtrlUISettings()
		{
			ChartUISettings result = default(ChartUISettings);
			List<SplitContainerParam> list = new List<SplitContainerParam>();
			list.Add(ChartUISettings.GetSingleChtParentSpcParam(typeof(TransTabCtrl)));
			List<ChtCtrlParam> chtCtrlParamList = new List<ChtCtrlParam>();
			result.TransTabCtrlParam = new TransTabCtrlParam
			{
				HasParentSpContainer = true,
				IsInParentSpContainerPanel1 = true,
				ParentSpContainerTag = Class521.smethod_0(2606)
			};
			result.SplitContainerParamList = list;
			result.ChtCtrlParamList = chtCtrlParamList;
			result.SingleFixedContent = typeof(TransTabCtrl).ToString();
			return result;
		}

		// Token: 0x060000F4 RID: 244 RVA: 0x00015478 File Offset: 0x00013678
		public static ChartUISettings GetSimpleChartUISettings()
		{
			List<SplitContainerParam> list = new List<SplitContainerParam>();
			list.Add(new SplitContainerParam(Orientation.Horizontal, false, true, 129, typeof(ChtCtrl_KLine), typeof(ChtCtrl_KLine), false, Class521.smethod_0(1449), true, Class521.smethod_0(2606), new Size(1216, 431)));
			List<ChtCtrlParam> list2 = new List<ChtCtrlParam>();
			ChtCtrlParam_KLine singleKLineChtParam = ChartUISettings.GetSingleKLineChtParam();
			list2.Add(singleKLineChtParam);
			TransTabCtrlParam transTabCtrlParam = new TransTabCtrlParam();
			transTabCtrlParam.HasParentSpContainer = false;
			transTabCtrlParam.IsInParentSpContainerPanel1 = true;
			transTabCtrlParam.ParentSpContainerTag = Class521.smethod_0(1449);
			return new ChartUISettings
			{
				TransTabCtrlParam = transTabCtrlParam,
				SplitContainerParamList = list,
				ChtCtrlParamList = list2,
				SingleFixedContent = typeof(ChtCtrlParam_KLine).ToString()
			};
		}

		// Token: 0x060000F5 RID: 245 RVA: 0x00015550 File Offset: 0x00013750
		public static ChtCtrlParam_KLine GetSingleKLineChtParam()
		{
			ChtCtrlParam_KLine chtCtrlParam_KLine = new ChtCtrlParam_KLine();
			chtCtrlParam_KLine.HasParentSpContainer = true;
			List<Indicator> indList = new List<Indicator>();
			List<ChartParam> list = new List<ChartParam>();
			ChartParam item3 = new ChartParam(ChartType.CandleStick, indList, new List<UserDefineIndScript>(), false);
			list.Add(item3);
			List<UserDefineIndScript> list2 = new List<UserDefineIndScript>();
			IndParamsValue pVal = new IndParamsValue(Class521.smethod_0(2652), new List<NameDoubleValue>());
			UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault((UserDefineIndScript item) => item.Name == pVal.IndName);
			if (userDefineIndScript != null)
			{
				userDefineIndScript = (userDefineIndScript.System.ICloneable.Clone() as UserDefineIndScript);
			}
			list2.Add(userDefineIndScript);
			ChartParam item2 = new ChartParam(ChartType.MACD, null, list2, false);
			list.Add(item2);
			chtCtrlParam_KLine.ChartParamList = list;
			chtCtrlParam_KLine.IsInParentSpContainerPanel1 = true;
			chtCtrlParam_KLine.MaxSticksPerChart = 96;
			chtCtrlParam_KLine.ParentSpContainerTag = Class521.smethod_0(2606);
			chtCtrlParam_KLine.PeriodUnits = new int?(1);
			chtCtrlParam_KLine.PeriodType = PeriodType.ByMins;
			chtCtrlParam_KLine.SpCParam_this = new SplitContainerParam(Orientation.Horizontal, false, false, 326, null, null, false, null, false, null, new Size(1210, 425));
			chtCtrlParam_KLine.SpCParam_VolMACD = new SplitContainerParam(Orientation.Horizontal, false, true, 37, null, null, false, null, false, null, new Size(1210, 98));
			chtCtrlParam_KLine.Tag = Class521.smethod_0(2657);
			return chtCtrlParam_KLine;
		}

		// Token: 0x060000F6 RID: 246 RVA: 0x00015694 File Offset: 0x00013894
		private static List<UserDefineIndScript> GetIndListFromXEls(IEnumerable<XElement> xEls)
		{
			List<UserDefineIndScript> list = new List<UserDefineIndScript>();
			foreach (XElement xelement in xEls)
			{
				string value = xelement.Attribute(Class521.smethod_0(2690)).Value;
				if (value.StartsWith(Class521.smethod_0(2699)))
				{
					int num = value.LastIndexOf(Class521.smethod_0(2712));
					string indName = value.Substring(num + 1, value.Length - num - 1);
					if (indName.Equals(Class521.smethod_0(2717), StringComparison.InvariantCultureIgnoreCase))
					{
						indName = Class521.smethod_0(2722);
					}
					UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault((UserDefineIndScript item) => item.Name == indName);
					if (userDefineIndScript != null)
					{
						UserDefineIndScript item2 = userDefineIndScript.System.ICloneable.Clone() as UserDefineIndScript;
						list.Add(item2);
					}
				}
			}
			return list;
		}

		// Token: 0x060000F7 RID: 247 RVA: 0x000157A4 File Offset: 0x000139A4
		public bool ShallSaveCtrlToXML(Type ctrlType)
		{
			if (!string.IsNullOrEmpty(this.SingleFixedContent))
			{
				if (ctrlType == typeof(ChtCtrl_KLine) && (this.IsSingleTransTabCtrl || this.IsSingleTickChart))
				{
					return true;
				}
				if (ctrlType.ToString() != this.SingleFixedContent)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x060000F8 RID: 248 RVA: 0x000157FC File Offset: 0x000139FC
		public List<int> GetLinkedSymbolIds()
		{
			List<int> list = new List<int>();
			if (this.ChtCtrlParamList != null)
			{
				foreach (ChtCtrlParam chtCtrlParam in this.ChtCtrlParamList)
				{
					if (chtCtrlParam.IfNoSync && chtCtrlParam.LinkedSymbolId != null && !list.Contains(chtCtrlParam.LinkedSymbolId.Value))
					{
						list.Add(chtCtrlParam.LinkedSymbolId.Value);
					}
				}
			}
			return list;
		}

		// Token: 0x060000F9 RID: 249 RVA: 0x000158A0 File Offset: 0x00013AA0
		public List<SplitContainer> RestoreSplitContainers(Panel panel)
		{
			panel.SuspendLayout();
			List<SplitContainer> list = new List<SplitContainer>();
			foreach (SplitContainerParam splitContainerParam in this.SplitContainerParamList)
			{
				SplitContainer splitContainer = new SplitContainer();
				if (splitContainerParam.HasParentSpContainer)
				{
					SplitContainer splitContainer2 = Base.UI.smethod_103(list, splitContainerParam.ParentSpContainerTag);
					if (splitContainer2 != null && splitContainerParam.IsInParentSpContainerPanel1)
					{
						splitContainer2.Panel1.Controls.Add(splitContainer);
					}
					else
					{
						splitContainer2.Panel2.Controls.Add(splitContainer);
					}
				}
				else
				{
					panel.Controls.Add(splitContainer);
				}
				try
				{
					splitContainer.Dock = DockStyle.Fill;
				}
				catch
				{
				}
				try
				{
					IL_9C:
					splitContainer.SplitterWidth = 1;
				}
				catch
				{
				}
				splitContainer.Location = new Point(0, 0);
				splitContainer.BorderStyle = BorderStyle.None;
				splitContainer.FixedPanel = FixedPanel.None;
				splitContainer.Size = splitContainerParam.Size;
				splitContainer.Panel1Collapsed = splitContainerParam.Panel1Collapsed;
				splitContainer.Panel2Collapsed = splitContainerParam.Panel2Collapsed;
				splitContainer.Tag = splitContainerParam.Tag;
				try
				{
					if (splitContainerParam.Orientation == Orientation.Horizontal)
					{
						splitContainer.SplitterDistance = (int)Math.Round((double)splitContainerParam.SplitterDistance * (double)splitContainer.Size.Height / (double)splitContainerParam.Size.Height, 0);
					}
					else
					{
						splitContainer.SplitterDistance = (int)Math.Round((double)splitContainerParam.SplitterDistance * (double)splitContainer.Size.Width / (double)splitContainerParam.Size.Width, 0);
					}
					splitContainer.Orientation = splitContainerParam.Orientation;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					if (splitContainerParam.Tag == Class521.smethod_0(2606))
					{
						try
						{
							splitContainer.Orientation = Orientation.Horizontal;
							splitContainer.Height = 1280;
							splitContainer.Width = 760;
							splitContainer.SplitterDistance = 50;
							goto IL_253;
						}
						catch
						{
							goto IL_253;
						}
					}
					try
					{
						if (splitContainer.Height == 0)
						{
							if (splitContainer.DisplayRectangle.Y != 0)
							{
								splitContainer.Height = splitContainer.DisplayRectangle.Y;
							}
							else
							{
								splitContainer.Height = 100;
							}
						}
						if (splitContainer.Width == 0)
						{
							if (splitContainer.DisplayRectangle.X != 0)
							{
								splitContainer.Width = splitContainer.DisplayRectangle.X;
							}
							else
							{
								splitContainer.Width = 150;
							}
						}
						splitContainer.Orientation = splitContainerParam.Orientation;
						splitContainer.SplitterDistance = 50;
					}
					catch
					{
					}
					IL_253:;
				}
				list.Add(splitContainer);
				continue;
				goto IL_9C;
			}
			panel.ResumeLayout();
			return list;
		}

		// Token: 0x060000FA RID: 250 RVA: 0x00015BC8 File Offset: 0x00013DC8
		public List<int> GetStkIdListFromChartParams(bool inclCurrSelectedSymb = true)
		{
			List<int> list = new List<int>();
			foreach (ChtCtrlParam chtCtrlParam in this.ChtCtrlParamList)
			{
				if (chtCtrlParam.IfNoSync)
				{
					int value = chtCtrlParam.LinkedSymbolId.Value;
					if (Base.Data.UsrStkSymbols.ContainsKey(value))
					{
						if (!list.Contains(value))
						{
							list.Add(value);
						}
					}
					else
					{
						chtCtrlParam.IfNoSync = false;
					}
				}
				else if (inclCurrSelectedSymb && !list.Contains(Base.Data.CurrSelectedSymbol.ID))
				{
					list.Add(Base.Data.CurrSelectedSymbol.ID);
				}
			}
			return list;
		}

		// Token: 0x060000FB RID: 251 RVA: 0x00015C88 File Offset: 0x00013E88
		public TransTabCtrl GetTransTabCtrlFromParam(List<SplitContainer> splitContainerList)
		{
			TransTabCtrlParam transTabCtrlParam = this.TransTabCtrlParam;
			TransTabCtrl result;
			if (transTabCtrlParam == null)
			{
				result = null;
			}
			else
			{
				SplitContainer splitContainer = Base.UI.smethod_103(splitContainerList, transTabCtrlParam.ParentSpContainerTag);
				TransTabCtrl transTabCtrl = null;
				if (splitContainer != null)
				{
					if (transTabCtrlParam.IsInParentSpContainerPanel1)
					{
						if (splitContainer.Panel1.Controls.Count == 0)
						{
							transTabCtrl = new TransTabCtrl(splitContainer.Panel1);
						}
					}
					else if (splitContainer.Panel2.Controls.Count == 0)
					{
						transTabCtrl = new TransTabCtrl(splitContainer.Panel2);
					}
				}
				result = transTabCtrl;
			}
			return result;
		}

		// Token: 0x060000FC RID: 252 RVA: 0x00002E4B File Offset: 0x0000104B
		public void SetSplitContainerParamList(List<SplitContainer> spList)
		{
			this.SplitContainerParamList = this.GetSpContainerParamList(spList);
		}

		// Token: 0x060000FD RID: 253 RVA: 0x00015D00 File Offset: 0x00013F00
		public List<SplitContainerParam> GetSpContainerParamList(List<SplitContainer> spList)
		{
			List<SplitContainerParam> list = new List<SplitContainerParam>();
			foreach (SplitContainer sp in spList)
			{
				SplitContainerParam splitContainerParam = this.GetSplitContainerParam(sp);
				list.Add(splitContainerParam);
			}
			return list;
		}

		// Token: 0x060000FE RID: 254 RVA: 0x00015D64 File Offset: 0x00013F64
		public SplitContainerParam GetSplitContainerParam(SplitContainer sp)
		{
			SplitContainerParam splitContainerParam = new SplitContainerParam();
			splitContainerParam.Panel1CtrlType = null;
			splitContainerParam.Panel2CtrlType = null;
			splitContainerParam.HasParentSpContainer = false;
			splitContainerParam.ParentSpContainerTag = Class521.smethod_0(1449);
			splitContainerParam.IsInParentSpContainerPanel1 = true;
			splitContainerParam.Orientation = sp.Orientation;
			splitContainerParam.Panel1Collapsed = sp.Panel1Collapsed;
			splitContainerParam.Panel2Collapsed = sp.Panel2Collapsed;
			splitContainerParam.SplitterDistance = sp.SplitterDistance;
			splitContainerParam.Tag = sp.Tag.ToString();
			splitContainerParam.Size = sp.Size;
			if (sp.Panel1.Controls.Count > 0)
			{
				splitContainerParam.Panel1CtrlType = this.GetTypeOfPanelCtrl(sp.Panel1);
			}
			if (sp.Panel2.Controls.Count > 0)
			{
				splitContainerParam.Panel2CtrlType = this.GetTypeOfPanelCtrl(sp.Panel2);
			}
			if (sp.Parent.GetType() == typeof(SplitterPanel))
			{
				SplitterPanel splitterPanel = (SplitterPanel)sp.Parent;
				splitContainerParam.HasParentSpContainer = true;
				splitContainerParam.ParentSpContainerTag = splitterPanel.Parent.Tag.ToString();
				if (splitterPanel == ((SplitContainer)splitterPanel.Parent).Panel2)
				{
					splitContainerParam.IsInParentSpContainerPanel1 = false;
				}
			}
			return splitContainerParam;
		}

		// Token: 0x060000FF RID: 255 RVA: 0x00015E9C File Offset: 0x0001409C
		private Type GetTypeOfPanelCtrl(SplitterPanel spPanel)
		{
			Type type = spPanel.Controls[0].GetType();
			if ((type == typeof(Class58) || type == typeof(Label)) && spPanel.Controls.Count > 1)
			{
				type = spPanel.Controls[1].GetType();
			}
			return type;
		}

		// Token: 0x06000100 RID: 256 RVA: 0x00002E5C File Offset: 0x0000105C
		public void SetChtCtrlParamList(List<ChtCtrl> chtList)
		{
			this.ChtCtrlParamList = this.GetChtCtrlParamList(chtList);
		}

		// Token: 0x06000101 RID: 257 RVA: 0x00015EFC File Offset: 0x000140FC
		public List<ChtCtrlParam> GetChtCtrlParamList(List<ChtCtrl> chtList)
		{
			List<ChtCtrlParam> list = new List<ChtCtrlParam>();
			if (chtList != null && chtList.Count > 0)
			{
				IEnumerable<ChtCtrl> enumerable;
				if (!this.IsSingleTransTabCtrl && !this.IsSingleKLineChart)
				{
					if (this.IsSingleTickChart)
					{
						enumerable = chtList;
					}
					else
					{
						enumerable = from c in chtList
						where !c.IsSwitchedBehind
						select c;
					}
				}
				else
				{
					enumerable = from c in chtList
					where c is ChtCtrl_KLine
					select c;
				}
				foreach (ChtCtrl chtCtrl in enumerable)
				{
					ChtCtrlParam item = chtCtrl.vmethod_26();
					list.Add(item);
				}
			}
			return list;
		}

		// Token: 0x06000102 RID: 258 RVA: 0x00015FD4 File Offset: 0x000141D4
		public void SetTransTabCtrlParam(TransTabCtrl ttCtrl)
		{
			TransTabs transTabCtrlParam = null;
			if (ttCtrl != null)
			{
				transTabCtrlParam = ttCtrl.TransTabs;
			}
			this.SetTransTabCtrlParam(transTabCtrlParam);
		}

		// Token: 0x06000103 RID: 259 RVA: 0x00015FF8 File Offset: 0x000141F8
		public void SetTransTabCtrlParam(TransTabs transTabs = null)
		{
			if (this.IsSingleTransTabCtrl)
			{
				if (transTabs == null)
				{
					this.TransTabCtrlParam = new TransTabCtrlParam();
					this.TransTabCtrlParam.HasParentSpContainer = true;
					this.TransTabCtrlParam.IsInParentSpContainerPanel1 = true;
					this.TransTabCtrlParam.ParentSpContainerTag = Class521.smethod_0(2606);
				}
				else
				{
					this.TransTabCtrlParam = this.GetTransTabCtrlParam(transTabs.ParentTransTabCtrl);
				}
			}
			else if (transTabs != null && transTabs.ParentTransTabCtrl != null && !transTabs.ParentTransTabCtrl.IsSwitchedBehind)
			{
				this.TransTabCtrlParam = this.GetTransTabCtrlParam(transTabs.ParentTransTabCtrl);
			}
			else
			{
				this.TransTabCtrlParam = null;
			}
		}

		// Token: 0x06000104 RID: 260 RVA: 0x00016094 File Offset: 0x00014294
		public TransTabCtrlParam GetTransTabCtrlParam(TransTabCtrl ttCtrl)
		{
			TransTabCtrlParam transTabCtrlParam = new TransTabCtrlParam();
			if (ttCtrl != null && ttCtrl.Parent != null && ttCtrl.Parent.GetType() == typeof(SplitterPanel))
			{
				SplitterPanel splitterPanel = (SplitterPanel)ttCtrl.Parent;
				transTabCtrlParam.HasParentSpContainer = true;
				transTabCtrlParam.ParentSpContainerTag = splitterPanel.Parent.Tag.ToString();
				if (splitterPanel == ((SplitContainer)splitterPanel.Parent).Panel2)
				{
					transTabCtrlParam.IsInParentSpContainerPanel1 = false;
				}
			}
			return transTabCtrlParam;
		}

		// Token: 0x06000105 RID: 261 RVA: 0x00016114 File Offset: 0x00014314
		public XElement GetXElement()
		{
			XElement xelement = new XElement(Class521.smethod_0(1867));
			if (!string.IsNullOrEmpty(this.SingleFixedContent))
			{
				XElement content = new XElement(Class521.smethod_0(1959), this.SingleFixedContent);
				xelement.Add(content);
			}
			if (this.SplitContainerParamList != null)
			{
				XElement xelement2 = new XElement(Class521.smethod_0(1897));
				foreach (SplitContainerParam splitContainerParam in this.SplitContainerParamList)
				{
					XElement xelement3 = splitContainerParam.GetXElement(Class521.smethod_0(1930));
					if (xelement3 != null)
					{
						xelement2.Add(xelement3);
					}
				}
				xelement.Add(xelement2);
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(2727)));
			}
			if (this.TransTabCtrlParam != null && (this.IsSingleTransTabCtrl || this.ShallSaveCtrlToXML(typeof(TransTabCtrl))))
			{
				XElement xelement4 = this.TransTabCtrlParam.GetXElement();
				xelement.Add(xelement4);
			}
			XElement xelement5 = new XElement(Class521.smethod_0(2011));
			if (this.ShallSaveCtrlToXML(typeof(ChtCtrl_KLine)) || this.ShallSaveCtrlToXML(typeof(ChtCtrl_Tick)))
			{
				if (this.IsSingleKLineChart)
				{
					ChtCtrlParam chtCtrlParam = this.ChtCtrlParamList.FirstOrDefault((ChtCtrlParam c) => c is ChtCtrlParam_KLine);
					if (chtCtrlParam != null)
					{
						chtCtrlParam.IsSwitchedBehind = false;
						chtCtrlParam.HasParentSpContainer = true;
						chtCtrlParam.ParentSpContainerTag = Class521.smethod_0(2606);
						xelement5.Add(chtCtrlParam.vmethod_0());
					}
					else
					{
						Class184.smethod_0(new Exception(Class521.smethod_0(2776)));
					}
				}
				else
				{
					if (this.IsSingleTickChart)
					{
						using (List<ChtCtrlParam>.Enumerator enumerator2 = this.ChtCtrlParamList.GetEnumerator())
						{
							while (enumerator2.MoveNext())
							{
								ChtCtrlParam chtCtrlParam2 = enumerator2.Current;
								bool flag = !(chtCtrlParam2 is ChtCtrlParam_KLine);
								if (flag)
								{
									chtCtrlParam2.IsSwitchedBehind = false;
									chtCtrlParam2.HasParentSpContainer = true;
									chtCtrlParam2.ParentSpContainerTag = Class521.smethod_0(2606);
								}
								else
								{
									chtCtrlParam2.IsSwitchedBehind = true;
								}
								XElement xelement6 = chtCtrlParam2.vmethod_0();
								if (flag)
								{
									xelement6.SetAttributeValue(Class521.smethod_0(2053), Class521.smethod_0(2841));
								}
								xelement5.Add(xelement6);
							}
							goto IL_385;
						}
					}
					if (this.IsSingleTransTabCtrl)
					{
						ChtCtrlParam chtCtrlParam3 = this.ChtCtrlParamList.FirstOrDefault(delegate(ChtCtrlParam p)
						{
							bool result;
							if (p is ChtCtrlParam_KLine)
							{
								result = !p.IsSwitchedBehind;
							}
							else
							{
								result = false;
							}
							return result;
						});
						if (chtCtrlParam3 == null)
						{
							chtCtrlParam3 = this.ChtCtrlParamList.FirstOrDefault((ChtCtrlParam p) => p is ChtCtrlParam_KLine);
						}
						if (chtCtrlParam3 != null)
						{
							chtCtrlParam3.IsSwitchedBehind = true;
							XElement content2 = chtCtrlParam3.vmethod_0();
							xelement5.Add(content2);
						}
					}
					else
					{
						foreach (ChtCtrlParam chtCtrlParam4 in from p in this.ChtCtrlParamList
						where !p.IsSwitchedBehind
						select p)
						{
							XElement xelement7 = chtCtrlParam4.vmethod_0();
							if (!(chtCtrlParam4 is ChtCtrlParam_KLine))
							{
								xelement7.SetAttributeValue(Class521.smethod_0(2053), Class521.smethod_0(2841));
							}
							xelement5.Add(xelement7);
						}
					}
				}
			}
			IL_385:
			xelement.Add(xelement5);
			return xelement;
		}

		// Token: 0x17000041 RID: 65
		// (get) Token: 0x06000106 RID: 262 RVA: 0x000164DC File Offset: 0x000146DC
		// (set) Token: 0x06000107 RID: 263 RVA: 0x00002E6D File Offset: 0x0000106D
		public List<SplitContainerParam> SplitContainerParamList
		{
			get
			{
				return this._SplitContainerParamList;
			}
			set
			{
				this._SplitContainerParamList = value;
			}
		}

		// Token: 0x17000042 RID: 66
		// (get) Token: 0x06000108 RID: 264 RVA: 0x000164F4 File Offset: 0x000146F4
		// (set) Token: 0x06000109 RID: 265 RVA: 0x00002E78 File Offset: 0x00001078
		public List<ChtCtrlParam> ChtCtrlParamList
		{
			get
			{
				return this._ChtCtrlParamList;
			}
			set
			{
				this._ChtCtrlParamList = value;
			}
		}

		// Token: 0x17000043 RID: 67
		// (get) Token: 0x0600010A RID: 266 RVA: 0x0001650C File Offset: 0x0001470C
		// (set) Token: 0x0600010B RID: 267 RVA: 0x00002E83 File Offset: 0x00001083
		public TransTabCtrlParam TransTabCtrlParam
		{
			get
			{
				return this._TransTabCtrlParam;
			}
			set
			{
				this._TransTabCtrlParam = value;
			}
		}

		// Token: 0x17000044 RID: 68
		// (get) Token: 0x0600010C RID: 268 RVA: 0x00016524 File Offset: 0x00014724
		public List<int> LinkedSymbolIds
		{
			get
			{
				return this.GetLinkedSymbolIds();
			}
		}

		// Token: 0x17000045 RID: 69
		// (get) Token: 0x0600010D RID: 269 RVA: 0x0001653C File Offset: 0x0001473C
		// (set) Token: 0x0600010E RID: 270 RVA: 0x00002E8E File Offset: 0x0000108E
		public string SingleFixedContent
		{
			get
			{
				return this._SingleFixedContent;
			}
			set
			{
				this._SingleFixedContent = value;
			}
		}

		// Token: 0x17000046 RID: 70
		// (get) Token: 0x0600010F RID: 271 RVA: 0x00016554 File Offset: 0x00014754
		public bool IsSingleFixedContent
		{
			get
			{
				return !string.IsNullOrEmpty(this.SingleFixedContent);
			}
		}

		// Token: 0x17000047 RID: 71
		// (get) Token: 0x06000110 RID: 272 RVA: 0x00016574 File Offset: 0x00014774
		public bool IsSingleTransTabCtrl
		{
			get
			{
				bool result;
				if (this.IsSingleFixedContent)
				{
					result = (this.SingleFixedContent == typeof(TransTabCtrl).ToString());
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000048 RID: 72
		// (get) Token: 0x06000111 RID: 273 RVA: 0x000165AC File Offset: 0x000147AC
		public bool IsSingleTickChart
		{
			get
			{
				bool result;
				if (this.IsSingleFixedContent)
				{
					result = (this.SingleFixedContent == typeof(ChtCtrl_Tick).ToString());
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000049 RID: 73
		// (get) Token: 0x06000112 RID: 274 RVA: 0x000165E4 File Offset: 0x000147E4
		public bool IsSingleKLineChart
		{
			get
			{
				bool result;
				if (this.IsSingleFixedContent)
				{
					result = (this.SingleFixedContent == typeof(ChtCtrl_KLine).ToString());
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700004A RID: 74
		// (get) Token: 0x06000113 RID: 275 RVA: 0x0001661C File Offset: 0x0001481C
		public bool HasSyncCharts
		{
			get
			{
				bool result = false;
				if (this.ChtCtrlParamList != null)
				{
					result = this.ChtCtrlParamList.Exists((ChtCtrlParam c) => !c.IfNoSync);
				}
				return result;
			}
		}

		// Token: 0x0400005D RID: 93
		private List<SplitContainerParam> _SplitContainerParamList;

		// Token: 0x0400005E RID: 94
		private List<ChtCtrlParam> _ChtCtrlParamList;

		// Token: 0x0400005F RID: 95
		private TransTabCtrlParam _TransTabCtrlParam;

		// Token: 0x04000060 RID: 96
		private string _SingleFixedContent;
	}
}
