﻿using System;
using ns13;
using ns15;
using ns20;
using ns32;

namespace ns16
{
	// Token: 0x020003CE RID: 974
	internal static class Class518
	{
		// Token: 0x060026F8 RID: 9976 RVA: 0x0000EFDA File Offset: 0x0000D1DA
		public static Class516 smethod_0(UIntPtr uintptr_0, Enum34 enum34_0, Enum32 enum32_0, string string_0, out Enum33 enum33_0)
		{
			return Class519.OpenKey(uintptr_0, enum34_0, enum32_0, string_0, out enum33_0);
		}
	}
}
