﻿using System;
using System.ComponentModel;
using ns9;
using TEx;

namespace ns27
{
	// Token: 0x02000202 RID: 514
	[DesignerCategory("Code")]
	internal sealed class Class67 : Class65
	{
		// Token: 0x06001505 RID: 5381 RVA: 0x000086C6 File Offset: 0x000068C6
		public Class67(DrawLineStyle drawLineStyle_1 = null, IContainer icontainer_1 = null) : base(typeof(DrawLineWidth), drawLineStyle_1, icontainer_1)
		{
		}

		// Token: 0x06001506 RID: 5382 RVA: 0x0008E818 File Offset: 0x0008CA18
		protected override float vmethod_1(int int_0)
		{
			return DrawLineStyle.smethod_0((DrawLineWidth)int_0);
		}
	}
}
