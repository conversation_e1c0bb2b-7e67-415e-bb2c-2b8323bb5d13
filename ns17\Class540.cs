﻿using System;

namespace ns17
{
	// Token: 0x020003F9 RID: 1017
	internal sealed class Class540
	{
		// Token: 0x060027A6 RID: 10150 RVA: 0x0000F3D2 File Offset: 0x0000D5D2
		public Class540(object object_1, bool bool_1) : this(object_1, (object_1 != null) ? object_1.GetType() : null, bool_1)
		{
		}

		// Token: 0x060027A7 RID: 10151 RVA: 0x0000F3E8 File Offset: 0x0000D5E8
		public Class540(object object_1, Type type_1, bool bool_1)
		{
			this.object_0 = object_1;
			this.type_0 = type_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x170006CE RID: 1742
		// (get) Token: 0x060027A8 RID: 10152 RVA: 0x0000F405 File Offset: 0x0000D605
		public bool FirstLevel
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x060027A9 RID: 10153 RVA: 0x0000F40D File Offset: 0x0000D60D
		public object method_0()
		{
			return this.object_0;
		}

		// Token: 0x060027AA RID: 10154 RVA: 0x0000F415 File Offset: 0x0000D615
		public Type method_1()
		{
			return this.type_0;
		}

		// Token: 0x040013B6 RID: 5046
		private readonly Type type_0;

		// Token: 0x040013B7 RID: 5047
		private readonly object object_0;

		// Token: 0x040013B8 RID: 5048
		private readonly bool bool_0;
	}
}
