﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns18;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002D1 RID: 721
	public sealed partial class FormIndFunction : Form
	{
		// Token: 0x06002043 RID: 8259 RVA: 0x000E56A0 File Offset: 0x000E38A0
		public FormIndFunction()
		{
			this.method_6();
			base.Load += this.FormIndFunction_Load;
			base.AcceptButton = this.button_1;
			this.FunctionName = Class521.smethod_0(1449);
			this.dataGridView_0.Click += this.dataGridView_0_Click;
			this.dataGridView_1.Click += this.dataGridView_1_Click;
			this.dataGridView_1.DoubleClick += this.dataGridView_1_DoubleClick;
			Base.UI.smethod_55(this.dataGridView_0);
			Base.UI.smethod_55(this.dataGridView_1);
			base.AcceptButton = this.button_1;
			base.CancelButton = this.button_2;
			this.textBox_0.Focus();
			Base.UI.smethod_54(this);
		}

		// Token: 0x06002044 RID: 8260 RVA: 0x0000D1F5 File Offset: 0x0000B3F5
		private void dataGridView_1_DoubleClick(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x06002045 RID: 8261 RVA: 0x0000D1FF File Offset: 0x0000B3FF
		private void dataGridView_1_Click(object sender, EventArgs e)
		{
			this.dataGridView_1.CurrentRow.Selected = true;
		}

		// Token: 0x06002046 RID: 8262 RVA: 0x000E577C File Offset: 0x000E397C
		private List<IndFuncAttri> method_0(string string_1)
		{
			FormIndFunction.Class376 @class = new FormIndFunction.Class376();
			@class.string_0 = string_1;
			List<IndFuncAttri> result;
			if (@class.string_0 == Class521.smethod_0(95670))
			{
				result = this.list_0;
			}
			else
			{
				IGrouping<string, IndFuncAttri> grouping = this.list_0.GroupBy(new Func<IndFuncAttri, string>(FormIndFunction.<>c.<>9.method_0)).SingleOrDefault(new Func<IGrouping<string, IndFuncAttri>, bool>(@class.method_0));
				if (grouping != null)
				{
					result = new List<IndFuncAttri>(grouping.ToArray<IndFuncAttri>());
				}
				else
				{
					result = new List<IndFuncAttri>();
				}
			}
			return result;
		}

		// Token: 0x06002047 RID: 8263 RVA: 0x000E580C File Offset: 0x000E3A0C
		private void method_1(List<IndFuncAttri> list_1)
		{
			if (list_1.Any<IndFuncAttri>())
			{
				List<NameScript> dataSource = this.method_3(list_1);
				this.dataGridView_1.DataSource = dataSource;
				this.dataGridView_1.Columns[Class521.smethod_0(1858)].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
				this.dataGridView_1.Columns[Class521.smethod_0(95687)].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
			}
		}

		// Token: 0x06002048 RID: 8264 RVA: 0x000E5878 File Offset: 0x000E3A78
		private void method_2(string string_1)
		{
			try
			{
				List<IndFuncAttri> list_ = this.method_0(string_1);
				this.method_1(list_);
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x06002049 RID: 8265 RVA: 0x000E58AC File Offset: 0x000E3AAC
		private void dataGridView_0_Click(object sender, EventArgs e)
		{
			try
			{
				string string_ = this.dataGridView_0.CurrentRow.Cells[Class521.smethod_0(95696)].Value.ToString();
				this.method_2(string_);
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x0600204A RID: 8266 RVA: 0x000E5904 File Offset: 0x000E3B04
		private List<NameScript> method_3(List<IndFuncAttri> list_1)
		{
			List<NameScript> list = new List<NameScript>();
			foreach (IndFuncAttri indFuncAttri in list_1)
			{
				NameScript item = new NameScript(indFuncAttri.Name, indFuncAttri.ParamStr + indFuncAttri.Script);
				list.Add(item);
			}
			return list;
		}

		// Token: 0x0600204B RID: 8267 RVA: 0x000E5980 File Offset: 0x000E3B80
		private void FormIndFunction_Load(object sender, EventArgs e)
		{
			this.dataGridView_1.ColumnHeadersVisible = false;
			this.dataGridView_1.RowHeadersVisible = false;
			this.dataGridView_1.ReadOnly = true;
			this.dataGridView_1.CellBorderStyle = DataGridViewCellBorderStyle.None;
			DataGridViewTextBoxColumn dataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
			dataGridViewTextBoxColumn.Name = Class521.smethod_0(95696);
			dataGridViewTextBoxColumn.HeaderText = Class521.smethod_0(95705);
			this.dataGridView_0.Columns.Add(dataGridViewTextBoxColumn);
			this.dataGridView_0.Columns[0].Width = this.dataGridView_0.Width;
			this.dataGridView_0.ColumnHeadersVisible = false;
			this.dataGridView_0.RowHeadersVisible = false;
			this.dataGridView_0.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridView_0.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridView_0.ReadOnly = true;
			this.list_0 = ParserEnvironment.IndFuncAttriList;
			List<string> list = this.list_0.GroupBy(new Func<IndFuncAttri, string>(FormIndFunction.<>c.<>9.method_1)).Select(new Func<IGrouping<string, IndFuncAttri>, string>(FormIndFunction.<>c.<>9.method_2)).ToList<string>();
			int index = this.dataGridView_0.Rows.Add();
			this.dataGridView_0.Rows[index].Cells[Class521.smethod_0(95696)].Value = Class521.smethod_0(95670);
			foreach (string text in list)
			{
				string[] array = text.Split(new char[]
				{
					'.'
				});
				if (array.Count<string>() == 2)
				{
					int index2 = this.dataGridView_0.Rows.Add();
					this.dataGridView_0.Rows[index2].Cells[Class521.smethod_0(95696)].Value = array[1];
				}
			}
			this.textBox_0.Select();
			this.method_2(Class521.smethod_0(95670));
		}

		// Token: 0x0600204C RID: 8268 RVA: 0x000E5BA8 File Offset: 0x000E3DA8
		private void method_4()
		{
			if (this.dataGridView_1.Rows.Count > 0 && this.dataGridView_1.CurrentRow != null)
			{
				this.FunctionName = this.dataGridView_1.CurrentRow.Cells[Class521.smethod_0(1858)].Value.ToString();
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Cancel;
			}
			base.Close();
		}

		// Token: 0x170005B1 RID: 1457
		// (get) Token: 0x0600204D RID: 8269 RVA: 0x000E5C1C File Offset: 0x000E3E1C
		// (set) Token: 0x0600204E RID: 8270 RVA: 0x0000D214 File Offset: 0x0000B414
		public string FunctionName { get; set; }

		// Token: 0x0600204F RID: 8271 RVA: 0x000E5C34 File Offset: 0x000E3E34
		private void button_1_Click(object sender, EventArgs e)
		{
			this.method_4();
			if (this.dataGridView_1.Rows.Count > 0 && this.dataGridView_1.CurrentRow != null)
			{
				this.FunctionName = this.dataGridView_1.CurrentRow.Cells[Class521.smethod_0(1858)].Value.ToString();
			}
		}

		// Token: 0x06002050 RID: 8272 RVA: 0x0000460C File Offset: 0x0000280C
		private void button_2_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x06002051 RID: 8273 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_5()
		{
		}

		// Token: 0x06002052 RID: 8274 RVA: 0x000E5C98 File Offset: 0x000E3E98
		private void button_0_Click(object sender, EventArgs e)
		{
			FormIndFunction.Class377 @class = new FormIndFunction.Class377();
			@class.string_0 = this.textBox_0.Text.Trim();
			List<IndFuncAttri> list = this.list_0.Where(new Func<IndFuncAttri, bool>(@class.method_0)).ToList<IndFuncAttri>();
			list.Sort();
			this.method_1(list);
		}

		// Token: 0x06002053 RID: 8275 RVA: 0x000E5CF0 File Offset: 0x000E3EF0
		private void textBox_0_TextChanged(object sender, EventArgs e)
		{
			FormIndFunction.Class378 @class = new FormIndFunction.Class378();
			@class.string_0 = this.textBox_0.Text.Trim();
			List<IndFuncAttri> list = this.list_0.Where(new Func<IndFuncAttri, bool>(@class.method_0)).ToList<IndFuncAttri>();
			list.Sort();
			this.method_1(list);
		}

		// Token: 0x06002054 RID: 8276 RVA: 0x0000D21F File Offset: 0x0000B41F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002055 RID: 8277 RVA: 0x000E5D48 File Offset: 0x000E3F48
		private void method_6()
		{
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.button_0 = new Button();
			this.textBox_0 = new TextBox();
			this.button_1 = new Button();
			this.button_2 = new Button();
			this.dataGridView_0 = new DataGridView();
			this.dataGridView_1 = new DataGridView();
			((ISupportInitialize)this.dataGridView_0).BeginInit();
			((ISupportInitialize)this.dataGridView_1).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(17, 17);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(82, 15);
			this.label_0.TabIndex = 3;
			this.label_0.Text = Class521.smethod_0(95714);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(238, 17);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(82, 15);
			this.label_1.TabIndex = 3;
			this.label_1.Text = Class521.smethod_0(95735);
			this.button_0.Location = new Point(18, 343);
			this.button_0.Margin = new Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(95756);
			this.button_0.Size = new Size(100, 32);
			this.button_0.TabIndex = 2;
			this.button_0.Text = Class521.smethod_0(95773);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.textBox_0.CharacterCasing = CharacterCasing.Upper;
			this.textBox_0.Location = new Point(130, 347);
			this.textBox_0.Margin = new Padding(3, 2, 3, 2);
			this.textBox_0.Name = Class521.smethod_0(95782);
			this.textBox_0.Size = new Size(205, 25);
			this.textBox_0.TabIndex = 3;
			this.textBox_0.TextChanged += this.textBox_0_TextChanged;
			this.button_1.Location = new Point(706, 343);
			this.button_1.Margin = new Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(95505);
			this.button_1.Size = new Size(110, 32);
			this.button_1.TabIndex = 4;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.button_2.Location = new Point(830, 343);
			this.button_2.Margin = new Padding(3, 2, 3, 2);
			this.button_2.Name = Class521.smethod_0(95803);
			this.button_2.Size = new Size(110, 32);
			this.button_2.TabIndex = 5;
			this.button_2.Text = Class521.smethod_0(5783);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_2.Click += this.button_2_Click;
			this.dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_0.Location = new Point(19, 40);
			this.dataGridView_0.Margin = new Padding(3, 2, 3, 2);
			this.dataGridView_0.Name = Class521.smethod_0(95820);
			this.dataGridView_0.RowTemplate.Height = 20;
			this.dataGridView_0.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView_0.Size = new Size(204, 287);
			this.dataGridView_0.TabIndex = 0;
			this.dataGridView_1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView_1.Location = new Point(240, 40);
			this.dataGridView_1.Margin = new Padding(3, 2, 3, 2);
			this.dataGridView_1.Name = Class521.smethod_0(95849);
			this.dataGridView_1.RowTemplate.Height = 20;
			this.dataGridView_1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView_1.Size = new Size(700, 287);
			this.dataGridView_1.TabIndex = 1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(959, 392);
			base.Controls.Add(this.dataGridView_1);
			base.Controls.Add(this.dataGridView_0);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.button_2);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.Margin = new Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(95878);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(95899);
			((ISupportInitialize)this.dataGridView_0).EndInit();
			((ISupportInitialize)this.dataGridView_1).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000FD5 RID: 4053
		private List<IndFuncAttri> list_0 = new List<IndFuncAttri>();

		// Token: 0x04000FD6 RID: 4054
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000FD7 RID: 4055
		private IContainer icontainer_0;

		// Token: 0x04000FD8 RID: 4056
		private Label label_0;

		// Token: 0x04000FD9 RID: 4057
		private Label label_1;

		// Token: 0x04000FDA RID: 4058
		private Button button_0;

		// Token: 0x04000FDB RID: 4059
		private TextBox textBox_0;

		// Token: 0x04000FDC RID: 4060
		private Button button_1;

		// Token: 0x04000FDD RID: 4061
		private Button button_2;

		// Token: 0x04000FDE RID: 4062
		private DataGridView dataGridView_0;

		// Token: 0x04000FDF RID: 4063
		private DataGridView dataGridView_1;

		// Token: 0x020002D2 RID: 722
		[CompilerGenerated]
		private sealed class Class376
		{
			// Token: 0x06002057 RID: 8279 RVA: 0x000E6358 File Offset: 0x000E4558
			internal bool method_0(IGrouping<string, IndFuncAttri> igrouping_0)
			{
				return igrouping_0.Key.Contains(this.string_0);
			}

			// Token: 0x04000FE0 RID: 4064
			public string string_0;
		}

		// Token: 0x020002D4 RID: 724
		[CompilerGenerated]
		private sealed class Class377
		{
			// Token: 0x0600205E RID: 8286 RVA: 0x000E63AC File Offset: 0x000E45AC
			internal bool method_0(IndFuncAttri indFuncAttri_0)
			{
				return indFuncAttri_0.Name.IndexOf(this.string_0) > 0;
			}

			// Token: 0x04000FE5 RID: 4069
			public string string_0;
		}

		// Token: 0x020002D5 RID: 725
		[CompilerGenerated]
		private sealed class Class378
		{
			// Token: 0x06002060 RID: 8288 RVA: 0x000E63D4 File Offset: 0x000E45D4
			internal bool method_0(IndFuncAttri indFuncAttri_0)
			{
				return indFuncAttri_0.Name.IndexOf(this.string_0) >= 0;
			}

			// Token: 0x04000FE6 RID: 4070
			public string string_0;
		}
	}
}
