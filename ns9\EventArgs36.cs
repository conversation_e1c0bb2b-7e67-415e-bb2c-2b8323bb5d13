﻿using System;
using System.Security;

namespace ns9
{
	// Token: 0x020003FF RID: 1023
	internal sealed class EventArgs36 : EventArgs
	{
		// Token: 0x170006D6 RID: 1750
		// (get) Token: 0x060027C7 RID: 10183 RVA: 0x0000F517 File Offset: 0x0000D717
		public SecurityException SecurityException
		{
			get
			{
				return this.securityException_0;
			}
		}

		// Token: 0x170006D7 RID: 1751
		// (get) Token: 0x060027C8 RID: 10184 RVA: 0x0000F51F File Offset: 0x0000D71F
		public string SecurityMessage
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x170006D8 RID: 1752
		// (get) Token: 0x060027C9 RID: 10185 RVA: 0x0000F527 File Offset: 0x0000D727
		public bool CanContinue
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x170006D9 RID: 1753
		// (get) Token: 0x060027CA RID: 10186 RVA: 0x0000F52F File Offset: 0x0000D72F
		// (set) Token: 0x060027CB RID: 10187 RVA: 0x0000F537 File Offset: 0x0000D737
		public bool TryToContinue
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170006DA RID: 1754
		// (get) Token: 0x060027CC RID: 10188 RVA: 0x0000F540 File Offset: 0x0000D740
		// (set) Token: 0x060027CD RID: 10189 RVA: 0x0000F548 File Offset: 0x0000D748
		public bool ReportException
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x060027CE RID: 10190 RVA: 0x0000F551 File Offset: 0x0000D751
		public EventArgs36(SecurityException securityException_1)
		{
			this.securityException_0 = securityException_1;
		}

		// Token: 0x060027CF RID: 10191 RVA: 0x0000F572 File Offset: 0x0000D772
		public EventArgs36(SecurityException securityException_1, bool bool_3) : this(securityException_1)
		{
			this.bool_2 = bool_3;
		}

		// Token: 0x060027D0 RID: 10192 RVA: 0x0000F582 File Offset: 0x0000D782
		public EventArgs36(string string_1, bool bool_3) : this(new SecurityException(string_1), bool_3)
		{
			this.string_0 = string_1;
		}

		// Token: 0x040013C0 RID: 5056
		private SecurityException securityException_0;

		// Token: 0x040013C1 RID: 5057
		private string string_0 = string.Empty;

		// Token: 0x040013C2 RID: 5058
		private bool bool_0;

		// Token: 0x040013C3 RID: 5059
		private bool bool_1;

		// Token: 0x040013C4 RID: 5060
		private bool bool_2 = true;
	}
}
