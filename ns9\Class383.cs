﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns1;
using ns10;
using ns11;
using ns12;
using ns16;
using ns17;
using ns18;
using ns20;
using ns21;
using ns22;
using ns24;
using ns25;
using ns26;
using ns3;
using ns31;
using ns5;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns9
{
	// Token: 0x020002E2 RID: 738
	internal abstract class Class383
	{
		// Token: 0x060020D0 RID: 8400 RVA: 0x0000D4A2 File Offset: 0x0000B6A2
		public Class383(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			this.indEx_0 = indEx_1;
			this.IndData = dataArray_1;
			this.DP = dataProvider_1;
		}

		// Token: 0x060020D1 RID: 8401 RVA: 0x000E9988 File Offset: 0x000E7B88
		protected DateTime method_0(int int_0)
		{
			if (int_0 >= this.DP.PeriodHisDataList.Count)
			{
				Class184.smethod_0(new Exception(string.Concat(new object[]
				{
					Class521.smethod_0(97062),
					int_0,
					Class521.smethod_0(97079),
					this.indEx_0.method_7()
				})));
				int_0 = this.DP.PeriodHisDataList.Count - 1;
			}
			return this.DP.PeriodHisDataList.Keys[int_0];
		}

		// Token: 0x060020D2 RID: 8402
		protected abstract PointPair vmethod_0(int int_0, DataArray dataArray_1);

		// Token: 0x060020D3 RID: 8403 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_1(ZedGraphControl zedGraphControl_0)
		{
		}

		// Token: 0x060020D4 RID: 8404 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_2(int int_0)
		{
		}

		// Token: 0x060020D5 RID: 8405 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_3(int int_0, DataArray dataArray_1)
		{
		}

		// Token: 0x060020D6 RID: 8406 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void vmethod_4(int int_0, DataArray dataArray_1)
		{
		}

		// Token: 0x060020D7 RID: 8407
		public abstract double vmethod_5(int int_0, HisData hisData_0);

		// Token: 0x060020D8 RID: 8408
		public abstract void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0);

		// Token: 0x060020D9 RID: 8409 RVA: 0x000E9A1C File Offset: 0x000E7C1C
		public static List<ShapeCurve> smethod_0(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			List<ShapeCurve> list = new List<ShapeCurve>();
			string shapeStr = dataArray_1.ShapeStr;
			List<ShapeCurve> result;
			if (shapeStr == null)
			{
				ShapeCurve item = Class383.smethod_2(dataArray_1, dataProvider_1, indEx_1);
				list.Add(item);
				result = list;
			}
			else if (shapeStr.Equals(Class521.smethod_0(96756), StringComparison.InvariantCultureIgnoreCase))
			{
				List<DataArray> list2 = Class383.smethod_1(dataArray_1);
				if (list2.Count != 2)
				{
					throw new Exception(Class521.smethod_0(97108));
				}
				Class391 item2 = new Class391(list2[0], dataProvider_1, indEx_1);
				Class391 item3 = new Class391(list2[1], dataProvider_1, indEx_1);
				list.Add(item2);
				list.Add(item3);
				result = list;
			}
			else
			{
				ShapeCurve item4 = Class383.smethod_2(dataArray_1, dataProvider_1, indEx_1);
				list.Add(item4);
				result = list;
			}
			return result;
		}

		// Token: 0x060020DA RID: 8410 RVA: 0x000E9ACC File Offset: 0x000E7CCC
		public static List<DataArray> smethod_1(DataArray dataArray_1)
		{
			List<DataArray> list = new List<DataArray>();
			string shapeStr = dataArray_1.ShapeStr;
			if (shapeStr == null)
			{
				throw new Exception(Class521.smethod_0(97161));
			}
			if (shapeStr.ToUpper() == Class521.smethod_0(96756))
			{
				DataArray dataArray = (DataArray)dataArray_1.Clone();
				dataArray.OtherDataArrayList = dataArray_1.OtherDataArrayList;
				dataArray.ColorStr = dataArray_1.SingleData[Class521.smethod_0(97210)].ToString();
				dataArray.LineTypeStr = dataArray_1.LineTypeStr;
				dataArray.LineWithStr = dataArray_1.LineWithStr;
				list.Add(dataArray);
				DataArray dataArray2 = (DataArray)dataArray_1.Clone();
				dataArray2.OtherDataArrayList.Add((DataArray)dataArray_1.OtherDataArrayList[0].Clone());
				for (int i = 0; i < dataArray2.OtherDataArrayList[0].Data.Length; i++)
				{
					if (dataArray2.OtherDataArrayList[0].Data[i] == 0.0)
					{
						dataArray2.OtherDataArrayList[0].Data[i] = 1.0;
					}
					else
					{
						dataArray2.OtherDataArrayList[0].Data[i] = 0.0;
					}
				}
				dataArray2.ColorStr = dataArray_1.SingleData[Class521.smethod_0(97219)].ToString();
				dataArray2.LineTypeStr = dataArray_1.LineTypeStr;
				dataArray2.LineWithStr = dataArray_1.LineWithStr;
				list.Add(dataArray2);
				return list;
			}
			throw new Exception(Class521.smethod_0(97161));
		}

		// Token: 0x060020DB RID: 8411 RVA: 0x000E9C64 File Offset: 0x000E7E64
		public static ShapeCurve smethod_2(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			Class383.Class404 @class = new Class383.Class404();
			string shapeStr = dataArray_1.ShapeStr;
			ShapeCurve result;
			if (shapeStr == null)
			{
				result = new Class390(dataArray_1, dataProvider_1, indEx_1);
			}
			else
			{
				@class.string_0 = shapeStr.ToUpper();
				if (ParserEnvironment.string_0.Any(new Func<string, bool>(@class.method_0)))
				{
					if (@class.string_0 == Class521.smethod_0(97228))
					{
						result = new Class390(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97237))
					{
						result = new Class392(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97250))
					{
						result = new Class393(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97267))
					{
						result = new Class401(dataArray_1, dataProvider_1, indEx_1);
					}
					else
					{
						if (!(@class.string_0 == Class521.smethod_0(97276)))
						{
							throw new Exception(string.Format(Class521.smethod_0(97289), shapeStr));
						}
						result = new Class400(dataArray_1, dataProvider_1, indEx_1);
					}
				}
				else if (!(@class.string_0 == Class521.smethod_0(97326)) && !(@class.string_0 == Class521.smethod_0(97339)))
				{
					if (@class.string_0 == Class521.smethod_0(97356))
					{
						result = new Class394(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97369))
					{
						result = new Class395(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97386))
					{
						result = new Class396(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97399))
					{
						result = new Class397(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97412))
					{
						result = new ShapeDrawICON(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97425))
					{
						result = new Class398(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97438))
					{
						result = new Class391(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97451))
					{
						result = new Class388(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97460))
					{
						result = new Class386(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97477))
					{
						result = new Class389(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97498))
					{
						result = new Class384(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == Class521.smethod_0(97511))
					{
						result = new Class385(dataArray_1, dataProvider_1, indEx_1);
					}
					else
					{
						if (!(@class.string_0 == Class521.smethod_0(97528)))
						{
							throw new Exception(string.Format(Class521.smethod_0(97289), shapeStr));
						}
						result = new Class387(dataArray_1, dataProvider_1, indEx_1);
					}
				}
				else
				{
					result = new Class399(dataArray_1, dataProvider_1, indEx_1);
				}
			}
			return result;
		}

		// Token: 0x060020DC RID: 8412 RVA: 0x0000D4C1 File Offset: 0x0000B6C1
		public virtual void vmethod_7(int int_0)
		{
			this.rollingPointPairList_0 = new RollingPointPairList(int_0);
		}

		// Token: 0x170005BB RID: 1467
		// (get) Token: 0x060020DD RID: 8413 RVA: 0x000E9FB4 File Offset: 0x000E81B4
		// (set) Token: 0x060020DE RID: 8414 RVA: 0x0000D4D1 File Offset: 0x0000B6D1
		public RollingPointPairList DataView
		{
			get
			{
				return this.rollingPointPairList_0;
			}
			private set
			{
				this.rollingPointPairList_0 = value;
			}
		}

		// Token: 0x170005BC RID: 1468
		// (get) Token: 0x060020DF RID: 8415 RVA: 0x000E9FCC File Offset: 0x000E81CC
		// (set) Token: 0x060020E0 RID: 8416 RVA: 0x0000D4DC File Offset: 0x0000B6DC
		public DataArray IndData { get; protected set; }

		// Token: 0x170005BD RID: 1469
		// (get) Token: 0x060020E1 RID: 8417 RVA: 0x000E9FE4 File Offset: 0x000E81E4
		// (set) Token: 0x060020E2 RID: 8418 RVA: 0x0000D4E7 File Offset: 0x0000B6E7
		protected DataProvider DP { get; set; }

		// Token: 0x170005BE RID: 1470
		// (get) Token: 0x060020E3 RID: 8419 RVA: 0x000E9FFC File Offset: 0x000E81FC
		protected int KCount
		{
			get
			{
				return this.DP.PeriodHisDataList.Keys.Count;
			}
		}

		// Token: 0x04001019 RID: 4121
		protected IndEx indEx_0;

		// Token: 0x0400101A RID: 4122
		protected RollingPointPairList rollingPointPairList_0;

		// Token: 0x0400101B RID: 4123
		[CompilerGenerated]
		private DataArray dataArray_0;

		// Token: 0x0400101C RID: 4124
		[CompilerGenerated]
		private DataProvider dataProvider_0;

		// Token: 0x020002E3 RID: 739
		[CompilerGenerated]
		private sealed class Class404
		{
			// Token: 0x060020E5 RID: 8421 RVA: 0x000EA024 File Offset: 0x000E8224
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x0400101D RID: 4125
			public string string_0;
		}
	}
}
