﻿using System;
using ns18;

namespace ns3
{
	// Token: 0x020003C3 RID: 963
	internal sealed class Class512
	{
		// Token: 0x170006B8 RID: 1720
		// (get) Token: 0x060026D1 RID: 9937 RVA: 0x0000EEAF File Offset: 0x0000D0AF
		public static Version Version
		{
			get
			{
				return Class512.version_0;
			}
		}

		// Token: 0x170006B9 RID: 1721
		// (get) Token: 0x060026D2 RID: 9938 RVA: 0x0000EEB6 File Offset: 0x0000D0B6
		public static string AppName
		{
			get
			{
				return Class512.AppNameMinusVersion + Class521.smethod_0(3636) + Class512.MajorVersion;
			}
		}

		// Token: 0x170006BA RID: 1722
		// (get) Token: 0x060026D3 RID: 9939 RVA: 0x0000EED6 File Offset: 0x0000D0D6
		public static string TitleVersion
		{
			get
			{
				string result;
				if ((result = Class512.string_0) == null)
				{
					result = (Class512.string_0 = string.Format(Class521.smethod_0(116058), Class512.version_0.Major, Class512.version_0.Minor));
				}
				return result;
			}
		}

		// Token: 0x170006BB RID: 1723
		// (get) Token: 0x060026D4 RID: 9940 RVA: 0x0000EF14 File Offset: 0x0000D114
		public static int MajorVersion
		{
			get
			{
				return Class512.version_0.Major;
			}
		}

		// Token: 0x170006BC RID: 1724
		// (get) Token: 0x060026D5 RID: 9941 RVA: 0x0000EF20 File Offset: 0x0000D120
		public static string AppNameMinusVersion
		{
			get
			{
				return Class521.smethod_0(116071);
			}
		}

		// Token: 0x060026D6 RID: 9942 RVA: 0x00002D25 File Offset: 0x00000F25
		private Class512()
		{
		}

		// Token: 0x040012BE RID: 4798
		private static Version version_0 = new Version(Class521.smethod_0(116092));

		// Token: 0x040012BF RID: 4799
		private static string string_0 = null;
	}
}
