﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using ns18;

namespace TEx.Trading
{
	// Token: 0x020003B0 RID: 944
	[Serializable]
	internal sealed class Account : INotifyPropertyChanging, INotifyPropertyChanged
	{
		// Token: 0x06002648 RID: 9800 RVA: 0x00002D25 File Offset: 0x00000F25
		public Account()
		{
		}

		// Token: 0x06002649 RID: 9801 RVA: 0x00105088 File Offset: 0x00103288
		protected Account(SerializationInfo info, StreamingContext context)
		{
			this._ID = info.GetInt32(Class521.smethod_0(33549));
			this._AcctName = info.GetString(Class521.smethod_0(16490));
			this._IniBal = info.GetDecimal(Class521.smethod_0(110497));
			this._EndingBal = (decimal?)info.GetValue(Class521.smethod_0(110506), typeof(decimal?));
			this._LastSymbID = (int?)info.GetValue(Class521.smethod_0(110519), typeof(int?));
			this._LastSymbDT = (DateTime?)info.GetValue(Class521.smethod_0(110536), typeof(DateTime?));
			this._IsReadOnly = info.GetBoolean(Class521.smethod_0(110553));
			this._OpenDate = (DateTime?)info.GetValue(Class521.smethod_0(110570), typeof(DateTime?));
			this._UpdateTime = (DateTime?)info.GetValue(Class521.smethod_0(110583), typeof(DateTime?));
			this._Notes = info.GetString(Class521.smethod_0(33326));
			try
			{
				this._LastSymbIdAndDtList = (List<KeyValuePair<int, DateTime>>)info.GetValue(Class521.smethod_0(110600), typeof(List<KeyValuePair<int, DateTime>>));
			}
			catch
			{
			}
		}

		// Token: 0x0600264A RID: 9802 RVA: 0x00105200 File Offset: 0x00103400
		public void vmethod_0(SerializationInfo serializationInfo_0, StreamingContext streamingContext_0)
		{
			serializationInfo_0.AddValue(Class521.smethod_0(33549), this._ID);
			serializationInfo_0.AddValue(Class521.smethod_0(16490), this._AcctName);
			serializationInfo_0.AddValue(Class521.smethod_0(110497), this._IniBal);
			serializationInfo_0.AddValue(Class521.smethod_0(110506), this._EndingBal);
			serializationInfo_0.AddValue(Class521.smethod_0(110553), this._IsReadOnly);
			serializationInfo_0.AddValue(Class521.smethod_0(110519), this._LastSymbID);
			serializationInfo_0.AddValue(Class521.smethod_0(110536), this._LastSymbDT);
			serializationInfo_0.AddValue(Class521.smethod_0(110600), this._LastSymbIdAndDtList);
			serializationInfo_0.AddValue(Class521.smethod_0(110570), this._OpenDate);
			serializationInfo_0.AddValue(Class521.smethod_0(110583), this._UpdateTime);
			serializationInfo_0.AddValue(Class521.smethod_0(33326), this._Notes);
		}

		// Token: 0x17000681 RID: 1665
		// (get) Token: 0x0600264B RID: 9803 RVA: 0x0010531C File Offset: 0x0010351C
		// (set) Token: 0x0600264C RID: 9804 RVA: 0x0000EA12 File Offset: 0x0000CC12
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if (this._ID != value)
				{
					this.vmethod_1();
					this._ID = value;
					this.vmethod_2(Class521.smethod_0(33549));
				}
			}
		}

		// Token: 0x17000682 RID: 1666
		// (get) Token: 0x0600264D RID: 9805 RVA: 0x00105334 File Offset: 0x00103534
		// (set) Token: 0x0600264E RID: 9806 RVA: 0x0000EA3C File Offset: 0x0000CC3C
		public string AcctName
		{
			get
			{
				return this._AcctName;
			}
			set
			{
				if (this._AcctName != value)
				{
					this.vmethod_1();
					this._AcctName = value;
					this.vmethod_2(Class521.smethod_0(16490));
				}
			}
		}

		// Token: 0x17000683 RID: 1667
		// (get) Token: 0x0600264F RID: 9807 RVA: 0x0010534C File Offset: 0x0010354C
		// (set) Token: 0x06002650 RID: 9808 RVA: 0x0000EA6B File Offset: 0x0000CC6B
		public decimal IniBal
		{
			get
			{
				return this._IniBal;
			}
			set
			{
				if (this._IniBal != value)
				{
					this.vmethod_1();
					this._IniBal = value;
					this.vmethod_2(Class521.smethod_0(110497));
				}
			}
		}

		// Token: 0x17000684 RID: 1668
		// (get) Token: 0x06002651 RID: 9809 RVA: 0x00105364 File Offset: 0x00103564
		// (set) Token: 0x06002652 RID: 9810 RVA: 0x0010537C File Offset: 0x0010357C
		public decimal? EndingBal
		{
			get
			{
				return this._EndingBal;
			}
			set
			{
				decimal? endingBal = this._EndingBal;
				decimal? num = value;
				if (!(endingBal.GetValueOrDefault() == num.GetValueOrDefault() & endingBal != null == (num != null)))
				{
					this.vmethod_1();
					this._EndingBal = value;
					this.vmethod_2(Class521.smethod_0(110506));
				}
			}
		}

		// Token: 0x17000685 RID: 1669
		// (get) Token: 0x06002653 RID: 9811 RVA: 0x001053D8 File Offset: 0x001035D8
		// (set) Token: 0x06002654 RID: 9812 RVA: 0x001053F0 File Offset: 0x001035F0
		public DateTime? OpenDate
		{
			get
			{
				return this._OpenDate;
			}
			set
			{
				if (this._OpenDate != value)
				{
					this.vmethod_1();
					this._OpenDate = value;
					this.vmethod_2(Class521.smethod_0(110570));
				}
			}
		}

		// Token: 0x17000686 RID: 1670
		// (get) Token: 0x06002655 RID: 9813 RVA: 0x0010545C File Offset: 0x0010365C
		// (set) Token: 0x06002656 RID: 9814 RVA: 0x00105474 File Offset: 0x00103674
		public int? LastSymbID
		{
			get
			{
				return this._LastSymbID;
			}
			set
			{
				int? lastSymbID = this._LastSymbID;
				int? num = value;
				if (!(lastSymbID.GetValueOrDefault() == num.GetValueOrDefault() & lastSymbID != null == (num != null)))
				{
					this.vmethod_1();
					this._LastSymbID = value;
					this.vmethod_2(Class521.smethod_0(110519));
				}
			}
		}

		// Token: 0x17000687 RID: 1671
		// (get) Token: 0x06002657 RID: 9815 RVA: 0x001054CC File Offset: 0x001036CC
		// (set) Token: 0x06002658 RID: 9816 RVA: 0x001054E4 File Offset: 0x001036E4
		public DateTime? LastSymbDT
		{
			get
			{
				return this._LastSymbDT;
			}
			set
			{
				if (this._LastSymbDT != value)
				{
					DateTime? dateTime = value;
					DateTime d = default(DateTime);
					if (dateTime != null && (dateTime == null || dateTime.GetValueOrDefault() == d))
					{
						throw new Exception(Class521.smethod_0(110629));
					}
					this.vmethod_1();
					this._LastSymbDT = value;
					this.vmethod_2(Class521.smethod_0(110536));
				}
			}
		}

		// Token: 0x17000688 RID: 1672
		// (get) Token: 0x06002659 RID: 9817 RVA: 0x00105590 File Offset: 0x00103790
		// (set) Token: 0x0600265A RID: 9818 RVA: 0x0000EA9A File Offset: 0x0000CC9A
		public List<KeyValuePair<int, DateTime>> LastSymbIdAndDtList
		{
			get
			{
				return this._LastSymbIdAndDtList;
			}
			set
			{
				if (this._LastSymbIdAndDtList != value)
				{
					this._LastSymbIdAndDtList = value;
				}
			}
		}

		// Token: 0x17000689 RID: 1673
		// (get) Token: 0x0600265B RID: 9819 RVA: 0x001055A8 File Offset: 0x001037A8
		// (set) Token: 0x0600265C RID: 9820 RVA: 0x0000EAAE File Offset: 0x0000CCAE
		public string Notes
		{
			get
			{
				return this._Notes;
			}
			set
			{
				if (this._Notes != value)
				{
					this.vmethod_1();
					this._Notes = value;
					this.vmethod_2(Class521.smethod_0(33326));
				}
			}
		}

		// Token: 0x1700068A RID: 1674
		// (get) Token: 0x0600265D RID: 9821 RVA: 0x001055C0 File Offset: 0x001037C0
		// (set) Token: 0x0600265E RID: 9822 RVA: 0x001055D8 File Offset: 0x001037D8
		public DateTime? UpdateTime
		{
			get
			{
				return this._UpdateTime;
			}
			set
			{
				if (this._UpdateTime != value)
				{
					this.vmethod_1();
					this._UpdateTime = value;
					this.vmethod_2(Class521.smethod_0(110583));
				}
			}
		}

		// Token: 0x1700068B RID: 1675
		// (get) Token: 0x0600265F RID: 9823 RVA: 0x00105644 File Offset: 0x00103844
		// (set) Token: 0x06002660 RID: 9824 RVA: 0x0000EADD File Offset: 0x0000CCDD
		public bool IsReadOnly
		{
			get
			{
				return this._IsReadOnly;
			}
			set
			{
				if (this._IsReadOnly != value)
				{
					this.vmethod_1();
					this._IsReadOnly = value;
					this.vmethod_2(Class521.smethod_0(110553));
				}
			}
		}

		// Token: 0x1700068C RID: 1676
		// (get) Token: 0x06002661 RID: 9825 RVA: 0x0010565C File Offset: 0x0010385C
		// (set) Token: 0x06002662 RID: 9826 RVA: 0x0000EB07 File Offset: 0x0000CD07
		public string UserName
		{
			get
			{
				return this._UserName;
			}
			set
			{
				this._UserName = value;
			}
		}

		// Token: 0x140000BA RID: 186
		// (add) Token: 0x06002663 RID: 9827 RVA: 0x00105674 File Offset: 0x00103874
		// (remove) Token: 0x06002664 RID: 9828 RVA: 0x001056AC File Offset: 0x001038AC
		public event PropertyChangingEventHandler PropertyChanging;

		// Token: 0x140000BB RID: 187
		// (add) Token: 0x06002665 RID: 9829 RVA: 0x001056E4 File Offset: 0x001038E4
		// (remove) Token: 0x06002666 RID: 9830 RVA: 0x0010571C File Offset: 0x0010391C
		public event PropertyChangedEventHandler PropertyChanged;

		// Token: 0x06002667 RID: 9831 RVA: 0x0000EB12 File Offset: 0x0000CD12
		protected void vmethod_1()
		{
			if (this.PropertyChanging != null)
			{
				this.PropertyChanging(this, Account.emptyChangingEventArgs);
			}
		}

		// Token: 0x06002668 RID: 9832 RVA: 0x0000EB2F File Offset: 0x0000CD2F
		protected void vmethod_2(string string_0)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(string_0));
			}
		}

		// Token: 0x04001272 RID: 4722
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(string.Empty);

		// Token: 0x04001273 RID: 4723
		private int _ID;

		// Token: 0x04001274 RID: 4724
		private string _AcctName;

		// Token: 0x04001275 RID: 4725
		private decimal _IniBal;

		// Token: 0x04001276 RID: 4726
		private decimal? _EndingBal;

		// Token: 0x04001277 RID: 4727
		private DateTime? _OpenDate;

		// Token: 0x04001278 RID: 4728
		private int? _LastSymbID;

		// Token: 0x04001279 RID: 4729
		private DateTime? _LastSymbDT;

		// Token: 0x0400127A RID: 4730
		private List<KeyValuePair<int, DateTime>> _LastSymbIdAndDtList;

		// Token: 0x0400127B RID: 4731
		private string _Notes;

		// Token: 0x0400127C RID: 4732
		private DateTime? _UpdateTime;

		// Token: 0x0400127D RID: 4733
		private bool _IsReadOnly;

		// Token: 0x0400127E RID: 4734
		private string _UserName;

		// Token: 0x0400127F RID: 4735
		[CompilerGenerated]
		private PropertyChangingEventHandler PropertyChanging;

		// Token: 0x04001280 RID: 4736
		[CompilerGenerated]
		private PropertyChangedEventHandler PropertyChanged;
	}
}
