﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns1;
using ns18;
using TEx.Chart;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020001CC RID: 460
	internal sealed class ChartTickV : Class226
	{
		// Token: 0x06001206 RID: 4614 RVA: 0x0000782F File Offset: 0x00005A2F
		public ChartTickV(ChtCtrl_Tick dChtCtrl, SplitterPanel panel) : base(dChtCtrl, panel)
		{
		}

		// Token: 0x06001207 RID: 4615 RVA: 0x000813E0 File Offset: 0x0007F5E0
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.ChartType = ChartType.TickVol;
			this.vmethod_2();
			this.method_81();
			this.vmethod_3();
			base.GraphPane.XAxis.MajorGrid.IsVisible = false;
			base.GraphPane.XAxis.MajorTic.Size = 0f;
			base.GraphPane.YAxis.Scale.Min = 0.0;
			base.GraphPane.YAxis.Scale.MaxAuto = true;
			this.ApplyTheme(Base.UI.Form.ChartTheme);
			base.ZedGraphControl.AxisChange();
			base.ZedGraphControl.Refresh();
			base.GraphPane.Y2Axis.ScaleFormatEvent += this.method_82;
		}

		// Token: 0x06001208 RID: 4616 RVA: 0x000814B4 File Offset: 0x0007F6B4
		public override void vmethod_2()
		{
			base.vmethod_2();
			GraphPane graphPane = base.GraphPane;
			PointPairList points = new PointPairList();
			string text = Class521.smethod_0(48770);
			this.stickItem_0 = graphPane.AddStick(text, points, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : this.color_0);
			this.stickItem_0.Tag = text;
			PointPairList points2 = new PointPairList();
			string text2 = Class521.smethod_0(48783);
			this.lineItem_0 = graphPane.AddCurve(text2, points2, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_1, SymbolType.None);
			this.lineItem_0.Tag = text2;
			this.double_2 = 0.0;
			this.double_3 = 0.0;
			this.double_4 = double.MaxValue;
			this.double_0 = 1.0;
			this.method_78();
		}

		// Token: 0x06001209 RID: 4617 RVA: 0x000815A0 File Offset: 0x0007F7A0
		public override void vmethod_3()
		{
			base.vmethod_3();
			GraphPane graphPane = base.GraphPane;
			graphPane.Margin.Left = 36f;
			graphPane.Margin.Right = 5f;
			graphPane.Margin.Top = 0f;
			graphPane.Margin.Bottom = 4f;
			graphPane.YAxis.Scale.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : this.color_0);
			graphPane.Y2Axis.Scale.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_1);
			graphPane.XAxis.Scale.Format = Class521.smethod_0(48020);
		}

		// Token: 0x0600120A RID: 4618 RVA: 0x00081670 File Offset: 0x0007F870
		private void method_78()
		{
			GraphPane graphPane = base.GraphPane;
			List<TextObj> list = base.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartTickV.<>c.<>9.method_0)).Cast<TextObj>().ToList<TextObj>();
			for (int i = 0; i < list.Count; i++)
			{
				base.GraphPane.GraphObjList.Remove(list[i]);
				list[i] = null;
			}
			if (this.list_1 != null)
			{
				for (int j = 0; j < this.list_1.Count; j++)
				{
					this.list_1[j] = null;
				}
			}
			this.list_1 = new List<TextObj>();
			if (base.ChtCtrl_Tick.IfLastTimeLineXsHasNightData && base.SymbDataSet.CurrHisDataSet.CurrExchgOBT.NightOpenTime != null)
			{
				DateTime value = base.SymbDataSet.CurrHisDataSet.CurrExchgOBT.NightOpenTime.Value;
				this.method_79(string.Format(Class521.smethod_0(48796), value), 0);
			}
			List<Struct3> timeLineXs = base.ChtCtrl_Tick.TimeLineXs;
			if (timeLineXs != null)
			{
				foreach (Struct3 @struct in timeLineXs)
				{
					if (@struct.bool_0)
					{
						this.method_79(@struct.string_0, @struct.int_0);
					}
				}
			}
		}

		// Token: 0x0600120B RID: 4619 RVA: 0x000817FC File Offset: 0x0007F9FC
		public void method_79(string string_11, int int_1)
		{
			double y = 1.03;
			TextObj textObj = new TextObj(string_11, (double)int_1, y, CoordType.XScaleYChartFraction, AlignH.Center, AlignV.Top);
			this.method_80(textObj);
			base.GraphPane.GraphObjList.Add(textObj);
			this.list_1.Add(textObj);
		}

		// Token: 0x0600120C RID: 4620 RVA: 0x00081848 File Offset: 0x0007FA48
		private void method_80(TextObj textObj_5)
		{
			textObj_5.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Silver : Color.Black);
			textObj_5.IsClippedToChartRect = false;
			textObj_5.FontSpec.Fill.IsVisible = false;
			textObj_5.FontSpec.Border.IsVisible = false;
			textObj_5.FontSpec.Size = 11f;
			textObj_5.ZOrder = ZOrder.A_InFront;
			textObj_5.Tag = ChartTickV.string_10;
		}

		// Token: 0x0600120D RID: 4621 RVA: 0x000818C8 File Offset: 0x0007FAC8
		private void method_81()
		{
			GraphPane graphPane = base.GraphPane;
			this.textObj_4 = new TextObj(Class521.smethod_0(47454), 0.88, 0.015, CoordType.ChartFraction, AlignH.Right, AlignV.Top);
			base.method_45(this.textObj_4);
			this.textObj_4.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : this.color_0);
			this.textObj_3 = new TextObj(Class521.smethod_0(48727), 0.998, 0.015, CoordType.ChartFraction, AlignH.Right, AlignV.Top);
			base.method_45(this.textObj_3);
			this.textObj_3.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_1);
			base.HeaderTextObj.FontSpec.Size = 12f;
			base.HeaderTextObj.FontSpec.IsBold = true;
			graphPane.GraphObjList.Add(this.textObj_3);
			graphPane.GraphObjList.Add(this.textObj_4);
		}

		// Token: 0x0600120E RID: 4622 RVA: 0x000078C5 File Offset: 0x00005AC5
		public override void vmethod_4(int int_1)
		{
			this.int_0++;
			if (this.int_0 > 1)
			{
				this.vmethod_2();
				base.vmethod_15();
			}
			base.vmethod_4(int_1);
		}

		// Token: 0x0600120F RID: 4623 RVA: 0x000819E4 File Offset: 0x0007FBE4
		public override void vmethod_5(HisData hisData_0)
		{
			if (base.SymbDataSet.HasValidDataSet)
			{
				double x = XDate.DateTimeToXLDate(hisData_0.Date);
				HisData hisData = base.SymbDataSet.method_65(hisData_0.Date);
				double num = (hisData == null) ? 0.0 : ((hisData.Volume != null) ? hisData.Volume.Value : 0.0);
				double num4;
				if (hisData_0.Volume != null)
				{
					double? num2 = hisData_0.Volume;
					double num3 = 0.0;
					if (!(num2.GetValueOrDefault() == num3 & num2 != null))
					{
						num4 = hisData_0.Volume.Value;
						goto IL_B6;
					}
				}
				num4 = num;
				IL_B6:
				double num5 = num4;
				if (base.StartHDDateTime == hisData_0.Date)
				{
					this.double_2 = 0.0;
					this.double_0 = 1.0;
				}
				if (num5 > this.double_2)
				{
					this.double_2 = num5;
				}
				PointPair point = new PointPair(x, num5);
				this.stickItem_0.AddPoint(point);
				if (!base.Symbol.IsStock && hisData_0.Amount != null)
				{
					double num6 = (hisData == null) ? 0.0 : ((hisData.Amount != null) ? hisData.Amount.Value : 0.0);
					double num7;
					if (hisData_0.Amount != null)
					{
						double? num2 = hisData_0.Amount;
						double num3 = 0.0;
						if (!(num2.GetValueOrDefault() == num3 & num2 != null))
						{
							num7 = hisData_0.Amount.Value;
							goto IL_1BA;
						}
					}
					num7 = num6;
					IL_1BA:
					double num8 = num7;
					if (num8 > this.double_3)
					{
						this.double_3 = num8;
					}
					if (num8 < this.double_4)
					{
						this.double_4 = num8;
					}
					double y = num8 * this.double_0;
					this.double_1 = this.double_2 * (0.2 + 0.2 * (double)(this.stickItem_0.NPts + 1) / base.GraphPane.XAxis.Scale.Max);
					if (Math.Round(this.double_3 * this.double_0, 0) != Math.Round(this.double_2, 0))
					{
						this.double_0 = this.double_2 / this.double_3;
						y = num8 * this.double_0;
					}
					if (this.double_3 != this.double_4 && this.lineItem_0.NPts > 0 && Math.Round((this.double_3 - this.double_4) * this.double_0) != Math.Round(this.double_1))
					{
						List<HisData> chtHDList = base.ChtHDList;
						int i = 0;
						while (i < chtHDList.Count<HisData>() - 1)
						{
							HisData hisData2 = chtHDList[i];
							double num9 = (i >= 1) ? chtHDList[i - 1].Amount.Value : 0.0;
							if (hisData2.Amount == null)
							{
								goto IL_34F;
							}
							double? num2 = hisData2.Amount;
							double num3 = 0.0;
							if (num2.GetValueOrDefault() == num3 & num2 != null)
							{
								goto IL_34F;
							}
							double num10 = hisData2.Amount.Value;
							IL_351:
							double num11 = num10;
							if (i >= 0 && i < this.lineItem_0.Points.Count)
							{
								this.lineItem_0.Points[i].Y = this.double_2 - this.double_1 + (num11 - this.double_4) * this.double_1 / (this.double_3 - this.double_4);
							}
							i++;
							continue;
							IL_34F:
							num10 = num9;
							goto IL_351;
						}
						y = this.double_2 - this.double_1 + (num8 - this.double_4) * this.double_1 / (this.double_3 - this.double_4);
					}
					PointPair point2 = new PointPair(x, y);
					this.lineItem_0.AddPoint(point2);
				}
				base.vmethod_5(hisData_0);
				if (base.Symbol.Type == TradingSymbolType.Forex && hisData_0.Date.Hour % 2 == 0 && hisData_0.Date.Minute == 0 && hisData_0.Date.Second == 0 && base.GraphPane.CurveList[0].NPts > 0)
				{
					this.method_79(hisData_0.Date.Hour.ToString() + Class521.smethod_0(48813), base.GraphPane.CurveList[0].NPts - 1);
				}
			}
		}

		// Token: 0x06001210 RID: 4624 RVA: 0x000078F3 File Offset: 0x00005AF3
		public override void vmethod_11(HisData hisData_0)
		{
			base.ZedGraphControl.AxisChange();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x06001211 RID: 4625 RVA: 0x00081EAC File Offset: 0x000800AC
		public override void ApplyTheme(ChartTheme theme)
		{
			base.ApplyTheme(theme);
			GraphPane graphPane = base.GraphPane;
			if (theme == ChartTheme.Classic)
			{
				this.stickItem_0.Line.Color = Color.Yellow;
				this.lineItem_0.Line.Color = Color.White;
				this.textObj_3.FontSpec.FontColor = Color.White;
				this.textObj_4.FontSpec.FontColor = Color.Yellow;
				graphPane.YAxis.Scale.FontSpec.FontColor = Color.Yellow;
				graphPane.Y2Axis.Scale.FontSpec.FontColor = Color.White;
			}
			else
			{
				this.stickItem_0.Line.Color = this.color_0;
				this.lineItem_0.Line.Color = this.color_1;
				if (theme == ChartTheme.Modern)
				{
					graphPane.Fill = new Fill(Color.FromArgb(240, 240, 255), Color.FromArgb(235, 235, 255), 90f);
				}
				else if (theme == ChartTheme.Yellow)
				{
					Color color = Color.FromArgb(255, 255, 236);
					graphPane.Fill = new Fill(color);
				}
				this.textObj_3.FontSpec.FontColor = this.color_1;
				this.textObj_4.FontSpec.FontColor = this.color_0;
				graphPane.YAxis.Scale.FontSpec.FontColor = this.color_0;
				graphPane.Y2Axis.Scale.FontSpec.FontColor = this.color_1;
			}
			foreach (TextObj textObj in this.list_1)
			{
				textObj.FontSpec.FontColor = base.HeaderTextObj.FontSpec.FontColor;
			}
		}

		// Token: 0x06001212 RID: 4626 RVA: 0x000820A8 File Offset: 0x000802A8
		private string method_82(GraphPane graphPane_0, Axis axis_0, double double_5, int int_1)
		{
			double num = 0.0;
			string empty;
			if (base.ChtCtrl_Tick.IndexOfLastItemShownInScr == 0)
			{
				if (int_1 != 0)
				{
					goto IL_161;
				}
				try
				{
					num = Math.Round(((double_5 - this.double_2 + this.double_1) * (this.double_3 - this.double_4) / this.double_1 + this.double_4) / 10.0) * 10.0;
				}
				catch
				{
					empty = string.Empty;
					goto IL_123;
				}
				if (num != double.NaN && num != double.PositiveInfinity && num != double.NegativeInfinity && num >= 0.0)
				{
					return num.ToString();
				}
				goto IL_161;
			}
			else
			{
				if (double_5 <= this.double_2 / 2.0)
				{
					goto IL_161;
				}
				try
				{
					num = Math.Round(((double_5 - this.double_2 + this.double_1) * (this.double_3 - this.double_4) / this.double_1 + this.double_4) / 10.0) * 10.0;
					goto IL_127;
				}
				catch
				{
					empty = string.Empty;
				}
				goto IL_123;
				IL_127:
				if (num != double.NaN && num != double.PositiveInfinity && num != double.NegativeInfinity && num >= 0.0)
				{
					return num.ToString();
				}
				goto IL_161;
			}
			IL_123:
			return empty;
			IL_161:
			return string.Empty;
		}

		// Token: 0x06001213 RID: 4627 RVA: 0x000041B9 File Offset: 0x000023B9
		public override void vmethod_13(string string_11)
		{
		}

		// Token: 0x170002A0 RID: 672
		// (get) Token: 0x06001214 RID: 4628 RVA: 0x0008223C File Offset: 0x0008043C
		public double VolAmtRatio
		{
			get
			{
				return this.double_0;
			}
		}

		// Token: 0x0400096D RID: 2413
		private StickItem stickItem_0;

		// Token: 0x0400096E RID: 2414
		private LineItem lineItem_0;

		// Token: 0x0400096F RID: 2415
		private double double_0;

		// Token: 0x04000970 RID: 2416
		private double double_1;

		// Token: 0x04000971 RID: 2417
		private double double_2;

		// Token: 0x04000972 RID: 2418
		private double double_3;

		// Token: 0x04000973 RID: 2419
		private double double_4;

		// Token: 0x04000974 RID: 2420
		private TextObj textObj_3;

		// Token: 0x04000975 RID: 2421
		private TextObj textObj_4;

		// Token: 0x04000976 RID: 2422
		private List<TextObj> list_1;

		// Token: 0x04000977 RID: 2423
		private static readonly string string_10 = Class521.smethod_0(48818);

		// Token: 0x04000978 RID: 2424
		private int int_0;
	}
}
