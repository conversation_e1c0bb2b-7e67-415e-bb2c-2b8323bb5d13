﻿using System;
using System.Net;
using ns17;
using ns18;
using SmartAssembly.SmartExceptionsCore;

namespace ns30
{
	// Token: 0x0200040A RID: 1034
	internal sealed class Class546
	{
		// Token: 0x0600280F RID: 10255 RVA: 0x0000F8AD File Offset: 0x0000DAAD
		public void method_0(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x06002810 RID: 10256 RVA: 0x0010D58C File Offset: 0x0010B78C
		public void method_1(Delegate39 delegate39_0)
		{
			if (this.string_2 == null)
			{
				try
				{
					UploadReportLoginService uploadReportLoginService = new UploadReportLoginService();
					if (this.iwebProxy_0 != null)
					{
						uploadReportLoginService.Proxy = this.iwebProxy_0;
					}
					this.string_2 = uploadReportLoginService.GetServerURL(this.string_1);
					if (this.string_2.Length == 0)
					{
						throw new ApplicationException(Class521.smethod_0(119382));
					}
					if (this.string_2 == Class521.smethod_0(119423))
					{
						this.string_2 = Class546.string_0;
					}
				}
				catch (Exception ex)
				{
					delegate39_0(Class521.smethod_0(119432) + ex.Message);
					return;
				}
			}
			delegate39_0(this.string_2.StartsWith(Class521.smethod_0(119449)) ? this.string_2 : Class521.smethod_0(119454));
		}

		// Token: 0x06002811 RID: 10257 RVA: 0x0010D670 File Offset: 0x0010B870
		public void method_2(byte[] byte_0, string string_3, string string_4, string string_5, Delegate39 delegate39_0)
		{
			try
			{
				ReportingService reportingService = new ReportingService(this.string_2);
				if (this.iwebProxy_0 != null)
				{
					reportingService.Proxy = this.iwebProxy_0;
				}
				delegate39_0(reportingService.UploadReport2(this.string_1, byte_0, string_3, string_4, string_5));
			}
			catch (Exception ex)
			{
				delegate39_0(Class521.smethod_0(119459) + ex.Message);
			}
		}

		// Token: 0x06002812 RID: 10258 RVA: 0x0000F8B6 File Offset: 0x0000DAB6
		public Class546(string string_3)
		{
			this.string_1 = string_3;
		}

		// Token: 0x040013DE RID: 5086
		internal static readonly string string_0 = Class521.smethod_0(119476);

		// Token: 0x040013DF RID: 5087
		private string string_1;

		// Token: 0x040013E0 RID: 5088
		private string string_2;

		// Token: 0x040013E1 RID: 5089
		private IWebProxy iwebProxy_0;
	}
}
