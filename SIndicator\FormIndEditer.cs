﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using AutocompleteMenuNS;
using ns11;
using ns17;
using ns18;
using ns25;
using ns8;
using ns9;
using ScintillaNET;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000308 RID: 776
	public sealed partial class FormIndEditer : Form
	{
		// Token: 0x140000A2 RID: 162
		// (add) Token: 0x06002170 RID: 8560 RVA: 0x000ED520 File Offset: 0x000EB720
		// (remove) Token: 0x06002171 RID: 8561 RVA: 0x000ED558 File Offset: 0x000EB758
		public event EventHandler ReadyToLoadNewIndToChart
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x140000A3 RID: 163
		// (add) Token: 0x06002172 RID: 8562 RVA: 0x000ED590 File Offset: 0x000EB790
		// (remove) Token: 0x06002173 RID: 8563 RVA: 0x000ED5C8 File Offset: 0x000EB7C8
		public event EventHandler ReadyToReloadModifiedIndToChart
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002174 RID: 8564 RVA: 0x000ED600 File Offset: 0x000EB800
		private void method_0(UserDefineIndScript userDefineIndScript_1)
		{
			EventArgs30 e = new EventArgs30(userDefineIndScript_1);
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002175 RID: 8565 RVA: 0x000ED62C File Offset: 0x000EB82C
		private void method_1(UserDefineIndScript userDefineIndScript_1)
		{
			EventArgs30 e = new EventArgs30(userDefineIndScript_1);
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002176 RID: 8566 RVA: 0x000ED658 File Offset: 0x000EB858
		public FormIndEditer()
		{
			this.method_23();
			this.scintilla_0 = new Scintilla();
			this.tabControl_0 = new TabControl();
			this.textBox_0 = new TextBox();
			this.autocompleteMenu_0 = new AutocompleteMenu();
			base.Load += this.FormIndEditer_Load;
			base.FormClosed += this.FormIndEditer_FormClosed;
			Base.UI.smethod_54(this);
			base.Shown += this.FormIndEditer_Shown;
			Base.UI.smethod_54(this);
			this.textBox_3.KeyPress += this.textBox_3_KeyPress;
			this.textBox_3.GotFocus += this.textBox_3_GotFocus;
			this.textBox_3.LostFocus += this.textBox_3_LostFocus;
			this.button_4.Click += this.button_4_Click;
			this.button_5.Click += this.button_5_Click;
			base.KeyDown += this.FormIndEditer_KeyDown;
			this.button_0.Click += this.button_0_Click;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Click += this.button_3_Click;
			this.button_1.Click += this.button_1_Click;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			if (this.comboBox_0.Items.Count > 0)
			{
				this.comboBox_0.SelectedIndex = 0;
				this.textBox_3.Enabled = false;
			}
			this.comboBox_1.Items.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				this.comboBox_1.Items.Add(userDefineIndGroup.Group);
			}
			if (this.comboBox_1.Items.Count > 0)
			{
				this.comboBox_1.SelectedIndex = this.comboBox_1.Items.Count - 1;
			}
			else
			{
				this.comboBox_1.Text = Class521.smethod_0(96230);
			}
			this.tabControl_0.Parent = this;
			TabPage tabPage = new TabPage(Class521.smethod_0(100288));
			this.scintilla_0.GotFocus += this.scintilla_0_GotFocus;
			tabPage.Controls.Add(this.scintilla_0);
			this.scintilla_0.Dock = DockStyle.Fill;
			this.tabControl_0.TabPages.Add(tabPage);
			TabPage tabPage2 = new TabPage(Class521.smethod_0(100297));
			this.textBox_0.Multiline = true;
			this.textBox_0.Parent = tabPage2;
			this.textBox_0.Dock = DockStyle.Fill;
			this.tabControl_0.TabPages.Add(tabPage2);
			this.control13_0 = new Control13();
			this.control13_0.Parent = this.groupBox_0;
			this.control13_0.Dock = DockStyle.Fill;
			this.control13_0.method_2(new List<UserDefineParam>(), Enum25.flag_0 | Enum25.flag_1 | Enum25.flag_2, Control13.Enum27.const_0);
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(1449);
			this.method_3();
		}

		// Token: 0x06002177 RID: 8567 RVA: 0x000ED9AC File Offset: 0x000EBBAC
		private void textBox_3_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (e.KeyChar != ';' && !char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar) && e.KeyChar != '-')
			{
				if (e.KeyChar != '.')
				{
					this.method_2();
					e.Handled = true;
					return;
				}
			}
			e.Handled = false;
		}

		// Token: 0x06002178 RID: 8568 RVA: 0x0000D74F File Offset: 0x0000B94F
		private void method_2()
		{
			MessageBox.Show(Class521.smethod_0(100306), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06002179 RID: 8569 RVA: 0x000EDA0C File Offset: 0x000EBC0C
		private void textBox_3_LostFocus(object sender, EventArgs e)
		{
			string text = this.textBox_3.Text;
			if (string.IsNullOrEmpty(text))
			{
				this.textBox_3.Text = Class521.smethod_0(70913);
			}
			else
			{
				foreach (char c in text.ToCharArray())
				{
					if (c != ';' && !char.IsDigit(c) && !char.IsControl(c) && c != ' ' && c != '.' && c != '-')
					{
						this.method_2();
						this.textBox_3.Focus();
						return;
					}
				}
				text = text.Trim().Replace(' ', '\0');
				foreach (string value in text.Split(new char[]
				{
					';'
				}))
				{
					try
					{
						Convert.ToDouble(value);
					}
					catch
					{
						this.method_2();
						this.textBox_3.Focus();
						break;
					}
				}
			}
		}

		// Token: 0x0600217A RID: 8570 RVA: 0x0000D770 File Offset: 0x0000B970
		private void textBox_3_GotFocus(object sender, EventArgs e)
		{
			if (this.textBox_3.Text == Class521.smethod_0(70913))
			{
				this.textBox_3.Text = string.Empty;
			}
		}

		// Token: 0x0600217B RID: 8571 RVA: 0x0000D7A0 File Offset: 0x0000B9A0
		private void FormIndEditer_Load(object sender, EventArgs e)
		{
			HToken.WrongToken += this.method_9;
			this.textBox_1.Focus();
		}

		// Token: 0x0600217C RID: 8572 RVA: 0x000EDB04 File Offset: 0x000EBD04
		private void FormIndEditer_Shown(object sender, EventArgs e)
		{
			this.tabControl_0.Top = this.button_0.Top + this.button_0.Height / 2;
			this.tabControl_0.Left = this.groupBox_1.Left;
			this.tabControl_0.Height = base.ClientSize.Height - this.tabControl_0.Top - this.statusStrip_0.Height;
			this.tabControl_0.Width = base.ClientSize.Width - this.tabControl_0.Left - 8;
			if (this.userDefineIndScript_0 != null)
			{
				this.scintilla_0.Focus();
				this.scintilla_0.SelectionStart = this.scintilla_0.Text.Length;
			}
			else
			{
				this.textBox_1.Focus();
			}
		}

		// Token: 0x0600217D RID: 8573 RVA: 0x0000D7C1 File Offset: 0x0000B9C1
		private void FormIndEditer_FormClosed(object sender, FormClosedEventArgs e)
		{
			HToken.WrongToken -= this.method_9;
		}

		// Token: 0x0600217E RID: 8574 RVA: 0x000041B9 File Offset: 0x000023B9
		private void FormIndEditer_KeyDown(object sender, KeyEventArgs e)
		{
		}

		// Token: 0x0600217F RID: 8575 RVA: 0x000EDBE4 File Offset: 0x000EBDE4
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_0.SelectedIndex == 0)
			{
				this.textBox_3.Text = Class521.smethod_0(1449);
				this.textBox_3.Enabled = false;
			}
			else
			{
				this.textBox_3.Text = Class521.smethod_0(70913);
				this.textBox_3.Enabled = true;
			}
		}

		// Token: 0x06002180 RID: 8576 RVA: 0x000EDC44 File Offset: 0x000EBE44
		private void method_3()
		{
			this.autocompleteMenu_0.TargetControlWrapper = new ScintillaWrapper(this.scintilla_0);
			this.method_14(this.scintilla_0);
			for (int i = 0; i < this.scintilla_0.Styles.Count; i++)
			{
				this.scintilla_0.Styles[i].Case = StyleCase.Upper;
			}
			this.scintilla_0.MouseDwellTime = 100;
			this.scintilla_0.VScrollBar = true;
			this.scintilla_0.HScrollBar = true;
			this.scintilla_0.Styles[38].Font = Class521.smethod_0(100367);
			this.scintilla_0.Styles[38].SizeF = 10f;
			this.scintilla_0.Styles[38].Bold = false;
			this.scintilla_0.Styles[38].ForeColor = SystemColors.InfoText;
			this.scintilla_0.Styles[38].BackColor = SystemColors.Info;
			this.scintilla_0.DwellStart += this.method_7;
			this.scintilla_0.MouseEnter += this.scintilla_0_MouseEnter;
			this.scintilla_0.MouseDown += this.scintilla_0_MouseDown;
			this.scintilla_0.KeyDown += this.scintilla_0_KeyDown;
			this.scintilla_0.KeyUp += this.scintilla_0_KeyUp;
			this.autocompleteMenu_0.AutoPopup = true;
			this.autocompleteMenu_0.AllowsTabKey = true;
			this.method_13(ParserEnvironment.smethod_11(this.control13_0.ShowList.ToList<UserDefineParam>()));
			this.scintilla_0.UpdateUI += this.method_12;
		}

		// Token: 0x06002181 RID: 8577 RVA: 0x0000D7D6 File Offset: 0x0000B9D6
		private void scintilla_0_KeyUp(object sender, KeyEventArgs e)
		{
			this.method_4();
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(1449);
		}

		// Token: 0x06002182 RID: 8578 RVA: 0x0000D7D6 File Offset: 0x0000B9D6
		private void scintilla_0_MouseDown(object sender, MouseEventArgs e)
		{
			this.method_4();
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(1449);
		}

		// Token: 0x06002183 RID: 8579 RVA: 0x000EDE18 File Offset: 0x000EC018
		private void method_4()
		{
			int num = this.scintilla_0.CurrentLine + 1;
			int column = this.scintilla_0.GetColumn(this.scintilla_0.CurrentPosition);
			this.toolStripStatusLabel_0.Text = string.Concat(new object[]
			{
				Class521.smethod_0(100376),
				num,
				Class521.smethod_0(100385),
				column
			});
		}

		// Token: 0x06002184 RID: 8580 RVA: 0x0000D7F5 File Offset: 0x0000B9F5
		private void scintilla_0_KeyDown(object sender, KeyEventArgs e)
		{
			this.method_10();
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(1449);
		}

		// Token: 0x06002185 RID: 8581 RVA: 0x000EDE90 File Offset: 0x000EC090
		private void button_1_Click(object sender, EventArgs e)
		{
			Form23 form = new Form23();
			if (form.ShowDialog() == DialogResult.OK)
			{
				this.method_19(Class521.smethod_0(100394) + form.string_0 + Class521.smethod_0(59695));
			}
		}

		// Token: 0x06002186 RID: 8582 RVA: 0x000EDED4 File Offset: 0x000EC0D4
		private void method_5()
		{
			UserDefineIndScript userDefineIndScript_ = this.method_20();
			if (this.method_16(userDefineIndScript_))
			{
				this.method_22(userDefineIndScript_, false, false);
				base.Close();
			}
		}

		// Token: 0x06002187 RID: 8583 RVA: 0x000EDF04 File Offset: 0x000EC104
		private void button_2_Click(object sender, EventArgs e)
		{
			ColorDialog colorDialog = new ColorDialog();
			colorDialog.AllowFullOpen = true;
			colorDialog.ShowHelp = true;
			colorDialog.Color = Color.Red;
			colorDialog.AnyColor = true;
			if (colorDialog.ShowDialog() == DialogResult.OK)
			{
				string string_ = this.method_6(colorDialog.Color);
				this.method_19(string_);
			}
		}

		// Token: 0x06002188 RID: 8584 RVA: 0x000EDF58 File Offset: 0x000EC158
		private string method_6(Color color_0)
		{
			return Class521.smethod_0(100399) + color_0.B.ToString(Class521.smethod_0(100408)).Substring(2, 2) + color_0.G.ToString(Class521.smethod_0(100408)).Substring(2, 2) + color_0.R.ToString(Class521.smethod_0(100408)).Substring(2, 2);
		}

		// Token: 0x06002189 RID: 8585 RVA: 0x0000D814 File Offset: 0x0000BA14
		private void scintilla_0_MouseEnter(object sender, EventArgs e)
		{
			this.scintilla_0.CallTipCancel();
		}

		// Token: 0x0600218A RID: 8586 RVA: 0x000EDFD8 File Offset: 0x000EC1D8
		private void method_7(object sender, DwellEventArgs e)
		{
			int position = e.Position;
			if (position > 0)
			{
				if (position < this.scintilla_0.Text.Length)
				{
					if (char.IsLetter(this.scintilla_0.Text[position]))
					{
						string text = Class521.smethod_0(1449);
						string text2 = Class521.smethod_0(1449);
						string text3 = Class521.smethod_0(1449);
						for (int i = position; i >= 0; i--)
						{
							char c = this.scintilla_0.Text[i];
							if (!char.IsLetterOrDigit(c))
							{
								break;
							}
							text2 = c.ToString() + text2;
						}
						for (int j = position + 1; j < this.scintilla_0.Text.Length; j++)
						{
							char c2 = this.scintilla_0.Text[j];
							if (!char.IsLetterOrDigit(c2))
							{
								break;
							}
							text3 += c2.ToString();
						}
						text = text2 + text3;
						if (text != Class521.smethod_0(1449))
						{
							string text4 = ParserEnvironment.smethod_0(text.ToUpper());
							if (text4 != Class521.smethod_0(1449))
							{
								this.scintilla_0.CallTipShow(e.Position, text4);
							}
						}
					}
				}
			}
		}

		// Token: 0x0600218B RID: 8587 RVA: 0x000EE124 File Offset: 0x000EC324
		public bool method_8(UserDefineIndScript userDefineIndScript_1)
		{
			FormIndEditer.Class427 @class = new FormIndEditer.Class427();
			@class.userDefineIndScript_0 = userDefineIndScript_1;
			@class.userDefineIndScript_1 = UserDefineFileMgr.UDSList.FirstOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
			bool result;
			if (@class.userDefineIndScript_1 == null)
			{
				MessageBox.Show(Class521.smethod_0(100413) + @class.userDefineIndScript_0.Name + Class521.smethod_0(100458), Class521.smethod_0(96159), MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
				result = false;
			}
			else
			{
				this.userDefineIndScript_0 = @class.userDefineIndScript_1;
				this.textBox_1.Enabled = false;
				this.textBox_1.Text = @class.userDefineIndScript_1.Name;
				this.textBox_2.Text = @class.userDefineIndScript_1.Script;
				if (this.comboBox_0.Items.Count != 2)
				{
					throw new Exception(Class521.smethod_0(100495));
				}
				if (@class.userDefineIndScript_1.MainK)
				{
					this.comboBox_0.SelectedItem = this.comboBox_0.Items[0];
					this.textBox_3.Enabled = false;
					this.textBox_3.Text = Class521.smethod_0(1449);
				}
				else
				{
					this.comboBox_0.SelectedItem = this.comboBox_0.Items[1];
					this.textBox_3.Enabled = true;
					if (@class.userDefineIndScript_1.YLine == Class521.smethod_0(1449))
					{
						this.textBox_3.Text = Class521.smethod_0(70913);
					}
					else
					{
						this.textBox_3.Text = @class.userDefineIndScript_1.YLine;
					}
				}
				this.control13_0.method_2(@class.userDefineIndScript_1.UserDefineParams, Enum25.flag_0 | Enum25.flag_1 | Enum25.flag_2, Control13.Enum27.const_0);
				this.textBox_0.Text = @class.userDefineIndScript_1.Instruction.Replace(Class521.smethod_0(59499), Class521.smethod_0(95967));
				this.scintilla_0.Text = @class.userDefineIndScript_1.Code;
				foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
				{
					IEnumerable<UserDefineIndScript> udslist = userDefineIndGroup.UDSList;
					Func<UserDefineIndScript, bool> predicate;
					if ((predicate = @class.func_0) == null)
					{
						predicate = (@class.func_0 = new Func<UserDefineIndScript, bool>(@class.method_1));
					}
					if (udslist.Any(predicate))
					{
						this.comboBox_1.Text = userDefineIndGroup.Group;
					}
				}
				result = true;
			}
			return result;
		}

		// Token: 0x0600218C RID: 8588 RVA: 0x0000D823 File Offset: 0x0000BA23
		private void scintilla_0_GotFocus(object sender, EventArgs e)
		{
			this.method_13(ParserEnvironment.smethod_11(this.control13_0.ShowList));
		}

		// Token: 0x0600218D RID: 8589 RVA: 0x000EE3A8 File Offset: 0x000EC5A8
		private void method_9(object sender, EventArgs e)
		{
			EventArgs31 eventArgs = e as EventArgs31;
			if (eventArgs != null)
			{
				HToken htoken = eventArgs.Obj as HToken;
				if (htoken != null)
				{
					int line = htoken.Line;
					this.method_11(htoken);
				}
			}
		}

		// Token: 0x0600218E RID: 8590 RVA: 0x0000D83D File Offset: 0x0000BA3D
		private void method_10()
		{
			this.scintilla_0.IndicatorCurrent = 8;
			this.scintilla_0.IndicatorClearRange(0, this.scintilla_0.TextLength);
		}

		// Token: 0x0600218F RID: 8591 RVA: 0x000EE3E4 File Offset: 0x000EC5E4
		private void method_11(HToken htoken_0)
		{
			this.method_10();
			this.scintilla_0.Indicators[8].Style = IndicatorStyle.StraightBox;
			this.scintilla_0.Indicators[8].Under = true;
			this.scintilla_0.Indicators[8].ForeColor = Color.Red;
			this.scintilla_0.Indicators[8].OutlineAlpha = 50;
			this.scintilla_0.Indicators[8].Alpha = 100;
			if (htoken_0.Line - 1 < this.scintilla_0.Lines.Count)
			{
				Line line = this.scintilla_0.Lines[htoken_0.Line - 1];
				this.scintilla_0.IndicatorFillRange(line.Position, line.Length);
				if (!string.IsNullOrEmpty(htoken_0.Symbol.Name))
				{
					this.toolStripStatusLabel_1.Text = Class521.smethod_0(100528);
				}
			}
		}

		// Token: 0x06002190 RID: 8592 RVA: 0x000EE4E8 File Offset: 0x000EC6E8
		private void method_12(object sender, UpdateUIEventArgs e)
		{
			Scintilla scintilla = sender as Scintilla;
			if (scintilla != null)
			{
				if ((e.Change & UpdateChange.Selection) > (UpdateChange)0)
				{
					int currentPosition = scintilla.CurrentPosition;
					int anchorPosition = scintilla.AnchorPosition;
				}
			}
		}

		// Token: 0x06002191 RID: 8593 RVA: 0x000EE51C File Offset: 0x000EC71C
		private void method_13(string[] string_1)
		{
			List<AutocompleteItem> list = new List<AutocompleteItem>();
			foreach (string text in string_1)
			{
				list.Add(new AutocompleteItem(text)
				{
					ImageIndex = 1
				});
			}
			this.autocompleteMenu_0.SetAutocompleteItems(list);
		}

		// Token: 0x06002192 RID: 8594 RVA: 0x000EE564 File Offset: 0x000EC764
		private void method_14(Scintilla scintilla_1)
		{
			scintilla_1.Margins[0].Width = 20;
			scintilla_1.Styles[0].ForeColor = Color.Silver;
			scintilla_1.Styles[1].ForeColor = Color.FromArgb(0, 128, 0);
			scintilla_1.Styles[2].ForeColor = Color.FromArgb(0, 128, 0);
			scintilla_1.Styles[15].ForeColor = Color.FromArgb(128, 128, 128);
			scintilla_1.Styles[4].ForeColor = Color.Olive;
			scintilla_1.Styles[5].ForeColor = Color.Blue;
			scintilla_1.Styles[16].ForeColor = Color.Blue;
			scintilla_1.Styles[6].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[7].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[13].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[12].BackColor = Color.Pink;
			scintilla_1.Styles[10].ForeColor = Color.Purple;
			scintilla_1.Styles[9].ForeColor = Color.Maroon;
			scintilla_1.Lexer = Lexer.Cpp;
			string[] value = ParserEnvironment.smethod_11(this.control13_0.ShowList.ToList<UserDefineParam>());
			string keywords = string.Join(Class521.smethod_0(3636), value);
			scintilla_1.SetKeywords(0, keywords);
			scintilla_1.SetProperty(Class521.smethod_0(100545), Class521.smethod_0(4933));
			scintilla_1.SetProperty(Class521.smethod_0(100554), Class521.smethod_0(4933));
			scintilla_1.Margins[2].Type = MarginType.Symbol;
			scintilla_1.Margins[2].Mask = 4261412864U;
			scintilla_1.Margins[2].Sensitive = true;
			scintilla_1.Margins[2].Width = 20;
			for (int i = 25; i <= 31; i++)
			{
				scintilla_1.Markers[i].SetForeColor(SystemColors.ControlLightLight);
				scintilla_1.Markers[i].SetBackColor(SystemColors.ControlDark);
			}
			scintilla_1.Markers[30].Symbol = MarkerSymbol.BoxPlus;
			scintilla_1.Markers[31].Symbol = MarkerSymbol.BoxMinus;
			scintilla_1.Markers[25].Symbol = MarkerSymbol.BoxPlusConnected;
			scintilla_1.Markers[27].Symbol = MarkerSymbol.TCorner;
			scintilla_1.Markers[26].Symbol = MarkerSymbol.BoxMinusConnected;
			scintilla_1.Markers[29].Symbol = MarkerSymbol.VLine;
			scintilla_1.Markers[28].Symbol = MarkerSymbol.LCorner;
			scintilla_1.AutomaticFold = (AutomaticFold.Show | AutomaticFold.Click | AutomaticFold.Change);
		}

		// Token: 0x06002193 RID: 8595 RVA: 0x000EE868 File Offset: 0x000ECA68
		public static bool smethod_0(List<UserDefineParam> list_0)
		{
			bool result;
			using (List<UserDefineParam>.Enumerator enumerator = list_0.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (!enumerator.Current.Check())
					{
						result = false;
						goto IL_77;
					}
				}
			}
			List<string> list = list_0.Select(new Func<UserDefineParam, string>(FormIndEditer.<>c.<>9.method_0)).ToList<string>();
			list.Distinct<string>().Count<string>();
			int count = list.Count;
			return true;
			IL_77:
			return result;
		}

		// Token: 0x06002194 RID: 8596 RVA: 0x0002ECFC File Offset: 0x0002CEFC
		private bool method_15(string string_1)
		{
			return false;
		}

		// Token: 0x06002195 RID: 8597 RVA: 0x000EE904 File Offset: 0x000ECB04
		private bool method_16(UserDefineIndScript userDefineIndScript_1)
		{
			string name = userDefineIndScript_1.Name;
			string text = Class521.smethod_0(1449);
			bool result;
			if (!UserDefineIndScript.smethod_0(name, out text))
			{
				MessageBox.Show(text, Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_1.Focus();
				result = false;
			}
			else if (string.IsNullOrEmpty(userDefineIndScript_1.Code))
			{
				MessageBox.Show(Class521.smethod_0(100571), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.scintilla_0.Focus();
				result = false;
			}
			else
			{
				if (string.IsNullOrEmpty(this.comboBox_1.Text))
				{
					MessageBox.Show(Class521.smethod_0(100604), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				if (!FormIndEditer.smethod_0(userDefineIndScript_1.UserDefineParams))
				{
					result = false;
				}
				else
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x06002196 RID: 8598 RVA: 0x000EE9D0 File Offset: 0x000ECBD0
		private string method_17()
		{
			string result = string.Empty;
			string text = this.textBox_3.Text.Trim();
			if (text == Class521.smethod_0(70913))
			{
				result = string.Empty;
			}
			else
			{
				result = text;
			}
			return result;
		}

		// Token: 0x06002197 RID: 8599 RVA: 0x000EEA18 File Offset: 0x000ECC18
		private bool method_18(UserDefineIndScript userDefineIndScript_1)
		{
			bool result;
			try
			{
				this.method_10();
				if (!this.method_16(userDefineIndScript_1))
				{
					result = false;
				}
				else
				{
					string code = userDefineIndScript_1.Code;
					if (UserDefineInd.smethod_1(userDefineIndScript_1, Base.Data.CurrSymbDataSet.CurrSymbol.MstSymbol))
					{
						userDefineIndScript_1.Check = true;
						result = true;
					}
					else
					{
						result = false;
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
				result = false;
			}
			return result;
		}

		// Token: 0x06002198 RID: 8600 RVA: 0x0000D864 File Offset: 0x0000BA64
		private void button_0_Click(object sender, EventArgs e)
		{
			if (this.method_18(this.method_20()))
			{
				MessageBox.Show(Class521.smethod_0(100633), Class521.smethod_0(5801), MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x06002199 RID: 8601 RVA: 0x000EEA8C File Offset: 0x000ECC8C
		private void method_19(string string_1)
		{
			int position = this.scintilla_0.CurrentPosition + string_1.Length;
			this.scintilla_0.InsertText(this.scintilla_0.CurrentPosition, string_1);
			this.scintilla_0.Select();
			this.scintilla_0.GotoPosition(position);
		}

		// Token: 0x0600219A RID: 8602 RVA: 0x000EEADC File Offset: 0x000ECCDC
		private void button_3_Click(object sender, EventArgs e)
		{
			FormIndFunction formIndFunction = new FormIndFunction();
			formIndFunction.Top = base.Top;
			formIndFunction.Left = base.Left;
			if (formIndFunction.ShowDialog() == DialogResult.OK && formIndFunction.FunctionName != Class521.smethod_0(1449))
			{
				this.method_19(formIndFunction.FunctionName);
			}
		}

		// Token: 0x0600219B RID: 8603 RVA: 0x000EEB38 File Offset: 0x000ECD38
		private UserDefineIndScript method_20()
		{
			bool mainK = this.comboBox_0.SelectedIndex == 0;
			return new UserDefineIndScript(this.textBox_1.Text, this.scintilla_0.Text.ToUpper(), this.control13_0.ShowList, mainK, this.textBox_2.Text, false, this.method_17(), this.textBox_0.Text.Replace(Class521.smethod_0(95967), Class521.smethod_0(59499)).Trim());
		}

		// Token: 0x0600219C RID: 8604 RVA: 0x000EEBC4 File Offset: 0x000ECDC4
		private void button_5_Click(object sender, EventArgs e)
		{
			try
			{
				if (FormIndEditer.smethod_0(this.control13_0.ShowList))
				{
					this.AddOrMdfIndName = Class521.smethod_0(1449);
					UserDefineIndScript userDefineIndScript = this.method_20();
					if (userDefineIndScript.method_1(this.userDefineIndScript_0))
					{
						base.Close();
					}
					else
					{
						userDefineIndScript.UserIndFlag = true;
						DialogResult dialogResult = MessageBox.Show(Class521.smethod_0(100662), Class521.smethod_0(7730), MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
						if (dialogResult == DialogResult.Yes)
						{
							if (this.method_18(userDefineIndScript))
							{
								this.method_22(userDefineIndScript, true, true);
								base.Close();
							}
						}
						else if (dialogResult == DialogResult.No)
						{
							base.Close();
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		// Token: 0x0600219D RID: 8605 RVA: 0x000EEC84 File Offset: 0x000ECE84
		private void method_21(UserDefineIndScript userDefineIndScript_1)
		{
			FormIndEditer.Class428 @class = new FormIndEditer.Class428();
			@class.userDefineIndScript_0 = userDefineIndScript_1;
			if (Base.UI.ChtCtrl_KLineList != null)
			{
				foreach (ChtCtrl_KLine chtCtrl_KLine in Base.UI.ChtCtrl_KLineList)
				{
					IEnumerable<Indicator> indList = chtCtrl_KLine.IndList;
					Func<Indicator, bool> predicate;
					if ((predicate = @class.func_0) == null)
					{
						predicate = (@class.func_0 = new Func<Indicator, bool>(@class.method_0));
					}
					foreach (Indicator indicator in indList.Where(predicate))
					{
						if (indicator != null)
						{
							indicator.Chart.method_84(@class.userDefineIndScript_0);
						}
					}
				}
			}
			Base.UI.smethod_181(@class.userDefineIndScript_0);
		}

		// Token: 0x0600219E RID: 8606 RVA: 0x000EED64 File Offset: 0x000ECF64
		private void method_22(UserDefineIndScript userDefineIndScript_1, bool bool_0, bool bool_1)
		{
			this.AddOrMdfIndName = userDefineIndScript_1.Name;
			if (this.eventHandler_0 != null)
			{
				UserDefineFileMgr.smethod_23(userDefineIndScript_1, this.comboBox_1.Text.Trim());
				if (bool_0 && bool_1)
				{
					Base.UI.smethod_164();
					if (MessageBox.Show(Class521.smethod_0(100715), Class521.smethod_0(7587), MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) == DialogResult.Yes)
					{
						this.method_0(userDefineIndScript_1);
					}
				}
			}
			else if (this.eventHandler_1 != null)
			{
				UserDefineFileMgr.smethod_22(userDefineIndScript_1, this.comboBox_1.Text.Trim());
				if (bool_0 && bool_1)
				{
					this.method_21(userDefineIndScript_1);
				}
			}
			else if (this.IsAddNew != null)
			{
				if (this.IsAddNew.Value)
				{
					UserDefineFileMgr.smethod_23(userDefineIndScript_1, this.comboBox_1.Text.Trim());
					if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine && MessageBox.Show(Class521.smethod_0(100768), Class521.smethod_0(7587), MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) == DialogResult.Yes)
					{
						(Base.UI.SelectedChtCtrl as ChtCtrl_KLine).Chart_CS.method_82(userDefineIndScript_1);
					}
				}
				else
				{
					UserDefineFileMgr.smethod_22(userDefineIndScript_1, this.comboBox_1.Text.Trim());
					this.method_21(userDefineIndScript_1);
				}
			}
			Base.UI.smethod_164();
		}

		// Token: 0x0600219F RID: 8607 RVA: 0x000EEEB0 File Offset: 0x000ED0B0
		private void button_4_Click(object sender, EventArgs e)
		{
			UserDefineIndScript userDefineIndScript = this.method_20();
			if (this.method_18(userDefineIndScript))
			{
				if (!userDefineIndScript.method_1(this.userDefineIndScript_0))
				{
					userDefineIndScript.UserIndFlag = true;
				}
				this.method_22(userDefineIndScript, true, true);
				base.Close();
			}
		}

		// Token: 0x170005C8 RID: 1480
		// (get) Token: 0x060021A0 RID: 8608 RVA: 0x000EEEF8 File Offset: 0x000ED0F8
		// (set) Token: 0x060021A1 RID: 8609 RVA: 0x0000D893 File Offset: 0x0000BA93
		public string AddOrMdfIndName { get; private set; }

		// Token: 0x170005C9 RID: 1481
		// (get) Token: 0x060021A2 RID: 8610 RVA: 0x000EEF10 File Offset: 0x000ED110
		// (set) Token: 0x060021A3 RID: 8611 RVA: 0x0000D89E File Offset: 0x0000BA9E
		public string Group
		{
			get
			{
				return this.comboBox_1.Text;
			}
			set
			{
				this.comboBox_1.Text = value;
			}
		}

		// Token: 0x170005CA RID: 1482
		// (get) Token: 0x060021A4 RID: 8612 RVA: 0x000EEF2C File Offset: 0x000ED12C
		// (set) Token: 0x060021A5 RID: 8613 RVA: 0x0000D8AE File Offset: 0x0000BAAE
		public bool? IsAddNew { get; set; }

		// Token: 0x060021A6 RID: 8614 RVA: 0x0000D8B9 File Offset: 0x0000BAB9
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060021A7 RID: 8615 RVA: 0x000EEF44 File Offset: 0x000ED144
		private void method_23()
		{
			this.label_0 = new Label();
			this.textBox_1 = new TextBox();
			this.label_1 = new Label();
			this.textBox_2 = new TextBox();
			this.label_2 = new Label();
			this.comboBox_0 = new ComboBox();
			this.label_3 = new Label();
			this.comboBox_1 = new ComboBox();
			this.groupBox_0 = new GroupBox();
			this.statusStrip_0 = new StatusStrip();
			this.toolStripStatusLabel_0 = new ToolStripStatusLabel();
			this.toolStripStatusLabel_1 = new ToolStripStatusLabel();
			this.label_4 = new Label();
			this.textBox_3 = new TextBox();
			this.button_1 = new Button();
			this.button_2 = new Button();
			this.button_3 = new Button();
			this.button_0 = new Button();
			this.button_4 = new Button();
			this.button_5 = new Button();
			this.groupBox_1 = new GroupBox();
			this.statusStrip_0.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(23, 31);
			this.label_0.Margin = new Padding(4, 0, 4, 0);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(37, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(1472);
			this.textBox_1.Location = new Point(68, 28);
			this.textBox_1.Margin = new Padding(4);
			this.textBox_1.Name = Class521.smethod_0(100829);
			this.textBox_1.Size = new Size(132, 25);
			this.textBox_1.TabIndex = 0;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(23, 67);
			this.label_1.Margin = new Padding(4, 0, 4, 0);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(37, 15);
			this.label_1.TabIndex = 0;
			this.label_1.Text = Class521.smethod_0(100846);
			this.textBox_2.Location = new Point(68, 64);
			this.textBox_2.Margin = new Padding(4);
			this.textBox_2.Name = Class521.smethod_0(100855);
			this.textBox_2.Size = new Size(294, 25);
			this.textBox_2.TabIndex = 1;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(23, 103);
			this.label_2.Margin = new Padding(4, 0, 4, 0);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(37, 15);
			this.label_2.TabIndex = 0;
			this.label_2.Text = Class521.smethod_0(100880);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Items.AddRange(new object[]
			{
				Class521.smethod_0(48068),
				Class521.smethod_0(48085)
			});
			this.comboBox_0.Location = new Point(68, 100);
			this.comboBox_0.Margin = new Padding(4);
			this.comboBox_0.Name = Class521.smethod_0(100889);
			this.comboBox_0.Size = new Size(118, 23);
			this.comboBox_0.TabIndex = 2;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(198, 103);
			this.label_3.Margin = new Padding(4, 0, 4, 0);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(37, 15);
			this.label_3.TabIndex = 0;
			this.label_3.Text = Class521.smethod_0(96405);
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Items.AddRange(new object[]
			{
				Class521.smethod_0(100922),
				Class521.smethod_0(100947)
			});
			this.comboBox_1.Location = new Point(243, 100);
			this.comboBox_1.Margin = new Padding(4);
			this.comboBox_1.Name = Class521.smethod_0(100964);
			this.comboBox_1.Size = new Size(119, 23);
			this.comboBox_1.TabIndex = 3;
			this.groupBox_0.Location = new Point(416, 7);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(390, 197);
			this.groupBox_0.TabIndex = 1;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(47794);
			this.statusStrip_0.ImageScalingSize = new Size(20, 20);
			this.statusStrip_0.Items.AddRange(new ToolStripItem[]
			{
				this.toolStripStatusLabel_0,
				this.toolStripStatusLabel_1
			});
			this.statusStrip_0.Location = new Point(0, 625);
			this.statusStrip_0.Name = Class521.smethod_0(45241);
			this.statusStrip_0.RightToLeft = RightToLeft.Yes;
			this.statusStrip_0.Size = new Size(924, 25);
			this.statusStrip_0.SizingGrip = false;
			this.statusStrip_0.TabIndex = 6;
			this.statusStrip_0.Text = Class521.smethod_0(45241);
			this.toolStripStatusLabel_0.Name = Class521.smethod_0(100985);
			this.toolStripStatusLabel_0.Size = new Size(0, 0);
			this.toolStripStatusLabel_1.Name = Class521.smethod_0(101006);
			this.toolStripStatusLabel_1.Size = new Size(167, 20);
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(101019);
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(23, 136);
			this.label_4.Margin = new Padding(4, 0, 4, 0);
			this.label_4.Name = Class521.smethod_0(5893);
			this.label_4.Size = new Size(172, 15);
			this.label_4.TabIndex = 0;
			this.label_4.Text = Class521.smethod_0(101048);
			this.textBox_3.Location = new Point(26, 158);
			this.textBox_3.Margin = new Padding(4);
			this.textBox_3.Name = Class521.smethod_0(101093);
			this.textBox_3.Size = new Size(336, 25);
			this.textBox_3.TabIndex = 4;
			this.button_1.Location = new Point(616, 214);
			this.button_1.Name = Class521.smethod_0(101110);
			this.button_1.Size = new Size(90, 30);
			this.button_1.TabIndex = 4;
			this.button_1.Text = Class521.smethod_0(101131);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_2.Location = new Point(516, 214);
			this.button_2.Name = Class521.smethod_0(101148);
			this.button_2.Size = new Size(90, 30);
			this.button_2.TabIndex = 3;
			this.button_2.Text = Class521.smethod_0(101173);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_3.Location = new Point(416, 214);
			this.button_3.Name = Class521.smethod_0(101190);
			this.button_3.Size = new Size(90, 30);
			this.button_3.TabIndex = 2;
			this.button_3.Text = Class521.smethod_0(101215);
			this.button_3.UseVisualStyleBackColor = true;
			this.button_0.Location = new Point(716, 214);
			this.button_0.Name = Class521.smethod_0(101232);
			this.button_0.Size = new Size(90, 30);
			this.button_0.TabIndex = 5;
			this.button_0.Text = Class521.smethod_0(101253);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_4.Location = new Point(817, 43);
			this.button_4.Name = Class521.smethod_0(95505);
			this.button_4.Size = new Size(95, 30);
			this.button_4.TabIndex = 6;
			this.button_4.Text = Class521.smethod_0(5801);
			this.button_4.UseVisualStyleBackColor = true;
			this.button_5.Location = new Point(817, 91);
			this.button_5.Name = Class521.smethod_0(95518);
			this.button_5.Size = new Size(95, 30);
			this.button_5.TabIndex = 7;
			this.button_5.Text = Class521.smethod_0(5783);
			this.button_5.UseVisualStyleBackColor = true;
			this.groupBox_1.Controls.Add(this.textBox_3);
			this.groupBox_1.Controls.Add(this.label_4);
			this.groupBox_1.Controls.Add(this.label_0);
			this.groupBox_1.Controls.Add(this.textBox_1);
			this.groupBox_1.Controls.Add(this.label_1);
			this.groupBox_1.Controls.Add(this.textBox_2);
			this.groupBox_1.Controls.Add(this.label_2);
			this.groupBox_1.Controls.Add(this.comboBox_0);
			this.groupBox_1.Controls.Add(this.label_3);
			this.groupBox_1.Controls.Add(this.comboBox_1);
			this.groupBox_1.Location = new Point(12, 7);
			this.groupBox_1.Name = Class521.smethod_0(20273);
			this.groupBox_1.Size = new Size(390, 197);
			this.groupBox_1.TabIndex = 0;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(101270);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(924, 650);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_2);
			base.Controls.Add(this.button_5);
			base.Controls.Add(this.button_3);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.button_4);
			base.Controls.Add(this.statusStrip_0);
			base.Controls.Add(this.groupBox_0);
			this.DoubleBuffered = true;
			base.Margin = new Padding(4);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(101287);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(101308);
			this.statusStrip_0.ResumeLayout(false);
			this.statusStrip_0.PerformLayout();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x0400103A RID: 4154
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400103B RID: 4155
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x0400103C RID: 4156
		private Scintilla scintilla_0;

		// Token: 0x0400103D RID: 4157
		private TabControl tabControl_0;

		// Token: 0x0400103E RID: 4158
		private TextBox textBox_0;

		// Token: 0x0400103F RID: 4159
		private AutocompleteMenu autocompleteMenu_0;

		// Token: 0x04001040 RID: 4160
		private Control13 control13_0;

		// Token: 0x04001041 RID: 4161
		private UserDefineIndScript userDefineIndScript_0;

		// Token: 0x04001042 RID: 4162
		private const int int_0 = 8;

		// Token: 0x04001043 RID: 4163
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04001044 RID: 4164
		[CompilerGenerated]
		private bool? nullable_0;

		// Token: 0x04001045 RID: 4165
		private IContainer icontainer_0;

		// Token: 0x04001046 RID: 4166
		private Label label_0;

		// Token: 0x04001047 RID: 4167
		private TextBox textBox_1;

		// Token: 0x04001048 RID: 4168
		private Label label_1;

		// Token: 0x04001049 RID: 4169
		private TextBox textBox_2;

		// Token: 0x0400104A RID: 4170
		private Label label_2;

		// Token: 0x0400104B RID: 4171
		private ComboBox comboBox_0;

		// Token: 0x0400104C RID: 4172
		private Label label_3;

		// Token: 0x0400104D RID: 4173
		private ComboBox comboBox_1;

		// Token: 0x0400104E RID: 4174
		private GroupBox groupBox_0;

		// Token: 0x0400104F RID: 4175
		private StatusStrip statusStrip_0;

		// Token: 0x04001050 RID: 4176
		private Label label_4;

		// Token: 0x04001051 RID: 4177
		private TextBox textBox_3;

		// Token: 0x04001052 RID: 4178
		private Button button_0;

		// Token: 0x04001053 RID: 4179
		private Button button_1;

		// Token: 0x04001054 RID: 4180
		private Button button_2;

		// Token: 0x04001055 RID: 4181
		private Button button_3;

		// Token: 0x04001056 RID: 4182
		private Button button_4;

		// Token: 0x04001057 RID: 4183
		private Button button_5;

		// Token: 0x04001058 RID: 4184
		private GroupBox groupBox_1;

		// Token: 0x04001059 RID: 4185
		private ToolStripStatusLabel toolStripStatusLabel_0;

		// Token: 0x0400105A RID: 4186
		private ToolStripStatusLabel toolStripStatusLabel_1;

		// Token: 0x02000309 RID: 777
		[CompilerGenerated]
		private sealed class Class427
		{
			// Token: 0x060021A9 RID: 8617 RVA: 0x000EFC0C File Offset: 0x000EDE0C
			internal bool method_0(UserDefineIndScript userDefineIndScript_2)
			{
				return userDefineIndScript_2.Name.Trim() == this.userDefineIndScript_0.Name.Trim();
			}

			// Token: 0x060021AA RID: 8618 RVA: 0x000EFC40 File Offset: 0x000EDE40
			internal bool method_1(UserDefineIndScript userDefineIndScript_2)
			{
				return userDefineIndScript_2.Name == this.userDefineIndScript_1.Name;
			}

			// Token: 0x0400105B RID: 4187
			public UserDefineIndScript userDefineIndScript_0;

			// Token: 0x0400105C RID: 4188
			public UserDefineIndScript userDefineIndScript_1;

			// Token: 0x0400105D RID: 4189
			public Func<UserDefineIndScript, bool> func_0;
		}

		// Token: 0x0200030B RID: 779
		[CompilerGenerated]
		private sealed class Class428
		{
			// Token: 0x060021AF RID: 8623 RVA: 0x000EFC80 File Offset: 0x000EDE80
			internal bool method_0(Indicator indicator_0)
			{
				bool result;
				if (indicator_0.EnName == this.userDefineIndScript_0.Name)
				{
					result = (indicator_0.IsMainChartInd == this.userDefineIndScript_0.MainK);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04001060 RID: 4192
			public UserDefineIndScript userDefineIndScript_0;

			// Token: 0x04001061 RID: 4193
			public Func<Indicator, bool> func_0;
		}
	}
}
