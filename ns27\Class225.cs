﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns18;
using ns4;
using TEx;
using TEx.Chart;
using TEx.Comn;

namespace ns27
{
	// Token: 0x020002A9 RID: 681
	internal sealed class Class225 : ChartKLSub
	{
		// Token: 0x06001E26 RID: 7718 RVA: 0x000D404C File Offset: 0x000D224C
		public Class225(ChtCtrl_KLine chtCtrl_KLine_0, SplitterPanel splitterPanel_1, bool bool_5) : base(chtCtrl_KLine_0, splitterPanel_1)
		{
			base.ChartType = ChartType.MACD;
			base.IsXAxisVisible = bool_5;
			List<Indicator> indList = new List<Indicator>();
			base.IndList = indList;
			this.vmethod_2();
			this.vmethod_3();
			this.ApplyTheme(Base.UI.Form.ChartTheme);
			if (base.ZedGraphControl.GraphPane != null)
			{
				base.ZedGraphControl.GraphPane.YAxis.ScaleFormatEvent += this.method_151;
			}
		}

		// Token: 0x06001E27 RID: 7719 RVA: 0x0000CA20 File Offset: 0x0000AC20
		public Class225(ChtCtrl_KLine chtCtrl_KLine_0, SplitterPanel splitterPanel_1) : this(chtCtrl_KLine_0, splitterPanel_1, true)
		{
		}

		// Token: 0x06001E28 RID: 7720 RVA: 0x000D40C8 File Offset: 0x000D22C8
		private string method_151(GraphPane graphPane_0, Axis axis_0, double double_0, int int_0)
		{
			double num = 100000000.0;
			string result;
			if (double_0 >= num)
			{
				result = Math.Round(double_0 / num, 2).ToString() + Class521.smethod_0(86771);
			}
			else if (double_0 >= 1000000.0)
			{
				result = Math.Round(double_0 / 10000.0, 2).ToString() + Class521.smethod_0(86776);
			}
			else
			{
				result = double_0.ToString();
			}
			return result;
		}

		// Token: 0x06001E29 RID: 7721 RVA: 0x000D4148 File Offset: 0x000D2348
		public override void vmethod_7(int int_0, HisData hisData_0, bool bool_5)
		{
			HisData hisData = hisData_0.Clone();
			if (bool_5)
			{
				hisData = base.method_90(int_0, hisData_0);
				if (hisData == null)
				{
					return;
				}
			}
			base.vmethod_7(int_0, hisData, bool_5);
		}

		// Token: 0x06001E2A RID: 7722 RVA: 0x000D4178 File Offset: 0x000D2378
		public override void vmethod_3()
		{
			base.vmethod_3();
			GraphPane graphPane = base.GraphPane;
			graphPane.Margin.Left = 25f;
			graphPane.Margin.Right = -10f;
			graphPane.Margin.Top = 0f;
			graphPane.Margin.Bottom = 4f;
			graphPane.YAxis.Scale.FormatAuto = true;
			graphPane.YAxis.MajorGrid.DashOff = 2f;
			graphPane.YAxis.Scale.MaxAuto = true;
			graphPane.YAxis.Scale.MinAuto = true;
		}

		// Token: 0x06001E2B RID: 7723 RVA: 0x000D421C File Offset: 0x000D241C
		public override void ApplyTheme(ChartTheme theme)
		{
			if (theme == ChartTheme.Classic)
			{
				base.GraphPane.YAxis.Color = Color.Maroon;
			}
			else
			{
				if (theme == ChartTheme.Modern)
				{
					Color color = Color.FromArgb(235, 235, 255);
					Color color2 = Color.FromArgb(230, 230, 255);
					int num = base.ChtCtrl.ChartList.IndexOf(this);
					if (num > 0)
					{
						int num2 = (num - 1) * 2;
						Color color_ = Class181.color_14;
						color = Color.FromArgb((int)color_.R - num2, (int)color_.G - num2, (int)color_.B);
						color2 = Color.FromArgb((int)color_.R - num2 - 2, (int)color_.G - num2 - 2, (int)color_.B);
					}
					base.GraphPane.Fill = new Fill(color, color2, 90f);
				}
				else if (theme == ChartTheme.Yellow)
				{
					Color color3 = Color.FromArgb(255, 255, 236);
					base.GraphPane.Fill = new Fill(color3);
				}
				base.GraphPane.YAxis.Color = Color.LightGray;
			}
			base.ApplyTheme(theme);
		}

		// Token: 0x06001E2C RID: 7724 RVA: 0x0000CA2B File Offset: 0x0000AC2B
		public override void vmethod_2()
		{
			base.vmethod_2();
			base.vmethod_24();
			base.GraphPane.YAxis.MajorGrid.IsZeroLine = false;
		}

		// Token: 0x06001E2D RID: 7725 RVA: 0x000D4340 File Offset: 0x000D2540
		public override void vmethod_11(HisData hisData_0)
		{
			foreach (Indicator indicator in base.IndList)
			{
				indicator.RescaleAxis();
			}
			base.ZedGraphControl.AxisChange();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x06001E2E RID: 7726 RVA: 0x000D43A8 File Offset: 0x000D25A8
		protected override int vmethod_14()
		{
			int result;
			if (base.GraphPane.YAxis.Scale.Max <= 1000.0 && base.GraphPane.YAxis.Scale.Min >= -1000.0)
			{
				result = 2;
			}
			else
			{
				result = 0;
			}
			return result;
		}

		// Token: 0x06001E2F RID: 7727 RVA: 0x000D4400 File Offset: 0x000D2600
		protected override void vmethod_19()
		{
			base.vmethod_19();
			if (base.IndList.Any<Indicator>())
			{
				string infoText = base.IndList[0].GetInfoText();
				base.HeaderTextObj.Text = infoText;
			}
		}
	}
}
