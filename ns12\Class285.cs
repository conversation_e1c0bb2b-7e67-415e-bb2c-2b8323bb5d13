﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns10;
using ns18;
using ns26;
using ns28;
using ns4;
using TEx;
using TEx.Util;

namespace ns12
{
	// Token: 0x02000209 RID: 521
	[DesignerCategory("Code")]
	internal class Class285 : Class284
	{
		// Token: 0x06001559 RID: 5465 RVA: 0x0000892C File Offset: 0x00006B2C
		public Class285(bool bool_2 = false) : base(bool_2)
		{
			base.CellFormatting += this.Class285_CellFormatting;
		}

		// Token: 0x0600155A RID: 5466 RVA: 0x00091B6C File Offset: 0x0008FD6C
		protected virtual void Class285_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			DataGridViewCell dataGridViewCell = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
			if (e.Value != null)
			{
				if (e.Value.GetType() == typeof(double))
				{
					if (double.IsNaN((double)e.Value))
					{
						e.Value = Class521.smethod_0(3210);
					}
				}
				else if (e.Value as string == Class521.smethod_0(52046))
				{
					e.Value = Class521.smethod_0(3210);
				}
				if (this.vmethod_4(dataGridView, e))
				{
					string text = e.Value.ToString();
					if (!string.IsNullOrEmpty(text))
					{
						if (!Utility.IsDigitChars(text, true, true))
						{
							goto IL_E8;
						}
						try
						{
							e.Value = Utility.GetUnitAbbrNbString(text);
							goto IL_E8;
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
							goto IL_E8;
						}
					}
					e.Value = Class521.smethod_0(3210);
				}
				IL_E8:
				if (this.vmethod_5(dataGridView, e))
				{
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						e.CellStyle.BackColor = Color.FromArgb(49, 49, 49);
						e.CellStyle.ForeColor = Class181.color_9;
						e.CellStyle.SelectionForeColor = Class181.color_9;
					}
					else
					{
						e.CellStyle.BackColor = Color.FromKnownColor(KnownColor.Control);
					}
				}
			}
			else
			{
				e.Value = Class521.smethod_0(3210);
			}
		}

		// Token: 0x0600155B RID: 5467 RVA: 0x000239B0 File Offset: 0x00021BB0
		protected virtual bool vmethod_4(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return true;
		}

		// Token: 0x0600155C RID: 5468 RVA: 0x00091CE4 File Offset: 0x0008FEE4
		protected virtual bool vmethod_5(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return !this.vmethod_4(dataGridView_0, dataGridViewCellFormattingEventArgs_0);
		}

		// Token: 0x0600155D RID: 5469 RVA: 0x00091D00 File Offset: 0x0008FF00
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.BorderStyle = BorderStyle.None;
			base.CellBorderStyle = DataGridViewCellBorderStyle.Single;
			base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			base.AllowUserToOrderColumns = false;
			base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			base.ColumnHeadersDefaultCellStyle.Padding = new Padding(0);
			base.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			base.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
		}

		// Token: 0x0600155E RID: 5470 RVA: 0x00091D60 File Offset: 0x0008FF60
		protected override void vmethod_2()
		{
			if (!base.IsDisposed)
			{
				try
				{
					Color color;
					Color backColor;
					Color backColor2;
					Color foreColor;
					Color color2;
					Color selectionBackColor;
					Color backgroundColor;
					Color gridColor;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						color = Class181.color_11;
						backColor = Class181.color_3;
						backColor2 = Color.FromArgb(49, 49, 49);
						foreColor = Class181.color_9;
						color2 = color;
						selectionBackColor = Color.FromArgb(74, 74, 74);
						backgroundColor = Class181.color_3;
						gridColor = Color.FromArgb(69, 69, 69);
					}
					else
					{
						color = Class181.color_3;
						backColor = Color.White;
						backColor2 = Color.FromKnownColor(KnownColor.Control);
						foreColor = Class181.color_3;
						color2 = color;
						selectionBackColor = Class181.color_12;
						backgroundColor = Color.White;
						gridColor = Color.FromArgb(255, 250, 240);
					}
					this.smethod_0();
					base.DefaultCellStyle.ForeColor = color;
					base.DefaultCellStyle.BackColor = backColor;
					base.RowsDefaultCellStyle.ForeColor = color;
					base.DefaultCellStyle.SelectionForeColor = color;
					base.DefaultCellStyle.SelectionBackColor = selectionBackColor;
					base.AlternatingRowsDefaultCellStyle.BackColor = backColor;
					base.ColumnHeadersDefaultCellStyle.BackColor = backColor2;
					base.ColumnHeadersDefaultCellStyle.ForeColor = foreColor;
					base.RowHeadersDefaultCellStyle.BackColor = backColor2;
					base.RowHeadersDefaultCellStyle.ForeColor = color2;
					base.RowHeadersDefaultCellStyle.SelectionBackColor = selectionBackColor;
					base.RowHeadersDefaultCellStyle.SelectionForeColor = color2;
					try
					{
						base.GridColor = gridColor;
					}
					catch
					{
					}
					base.BackgroundColor = backgroundColor;
					this.smethod_1();
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}
	}
}
