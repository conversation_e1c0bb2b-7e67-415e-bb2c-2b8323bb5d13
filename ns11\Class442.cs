﻿using System;
using System.Runtime.CompilerServices;
using ns7;

namespace ns11
{
	// Token: 0x02000328 RID: 808
	internal sealed class Class442
	{
		// Token: 0x170005E0 RID: 1504
		// (get) Token: 0x0600224F RID: 8783 RVA: 0x000F3304 File Offset: 0x000F1504
		// (set) Token: 0x06002250 RID: 8784 RVA: 0x0000DA68 File Offset: 0x0000BC68
		public Enum26 HSymbolType { get; private set; }

		// Token: 0x170005E1 RID: 1505
		// (get) Token: 0x06002251 RID: 8785 RVA: 0x000F331C File Offset: 0x000F151C
		// (set) Token: 0x06002252 RID: 8786 RVA: 0x0000DA73 File Offset: 0x0000BC73
		public string Name { get; private set; }

		// Token: 0x06002253 RID: 8787 RVA: 0x0000DA7E File Offset: 0x0000BC7E
		public void method_0(Enum26 enum26_1)
		{
			this.HSymbolType = enum26_1;
		}

		// Token: 0x06002254 RID: 8788 RVA: 0x0000DA89 File Offset: 0x0000BC89
		public Class442(Enum26 enum26_1, string string_1)
		{
			this.HSymbolType = enum26_1;
			this.Name = string_1;
		}

		// Token: 0x040010B8 RID: 4280
		[CompilerGenerated]
		private Enum26 enum26_0;

		// Token: 0x040010B9 RID: 4281
		[CompilerGenerated]
		private string string_0;
	}
}
