﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns18;
using ns26;
using TEx;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace ns20
{
	// Token: 0x020000A0 RID: 160
	internal sealed partial class Form7 : Form
	{
		// Token: 0x14000022 RID: 34
		// (add) Token: 0x06000551 RID: 1361 RVA: 0x00029E20 File Offset: 0x00028020
		// (remove) Token: 0x06000552 RID: 1362 RVA: 0x00029E58 File Offset: 0x00028058
		public event EventHandler RecAdded
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000553 RID: 1363 RVA: 0x00029E90 File Offset: 0x00028090
		protected void vmethod_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x14000023 RID: 35
		// (add) Token: 0x06000554 RID: 1364 RVA: 0x00029EB8 File Offset: 0x000280B8
		// (remove) Token: 0x06000555 RID: 1365 RVA: 0x00029EF0 File Offset: 0x000280F0
		public event EventHandler RecUpdated
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000556 RID: 1366 RVA: 0x00029F28 File Offset: 0x00028128
		protected void vmethod_1()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06000557 RID: 1367 RVA: 0x0000456A File Offset: 0x0000276A
		public Form7(CfmmcAcct cfmmcAcct_1) : this()
		{
			this.method_0(cfmmcAcct_1);
		}

		// Token: 0x06000558 RID: 1368 RVA: 0x0000457B File Offset: 0x0000277B
		public Form7()
		{
			this.method_4();
			this.method_1();
			this.button_1.Click += this.button_1_Click;
			this.button_0.Focus();
		}

		// Token: 0x06000559 RID: 1369 RVA: 0x00029F50 File Offset: 0x00028150
		private void method_0(CfmmcAcct cfmmcAcct_1)
		{
			if (cfmmcAcct_1 != null)
			{
				this.cfmmcAcct_0 = cfmmcAcct_1;
				this.textBox_0.Text = cfmmcAcct_1.ID;
				this.textBox_0.Enabled = false;
				this.textBox_1.Text = cfmmcAcct_1.Password;
				this.textBox_2.Text = cfmmcAcct_1.Note;
				using (List<BindingAcct>.Enumerator enumerator = cfmmcAcct_1.BindingAccts.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Form7.Class63 @class = new Form7.Class63();
						@class.bindingAcct_0 = enumerator.Current;
						if (Base.Acct.CurrAccounts.Where(new Func<Account, bool>(@class.method_0)).Any<Account>())
						{
							for (int i = 0; i < this.checkedListBox_0.Items.Count; i++)
							{
								Account account = (this.checkedListBox_0.Items[i] as ComboBoxItem).Value as Account;
								if (account.UserName == @class.bindingAcct_0.UsrName && account.ID == @class.bindingAcct_0.Id)
								{
									this.checkedListBox_0.SetItemChecked(i, true);
									break;
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x0600055A RID: 1370 RVA: 0x0002A094 File Offset: 0x00028294
		private void method_1()
		{
			foreach (Account account in Base.Acct.CurrAccounts)
			{
				ComboBoxItem comboBoxItem = new ComboBoxItem();
				comboBoxItem.Text = account.AcctName;
				bool isChecked = false;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					ComboBoxItem comboBoxItem2 = comboBoxItem;
					comboBoxItem2.Text += Class521.smethod_0(9970);
				}
				comboBoxItem.Value = account;
				this.checkedListBox_0.Items.Add(comboBoxItem, isChecked);
			}
		}

		// Token: 0x0600055B RID: 1371 RVA: 0x0002A140 File Offset: 0x00028340
		private void button_1_Click(object sender, EventArgs e)
		{
			string text = this.textBox_0.Text;
			string text2 = this.textBox_1.Text;
			if (this.CfmmcAcctInEdit == null && (string.IsNullOrEmpty(text) || text.Length < 8))
			{
				MessageBox.Show(Class521.smethod_0(9991), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_0.Focus();
			}
			else if (string.IsNullOrEmpty(text2))
			{
				MessageBox.Show(Class521.smethod_0(10041), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_1.Focus();
			}
			else
			{
				List<BindingAcct> list = new List<BindingAcct>();
				foreach (object obj in this.checkedListBox_0.CheckedIndices)
				{
					int index = (int)obj;
					Account account = (this.checkedListBox_0.Items[index] as ComboBoxItem).Value as Account;
					BindingAcct item = new BindingAcct(account.UserName, account.ID);
					list.Add(item);
				}
				if (!list.Any<BindingAcct>())
				{
					MessageBox.Show(Class521.smethod_0(10074), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.checkedListBox_0.Focus();
				}
				else if (this.CfmmcAcctInEdit != null)
				{
					this.method_2(this.CfmmcAcctInEdit, text, text2, list);
					this.vmethod_1();
					MessageBox.Show(Class521.smethod_0(10183), Class521.smethod_0(5801), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					base.Close();
				}
				else
				{
					CfmmcAcct cfmmcAcct_ = new CfmmcAcct();
					if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(text2))
					{
						this.method_2(cfmmcAcct_, text, text2, list);
						this.vmethod_0();
						MessageBox.Show(Class521.smethod_0(10248), Class521.smethod_0(5801), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
						base.Close();
					}
				}
			}
		}

		// Token: 0x0600055C RID: 1372 RVA: 0x0002A318 File Offset: 0x00028518
		private void method_2(CfmmcAcct cfmmcAcct_1, string string_0, string string_1, List<BindingAcct> list_0)
		{
			cfmmcAcct_1.ID = string_0;
			cfmmcAcct_1.Password = string_1;
			cfmmcAcct_1.BindingAccts = list_0;
			if (!string.IsNullOrEmpty(this.textBox_2.Text))
			{
				cfmmcAcct_1.Note = this.textBox_2.Text;
			}
			Base.UI.smethod_177(Class521.smethod_0(10305), this.method_3());
			Class466.smethod_0(cfmmcAcct_1);
			Class466.smethod_24(cfmmcAcct_1);
			Base.UI.smethod_178();
		}

		// Token: 0x0600055D RID: 1373 RVA: 0x0002A388 File Offset: 0x00028588
		private Point? method_3()
		{
			Point? result = null;
			if (base.Visible)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x17000109 RID: 265
		// (get) Token: 0x0600055E RID: 1374 RVA: 0x0002A3E4 File Offset: 0x000285E4
		public CfmmcAcct CfmmcAcctInEdit
		{
			get
			{
				return this.cfmmcAcct_0;
			}
		}

		// Token: 0x0600055F RID: 1375 RVA: 0x000045B4 File Offset: 0x000027B4
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000560 RID: 1376 RVA: 0x0002A3FC File Offset: 0x000285FC
		private void method_4()
		{
			this.label_0 = new Label();
			this.textBox_0 = new TextBox();
			this.textBox_1 = new TextBox();
			this.label_1 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.checkedListBox_0 = new CheckedListBox();
			this.textBox_2 = new TextBox();
			this.label_2 = new Label();
			this.pictureBox_0 = new PictureBox();
			this.label_3 = new Label();
			this.groupBox_0 = new GroupBox();
			this.groupBox_1 = new GroupBox();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			this.groupBox_0.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(12, 27);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(82, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(10342);
			this.textBox_0.Location = new Point(99, 24);
			this.textBox_0.Name = Class521.smethod_0(10363);
			this.textBox_0.Size = new Size(161, 25);
			this.textBox_0.TabIndex = 0;
			this.textBox_1.Location = new Point(99, 59);
			this.textBox_1.Name = Class521.smethod_0(10380);
			this.textBox_1.Size = new Size(161, 25);
			this.textBox_1.TabIndex = 1;
			this.textBox_1.UseSystemPasswordChar = true;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(12, 62);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(82, 15);
			this.label_1.TabIndex = 2;
			this.label_1.Text = Class521.smethod_0(10401);
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(470, 247);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(111, 30);
			this.button_0.TabIndex = 6;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(342, 247);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(111, 30);
			this.button_1.TabIndex = 5;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.checkedListBox_0.BorderStyle = BorderStyle.FixedSingle;
			this.checkedListBox_0.FormattingEnabled = true;
			this.checkedListBox_0.Location = new Point(20, 27);
			this.checkedListBox_0.Name = Class521.smethod_0(10422);
			this.checkedListBox_0.Size = new Size(235, 82);
			this.checkedListBox_0.TabIndex = 0;
			this.textBox_2.Location = new Point(99, 95);
			this.textBox_2.Multiline = true;
			this.textBox_2.Name = Class521.smethod_0(10447);
			this.textBox_2.Size = new Size(161, 86);
			this.textBox_2.TabIndex = 2;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(12, 95);
			this.label_2.Name = Class521.smethod_0(7019);
			this.label_2.Size = new Size(52, 15);
			this.label_2.TabIndex = 10;
			this.label_2.Text = Class521.smethod_0(5836);
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(16, 123);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 25;
			this.pictureBox_0.TabStop = false;
			this.label_3.Location = new Point(39, 123);
			this.label_3.Name = Class521.smethod_0(10464);
			this.label_3.Size = new Size(216, 72);
			this.label_3.TabIndex = 24;
			this.label_3.Text = Class521.smethod_0(10477);
			this.groupBox_0.Controls.Add(this.checkedListBox_0);
			this.groupBox_0.Controls.Add(this.label_3);
			this.groupBox_0.Controls.Add(this.pictureBox_0);
			this.groupBox_0.Location = new Point(310, 22);
			this.groupBox_0.Name = Class521.smethod_0(10647);
			this.groupBox_0.Size = new Size(271, 201);
			this.groupBox_0.TabIndex = 3;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(10660);
			this.groupBox_1.Controls.Add(this.textBox_0);
			this.groupBox_1.Controls.Add(this.label_0);
			this.groupBox_1.Controls.Add(this.textBox_2);
			this.groupBox_1.Controls.Add(this.label_2);
			this.groupBox_1.Controls.Add(this.label_1);
			this.groupBox_1.Controls.Add(this.textBox_1);
			this.groupBox_1.Location = new Point(22, 22);
			this.groupBox_1.Name = Class521.smethod_0(10705);
			this.groupBox_1.Size = new Size(273, 201);
			this.groupBox_1.TabIndex = 11;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(10718);
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(603, 295);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(10743);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(10768);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x0400023A RID: 570
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400023B RID: 571
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x0400023C RID: 572
		private CfmmcAcct cfmmcAcct_0;

		// Token: 0x0400023D RID: 573
		private IContainer icontainer_0;

		// Token: 0x0400023E RID: 574
		private Label label_0;

		// Token: 0x0400023F RID: 575
		private TextBox textBox_0;

		// Token: 0x04000240 RID: 576
		private TextBox textBox_1;

		// Token: 0x04000241 RID: 577
		private Label label_1;

		// Token: 0x04000242 RID: 578
		private Button button_0;

		// Token: 0x04000243 RID: 579
		private Button button_1;

		// Token: 0x04000244 RID: 580
		private CheckedListBox checkedListBox_0;

		// Token: 0x04000245 RID: 581
		private TextBox textBox_2;

		// Token: 0x04000246 RID: 582
		private Label label_2;

		// Token: 0x04000247 RID: 583
		private PictureBox pictureBox_0;

		// Token: 0x04000248 RID: 584
		private Label label_3;

		// Token: 0x04000249 RID: 585
		private GroupBox groupBox_0;

		// Token: 0x0400024A RID: 586
		private GroupBox groupBox_1;

		// Token: 0x020000A1 RID: 161
		[CompilerGenerated]
		private sealed class Class63
		{
			// Token: 0x06000562 RID: 1378 RVA: 0x0002ABF4 File Offset: 0x00028DF4
			internal bool method_0(Account account_0)
			{
				bool result;
				if (account_0.UserName == this.bindingAcct_0.UsrName)
				{
					result = (account_0.ID == this.bindingAcct_0.Id);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400024B RID: 587
			public BindingAcct bindingAcct_0;
		}
	}
}
