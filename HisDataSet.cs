﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Threading;
using ns15;
using ns16;
using ns18;
using ns26;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200028E RID: 654
	[Serializable]
	internal sealed class HisDataSet : ISerializable
	{
		// Token: 0x1400009A RID: 154
		// (add) Token: 0x06001D21 RID: 7457 RVA: 0x000CE1D0 File Offset: 0x000CC3D0
		// (remove) Token: 0x06001D22 RID: 7458 RVA: 0x000CE208 File Offset: 0x000CC408
		public event EventHandler CurrHisDataChanging
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001D23 RID: 7459 RVA: 0x0000C400 File Offset: 0x0000A600
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x1400009B RID: 155
		// (add) Token: 0x06001D24 RID: 7460 RVA: 0x000CE240 File Offset: 0x000CC440
		// (remove) Token: 0x06001D25 RID: 7461 RVA: 0x000CE278 File Offset: 0x000CC478
		public event EventHandler CurrHisDataChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001D26 RID: 7462 RVA: 0x0000C41B File Offset: 0x0000A61B
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x1400009C RID: 156
		// (add) Token: 0x06001D27 RID: 7463 RVA: 0x000CE2B0 File Offset: 0x000CC4B0
		// (remove) Token: 0x06001D28 RID: 7464 RVA: 0x000CE2E8 File Offset: 0x000CC4E8
		public event Delegate28 CurrDateChanging
		{
			[CompilerGenerated]
			add
			{
				Delegate28 @delegate = this.delegate28_0;
				Delegate28 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate28 value2 = (Delegate28)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate28>(ref this.delegate28_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate28 @delegate = this.delegate28_0;
				Delegate28 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate28 value2 = (Delegate28)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate28>(ref this.delegate28_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001D29 RID: 7465 RVA: 0x0000C436 File Offset: 0x0000A636
		protected void method_2(DateTime dateTime_4, DateTime dateTime_5)
		{
			Delegate28 @delegate = this.delegate28_0;
			if (@delegate != null)
			{
				@delegate(this, new EventArgs24(dateTime_4, dateTime_5));
			}
		}

		// Token: 0x1400009D RID: 157
		// (add) Token: 0x06001D2A RID: 7466 RVA: 0x000CE320 File Offset: 0x000CC520
		// (remove) Token: 0x06001D2B RID: 7467 RVA: 0x000CE358 File Offset: 0x000CC558
		public event Delegate28 CurrDateChanged
		{
			[CompilerGenerated]
			add
			{
				Delegate28 @delegate = this.delegate28_1;
				Delegate28 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate28 value2 = (Delegate28)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate28>(ref this.delegate28_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate28 @delegate = this.delegate28_1;
				Delegate28 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate28 value2 = (Delegate28)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate28>(ref this.delegate28_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001D2C RID: 7468 RVA: 0x0000C453 File Offset: 0x0000A653
		protected void method_3(DateTime dateTime_4, DateTime dateTime_5)
		{
			Delegate28 @delegate = this.delegate28_1;
			if (@delegate != null)
			{
				@delegate(this, new EventArgs24(dateTime_4, dateTime_5));
			}
		}

		// Token: 0x06001D2D RID: 7469 RVA: 0x0000C470 File Offset: 0x0000A670
		public HisDataSet(SymbDataSet sds, SortedList<DateTime, HisData> sHdList)
		{
			this.symbDataSet_0 = sds;
			this.int_0 = sds.SymblID;
			this.sortedList_0 = sHdList;
		}

		// Token: 0x06001D2E RID: 7470 RVA: 0x000CE390 File Offset: 0x000CC590
		public HisDataSet(SymbDataSet sds, DateTime lastSymbDT)
		{
			this.symbDataSet_0 = sds;
			this.int_0 = sds.SymblID;
			DateTime dateTime = lastSymbDT;
			int num = 30;
			int num2 = 5;
			int num3 = 0;
			int num4 = 0;
			DateTime dateTime2 = this.HisDataStartDT.Value;
			if (TApp.IsTrialUser && sds.HisDataList != null && sds.HisDataList.Any<KeyValuePair<DateTime, HisData>>() && sds.HisDataList.First<KeyValuePair<DateTime, HisData>>().Key < dateTime2)
			{
				dateTime2 = sds.HisDataList.First<KeyValuePair<DateTime, HisData>>().Key;
				if ((lastSymbDT - dateTime2).TotalDays < 240.0)
				{
					dateTime2 = lastSymbDT.AddDays(-240.0);
				}
			}
			int num5 = Convert.ToInt32(Math.Ceiling((lastSymbDT.Date - dateTime2).TotalDays));
			int num6 = Convert.ToInt32(Math.Ceiling((this.HisDataEndDT.Value - lastSymbDT.Date).TotalDays));
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			while (sortedList == null || !sortedList.Any<KeyValuePair<DateTime, HisData>>() || sortedList.Count < 3000)
			{
				DateTime t = dateTime.Date.AddDays((double)(-(double)num));
				DateTime dateTime3 = lastSymbDT.AddDays(-1.0);
				if (t < dateTime2)
				{
					num3 = num5;
				}
				sortedList = this.SymbDataSet.method_87(t, dateTime3);
				if (num3 == num5)
				{
					IL_195:
					dateTime = lastSymbDT;
					SortedList<DateTime, HisData> sortedList2 = new SortedList<DateTime, HisData>();
					while (sortedList2 == null || !sortedList2.Any<KeyValuePair<DateTime, HisData>>())
					{
						DateTime dateTime4 = dateTime;
						DateTime t2 = dateTime.Date.AddDays((double)num2);
						if (t2 > this.HisDataEndDT.Value)
						{
							num4 = num6;
						}
						sortedList2 = this.SymbDataSet.method_87(dateTime4, t2);
						if (num4 == num6)
						{
							IL_218:
							if (sortedList2 != null)
							{
								foreach (KeyValuePair<DateTime, HisData> keyValuePair in sortedList2)
								{
									try
									{
										sortedList.Add(keyValuePair.Key, keyValuePair.Value);
									}
									catch (Exception exception_)
									{
										Class184.smethod_0(exception_);
									}
								}
							}
							this.sortedList_0 = sortedList;
							this.SubsetStartDate = this.HisDataStartDT.Value;
							this.SubsetEndDate = this.HisDataEndDT.Value;
							this.method_4(lastSymbDT);
							this.method_14();
							return;
						}
						num4 += num2;
						if (num4 > num6)
						{
							num4 = num6;
						}
						dateTime = lastSymbDT.AddDays((double)num4);
					}
					goto IL_218;
				}
				num3 += num;
				if (num3 > num5)
				{
					num3 = num5;
				}
				dateTime = lastSymbDT.AddDays((double)(-(double)num3));
			}
			goto IL_195;
		}

		// Token: 0x06001D2F RID: 7471 RVA: 0x000CE664 File Offset: 0x000CC864
		protected HisDataSet(SerializationInfo info, StreamingContext context)
		{
			this.sortedList_0 = (SortedList<DateTime, HisData>)info.GetValue(Class521.smethod_0(85294), typeof(SortedList<DateTime, HisData>));
			this.dateTime_0 = info.GetDateTime(Class521.smethod_0(85319));
			this.dateTime_1 = info.GetDateTime(Class521.smethod_0(85348));
			this.dateTime_2 = info.GetDateTime(Class521.smethod_0(85373));
			this.dateTime_3 = info.GetDateTime(Class521.smethod_0(85394));
			this.int_0 = info.GetInt32(Class521.smethod_0(85415));
			this.nullable_1 = (int?)info.GetValue(Class521.smethod_0(85428), typeof(int?));
			this.hisData_0 = (HisData)info.GetValue(Class521.smethod_0(85461), typeof(HisData));
			this.double_0 = info.GetDouble(Class521.smethod_0(85478));
			try
			{
				this.double_1 = info.GetDouble(Class521.smethod_0(85478));
			}
			catch
			{
				this.double_1 = 0.0;
			}
		}

		// Token: 0x06001D30 RID: 7472 RVA: 0x000CE7A8 File Offset: 0x000CC9A8
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(85294), this.sortedList_0);
			info.AddValue(Class521.smethod_0(85319), this.dateTime_0);
			info.AddValue(Class521.smethod_0(85348), this.dateTime_1);
			info.AddValue(Class521.smethod_0(85373), this.dateTime_2);
			info.AddValue(Class521.smethod_0(85394), this.dateTime_3);
			info.AddValue(Class521.smethod_0(85415), this.symbDataSet_0.SymblID);
			info.AddValue(Class521.smethod_0(85428), this.nullable_1);
			info.AddValue(Class521.smethod_0(85461), this.hisData_0);
			info.AddValue(Class521.smethod_0(85478), this.double_0);
			info.AddValue(Class521.smethod_0(85495), this.double_1);
		}

		// Token: 0x06001D31 RID: 7473 RVA: 0x000CE8A0 File Offset: 0x000CCAA0
		public bool method_4(DateTime dateTime_4)
		{
			HisDataSet.Class342 @class = new HisDataSet.Class342();
			@class.dateTime_0 = dateTime_4;
			HisData hisData = null;
			if (this.sortedList_0 != null)
			{
				if (this.sortedList_0.TryGetValue(@class.dateTime_0, out hisData))
				{
					this.CurrHisData = hisData;
					return true;
				}
				if (!Base.UI.Form.IsInBlindTestMode)
				{
					if (Base.Data.SymbDataSets.Count != 1)
					{
						hisData = this.sortedList_0.Values.LastOrDefault(new Func<HisData, bool>(@class.method_0));
						if (hisData != null)
						{
							this.CurrHisData = hisData;
							return true;
						}
						goto IL_A7;
					}
				}
				this.CurrHisData = this.method_13(@class.dateTime_0);
				return true;
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(85512)));
			}
			IL_A7:
			return false;
		}

		// Token: 0x06001D32 RID: 7474 RVA: 0x000CE95C File Offset: 0x000CCB5C
		public bool method_5()
		{
			DateTime dateTime = this.dateTime_2.Date.AddDays(-20.0);
			DateTime t = this.dateTime_2.Date;
			SortedList<DateTime, HisData> sortedList = null;
			int num = dateTime.Year;
			List<DatFileInfo> list = null;
			while (list == null && num <= this.dateTime_3.Date.Year)
			{
				list = Base.Data.smethod_41(this.SymblID, num);
				if (list != null)
				{
					if (this.dateTime_2.Date.Year != num)
					{
						break;
					}
					DateTime endDate = list.First<DatFileInfo>().EndDate;
					if (endDate.Date >= this.dateTime_2.Date)
					{
						break;
					}
					num++;
				}
				else
				{
					num++;
				}
			}
			int year = this.SymbDataSet.CurrStkMeta.BeginDate.Value.Year;
			if (list == null)
			{
				num = dateTime.Year;
				while (list == null && num >= year)
				{
					list = Base.Data.smethod_41(this.SymblID, num);
					if (list != null)
					{
						break;
					}
					num--;
				}
			}
			if (list != null && list.Any<DatFileInfo>())
			{
				HisDataSet.Class343 @class = new HisDataSet.Class343();
				if (num > this.dateTime_3.Date.Year)
				{
					num = this.dateTime_3.Date.Year;
				}
				else if (num < year)
				{
					num = year;
				}
				DateTime beginDate = list.First<DatFileInfo>().BeginDate;
				if (num != dateTime.Year)
				{
					dateTime = beginDate;
					t = dateTime.AddDays(20.0);
				}
				if (t < beginDate)
				{
					t = beginDate;
				}
				int num2 = 5;
				@class.dateTime_0 = t;
				while (sortedList == null || !sortedList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Any<KeyValuePair<DateTime, HisData>>())
				{
					t = t.AddDays((double)num2);
					if (t > this.dateTime_3.Date)
					{
						t = this.dateTime_3.Date;
					}
					sortedList = this.SymbDataSet.method_87(dateTime, t);
					if (t >= this.dateTime_3.Date)
					{
						IL_203:
						if (sortedList != null && sortedList.Any<KeyValuePair<DateTime, HisData>>())
						{
							this.FetchedHisDataList = sortedList;
							if (this.FetchedHisDataList != null && this.FetchedHisDataList.Any<KeyValuePair<DateTime, HisData>>())
							{
								HisData value = this.sortedList_0.FirstOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(this.method_31)).Value;
								if (value != null)
								{
									this.hisData_0 = value;
									this.method_14();
								}
							}
							return true;
						}
						return false;
					}
				}
				goto IL_203;
			}
			return false;
		}

		// Token: 0x06001D33 RID: 7475 RVA: 0x000CEBD8 File Offset: 0x000CCDD8
		private int method_6(DateTime dateTime_4)
		{
			int num = dateTime_4.Year;
			List<DatFileInfo> list = null;
			while (list == null && num <= this.dateTime_3.Date.Year)
			{
				list = Base.Data.smethod_41(this.SymblID, num);
				if (list != null)
				{
					if (this.dateTime_2.Date.Year != num)
					{
						break;
					}
					DateTime endDate = list.First<DatFileInfo>().EndDate;
					if (endDate.Date >= this.dateTime_2.Date)
					{
						break;
					}
				}
				else
				{
					num++;
				}
			}
			if (list == null)
			{
				num = dateTime_4.Year;
				while (list == null && num >= this.dateTime_2.Date.Year)
				{
					list = Base.Data.smethod_41(this.SymblID, num);
					if (list != null)
					{
						break;
					}
					num--;
				}
			}
			if (num > this.dateTime_3.Date.Year)
			{
				num = this.dateTime_3.Date.Year;
			}
			else if (num < this.dateTime_2.Date.Year)
			{
				num = this.dateTime_2.Date.Year;
			}
			return num;
		}

		// Token: 0x06001D34 RID: 7476 RVA: 0x000CECF4 File Offset: 0x000CCEF4
		public bool method_7(DateTime dateTime_4, DateTime? nullable_3 = null)
		{
			DateTime dateTime = this.dateTime_2;
			DateTime dateTime2 = this.dateTime_3;
			this.dateTime_2 = dateTime_4;
			if (nullable_3 != null)
			{
				this.dateTime_3 = nullable_3.Value;
			}
			bool result;
			if (!this.method_5())
			{
				this.dateTime_2 = dateTime;
				this.dateTime_3 = dateTime2;
				result = false;
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001D35 RID: 7477 RVA: 0x000CED4C File Offset: 0x000CCF4C
		public bool method_8()
		{
			int int_ = 30;
			if (this.SymbDataSet.CurrSymbol.Type == TradingSymbolType.Forex)
			{
				int_ = 15;
			}
			return this.method_9(int_);
		}

		// Token: 0x06001D36 RID: 7478 RVA: 0x000CED80 File Offset: 0x000CCF80
		public bool method_9(int int_1)
		{
			bool flag = false;
			int num = int_1;
			while (!flag)
			{
				DateTime dateTime_ = this.FetchedHDDataStartDate.Date.AddDays((double)(-(double)num));
				flag = this.method_10(dateTime_, this.FetchedHDDataEndDate.Date);
				num += int_1;
				if (dateTime_.Date < this.SymbDataSet.CurrStkMeta.BeginDate.Value.Date)
				{
					break;
				}
			}
			return flag;
		}

		// Token: 0x06001D37 RID: 7479 RVA: 0x000CEE00 File Offset: 0x000CD000
		public bool method_10(DateTime dateTime_4, DateTime dateTime_5)
		{
			SortedList<DateTime, HisData> sortedList = this.SymbDataSet.method_87(dateTime_4, dateTime_5);
			bool result;
			if (sortedList != null && sortedList.Any<KeyValuePair<DateTime, HisData>>() && this.FetchedHisDataList != null && this.FetchedHisDataList.Any<KeyValuePair<DateTime, HisData>>() && (!(this.FetchedHisDataList.Keys.First<DateTime>() == sortedList.Keys.First<DateTime>()) || !(this.FetchedHisDataList.Keys.Last<DateTime>() == sortedList.Keys.Last<DateTime>())))
			{
				this.FetchedHisDataList = sortedList;
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001D38 RID: 7480 RVA: 0x000CEE90 File Offset: 0x000CD090
		public bool method_11(DateTime dateTime_4, int int_1, DateTime dateTime_5)
		{
			bool flag = false;
			int num = int_1;
			while (!flag || (dateTime_4 - this.FetchedHDDataStartDate.Date).TotalDays < (double)int_1)
			{
				DateTime dateTime_6 = dateTime_4.Date.AddDays((double)(-(double)num));
				flag = this.method_10(dateTime_6, dateTime_5);
				num += int_1;
				if (dateTime_6.Date < this.SymbDataSet.CurrStkMeta.BeginDate.Value.Date)
				{
					IL_7F:
					return flag;
				}
			}
			goto IL_7F;
		}

		// Token: 0x06001D39 RID: 7481 RVA: 0x000CEF24 File Offset: 0x000CD124
		public HisData method_12(DateTime dateTime_4)
		{
			HisDataSet.Class344 @class = new HisDataSet.Class344();
			@class.dateTime_0 = dateTime_4;
			HisData hisData = null;
			if (!this.FetchedHisDataList.TryGetValue(@class.dateTime_0, out hisData))
			{
				HisData hisData2 = this.FetchedHisDataList.Values.LastOrDefault(new Func<HisData, bool>(@class.method_0));
				if (hisData2 == null)
				{
					hisData2 = this.FetchedHisDataList.Values.FirstOrDefault(new Func<HisData, bool>(@class.method_1));
				}
				if (hisData2 != null)
				{
					hisData = new HisData();
					hisData.Open = hisData2.Close;
					hisData.High = hisData2.Close;
					hisData.Low = hisData2.Close;
					hisData.Close = hisData2.Close;
					hisData.Volume = hisData2.Volume;
					hisData.Amount = hisData2.Amount;
					hisData.Date = @class.dateTime_0;
				}
			}
			return hisData;
		}

		// Token: 0x06001D3A RID: 7482 RVA: 0x000CEFF8 File Offset: 0x000CD1F8
		public HisData method_13(DateTime dateTime_4)
		{
			HisDataSet.Class345 @class = new HisDataSet.Class345();
			@class.dateTime_0 = dateTime_4;
			HisData hisData = new HisData();
			HisData result;
			if (this.sortedList_0.TryGetValue(@class.dateTime_0, out hisData))
			{
				result = hisData;
			}
			else
			{
				hisData = this.sortedList_0.Values.LastOrDefault(new Func<HisData, bool>(@class.method_0));
				if (hisData == null)
				{
					hisData = this.sortedList_0.Values.FirstOrDefault(new Func<HisData, bool>(@class.method_1));
				}
				if (hisData == null)
				{
					throw new Exception(Class521.smethod_0(85553));
				}
				result = hisData;
			}
			return result;
		}

		// Token: 0x06001D3B RID: 7483 RVA: 0x0000C494 File Offset: 0x0000A694
		public void method_14()
		{
			this.method_15(this.HisDataDT);
		}

		// Token: 0x06001D3C RID: 7484 RVA: 0x000CF088 File Offset: 0x000CD288
		public void method_15(DateTime dateTime_4)
		{
			if (dateTime_4 != DateTime.MinValue)
			{
				this.exchgOBT_0 = this.SymbDataSet.method_67(dateTime_4);
				this.symbNtTrDate_0 = this.SymbDataSet.method_68(dateTime_4);
				this.nullable_2 = new DateTime?(this.method_17(dateTime_4));
			}
		}

		// Token: 0x06001D3D RID: 7485 RVA: 0x000CF0DC File Offset: 0x000CD2DC
		public DateTime method_16()
		{
			return this.method_18(this.FetchedHisDataList, this.HisDataDT, this.SymbDataSet.CurrSymbol);
		}

		// Token: 0x06001D3E RID: 7486 RVA: 0x000CF10C File Offset: 0x000CD30C
		public DateTime method_17(DateTime dateTime_4)
		{
			return this.method_18(this.FetchedHisDataList, dateTime_4, this.SymbDataSet.CurrSymbol);
		}

		// Token: 0x06001D3F RID: 7487 RVA: 0x000CF138 File Offset: 0x000CD338
		public DateTime method_18(SortedList<DateTime, HisData> sortedList_1, DateTime dateTime_4, TradingSymbol tradingSymbol_0)
		{
			if (this.CurrExchgOBT == null)
			{
				this.exchgOBT_0 = this.SymbDataSet.method_67(dateTime_4);
			}
			return this.method_19(sortedList_1, dateTime_4, this.CurrExchgOBT, this.SymbDataSet.CurrSymbol, 1, null);
		}

		// Token: 0x06001D40 RID: 7488 RVA: 0x000CF188 File Offset: 0x000CD388
		public DateTime method_19(SortedList<DateTime, HisData> sortedList_1, DateTime dateTime_4, ExchgOBT exchgOBT_1, TradingSymbol tradingSymbol_0, int int_1 = 1, int? nullable_3 = null)
		{
			HisDataSet.Class346 @class = new HisDataSet.Class346();
			@class.dateTime_0 = dateTime_4;
			@class.exchgOBT_0 = exchgOBT_1;
			DateTime? dateTime = null;
			if (@class.exchgOBT_0.IsDayTradingDT(@class.dateTime_0))
			{
				if (@class.exchgOBT_0.SupportsNightTrading)
				{
					try
					{
						int num = sortedList_1.IndexOfKey(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay).AddMinutes((double)int_1));
						if (num < 0 && nullable_3 != null)
						{
							num = sortedList_1.IndexOfKey(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay).AddMinutes((double)nullable_3.Value));
						}
						if (num > 0)
						{
							dateTime = new DateTime?(sortedList_1.Keys[num - 1]);
						}
						else
						{
							IEnumerable<KeyValuePair<DateTime, HisData>> source = sortedList_1.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0));
							dateTime = new DateTime?(source.Last<KeyValuePair<DateTime, HisData>>().Key);
						}
					}
					catch
					{
					}
					if (dateTime != null)
					{
						dateTime = @class.exchgOBT_0.GetNightTradingStartDT(dateTime.Value);
						if (dateTime == null)
						{
							dateTime = new DateTime?(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay));
						}
					}
					else
					{
						dateTime = new DateTime?(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay));
					}
				}
				else
				{
					dateTime = new DateTime?(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay));
				}
			}
			else if (@class.exchgOBT_0.SupportsNightTrading)
			{
				dateTime = @class.exchgOBT_0.GetNightTradingStartDT(@class.dateTime_0);
				if (dateTime == null)
				{
					dateTime = new DateTime?(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay));
				}
			}
			else
			{
				dateTime = new DateTime?(@class.dateTime_0.Date.Add(@class.exchgOBT_0.DayOpenTime.Value.TimeOfDay));
			}
			return dateTime.Value;
		}

		// Token: 0x06001D41 RID: 7489 RVA: 0x000CF448 File Offset: 0x000CD648
		public bool method_20()
		{
			return this.method_22(this.CurrDayBeginDT, this.HisDataDT, this.CurrExchgOBT);
		}

		// Token: 0x06001D42 RID: 7490 RVA: 0x000CF474 File Offset: 0x000CD674
		public bool method_21(DateTime dateTime_4)
		{
			DateTime dateTime_5 = this.method_17(dateTime_4);
			return this.method_22(dateTime_5, dateTime_4, this.CurrExchgOBT);
		}

		// Token: 0x06001D43 RID: 7491 RVA: 0x000CF49C File Offset: 0x000CD69C
		public bool method_22(DateTime dateTime_4, DateTime dateTime_5, ExchgOBT exchgOBT_1)
		{
			bool result;
			if (exchgOBT_1.NightOpenTime == null)
			{
				result = false;
			}
			else
			{
				result = (dateTime_4.TimeOfDay == exchgOBT_1.NightOpenTime.Value.TimeOfDay);
			}
			return result;
		}

		// Token: 0x06001D44 RID: 7492 RVA: 0x000CF4E4 File Offset: 0x000CD6E4
		public void method_23()
		{
			if (this.HisDataDT != default(DateTime))
			{
				this.method_24(this.HisDataDT);
			}
		}

		// Token: 0x06001D45 RID: 7493 RVA: 0x000CF518 File Offset: 0x000CD718
		public void method_24(DateTime dateTime_4)
		{
			HisDataSet.Class347 @class = new HisDataSet.Class347();
			@class.dateTime_0 = dateTime_4;
			if (this.FetchedHisDataList.Keys.Last<DateTime>() > @class.dateTime_0 && (!this.method_26(@class.dateTime_0, this.CurrHisData.Date) || this.CurrExchgOBT.IsDayEndDT(@class.dateTime_0)))
			{
				try
				{
					int num = this.FetchedHisDataList.IndexOfKey(@class.dateTime_0);
					DateTime dateTime_5;
					if (num >= 0)
					{
						if (num >= this.FetchedHisDataList.Count - 1)
						{
							return;
						}
						dateTime_5 = this.FetchedHisDataList.Keys[num + 1];
					}
					else
					{
						dateTime_5 = this.FetchedHisDataList.Keys.FirstOrDefault(new Func<DateTime, bool>(@class.method_0));
					}
					this.method_15(dateTime_5);
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001D46 RID: 7494 RVA: 0x000CF5FC File Offset: 0x000CD7FC
		public bool method_25(HisData hisData_2, HisData hisData_3)
		{
			bool result;
			if ((hisData_2 == null && hisData_3 != null) || (hisData_2 != null && hisData_3 == null))
			{
				result = false;
			}
			else if (hisData_2 == null && hisData_3 == null)
			{
				result = true;
			}
			else
			{
				result = this.method_26(hisData_2.Date, hisData_3.Date);
			}
			return result;
		}

		// Token: 0x06001D47 RID: 7495 RVA: 0x000CF63C File Offset: 0x000CD83C
		public bool method_26(DateTime dateTime_4, DateTime dateTime_5)
		{
			DateTime value = this.CurrExchgOBT.DayCloseTime.Value;
			TimeSpan value2 = value.Date.AddDays(1.0).AddMinutes(-1.0) - value;
			DateTime dateTime = dateTime_4.Add(value2);
			DateTime dateTime2 = dateTime_5.Add(value2);
			bool result;
			if (dateTime.Date != dateTime2.Date)
			{
				result = false;
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001D48 RID: 7496 RVA: 0x000CF6C4 File Offset: 0x000CD8C4
		private int method_27(SortedList<DateTime, HisData> sortedList_1)
		{
			List<int> list = this.method_28(sortedList_1);
			int num = 0;
			int count = list.Count;
			if (count > 1)
			{
				for (int i = 1; i < count - 3; i++)
				{
					int num2 = list[count - i];
					int num3 = list[count - i - 1];
					int num4 = num2 - num3;
					num2 = list[count - i - 1];
					num3 = list[count - i - 2];
					int num5 = num2 - num3;
					num2 = list[count - i - 2];
					num3 = list[count - i - 3];
					int num6 = num2 - num3;
					if (num4 == num5 && num5 == num6)
					{
						num = num4;
						break;
					}
				}
			}
			if (num == 0)
			{
				num = list[0] + 1;
			}
			return num;
		}

		// Token: 0x06001D49 RID: 7497 RVA: 0x000CF788 File Offset: 0x000CD988
		private List<int> method_28(SortedList<DateTime, HisData> sortedList_1)
		{
			DateTime date = Convert.ToDateTime(sortedList_1.First<KeyValuePair<DateTime, HisData>>().Value.Date).Date;
			DateTime date2 = Convert.ToDateTime(sortedList_1.Last<KeyValuePair<DateTime, HisData>>().Value.Date).Date;
			List<int> list = new List<int>();
			foreach (KeyValuePair<DateTime, HisData> keyValuePair in sortedList_1.OrderBy(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataSet.<>c.<>9.method_0)).GroupBy(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataSet.<>c.<>9.method_1)).Select(new Func<IGrouping<DateTime, KeyValuePair<DateTime, HisData>>, KeyValuePair<DateTime, HisData>>(HisDataSet.<>c.<>9.method_2)))
			{
				list.Add(sortedList_1.IndexOfKey(keyValuePair.Key));
			}
			return list;
		}

		// Token: 0x06001D4A RID: 7498 RVA: 0x000CF89C File Offset: 0x000CDA9C
		public bool method_29()
		{
			bool result;
			if (this.sortedList_0 != null)
			{
				result = (this.sortedList_0.Count > 0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x17000493 RID: 1171
		// (get) Token: 0x06001D4B RID: 7499 RVA: 0x000CF8C8 File Offset: 0x000CDAC8
		// (set) Token: 0x06001D4C RID: 7500 RVA: 0x0000C4A4 File Offset: 0x0000A6A4
		public SortedList<DateTime, HisData> FetchedHisDataList
		{
			get
			{
				return this.sortedList_0;
			}
			set
			{
				this.sortedList_0 = value;
				if (this.sortedList_0 != null && this.sortedList_0.Any<KeyValuePair<DateTime, HisData>>())
				{
					this.nullable_1 = new int?(this.method_27(this.sortedList_0));
				}
			}
		}

		// Token: 0x17000494 RID: 1172
		// (get) Token: 0x06001D4D RID: 7501 RVA: 0x000CF8E0 File Offset: 0x000CDAE0
		// (set) Token: 0x06001D4E RID: 7502 RVA: 0x0000C4DB File Offset: 0x0000A6DB
		public List<HDTick> FetchedTickDataList
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x17000495 RID: 1173
		// (get) Token: 0x06001D4F RID: 7503 RVA: 0x000CF8F8 File Offset: 0x000CDAF8
		// (set) Token: 0x06001D50 RID: 7504 RVA: 0x0000C4E6 File Offset: 0x0000A6E6
		public DateTime? CurrTickDT
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x17000496 RID: 1174
		// (get) Token: 0x06001D51 RID: 7505 RVA: 0x000CF910 File Offset: 0x000CDB10
		// (set) Token: 0x06001D52 RID: 7506 RVA: 0x0000C4F1 File Offset: 0x0000A6F1
		public DateTime SubsetStartDate
		{
			get
			{
				return this.dateTime_2;
			}
			set
			{
				this.dateTime_2 = value;
			}
		}

		// Token: 0x17000497 RID: 1175
		// (get) Token: 0x06001D53 RID: 7507 RVA: 0x000CF928 File Offset: 0x000CDB28
		// (set) Token: 0x06001D54 RID: 7508 RVA: 0x0000C4FC File Offset: 0x0000A6FC
		public DateTime SubsetEndDate
		{
			get
			{
				return this.dateTime_3;
			}
			set
			{
				this.dateTime_3 = value;
			}
		}

		// Token: 0x17000498 RID: 1176
		// (get) Token: 0x06001D55 RID: 7509 RVA: 0x000CF940 File Offset: 0x000CDB40
		public DateTime FetchedHDDataStartDate
		{
			get
			{
				return this.sortedList_0.First<KeyValuePair<DateTime, HisData>>().Value.Date;
			}
		}

		// Token: 0x17000499 RID: 1177
		// (get) Token: 0x06001D56 RID: 7510 RVA: 0x000CF96C File Offset: 0x000CDB6C
		public DateTime FetchedHDDataEndDate
		{
			get
			{
				return this.sortedList_0.Last<KeyValuePair<DateTime, HisData>>().Value.Date;
			}
		}

		// Token: 0x1700049A RID: 1178
		// (get) Token: 0x06001D57 RID: 7511 RVA: 0x000CF998 File Offset: 0x000CDB98
		public DateTime? HisDataStartDT
		{
			get
			{
				return this.SymbDataSet.CurrStkMeta.BeginDate;
			}
		}

		// Token: 0x1700049B RID: 1179
		// (get) Token: 0x06001D58 RID: 7512 RVA: 0x000CF9BC File Offset: 0x000CDBBC
		public DateTime? HisDataEndDT
		{
			get
			{
				return this.SymbDataSet.CurrStkMeta.EndDate;
			}
		}

		// Token: 0x1700049C RID: 1180
		// (get) Token: 0x06001D59 RID: 7513 RVA: 0x000CF9E0 File Offset: 0x000CDBE0
		// (set) Token: 0x06001D5A RID: 7514 RVA: 0x000CF9F8 File Offset: 0x000CDBF8
		public HisData CurrHisData
		{
			get
			{
				return this.hisData_0;
			}
			set
			{
				if (this.hisData_0 != value)
				{
					bool flag = false;
					DateTime? dateTime = null;
					if (this.hisData_0 != null)
					{
						dateTime = new DateTime?(this.hisData_0.Date);
					}
					if (!this.method_25(this.hisData_0, value))
					{
						flag = true;
					}
					this.hisData_0 = value;
					this.method_1();
					if (flag && this.hisData_0 != null && dateTime != null)
					{
						this.method_3(dateTime.Value, this.hisData_0.Date);
					}
				}
			}
		}

		// Token: 0x1700049D RID: 1181
		// (get) Token: 0x06001D5B RID: 7515 RVA: 0x000CFA80 File Offset: 0x000CDC80
		// (set) Token: 0x06001D5C RID: 7516 RVA: 0x000CFA98 File Offset: 0x000CDC98
		public HisData ComingHisData
		{
			get
			{
				return this.hisData_1;
			}
			set
			{
				if (this.hisData_1 != value)
				{
					bool flag = false;
					DateTime? dateTime = null;
					if (this.CurrHisData != null)
					{
						dateTime = new DateTime?(this.CurrHisData.Date);
					}
					if (!this.method_25(this.CurrHisData, value))
					{
						flag = true;
					}
					this.hisData_1 = value;
					this.method_0();
					if (flag && this.hisData_1 != null && dateTime != null)
					{
						this.method_2(dateTime.Value, this.hisData_1.Date);
					}
				}
			}
		}

		// Token: 0x1700049E RID: 1182
		// (get) Token: 0x06001D5D RID: 7517 RVA: 0x000CFB20 File Offset: 0x000CDD20
		// (set) Token: 0x06001D5E RID: 7518 RVA: 0x0000C507 File Offset: 0x0000A707
		public int SymblID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x1700049F RID: 1183
		// (get) Token: 0x06001D5F RID: 7519 RVA: 0x000CFB38 File Offset: 0x000CDD38
		// (set) Token: 0x06001D60 RID: 7520 RVA: 0x0000C512 File Offset: 0x0000A712
		public SymbDataSet SymbDataSet
		{
			get
			{
				return this.symbDataSet_0;
			}
			set
			{
				this.symbDataSet_0 = value;
			}
		}

		// Token: 0x170004A0 RID: 1184
		// (get) Token: 0x06001D61 RID: 7521 RVA: 0x000CFB50 File Offset: 0x000CDD50
		public int TotalMinsPerTradingDay
		{
			get
			{
				return this.CurrExchgOBT.GetTotalTradingMins(this.IfCurrDayHasNightData);
			}
		}

		// Token: 0x170004A1 RID: 1185
		// (get) Token: 0x06001D62 RID: 7522 RVA: 0x000CFB74 File Offset: 0x000CDD74
		// (set) Token: 0x06001D63 RID: 7523 RVA: 0x0000C51D File Offset: 0x0000A71D
		public double LastItemVol
		{
			get
			{
				return this.double_0;
			}
			set
			{
				this.double_0 = value;
			}
		}

		// Token: 0x170004A2 RID: 1186
		// (get) Token: 0x06001D64 RID: 7524 RVA: 0x000CFB8C File Offset: 0x000CDD8C
		// (set) Token: 0x06001D65 RID: 7525 RVA: 0x0000C528 File Offset: 0x0000A728
		public double LastItemAmt
		{
			get
			{
				return this.double_1;
			}
			set
			{
				this.double_1 = value;
			}
		}

		// Token: 0x170004A3 RID: 1187
		// (get) Token: 0x06001D66 RID: 7526 RVA: 0x000CFBA4 File Offset: 0x000CDDA4
		public bool IsCurrHisDataDayEndRec
		{
			get
			{
				bool result;
				try
				{
					if (this.CurrExchgOBT != null && this.CurrExchgOBT.DayCloseTime != null && this.SymbDataSet.LastHisData != null)
					{
						result = (this.CurrExchgOBT.IsDayEndDT(this.SymbDataSet.LastHisData.Date) || this.FetchedHisDataList.Keys.Last<DateTime>() == this.SymbDataSet.LastHisData.Date);
					}
					else
					{
						HisDataSet.Class348 @class = new HisDataSet.Class348();
						SortedList<DateTime, HisData> fetchedHisDataList = this.FetchedHisDataList;
						@class.hisData_0 = this.SymbDataSet.LastHisData;
						if (fetchedHisDataList != null && @class.hisData_0 != null)
						{
							result = !fetchedHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Any<KeyValuePair<DateTime, HisData>>();
						}
						else
						{
							result = false;
						}
					}
				}
				catch
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170004A4 RID: 1188
		// (get) Token: 0x06001D67 RID: 7527 RVA: 0x000CFC8C File Offset: 0x000CDE8C
		public bool IsCurrHisDataEndRec
		{
			get
			{
				bool result;
				if (this.CurrHisData.Date.Date == this.SubsetEndDate.Date)
				{
					result = this.IsCurrHisDataDayEndRec;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170004A5 RID: 1189
		// (get) Token: 0x06001D68 RID: 7528 RVA: 0x000CFCD0 File Offset: 0x000CDED0
		public ExchgOBT CurrExchgOBT
		{
			get
			{
				return this.exchgOBT_0;
			}
		}

		// Token: 0x170004A6 RID: 1190
		// (get) Token: 0x06001D69 RID: 7529 RVA: 0x000CFCE8 File Offset: 0x000CDEE8
		public SymbNtTrDate CurrSymbNtTrDate
		{
			get
			{
				return this.symbNtTrDate_0;
			}
		}

		// Token: 0x170004A7 RID: 1191
		// (get) Token: 0x06001D6A RID: 7530 RVA: 0x000CFD00 File Offset: 0x000CDF00
		public bool IfCurrDayHasNightData
		{
			get
			{
				return this.method_20();
			}
		}

		// Token: 0x170004A8 RID: 1192
		// (get) Token: 0x06001D6B RID: 7531 RVA: 0x000CFD18 File Offset: 0x000CDF18
		public DateTime CurrDayBeginDT
		{
			get
			{
				TimeSpan timeOfDay = this.CurrExchgOBT.DayOpenTime.Value.TimeOfDay;
				TimeSpan timeOfDay2 = this.CurrExchgOBT.DayCloseTime.Value.TimeOfDay;
				TimeSpan timeOfDay3 = this.CurrHisData.Date.TimeOfDay;
				bool flag = this.nullable_2 == null;
				bool flag2 = this.nullable_2.Value >= this.CurrHisData.Date && (timeOfDay3 != timeOfDay2 || (timeOfDay3 == timeOfDay2 && Base.UI.Form.IsSpanMovePrev));
				bool flag3 = this.nullable_2.Value.TimeOfDay < timeOfDay2 && (this.CurrHisData.Date - this.nullable_2.Value).TotalHours > 24.0;
				bool flag4 = this.nullable_2.Value.TimeOfDay > timeOfDay2 && (this.CurrHisData.Date - this.nullable_2.Value).TotalHours > 24.0 && (timeOfDay3 < timeOfDay || timeOfDay3 > timeOfDay2);
				bool flag5 = this.HisDataDT.Date != this.CurrHisData.Date.Date;
				if (flag || flag2 || flag3 || flag4 || flag5)
				{
					this.nullable_2 = new DateTime?(this.method_16());
				}
				return this.nullable_2.Value;
			}
		}

		// Token: 0x170004A9 RID: 1193
		// (get) Token: 0x06001D6C RID: 7532 RVA: 0x000CFED4 File Offset: 0x000CE0D4
		public double? LastDayClose
		{
			get
			{
				return this.method_30();
			}
		}

		// Token: 0x06001D6D RID: 7533 RVA: 0x000CFEEC File Offset: 0x000CE0EC
		private double? method_30()
		{
			HisDataSet.Class349 @class = new HisDataSet.Class349();
			double? result = null;
			@class.dateTime_0 = this.method_16();
			try
			{
				HisData hisData = this.FetchedHisDataList.Values.LastOrDefault(new Func<HisData, bool>(@class.method_0));
				if (hisData != null)
				{
					result = new double?(hisData.Close);
				}
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x170004AA RID: 1194
		// (get) Token: 0x06001D6E RID: 7534 RVA: 0x000CFF5C File Offset: 0x000CE15C
		public DateTime HisDataDT
		{
			get
			{
				DateTime result = default(DateTime);
				if (this.CurrHisData != null)
				{
					result = this.CurrHisData.Date;
				}
				else if (this.ComingHisData != null)
				{
					result = this.ComingHisData.Date;
				}
				return result;
			}
		}

		// Token: 0x06001D6F RID: 7535 RVA: 0x000CFFA0 File Offset: 0x000CE1A0
		[CompilerGenerated]
		private bool method_31(KeyValuePair<DateTime, HisData> keyValuePair_0)
		{
			return keyValuePair_0.Key >= this.dateTime_2;
		}

		// Token: 0x04000E6C RID: 3692
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000E6D RID: 3693
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000E6E RID: 3694
		[CompilerGenerated]
		private Delegate28 delegate28_0;

		// Token: 0x04000E6F RID: 3695
		[CompilerGenerated]
		private Delegate28 delegate28_1;

		// Token: 0x04000E70 RID: 3696
		private DateTime dateTime_0;

		// Token: 0x04000E71 RID: 3697
		private DateTime dateTime_1;

		// Token: 0x04000E72 RID: 3698
		private DateTime dateTime_2;

		// Token: 0x04000E73 RID: 3699
		private DateTime dateTime_3;

		// Token: 0x04000E74 RID: 3700
		private SortedList<DateTime, HisData> sortedList_0;

		// Token: 0x04000E75 RID: 3701
		private List<HDTick> list_0;

		// Token: 0x04000E76 RID: 3702
		private DateTime? nullable_0;

		// Token: 0x04000E77 RID: 3703
		private int? nullable_1;

		// Token: 0x04000E78 RID: 3704
		private ExchgOBT exchgOBT_0;

		// Token: 0x04000E79 RID: 3705
		private SymbNtTrDate symbNtTrDate_0;

		// Token: 0x04000E7A RID: 3706
		private DateTime? nullable_2;

		// Token: 0x04000E7B RID: 3707
		private HisData hisData_0;

		// Token: 0x04000E7C RID: 3708
		private HisData hisData_1;

		// Token: 0x04000E7D RID: 3709
		private int int_0;

		// Token: 0x04000E7E RID: 3710
		private SymbDataSet symbDataSet_0;

		// Token: 0x04000E7F RID: 3711
		private double double_0;

		// Token: 0x04000E80 RID: 3712
		private double double_1;

		// Token: 0x0200028F RID: 655
		[CompilerGenerated]
		private sealed class Class342
		{
			// Token: 0x06001D71 RID: 7537 RVA: 0x000CFFC4 File Offset: 0x000CE1C4
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date <= this.dateTime_0;
			}

			// Token: 0x04000E81 RID: 3713
			public DateTime dateTime_0;
		}

		// Token: 0x02000290 RID: 656
		[CompilerGenerated]
		private sealed class Class343
		{
			// Token: 0x06001D73 RID: 7539 RVA: 0x000CFFE8 File Offset: 0x000CE1E8
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key.Date >= this.dateTime_0.Date;
			}

			// Token: 0x04000E82 RID: 3714
			public DateTime dateTime_0;
		}

		// Token: 0x02000291 RID: 657
		[CompilerGenerated]
		private sealed class Class344
		{
			// Token: 0x06001D75 RID: 7541 RVA: 0x000D0018 File Offset: 0x000CE218
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date <= this.dateTime_0;
			}

			// Token: 0x06001D76 RID: 7542 RVA: 0x000D003C File Offset: 0x000CE23C
			internal bool method_1(HisData hisData_0)
			{
				return hisData_0.Date > this.dateTime_0;
			}

			// Token: 0x04000E83 RID: 3715
			public DateTime dateTime_0;
		}

		// Token: 0x02000292 RID: 658
		[CompilerGenerated]
		private sealed class Class345
		{
			// Token: 0x06001D78 RID: 7544 RVA: 0x000D0060 File Offset: 0x000CE260
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date <= this.dateTime_0;
			}

			// Token: 0x06001D79 RID: 7545 RVA: 0x000D0084 File Offset: 0x000CE284
			internal bool method_1(HisData hisData_0)
			{
				return hisData_0.Date >= this.dateTime_0;
			}

			// Token: 0x04000E84 RID: 3716
			public DateTime dateTime_0;
		}

		// Token: 0x02000293 RID: 659
		[CompilerGenerated]
		private sealed class Class346
		{
			// Token: 0x06001D7B RID: 7547 RVA: 0x000D00A8 File Offset: 0x000CE2A8
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				KeyValuePair<DateTime, HisData> keyValuePair = keyValuePair_0;
				return keyValuePair.Key < this.dateTime_0.Date.Add(this.exchgOBT_0.DayOpenTime.Value.TimeOfDay);
			}

			// Token: 0x04000E85 RID: 3717
			public DateTime dateTime_0;

			// Token: 0x04000E86 RID: 3718
			public ExchgOBT exchgOBT_0;
		}

		// Token: 0x02000294 RID: 660
		[CompilerGenerated]
		private sealed class Class347
		{
			// Token: 0x06001D7D RID: 7549 RVA: 0x000D00F8 File Offset: 0x000CE2F8
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1.Date > this.dateTime_0;
			}

			// Token: 0x04000E87 RID: 3719
			public DateTime dateTime_0;
		}

		// Token: 0x02000296 RID: 662
		[CompilerGenerated]
		private sealed class Class348
		{
			// Token: 0x06001D84 RID: 7556 RVA: 0x000D0150 File Offset: 0x000CE350
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key.Date == this.hisData_0.Date.Date)
				{
					result = (keyValuePair_0.Key.TimeOfDay > this.hisData_0.Date.TimeOfDay);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000E8C RID: 3724
			public HisData hisData_0;
		}

		// Token: 0x02000297 RID: 663
		[CompilerGenerated]
		private sealed class Class349
		{
			// Token: 0x06001D86 RID: 7558 RVA: 0x000D01B8 File Offset: 0x000CE3B8
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date < this.dateTime_0;
			}

			// Token: 0x04000E8D RID: 3725
			public DateTime dateTime_0;
		}
	}
}
