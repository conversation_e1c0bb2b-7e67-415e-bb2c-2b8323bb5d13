﻿using System;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns25;
using ns4;

namespace TEx
{
	// Token: 0x02000090 RID: 144
	public sealed class ScrShotPictureBox : PictureBox
	{
		// Token: 0x1400001D RID: 29
		// (add) Token: 0x060004B5 RID: 1205 RVA: 0x0002681C File Offset: 0x00024A1C
		// (remove) Token: 0x060004B6 RID: 1206 RVA: 0x00026854 File Offset: 0x00024A54
		public event EventHandler Selected
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060004B7 RID: 1207 RVA: 0x000040AF File Offset: 0x000022AF
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x060004B8 RID: 1208 RVA: 0x000040CA File Offset: 0x000022CA
		public ScrShotPictureBox()
		{
			base.MouseClick += this.ScrShotPictureBox_MouseClick;
			base.MouseEnter += this.ScrShotPictureBox_MouseEnter;
			base.MouseLeave += this.ScrShotPictureBox_MouseLeave;
		}

		// Token: 0x060004B9 RID: 1209 RVA: 0x0000410A File Offset: 0x0000230A
		private void ScrShotPictureBox_MouseClick(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Left && !this.IsSelected)
			{
				this.method_0();
				this.IsSelected = true;
			}
		}

		// Token: 0x060004BA RID: 1210 RVA: 0x0002688C File Offset: 0x00024A8C
		private void method_1()
		{
			Class51 @class = new Class51();
			@class.Location = new Point(base.Width - 42, base.Height - 42);
			@class.BackColor = Color.Transparent;
			base.Controls.Clear();
			base.Controls.Add(@class);
		}

		// Token: 0x060004BB RID: 1211 RVA: 0x00004130 File Offset: 0x00002330
		private void method_2()
		{
			base.Controls.Clear();
		}

		// Token: 0x060004BC RID: 1212 RVA: 0x0000413F File Offset: 0x0000233F
		private void ScrShotPictureBox_MouseEnter(object sender, EventArgs e)
		{
			this.method_3(Class181.color_22, 4);
		}

		// Token: 0x060004BD RID: 1213 RVA: 0x0000414F File Offset: 0x0000234F
		private void ScrShotPictureBox_MouseLeave(object sender, EventArgs e)
		{
			this.method_3(Color.White, 4);
		}

		// Token: 0x060004BE RID: 1214 RVA: 0x000268E0 File Offset: 0x00024AE0
		private void method_3(Color color_0, int int_0 = 4)
		{
			Form form = base.Parent as Form;
			if (form != null)
			{
				int x = base.Location.X - 10;
				int y = base.Location.Y - 10;
				int width = base.Width + 12;
				int height = base.Height + 12;
				if (TApp.IsHighDpiScreen)
				{
					x = base.Location.X - 5;
					y = base.Location.Y - 5;
					width = base.Width + 9;
					height = base.Height + 9;
				}
				Rectangle rect = new Rectangle(x, y, width, height);
				Graphics graphics = Graphics.FromHwnd(form.Handle);
				Pen pen = new Pen(color_0, (float)int_0);
				graphics.DrawRectangle(pen, rect);
			}
		}

		// Token: 0x170000F7 RID: 247
		// (get) Token: 0x060004BF RID: 1215 RVA: 0x000269A8 File Offset: 0x00024BA8
		// (set) Token: 0x060004C0 RID: 1216 RVA: 0x0000415F File Offset: 0x0000235F
		public bool IsSelected
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				if (this.bool_0 != value)
				{
					this.bool_0 = value;
					if (this.bool_0)
					{
						this.method_1();
					}
					else
					{
						this.method_2();
					}
				}
			}
		}

		// Token: 0x040001EA RID: 490
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040001EB RID: 491
		private bool bool_0;
	}
}
