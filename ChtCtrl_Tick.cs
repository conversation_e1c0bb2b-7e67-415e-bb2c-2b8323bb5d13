﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns18;
using ns26;
using ns9;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200023B RID: 571
	internal sealed class ChtCtrl_Tick : ChtCtrl
	{
		// Token: 0x0600184C RID: 6220 RVA: 0x00009FB7 File Offset: 0x000081B7
		public ChtCtrl_Tick(SymbDataSet sds, HisDataPeriodSet hisDataPeriodSet, string tag) : base(sds, hisDataPeriodSet, tag, sds.HasValidDataSet ? sds.CurrHisDataSet.TotalMinsPerTradingDay : 240)
		{
		}

		// Token: 0x0600184D RID: 6221 RVA: 0x000A77B4 File Offset: 0x000A59B4
		public void method_49()
		{
			if (base.ChartList.Contains(this.ChartTickM))
			{
				base.ChartList.Remove(this.ChartTickM);
			}
			this.ChartTickM = new ChartTickM(this, base.ChtSpC.Panel1);
			if (this.ChartTickM != null)
			{
				this.method_50();
				this.ChartTickM.ZedGraphControl.AxisChange();
				this.ChartTickM.ZedGraphControl.Refresh();
				base.ChartList.Add(this.ChartTickM);
				if (base.ChartList.Contains(this.ChartTickV))
				{
					base.ChartList.Remove(this.ChartTickV);
				}
				this.ChartTickV = new ChartTickV(this, base.ChtSpC.Panel2);
				if (this.ChartTickV != null)
				{
					this.method_51();
					this.ChartTickV.ZedGraphControl.AxisChange();
					this.ChartTickV.ZedGraphControl.Refresh();
					base.ChartList.Add(this.ChartTickV);
					this.vmethod_2();
					this.ChartTickM.ZedGraphControl.MouseEnter += base.method_45;
					this.ChartTickM.ZedGraphControl.MouseLeave += base.method_46;
					this.ChartTickM.ZedGraphControl.MouseHover += base.method_47;
				}
			}
		}

		// Token: 0x0600184E RID: 6222 RVA: 0x000A7920 File Offset: 0x000A5B20
		protected override void vmethod_5(int int_5)
		{
			if (base.PeriodHisDataList != null && base.PeriodHisDataList.Count >= 1 && int_5 >= 0 && int_5 < base.PeriodHisDataList.Count)
			{
				DateTime dateTime = base.PeriodHisDataList.Keys[int_5];
				HisData hisData;
				if (base.SymbDataSet.CurrHisDataSet != null && base.SymbDataSet.CurrHisDataSet.CurrExchgOBT != null)
				{
					this.StartHDDateTime = base.SymbDataSet.CurrHisDataSet.method_17(dateTime).AddMinutes(1.0);
					hisData = Class339.smethod_2(base.PeriodHisDataList, this.StartHDDateTime);
				}
				else
				{
					hisData = base.PeriodHisDataList.Values[int_5];
				}
				if (hisData != null)
				{
					this.ChtHDList = new List<HisData>();
					this.ChtHDList.Add(hisData);
					if (base.PeriodHisDataList.Count > 1)
					{
						while (hisData != null && hisData.Date < dateTime)
						{
							hisData = base.SymbDataSet.method_38(base.PeriodHisDataList, hisData);
							if (hisData != null)
							{
								this.ChtHDList.Add(hisData);
							}
						}
					}
					if (hisData != null && base.SymbDataSet.HasValidDataSet)
					{
						this.list_1 = this.method_53(hisData.Date);
					}
					if (base.SymbDataSet.HasValidDataSet)
					{
						this.bool_5 = base.SymbDataSet.CurrHisDataSet.IfCurrDayHasNightData;
					}
					base.vmethod_5(int_5);
				}
				else
				{
					Class184.smethod_0(new Exception(Class521.smethod_0(63952)));
				}
			}
			else if (base.PeriodHisDataList != null && base.PeriodHisDataList.Count >= 1)
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(63919)));
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(63874)));
			}
		}

		// Token: 0x0600184F RID: 6223 RVA: 0x000A7AE8 File Offset: 0x000A5CE8
		protected override void vmethod_6(HisData hisData_0)
		{
			if (base.SymbDataSet.IsNextHdDayBegin)
			{
				bool flag = base.SymbDataSet.CurrHisDataSet.method_21(hisData_0.Date);
				if (this.bool_5 != flag)
				{
					this.list_1 = this.method_53(hisData_0.Date);
					this.bool_5 = flag;
				}
				foreach (ChartBase chartBase in base.ChartList)
				{
					chartBase.vmethod_2();
				}
				this.dateTime_0 = hisData_0.Date;
				this.list_2 = new List<HisData>();
				this.list_2.Add(hisData_0);
			}
			else
			{
				if (this.list_2 == null)
				{
					this.list_2 = new List<HisData>();
				}
				this.list_2.Add(hisData_0);
			}
			base.vmethod_6(hisData_0);
		}

		// Token: 0x06001850 RID: 6224 RVA: 0x00009FDC File Offset: 0x000081DC
		public override void vmethod_33()
		{
			base.vmethod_33();
			this.method_50();
			this.method_51();
		}

		// Token: 0x06001851 RID: 6225 RVA: 0x000A7BD0 File Offset: 0x000A5DD0
		private void method_50()
		{
			if (this.ChartTickM != null && this.ChartTickM.GraphPane != null && base.ChtSpC != null)
			{
				try
				{
					this.ChartTickM.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 2f, (float)base.ChtSpC.Width - (float)base.ChartRect_LeftMargin * 1.85f, (float)(base.ChtSpC.Panel1.Height - 3));
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001852 RID: 6226 RVA: 0x000A7C68 File Offset: 0x000A5E68
		private void method_51()
		{
			if (this.ChartTickV != null && this.ChartTickV.GraphPane != null && base.ChtSpC != null)
			{
				try
				{
					this.ChartTickV.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 0f, (float)base.ChtSpC.Width - (float)base.ChartRect_LeftMargin * 1.85f, (float)(base.ChtSpC.Panel2.Height - base.ChartBottom_DTLabel_Height));
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001853 RID: 6227 RVA: 0x000A7D08 File Offset: 0x000A5F08
		public override void vmethod_25(SplitContainer splitContainer_2, ChtCtrlParam chtCtrlParam_0)
		{
			base.vmethod_25(splitContainer_2, chtCtrlParam_0);
			this.method_49();
			try
			{
				base.ChtSpC.SplitterDistance = (int)Math.Round(base.ChtSpC.Height * chtCtrlParam_0.SpCParam_this.SplitterDistance / chtCtrlParam_0.SpCParam_this.Size.Height);
			}
			catch
			{
			}
		}

		// Token: 0x06001854 RID: 6228 RVA: 0x00009EBD File Offset: 0x000080BD
		protected override void vmethod_32(object sender, SplitterEventArgs e)
		{
			this.vmethod_33();
		}

		// Token: 0x06001855 RID: 6229 RVA: 0x00009FF2 File Offset: 0x000081F2
		public override void vmethod_23(bool bool_6)
		{
			this.ChartTickM.method_66(bool_6);
		}

		// Token: 0x06001856 RID: 6230 RVA: 0x0000A002 File Offset: 0x00008202
		public override void vmethod_22()
		{
			this.ChartTickM.method_67();
		}

		// Token: 0x06001857 RID: 6231 RVA: 0x000A7D94 File Offset: 0x000A5F94
		protected List<Struct3> method_52()
		{
			List<Struct3> result;
			if (base.SymbDataSet.HasValidDataSet)
			{
				result = this.method_53(base.SymbDataSet.CurrHisDataSet.CurrHisData.Date);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001858 RID: 6232 RVA: 0x000A7DD4 File Offset: 0x000A5FD4
		protected List<Struct3> method_53(DateTime dateTime_1)
		{
			return this.method_54(dateTime_1, base.SymbDataSet.CurrHisDataSet.CurrExchgOBT, base.SymbDataSet.CurrHisDataSet.IfCurrDayHasNightData);
		}

		// Token: 0x06001859 RID: 6233 RVA: 0x000A7E0C File Offset: 0x000A600C
		protected List<Struct3> method_54(DateTime dateTime_1, ExchgOBT exchgOBT_0, bool bool_6)
		{
			List<Struct3> list = new List<Struct3>();
			if (base.SymbDataSet.CurrSymbol.Type != TradingSymbolType.Forex)
			{
				if (bool_6)
				{
					List<Struct3> list2 = this.method_55(dateTime_1, exchgOBT_0);
					list.AddRange(list2);
					List<Struct3> list3 = this.method_57(dateTime_1, exchgOBT_0, bool_6);
					if (list3[0].int_0 - list2.Last<Struct3>().int_0 < 60)
					{
						List<Struct3> list4 = new List<Struct3>();
						for (int i = 0; i < list3.Count; i++)
						{
							Struct3 item = list3[i];
							if (item.dateTime_0.Minute == 0)
							{
								item.bool_0 = true;
							}
							else
							{
								item.bool_0 = false;
							}
							list4.Add(item);
						}
						list3 = list4;
					}
					list.AddRange(list3);
				}
				else
				{
					list = this.method_57(dateTime_1, exchgOBT_0, false);
				}
			}
			return list;
		}

		// Token: 0x0600185A RID: 6234 RVA: 0x000A7EDC File Offset: 0x000A60DC
		private List<Struct3> method_55(DateTime dateTime_1, ExchgOBT exchgOBT_0)
		{
			List<Struct3> list = new List<Struct3>();
			int num = this.method_56(dateTime_1.Date.AddHours(21.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int int_ = this.method_56(dateTime_1.Date.AddHours(22.0).AddMinutes(0.0), dateTime_1, exchgOBT_0);
			int int_2 = this.method_56(dateTime_1.Date.AddHours(22.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int num2 = this.method_56(dateTime_1.Date.AddHours(23.0).AddMinutes(0.0), dateTime_1, exchgOBT_0);
			int num3 = this.method_56(dateTime_1.Date.AddHours(23.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int num4 = this.method_56(dateTime_1.Date.AddHours(24.0).AddMinutes(0.0), dateTime_1, exchgOBT_0);
			int num5 = this.method_56(dateTime_1.Date.AddHours(24.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int num6 = this.method_56(dateTime_1.Date.AddHours(25.0).AddMinutes(0.0), dateTime_1, exchgOBT_0);
			int num7 = this.method_56(dateTime_1.Date.AddHours(25.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int num8 = this.method_56(dateTime_1.Date.AddHours(26.0).AddMinutes(0.0), dateTime_1, exchgOBT_0);
			int num9 = this.method_56(dateTime_1.Date.AddHours(26.0).AddMinutes(30.0), dateTime_1, exchgOBT_0);
			int totalNightTradingMinsPerDay = exchgOBT_0.TotalNightTradingMinsPerDay;
			Struct3 item = new Struct3(num, new DateTime(1, 1, 1, 21, 30, 0), Class521.smethod_0(63969), false, num > 0);
			list.Add(item);
			Struct3 item2 = new Struct3(int_, new DateTime(1, 1, 1, 22, 0, 0), Class521.smethod_0(63978), true);
			list.Add(item2);
			Struct3 item3 = new Struct3(int_2, new DateTime(1, 1, 1, 22, 30, 0), Class521.smethod_0(63987), false);
			list.Add(item3);
			if (num2 <= totalNightTradingMinsPerDay)
			{
				Struct3 item4 = new Struct3(num2, new DateTime(1, 1, 1, 23, 0, 0), Class521.smethod_0(63996), true);
				list.Add(item4);
			}
			if (num3 <= totalNightTradingMinsPerDay)
			{
				Struct3 item5 = new Struct3(num3, new DateTime(1, 1, 1, 23, 30, 0), Class521.smethod_0(64005), false);
				list.Add(item5);
			}
			if (num4 <= totalNightTradingMinsPerDay)
			{
				Struct3 item6 = new Struct3(num4, new DateTime(1, 1, 1, 0, 0, 0), Class521.smethod_0(64014), true);
				list.Add(item6);
			}
			if (num5 <= totalNightTradingMinsPerDay)
			{
				Struct3 item7 = new Struct3(num5, new DateTime(1, 1, 1, 0, 30, 0), Class521.smethod_0(64023), false);
				list.Add(item7);
			}
			if (num6 <= totalNightTradingMinsPerDay)
			{
				Struct3 item8 = new Struct3(num6, new DateTime(1, 1, 1, 1, 0, 0), Class521.smethod_0(64032), true);
				list.Add(item8);
			}
			if (num7 <= totalNightTradingMinsPerDay)
			{
				Struct3 item9 = new Struct3(num7, new DateTime(1, 1, 1, 1, 30, 0), Class521.smethod_0(64041), false);
				list.Add(item9);
			}
			if (num8 <= totalNightTradingMinsPerDay)
			{
				Struct3 item10 = new Struct3(num8, new DateTime(1, 1, 1, 2, 0, 0), Class521.smethod_0(64050), true);
				list.Add(item10);
			}
			if (num9 <= totalNightTradingMinsPerDay)
			{
				Struct3 item11 = new Struct3(num9, new DateTime(1, 1, 1, 2, 30, 0), Class521.smethod_0(64059), false);
				list.Add(item11);
			}
			Struct3 item12 = list[list.Count - 1];
			item12.bool_2 = true;
			list.RemoveAt(list.Count - 1);
			list.Add(item12);
			return list;
		}

		// Token: 0x0600185B RID: 6235 RVA: 0x000A8360 File Offset: 0x000A6560
		private int method_56(DateTime dateTime_1, DateTime dateTime_2, ExchgOBT exchgOBT_0)
		{
			DateTime d = dateTime_2.Date.Add(exchgOBT_0.NightOpenTime.Value.TimeOfDay);
			return Convert.ToInt32((dateTime_1 - d).TotalMinutes);
		}

		// Token: 0x0600185C RID: 6236 RVA: 0x000A83B0 File Offset: 0x000A65B0
		private List<Struct3> method_57(DateTime dateTime_1, ExchgOBT exchgOBT_0, bool bool_6)
		{
			List<Struct3> list = new List<Struct3>();
			int num = this.method_58(dateTime_1.Date.AddHours(9.0).AddMinutes(30.0), exchgOBT_0, bool_6);
			int int_ = this.method_58(dateTime_1.Date.AddHours(10.0).AddMinutes(0.0), exchgOBT_0, bool_6);
			int int_2 = this.method_58(dateTime_1.Date.AddHours(10.0).AddMinutes(30.0), exchgOBT_0, bool_6);
			int int_3 = this.method_58(dateTime_1.Date.AddHours(11.0).AddMinutes(0.0), exchgOBT_0, bool_6);
			int num2 = this.method_58(dateTime_1.Date.AddHours(11.0).AddMinutes(30.0), exchgOBT_0, bool_6);
			int num3 = this.method_58(dateTime_1.Date.AddHours(13.0).AddMinutes(30.0), exchgOBT_0, bool_6);
			int num4 = this.method_58(dateTime_1.Date.AddHours(14.0).AddMinutes(0.0), exchgOBT_0, bool_6);
			int num5 = this.method_58(dateTime_1.Date.AddHours(14.0).AddMinutes(30.0), exchgOBT_0, bool_6);
			int num6 = this.method_58(dateTime_1.Date.AddHours(15.0).AddMinutes(0.0), exchgOBT_0, bool_6);
			int totalDayTradingMinsPerDay = exchgOBT_0.TotalDayTradingMinsPerDay;
			Struct3 item = new Struct3(num, new DateTime(1, 1, 1, 9, 30, 0), Class521.smethod_0(64068), true, num > 0);
			list.Add(item);
			Struct3 item2 = new Struct3(int_, new DateTime(1, 1, 1, 10, 0, 0), Class521.smethod_0(64077), false);
			list.Add(item2);
			Struct3 item3 = new Struct3(int_2, new DateTime(1, 1, 1, 10, 30, 0), Class521.smethod_0(64086), true);
			list.Add(item3);
			Struct3 item4 = new Struct3(int_3, new DateTime(1, 1, 1, 11, 0, 0), Class521.smethod_0(64095), false);
			list.Add(item4);
			Struct3 item5 = new Struct3(num2, new DateTime(1, 1, 1, 11, 30, 0), Class521.smethod_0(64104), true);
			list.Add(item5);
			if (num3 - num2 > 15)
			{
				Struct3 item6 = new Struct3(num3, new DateTime(1, 1, 1, 13, 30, 0), Class521.smethod_0(64113), false);
				list.Add(item6);
			}
			Struct3 item7 = new Struct3(num4, new DateTime(1, 1, 1, 14, 0, 0), Class521.smethod_0(64122), true);
			list.Add(item7);
			if (num5 - num4 > 15)
			{
				Struct3 item8 = new Struct3(num5, new DateTime(1, 1, 1, 14, 30, 0), Class521.smethod_0(64131), false);
				list.Add(item8);
			}
			Struct3 item9 = new Struct3(num6, new DateTime(1, 1, 1, 15, 0, 0), Class521.smethod_0(64140), true, num6 + 1 != totalDayTradingMinsPerDay);
			list.Add(item9);
			return list;
		}

		// Token: 0x0600185D RID: 6237 RVA: 0x000A873C File Offset: 0x000A693C
		private int method_58(DateTime dateTime_1, ExchgOBT exchgOBT_0, bool bool_6)
		{
			TimeSpan timeOfDay = exchgOBT_0.DayOpenTime.Value.TimeOfDay;
			int num = 0;
			if (bool_6)
			{
				num = exchgOBT_0.TotalNightTradingMinsPerDay;
			}
			int num2 = Convert.ToInt32((dateTime_1.TimeOfDay - timeOfDay).TotalMinutes) + num;
			if (exchgOBT_0.AMRestStartTime != null && exchgOBT_0.AMRestEndTime.Value.TimeOfDay <= dateTime_1.TimeOfDay)
			{
				num2 -= exchgOBT_0.AMRestMins;
			}
			if (exchgOBT_0.NoonBreakStartTime != null && exchgOBT_0.NoonBreakEndTime.Value.TimeOfDay <= dateTime_1.TimeOfDay)
			{
				num2 -= exchgOBT_0.NoonBreakMins;
			}
			if (exchgOBT_0.PMRestStartTime != null && exchgOBT_0.PMRestEndTime.Value.TimeOfDay <= dateTime_1.TimeOfDay)
			{
				num2 -= exchgOBT_0.PMRestMins;
			}
			return num2;
		}

		// Token: 0x0600185E RID: 6238 RVA: 0x000A884C File Offset: 0x000A6A4C
		protected override void vmethod_31(bool bool_6)
		{
			if (this.ChartTickM.GraphPane.Y2Axis.Scale.IsReverse != bool_6)
			{
				this.ChartTickM.GraphPane.Y2Axis.Scale.IsReverse = bool_6;
			}
			base.vmethod_31(bool_6);
		}

		// Token: 0x0600185F RID: 6239 RVA: 0x000A889C File Offset: 0x000A6A9C
		protected override int vmethod_1()
		{
			return this.ChartTickM.TickCurve.NPts;
		}

		// Token: 0x1700040D RID: 1037
		// (get) Token: 0x06001860 RID: 6240 RVA: 0x000A88C0 File Offset: 0x000A6AC0
		// (set) Token: 0x06001861 RID: 6241 RVA: 0x0000A011 File Offset: 0x00008211
		public ChartTickM ChartTickM
		{
			get
			{
				return this.chartTickM_0;
			}
			set
			{
				this.chartTickM_0 = value;
			}
		}

		// Token: 0x1700040E RID: 1038
		// (get) Token: 0x06001862 RID: 6242 RVA: 0x000A88D8 File Offset: 0x000A6AD8
		// (set) Token: 0x06001863 RID: 6243 RVA: 0x0000A01C File Offset: 0x0000821C
		public ChartTickV ChartTickV
		{
			get
			{
				return this.chartTickV_0;
			}
			set
			{
				this.chartTickV_0 = value;
			}
		}

		// Token: 0x1700040F RID: 1039
		// (get) Token: 0x06001864 RID: 6244 RVA: 0x000A88F0 File Offset: 0x000A6AF0
		// (set) Token: 0x06001865 RID: 6245 RVA: 0x0000A027 File Offset: 0x00008227
		public int StartHDIdx
		{
			get
			{
				return this.int_4;
			}
			set
			{
				this.int_4 = value;
			}
		}

		// Token: 0x17000410 RID: 1040
		// (get) Token: 0x06001866 RID: 6246 RVA: 0x000A8908 File Offset: 0x000A6B08
		// (set) Token: 0x06001867 RID: 6247 RVA: 0x0000A032 File Offset: 0x00008232
		public DateTime StartHDDateTime
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				this.dateTime_0 = value;
			}
		}

		// Token: 0x17000411 RID: 1041
		// (get) Token: 0x06001868 RID: 6248 RVA: 0x000A8920 File Offset: 0x000A6B20
		public List<Struct3> TimeLineXs
		{
			get
			{
				if (this.list_1 == null)
				{
					this.list_1 = this.method_52();
					if (base.SymbDataSet.HasValidDataSet)
					{
						this.bool_5 = base.SymbDataSet.CurrHisDataSet.IfCurrDayHasNightData;
					}
				}
				return this.list_1;
			}
		}

		// Token: 0x17000412 RID: 1042
		// (get) Token: 0x06001869 RID: 6249 RVA: 0x000A8970 File Offset: 0x000A6B70
		public bool IfLastTimeLineXsHasNightData
		{
			get
			{
				return this.bool_5;
			}
		}

		// Token: 0x17000413 RID: 1043
		// (get) Token: 0x0600186A RID: 6250 RVA: 0x000A8988 File Offset: 0x000A6B88
		public override ChartBase MainChart
		{
			get
			{
				return this.ChartTickM;
			}
		}

		// Token: 0x17000414 RID: 1044
		// (get) Token: 0x0600186B RID: 6251 RVA: 0x000A89A0 File Offset: 0x000A6BA0
		// (set) Token: 0x0600186C RID: 6252 RVA: 0x0000A03D File Offset: 0x0000823D
		public List<HisData> ChtHDList
		{
			get
			{
				return this.list_2;
			}
			set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x04000C31 RID: 3121
		private ChartTickM chartTickM_0;

		// Token: 0x04000C32 RID: 3122
		private ChartTickV chartTickV_0;

		// Token: 0x04000C33 RID: 3123
		private int int_4;

		// Token: 0x04000C34 RID: 3124
		private DateTime dateTime_0;

		// Token: 0x04000C35 RID: 3125
		private List<Struct3> list_1;

		// Token: 0x04000C36 RID: 3126
		private bool bool_5;

		// Token: 0x04000C37 RID: 3127
		private List<HisData> list_2;
	}
}
