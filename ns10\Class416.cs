﻿using System;
using ns12;
using ns13;
using ns18;
using ns28;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns10
{
	// Token: 0x0200030F RID: 783
	internal sealed class Class416 : Class415
	{
		// Token: 0x060021CB RID: 8651 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class416(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x060021CC RID: 8652 RVA: 0x000F01DC File Offset: 0x000EE3DC
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021CD RID: 8653 RVA: 0x000F0210 File Offset: 0x000EE410
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_17)
			{
				if (hsymbolType != Enum26.const_18)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101537)));
				}
				result = double_0 + double_1;
			}
			else
			{
				result = double_0 * double_1;
			}
			return result;
		}

		// Token: 0x060021CE RID: 8654 RVA: 0x000F0260 File Offset: 0x000EE460
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_17)
			{
				if (hsymbolType != Enum26.const_18)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101537)));
				}
				result = (dataArray_0 | dataArray_1);
			}
			else
			{
				result = (dataArray_0 & dataArray_1);
			}
			return result;
		}

		// Token: 0x060021CF RID: 8655 RVA: 0x000F02B8 File Offset: 0x000EE4B8
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			Class411 @class = Class419.smethod_0(tokenes_0);
			tokenes_0.method_1();
			HToken htoken = tokenes_0.Current;
			while (htoken.Symbol.HSymbolType == Enum26.const_18 || htoken.Symbol.HSymbolType == Enum26.const_17)
			{
				HToken htoken_ = htoken;
				tokenes_0.method_1();
				Class411 class411_ = Class419.smethod_0(tokenes_0);
				@class = new Class416(htoken_, @class, class411_);
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
			}
			tokenes_0.method_2();
			return @class;
		}
	}
}
