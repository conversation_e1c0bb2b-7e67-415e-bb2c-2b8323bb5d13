﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Threading;
using System.Xml.Linq;
using ns18;

namespace TEx
{
	// Token: 0x02000027 RID: 39
	[Serializable]
	internal sealed class ChartPage : ISerializable
	{
		// Token: 0x14000003 RID: 3
		// (add) Token: 0x060000E1 RID: 225 RVA: 0x00014464 File Offset: 0x00012664
		// (remove) Token: 0x060000E2 RID: 226 RVA: 0x0001449C File Offset: 0x0001269C
		public event PageUISettingEventHandler UISettingsChanging
		{
			[CompilerGenerated]
			add
			{
				PageUISettingEventHandler pageUISettingEventHandler = this.pageUISettingEventHandler_0;
				PageUISettingEventHandler pageUISettingEventHandler2;
				do
				{
					pageUISettingEventHandler2 = pageUISettingEventHandler;
					PageUISettingEventHandler value2 = (PageUISettingEventHandler)Delegate.Combine(pageUISettingEventHandler2, value);
					pageUISettingEventHandler = Interlocked.CompareExchange<PageUISettingEventHandler>(ref this.pageUISettingEventHandler_0, value2, pageUISettingEventHandler2);
				}
				while (pageUISettingEventHandler != pageUISettingEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				PageUISettingEventHandler pageUISettingEventHandler = this.pageUISettingEventHandler_0;
				PageUISettingEventHandler pageUISettingEventHandler2;
				do
				{
					pageUISettingEventHandler2 = pageUISettingEventHandler;
					PageUISettingEventHandler value2 = (PageUISettingEventHandler)Delegate.Remove(pageUISettingEventHandler2, value);
					pageUISettingEventHandler = Interlocked.CompareExchange<PageUISettingEventHandler>(ref this.pageUISettingEventHandler_0, value2, pageUISettingEventHandler2);
				}
				while (pageUISettingEventHandler != pageUISettingEventHandler2);
			}
		}

		// Token: 0x060000E3 RID: 227 RVA: 0x000144D4 File Offset: 0x000126D4
		private void method_0(PageUISettingCancelEventArgs pageUISettingCancelEventArgs_0)
		{
			PageUISettingEventHandler pageUISettingEventHandler = this.pageUISettingEventHandler_0;
			if (pageUISettingEventHandler != null)
			{
				pageUISettingEventHandler(this, pageUISettingCancelEventArgs_0);
			}
		}

		// Token: 0x14000004 RID: 4
		// (add) Token: 0x060000E4 RID: 228 RVA: 0x000144F8 File Offset: 0x000126F8
		// (remove) Token: 0x060000E5 RID: 229 RVA: 0x00014530 File Offset: 0x00012730
		public event EventHandler UISettingsChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060000E6 RID: 230 RVA: 0x00014568 File Offset: 0x00012768
		private void method_1()
		{
			EventHandler eventHandler = this.eventHandler_0;
			EventArgs e = new EventArgs();
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x060000E7 RID: 231 RVA: 0x00002D25 File Offset: 0x00000F25
		public ChartPage()
		{
		}

		// Token: 0x060000E8 RID: 232 RVA: 0x00002DF3 File Offset: 0x00000FF3
		public ChartPage(string name, ChartUISettings UISettings)
		{
			this.Name = name;
			this.UISettings = UISettings;
		}

		// Token: 0x060000E9 RID: 233 RVA: 0x00014590 File Offset: 0x00012790
		protected ChartPage(SerializationInfo info, StreamingContext context)
		{
			this.string_0 = info.GetString(Class521.smethod_0(1858));
			this.chartUISettings_0 = (ChartUISettings)info.GetValue(Class521.smethod_0(1867), typeof(ChartUISettings));
		}

		// Token: 0x060000EA RID: 234 RVA: 0x00002E0B File Offset: 0x0000100B
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(1858), this.string_0);
			info.AddValue(Class521.smethod_0(1867), this.chartUISettings_0);
		}

		// Token: 0x060000EB RID: 235 RVA: 0x000145E0 File Offset: 0x000127E0
		public XElement method_2()
		{
			XElement xelement = new XElement(Class521.smethod_0(1884));
			xelement.Add(new XElement(Class521.smethod_0(1858), this.Name));
			xelement.Add(this.UISettings.GetXElement());
			return xelement;
		}

		// Token: 0x1700003F RID: 63
		// (get) Token: 0x060000EC RID: 236 RVA: 0x0001463C File Offset: 0x0001283C
		// (set) Token: 0x060000ED RID: 237 RVA: 0x00002E40 File Offset: 0x00001040
		public string Name
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x17000040 RID: 64
		// (get) Token: 0x060000EE RID: 238 RVA: 0x00014654 File Offset: 0x00012854
		// (set) Token: 0x060000EF RID: 239 RVA: 0x0001466C File Offset: 0x0001286C
		public ChartUISettings UISettings
		{
			get
			{
				return this.chartUISettings_0;
			}
			set
			{
				PageUISettingCancelEventArgs pageUISettingCancelEventArgs = new PageUISettingCancelEventArgs(value);
				this.method_0(pageUISettingCancelEventArgs);
				if (!pageUISettingCancelEventArgs.Cancel)
				{
					this.chartUISettings_0 = value;
					this.method_1();
				}
			}
		}

		// Token: 0x04000059 RID: 89
		private string string_0;

		// Token: 0x0400005A RID: 90
		private ChartUISettings chartUISettings_0;

		// Token: 0x0400005B RID: 91
		[CompilerGenerated]
		private PageUISettingEventHandler pageUISettingEventHandler_0;

		// Token: 0x0400005C RID: 92
		[CompilerGenerated]
		private EventHandler eventHandler_0;
	}
}
