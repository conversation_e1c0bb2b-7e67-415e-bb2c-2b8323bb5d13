﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;

namespace ns27
{
	// Token: 0x02000200 RID: 512
	[DefaultProperty("BarInnerColor")]
	[DefaultEvent("Scroll")]
	[ToolboxBitmap(typeof(TrackBar))]
	internal sealed class Control0 : Control
	{
		// Token: 0x1400007D RID: 125
		// (add) Token: 0x060014A2 RID: 5282 RVA: 0x0008B6F4 File Offset: 0x000898F4
		// (remove) Token: 0x060014A3 RID: 5283 RVA: 0x0008B72C File Offset: 0x0008992C
		[Description("Event fires when the Value property changes")]
		[Category("Action")]
		public event EventHandler ValueChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x1400007E RID: 126
		// (add) Token: 0x060014A4 RID: 5284 RVA: 0x0008B764 File Offset: 0x00089964
		// (remove) Token: 0x060014A5 RID: 5285 RVA: 0x0008B79C File Offset: 0x0008999C
		[Category("Behavior")]
		[Description("Event fires when the Slider position is changed")]
		public event ScrollEventHandler Scroll
		{
			[CompilerGenerated]
			add
			{
				ScrollEventHandler scrollEventHandler = this.scrollEventHandler_0;
				ScrollEventHandler scrollEventHandler2;
				do
				{
					scrollEventHandler2 = scrollEventHandler;
					ScrollEventHandler value2 = (ScrollEventHandler)Delegate.Combine(scrollEventHandler2, value);
					scrollEventHandler = Interlocked.CompareExchange<ScrollEventHandler>(ref this.scrollEventHandler_0, value2, scrollEventHandler2);
				}
				while (scrollEventHandler != scrollEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				ScrollEventHandler scrollEventHandler = this.scrollEventHandler_0;
				ScrollEventHandler scrollEventHandler2;
				do
				{
					scrollEventHandler2 = scrollEventHandler;
					ScrollEventHandler value2 = (ScrollEventHandler)Delegate.Remove(scrollEventHandler2, value);
					scrollEventHandler = Interlocked.CompareExchange<ScrollEventHandler>(ref this.scrollEventHandler_0, value2, scrollEventHandler2);
				}
				while (scrollEventHandler != scrollEventHandler2);
			}
		}

		// Token: 0x1700035A RID: 858
		// (get) Token: 0x060014A6 RID: 5286 RVA: 0x0008B7D4 File Offset: 0x000899D4
		[Browsable(false)]
		public Rectangle ThumbRect
		{
			get
			{
				return this.rectangle_4;
			}
		}

		// Token: 0x1700035B RID: 859
		// (get) Token: 0x060014A7 RID: 5287 RVA: 0x0008B7EC File Offset: 0x000899EC
		// (set) Token: 0x060014A8 RID: 5288 RVA: 0x0008B804 File Offset: 0x00089A04
		[DefaultValue(16)]
		[Category("ColorSlider")]
		[Description("Set Slider thumb size")]
		public Size ThumbSize
		{
			get
			{
				return this.size_0;
			}
			set
			{
				int height = value.Height;
				int width = value.Width;
				if (height > 0 && width > 0)
				{
					this.size_0 = new Size(width, height);
					base.Invalidate();
					return;
				}
				throw new ArgumentOutOfRangeException(Class521.smethod_0(50382));
			}
		}

		// Token: 0x1700035C RID: 860
		// (get) Token: 0x060014A9 RID: 5289 RVA: 0x0008B850 File Offset: 0x00089A50
		// (set) Token: 0x060014AA RID: 5290 RVA: 0x0008B868 File Offset: 0x00089A68
		[Category("ColorSlider")]
		[DefaultValue(typeof(GraphicsPath), "null")]
		[Browsable(false)]
		[Description("Set Slider's thumb's custom shape")]
		public GraphicsPath ThumbCustomShape
		{
			get
			{
				return this.graphicsPath_0;
			}
			set
			{
				this.graphicsPath_0 = value;
				this.size_0 = new Size((int)value.GetBounds().Width, (int)value.GetBounds().Height);
				base.Invalidate();
			}
		}

		// Token: 0x1700035D RID: 861
		// (get) Token: 0x060014AB RID: 5291 RVA: 0x0008B8B0 File Offset: 0x00089AB0
		// (set) Token: 0x060014AC RID: 5292 RVA: 0x0008B8C8 File Offset: 0x00089AC8
		[DefaultValue(typeof(Size), "16; 16")]
		[Description("Set Slider's thumb round rect size")]
		[Category("ColorSlider")]
		public Size ThumbRoundRectSize
		{
			get
			{
				return this.size_1;
			}
			set
			{
				int num = value.Height;
				int num2 = value.Width;
				if (num <= 0)
				{
					num = 1;
				}
				if (num2 <= 0)
				{
					num2 = 1;
				}
				this.size_1 = new Size(num2, num);
				base.Invalidate();
			}
		}

		// Token: 0x1700035E RID: 862
		// (get) Token: 0x060014AD RID: 5293 RVA: 0x0008B908 File Offset: 0x00089B08
		// (set) Token: 0x060014AE RID: 5294 RVA: 0x0008B920 File Offset: 0x00089B20
		[DefaultValue(typeof(Size), "8; 8")]
		[Category("ColorSlider")]
		[Description("Set Slider's border round rect size")]
		public Size BorderRoundRectSize
		{
			get
			{
				return this.size_2;
			}
			set
			{
				int num = value.Height;
				int num2 = value.Width;
				if (num <= 0)
				{
					num = 1;
				}
				if (num2 <= 0)
				{
					num2 = 1;
				}
				this.size_2 = new Size(num2, num);
				base.Invalidate();
			}
		}

		// Token: 0x1700035F RID: 863
		// (get) Token: 0x060014AF RID: 5295 RVA: 0x0008B960 File Offset: 0x00089B60
		// (set) Token: 0x060014B0 RID: 5296 RVA: 0x00008426 File Offset: 0x00006626
		[DefaultValue(true)]
		[Description("Set whether to draw semitransparent thumb")]
		[Category("ColorSlider")]
		public bool DrawSemitransparentThumb
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000360 RID: 864
		// (get) Token: 0x060014B1 RID: 5297 RVA: 0x0008B978 File Offset: 0x00089B78
		// (set) Token: 0x060014B2 RID: 5298 RVA: 0x00008437 File Offset: 0x00006637
		[Category("ColorSlider")]
		[DefaultValue(null)]
		[Description("Set to use a specific Image for the thumb")]
		public Image ThumbImage
		{
			get
			{
				return this.image_0;
			}
			set
			{
				if (value != null)
				{
					this.image_0 = value;
				}
				else
				{
					this.image_0 = null;
				}
				base.Invalidate();
			}
		}

		// Token: 0x17000361 RID: 865
		// (get) Token: 0x060014B3 RID: 5299 RVA: 0x0008B990 File Offset: 0x00089B90
		// (set) Token: 0x060014B4 RID: 5300 RVA: 0x0008B9A8 File Offset: 0x00089BA8
		[Category("ColorSlider")]
		[Description("Set Slider padding (inside margins: left & right or bottom & top)")]
		[DefaultValue(0)]
		public new int Padding
		{
			get
			{
				return this.int_2;
			}
			set
			{
				if (this.int_2 != value)
				{
					this.int_2 = value;
					this.int_0 = (this.int_1 = this.int_2);
					base.Invalidate();
				}
			}
		}

		// Token: 0x17000362 RID: 866
		// (get) Token: 0x060014B5 RID: 5301 RVA: 0x0008B9E4 File Offset: 0x00089BE4
		// (set) Token: 0x060014B6 RID: 5302 RVA: 0x0008B9FC File Offset: 0x00089BFC
		[Description("Set Slider orientation")]
		[Category("ColorSlider")]
		[DefaultValue(Orientation.Horizontal)]
		public Orientation Orientation
		{
			get
			{
				return this.orientation_0;
			}
			set
			{
				if (this.orientation_0 != value)
				{
					this.orientation_0 = value;
					if (base.DesignMode)
					{
						int width = base.Width;
						base.Width = base.Height;
						base.Height = width;
					}
					base.Invalidate();
				}
			}
		}

		// Token: 0x17000363 RID: 867
		// (get) Token: 0x060014B7 RID: 5303 RVA: 0x0008BA44 File Offset: 0x00089C44
		// (set) Token: 0x060014B8 RID: 5304 RVA: 0x00008454 File Offset: 0x00006654
		[Category("ColorSlider")]
		[DefaultValue(false)]
		[Description("Set whether to draw focus rectangle")]
		public bool DrawFocusRectangle
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000364 RID: 868
		// (get) Token: 0x060014B9 RID: 5305 RVA: 0x0008BA5C File Offset: 0x00089C5C
		// (set) Token: 0x060014BA RID: 5306 RVA: 0x00008465 File Offset: 0x00006665
		[DefaultValue(true)]
		[Category("ColorSlider")]
		[Description("Set whether mouse entry and exit actions have impact on how control look")]
		public bool MouseEffects
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				this.bool_2 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000365 RID: 869
		// (get) Token: 0x060014BB RID: 5307 RVA: 0x0008BA74 File Offset: 0x00089C74
		// (set) Token: 0x060014BC RID: 5308 RVA: 0x0008BA8C File Offset: 0x00089C8C
		[Category("ColorSlider")]
		[DefaultValue(30)]
		[Description("Set Slider value")]
		public decimal Value
		{
			get
			{
				return this.decimal_0;
			}
			set
			{
				if (value >= this.decimal_1 & value <= this.decimal_2)
				{
					this.decimal_0 = value;
					if (this.eventHandler_0 != null)
					{
						this.eventHandler_0(this, new EventArgs());
					}
					base.Invalidate();
					return;
				}
				throw new ArgumentOutOfRangeException(Class521.smethod_0(50483));
			}
		}

		// Token: 0x17000366 RID: 870
		// (get) Token: 0x060014BD RID: 5309 RVA: 0x0008BAEC File Offset: 0x00089CEC
		// (set) Token: 0x060014BE RID: 5310 RVA: 0x0008BB04 File Offset: 0x00089D04
		[DefaultValue(0)]
		[Category("ColorSlider")]
		[Description("Set Slider minimal point")]
		public decimal Minimum
		{
			get
			{
				return this.decimal_1;
			}
			set
			{
				if (value < this.decimal_2)
				{
					this.decimal_1 = value;
					if (this.decimal_0 < this.decimal_1)
					{
						this.decimal_0 = this.decimal_1;
						if (this.eventHandler_0 != null)
						{
							this.eventHandler_0(this, new EventArgs());
						}
					}
					base.Invalidate();
					return;
				}
				throw new ArgumentOutOfRangeException(Class521.smethod_0(50544));
			}
		}

		// Token: 0x17000367 RID: 871
		// (get) Token: 0x060014BF RID: 5311 RVA: 0x0008BB78 File Offset: 0x00089D78
		// (set) Token: 0x060014C0 RID: 5312 RVA: 0x0008BB90 File Offset: 0x00089D90
		[DefaultValue(100)]
		[Description("Set Slider maximal point")]
		[Category("ColorSlider")]
		public decimal Maximum
		{
			get
			{
				return this.decimal_2;
			}
			set
			{
				if (value > this.decimal_1)
				{
					this.decimal_2 = value;
					if (this.decimal_0 > this.decimal_2)
					{
						this.decimal_0 = this.decimal_2;
						if (this.eventHandler_0 != null)
						{
							this.eventHandler_0(this, new EventArgs());
						}
					}
					base.Invalidate();
				}
			}
		}

		// Token: 0x17000368 RID: 872
		// (get) Token: 0x060014C1 RID: 5313 RVA: 0x0008BBF4 File Offset: 0x00089DF4
		// (set) Token: 0x060014C2 RID: 5314 RVA: 0x00008476 File Offset: 0x00006676
		[Category("ColorSlider")]
		[DefaultValue(1)]
		[Description("Set trackbar's small change")]
		public decimal SmallChange
		{
			get
			{
				return this.decimal_3;
			}
			set
			{
				this.decimal_3 = value;
			}
		}

		// Token: 0x17000369 RID: 873
		// (get) Token: 0x060014C3 RID: 5315 RVA: 0x0008BC0C File Offset: 0x00089E0C
		// (set) Token: 0x060014C4 RID: 5316 RVA: 0x00008481 File Offset: 0x00006681
		[DefaultValue(5)]
		[Description("Set trackbar's large change")]
		[Category("ColorSlider")]
		public decimal LargeChange
		{
			get
			{
				return this.decimal_4;
			}
			set
			{
				this.decimal_4 = value;
			}
		}

		// Token: 0x1700036A RID: 874
		// (get) Token: 0x060014C5 RID: 5317 RVA: 0x0008BC24 File Offset: 0x00089E24
		// (set) Token: 0x060014C6 RID: 5318 RVA: 0x0000848C File Offset: 0x0000668C
		[DefaultValue(10)]
		[Description("Set to how many parts is bar divided when using mouse wheel")]
		[Category("ColorSlider")]
		public int MouseWheelBarPartitions
		{
			get
			{
				return this.int_3;
			}
			set
			{
				if (value > 0)
				{
					this.int_3 = value;
					return;
				}
				throw new ArgumentOutOfRangeException(Class521.smethod_0(50601));
			}
		}

		// Token: 0x1700036B RID: 875
		// (get) Token: 0x060014C7 RID: 5319 RVA: 0x0008BC3C File Offset: 0x00089E3C
		// (set) Token: 0x060014C8 RID: 5320 RVA: 0x000084AB File Offset: 0x000066AB
		[Description("Sets Slider thumb outer color")]
		[Category("ColorSlider")]
		[DefaultValue(typeof(Color), "White")]
		public Color ThumbOuterColor
		{
			get
			{
				return this.color_0;
			}
			set
			{
				this.color_0 = value;
				base.Invalidate();
			}
		}

		// Token: 0x1700036C RID: 876
		// (get) Token: 0x060014C9 RID: 5321 RVA: 0x0008BC54 File Offset: 0x00089E54
		// (set) Token: 0x060014CA RID: 5322 RVA: 0x000084BC File Offset: 0x000066BC
		[Category("ColorSlider")]
		[Description("Set Slider thumb inner color")]
		public Color ThumbInnerColor
		{
			get
			{
				return this.color_1;
			}
			set
			{
				this.color_1 = value;
				base.Invalidate();
			}
		}

		// Token: 0x1700036D RID: 877
		// (get) Token: 0x060014CB RID: 5323 RVA: 0x0008BC6C File Offset: 0x00089E6C
		// (set) Token: 0x060014CC RID: 5324 RVA: 0x000084CD File Offset: 0x000066CD
		[Category("ColorSlider")]
		[Description("Set Slider thumb pen color")]
		public Color ThumbPenColor
		{
			get
			{
				return this.color_2;
			}
			set
			{
				this.color_2 = value;
				base.Invalidate();
			}
		}

		// Token: 0x1700036E RID: 878
		// (get) Token: 0x060014CD RID: 5325 RVA: 0x0008BC84 File Offset: 0x00089E84
		// (set) Token: 0x060014CE RID: 5326 RVA: 0x000084DE File Offset: 0x000066DE
		[Description("Set Slider bar inner color")]
		[DefaultValue(typeof(Color), "Black")]
		[Category("ColorSlider")]
		public Color BarInnerColor
		{
			get
			{
				return this.color_3;
			}
			set
			{
				this.color_3 = value;
				base.Invalidate();
			}
		}

		// Token: 0x1700036F RID: 879
		// (get) Token: 0x060014CF RID: 5327 RVA: 0x0008BC9C File Offset: 0x00089E9C
		// (set) Token: 0x060014D0 RID: 5328 RVA: 0x000084EF File Offset: 0x000066EF
		[Description("Gets or sets the top color of the elapsed")]
		[Category("ColorSlider")]
		public Color ElapsedPenColorTop
		{
			get
			{
				return this.color_4;
			}
			set
			{
				this.color_4 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000370 RID: 880
		// (get) Token: 0x060014D1 RID: 5329 RVA: 0x0008BCB4 File Offset: 0x00089EB4
		// (set) Token: 0x060014D2 RID: 5330 RVA: 0x00008500 File Offset: 0x00006700
		[Description("Gets or sets the bottom color of the elapsed")]
		[Category("ColorSlider")]
		public Color ElapsedPenColorBottom
		{
			get
			{
				return this.color_5;
			}
			set
			{
				this.color_5 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000371 RID: 881
		// (get) Token: 0x060014D3 RID: 5331 RVA: 0x0008BCCC File Offset: 0x00089ECC
		// (set) Token: 0x060014D4 RID: 5332 RVA: 0x00008511 File Offset: 0x00006711
		[Category("ColorSlider")]
		[Description("Gets or sets the top color of the bar")]
		public Color BarPenColorTop
		{
			get
			{
				return this.color_6;
			}
			set
			{
				this.color_6 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000372 RID: 882
		// (get) Token: 0x060014D5 RID: 5333 RVA: 0x0008BCE4 File Offset: 0x00089EE4
		// (set) Token: 0x060014D6 RID: 5334 RVA: 0x00008522 File Offset: 0x00006722
		[Description("Gets or sets the bottom color of the bar")]
		[Category("ColorSlider")]
		public Color BarPenColorBottom
		{
			get
			{
				return this.color_7;
			}
			set
			{
				this.color_7 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000373 RID: 883
		// (get) Token: 0x060014D7 RID: 5335 RVA: 0x0008BCFC File Offset: 0x00089EFC
		// (set) Token: 0x060014D8 RID: 5336 RVA: 0x00008533 File Offset: 0x00006733
		[Description("Set Slider's elapsed part inner color")]
		[Category("ColorSlider")]
		public Color ElapsedInnerColor
		{
			get
			{
				return this.color_8;
			}
			set
			{
				this.color_8 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000374 RID: 884
		// (get) Token: 0x060014D9 RID: 5337 RVA: 0x0008BD14 File Offset: 0x00089F14
		// (set) Token: 0x060014DA RID: 5338 RVA: 0x00008544 File Offset: 0x00006744
		[Description("Color of graduations")]
		[Category("ColorSlider")]
		public Color TickColor
		{
			get
			{
				return this.color_9;
			}
			set
			{
				if (value != this.color_9)
				{
					this.color_9 = value;
					base.Invalidate();
				}
			}
		}

		// Token: 0x17000375 RID: 885
		// (get) Token: 0x060014DB RID: 5339 RVA: 0x0008BD2C File Offset: 0x00089F2C
		// (set) Token: 0x060014DC RID: 5340 RVA: 0x00008563 File Offset: 0x00006763
		[Description("Gets or sets a value used to divide the graduation")]
		[Category("ColorSlider")]
		public float TickDivide
		{
			get
			{
				return this.float_0;
			}
			set
			{
				this.float_0 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000376 RID: 886
		// (get) Token: 0x060014DD RID: 5341 RVA: 0x0008BD44 File Offset: 0x00089F44
		// (set) Token: 0x060014DE RID: 5342 RVA: 0x00008574 File Offset: 0x00006774
		[Category("ColorSlider")]
		[Description("Gets or sets a value added to the graduation")]
		public float TickAdd
		{
			get
			{
				return this.float_1;
			}
			set
			{
				this.float_1 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000377 RID: 887
		// (get) Token: 0x060014DF RID: 5343 RVA: 0x0008BD5C File Offset: 0x00089F5C
		// (set) Token: 0x060014E0 RID: 5344 RVA: 0x00008585 File Offset: 0x00006785
		[Description("Gets or sets where to display the ticks")]
		[Category("ColorSlider")]
		[DefaultValue(TickStyle.TopLeft)]
		public TickStyle TickStyle
		{
			get
			{
				return this.tickStyle_0;
			}
			set
			{
				this.tickStyle_0 = value;
				base.Invalidate();
			}
		}

		// Token: 0x17000378 RID: 888
		// (get) Token: 0x060014E1 RID: 5345 RVA: 0x0008BD74 File Offset: 0x00089F74
		// (set) Token: 0x060014E2 RID: 5346 RVA: 0x00008596 File Offset: 0x00006796
		[Category("ColorSlider")]
		[Description("Set the number of intervals between minimum and maximum")]
		public decimal ScaleDivisions
		{
			get
			{
				return this.decimal_5;
			}
			set
			{
				if (value > 0m)
				{
					this.decimal_5 = value;
				}
				base.Invalidate();
			}
		}

		// Token: 0x17000379 RID: 889
		// (get) Token: 0x060014E3 RID: 5347 RVA: 0x0008BD8C File Offset: 0x00089F8C
		// (set) Token: 0x060014E4 RID: 5348 RVA: 0x0008BDA4 File Offset: 0x00089FA4
		[Description("Set the number of subdivisions between main divisions of graduation.")]
		[Category("ColorSlider")]
		public decimal ScaleSubDivisions
		{
			get
			{
				return this.decimal_6;
			}
			set
			{
				if (value > 0m && this.decimal_5 > 0m && (this.decimal_2 - this.decimal_1) / ((value + 1m) * this.decimal_5) > 0m)
				{
					this.decimal_6 = value;
				}
				base.Invalidate();
			}
		}

		// Token: 0x1700037A RID: 890
		// (get) Token: 0x060014E5 RID: 5349 RVA: 0x0008BE18 File Offset: 0x0008A018
		// (set) Token: 0x060014E6 RID: 5350 RVA: 0x0008BE30 File Offset: 0x0008A030
		[Description("Show or hide subdivisions of graduations")]
		[Category("ColorSlider")]
		public bool ShowSmallScale
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				if (value)
				{
					if (this.decimal_5 > 0m && this.decimal_6 > 0m && (this.decimal_2 - this.decimal_1) / ((this.decimal_6 + 1m) * this.decimal_5) > 0m)
					{
						this.bool_3 = value;
						base.Invalidate();
					}
					else
					{
						this.bool_3 = false;
					}
				}
				else
				{
					this.bool_3 = value;
					base.Invalidate();
				}
			}
		}

		// Token: 0x1700037B RID: 891
		// (get) Token: 0x060014E7 RID: 5351 RVA: 0x0008BEC8 File Offset: 0x0008A0C8
		// (set) Token: 0x060014E8 RID: 5352 RVA: 0x000085B4 File Offset: 0x000067B4
		[Category("ColorSlider")]
		[Description("Show or hide text value of graduations")]
		public bool ShowDivisionsText
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				this.bool_4 = value;
				base.Invalidate();
			}
		}

		// Token: 0x1700037C RID: 892
		// (get) Token: 0x060014E9 RID: 5353 RVA: 0x0008BEE0 File Offset: 0x0008A0E0
		// (set) Token: 0x060014EA RID: 5354 RVA: 0x000085C5 File Offset: 0x000067C5
		[Category("ColorSlider")]
		[Description("Get or Sets the Font of the Text being displayed.")]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Bindable(true)]
		[EditorBrowsable(EditorBrowsableState.Always)]
		[Browsable(true)]
		public Font Font
		{
			get
			{
				return base.Font;
			}
			set
			{
				base.Font = value;
				base.Invalidate();
				this.OnFontChanged(EventArgs.Empty);
			}
		}

		// Token: 0x1700037D RID: 893
		// (get) Token: 0x060014EB RID: 5355 RVA: 0x0008BEF8 File Offset: 0x0008A0F8
		// (set) Token: 0x060014EC RID: 5356 RVA: 0x000085E1 File Offset: 0x000067E1
		[Bindable(true)]
		[Category("ColorSlider")]
		[Browsable(true)]
		[Description("Get or Sets the Color of the Text being displayed.")]
		[EditorBrowsable(EditorBrowsableState.Always)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		public Color ForeColor
		{
			get
			{
				return base.ForeColor;
			}
			set
			{
				base.ForeColor = value;
				base.Invalidate();
				this.OnForeColorChanged(EventArgs.Empty);
			}
		}

		// Token: 0x1700037E RID: 894
		// (get) Token: 0x060014ED RID: 5357 RVA: 0x0008BF10 File Offset: 0x0008A110
		// (set) Token: 0x060014EE RID: 5358 RVA: 0x0008BF28 File Offset: 0x0008A128
		[Category("ColorSlider")]
		[DefaultValue(typeof(Control0.Enum19), "BlueColors")]
		[Description("Set Slider color schema. Has no effect when slider colors are changed manually after schema was applied.")]
		public Control0.Enum19 ColorSchema
		{
			get
			{
				return this.enum19_0;
			}
			set
			{
				this.enum19_0 = value;
				byte b = (byte)value;
				this.color_0 = this.color_10[(int)b, 0];
				this.color_1 = this.color_10[(int)b, 1];
				this.color_2 = this.color_10[(int)b, 2];
				this.color_3 = this.color_10[(int)b, 3];
				this.color_4 = this.color_10[(int)b, 4];
				this.color_5 = this.color_10[(int)b, 5];
				this.color_6 = this.color_10[(int)b, 6];
				this.color_7 = this.color_10[(int)b, 7];
				this.color_8 = this.color_10[(int)b, 8];
				base.Invalidate();
			}
		}

		// Token: 0x060014EF RID: 5359 RVA: 0x0008BFF4 File Offset: 0x0008A1F4
		public Control0(decimal decimal_7, decimal decimal_8, decimal decimal_9)
		{
			Color[,] array = new Color[3, 9];
			array[0, 0] = Color.White;
			array[0, 1] = Color.FromArgb(21, 56, 152);
			array[0, 2] = Color.FromArgb(21, 56, 152);
			array[0, 3] = Color.Black;
			array[0, 4] = Color.FromArgb(95, 140, 180);
			array[0, 5] = Color.FromArgb(99, 130, 208);
			array[0, 6] = Color.FromArgb(55, 60, 74);
			array[0, 7] = Color.FromArgb(87, 94, 110);
			array[0, 8] = Color.FromArgb(21, 56, 152);
			array[1, 0] = Color.White;
			array[1, 1] = Color.Red;
			array[1, 2] = Color.Red;
			array[1, 3] = Color.Black;
			array[1, 4] = Color.LightCoral;
			array[1, 5] = Color.Salmon;
			array[1, 6] = Color.FromArgb(55, 60, 74);
			array[1, 7] = Color.FromArgb(87, 94, 110);
			array[1, 8] = Color.Red;
			array[2, 0] = Color.White;
			array[2, 1] = Color.Green;
			array[2, 2] = Color.Green;
			array[2, 3] = Color.Black;
			array[2, 4] = Color.SpringGreen;
			array[2, 5] = Color.LightGreen;
			array[2, 6] = Color.FromArgb(55, 60, 74);
			array[2, 7] = Color.FromArgb(87, 94, 110);
			array[2, 8] = Color.Green;
			this.color_10 = array;
			base..ctor();
			this.method_2();
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.Selectable | ControlStyles.UserMouse | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, true);
			this.BackColor = Color.FromArgb(70, 77, 95);
			this.ForeColor = Color.White;
			this.Font = new Font(Class521.smethod_0(24023), 6f);
			this.Minimum = decimal_7;
			this.Maximum = decimal_8;
			this.Value = decimal_9;
		}

		// Token: 0x060014F0 RID: 5360 RVA: 0x000085FD File Offset: 0x000067FD
		public Control0() : this(0m, 100m, 30m)
		{
		}

		// Token: 0x060014F1 RID: 5361 RVA: 0x0008C370 File Offset: 0x0008A570
		protected void OnPaint(PaintEventArgs e)
		{
			if (!base.Enabled)
			{
				Color[] array = Control0.smethod_1(new Color[]
				{
					this.color_0,
					this.color_1,
					this.color_2,
					this.color_3,
					this.color_4,
					this.color_5,
					this.color_6,
					this.color_7,
					this.color_8
				});
				this.method_0(e, array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8]);
			}
			else if (this.bool_2 && this.bool_5)
			{
				Color[] array2 = Control0.smethod_2(new Color[]
				{
					this.color_0,
					this.color_1,
					this.color_2,
					this.color_3,
					this.color_4,
					this.color_5,
					this.color_6,
					this.color_7,
					this.color_8
				});
				this.method_0(e, array2[0], array2[1], array2[2], array2[3], array2[4], array2[5], array2[6], array2[7], array2[8]);
			}
			else
			{
				this.method_0(e, this.color_0, this.color_1, this.color_2, this.color_3, this.color_4, this.color_5, this.color_6, this.color_7, this.color_8);
			}
		}

		// Token: 0x060014F2 RID: 5362 RVA: 0x0008C574 File Offset: 0x0008A774
		private void method_0(PaintEventArgs paintEventArgs_0, Color color_11, Color color_12, Color color_13, Color color_14, Color color_15, Color color_16, Color color_17, Color color_18, Color color_19)
		{
			try
			{
				this.rectangle_0 = base.ClientRectangle;
				if (this.orientation_0 == Orientation.Horizontal)
				{
					if (this.image_0 != null)
					{
						decimal value = this.int_0 + (this.decimal_0 - this.decimal_1) * (base.ClientRectangle.Width - this.int_0 - this.int_1 - this.image_0.Width) / (this.decimal_2 - this.decimal_1);
						this.rectangle_4 = new Rectangle((int)value, base.ClientRectangle.Height / 2 - this.image_0.Height / 2, this.image_0.Width, this.image_0.Height);
					}
					else
					{
						decimal value2 = this.int_0 + (this.decimal_0 - this.decimal_1) * (base.ClientRectangle.Width - this.int_0 - this.int_1 - this.size_0.Width) / (this.decimal_2 - this.decimal_1);
						this.rectangle_4 = new Rectangle((int)value2, this.rectangle_0.Y + base.ClientRectangle.Height / 2 - this.size_0.Height / 2, this.size_0.Width, this.size_0.Height);
					}
				}
				else if (this.image_0 != null)
				{
					decimal value3 = this.int_1 + (this.decimal_2 - this.decimal_0) * (base.ClientRectangle.Height - this.int_0 - this.int_1 - this.image_0.Height) / (this.decimal_2 - this.decimal_1);
					this.rectangle_4 = new Rectangle(base.ClientRectangle.Width / 2 - this.image_0.Width / 2, (int)value3, this.image_0.Width, this.image_0.Height);
				}
				else
				{
					decimal value4 = this.int_1 + (this.decimal_2 - this.decimal_0) * (base.ClientRectangle.Height - this.int_0 - this.int_1 - this.size_0.Height) / (this.decimal_2 - this.decimal_1);
					this.rectangle_4 = new Rectangle(this.rectangle_0.X + base.ClientRectangle.Width / 2 - this.size_0.Width / 2, (int)value4, this.size_0.Width, this.size_0.Height);
				}
				this.rectangle_2 = this.rectangle_4;
				LinearGradientMode linearGradientMode;
				if (this.orientation_0 == Orientation.Horizontal)
				{
					this.rectangle_0.X = this.rectangle_0.X + this.int_0;
					this.rectangle_0.Width = this.rectangle_0.Width - this.int_0 - this.int_1;
					this.rectangle_1 = this.rectangle_0;
					this.rectangle_1.Height = this.rectangle_1.Height / 2;
					linearGradientMode = LinearGradientMode.Vertical;
					this.rectangle_2.Height = this.rectangle_2.Height / 2;
					this.rectangle_3 = this.rectangle_0;
					this.rectangle_3.Width = this.rectangle_4.Left + this.size_0.Width / 2 - this.int_0;
				}
				else
				{
					this.rectangle_0.Y = this.rectangle_0.Y + this.int_1;
					this.rectangle_0.Height = this.rectangle_0.Height - this.int_0 - this.int_1;
					this.rectangle_1 = this.rectangle_0;
					this.rectangle_1.Width = this.rectangle_1.Width / 2;
					linearGradientMode = LinearGradientMode.Vertical;
					this.rectangle_2.Width = this.rectangle_2.Width / 2;
					this.rectangle_3 = this.rectangle_0;
					this.rectangle_3.Height = this.rectangle_0.Height - (this.rectangle_4.Top + this.ThumbSize.Height / 2) + this.int_1;
					this.rectangle_3.Y = 1 + this.rectangle_4.Top + this.ThumbSize.Height / 2 + this.int_1;
				}
				GraphicsPath graphicsPath;
				if (this.graphicsPath_0 == null)
				{
					graphicsPath = Control0.smethod_0(this.rectangle_4, this.size_1);
				}
				else
				{
					graphicsPath = this.graphicsPath_0;
					Matrix matrix = new Matrix();
					matrix.Translate((float)this.rectangle_4.Left - graphicsPath.GetBounds().Left, (float)this.rectangle_4.Top - graphicsPath.GetBounds().Top);
					graphicsPath.Transform(matrix);
				}
				if (this.orientation_0 == Orientation.Horizontal)
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_14, 1f), this.rectangle_0.X, this.rectangle_0.Y + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_0.Width, this.rectangle_0.Y + this.rectangle_0.Height / 2);
				}
				else
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_14, 1f), this.rectangle_0.X + this.rectangle_0.Width / 2, this.rectangle_0.Y, this.rectangle_0.X + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height);
				}
				if (this.orientation_0 == Orientation.Horizontal)
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_19, 1f), this.rectangle_0.X, this.rectangle_0.Y + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_3.Width, this.rectangle_0.Y + this.rectangle_0.Height / 2);
				}
				else
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_19, 1f), this.rectangle_0.X + this.rectangle_0.Width / 2, this.rectangle_0.Y + (this.rectangle_0.Height - this.rectangle_3.Height), this.rectangle_0.X + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height);
				}
				if (this.orientation_0 == Orientation.Horizontal)
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_15, 1f), this.rectangle_0.X, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_3.Width, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_16, 1f), this.rectangle_0.X, this.rectangle_0.Y + 1 + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_3.Width, this.rectangle_0.Y + 1 + this.rectangle_0.Height / 2);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_17, 1f), this.rectangle_0.X + this.rectangle_3.Width, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_0.Width, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_18, 1f), this.rectangle_0.X + this.rectangle_3.Width, this.rectangle_0.Y + 1 + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_0.Width, this.rectangle_0.Y + 1 + this.rectangle_0.Height / 2);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_17, 1f), this.rectangle_0.X, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2, this.rectangle_0.X, this.rectangle_0.Y + this.rectangle_0.Height / 2 + 1);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_18, 1f), this.rectangle_0.X + this.rectangle_0.Width, this.rectangle_0.Y - 1 + this.rectangle_0.Height / 2, this.rectangle_0.X + this.rectangle_0.Width, this.rectangle_0.Y + 1 + this.rectangle_0.Height / 2);
				}
				else
				{
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_15, 1f), this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + (this.rectangle_0.Height - this.rectangle_3.Height), this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_16, 1f), this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + (this.rectangle_0.Height - this.rectangle_3.Height), this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_17, 1f), this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y, this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height - this.rectangle_3.Height);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_18, 1f), this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y, this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height - this.rectangle_3.Height);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_17, 1f), this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y, this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y);
					paintEventArgs_0.Graphics.DrawLine(new Pen(color_18, 1f), this.rectangle_0.X - 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height, this.rectangle_0.X + 1 + this.rectangle_0.Width / 2, this.rectangle_0.Y + this.rectangle_0.Height);
				}
				Color color = color_11;
				Color color2 = color_12;
				if (base.Capture && this.bool_0)
				{
					color = Color.FromArgb(175, color_11);
					color2 = Color.FromArgb(175, color_12);
				}
				LinearGradientBrush linearGradientBrush;
				if (this.orientation_0 == Orientation.Horizontal)
				{
					linearGradientBrush = new LinearGradientBrush(this.rectangle_4, color, color2, linearGradientMode);
				}
				else
				{
					linearGradientBrush = new LinearGradientBrush(this.rectangle_2, color, color2, linearGradientMode);
				}
				using (linearGradientBrush)
				{
					linearGradientBrush.WrapMode = WrapMode.TileFlipXY;
					paintEventArgs_0.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
					paintEventArgs_0.Graphics.FillPath(linearGradientBrush, graphicsPath);
					Color color3 = color_13;
					if (this.bool_2 && (base.Capture || this.bool_6))
					{
						color3 = ControlPaint.Dark(color3);
					}
					using (Pen pen = new Pen(color3))
					{
						if (this.image_0 != null)
						{
							Bitmap bitmap = new Bitmap(this.image_0);
							bitmap.MakeTransparent(Color.FromArgb(255, 0, 255));
							Rectangle srcRect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
							paintEventArgs_0.Graphics.DrawImage(bitmap, this.rectangle_4, srcRect, GraphicsUnit.Pixel);
							bitmap.Dispose();
						}
						else
						{
							paintEventArgs_0.Graphics.DrawPath(pen, graphicsPath);
						}
					}
				}
				if (this.Focused & this.bool_1)
				{
					using (Pen pen2 = new Pen(Color.FromArgb(200, color_15)))
					{
						pen2.DashStyle = DashStyle.Dot;
						Rectangle clientRectangle = base.ClientRectangle;
						clientRectangle.Width -= 2;
						int num = clientRectangle.Height;
						clientRectangle.Height = num - 1;
						num = clientRectangle.X;
						clientRectangle.X = num + 1;
						using (GraphicsPath graphicsPath2 = Control0.smethod_0(clientRectangle, this.size_2))
						{
							paintEventArgs_0.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
							paintEventArgs_0.Graphics.DrawPath(pen2, graphicsPath2);
						}
					}
				}
				if (this.tickStyle_0 != TickStyle.None)
				{
					int num2 = 1 + (int)(this.decimal_5 * (this.decimal_6 + 1m));
					int num3 = 0;
					int num4;
					int num5;
					float num6;
					int num7;
					if (this.orientation_0 == Orientation.Horizontal)
					{
						num4 = this.rectangle_4.Width / 2;
						num5 = this.rectangle_0.Width - this.rectangle_4.Width;
						num6 = (float)this.decimal_1;
						num7 = 2 + this.rectangle_4.Height / 2;
					}
					else
					{
						num4 = this.rectangle_4.Height / 2;
						num5 = this.rectangle_0.Height - this.rectangle_4.Height;
						num6 = (float)this.decimal_2;
						num7 = 2 + this.rectangle_4.Width / 2;
					}
					Pen pen3 = new Pen(this.color_9, 1f);
					Pen pen4 = new Pen(this.color_9, 1f);
					int num8 = 0;
					int num9 = 5;
					int num10 = 3;
					SolidBrush brush = new SolidBrush(this.ForeColor);
					string text = string.Format(Class521.smethod_0(50674), this.decimal_2);
					Font font = this.Font;
					SizeF sizeF = paintEventArgs_0.Graphics.MeasureString(text, font);
					int num11 = 0;
					while (num11 <= this.decimal_5)
					{
						float num12 = num6;
						if (this.float_0 != 0f)
						{
							num12 /= this.float_0;
						}
						if (this.float_1 != 0f)
						{
							num12 += this.float_1;
						}
						text = string.Format(Class521.smethod_0(50687), num12);
						SizeF sizeF2 = paintEventArgs_0.Graphics.MeasureString(text, font);
						if (this.orientation_0 == Orientation.Horizontal)
						{
							if (this.bool_4)
							{
								if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
								{
									float x = (float)(num4 + this.rectangle_0.X + num3) - (float)((double)sizeF2.Width * 0.5);
									float y = (float)(this.rectangle_0.Y + this.rectangle_0.Height / 2) - sizeF2.Height - (float)num9 - (float)num7;
									paintEventArgs_0.Graphics.DrawString(text, font, brush, x, y);
								}
								if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
								{
									float x = (float)(num4 + this.rectangle_0.X + num3) - (float)((double)sizeF2.Width * 0.5);
									float y = (float)(this.rectangle_0.Y + this.rectangle_0.Height / 2 + num9 + num7);
									paintEventArgs_0.Graphics.DrawString(text, font, brush, x, y);
								}
							}
							if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
							{
								int x2 = num4 + this.rectangle_0.X + num3;
								int y2 = this.rectangle_0.Y + this.rectangle_0.Height / 2 - num9 - num7;
								int x3 = num4 + this.rectangle_0.X + num3;
								int y3 = this.rectangle_0.Y + this.rectangle_0.Height / 2 - num7;
								paintEventArgs_0.Graphics.DrawLine(pen3, x2, y2, x3, y3);
							}
							if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
							{
								int x2 = num4 + this.rectangle_0.X + num3;
								int y2 = this.rectangle_0.Y + this.rectangle_0.Height / 2 + num7;
								int x3 = num4 + this.rectangle_0.X + num3;
								int y3 = this.rectangle_0.Y + this.rectangle_0.Height / 2 + num9 + num7;
								paintEventArgs_0.Graphics.DrawLine(pen3, x2, y2, x3, y3);
							}
							num6 += (float)((this.decimal_2 - this.decimal_1) / this.decimal_5);
							if (num11 < this.decimal_5)
							{
								int num13 = 0;
								while (num13 <= this.decimal_6)
								{
									num8++;
									num3 = num8 * num5 / (num2 - 1);
									if (this.bool_3)
									{
										if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
										{
											int x2 = num4 + this.rectangle_0.X + num3;
											int y2 = this.rectangle_0.Y + this.rectangle_0.Height / 2 - num10 - num7;
											int x3 = num4 + this.rectangle_0.X + num3;
											int y3 = this.rectangle_0.Y + this.rectangle_0.Height / 2 - num7;
											paintEventArgs_0.Graphics.DrawLine(pen4, x2, y2, x3, y3);
										}
										if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
										{
											int x2 = num4 + this.rectangle_0.X + num3;
											int y2 = this.rectangle_0.Y + this.rectangle_0.Height / 2 + num7;
											int x3 = num4 + this.rectangle_0.X + num3;
											int y3 = this.rectangle_0.Y + this.rectangle_0.Height / 2 + num10 + num7;
											paintEventArgs_0.Graphics.DrawLine(pen4, x2, y2, x3, y3);
										}
									}
									num13++;
								}
							}
						}
						else
						{
							if (this.bool_4)
							{
								if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
								{
									float x = (float)(this.rectangle_0.X + this.rectangle_0.Width / 2 - num9) - sizeF2.Width - (float)num7;
									float y = (float)(num4 + this.rectangle_0.Y + num3) - (float)((double)sizeF2.Height * 0.5);
									paintEventArgs_0.Graphics.DrawString(text, font, brush, x, y);
								}
								if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
								{
									float x = (float)(this.rectangle_0.X + this.rectangle_0.Width / 2 + num9 + num7);
									float y = (float)(num4 + this.rectangle_0.Y + num3) - (float)((double)sizeF2.Height * 0.5);
									paintEventArgs_0.Graphics.DrawString(text, font, brush, x, y);
								}
								float width = sizeF.Width;
							}
							if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
							{
								int x2 = this.rectangle_0.X + this.rectangle_0.Width / 2 - num9 - num7;
								int y2 = num4 + this.rectangle_0.Y + num3;
								int x3 = this.rectangle_0.X + this.rectangle_0.Width / 2 - num7;
								int y3 = num4 + this.rectangle_0.Y + num3;
								paintEventArgs_0.Graphics.DrawLine(pen3, x2, y2, x3, y3);
							}
							if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
							{
								int x2 = this.rectangle_0.X + this.rectangle_0.Width / 2 + num7;
								int y2 = num4 + this.rectangle_0.Y + num3;
								int x3 = this.rectangle_0.X + this.rectangle_0.Width / 2 + num9 + num7;
								int y3 = num4 + this.rectangle_0.Y + num3;
								paintEventArgs_0.Graphics.DrawLine(pen3, x2, y2, x3, y3);
							}
							num6 -= (float)((this.decimal_2 - this.decimal_1) / this.decimal_5);
							if (num11 < this.decimal_5)
							{
								int num14 = 0;
								while (num14 <= this.decimal_6)
								{
									num8++;
									num3 = num8 * num5 / (num2 - 1);
									if (this.bool_3)
									{
										if (this.tickStyle_0 == TickStyle.TopLeft || this.tickStyle_0 == TickStyle.Both)
										{
											int x2 = this.rectangle_0.X + this.rectangle_0.Width / 2 - num10 - num7;
											int y2 = num4 + this.rectangle_0.Y + num3;
											int x3 = this.rectangle_0.X + this.rectangle_0.Width / 2 - num7;
											int y3 = num4 + this.rectangle_0.Y + num3;
											paintEventArgs_0.Graphics.DrawLine(pen4, x2, y2, x3, y3);
										}
										if (this.tickStyle_0 == TickStyle.BottomRight || this.tickStyle_0 == TickStyle.Both)
										{
											int x2 = this.rectangle_0.X + this.rectangle_0.Width / 2 + num7;
											int y2 = num4 + this.rectangle_0.Y + num3;
											int x3 = this.rectangle_0.X + this.rectangle_0.Width / 2 + num10 + num7;
											int y3 = num4 + this.rectangle_0.Y + num3;
											paintEventArgs_0.Graphics.DrawLine(pen4, x2, y2, x3, y3);
										}
									}
									num14++;
								}
							}
						}
						num11++;
					}
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine(Class521.smethod_0(50700) + base.Name + Class521.smethod_0(50733) + ex.Message);
			}
		}

		// Token: 0x060014F3 RID: 5363 RVA: 0x00008618 File Offset: 0x00006818
		protected void OnEnabledChanged(EventArgs e)
		{
			base.OnEnabledChanged(e);
			base.Invalidate();
		}

		// Token: 0x060014F4 RID: 5364 RVA: 0x00008629 File Offset: 0x00006829
		protected void OnMouseEnter(EventArgs e)
		{
			base.OnMouseEnter(e);
			this.bool_5 = true;
			base.Invalidate();
		}

		// Token: 0x060014F5 RID: 5365 RVA: 0x00008641 File Offset: 0x00006841
		protected void OnMouseLeave(EventArgs e)
		{
			base.OnMouseLeave(e);
			this.bool_5 = false;
			this.bool_6 = false;
			base.Invalidate();
		}

		// Token: 0x060014F6 RID: 5366 RVA: 0x0008DE1C File Offset: 0x0008C01C
		protected void OnMouseDown(MouseEventArgs e)
		{
			base.OnMouseDown(e);
			if (e.Button == MouseButtons.Left)
			{
				base.Capture = true;
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.ThumbTrack, (int)this.decimal_0));
				}
				if (this.eventHandler_0 != null)
				{
					this.eventHandler_0(this, new EventArgs());
				}
				this.OnMouseMove(e);
			}
		}

		// Token: 0x060014F7 RID: 5367 RVA: 0x0008DE8C File Offset: 0x0008C08C
		protected void OnMouseMove(MouseEventArgs e)
		{
			base.OnMouseMove(e);
			this.bool_6 = Control0.smethod_3(e.Location, this.rectangle_4);
			if (base.Capture & e.Button == MouseButtons.Left)
			{
				ScrollEventType type = ScrollEventType.ThumbPosition;
				Point location = e.Location;
				int num = (this.orientation_0 == Orientation.Horizontal) ? location.X : location.Y;
				int num2 = this.size_0.Height >> 1;
				num -= num2;
				if (this.orientation_0 == Orientation.Horizontal)
				{
					if (this.image_0 != null)
					{
						this.decimal_0 = this.decimal_1 + (num - this.int_0) * (this.decimal_2 - this.decimal_1) / (base.ClientRectangle.Width - this.int_0 - this.int_1 - this.image_0.Width);
					}
					else
					{
						this.decimal_0 = this.decimal_1 + (num - this.int_0) * (this.decimal_2 - this.decimal_1) / (base.ClientRectangle.Width - this.int_0 - this.int_1 - this.size_0.Width);
					}
				}
				else if (this.image_0 != null)
				{
					this.decimal_0 = this.decimal_2 - (num - this.int_1) * (this.decimal_2 - this.decimal_1) / (base.ClientRectangle.Height - this.int_0 - this.int_1 - this.image_0.Height);
				}
				else
				{
					this.decimal_0 = this.decimal_2 - (num - this.int_1) * (this.decimal_2 - this.decimal_1) / (base.ClientRectangle.Height - this.int_0 - this.int_1 - this.size_0.Height);
				}
				int value = (int)Math.Round(this.decimal_0 / this.decimal_3);
				this.decimal_0 = value * this.decimal_3;
				if (this.decimal_0 <= this.decimal_1)
				{
					this.decimal_0 = this.decimal_1;
					type = ScrollEventType.First;
				}
				else if (this.decimal_0 >= this.decimal_2)
				{
					this.decimal_0 = this.decimal_2;
					type = ScrollEventType.Last;
				}
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(type, (int)this.decimal_0));
				}
				if (this.eventHandler_0 != null)
				{
					this.eventHandler_0(this, new EventArgs());
				}
			}
			base.Invalidate();
		}

		// Token: 0x060014F8 RID: 5368 RVA: 0x0008E188 File Offset: 0x0008C388
		protected void OnMouseUp(MouseEventArgs e)
		{
			base.OnMouseUp(e);
			base.Capture = false;
			this.bool_6 = Control0.smethod_3(e.Location, this.rectangle_4);
			if (this.scrollEventHandler_0 != null)
			{
				this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.EndScroll, (int)this.decimal_0));
			}
			if (this.eventHandler_0 != null)
			{
				this.eventHandler_0(this, new EventArgs());
			}
			base.Invalidate();
		}

		// Token: 0x060014F9 RID: 5369 RVA: 0x0008E200 File Offset: 0x0008C400
		protected void OnMouseWheel(MouseEventArgs e)
		{
			base.OnMouseWheel(e);
			if (this.bool_5)
			{
				decimal d = e.Delta / 120 * (this.decimal_2 - this.decimal_1) / this.int_3;
				this.method_1(this.Value + d);
				((HandledMouseEventArgs)e).Handled = true;
			}
		}

		// Token: 0x060014FA RID: 5370 RVA: 0x00008660 File Offset: 0x00006860
		protected void OnGotFocus(EventArgs e)
		{
			base.OnGotFocus(e);
			base.Invalidate();
		}

		// Token: 0x060014FB RID: 5371 RVA: 0x00008671 File Offset: 0x00006871
		protected void OnLostFocus(EventArgs e)
		{
			base.OnLostFocus(e);
			base.Invalidate();
		}

		// Token: 0x060014FC RID: 5372 RVA: 0x0008E274 File Offset: 0x0008C474
		protected void OnKeyUp(KeyEventArgs e)
		{
			base.OnKeyUp(e);
			switch (e.KeyCode)
			{
			case Keys.Prior:
				this.method_1(this.Value + (int)this.decimal_4);
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.LargeIncrement, (int)this.Value));
				}
				break;
			case Keys.Next:
				this.method_1(this.Value - (int)this.decimal_4);
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.LargeDecrement, (int)this.Value));
				}
				break;
			case Keys.End:
				this.Value = this.decimal_2;
				break;
			case Keys.Home:
				this.Value = this.decimal_1;
				break;
			case Keys.Left:
			case Keys.Down:
				this.method_1(this.Value - (int)this.decimal_3);
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.SmallDecrement, (int)this.Value));
				}
				break;
			case Keys.Up:
			case Keys.Right:
				this.method_1(this.Value + (int)this.decimal_3);
				if (this.scrollEventHandler_0 != null)
				{
					this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.SmallIncrement, (int)this.Value));
				}
				break;
			}
			if (this.scrollEventHandler_0 != null && this.Value == this.decimal_1)
			{
				this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.First, (int)this.Value));
			}
			if (this.scrollEventHandler_0 != null && this.Value == this.decimal_2)
			{
				this.scrollEventHandler_0(this, new ScrollEventArgs(ScrollEventType.Last, (int)this.Value));
			}
			Point point = base.PointToClient(Cursor.Position);
			this.OnMouseMove(new MouseEventArgs(MouseButtons.None, 0, point.X, point.Y, 0));
		}

		// Token: 0x060014FD RID: 5373 RVA: 0x0008E4A4 File Offset: 0x0008C6A4
		protected bool ProcessDialogKey(Keys keyData)
		{
			bool result;
			if (keyData == Keys.Tab | Control.ModifierKeys == Keys.Shift)
			{
				result = base.ProcessDialogKey(keyData);
			}
			else
			{
				this.OnKeyDown(new KeyEventArgs(keyData));
				result = true;
			}
			return result;
		}

		// Token: 0x060014FE RID: 5374 RVA: 0x0008E4E0 File Offset: 0x0008C6E0
		public static GraphicsPath smethod_0(Rectangle rectangle_5, Size size_3)
		{
			GraphicsPath graphicsPath = new GraphicsPath();
			graphicsPath.AddLine(rectangle_5.Left + size_3.Width / 2, rectangle_5.Top, rectangle_5.Right - size_3.Width / 2, rectangle_5.Top);
			graphicsPath.AddArc(rectangle_5.Right - size_3.Width, rectangle_5.Top, size_3.Width, size_3.Height, 270f, 90f);
			graphicsPath.AddLine(rectangle_5.Right, rectangle_5.Top + size_3.Height / 2, rectangle_5.Right, rectangle_5.Bottom - size_3.Width / 2);
			graphicsPath.AddArc(rectangle_5.Right - size_3.Width, rectangle_5.Bottom - size_3.Height, size_3.Width, size_3.Height, 0f, 90f);
			graphicsPath.AddLine(rectangle_5.Right - size_3.Width / 2, rectangle_5.Bottom, rectangle_5.Left + size_3.Width / 2, rectangle_5.Bottom);
			graphicsPath.AddArc(rectangle_5.Left, rectangle_5.Bottom - size_3.Height, size_3.Width, size_3.Height, 90f, 90f);
			graphicsPath.AddLine(rectangle_5.Left, rectangle_5.Bottom - size_3.Height / 2, rectangle_5.Left, rectangle_5.Top + size_3.Height / 2);
			graphicsPath.AddArc(rectangle_5.Left, rectangle_5.Top, size_3.Width, size_3.Height, 180f, 90f);
			return graphicsPath;
		}

		// Token: 0x060014FF RID: 5375 RVA: 0x0008E6A0 File Offset: 0x0008C8A0
		public static Color[] smethod_1(params Color[] color_11)
		{
			Color[] array = new Color[color_11.Length];
			for (int i = 0; i < color_11.Length; i++)
			{
				int num = (int)((double)color_11[i].R * 0.3 + (double)color_11[i].G * 0.6 + (double)color_11[i].B * 0.1);
				array[i] = Color.FromArgb(-65793 * (255 - num) - 1);
			}
			return array;
		}

		// Token: 0x06001500 RID: 5376 RVA: 0x0008E730 File Offset: 0x0008C930
		public static Color[] smethod_2(params Color[] color_11)
		{
			Color[] array = new Color[color_11.Length];
			for (int i = 0; i < color_11.Length; i++)
			{
				array[i] = ControlPaint.Light(color_11[i]);
			}
			return array;
		}

		// Token: 0x06001501 RID: 5377 RVA: 0x0008E76C File Offset: 0x0008C96C
		private void method_1(decimal decimal_7)
		{
			if (decimal_7 < this.decimal_1)
			{
				this.Value = this.decimal_1;
			}
			else if (decimal_7 > this.decimal_2)
			{
				this.Value = this.decimal_2;
			}
			else
			{
				this.Value = decimal_7;
			}
		}

		// Token: 0x06001502 RID: 5378 RVA: 0x0008E7BC File Offset: 0x0008C9BC
		private static bool smethod_3(Point point_0, Rectangle rectangle_5)
		{
			bool result;
			if (point_0.X > rectangle_5.Left & point_0.X < rectangle_5.Right & point_0.Y > rectangle_5.Top & point_0.Y < rectangle_5.Bottom)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001503 RID: 5379 RVA: 0x00008682 File Offset: 0x00006882
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001504 RID: 5380 RVA: 0x000086A3 File Offset: 0x000068A3
		private void method_2()
		{
			base.SuspendLayout();
			base.Size = new Size(200, 48);
			base.ResumeLayout(false);
		}

		// Token: 0x04000AB2 RID: 2738
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000AB3 RID: 2739
		[CompilerGenerated]
		private ScrollEventHandler scrollEventHandler_0;

		// Token: 0x04000AB4 RID: 2740
		private Rectangle rectangle_0;

		// Token: 0x04000AB5 RID: 2741
		private Rectangle rectangle_1;

		// Token: 0x04000AB6 RID: 2742
		private Rectangle rectangle_2;

		// Token: 0x04000AB7 RID: 2743
		private Rectangle rectangle_3;

		// Token: 0x04000AB8 RID: 2744
		private int int_0;

		// Token: 0x04000AB9 RID: 2745
		private int int_1;

		// Token: 0x04000ABA RID: 2746
		private Rectangle rectangle_4;

		// Token: 0x04000ABB RID: 2747
		private Size size_0 = new Size(16, 16);

		// Token: 0x04000ABC RID: 2748
		private GraphicsPath graphicsPath_0;

		// Token: 0x04000ABD RID: 2749
		private Size size_1 = new Size(16, 16);

		// Token: 0x04000ABE RID: 2750
		private Size size_2 = new Size(8, 8);

		// Token: 0x04000ABF RID: 2751
		private bool bool_0 = true;

		// Token: 0x04000AC0 RID: 2752
		private Image image_0;

		// Token: 0x04000AC1 RID: 2753
		private int int_2;

		// Token: 0x04000AC2 RID: 2754
		private Orientation orientation_0;

		// Token: 0x04000AC3 RID: 2755
		private bool bool_1;

		// Token: 0x04000AC4 RID: 2756
		private bool bool_2 = true;

		// Token: 0x04000AC5 RID: 2757
		private decimal decimal_0 = 30m;

		// Token: 0x04000AC6 RID: 2758
		private decimal decimal_1;

		// Token: 0x04000AC7 RID: 2759
		private decimal decimal_2 = 100m;

		// Token: 0x04000AC8 RID: 2760
		private decimal decimal_3 = 1m;

		// Token: 0x04000AC9 RID: 2761
		private decimal decimal_4 = 5m;

		// Token: 0x04000ACA RID: 2762
		private int int_3 = 10;

		// Token: 0x04000ACB RID: 2763
		private Color color_0 = Color.White;

		// Token: 0x04000ACC RID: 2764
		private Color color_1 = Color.FromArgb(21, 56, 152);

		// Token: 0x04000ACD RID: 2765
		private Color color_2 = Color.FromArgb(21, 56, 152);

		// Token: 0x04000ACE RID: 2766
		private Color color_3 = Color.Black;

		// Token: 0x04000ACF RID: 2767
		private Color color_4 = Color.FromArgb(95, 140, 180);

		// Token: 0x04000AD0 RID: 2768
		private Color color_5 = Color.FromArgb(99, 130, 208);

		// Token: 0x04000AD1 RID: 2769
		private Color color_6 = Color.FromArgb(55, 60, 74);

		// Token: 0x04000AD2 RID: 2770
		private Color color_7 = Color.FromArgb(87, 94, 110);

		// Token: 0x04000AD3 RID: 2771
		private Color color_8 = Color.FromArgb(21, 56, 152);

		// Token: 0x04000AD4 RID: 2772
		private Color color_9 = Color.White;

		// Token: 0x04000AD5 RID: 2773
		private float float_0;

		// Token: 0x04000AD6 RID: 2774
		private float float_1;

		// Token: 0x04000AD7 RID: 2775
		private TickStyle tickStyle_0 = TickStyle.TopLeft;

		// Token: 0x04000AD8 RID: 2776
		private decimal decimal_5 = 10m;

		// Token: 0x04000AD9 RID: 2777
		private decimal decimal_6 = 5m;

		// Token: 0x04000ADA RID: 2778
		private bool bool_3;

		// Token: 0x04000ADB RID: 2779
		private bool bool_4 = true;

		// Token: 0x04000ADC RID: 2780
		private Color[,] color_10;

		// Token: 0x04000ADD RID: 2781
		private Control0.Enum19 enum19_0;

		// Token: 0x04000ADE RID: 2782
		private bool bool_5;

		// Token: 0x04000ADF RID: 2783
		private bool bool_6;

		// Token: 0x04000AE0 RID: 2784
		private IContainer icontainer_0;

		// Token: 0x02000201 RID: 513
		public enum Enum19
		{
			// Token: 0x04000AE2 RID: 2786
			const_0,
			// Token: 0x04000AE3 RID: 2787
			const_1,
			// Token: 0x04000AE4 RID: 2788
			const_2
		}
	}
}
