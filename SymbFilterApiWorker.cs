﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using ns0;
using ns15;
using ns18;
using ns2;
using ns8;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000020 RID: 32
	internal sealed class SymbFilterApiWorker : Class10
	{
		// Token: 0x14000001 RID: 1
		// (add) Token: 0x060000B3 RID: 179 RVA: 0x000129D8 File Offset: 0x00010BD8
		// (remove) Token: 0x060000B4 RID: 180 RVA: 0x00012A10 File Offset: 0x00010C10
		public event EventHandler DuplicateRequestDetected
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060000B5 RID: 181 RVA: 0x00002D4E File Offset: 0x00000F4E
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000002 RID: 2
		// (add) Token: 0x060000B6 RID: 182 RVA: 0x00012A48 File Offset: 0x00010C48
		// (remove) Token: 0x060000B7 RID: 183 RVA: 0x00012A80 File Offset: 0x00010C80
		public event EventHandler SendingRequest
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060000B8 RID: 184 RVA: 0x00002D69 File Offset: 0x00000F69
		protected void method_2()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x060000B9 RID: 185 RVA: 0x00002D06 File Offset: 0x00000F06
		public SymbFilterApiWorker()
		{
			base.ResultCompressed = true;
		}

		// Token: 0x060000BA RID: 186 RVA: 0x00012AB8 File Offset: 0x00010CB8
		public void method_3(List<FilterCond> list_0, DateTime dateTime_0, string string_1, string string_2)
		{
			string text = this.method_4(list_0, dateTime_0);
			string value = text + string_1 + string_2;
			if (!string.IsNullOrEmpty(this.string_0) && this.string_0.Equals(value, StringComparison.InvariantCultureIgnoreCase))
			{
				this.method_1();
			}
			else
			{
				this.method_2();
				string gparam_ = Class521.smethod_0(1402);
				Dictionary<string, object> dictionary = new Dictionary<string, object>();
				DateTime dateTime = Base.Trading.smethod_22(dateTime_0);
				string[] gparam_2 = this.method_5(string_2);
				Class0<string, string, int, Class2<string, DateTime, string[], string, bool>> value2 = new Class0<string, string, int, Class2<string, DateTime, string[], string, bool>>(gparam_, TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class2<string, DateTime, string[], string, bool>(text, dateTime, gparam_2, string_1, false));
				dictionary[Class521.smethod_0(1376)] = value2;
				dictionary[Class521.smethod_0(1423)] = text;
				dictionary[Class521.smethod_0(1436)] = dateTime;
				base.GetHttpResponseInBackground(dictionary);
				this.string_0 = value;
			}
		}

		// Token: 0x060000BB RID: 187 RVA: 0x00012B94 File Offset: 0x00010D94
		private string method_4(List<FilterCond> list_0, DateTime dateTime_0)
		{
			string text = Class521.smethod_0(1449);
			List<FilterCond> list = list_0.OrderBy(new Func<FilterCond, string>(SymbFilterApiWorker.<>c.<>9.method_0)).ToList<FilterCond>();
			for (int i = 0; i < list.Count; i++)
			{
				string str = list[i].method_0(dateTime_0);
				string str2 = (i == list.Count - 1) ? Class521.smethod_0(1449) : Environment.NewLine;
				text = text + str + str2;
			}
			return text;
		}

		// Token: 0x060000BC RID: 188 RVA: 0x00012C28 File Offset: 0x00010E28
		private string[] method_5(string string_1)
		{
			SymbFilterApiWorker.Class12 @class = new SymbFilterApiWorker.Class12();
			@class.string_0 = string_1;
			string[] result = null;
			IEnumerable<StkSymbol> enumerable = null;
			if (TApp.IsTrialUser)
			{
				enumerable = TApp.SrvParams.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(SymbFilterApiWorker.<>c.<>9.method_1));
				if (!string.IsNullOrEmpty(@class.string_0))
				{
					enumerable = enumerable.Where(new Func<StkSymbol, bool>(@class.method_0));
				}
			}
			else if (!string.IsNullOrEmpty(@class.string_0))
			{
				enumerable = TApp.SrvParams.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1));
			}
			if (enumerable != null)
			{
				result = enumerable.Select(new Func<StkSymbol, string>(SymbFilterApiWorker.<>c.<>9.method_2)).ToArray<string>();
			}
			return result;
		}

		// Token: 0x060000BD RID: 189 RVA: 0x00012D04 File Offset: 0x00010F04
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && reqDict.ContainsKey(Class521.smethod_0(1423)))
			{
				string string_ = rslt.data as string;
				this.method_6(string_, reqDict);
			}
		}

		// Token: 0x060000BE RID: 190 RVA: 0x00012D3C File Offset: 0x00010F3C
		private void method_6(string string_1, Dictionary<string, object> dictionary_0)
		{
			if (string_1 != null)
			{
				DataTable dataTableFromCsv = Utility.GetDataTableFromCsv(string_1, new int[1]);
				DataTable dataTable = new DataTable();
				dataTable.Columns.Add(new DataColumn(Class521.smethod_0(1450)));
				dataTable.Columns.Add(new DataColumn(Class521.smethod_0(1463)));
				dataTable.Columns.Add(new DataColumn(Class521.smethod_0(1472)));
				foreach (StkSymbol stkSymbol in Base.Data.UsrStkSymbols.Values)
				{
					if (stkSymbol.IsStockCompany)
					{
						string text = stkSymbol.method_1();
						if (!string.IsNullOrEmpty(text))
						{
							object[] values = new object[]
							{
								text,
								stkSymbol.Code,
								stkSymbol.CNName
							};
							dataTable.Rows.Add(values);
						}
					}
				}
				DataTable dataTable2 = Utility.JoinTwoDataTablesOnOneColumn(dataTable, dataTableFromCsv, Class521.smethod_0(1450), JoinType.Inner);
				dataTable2.Columns.Remove(Class521.smethod_0(1450));
				Dictionary<string, List<FilterCond>> condDict = SymbFilterPanel.CondDict;
				List<FilterCond> list = new List<FilterCond>();
				foreach (List<FilterCond> collection in condDict.Values)
				{
					list.AddRange(collection);
				}
				Dictionary<int, int> dictionary = new Dictionary<int, int>();
				for (int i = 0; i < dataTable2.Columns.Count; i++)
				{
					SymbFilterApiWorker.Class13 @class = new SymbFilterApiWorker.Class13();
					@class.dataColumn_0 = dataTable2.Columns[i];
					if (@class.dataColumn_0.ColumnName == Class521.smethod_0(1481))
					{
						@class.dataColumn_0.ColumnName = Class521.smethod_0(1494);
					}
					else
					{
						FilterCond filterCond = list.FirstOrDefault(new Func<FilterCond, bool>(@class.method_0));
						if (filterCond != null)
						{
							string text2 = filterCond.Name;
							if (!(text2 == Class521.smethod_0(1507)) && !(text2 == Class521.smethod_0(1524)) && !(text2 == Class521.smethod_0(1537)) && !(text2 == Class521.smethod_0(1550)))
							{
								if (filterCond.UnitType == Enum20.const_3)
								{
									text2 += Class521.smethod_0(1576);
									dictionary.Add(i, 2);
								}
								else if (filterCond.UnitType == Enum20.const_2 || filterCond.UnitType == Enum20.const_1)
								{
									dictionary.Add(i, 0);
								}
							}
							else
							{
								text2 += Class521.smethod_0(1567);
								dictionary.Add(i, 0);
							}
							@class.dataColumn_0.ColumnName = text2;
						}
					}
				}
				if (dataTable2.Rows.Count > 0)
				{
					for (int j = 0; j < dataTable2.Rows.Count; j++)
					{
						DataRow dataRow = dataTable2.Rows[j];
						for (int k = 3; k < dataTable2.Columns.Count - 1; k++)
						{
							object obj = dataRow[k];
							if (obj != null && obj.GetType() == typeof(double) && !double.IsNaN((double)obj) && dictionary.ContainsKey(k))
							{
								dataRow[k] = Math.Round((double)obj, dictionary[k]);
							}
						}
					}
				}
				dictionary_0[Class521.smethod_0(1581)] = dataTable2;
			}
		}

		// Token: 0x04000043 RID: 67
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000044 RID: 68
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000045 RID: 69
		private string string_0;

		// Token: 0x02000022 RID: 34
		[CompilerGenerated]
		private sealed class Class12
		{
			// Token: 0x060000C5 RID: 197 RVA: 0x0001313C File Offset: 0x0001133C
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.IdxClassLv2 == this.string_0;
			}

			// Token: 0x060000C6 RID: 198 RVA: 0x0001313C File Offset: 0x0001133C
			internal bool method_1(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.IdxClassLv2 == this.string_0;
			}

			// Token: 0x0400004A RID: 74
			public string string_0;
		}

		// Token: 0x02000023 RID: 35
		[CompilerGenerated]
		private sealed class Class13
		{
			// Token: 0x060000C8 RID: 200 RVA: 0x00013160 File Offset: 0x00011360
			internal bool method_0(FilterCond filterCond_0)
			{
				return filterCond_0.TableKey.EndsWith(this.dataColumn_0.ColumnName);
			}

			// Token: 0x0400004B RID: 75
			public DataColumn dataColumn_0;
		}
	}
}
