﻿using System;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns18;
using ns25;
using ns26;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000213 RID: 531
	internal sealed class DataGridViewOpenTrans : Class292
	{
		// Token: 0x060015BE RID: 5566 RVA: 0x00094AAC File Offset: 0x00092CAC
		public DataGridViewOpenTrans()
		{
			base.MouseClick += this.DataGridViewOpenTrans_MouseClick;
			base.MouseDoubleClick += this.DataGridViewOpenTrans_MouseDoubleClick;
			base.RowContextMenuStripNeeded += this.DataGridViewOpenTrans_RowContextMenuStripNeeded;
			base.Resize += this.DataGridViewOpenTrans_Resize;
		}

		// Token: 0x060015BF RID: 5567 RVA: 0x00008C21 File Offset: 0x00006E21
		protected override void vmethod_1()
		{
			base.SuspendLayout();
			base.DataSource = Base.Trading.CurrOpenTransList;
			this.method_11();
			base.ResumeLayout();
		}

		// Token: 0x060015C0 RID: 5568 RVA: 0x00008ABA File Offset: 0x00006CBA
		public void method_5()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015C1 RID: 5569 RVA: 0x00094B0C File Offset: 0x00092D0C
		protected override void vmethod_0()
		{
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(this.method_9());
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015C2 RID: 5570 RVA: 0x00094B40 File Offset: 0x00092D40
		private ToolStripMenuItem method_6()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52658);
			toolStripMenuItem.Text = Class521.smethod_0(52679);
			toolStripMenuItem.Click += this.method_13;
			return toolStripMenuItem;
		}

		// Token: 0x060015C3 RID: 5571 RVA: 0x00094B88 File Offset: 0x00092D88
		private ToolStripMenuItem method_7()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52696);
			toolStripMenuItem.Text = Class521.smethod_0(52717);
			toolStripMenuItem.Click += this.method_15;
			return toolStripMenuItem;
		}

		// Token: 0x060015C4 RID: 5572 RVA: 0x00094BD0 File Offset: 0x00092DD0
		private ToolStripMenuItem method_8()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52734);
			toolStripMenuItem.Text = Class521.smethod_0(47066);
			toolStripMenuItem.Click += this.method_16;
			return toolStripMenuItem;
		}

		// Token: 0x060015C5 RID: 5573 RVA: 0x00094C18 File Offset: 0x00092E18
		private ToolStripMenuItem method_9()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52755);
			toolStripMenuItem.Text = Class521.smethod_0(52717);
			toolStripMenuItem.Click += this.method_15;
			toolStripMenuItem.Paint += this.method_17;
			return toolStripMenuItem;
		}

		// Token: 0x060015C6 RID: 5574 RVA: 0x00094C74 File Offset: 0x00092E74
		private ContextMenuStrip method_10()
		{
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(this.method_6());
			contextMenuStrip.Items.Add(this.method_7());
			contextMenuStrip.Items.Add(this.method_8());
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStripOfRow = contextMenuStrip;
			return contextMenuStrip;
		}

		// Token: 0x060015C7 RID: 5575 RVA: 0x00094CD0 File Offset: 0x00092ED0
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			base.vmethod_3(dataGridViewCellFormattingEventArgs_0);
			int columnIndex = dataGridViewCellFormattingEventArgs_0.ColumnIndex;
			DataGridViewRow dataGridViewRow = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex];
			DataGridViewCell dataGridViewCell_ = dataGridViewRow.Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
			string dataPropertyName = base.Columns[columnIndex].DataPropertyName;
			string a = dataPropertyName;
			ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
			if (!(a == Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_CurrPrice())), typeof(object)), new ParameterExpression[]
			{
				parameterExpression
			}))))
			{
				string a2 = dataPropertyName;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				if (!(a2 == Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))))
				{
					string a3 = dataPropertyName;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					if (a3 == Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_ProfitRatio())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					})))
					{
						decimal d = (decimal)dataGridViewCellFormattingEventArgs_0.Value;
						if (d != 0m)
						{
							string text = d.ToString(Class521.smethod_0(47501)) + Class521.smethod_0(5356);
							if (!text.EndsWith(Class521.smethod_0(5356)))
							{
								text += Class521.smethod_0(5356);
							}
							dataGridViewCellFormattingEventArgs_0.Value = text;
						}
						base.method_3(dataGridViewCell_);
						return;
					}
					string a4 = dataPropertyName;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					if (a4 == Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					})))
					{
						if (dataGridViewCellFormattingEventArgs_0.Value == null)
						{
							return;
						}
						try
						{
							dataGridViewCellFormattingEventArgs_0.CellStyle.Format = Class521.smethod_0(5141) + base.method_4((decimal)dataGridViewCellFormattingEventArgs_0.Value).ToString();
							base.method_3(dataGridViewCell_);
							return;
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
							return;
						}
					}
					string a5 = dataPropertyName;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					if (a5 == Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_LongOrShort())), new ParameterExpression[]
					{
						parameterExpression
					})))
					{
						base.method_2(dataGridViewCell_);
						return;
					}
					return;
				}
			}
			try
			{
				StkSymbol stkSymbol = SymbMgr.smethod_3((dataGridViewRow.DataBoundItem as ShownOpenTrans).SymbolID);
				dataGridViewCellFormattingEventArgs_0.CellStyle.Format = Class521.smethod_0(5141) + stkSymbol.DigitNb.ToString();
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
		}

		// Token: 0x060015C8 RID: 5576 RVA: 0x00095028 File Offset: 0x00093228
		private void DataGridViewOpenTrans_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			int columnIndex = base.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = base.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex >= 0 && rowIndex >= 0)
			{
				string dataPropertyName = base.Columns[columnIndex].DataPropertyName;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				if (dataPropertyName != Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_CurrPrice())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				})) && rowIndex >= 0)
				{
					ShownOpenTrans shownOpenTrans = base.Rows[rowIndex].DataBoundItem as ShownOpenTrans;
					if (shownOpenTrans.SymbolID != Base.Data.CurrSelectedSymbol.ID)
					{
						Base.Data.smethod_62(shownOpenTrans.SymbolID, false);
					}
					else if ((Base.UI.Form.IfNoConfClsTransWhenDblClick || MessageBox.Show(Class521.smethod_0(52776) + (Base.UI.Form.IsInBlindTestMode ? string.Empty : (Class521.smethod_0(5455) + shownOpenTrans.SymblCode + Class521.smethod_0(37572))) + Class521.smethod_0(52805), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes) && Base.Trading.smethod_57(shownOpenTrans))
					{
						this.method_5();
					}
				}
			}
		}

		// Token: 0x060015C9 RID: 5577 RVA: 0x000951A0 File Offset: 0x000933A0
		private void DataGridViewOpenTrans_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			dataGridViewRow.Selected = true;
			e.ContextMenuStrip = this.method_10();
			ShownOpenTrans shownOpenTrans = dataGridViewRow.DataBoundItem as ShownOpenTrans;
			e.ContextMenuStrip.Items[0].Tag = shownOpenTrans;
			ToolStripItem toolStripItem = e.ContextMenuStrip.Items[2];
			SymbDataSet symbDataSet = Base.Data.smethod_49(shownOpenTrans.SymbolID, false);
			if (symbDataSet != null && symbDataSet.HasValidDataSet && symbDataSet.CurrHisData.Close > 0.0)
			{
				toolStripItem.Tag = shownOpenTrans;
				toolStripItem.Enabled = true;
			}
			else
			{
				toolStripItem.Enabled = false;
			}
		}

		// Token: 0x060015CA RID: 5578 RVA: 0x0009524C File Offset: 0x0009344C
		private void method_11()
		{
			if (base.Columns.Count > 0)
			{
				base.AutoGenerateColumns = false;
				try
				{
					DataGridViewColumnCollection columns = base.Columns;
					ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_OpenUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 2;
					DataGridViewColumnCollection columns2 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns2[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_TodayUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 3;
					DataGridViewColumnCollection columns3 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns3[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_UsableUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 4;
					DataGridViewColumnCollection columns4 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns4[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].HeaderText = Class521.smethod_0(52810);
					DataGridViewColumnCollection columns5 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns5[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 5;
					DataGridViewColumnCollection columns6 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns6[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_ProfitRatio())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 6;
					DataGridViewColumnCollection columns7 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns7[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].HeaderText = Class521.smethod_0(52827);
					DataGridViewColumnCollection columns8 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns8[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 7;
					DataGridViewColumnCollection columns9 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns9[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_CurrPrice())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].DisplayIndex = 8;
					DataGridViewColumnCollection columns10 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns10[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Units())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = false;
					DataGridViewColumnCollection columns11 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns11[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Fee())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = false;
					DataGridViewColumnCollection columns12 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns12[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(Transaction.get_Notes())), new ParameterExpression[]
					{
						parameterExpression
					}))].Visible = false;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				try
				{
					if (Base.UI.Form.IsInBlindTestMode)
					{
						ParameterExpression parameterExpression;
						if (!Base.UI.Form.IsSingleBlindTest)
						{
							DataGridViewColumnCollection columns13 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns13[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_SymblCode())), new ParameterExpression[]
							{
								parameterExpression
							}))].Visible = false;
						}
						DataGridViewColumnCollection columns14 = base.Columns;
						parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
						columns14[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
						{
							parameterExpression
						}))].Visible = false;
						DataGridViewColumnCollection columns15 = base.Columns;
						parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
						columns15[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
						{
							parameterExpression
						}))].Visible = false;
					}
					else
					{
						DataGridViewColumnCollection columns16 = base.Columns;
						ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
						columns16[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_SymblCode())), new ParameterExpression[]
						{
							parameterExpression
						}))].Visible = true;
						if (Base.UI.Form.IfShowIndividualShownOpenTrans)
						{
							DataGridViewColumnCollection columns17 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns17[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
							{
								parameterExpression
							}))].Visible = true;
							DataGridViewColumnCollection columns18 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns18[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
							{
								parameterExpression
							}))].Visible = false;
						}
						else
						{
							DataGridViewColumnCollection columns19 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns19[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
							{
								parameterExpression
							}))].Visible = false;
							DataGridViewColumnCollection columns20 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns20[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
							{
								parameterExpression
							}))].Visible = true;
							DataGridViewColumnCollection columns21 = base.Columns;
							parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
							columns21[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
							{
								parameterExpression
							}))].HeaderText = Class521.smethod_0(52844);
						}
					}
				}
				catch (Exception exception_2)
				{
					Class184.smethod_0(exception_2);
				}
				this.Refresh();
			}
		}

		// Token: 0x060015CB RID: 5579 RVA: 0x00095B04 File Offset: 0x00093D04
		private void DataGridViewOpenTrans_MouseClick(object sender, MouseEventArgs e)
		{
			int columnIndex = base.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = base.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex == 4 && rowIndex >= 0)
			{
				ShownOpenTrans shownOpenTrans_ = base.Rows[rowIndex].DataBoundItem as ShownOpenTrans;
				this.method_12(shownOpenTrans_);
			}
		}

		// Token: 0x060015CC RID: 5580 RVA: 0x00008C42 File Offset: 0x00006E42
		public void method_12(ShownOpenTrans shownOpenTrans_0)
		{
			if (shownOpenTrans_0 != null && Base.Data.smethod_52(shownOpenTrans_0.SymbolID) != null)
			{
				new SetStopLimitForm
				{
					Tag = shownOpenTrans_0,
					ShowInTaskbar = false
				}.ShowDialog();
			}
		}

		// Token: 0x060015CD RID: 5581 RVA: 0x000041B9 File Offset: 0x000023B9
		private void DataGridViewOpenTrans_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015CE RID: 5582 RVA: 0x00008C6F File Offset: 0x00006E6F
		private void method_13(object sender, EventArgs e)
		{
			this.method_14();
		}

		// Token: 0x060015CF RID: 5583 RVA: 0x00095B68 File Offset: 0x00093D68
		public void method_14()
		{
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else if (base.SelectedRows.Count > 0 && Base.Trading.smethod_57(base.SelectedRows[0].DataBoundItem as ShownOpenTrans))
			{
				this.method_5();
			}
		}

		// Token: 0x060015D0 RID: 5584 RVA: 0x00008C79 File Offset: 0x00006E79
		private void method_15(object sender, EventArgs e)
		{
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else if (Base.Trading.smethod_217() && Base.Trading.smethod_55())
			{
				this.method_5();
			}
		}

		// Token: 0x060015D1 RID: 5585 RVA: 0x00095BBC File Offset: 0x00093DBC
		private void method_16(object sender, EventArgs e)
		{
			ShownOpenTrans shownOpenTrans_ = (sender as ToolStripMenuItem).Tag as ShownOpenTrans;
			this.method_12(shownOpenTrans_);
		}

		// Token: 0x060015D2 RID: 5586 RVA: 0x00008CA4 File Offset: 0x00006EA4
		private void method_17(object sender, PaintEventArgs e)
		{
			if (Base.Trading.CurrOpenTransList.Count < 1)
			{
				(sender as ToolStripMenuItem).Enabled = false;
			}
			else
			{
				(sender as ToolStripMenuItem).Enabled = true;
			}
		}

		// Token: 0x060015D3 RID: 5587 RVA: 0x00095BE4 File Offset: 0x00093DE4
		private void method_18()
		{
			int num = 0;
			VScrollBar vscrollBar = base.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = base.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 586m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					DataGridViewColumnCollection columns = base.Columns;
					ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_SymblCode())), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(66 * num2 / d)) : 66);
					DataGridViewColumnCollection columns2 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns2[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_LongOrShort())), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					DataGridViewColumnCollection columns3 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns3[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_TodayUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					DataGridViewColumnCollection columns4 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns4[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_UsableUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					DataGridViewColumnCollection columns5 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns5[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_ProfitRatio())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(74 * num2 / d)) : 74);
					DataGridViewColumnCollection columns6 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns6[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Price())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(70 * num2 / d)) : 70);
					DataGridViewColumnCollection columns7 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns7[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_OpenUnits())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					DataGridViewColumnCollection columns8 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					columns8[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))].Width = (flag ? Convert.ToInt32(Math.Floor(84 * num2 / d)) : 84);
					DataGridViewColumnCollection columns9 = base.Columns;
					parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
					DataGridViewColumn dataGridViewColumn = columns9[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_CreateTime())), typeof(object)), new ParameterExpression[]
					{
						parameterExpression
					}))];
					if (!dataGridViewColumn.Visible)
					{
						DataGridViewColumnCollection columns10 = base.Columns;
						parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
						dataGridViewColumn = columns10[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_UpdateTime())), typeof(object)), new ParameterExpression[]
						{
							parameterExpression
						}))];
					}
					dataGridViewColumn.Width = (flag ? Convert.ToInt32(Math.Floor(120 * num2 / d)) : 120);
					return;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 406m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 466m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				DataGridViewColumnCollection columns11 = base.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns11[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_SymblCode())), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(66 * num2 / d)) : 66);
			}
			try
			{
				DataGridViewColumnCollection columns12 = base.Columns;
				ParameterExpression parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns12[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_LongOrShort())), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				DataGridViewColumnCollection columns13 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns13[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_TodayUnits())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				DataGridViewColumnCollection columns14 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns14[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_UsableUnits())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				DataGridViewColumnCollection columns15 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns15[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(ShownOpenTrans.get_ProfitRatio())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(73 * num2 / d)) : 73);
				DataGridViewColumnCollection columns16 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns16[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_OpenUnits())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				DataGridViewColumnCollection columns17 = base.Columns;
				parameterExpression = Expression.Parameter(typeof(ShownOpenTrans), Class521.smethod_0(52097));
				columns17[Utility.GetPropertyName<ShownOpenTrans>(Expression.Lambda<Func<ShownOpenTrans, object>>(Expression.Convert(Expression.Property(parameterExpression, methodof(Transaction.get_Profit())), typeof(object)), new ParameterExpression[]
				{
					parameterExpression
				}))].Width = (flag ? Convert.ToInt32(Math.Floor(85 * num2 / d)) : 85);
			}
			catch (Exception exception_2)
			{
				Class184.smethod_0(exception_2);
			}
		}

		// Token: 0x1700038B RID: 907
		// (get) Token: 0x060015D4 RID: 5588 RVA: 0x00096598 File Offset: 0x00094798
		// (set) Token: 0x060015D5 RID: 5589 RVA: 0x00008CCF File Offset: 0x00006ECF
		public ContextMenuStrip ContextMenuStripOfRow { get; set; }

		// Token: 0x04000B12 RID: 2834
		[CompilerGenerated]
		private ContextMenuStrip contextMenuStrip_0;
	}
}
