﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;
using ns18;

namespace ns26
{
	// Token: 0x020002CB RID: 715
	[DebuggerNonUserCode]
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
	[CompilerGenerated]
	internal sealed class Class375
	{
		// Token: 0x06001F4E RID: 8014 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class375()
		{
		}

		// Token: 0x170004D6 RID: 1238
		// (get) Token: 0x06001F4F RID: 8015 RVA: 0x000E1EF8 File Offset: 0x000E00F8
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class375.resourceManager_0 == null)
				{
					Class375.resourceManager_0 = new ResourceManager(Class521.smethod_0(86826), typeof(Class375).Assembly);
				}
				return Class375.resourceManager_0;
			}
		}

		// Token: 0x170004D7 RID: 1239
		// (get) Token: 0x06001F50 RID: 8016 RVA: 0x000E1F38 File Offset: 0x000E0138
		// (set) Token: 0x06001F51 RID: 8017 RVA: 0x0000D135 File Offset: 0x0000B335
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class375.cultureInfo_0;
			}
			set
			{
				Class375.cultureInfo_0 = value;
			}
		}

		// Token: 0x170004D8 RID: 1240
		// (get) Token: 0x06001F52 RID: 8018 RVA: 0x000E1F50 File Offset: 0x000E0150
		internal static Bitmap _1683_Lightbulb_32x32
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91217), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004D9 RID: 1241
		// (get) Token: 0x06001F53 RID: 8019 RVA: 0x000E1F80 File Offset: 0x000E0180
		internal static Bitmap _45DgreeDn
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91246), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DA RID: 1242
		// (get) Token: 0x06001F54 RID: 8020 RVA: 0x000E1FB0 File Offset: 0x000E01B0
		internal static Bitmap _45DgreeUp
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91263), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DB RID: 1243
		// (get) Token: 0x06001F55 RID: 8021 RVA: 0x000E1FE0 File Offset: 0x000E01E0
		internal static Bitmap aboutBanner
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91280), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DC RID: 1244
		// (get) Token: 0x06001F56 RID: 8022 RVA: 0x000E2010 File Offset: 0x000E0210
		internal static Bitmap aboutBanner_TExLY
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91297), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DD RID: 1245
		// (get) Token: 0x06001F57 RID: 8023 RVA: 0x000E2040 File Offset: 0x000E0240
		internal static Bitmap add_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91322), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DE RID: 1246
		// (get) Token: 0x06001F58 RID: 8024 RVA: 0x000E2070 File Offset: 0x000E0270
		internal static Bitmap AddFile_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91335), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004DF RID: 1247
		// (get) Token: 0x06001F59 RID: 8025 RVA: 0x000E20A0 File Offset: 0x000E02A0
		internal static Bitmap application_side_expand
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91356), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E0 RID: 1248
		// (get) Token: 0x06001F5A RID: 8026 RVA: 0x000E20D0 File Offset: 0x000E02D0
		internal static Bitmap application_split
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91389), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E1 RID: 1249
		// (get) Token: 0x06001F5B RID: 8027 RVA: 0x000E2100 File Offset: 0x000E0300
		internal static Bitmap Arrow
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91414), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E2 RID: 1250
		// (get) Token: 0x06001F5C RID: 8028 RVA: 0x000E2130 File Offset: 0x000E0330
		internal static Bitmap autolimit
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91423), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E3 RID: 1251
		// (get) Token: 0x06001F5D RID: 8029 RVA: 0x000E2160 File Offset: 0x000E0360
		internal static Bitmap autolimit_gray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91436), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E4 RID: 1252
		// (get) Token: 0x06001F5E RID: 8030 RVA: 0x000E2190 File Offset: 0x000E0390
		internal static Bitmap autostop
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91457), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E5 RID: 1253
		// (get) Token: 0x06001F5F RID: 8031 RVA: 0x000E21C0 File Offset: 0x000E03C0
		internal static Bitmap autostop_gray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91470), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E6 RID: 1254
		// (get) Token: 0x06001F60 RID: 8032 RVA: 0x000E21F0 File Offset: 0x000E03F0
		internal static Bitmap biArrow
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91491), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E7 RID: 1255
		// (get) Token: 0x06001F61 RID: 8033 RVA: 0x000E2220 File Offset: 0x000E0420
		internal static Bitmap BigArrwSamples
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91504), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E8 RID: 1256
		// (get) Token: 0x06001F62 RID: 8034 RVA: 0x000E2250 File Offset: 0x000E0450
		internal static Bitmap BlindTest_48x48
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91525), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004E9 RID: 1257
		// (get) Token: 0x06001F63 RID: 8035 RVA: 0x000E2280 File Offset: 0x000E0480
		internal static Icon BlindTestIcon
		{
			get
			{
				return (Icon)Class375.ResourceManager.GetObject(Class521.smethod_0(22310), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004EA RID: 1258
		// (get) Token: 0x06001F64 RID: 8036 RVA: 0x000E22B0 File Offset: 0x000E04B0
		internal static Bitmap blueline
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91546), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004EB RID: 1259
		// (get) Token: 0x06001F65 RID: 8037 RVA: 0x000E22E0 File Offset: 0x000E04E0
		internal static Bitmap Book_angleHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91559), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004EC RID: 1260
		// (get) Token: 0x06001F66 RID: 8038 RVA: 0x000E2310 File Offset: 0x000E0510
		internal static Bitmap Book_openHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91576), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004ED RID: 1261
		// (get) Token: 0x06001F67 RID: 8039 RVA: 0x000E2340 File Offset: 0x000E0540
		internal static Icon BookIcon
		{
			get
			{
				return (Icon)Class375.ResourceManager.GetObject(Class521.smethod_0(91593), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004EE RID: 1262
		// (get) Token: 0x06001F68 RID: 8040 RVA: 0x000E2370 File Offset: 0x000E0570
		internal static Bitmap BTN_Thumb
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91606), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004EF RID: 1263
		// (get) Token: 0x06001F69 RID: 8041 RVA: 0x000E23A0 File Offset: 0x000E05A0
		internal static Bitmap BTN_Thumb_Blue
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91619), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F0 RID: 1264
		// (get) Token: 0x06001F6A RID: 8042 RVA: 0x000E23D0 File Offset: 0x000E05D0
		internal static Bitmap calendar
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91640), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F1 RID: 1265
		// (get) Token: 0x06001F6B RID: 8043 RVA: 0x000E2400 File Offset: 0x000E0600
		internal static Bitmap calendar_date
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91653), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F2 RID: 1266
		// (get) Token: 0x06001F6C RID: 8044 RVA: 0x000E2430 File Offset: 0x000E0630
		internal static Bitmap calendar_date_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91674), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F3 RID: 1267
		// (get) Token: 0x06001F6D RID: 8045 RVA: 0x000E2460 File Offset: 0x000E0660
		internal static Bitmap Calendar_scheduleHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91703), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F4 RID: 1268
		// (get) Token: 0x06001F6E RID: 8046 RVA: 0x000E2490 File Offset: 0x000E0690
		internal static Bitmap chart
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91732), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F5 RID: 1269
		// (get) Token: 0x06001F6F RID: 8047 RVA: 0x000E24C0 File Offset: 0x000E06C0
		internal static Bitmap check
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91741), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F6 RID: 1270
		// (get) Token: 0x06001F70 RID: 8048 RVA: 0x000E24F0 File Offset: 0x000E06F0
		internal static byte[] chtpg
		{
			get
			{
				return (byte[])Class375.ResourceManager.GetObject(Class521.smethod_0(91750), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F7 RID: 1271
		// (get) Token: 0x06001F71 RID: 8049 RVA: 0x000E2520 File Offset: 0x000E0720
		internal static Bitmap CloseLongArrow_Green
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91759), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F8 RID: 1272
		// (get) Token: 0x06001F72 RID: 8050 RVA: 0x000E2550 File Offset: 0x000E0750
		internal static Bitmap CloseLongArrow_GreenLt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91788), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004F9 RID: 1273
		// (get) Token: 0x06001F73 RID: 8051 RVA: 0x000E2580 File Offset: 0x000E0780
		internal static Bitmap CloseShortArrow_Red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91821), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FA RID: 1274
		// (get) Token: 0x06001F74 RID: 8052 RVA: 0x000E25B0 File Offset: 0x000E07B0
		internal static Bitmap CloseShortArrow_RedLt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91850), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FB RID: 1275
		// (get) Token: 0x06001F75 RID: 8053 RVA: 0x000E25E0 File Offset: 0x000E07E0
		internal static Bitmap CloudStorage
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91879), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FC RID: 1276
		// (get) Token: 0x06001F76 RID: 8054 RVA: 0x000E2610 File Offset: 0x000E0810
		internal static Bitmap computer_accept
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91896), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FD RID: 1277
		// (get) Token: 0x06001F77 RID: 8055 RVA: 0x000E2640 File Offset: 0x000E0840
		internal static Bitmap cross_gray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91917), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FE RID: 1278
		// (get) Token: 0x06001F78 RID: 8056 RVA: 0x000E2670 File Offset: 0x000E0870
		internal static Bitmap cross_lightgray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91934), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170004FF RID: 1279
		// (get) Token: 0x06001F79 RID: 8057 RVA: 0x000E26A0 File Offset: 0x000E08A0
		internal static Bitmap database_down
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91955), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000500 RID: 1280
		// (get) Token: 0x06001F7A RID: 8058 RVA: 0x000E26D0 File Offset: 0x000E08D0
		internal static Bitmap DelDraw
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91976), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000501 RID: 1281
		// (get) Token: 0x06001F7B RID: 8059 RVA: 0x000E2700 File Offset: 0x000E0900
		internal static Bitmap DeleteHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(91989), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000502 RID: 1282
		// (get) Token: 0x06001F7C RID: 8060 RVA: 0x000E2730 File Offset: 0x000E0930
		internal static Bitmap door_in
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92002), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000503 RID: 1283
		// (get) Token: 0x06001F7D RID: 8061 RVA: 0x000E2760 File Offset: 0x000E0960
		internal static Bitmap DrawClsRevOpen
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92015), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000504 RID: 1284
		// (get) Token: 0x06001F7E RID: 8062 RVA: 0x000E2790 File Offset: 0x000E0990
		internal static Bitmap DrawCondClose
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92036), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000505 RID: 1285
		// (get) Token: 0x06001F7F RID: 8063 RVA: 0x000E27C0 File Offset: 0x000E09C0
		internal static Bitmap DrawOdrLineBtnIcon
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92057), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000506 RID: 1286
		// (get) Token: 0x06001F80 RID: 8064 RVA: 0x000E27F0 File Offset: 0x000E09F0
		internal static Bitmap DrawOdrLineBtnIconBlue
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92082), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000507 RID: 1287
		// (get) Token: 0x06001F81 RID: 8065 RVA: 0x000E2820 File Offset: 0x000E0A20
		internal static Bitmap DrawOpenLongOdr
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92115), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000508 RID: 1288
		// (get) Token: 0x06001F82 RID: 8066 RVA: 0x000E2850 File Offset: 0x000E0A50
		internal static Bitmap DrawOpenShrtOdr
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92136), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000509 RID: 1289
		// (get) Token: 0x06001F83 RID: 8067 RVA: 0x000E2880 File Offset: 0x000E0A80
		internal static Bitmap DrawROpen
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92157), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050A RID: 1290
		// (get) Token: 0x06001F84 RID: 8068 RVA: 0x000E28B0 File Offset: 0x000E0AB0
		internal static Bitmap DrawStopLoss
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92170), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050B RID: 1291
		// (get) Token: 0x06001F85 RID: 8069 RVA: 0x000E28E0 File Offset: 0x000E0AE0
		internal static Bitmap DrawStopPrft
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92187), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050C RID: 1292
		// (get) Token: 0x06001F86 RID: 8070 RVA: 0x000E2910 File Offset: 0x000E0B10
		internal static Bitmap EditHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92204), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050D RID: 1293
		// (get) Token: 0x06001F87 RID: 8071 RVA: 0x000E2940 File Offset: 0x000E0B40
		internal static Bitmap Ellipse
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92213), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050E RID: 1294
		// (get) Token: 0x06001F88 RID: 8072 RVA: 0x000E2970 File Offset: 0x000E0B70
		internal static Bitmap expand_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92226), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700050F RID: 1295
		// (get) Token: 0x06001F89 RID: 8073 RVA: 0x000E29A0 File Offset: 0x000E0BA0
		internal static Bitmap expand_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92251), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000510 RID: 1296
		// (get) Token: 0x06001F8A RID: 8074 RVA: 0x000E29D0 File Offset: 0x000E0BD0
		internal static Bitmap fast_forward
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92276), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000511 RID: 1297
		// (get) Token: 0x06001F8B RID: 8075 RVA: 0x000E2A00 File Offset: 0x000E0C00
		internal static Bitmap fast_forward_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92293), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000512 RID: 1298
		// (get) Token: 0x06001F8C RID: 8076 RVA: 0x000E2A30 File Offset: 0x000E0C30
		internal static Bitmap FbLines
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92318), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000513 RID: 1299
		// (get) Token: 0x06001F8D RID: 8077 RVA: 0x000E2A60 File Offset: 0x000E0C60
		internal static Bitmap FbLines_Ext
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92331), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000514 RID: 1300
		// (get) Token: 0x06001F8E RID: 8078 RVA: 0x000E2A90 File Offset: 0x000E0C90
		internal static Bitmap files
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92348), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000515 RID: 1301
		// (get) Token: 0x06001F8F RID: 8079 RVA: 0x000E2AC0 File Offset: 0x000E0CC0
		internal static Bitmap flash
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92357), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000516 RID: 1302
		// (get) Token: 0x06001F90 RID: 8080 RVA: 0x000E2AF0 File Offset: 0x000E0CF0
		internal static Bitmap flash_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92366), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000517 RID: 1303
		// (get) Token: 0x06001F91 RID: 8081 RVA: 0x000E2B20 File Offset: 0x000E0D20
		internal static Bitmap floppy_disc
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92383), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000518 RID: 1304
		// (get) Token: 0x06001F92 RID: 8082 RVA: 0x000E2B50 File Offset: 0x000E0D50
		internal static Bitmap floppy_disc_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92400), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000519 RID: 1305
		// (get) Token: 0x06001F93 RID: 8083 RVA: 0x000E2B80 File Offset: 0x000E0D80
		internal static Bitmap folder_accept
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92425), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051A RID: 1306
		// (get) Token: 0x06001F94 RID: 8084 RVA: 0x000E2BB0 File Offset: 0x000E0DB0
		internal static Bitmap folder_add
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92446), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051B RID: 1307
		// (get) Token: 0x06001F95 RID: 8085 RVA: 0x000E2BE0 File Offset: 0x000E0DE0
		internal static Bitmap folder_up_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92463), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051C RID: 1308
		// (get) Token: 0x06001F96 RID: 8086 RVA: 0x000E2C10 File Offset: 0x000E0E10
		internal static Bitmap FolderClosed
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92484), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051D RID: 1309
		// (get) Token: 0x06001F97 RID: 8087 RVA: 0x000E2C40 File Offset: 0x000E0E40
		internal static Bitmap FolderOpen
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92501), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051E RID: 1310
		// (get) Token: 0x06001F98 RID: 8088 RVA: 0x000E2C70 File Offset: 0x000E0E70
		internal static Bitmap GannFan
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92518), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700051F RID: 1311
		// (get) Token: 0x06001F99 RID: 8089 RVA: 0x000E2CA0 File Offset: 0x000E0EA0
		internal static Bitmap GannLines
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92531), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000520 RID: 1312
		// (get) Token: 0x06001F9A RID: 8090 RVA: 0x000E2CD0 File Offset: 0x000E0ED0
		internal static Bitmap Glass_Face_48
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92544), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000521 RID: 1313
		// (get) Token: 0x06001F9B RID: 8091 RVA: 0x000E2D00 File Offset: 0x000E0F00
		internal static Bitmap GlassFace_18
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92565), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000522 RID: 1314
		// (get) Token: 0x06001F9C RID: 8092 RVA: 0x000E2D30 File Offset: 0x000E0F30
		internal static Bitmap GlassFace_18_BW
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92582), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000523 RID: 1315
		// (get) Token: 0x06001F9D RID: 8093 RVA: 0x000E2D60 File Offset: 0x000E0F60
		internal static Bitmap GoldenRatio
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92603), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000524 RID: 1316
		// (get) Token: 0x06001F9E RID: 8094 RVA: 0x000E2D90 File Offset: 0x000E0F90
		internal static Bitmap GreenArrow_Down
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92620), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000525 RID: 1317
		// (get) Token: 0x06001F9F RID: 8095 RVA: 0x000E2DC0 File Offset: 0x000E0FC0
		internal static Bitmap GreenArrow_LDown
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92641), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000526 RID: 1318
		// (get) Token: 0x06001FA0 RID: 8096 RVA: 0x000E2DF0 File Offset: 0x000E0FF0
		internal static Bitmap GreenArrow_RDown
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92666), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000527 RID: 1319
		// (get) Token: 0x06001FA1 RID: 8097 RVA: 0x000E2E20 File Offset: 0x000E1020
		internal static Bitmap GreenArrow8px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92691), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000528 RID: 1320
		// (get) Token: 0x06001FA2 RID: 8098 RVA: 0x000E2E50 File Offset: 0x000E1050
		internal static Bitmap layout_add
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92712), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000529 RID: 1321
		// (get) Token: 0x06001FA3 RID: 8099 RVA: 0x000E2E80 File Offset: 0x000E1080
		internal static Bitmap Line
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92729), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052A RID: 1322
		// (get) Token: 0x06001FA4 RID: 8100 RVA: 0x000E2EB0 File Offset: 0x000E10B0
		internal static Bitmap line_chart
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92738), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052B RID: 1323
		// (get) Token: 0x06001FA5 RID: 8101 RVA: 0x000E2EE0 File Offset: 0x000E10E0
		internal static Bitmap line_chart_darkblack
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92755), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052C RID: 1324
		// (get) Token: 0x06001FA6 RID: 8102 RVA: 0x000E2F10 File Offset: 0x000E1110
		internal static Bitmap line_chart_lightgray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92784), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052D RID: 1325
		// (get) Token: 0x06001FA7 RID: 8103 RVA: 0x000E2F40 File Offset: 0x000E1140
		internal static Bitmap LineD
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92813), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052E RID: 1326
		// (get) Token: 0x06001FA8 RID: 8104 RVA: 0x000E2F70 File Offset: 0x000E1170
		internal static Bitmap LineDExt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92822), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700052F RID: 1327
		// (get) Token: 0x06001FA9 RID: 8105 RVA: 0x000E2FA0 File Offset: 0x000E11A0
		internal static Bitmap LineDH
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92835), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000530 RID: 1328
		// (get) Token: 0x06001FAA RID: 8106 RVA: 0x000E2FD0 File Offset: 0x000E11D0
		internal static Bitmap LineH
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92844), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000531 RID: 1329
		// (get) Token: 0x06001FAB RID: 8107 RVA: 0x000E3000 File Offset: 0x000E1200
		internal static Bitmap LineHExt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92853), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000532 RID: 1330
		// (get) Token: 0x06001FAC RID: 8108 RVA: 0x000E3030 File Offset: 0x000E1230
		internal static Bitmap LineV
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92866), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000533 RID: 1331
		// (get) Token: 0x06001FAD RID: 8109 RVA: 0x000E3060 File Offset: 0x000E1260
		internal static Bitmap LoginBanner_LY
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92875), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000534 RID: 1332
		// (get) Token: 0x06001FAE RID: 8110 RVA: 0x000E3090 File Offset: 0x000E1290
		internal static Bitmap LoginBannerNew
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92896), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000535 RID: 1333
		// (get) Token: 0x06001FAF RID: 8111 RVA: 0x000E30C0 File Offset: 0x000E12C0
		internal static Bitmap ly_c01_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92917), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000536 RID: 1334
		// (get) Token: 0x06001FB0 RID: 8112 RVA: 0x000E30F0 File Offset: 0x000E12F0
		internal static Bitmap ly_c01_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92946), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000537 RID: 1335
		// (get) Token: 0x06001FB1 RID: 8113 RVA: 0x000E3120 File Offset: 0x000E1320
		internal static Bitmap ly_c01_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(92975), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000538 RID: 1336
		// (get) Token: 0x06001FB2 RID: 8114 RVA: 0x000E3150 File Offset: 0x000E1350
		internal static Bitmap ly_c01_s04_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93004), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000539 RID: 1337
		// (get) Token: 0x06001FB3 RID: 8115 RVA: 0x000E3180 File Offset: 0x000E1380
		internal static Bitmap ly_c02_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93033), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053A RID: 1338
		// (get) Token: 0x06001FB4 RID: 8116 RVA: 0x000E31B0 File Offset: 0x000E13B0
		internal static Bitmap ly_c02_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93062), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053B RID: 1339
		// (get) Token: 0x06001FB5 RID: 8117 RVA: 0x000E31E0 File Offset: 0x000E13E0
		internal static Bitmap ly_c02_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93091), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053C RID: 1340
		// (get) Token: 0x06001FB6 RID: 8118 RVA: 0x000E3210 File Offset: 0x000E1410
		internal static Bitmap ly_c03_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93120), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053D RID: 1341
		// (get) Token: 0x06001FB7 RID: 8119 RVA: 0x000E3240 File Offset: 0x000E1440
		internal static Bitmap ly_c03_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93149), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053E RID: 1342
		// (get) Token: 0x06001FB8 RID: 8120 RVA: 0x000E3270 File Offset: 0x000E1470
		internal static Bitmap ly_c04_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93178), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700053F RID: 1343
		// (get) Token: 0x06001FB9 RID: 8121 RVA: 0x000E32A0 File Offset: 0x000E14A0
		internal static Bitmap ly_c04_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93207), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000540 RID: 1344
		// (get) Token: 0x06001FBA RID: 8122 RVA: 0x000E32D0 File Offset: 0x000E14D0
		internal static Bitmap ly_c04_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93236), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000541 RID: 1345
		// (get) Token: 0x06001FBB RID: 8123 RVA: 0x000E3300 File Offset: 0x000E1500
		internal static Bitmap ly_c05_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93265), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000542 RID: 1346
		// (get) Token: 0x06001FBC RID: 8124 RVA: 0x000E3330 File Offset: 0x000E1530
		internal static Bitmap ly_c05_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93294), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000543 RID: 1347
		// (get) Token: 0x06001FBD RID: 8125 RVA: 0x000E3360 File Offset: 0x000E1560
		internal static Bitmap ly_c05_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93323), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000544 RID: 1348
		// (get) Token: 0x06001FBE RID: 8126 RVA: 0x000E3390 File Offset: 0x000E1590
		internal static Bitmap ly_c06_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93352), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000545 RID: 1349
		// (get) Token: 0x06001FBF RID: 8127 RVA: 0x000E33C0 File Offset: 0x000E15C0
		internal static Bitmap ly_c06_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93381), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000546 RID: 1350
		// (get) Token: 0x06001FC0 RID: 8128 RVA: 0x000E33F0 File Offset: 0x000E15F0
		internal static Bitmap ly_c07_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93410), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000547 RID: 1351
		// (get) Token: 0x06001FC1 RID: 8129 RVA: 0x000E3420 File Offset: 0x000E1620
		internal static Bitmap ly_c08_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93439), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000548 RID: 1352
		// (get) Token: 0x06001FC2 RID: 8130 RVA: 0x000E3450 File Offset: 0x000E1650
		internal static Bitmap ly_c08_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93468), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000549 RID: 1353
		// (get) Token: 0x06001FC3 RID: 8131 RVA: 0x000E3480 File Offset: 0x000E1680
		internal static Bitmap ly_c09_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93497), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054A RID: 1354
		// (get) Token: 0x06001FC4 RID: 8132 RVA: 0x000E34B0 File Offset: 0x000E16B0
		internal static Bitmap ly_c09_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93526), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054B RID: 1355
		// (get) Token: 0x06001FC5 RID: 8133 RVA: 0x000E34E0 File Offset: 0x000E16E0
		internal static Bitmap ly_c09_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93555), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054C RID: 1356
		// (get) Token: 0x06001FC6 RID: 8134 RVA: 0x000E3510 File Offset: 0x000E1710
		internal static Bitmap ly_c10_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93584), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054D RID: 1357
		// (get) Token: 0x06001FC7 RID: 8135 RVA: 0x000E3540 File Offset: 0x000E1740
		internal static Bitmap ly_c10_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93613), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054E RID: 1358
		// (get) Token: 0x06001FC8 RID: 8136 RVA: 0x000E3570 File Offset: 0x000E1770
		internal static Bitmap ly_c10_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93642), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700054F RID: 1359
		// (get) Token: 0x06001FC9 RID: 8137 RVA: 0x000E35A0 File Offset: 0x000E17A0
		internal static Bitmap ly_c11_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93671), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000550 RID: 1360
		// (get) Token: 0x06001FCA RID: 8138 RVA: 0x000E35D0 File Offset: 0x000E17D0
		internal static Bitmap ly_c11_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93700), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000551 RID: 1361
		// (get) Token: 0x06001FCB RID: 8139 RVA: 0x000E3600 File Offset: 0x000E1800
		internal static Bitmap ly_c11_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93729), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000552 RID: 1362
		// (get) Token: 0x06001FCC RID: 8140 RVA: 0x000E3630 File Offset: 0x000E1830
		internal static Bitmap ly_c11_s04_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93758), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000553 RID: 1363
		// (get) Token: 0x06001FCD RID: 8141 RVA: 0x000E3660 File Offset: 0x000E1860
		internal static Bitmap ly_c12_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93787), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000554 RID: 1364
		// (get) Token: 0x06001FCE RID: 8142 RVA: 0x000E3690 File Offset: 0x000E1890
		internal static Bitmap ly_c12_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93816), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000555 RID: 1365
		// (get) Token: 0x06001FCF RID: 8143 RVA: 0x000E36C0 File Offset: 0x000E18C0
		internal static Bitmap max_small
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93845), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000556 RID: 1366
		// (get) Token: 0x06001FD0 RID: 8144 RVA: 0x000E36F0 File Offset: 0x000E18F0
		internal static Bitmap max_small_darkgray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(67338), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000557 RID: 1367
		// (get) Token: 0x06001FD1 RID: 8145 RVA: 0x000E3720 File Offset: 0x000E1920
		internal static Bitmap max_small_gray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93858), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000558 RID: 1368
		// (get) Token: 0x06001FD2 RID: 8146 RVA: 0x000E3750 File Offset: 0x000E1950
		internal static Bitmap max_small_white
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(67363), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000559 RID: 1369
		// (get) Token: 0x06001FD3 RID: 8147 RVA: 0x000E3780 File Offset: 0x000E1980
		internal static Bitmap MeasureObj
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93879), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055A RID: 1370
		// (get) Token: 0x06001FD4 RID: 8148 RVA: 0x000E37B0 File Offset: 0x000E19B0
		internal static Bitmap MousePointer
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93896), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055B RID: 1371
		// (get) Token: 0x06001FD5 RID: 8149 RVA: 0x000E37E0 File Offset: 0x000E19E0
		internal static Bitmap MoveLeft
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93913), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055C RID: 1372
		// (get) Token: 0x06001FD6 RID: 8150 RVA: 0x000E3810 File Offset: 0x000E1A10
		internal static Bitmap MoveRight
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93926), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055D RID: 1373
		// (get) Token: 0x06001FD7 RID: 8151 RVA: 0x000E3840 File Offset: 0x000E1A40
		internal static Bitmap NewBook
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93939), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055E RID: 1374
		// (get) Token: 0x06001FD8 RID: 8152 RVA: 0x000E3870 File Offset: 0x000E1A70
		internal static Bitmap NewFolder
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93952), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700055F RID: 1375
		// (get) Token: 0x06001FD9 RID: 8153 RVA: 0x000E38A0 File Offset: 0x000E1AA0
		internal static Bitmap notebook
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93965), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000560 RID: 1376
		// (get) Token: 0x06001FDA RID: 8154 RVA: 0x000E38D0 File Offset: 0x000E1AD0
		internal static Bitmap openHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93978), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000561 RID: 1377
		// (get) Token: 0x06001FDB RID: 8155 RVA: 0x000E3900 File Offset: 0x000E1B00
		internal static Bitmap OpenLongArrow_Red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(93987), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000562 RID: 1378
		// (get) Token: 0x06001FDC RID: 8156 RVA: 0x000E3930 File Offset: 0x000E1B30
		internal static Bitmap OpenLongArrow_RedLt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94012), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000563 RID: 1379
		// (get) Token: 0x06001FDD RID: 8157 RVA: 0x000E3960 File Offset: 0x000E1B60
		internal static Bitmap OpenShortArrow_Green
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94041), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000564 RID: 1380
		// (get) Token: 0x06001FDE RID: 8158 RVA: 0x000E3990 File Offset: 0x000E1B90
		internal static Bitmap OpenShortArrow_GreenLt
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94070), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000565 RID: 1381
		// (get) Token: 0x06001FDF RID: 8159 RVA: 0x000E39C0 File Offset: 0x000E1BC0
		internal static Bitmap page
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94103), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000566 RID: 1382
		// (get) Token: 0x06001FE0 RID: 8160 RVA: 0x000E39F0 File Offset: 0x000E1BF0
		internal static Bitmap page_four_cht
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94112), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000567 RID: 1383
		// (get) Token: 0x06001FE1 RID: 8161 RVA: 0x000E3A20 File Offset: 0x000E1C20
		internal static Bitmap page_simple_cht
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94133), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000568 RID: 1384
		// (get) Token: 0x06001FE2 RID: 8162 RVA: 0x000E3A50 File Offset: 0x000E1C50
		internal static Bitmap page_symbs
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94154), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000569 RID: 1385
		// (get) Token: 0x06001FE3 RID: 8163 RVA: 0x000E3A80 File Offset: 0x000E1C80
		internal static Bitmap page_three_cht
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94171), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056A RID: 1386
		// (get) Token: 0x06001FE4 RID: 8164 RVA: 0x000E3AB0 File Offset: 0x000E1CB0
		internal static Bitmap page_tick_cht
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94192), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056B RID: 1387
		// (get) Token: 0x06001FE5 RID: 8165 RVA: 0x000E3AE0 File Offset: 0x000E1CE0
		internal static Bitmap page_two_cht
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94213), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056C RID: 1388
		// (get) Token: 0x06001FE6 RID: 8166 RVA: 0x000E3B10 File Offset: 0x000E1D10
		internal static Bitmap ParalLine
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94230), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056D RID: 1389
		// (get) Token: 0x06001FE7 RID: 8167 RVA: 0x000E3B40 File Offset: 0x000E1D40
		internal static Bitmap pause
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94243), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056E RID: 1390
		// (get) Token: 0x06001FE8 RID: 8168 RVA: 0x000E3B70 File Offset: 0x000E1D70
		internal static Bitmap pause_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94252), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700056F RID: 1391
		// (get) Token: 0x06001FE9 RID: 8169 RVA: 0x000E3BA0 File Offset: 0x000E1DA0
		internal static Bitmap pause_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94277), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000570 RID: 1392
		// (get) Token: 0x06001FEA RID: 8170 RVA: 0x000E3BD0 File Offset: 0x000E1DD0
		internal static Bitmap PeriodLines
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94302), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000571 RID: 1393
		// (get) Token: 0x06001FEB RID: 8171 RVA: 0x000E3C00 File Offset: 0x000E1E00
		internal static Bitmap play
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94319), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000572 RID: 1394
		// (get) Token: 0x06001FEC RID: 8172 RVA: 0x000E3C30 File Offset: 0x000E1E30
		internal static Bitmap play_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94328), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000573 RID: 1395
		// (get) Token: 0x06001FED RID: 8173 RVA: 0x000E3C60 File Offset: 0x000E1E60
		internal static Bitmap play_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94349), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000574 RID: 1396
		// (get) Token: 0x06001FEE RID: 8174 RVA: 0x000E3C90 File Offset: 0x000E1E90
		internal static Bitmap process
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94370), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000575 RID: 1397
		// (get) Token: 0x06001FEF RID: 8175 RVA: 0x000E3CC0 File Offset: 0x000E1EC0
		internal static Bitmap processes
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94383), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000576 RID: 1398
		// (get) Token: 0x06001FF0 RID: 8176 RVA: 0x000E3CF0 File Offset: 0x000E1EF0
		internal static Bitmap quesmark
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94396), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000577 RID: 1399
		// (get) Token: 0x06001FF1 RID: 8177 RVA: 0x000E3D20 File Offset: 0x000E1F20
		internal static Bitmap Range
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94409), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000578 RID: 1400
		// (get) Token: 0x06001FF2 RID: 8178 RVA: 0x000E3D50 File Offset: 0x000E1F50
		internal static Bitmap Ratio
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94418), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000579 RID: 1401
		// (get) Token: 0x06001FF3 RID: 8179 RVA: 0x000E3D80 File Offset: 0x000E1F80
		internal static Bitmap RedArrow_LUp
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94427), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057A RID: 1402
		// (get) Token: 0x06001FF4 RID: 8180 RVA: 0x000E3DB0 File Offset: 0x000E1FB0
		internal static Bitmap RedArrow_RUp
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94444), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057B RID: 1403
		// (get) Token: 0x06001FF5 RID: 8181 RVA: 0x000E3DE0 File Offset: 0x000E1FE0
		internal static Bitmap RedArrow_Up
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94461), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057C RID: 1404
		// (get) Token: 0x06001FF6 RID: 8182 RVA: 0x000E3E10 File Offset: 0x000E2010
		internal static Bitmap RedArrow8px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94478), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057D RID: 1405
		// (get) Token: 0x06001FF7 RID: 8183 RVA: 0x000E3E40 File Offset: 0x000E2040
		internal static Bitmap remove_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94495), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057E RID: 1406
		// (get) Token: 0x06001FF8 RID: 8184 RVA: 0x000E3E70 File Offset: 0x000E2070
		internal static Bitmap remove_blue_16x
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94512), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700057F RID: 1407
		// (get) Token: 0x06001FF9 RID: 8185 RVA: 0x000E3EA0 File Offset: 0x000E20A0
		internal static Bitmap remove_gray_16x
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94533), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000580 RID: 1408
		// (get) Token: 0x06001FFA RID: 8186 RVA: 0x000E3ED0 File Offset: 0x000E20D0
		internal static Bitmap RenameHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94554), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000581 RID: 1409
		// (get) Token: 0x06001FFB RID: 8187 RVA: 0x000E3F00 File Offset: 0x000E2100
		internal static Bitmap repeat
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94567), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000582 RID: 1410
		// (get) Token: 0x06001FFC RID: 8188 RVA: 0x000E3F30 File Offset: 0x000E2130
		internal static Bitmap restore_small
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94576), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000583 RID: 1411
		// (get) Token: 0x06001FFD RID: 8189 RVA: 0x000E3F60 File Offset: 0x000E2160
		internal static Bitmap restore_small_darkgray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(67276), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000584 RID: 1412
		// (get) Token: 0x06001FFE RID: 8190 RVA: 0x000E3F90 File Offset: 0x000E2190
		internal static Bitmap restore_small_gray
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94597), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000585 RID: 1413
		// (get) Token: 0x06001FFF RID: 8191 RVA: 0x000E3FC0 File Offset: 0x000E21C0
		internal static Bitmap restore_small_white
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(67309), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000586 RID: 1414
		// (get) Token: 0x06002000 RID: 8192 RVA: 0x000E3FF0 File Offset: 0x000E21F0
		internal static Bitmap rewind
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94622), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000587 RID: 1415
		// (get) Token: 0x06002001 RID: 8193 RVA: 0x000E4020 File Offset: 0x000E2220
		internal static Bitmap rewind_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94631), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000588 RID: 1416
		// (get) Token: 0x06002002 RID: 8194 RVA: 0x000E4050 File Offset: 0x000E2250
		internal static Bitmap saveHS
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94648), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000589 RID: 1417
		// (get) Token: 0x06002003 RID: 8195 RVA: 0x000E4080 File Offset: 0x000E2280
		internal static Bitmap search_16x16
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94657), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058A RID: 1418
		// (get) Token: 0x06002004 RID: 8196 RVA: 0x000E40B0 File Offset: 0x000E22B0
		internal static Bitmap shortcuts24x24
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94674), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058B RID: 1419
		// (get) Token: 0x06002005 RID: 8197 RVA: 0x000E40E0 File Offset: 0x000E22E0
		internal static Bitmap SmallArrwSample
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94695), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058C RID: 1420
		// (get) Token: 0x06002006 RID: 8198 RVA: 0x000E4110 File Offset: 0x000E2310
		internal static Bitmap Square
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94716), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058D RID: 1421
		// (get) Token: 0x06002007 RID: 8199 RVA: 0x000E4140 File Offset: 0x000E2340
		internal static Bitmap stop_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94725), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058E RID: 1422
		// (get) Token: 0x06002008 RID: 8200 RVA: 0x000E4170 File Offset: 0x000E2370
		internal static Bitmap stop_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94746), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700058F RID: 1423
		// (get) Token: 0x06002009 RID: 8201 RVA: 0x000E41A0 File Offset: 0x000E23A0
		internal static Bitmap SupportWxQrCode
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94767), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000590 RID: 1424
		// (get) Token: 0x0600200A RID: 8202 RVA: 0x000E41D0 File Offset: 0x000E23D0
		internal static Icon TExIcoBlue
		{
			get
			{
				return (Icon)Class375.ResourceManager.GetObject(Class521.smethod_0(94788), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000591 RID: 1425
		// (get) Token: 0x0600200B RID: 8203 RVA: 0x000E4200 File Offset: 0x000E2400
		internal static Icon TExIcoRed
		{
			get
			{
				return (Icon)Class375.ResourceManager.GetObject(Class521.smethod_0(94805), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000592 RID: 1426
		// (get) Token: 0x0600200C RID: 8204 RVA: 0x000E4230 File Offset: 0x000E2430
		internal static Bitmap Text
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(4793), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000593 RID: 1427
		// (get) Token: 0x0600200D RID: 8205 RVA: 0x000E4260 File Offset: 0x000E2460
		internal static Bitmap theme_1_1
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94818), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000594 RID: 1428
		// (get) Token: 0x0600200E RID: 8206 RVA: 0x000E4290 File Offset: 0x000E2490
		internal static Bitmap theme_1_2
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94831), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000595 RID: 1429
		// (get) Token: 0x0600200F RID: 8207 RVA: 0x000E42C0 File Offset: 0x000E24C0
		internal static Bitmap theme_1_3
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94844), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000596 RID: 1430
		// (get) Token: 0x06002010 RID: 8208 RVA: 0x000E42F0 File Offset: 0x000E24F0
		internal static Bitmap theme_2_1
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94857), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000597 RID: 1431
		// (get) Token: 0x06002011 RID: 8209 RVA: 0x000E4320 File Offset: 0x000E2520
		internal static Bitmap theme_2_2
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94870), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000598 RID: 1432
		// (get) Token: 0x06002012 RID: 8210 RVA: 0x000E4350 File Offset: 0x000E2550
		internal static Bitmap theme_2_3
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94883), Class375.cultureInfo_0);
			}
		}

		// Token: 0x17000599 RID: 1433
		// (get) Token: 0x06002013 RID: 8211 RVA: 0x000E4380 File Offset: 0x000E2580
		internal static Bitmap theme_3_1
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94896), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059A RID: 1434
		// (get) Token: 0x06002014 RID: 8212 RVA: 0x000E43B0 File Offset: 0x000E25B0
		internal static Bitmap theme_3_2
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94909), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059B RID: 1435
		// (get) Token: 0x06002015 RID: 8213 RVA: 0x000E43E0 File Offset: 0x000E25E0
		internal static Bitmap theme_3_3
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94922), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059C RID: 1436
		// (get) Token: 0x06002016 RID: 8214 RVA: 0x000E4410 File Offset: 0x000E2610
		internal static Bitmap theme_4_1
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94935), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059D RID: 1437
		// (get) Token: 0x06002017 RID: 8215 RVA: 0x000E4440 File Offset: 0x000E2640
		internal static Bitmap theme_4_2
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94948), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059E RID: 1438
		// (get) Token: 0x06002018 RID: 8216 RVA: 0x000E4470 File Offset: 0x000E2670
		internal static Bitmap theme_4_3
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94961), Class375.cultureInfo_0);
			}
		}

		// Token: 0x1700059F RID: 1439
		// (get) Token: 0x06002019 RID: 8217 RVA: 0x000E44A0 File Offset: 0x000E26A0
		internal static Bitmap transparent
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94974), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A0 RID: 1440
		// (get) Token: 0x0600201A RID: 8218 RVA: 0x000E44D0 File Offset: 0x000E26D0
		internal static Bitmap TrendSpeed
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(94991), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A1 RID: 1441
		// (get) Token: 0x0600201B RID: 8219 RVA: 0x000E4500 File Offset: 0x000E2700
		internal static Bitmap volume_dec_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95008), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A2 RID: 1442
		// (get) Token: 0x0600201C RID: 8220 RVA: 0x000E4530 File Offset: 0x000E2730
		internal static Bitmap volume_dec_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95037), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A3 RID: 1443
		// (get) Token: 0x0600201D RID: 8221 RVA: 0x000E4560 File Offset: 0x000E2760
		internal static Bitmap volume_inc_blue1_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95066), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A4 RID: 1444
		// (get) Token: 0x0600201E RID: 8222 RVA: 0x000E4590 File Offset: 0x000E2790
		internal static Bitmap volume_inc_blue2_28px
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95095), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A5 RID: 1445
		// (get) Token: 0x0600201F RID: 8223 RVA: 0x000E45C0 File Offset: 0x000E27C0
		internal static Bitmap WaveRuler
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95124), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A6 RID: 1446
		// (get) Token: 0x06002020 RID: 8224 RVA: 0x000E45F0 File Offset: 0x000E27F0
		internal static Bitmap window_edit
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95137), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A7 RID: 1447
		// (get) Token: 0x06002021 RID: 8225 RVA: 0x000E4620 File Offset: 0x000E2820
		internal static Bitmap window_maximize_blue
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95154), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A8 RID: 1448
		// (get) Token: 0x06002022 RID: 8226 RVA: 0x000E4650 File Offset: 0x000E2850
		internal static Bitmap window_maximize_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(85590), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005A9 RID: 1449
		// (get) Token: 0x06002023 RID: 8227 RVA: 0x000E4680 File Offset: 0x000E2880
		internal static Bitmap window_restore_blue
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95183), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005AA RID: 1450
		// (get) Token: 0x06002024 RID: 8228 RVA: 0x000E46B0 File Offset: 0x000E28B0
		internal static Bitmap window_restore_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(85619), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005AB RID: 1451
		// (get) Token: 0x06002025 RID: 8229 RVA: 0x000E46E0 File Offset: 0x000E28E0
		internal static Bitmap zoomin
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95212), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005AC RID: 1452
		// (get) Token: 0x06002026 RID: 8230 RVA: 0x000E4710 File Offset: 0x000E2910
		internal static Bitmap zoomin_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95221), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005AD RID: 1453
		// (get) Token: 0x06002027 RID: 8231 RVA: 0x000E4740 File Offset: 0x000E2940
		internal static Bitmap zoomout
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95238), Class375.cultureInfo_0);
			}
		}

		// Token: 0x170005AE RID: 1454
		// (get) Token: 0x06002028 RID: 8232 RVA: 0x000E4770 File Offset: 0x000E2970
		internal static Bitmap zoomout_red
		{
			get
			{
				return (Bitmap)Class375.ResourceManager.GetObject(Class521.smethod_0(95251), Class375.cultureInfo_0);
			}
		}

		// Token: 0x04000FB9 RID: 4025
		private static ResourceManager resourceManager_0;

		// Token: 0x04000FBA RID: 4026
		private static CultureInfo cultureInfo_0;
	}
}
