﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns18;
using ns24;
using ns30;
using TEx;
using TEx.Trading;
using TEx.Util;

namespace ns26
{
	// Token: 0x0200016C RID: 364
	internal sealed partial class Form12 : Form
	{
		// Token: 0x06000DD2 RID: 3538 RVA: 0x00059944 File Offset: 0x00057B44
		public Form12(CondOrder condOrder_1) : this(condOrder_1.TransID, condOrder_1.StkSymbol, condOrder_1.OrderType, new int?((int)condOrder_1.ComparisonOpt), condOrder_1.CondPrice, condOrder_1.ExePrice, condOrder_1.Units, condOrder_1.TrailingStopPts, null)
		{
			this.condOrder_0 = Base.Trading.smethod_114(condOrder_1.ID);
			this.Text = Class521.smethod_0(24544);
		}

		// Token: 0x06000DD3 RID: 3539 RVA: 0x000599B0 File Offset: 0x00057BB0
		public Form12(int? nullable_0, StkSymbol stkSymbol_1, OrderType orderType_0, int? nullable_1, decimal decimal_0, decimal decimal_1, decimal decimal_2, decimal? nullable_2, List<Transaction> list_0)
		{
			this.method_12();
			this.StkSymbol = stkSymbol_1;
			this.method_9(this.numericUpDown_1);
			decimal decimal_3 = Convert.ToDecimal(1.0 / Math.Pow(10.0, (double)this.stkSymbol_0.DigitNb));
			this.method_8(this.numericUpDown_0, decimal_3);
			this.method_8(this.class304_0, 0m);
			this.method_3(nullable_1);
			this.method_4(orderType_0);
			this.CondPrice = decimal_0;
			this.ExPrice = decimal_1;
			this.Units = decimal_2;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			this.comboBox_1.SelectedIndexChanged += this.comboBox_1_SelectedIndexChanged;
			this.comboBox_2.SelectedIndexChanged += this.comboBox_2_SelectedIndexChanged;
			this.class304_0.BeforeValueDecrement += this.method_1;
			this.class304_0.BeforeValueIncrement += this.method_0;
			this.class304_0.ValueChanged += this.class304_0_ValueChanged;
			if (decimal_1 == 0m)
			{
				this.class304_0.DecimalPlaces = 0;
			}
			if (!stkSymbol_1.IsFutures)
			{
				this.label_5.Text = Class521.smethod_0(11734);
			}
			decimal num = (stkSymbol_1.LeastPriceVar != null) ? stkSymbol_1.LeastPriceVar.Value : 1m;
			this.numericUpDown_2.Minimum = num;
			this.numericUpDown_2.Maximum = 100000m;
			this.numericUpDown_2.Increment = num;
			this.numericUpDown_2.DecimalPlaces = stkSymbol_1.DigitNb;
			if (nullable_0 != null && nullable_2 == null)
			{
				this.groupBox_2.Enabled = false;
			}
			else if (nullable_0 != null && nullable_2 != null)
			{
				ShownOpenTrans shownOpenTrans = Base.Trading.smethod_145(nullable_0.Value);
				if (shownOpenTrans != null)
				{
					ComboBoxItem item = this.method_5(shownOpenTrans);
					this.comboBox_3.Items.Add(item);
					this.comboBox_3.SelectedIndex = 0;
					this.numericUpDown_2.Value = nullable_2.Value;
					this.checkBox_0.Enabled = true;
					this.checkBox_0.Checked = true;
				}
				this.groupBox_1.Enabled = false;
			}
			else if (list_0 != null && list_0.Any<Transaction>())
			{
				foreach (Transaction transaction_ in list_0)
				{
					ComboBoxItem item2 = this.method_5(transaction_);
					this.comboBox_3.Items.Add(item2);
				}
				this.comboBox_3.SelectedIndex = 0;
				decimal value = num * 100m;
				this.numericUpDown_2.Value = value;
			}
			else
			{
				this.groupBox_2.Enabled = false;
			}
			if (this.checkBox_0.Enabled)
			{
				this.checkBox_0.CheckedChanged += this.checkBox_0_CheckedChanged;
				if (!this.checkBox_0.Checked)
				{
					this.method_7(false);
				}
			}
		}

		// Token: 0x06000DD4 RID: 3540 RVA: 0x00059D08 File Offset: 0x00057F08
		private void method_0(object sender, CancelEventArgs e)
		{
			if (this.class304_0.Value == 0m)
			{
				StkSymbol stkSymbol = this.method_2();
				SymbDataSet symbDataSet = Base.Data.smethod_50(stkSymbol.ID, false, false);
				if (symbDataSet != null)
				{
					decimal d = Convert.ToDecimal(symbDataSet.LastHisData.Close);
					this.class304_0.Value = d + this.class304_0.Increment;
				}
				this.class304_0.DecimalPlaces = stkSymbol.DigitNb;
			}
		}

		// Token: 0x06000DD5 RID: 3541 RVA: 0x00059D84 File Offset: 0x00057F84
		private void method_1(object sender, CancelEventArgs e)
		{
			if (this.class304_0.Value == 0m)
			{
				StkSymbol stkSymbol = this.method_2();
				if (stkSymbol != null)
				{
					this.class304_0.DecimalPlaces = stkSymbol.DigitNb;
				}
				SymbDataSet symbDataSet = Base.Data.smethod_50(stkSymbol.ID, false, false);
				if (symbDataSet != null && symbDataSet.LastHisData != null)
				{
					decimal d = Convert.ToDecimal(symbDataSet.LastHisData.Close);
					this.class304_0.Value = d - this.class304_0.Increment;
				}
			}
		}

		// Token: 0x06000DD6 RID: 3542 RVA: 0x00059E0C File Offset: 0x0005800C
		private void class304_0_ValueChanged(object sender, EventArgs e)
		{
			if (this.class304_0.Value == 0m)
			{
				this.class304_0.DecimalPlaces = 0;
			}
			else
			{
				StkSymbol stkSymbol = this.method_2();
				if (stkSymbol != null && this.class304_0.DecimalPlaces != stkSymbol.DigitNb)
				{
					this.class304_0.DecimalPlaces = stkSymbol.DigitNb;
				}
			}
		}

		// Token: 0x06000DD7 RID: 3543 RVA: 0x00059E70 File Offset: 0x00058070
		private StkSymbol method_2()
		{
			int int_;
			if (this.CondOrder != null)
			{
				int_ = this.CondOrder.SymbID;
			}
			else
			{
				int_ = this.StkSymbol.ID;
			}
			return SymbMgr.smethod_3(int_);
		}

		// Token: 0x06000DD8 RID: 3544 RVA: 0x00006288 File Offset: 0x00004488
		private void method_3(int? nullable_0)
		{
			this.comboBox_0.SelectedIndex = ((nullable_0 != null) ? nullable_0.Value : 0);
		}

		// Token: 0x06000DD9 RID: 3545 RVA: 0x00059EAC File Offset: 0x000580AC
		private void method_4(OrderType orderType_0)
		{
			this.IsLong = (orderType_0 == OrderType.Order_OpenLong || orderType_0 == OrderType.Order_CloseShort);
			if (orderType_0 != OrderType.Order_OpenLong)
			{
				if (orderType_0 != OrderType.Order_OpenShort)
				{
					if (orderType_0 != OrderType.Order_CloseLong)
					{
						if (orderType_0 != OrderType.Order_CloseShort)
						{
							this.comboBox_2.SelectedIndex = 2;
							return;
						}
					}
					this.comboBox_2.SelectedIndex = 1;
					return;
				}
			}
			this.comboBox_2.SelectedIndex = 0;
		}

		// Token: 0x06000DDA RID: 3546 RVA: 0x00059F08 File Offset: 0x00058108
		private ComboBoxItem method_5(Transaction transaction_0)
		{
			string text = this.method_6(transaction_0);
			return new ComboBoxItem
			{
				Text = text,
				Value = transaction_0
			};
		}

		// Token: 0x06000DDB RID: 3547 RVA: 0x00059F34 File Offset: 0x00058134
		private string method_6(Transaction transaction_0)
		{
			StkSymbol stkSymbol = SymbMgr.smethod_3(transaction_0.SymbolID);
			return string.Concat(new string[]
			{
				Base.Trading.smethod_149((Enum17)transaction_0.TransType).Replace(Class521.smethod_0(11757), Class521.smethod_0(24565)),
				Class521.smethod_0(24570),
				transaction_0.OpenUnits.ToString(),
				stkSymbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734),
				Class521.smethod_0(24570),
				(transaction_0.Price / 1.0000000000000000000m).ToString()
			});
		}

		// Token: 0x06000DDC RID: 3548 RVA: 0x000062AA File Offset: 0x000044AA
		private void method_7(bool bool_0)
		{
			this.label_6.Enabled = bool_0;
			this.numericUpDown_2.Enabled = bool_0;
			this.comboBox_3.Enabled = bool_0;
			this.label_7.Enabled = bool_0;
		}

		// Token: 0x06000DDD RID: 3549 RVA: 0x0005A000 File Offset: 0x00058200
		private void checkBox_0_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_0.Checked)
			{
				this.groupBox_1.Enabled = false;
				this.method_7(true);
			}
			else
			{
				this.groupBox_1.Enabled = true;
				this.method_7(false);
				if (this.CondOrder != null)
				{
					this.method_3(new int?((int)this.CondOrder.ComparisonOpt));
					this.method_4(this.CondOrder.OrderType);
					this.CondPrice = this.CondOrder.CondPrice;
					this.ExPrice = this.CondPrice;
					this.Units = this.CondOrder.Units;
				}
			}
		}

		// Token: 0x06000DDE RID: 3550 RVA: 0x00059170 File Offset: 0x00057370
		private void Form12_Load(object sender, EventArgs e)
		{
			if (base.Owner != null)
			{
				base.Location = new Point(base.Owner.Location.X + base.Owner.Width / 2 - base.Width / 2, base.Owner.Location.Y + base.Owner.Height / 2 - base.Height / 2);
			}
		}

		// Token: 0x06000DDF RID: 3551 RVA: 0x0005A0A4 File Offset: 0x000582A4
		private void method_8(NumericUpDown numericUpDown_3, decimal decimal_0)
		{
			numericUpDown_3.Maximum = 9999999m;
			numericUpDown_3.Minimum = decimal_0;
			numericUpDown_3.DecimalPlaces = this.stkSymbol_0.DigitNb;
			numericUpDown_3.Increment = this.stkSymbol_0.LeastPriceVar.Value;
		}

		// Token: 0x06000DE0 RID: 3552 RVA: 0x00006232 File Offset: 0x00004432
		private void method_9(NumericUpDown numericUpDown_3)
		{
			numericUpDown_3.Increment = 1m;
			numericUpDown_3.Maximum = 9999999m;
			numericUpDown_3.Minimum = 1m;
		}

		// Token: 0x06000DE1 RID: 3553 RVA: 0x000062DE File Offset: 0x000044DE
		private void comboBox_1_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_1.SelectedIndex == 0)
			{
				this.IsLong = true;
			}
			else
			{
				this.IsLong = false;
			}
		}

		// Token: 0x06000DE2 RID: 3554 RVA: 0x000041B9 File Offset: 0x000023B9
		private void comboBox_2_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000DE3 RID: 3555 RVA: 0x0005A0F4 File Offset: 0x000582F4
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.checkBox_0.Enabled && this.checkBox_0.Checked)
			{
				if (this.condOrder_0 == null)
				{
					Transaction transaction = (this.comboBox_3.SelectedItem as ComboBoxItem).Value as Transaction;
					decimal value = this.numericUpDown_2.Value;
					long value2 = (transaction.OpenUnits != null) ? transaction.OpenUnits.Value : transaction.Units;
					Base.Trading.smethod_85(transaction, null, new decimal?(value), value2);
				}
				else
				{
					Base.Trading.smethod_93(this.condOrder_0.ID, null, null, new decimal?(this.numericUpDown_2.Value), this.condOrder_0.Units, true);
				}
				base.Close();
			}
			else
			{
				Form12.Class205 @class = new Form12.Class205();
				@class.condOrder_0 = new CondOrder();
				@class.condOrder_0.AcctID = Base.Acct.CurrAccount.ID;
				@class.condOrder_0.SymbID = ((this.CondOrder == null) ? this.StkSymbol.ID : this.CondOrder.SymbID);
				@class.condOrder_0.CreateTime = DateTime.Now;
				@class.condOrder_0.ComparisonOpt = this.method_10();
				@class.condOrder_0.CondPrice = this.CondPrice;
				@class.condOrder_0.ExePrice = this.ExPrice;
				@class.condOrder_0.OrderType = this.method_11();
				@class.condOrder_0.Units = this.Units;
				@class.condOrder_0.OrderStatus = OrderStatus.Active;
				if (!Base.Data.smethod_124(SymbMgr.smethod_3(@class.condOrder_0.SymbID)) && (@class.condOrder_0.OrderType == OrderType.Order_OpenShort || @class.condOrder_0.OrderType == OrderType.Order_CloseLongRevOpen))
				{
					MessageBox.Show(Class521.smethod_0(24575), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				else
				{
					int selectedIndex = this.comboBox_2.SelectedIndex;
					if (selectedIndex > 0 && Base.Trading.CurrOpenTransList != null)
					{
						Form12.Class206 class2 = new Form12.Class206();
						class2.class205_0 = @class;
						class2.enum17_0 = Base.Trading.smethod_175(class2.class205_0.condOrder_0.OrderType);
						if (!Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(class2.method_0)).Any<ShownOpenTrans>() && MessageBox.Show(Class521.smethod_0(24680) + ((selectedIndex == 1) ? Class521.smethod_0(1449) : Class521.smethod_0(24753)) + Class521.smethod_0(24762), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
						{
							return;
						}
					}
					bool flag = false;
					SymbDataSet symbDataSet = Base.Data.smethod_50(this.StkSymbol.ID, false, false);
					if (symbDataSet != null)
					{
						decimal d = Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close);
						if (@class.condOrder_0.SymbID == this.StkSymbol.ID && ((@class.condOrder_0.ComparisonOpt == ComparisonOpt.Less && @class.condOrder_0.CondPrice > d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.LessOrEqual && @class.condOrder_0.CondPrice >= d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.Bigger && @class.condOrder_0.CondPrice < d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.BiggerOrEqual && @class.condOrder_0.CondPrice <= d)))
						{
							flag = true;
						}
					}
					if (flag)
					{
						if (MessageBox.Show(Class521.smethod_0(24783), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							if (this.CondOrder != null)
							{
								Base.Trading.smethod_94(this.CondOrder.ID, OrderStatus.Executed);
							}
							Base.Trading.smethod_199(@class.condOrder_0.SymbID, @class.condOrder_0.OrderType, (long)Convert.ToInt32(@class.condOrder_0.Units), new decimal?(@class.condOrder_0.ExePrice));
							base.Close();
						}
					}
					else
					{
						if (this.CondOrder != null)
						{
							Base.Trading.smethod_91(this.CondOrder.ID, @class.condOrder_0.ComparisonOpt, @class.condOrder_0.CondPrice, @class.condOrder_0.ExePrice, @class.condOrder_0.OrderType, @class.condOrder_0.Units, null);
						}
						else
						{
							Base.Trading.smethod_86(this.StkSymbol.ID, this.ComparisonOpt, this.CondPrice, this.ExPrice, this.OrderType, this.Units);
						}
						base.Close();
					}
				}
			}
		}

		// Token: 0x06000DE4 RID: 3556 RVA: 0x00004273 File Offset: 0x00002473
		public void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x1700022B RID: 555
		// (get) Token: 0x06000DE5 RID: 3557 RVA: 0x0005A5C8 File Offset: 0x000587C8
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				return this.method_10();
			}
		}

		// Token: 0x06000DE6 RID: 3558 RVA: 0x0005A5E0 File Offset: 0x000587E0
		private ComparisonOpt method_10()
		{
			int selectedIndex = this.comboBox_0.SelectedIndex;
			ComparisonOpt result;
			if (selectedIndex == 0)
			{
				result = ComparisonOpt.Bigger;
			}
			else if (selectedIndex == 1)
			{
				result = ComparisonOpt.BiggerOrEqual;
			}
			else if (selectedIndex == 2)
			{
				result = ComparisonOpt.Less;
			}
			else
			{
				result = ComparisonOpt.LessOrEqual;
			}
			return result;
		}

		// Token: 0x1700022C RID: 556
		// (get) Token: 0x06000DE7 RID: 3559 RVA: 0x0005A618 File Offset: 0x00058818
		public OrderType OrderType
		{
			get
			{
				return this.method_11();
			}
		}

		// Token: 0x06000DE8 RID: 3560 RVA: 0x0005A630 File Offset: 0x00058830
		private OrderType method_11()
		{
			int selectedIndex = this.comboBox_2.SelectedIndex;
			OrderType result;
			if (this.IsLong)
			{
				if (selectedIndex == 0)
				{
					result = OrderType.Order_OpenLong;
				}
				else if (selectedIndex == 1)
				{
					result = OrderType.Order_CloseShort;
				}
				else
				{
					result = OrderType.Order_CloseShortRevOpen;
				}
			}
			else if (selectedIndex == 0)
			{
				result = OrderType.Order_OpenShort;
			}
			else if (selectedIndex == 1)
			{
				result = OrderType.Order_CloseLong;
			}
			else
			{
				result = OrderType.Order_CloseLongRevOpen;
			}
			return result;
		}

		// Token: 0x1700022D RID: 557
		// (get) Token: 0x06000DE9 RID: 3561 RVA: 0x0005A678 File Offset: 0x00058878
		// (set) Token: 0x06000DEA RID: 3562 RVA: 0x000062FF File Offset: 0x000044FF
		public bool IsLong
		{
			get
			{
				return this.comboBox_1.SelectedIndex == 0;
			}
			set
			{
				if (value)
				{
					this.comboBox_1.SelectedIndex = 0;
				}
				else
				{
					this.comboBox_1.SelectedIndex = 1;
				}
			}
		}

		// Token: 0x1700022E RID: 558
		// (get) Token: 0x06000DEB RID: 3563 RVA: 0x0005A698 File Offset: 0x00058898
		// (set) Token: 0x06000DEC RID: 3564 RVA: 0x0005A6B0 File Offset: 0x000588B0
		public StkSymbol StkSymbol
		{
			get
			{
				return this.stkSymbol_0;
			}
			set
			{
				this.stkSymbol_0 = value;
				if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
				{
					this.label_4.Text = Class521.smethod_0(24382);
				}
				else
				{
					this.label_4.Text = this.stkSymbol_0.CNName + Class521.smethod_0(24872) + this.stkSymbol_0.Code + Class521.smethod_0(5046);
				}
			}
		}

		// Token: 0x1700022F RID: 559
		// (get) Token: 0x06000DED RID: 3565 RVA: 0x0005A730 File Offset: 0x00058930
		// (set) Token: 0x06000DEE RID: 3566 RVA: 0x00006320 File Offset: 0x00004520
		public decimal CondPrice
		{
			get
			{
				decimal result;
				if (this.numericUpDown_0.Enabled)
				{
					result = this.numericUpDown_0.Value;
				}
				else
				{
					result = 0m;
				}
				return result;
			}
			set
			{
				if (value > 0m)
				{
					this.numericUpDown_0.Enabled = true;
					this.numericUpDown_0.Value = value;
				}
				else
				{
					this.numericUpDown_0.Enabled = false;
				}
			}
		}

		// Token: 0x17000230 RID: 560
		// (get) Token: 0x06000DEF RID: 3567 RVA: 0x0005A764 File Offset: 0x00058964
		// (set) Token: 0x06000DF0 RID: 3568 RVA: 0x00006357 File Offset: 0x00004557
		public decimal ExPrice
		{
			get
			{
				return this.class304_0.Value;
			}
			set
			{
				if (value >= 0m)
				{
					this.class304_0.Enabled = true;
					this.class304_0.Value = value;
				}
				else
				{
					this.class304_0.Enabled = false;
				}
			}
		}

		// Token: 0x17000231 RID: 561
		// (get) Token: 0x06000DF1 RID: 3569 RVA: 0x0005A780 File Offset: 0x00058980
		// (set) Token: 0x06000DF2 RID: 3570 RVA: 0x0000638E File Offset: 0x0000458E
		public decimal Units
		{
			get
			{
				return this.numericUpDown_1.Value;
			}
			set
			{
				if (value > 0m)
				{
					this.numericUpDown_1.Value = value;
				}
			}
		}

		// Token: 0x17000232 RID: 562
		// (get) Token: 0x06000DF3 RID: 3571 RVA: 0x0005A79C File Offset: 0x0005899C
		// (set) Token: 0x06000DF4 RID: 3572 RVA: 0x000063AB File Offset: 0x000045AB
		public CondOrder CondOrder
		{
			get
			{
				return this.condOrder_0;
			}
			set
			{
				this.condOrder_0 = value;
			}
		}

		// Token: 0x06000DF5 RID: 3573 RVA: 0x000063B6 File Offset: 0x000045B6
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000DF6 RID: 3574 RVA: 0x0005A7B4 File Offset: 0x000589B4
		private void method_12()
		{
			this.comboBox_0 = new ComboBox();
			this.label_0 = new Label();
			this.numericUpDown_0 = new NumericUpDown();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.comboBox_1 = new ComboBox();
			this.groupBox_0 = new GroupBox();
			this.label_4 = new Label();
			this.comboBox_2 = new ComboBox();
			this.numericUpDown_1 = new NumericUpDown();
			this.label_5 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.label_6 = new Label();
			this.numericUpDown_2 = new NumericUpDown();
			this.checkBox_0 = new CheckBox();
			this.groupBox_1 = new GroupBox();
			this.label_9 = new Label();
			this.label_8 = new Label();
			this.class304_0 = new Class304();
			this.groupBox_2 = new GroupBox();
			this.label_7 = new Label();
			this.comboBox_3 = new ComboBox();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			((ISupportInitialize)this.numericUpDown_1).BeginInit();
			((ISupportInitialize)this.numericUpDown_2).BeginInit();
			this.groupBox_1.SuspendLayout();
			((ISupportInitialize)this.class304_0).BeginInit();
			this.groupBox_2.SuspendLayout();
			base.SuspendLayout();
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Items.AddRange(new object[]
			{
				Class521.smethod_0(24877),
				Class521.smethod_0(24882),
				Class521.smethod_0(24887),
				Class521.smethod_0(24892)
			});
			this.comboBox_0.Location = new Point(333, 33);
			this.comboBox_0.Name = Class521.smethod_0(24897);
			this.comboBox_0.Size = new Size(65, 23);
			this.comboBox_0.TabIndex = 1;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(256, 37);
			this.label_0.Name = Class521.smethod_0(5827);
			this.label_0.Size = new Size(67, 15);
			this.label_0.TabIndex = 2;
			this.label_0.Text = Class521.smethod_0(24922);
			this.numericUpDown_0.Location = new Point(410, 32);
			this.numericUpDown_0.Name = Class521.smethod_0(24939);
			this.numericUpDown_0.Size = new Size(96, 25);
			this.numericUpDown_0.TabIndex = 3;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(512, 37);
			this.label_1.Name = Class521.smethod_0(5849);
			this.label_1.Size = new Size(22, 15);
			this.label_1.TabIndex = 4;
			this.label_1.Text = Class521.smethod_0(24972);
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(22, 93);
			this.label_2.Name = Class521.smethod_0(7019);
			this.label_2.Size = new Size(22, 15);
			this.label_2.TabIndex = 5;
			this.label_2.Text = Class521.smethod_0(24977);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(157, 93);
			this.label_3.Name = Class521.smethod_0(5893);
			this.label_3.Size = new Size(37, 15);
			this.label_3.TabIndex = 7;
			this.label_3.Text = Class521.smethod_0(24982);
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Items.AddRange(new object[]
			{
				Class521.smethod_0(24991),
				Class521.smethod_0(25000)
			});
			this.comboBox_1.Location = new Point(257, 90);
			this.comboBox_1.Name = Class521.smethod_0(25009);
			this.comboBox_1.Size = new Size(64, 23);
			this.comboBox_1.TabIndex = 8;
			this.groupBox_0.Location = new Point(21, 73);
			this.groupBox_0.Name = Class521.smethod_0(25034);
			this.groupBox_0.Size = new Size(508, 2);
			this.groupBox_0.TabIndex = 9;
			this.groupBox_0.TabStop = false;
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(73, 37);
			this.label_4.Name = Class521.smethod_0(24391);
			this.label_4.Size = new Size(55, 15);
			this.label_4.TabIndex = 10;
			this.label_4.Text = Class521.smethod_0(25055);
			this.label_4.TextAlign = ContentAlignment.MiddleLeft;
			this.comboBox_2.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_2.FormattingEnabled = true;
			this.comboBox_2.Items.AddRange(new object[]
			{
				Class521.smethod_0(25064),
				Class521.smethod_0(25073),
				Class521.smethod_0(24753)
			});
			this.comboBox_2.Location = new Point(333, 90);
			this.comboBox_2.Name = Class521.smethod_0(25082);
			this.comboBox_2.Size = new Size(64, 23);
			this.comboBox_2.TabIndex = 11;
			this.numericUpDown_1.Location = new Point(409, 89);
			this.numericUpDown_1.Name = Class521.smethod_0(16172);
			this.numericUpDown_1.Size = new Size(97, 25);
			this.numericUpDown_1.TabIndex = 12;
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(511, 94);
			this.label_5.Name = Class521.smethod_0(25111);
			this.label_5.Size = new Size(22, 15);
			this.label_5.TabIndex = 13;
			this.label_5.Text = Class521.smethod_0(11739);
			this.button_0.DialogResult = DialogResult.Cancel;
			this.button_0.Location = new Point(475, 288);
			this.button_0.Name = Class521.smethod_0(7421);
			this.button_0.Size = new Size(106, 30);
			this.button_0.TabIndex = 15;
			this.button_0.Text = Class521.smethod_0(5783);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_1.Location = new Point(357, 288);
			this.button_1.Name = Class521.smethod_0(7442);
			this.button_1.Size = new Size(106, 30);
			this.button_1.TabIndex = 14;
			this.button_1.Text = Class521.smethod_0(5801);
			this.button_1.UseVisualStyleBackColor = true;
			this.label_6.Location = new Point(124, 33);
			this.label_6.Name = Class521.smethod_0(25136);
			this.label_6.Size = new Size(47, 23);
			this.label_6.TabIndex = 26;
			this.label_6.Text = Class521.smethod_0(25161);
			this.label_6.TextAlign = ContentAlignment.MiddleRight;
			this.numericUpDown_2.Location = new Point(178, 33);
			this.numericUpDown_2.Name = Class521.smethod_0(25170);
			this.numericUpDown_2.Size = new Size(76, 25);
			this.numericUpDown_2.TabIndex = 25;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(24, 36);
			this.checkBox_0.Name = Class521.smethod_0(25211);
			this.checkBox_0.Size = new Size(89, 19);
			this.checkBox_0.TabIndex = 27;
			this.checkBox_0.Text = Class521.smethod_0(25240);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.groupBox_1.Controls.Add(this.label_9);
			this.groupBox_1.Controls.Add(this.label_8);
			this.groupBox_1.Controls.Add(this.comboBox_2);
			this.groupBox_1.Controls.Add(this.comboBox_0);
			this.groupBox_1.Controls.Add(this.label_2);
			this.groupBox_1.Controls.Add(this.label_4);
			this.groupBox_1.Controls.Add(this.class304_0);
			this.groupBox_1.Controls.Add(this.label_0);
			this.groupBox_1.Controls.Add(this.label_5);
			this.groupBox_1.Controls.Add(this.comboBox_1);
			this.groupBox_1.Controls.Add(this.groupBox_0);
			this.groupBox_1.Controls.Add(this.numericUpDown_0);
			this.groupBox_1.Controls.Add(this.label_3);
			this.groupBox_1.Controls.Add(this.numericUpDown_1);
			this.groupBox_1.Controls.Add(this.label_1);
			this.groupBox_1.Location = new Point(28, 21);
			this.groupBox_1.Name = Class521.smethod_0(25257);
			this.groupBox_1.Size = new Size(553, 150);
			this.groupBox_1.TabIndex = 18;
			this.groupBox_1.TabStop = false;
			this.label_9.AutoSize = true;
			this.label_9.Location = new Point(21, 37);
			this.label_9.Name = Class521.smethod_0(5915);
			this.label_9.Size = new Size(45, 15);
			this.label_9.TabIndex = 15;
			this.label_9.Text = Class521.smethod_0(4587);
			this.label_8.AutoSize = true;
			this.label_8.Location = new Point(43, 117);
			this.label_8.Name = Class521.smethod_0(5871);
			this.label_8.Size = new Size(105, 15);
			this.label_8.TabIndex = 14;
			this.label_8.Text = Class521.smethod_0(25278);
			this.class304_0.Location = new Point(51, 88);
			this.class304_0.Name = Class521.smethod_0(25307);
			this.class304_0.Size = new Size(97, 25);
			this.class304_0.TabIndex = 6;
			this.groupBox_2.Controls.Add(this.label_7);
			this.groupBox_2.Controls.Add(this.checkBox_0);
			this.groupBox_2.Controls.Add(this.label_6);
			this.groupBox_2.Controls.Add(this.comboBox_3);
			this.groupBox_2.Controls.Add(this.numericUpDown_2);
			this.groupBox_2.Location = new Point(28, 178);
			this.groupBox_2.Name = Class521.smethod_0(25336);
			this.groupBox_2.Size = new Size(553, 80);
			this.groupBox_2.TabIndex = 19;
			this.groupBox_2.TabStop = false;
			this.label_7.AutoSize = true;
			this.label_7.Location = new Point(260, 37);
			this.label_7.Name = Class521.smethod_0(25365);
			this.label_7.Size = new Size(67, 15);
			this.label_7.TabIndex = 29;
			this.label_7.Text = Class521.smethod_0(25382);
			this.comboBox_3.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_3.FormattingEnabled = true;
			this.comboBox_3.Location = new Point(333, 33);
			this.comboBox_3.Name = Class521.smethod_0(25399);
			this.comboBox_3.Size = new Size(196, 23);
			this.comboBox_3.TabIndex = 28;
			base.AcceptButton = this.button_1;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_0;
			base.ClientSize = new Size(609, 330);
			base.Controls.Add(this.groupBox_2);
			base.Controls.Add(this.groupBox_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.button_1);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(25424);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(25445);
			base.Load += this.Form12_Load;
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			((ISupportInitialize)this.numericUpDown_1).EndInit();
			((ISupportInitialize)this.numericUpDown_2).EndInit();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			((ISupportInitialize)this.class304_0).EndInit();
			this.groupBox_2.ResumeLayout(false);
			this.groupBox_2.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x0400071D RID: 1821
		private StkSymbol stkSymbol_0;

		// Token: 0x0400071E RID: 1822
		private CondOrder condOrder_0;

		// Token: 0x0400071F RID: 1823
		private IContainer icontainer_0;

		// Token: 0x04000720 RID: 1824
		private ComboBox comboBox_0;

		// Token: 0x04000721 RID: 1825
		private Label label_0;

		// Token: 0x04000722 RID: 1826
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000723 RID: 1827
		private Label label_1;

		// Token: 0x04000724 RID: 1828
		private Label label_2;

		// Token: 0x04000725 RID: 1829
		private Class304 class304_0;

		// Token: 0x04000726 RID: 1830
		private Label label_3;

		// Token: 0x04000727 RID: 1831
		private ComboBox comboBox_1;

		// Token: 0x04000728 RID: 1832
		private GroupBox groupBox_0;

		// Token: 0x04000729 RID: 1833
		private Label label_4;

		// Token: 0x0400072A RID: 1834
		private ComboBox comboBox_2;

		// Token: 0x0400072B RID: 1835
		private NumericUpDown numericUpDown_1;

		// Token: 0x0400072C RID: 1836
		private Label label_5;

		// Token: 0x0400072D RID: 1837
		private Button button_0;

		// Token: 0x0400072E RID: 1838
		private Button button_1;

		// Token: 0x0400072F RID: 1839
		private Label label_6;

		// Token: 0x04000730 RID: 1840
		private NumericUpDown numericUpDown_2;

		// Token: 0x04000731 RID: 1841
		private CheckBox checkBox_0;

		// Token: 0x04000732 RID: 1842
		private GroupBox groupBox_1;

		// Token: 0x04000733 RID: 1843
		private GroupBox groupBox_2;

		// Token: 0x04000734 RID: 1844
		private Label label_7;

		// Token: 0x04000735 RID: 1845
		private ComboBox comboBox_3;

		// Token: 0x04000736 RID: 1846
		private Label label_8;

		// Token: 0x04000737 RID: 1847
		private Label label_9;

		// Token: 0x0200016D RID: 365
		[CompilerGenerated]
		private sealed class Class205
		{
			// Token: 0x04000738 RID: 1848
			public CondOrder condOrder_0;
		}

		// Token: 0x0200016E RID: 366
		[CompilerGenerated]
		private sealed class Class206
		{
			// Token: 0x06000DF9 RID: 3577 RVA: 0x0005B64C File Offset: 0x0005984C
			internal bool method_0(ShownOpenTrans shownOpenTrans_0)
			{
				bool result;
				if (shownOpenTrans_0.SymbolID == this.class205_0.condOrder_0.SymbID)
				{
					result = (shownOpenTrans_0.TransType == (int)this.enum17_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000739 RID: 1849
			public Enum17 enum17_0;

			// Token: 0x0400073A RID: 1850
			public Form12.Class205 class205_0;
		}
	}
}
