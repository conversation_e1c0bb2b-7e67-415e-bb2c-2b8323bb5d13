﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Controls;
using DevComponents.Editors;
using ns0;
using ns10;
using ns18;
using ns26;
using ns3;
using ns31;
using ns8;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001AB RID: 427
	public sealed partial class ImportTransForm : Form, Interface2, Interface3
	{
		// Token: 0x06001089 RID: 4233 RVA: 0x00074CA8 File Offset: 0x00072EA8
		public ImportTransForm(string filePath)
		{
			this.method_8();
			this.string_0 = filePath;
			this.label_2.Text = this.string_0;
			this.label_4.Text = Class521.smethod_0(44127);
			base.Load += this.ImportTransForm_Load;
			this.class288_0.RowHeadersWidth = 60;
		}

		// Token: 0x0600108A RID: 4234 RVA: 0x00074D10 File Offset: 0x00072F10
		private void ImportTransForm_Load(object sender, EventArgs e)
		{
			Base.UI.smethod_177(Class521.smethod_0(44148), this.method_0());
			this.method_4();
			this.button_0.Click += this.button_0_Click;
			this.transFileImporter_0 = new TransFileImporter(this, DateTime.Now);
			string text = string.Empty;
			try
			{
				text = this.method_2(this.string_0);
			}
			catch (Exception ex)
			{
				Base.UI.smethod_178();
				MessageBox.Show(ex.Message, Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				base.Close();
				return;
			}
			Base.UI.smethod_178();
			base.Activate();
			if (text == Class521.smethod_0(9965))
			{
				this.Text = Class521.smethod_0(20671);
				this.label_4.Text = this.transFileImporter_0.BackData.Data.Count.ToString();
			}
			else
			{
				MessageBox.Show(text, Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				base.Close();
			}
		}

		// Token: 0x0600108B RID: 4235 RVA: 0x0002A388 File Offset: 0x00028588
		private Point? method_0()
		{
			Point? result = null;
			if (base.Visible)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x0600108C RID: 4236 RVA: 0x00007158 File Offset: 0x00005358
		private void method_1(object sender, DataGridViewDataErrorEventArgs e)
		{
			MessageBox.Show(Class521.smethod_0(44177) + e.Exception.Message, Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			base.Close();
		}

		// Token: 0x0600108D RID: 4237 RVA: 0x00074E20 File Offset: 0x00073020
		private string method_2(string string_1)
		{
			this.class288_0.DataSource = null;
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(44206);
			string text = this.transFileImporter_0.method_9(string_1);
			if (text == Class521.smethod_0(9965))
			{
				try
				{
					this.class288_0.DataSource = this.transFileImporter_0.BackData.Data;
				}
				catch (Exception ex)
				{
					MessageBox.Show(ex.Message);
				}
				this.method_3();
			}
			return text;
		}

		// Token: 0x0600108E RID: 4238 RVA: 0x00074EB4 File Offset: 0x000730B4
		private void method_3()
		{
			for (int i = 0; i < this.class288_0.Rows.Count; i++)
			{
				DataGridViewRow dataGridViewRow = this.class288_0.Rows[i];
				dataGridViewRow.HeaderCell.Value = string.Format(Class521.smethod_0(44271), dataGridViewRow.Index + 1);
			}
		}

		// Token: 0x0600108F RID: 4239 RVA: 0x00074F18 File Offset: 0x00073118
		private void method_4()
		{
			List<Account> list = Base.Acct.Accounts.Where(new Func<Account, bool>(ImportTransForm.<>c.<>9.method_0)).ToList<Account>();
			int selectedIndex = list.FindIndex(new Predicate<Account>(ImportTransForm.<>c.<>9.method_1));
			foreach (Account account in list)
			{
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = account.AcctName;
				comboBoxItem.Value = account.ID;
				this.comboBox_0.Items.Add(comboBoxItem);
			}
			this.comboBox_0.SelectedIndex = selectedIndex;
		}

		// Token: 0x06001090 RID: 4240 RVA: 0x0000718F File Offset: 0x0000538F
		public void imethod_0(string string_1, int int_0)
		{
			this.toolStripStatusLabel_1.Text = string_1;
		}

		// Token: 0x06001091 RID: 4241 RVA: 0x00074FF8 File Offset: 0x000731F8
		private void button_0_Click(object sender, EventArgs e)
		{
			this.class288_0.EndEdit();
			if (this.transFileImporter_0.BackData.Data.Count > 0)
			{
				if (this.comboBox_0.Text == Class521.smethod_0(1449))
				{
					this.toolStripStatusLabel_1.Text = Class521.smethod_0(44276);
				}
				else
				{
					this.toolStripStatusLabel_1.Text = Class521.smethod_0(44305);
					try
					{
						this.button_0.Enabled = false;
						string text;
						if (!this.transFileImporter_0.method_11(out text))
						{
							this.toolStripStatusLabel_1.Text = text;
							this.button_0.Enabled = true;
						}
						else
						{
							for (int i = 0; i < this.transFileImporter_0.BackData.Data.Count; i++)
							{
								if (this.transFileImporter_0.BackData.Data[i].CloseID != null)
								{
									int num = Convert.ToInt32(this.transFileImporter_0.BackData.Data[i].CloseID);
									num--;
									this.transFileImporter_0.BackData.Data[i].method_0(num.ToString());
								}
							}
							TEx.Util.ComboBoxItem comboBoxItem = this.comboBox_0.SelectedItem as TEx.Util.ComboBoxItem;
							TransFileImporter.smethod_1(this.transFileImporter_0.BackData, Convert.ToInt32(comboBoxItem.Value));
							this.button_0.Enabled = true;
							this.toolStripStatusLabel_1.Text = Class521.smethod_0(44342);
							MessageBox.Show(Class521.smethod_0(44363), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
							base.Close();
						}
					}
					catch (Exception ex)
					{
						Class184.smethod_0(ex);
						MessageBox.Show(Class521.smethod_0(44404) + Environment.NewLine + ex.Message, Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.button_0.Enabled = true;
						this.toolStripStatusLabel_1.Text = Class521.smethod_0(1449);
					}
				}
			}
		}

		// Token: 0x06001092 RID: 4242 RVA: 0x00075220 File Offset: 0x00073420
		private string method_5(string string_1)
		{
			for (int i = 0; i < this.class288_0.Columns.Count; i++)
			{
				if (this.class288_0.Columns[i].DataPropertyName == string_1)
				{
					return this.class288_0.Columns[i].Name;
				}
			}
			return Class521.smethod_0(1449);
		}

		// Token: 0x06001093 RID: 4243 RVA: 0x00075290 File Offset: 0x00073490
		private void method_6(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex != -1)
			{
				if (e.RowIndex != -1)
				{
					TransData transData = this.transFileImporter_0.BackData.Data[e.RowIndex];
					string dataPropertyName = this.class288_0.Columns[e.ColumnIndex].DataPropertyName;
					object editedFormattedValue = this.class288_0.CurrentCell.EditedFormattedValue;
					transData.method_2(dataPropertyName, editedFormattedValue);
					List<TransData> data = this.transFileImporter_0.BackData.Data;
					if (data.Count > e.RowIndex)
					{
						data[e.RowIndex] = transData;
					}
					this.class288_0.Refresh();
				}
			}
		}

		// Token: 0x06001094 RID: 4244 RVA: 0x0000719F File Offset: 0x0000539F
		private void ImportTransForm_Load_1(object sender, EventArgs e)
		{
			Array.ForEach<string>(TransData.FieldBuyOrSell, new Action<string>(this.method_9));
			Array.ForEach<string>(TransData.FieldOpenOrClose, new Action<string>(this.method_10));
		}

		// Token: 0x06001095 RID: 4245 RVA: 0x00075344 File Offset: 0x00073544
		private void method_7(object sender, DataGridViewBindingCompleteEventArgs e)
		{
			try
			{
				for (int i = 0; i < this.transFileImporter_0.DefaultVar.Count; i++)
				{
					Class482 @class = this.transFileImporter_0.DefaultVar[i];
					string text = this.method_5(@class.string_0);
					if (text != Class521.smethod_0(1449))
					{
						this.class288_0.Rows[@class.int_0].Cells[text].Style.BackColor = Color.Yellow;
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x06001096 RID: 4246 RVA: 0x000071CF File Offset: 0x000053CF
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001097 RID: 4247 RVA: 0x000753E4 File Offset: 0x000735E4
		private void method_8()
		{
			this.icontainer_0 = new Container();
			DataGridViewCellStyle dataGridViewCellStyle = new DataGridViewCellStyle();
			DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
			DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
			this.class288_0 = new Class288();
			this.dataGridViewTextBoxColumn_6 = new DataGridViewTextBoxColumn();
			this.dataGridViewDateTimeInputColumn_2 = new DataGridViewDateTimeInputColumn();
			this.dataGridViewDateTimeInputColumn_3 = new DataGridViewDateTimeInputColumn();
			this.dataGridViewTextBoxColumn_7 = new DataGridViewTextBoxColumn();
			this.dataGridViewComboBoxColumn_2 = new DataGridViewComboBoxColumn();
			this.dataGridViewComboBoxColumn_3 = new DataGridViewComboBoxColumn();
			this.dataGridViewDoubleInputColumn_0 = new DataGridViewDoubleInputColumn();
			this.dataGridViewIntegerInputColumn_0 = new DataGridViewIntegerInputColumn();
			this.dataGridViewDoubleInputColumn_1 = new DataGridViewDoubleInputColumn();
			this.dataGridViewTextBoxColumn_8 = new DataGridViewTextBoxColumn();
			this.dataGridViewTextBoxColumn_9 = new DataGridViewTextBoxColumn();
			this.bindingSource_0 = new BindingSource(this.icontainer_0);
			this.openFileDialog_0 = new OpenFileDialog();
			this.dataGridViewTextBoxColumn_0 = new DataGridViewTextBoxColumn();
			this.statusStrip_0 = new StatusStrip();
			this.statusStrip_1 = new StatusStrip();
			this.toolStripProgressBar_0 = new ToolStripProgressBar();
			this.toolStripStatusLabel_0 = new ToolStripStatusLabel();
			this.toolStripStatusLabel_1 = new ToolStripStatusLabel();
			this.button_0 = new Button();
			this.label_0 = new Label();
			this.comboBox_0 = new ComboBox();
			this.groupBox_0 = new GroupBox();
			this.button_1 = new Button();
			this.label_1 = new Label();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.label_4 = new Label();
			((ISupportInitialize)this.class288_0).BeginInit();
			((ISupportInitialize)this.bindingSource_0).BeginInit();
			this.statusStrip_1.SuspendLayout();
			this.groupBox_0.SuspendLayout();
			base.SuspendLayout();
			this.class288_0.AutoGenerateColumns = false;
			this.class288_0.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.class288_0.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			this.class288_0.BackgroundColor = SystemColors.Control;
			this.class288_0.BorderStyle = BorderStyle.None;
			this.class288_0.BypassStyle = true;
			this.class288_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.class288_0.Columns.AddRange(new DataGridViewColumn[]
			{
				this.dataGridViewTextBoxColumn_6,
				this.dataGridViewDateTimeInputColumn_2,
				this.dataGridViewDateTimeInputColumn_3,
				this.dataGridViewTextBoxColumn_7,
				this.dataGridViewComboBoxColumn_2,
				this.dataGridViewComboBoxColumn_3,
				this.dataGridViewDoubleInputColumn_0,
				this.dataGridViewIntegerInputColumn_0,
				this.dataGridViewDoubleInputColumn_1,
				this.dataGridViewTextBoxColumn_8,
				this.dataGridViewTextBoxColumn_9
			});
			this.class288_0.DataSource = this.bindingSource_0;
			dataGridViewCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
			dataGridViewCellStyle.BackColor = SystemColors.Window;
			dataGridViewCellStyle.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			dataGridViewCellStyle.ForeColor = SystemColors.ControlText;
			dataGridViewCellStyle.SelectionBackColor = SystemColors.Highlight;
			dataGridViewCellStyle.SelectionForeColor = SystemColors.ControlText;
			dataGridViewCellStyle.WrapMode = DataGridViewTriState.False;
			this.class288_0.DefaultCellStyle = dataGridViewCellStyle;
			this.class288_0.Dock = DockStyle.Fill;
			this.class288_0.EnableHeadersVisualStyles = false;
			this.class288_0.GridColor = Color.FromArgb(208, 215, 229);
			this.class288_0.Location = new Point(3, 21);
			this.class288_0.Margin = new System.Windows.Forms.Padding(4);
			this.class288_0.Name = Class521.smethod_0(44497);
			dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
			dataGridViewCellStyle2.BackColor = Color.FromArgb(245, 245, 245);
			dataGridViewCellStyle2.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			dataGridViewCellStyle2.ForeColor = SystemColors.WindowText;
			dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
			dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
			dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
			this.class288_0.RowHeadersDefaultCellStyle = dataGridViewCellStyle2;
			this.class288_0.RowHeadersWidth = 45;
			dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.class288_0.RowsDefaultCellStyle = dataGridViewCellStyle3;
			this.class288_0.RowTemplate.Height = 23;
			this.class288_0.Size = new Size(1026, 446);
			this.class288_0.TabIndex = 1;
			this.dataGridViewTextBoxColumn_6.DataPropertyName = Class521.smethod_0(44510);
			this.dataGridViewTextBoxColumn_6.HeaderText = Class521.smethod_0(1463);
			this.dataGridViewTextBoxColumn_6.Name = Class521.smethod_0(44523);
			this.dataGridViewDateTimeInputColumn_2.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDateTimeInputColumn_2.BackgroundStyle.Class = Class521.smethod_0(44568);
			this.dataGridViewDateTimeInputColumn_2.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_2.BackgroundStyle.TextColor = SystemColors.ControlText;
			this.dataGridViewDateTimeInputColumn_2.ButtonDropDown.Visible = true;
			this.dataGridViewDateTimeInputColumn_2.DataPropertyName = Class521.smethod_0(44605);
			this.dataGridViewDateTimeInputColumn_2.HeaderText = Class521.smethod_0(44610);
			this.dataGridViewDateTimeInputColumn_2.InputHorizontalAlignment = eHorizontalAlignment.Center;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.DisplayMonth = new DateTime(2016, 4, 1, 0, 0, 0, 0);
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.FirstDayOfWeek = DayOfWeek.Monday;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.MarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_2.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dataGridViewDateTimeInputColumn_2.Name = Class521.smethod_0(44627);
			this.dataGridViewDateTimeInputColumn_2.Resizable = DataGridViewTriState.True;
			this.dataGridViewDateTimeInputColumn_3.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDateTimeInputColumn_3.BackgroundStyle.Class = Class521.smethod_0(44568);
			this.dataGridViewDateTimeInputColumn_3.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_3.BackgroundStyle.TextColor = SystemColors.ControlText;
			this.dataGridViewDateTimeInputColumn_3.DataPropertyName = Class521.smethod_0(17996);
			this.dataGridViewDateTimeInputColumn_3.Format = eDateTimePickerFormat.LongTime;
			this.dataGridViewDateTimeInputColumn_3.HeaderText = Class521.smethod_0(21638);
			this.dataGridViewDateTimeInputColumn_3.InputHorizontalAlignment = eHorizontalAlignment.Center;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.DisplayMonth = new DateTime(2016, 4, 1, 0, 0, 0, 0);
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.FirstDayOfWeek = DayOfWeek.Monday;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.MarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.Visible = false;
			this.dataGridViewDateTimeInputColumn_3.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dataGridViewDateTimeInputColumn_3.Name = Class521.smethod_0(44668);
			this.dataGridViewDateTimeInputColumn_3.Resizable = DataGridViewTriState.True;
			this.dataGridViewTextBoxColumn_7.DataPropertyName = Class521.smethod_0(44709);
			this.dataGridViewTextBoxColumn_7.HeaderText = Class521.smethod_0(44722);
			this.dataGridViewTextBoxColumn_7.Name = Class521.smethod_0(44739);
			this.dataGridViewTextBoxColumn_7.Visible = false;
			this.dataGridViewComboBoxColumn_2.DataPropertyName = Class521.smethod_0(44784);
			this.dataGridViewComboBoxColumn_2.HeaderText = Class521.smethod_0(44797);
			this.dataGridViewComboBoxColumn_2.Name = Class521.smethod_0(44806);
			this.dataGridViewComboBoxColumn_2.Resizable = DataGridViewTriState.True;
			this.dataGridViewComboBoxColumn_2.SortMode = DataGridViewColumnSortMode.Automatic;
			this.dataGridViewComboBoxColumn_3.DataPropertyName = Class521.smethod_0(44823);
			this.dataGridViewComboBoxColumn_3.HeaderText = Class521.smethod_0(44840);
			this.dataGridViewComboBoxColumn_3.Name = Class521.smethod_0(44849);
			this.dataGridViewComboBoxColumn_3.Resizable = DataGridViewTriState.True;
			this.dataGridViewComboBoxColumn_3.SortMode = DataGridViewColumnSortMode.Automatic;
			this.dataGridViewDoubleInputColumn_0.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDoubleInputColumn_0.BackgroundStyle.Class = Class521.smethod_0(44870);
			this.dataGridViewDoubleInputColumn_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDoubleInputColumn_0.BackgroundStyle.TextColor = SystemColors.ControlText;
			this.dataGridViewDoubleInputColumn_0.DataPropertyName = Class521.smethod_0(44907);
			this.dataGridViewDoubleInputColumn_0.HeaderText = Class521.smethod_0(24982);
			this.dataGridViewDoubleInputColumn_0.Increment = 1.0;
			this.dataGridViewDoubleInputColumn_0.InputHorizontalAlignment = eHorizontalAlignment.Center;
			this.dataGridViewDoubleInputColumn_0.Name = Class521.smethod_0(44916);
			this.dataGridViewDoubleInputColumn_0.Resizable = DataGridViewTriState.True;
			this.dataGridViewIntegerInputColumn_0.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewIntegerInputColumn_0.BackgroundStyle.Class = Class521.smethod_0(44870);
			this.dataGridViewIntegerInputColumn_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewIntegerInputColumn_0.BackgroundStyle.TextColor = SystemColors.ControlText;
			this.dataGridViewIntegerInputColumn_0.DataPropertyName = Class521.smethod_0(44953);
			this.dataGridViewIntegerInputColumn_0.HeaderText = Class521.smethod_0(44962);
			this.dataGridViewIntegerInputColumn_0.InputHorizontalAlignment = eHorizontalAlignment.Center;
			this.dataGridViewIntegerInputColumn_0.Name = Class521.smethod_0(44971);
			this.dataGridViewIntegerInputColumn_0.Resizable = DataGridViewTriState.True;
			this.dataGridViewDoubleInputColumn_1.BackgroundStyle.BackColor = SystemColors.Window;
			this.dataGridViewDoubleInputColumn_1.BackgroundStyle.Class = Class521.smethod_0(44870);
			this.dataGridViewDoubleInputColumn_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.dataGridViewDoubleInputColumn_1.BackgroundStyle.TextColor = SystemColors.ControlText;
			this.dataGridViewDoubleInputColumn_1.DataPropertyName = Class521.smethod_0(45008);
			this.dataGridViewDoubleInputColumn_1.HeaderText = Class521.smethod_0(45021);
			this.dataGridViewDoubleInputColumn_1.Increment = 1.0;
			this.dataGridViewDoubleInputColumn_1.InputHorizontalAlignment = eHorizontalAlignment.Center;
			this.dataGridViewDoubleInputColumn_1.Name = Class521.smethod_0(45034);
			this.dataGridViewDoubleInputColumn_1.Resizable = DataGridViewTriState.True;
			this.dataGridViewTextBoxColumn_8.DataPropertyName = Class521.smethod_0(45071);
			this.dataGridViewTextBoxColumn_8.HeaderText = Class521.smethod_0(45084);
			this.dataGridViewTextBoxColumn_8.Name = Class521.smethod_0(45093);
			this.dataGridViewTextBoxColumn_8.Visible = false;
			this.dataGridViewTextBoxColumn_9.DataPropertyName = Class521.smethod_0(45114);
			this.dataGridViewTextBoxColumn_9.HeaderText = Class521.smethod_0(45123);
			this.dataGridViewTextBoxColumn_9.Name = Class521.smethod_0(45114);
			this.bindingSource_0.DataMember = Class521.smethod_0(45140);
			this.bindingSource_0.DataSource = typeof(Class451);
			this.openFileDialog_0.FileName = Class521.smethod_0(45149);
			this.dataGridViewTextBoxColumn_0.DataPropertyName = Class521.smethod_0(45170);
			this.dataGridViewTextBoxColumn_0.HeaderText = Class521.smethod_0(45170);
			this.dataGridViewTextBoxColumn_0.Name = Class521.smethod_0(45179);
			this.dataGridViewTextBoxColumn_0.Width = 54;
			this.statusStrip_0.AutoSize = false;
			this.statusStrip_0.Dock = DockStyle.None;
			this.statusStrip_0.ImageScalingSize = new Size(20, 20);
			this.statusStrip_0.Location = new Point(143, 0);
			this.statusStrip_0.Name = Class521.smethod_0(45220);
			this.statusStrip_0.Padding = new System.Windows.Forms.Padding(1, 0, 19, 0);
			this.statusStrip_0.Size = new Size(996, 25);
			this.statusStrip_0.TabIndex = 3;
			this.statusStrip_1.ImageScalingSize = new Size(20, 20);
			this.statusStrip_1.Items.AddRange(new ToolStripItem[]
			{
				this.toolStripProgressBar_0,
				this.toolStripStatusLabel_0,
				this.toolStripStatusLabel_1
			});
			this.statusStrip_1.Location = new Point(0, 601);
			this.statusStrip_1.Name = Class521.smethod_0(45241);
			this.statusStrip_1.Padding = new System.Windows.Forms.Padding(1, 0, 19, 0);
			this.statusStrip_1.Size = new Size(1068, 25);
			this.statusStrip_1.TabIndex = 4;
			this.statusStrip_1.Text = Class521.smethod_0(45241);
			this.toolStripProgressBar_0.Name = Class521.smethod_0(45258);
			this.toolStripProgressBar_0.Size = new Size(150, 19);
			this.toolStripProgressBar_0.Visible = false;
			this.toolStripStatusLabel_0.Name = Class521.smethod_0(45287);
			this.toolStripStatusLabel_0.Size = new Size(959, 20);
			this.toolStripStatusLabel_0.Spring = true;
			this.toolStripStatusLabel_1.Image = Class375._1683_Lightbulb_32x32;
			this.toolStripStatusLabel_1.ImageAlign = ContentAlignment.MiddleLeft;
			this.toolStripStatusLabel_1.Name = Class521.smethod_0(45304);
			this.toolStripStatusLabel_1.Size = new Size(89, 20);
			this.toolStripStatusLabel_1.Text = Class521.smethod_0(45321);
			this.toolStripStatusLabel_1.TextAlign = ContentAlignment.MiddleRight;
			this.button_0.Location = new Point(900, 23);
			this.button_0.Name = Class521.smethod_0(45338);
			this.button_0.Size = new Size(130, 34);
			this.button_0.TabIndex = 6;
			this.button_0.Text = Class521.smethod_0(45355);
			this.button_0.UseVisualStyleBackColor = true;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(23, 84);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(82, 15);
			this.label_0.TabIndex = 8;
			this.label_0.Text = Class521.smethod_0(45364);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(114, 80);
			this.comboBox_0.Name = Class521.smethod_0(45385);
			this.comboBox_0.Size = new Size(187, 23);
			this.comboBox_0.TabIndex = 9;
			this.groupBox_0.Controls.Add(this.class288_0);
			this.groupBox_0.Location = new Point(18, 116);
			this.groupBox_0.Name = Class521.smethod_0(10705);
			this.groupBox_0.Size = new Size(1032, 470);
			this.groupBox_0.TabIndex = 10;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(45398);
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Location = new Point(900, 68);
			this.button_1.Name = Class521.smethod_0(24207);
			this.button_1.Size = new Size(130, 34);
			this.button_1.TabIndex = 11;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(23, 23);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(82, 15);
			this.label_1.TabIndex = 12;
			this.label_1.Text = Class521.smethod_0(20709);
			this.label_2.Location = new Point(114, 23);
			this.label_2.Name = Class521.smethod_0(45415);
			this.label_2.Size = new Size(754, 15);
			this.label_2.TabIndex = 13;
			this.label_2.Text = Class521.smethod_0(45415);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(23, 53);
			this.label_3.Name = Class521.smethod_0(5849);
			this.label_3.Size = new Size(82, 15);
			this.label_3.TabIndex = 14;
			this.label_3.Text = Class521.smethod_0(45436);
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(114, 53);
			this.label_4.Name = Class521.smethod_0(45457);
			this.label_4.Size = new Size(119, 15);
			this.label_4.TabIndex = 15;
			this.label_4.Text = Class521.smethod_0(45457);
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(1068, 626);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.comboBox_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.groupBox_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.statusStrip_1);
			base.Controls.Add(this.label_2);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(45478);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(20671);
			base.Load += this.ImportTransForm_Load_1;
			((ISupportInitialize)this.class288_0).EndInit();
			((ISupportInitialize)this.bindingSource_0).EndInit();
			this.statusStrip_1.ResumeLayout(false);
			this.statusStrip_1.PerformLayout();
			this.groupBox_0.ResumeLayout(false);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x06001098 RID: 4248 RVA: 0x000071F0 File Offset: 0x000053F0
		[CompilerGenerated]
		private void method_9(string string_1)
		{
			(this.class288_0.Columns[Class521.smethod_0(44806)] as DataGridViewComboBoxColumn).Items.Add(string_1);
		}

		// Token: 0x06001099 RID: 4249 RVA: 0x0000721F File Offset: 0x0000541F
		[CompilerGenerated]
		private void method_10(string string_1)
		{
			(this.class288_0.Columns[Class521.smethod_0(44849)] as DataGridViewComboBoxColumn).Items.Add(string_1);
		}

		// Token: 0x040008B1 RID: 2225
		private TransFileImporter transFileImporter_0;

		// Token: 0x040008B2 RID: 2226
		private string string_0;

		// Token: 0x040008B3 RID: 2227
		private IContainer icontainer_0;

		// Token: 0x040008B4 RID: 2228
		private OpenFileDialog openFileDialog_0;

		// Token: 0x040008B5 RID: 2229
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_0;

		// Token: 0x040008B6 RID: 2230
		private BindingSource bindingSource_0;

		// Token: 0x040008B7 RID: 2231
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_1;

		// Token: 0x040008B8 RID: 2232
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_0;

		// Token: 0x040008B9 RID: 2233
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_1;

		// Token: 0x040008BA RID: 2234
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_2;

		// Token: 0x040008BB RID: 2235
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_0;

		// Token: 0x040008BC RID: 2236
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_1;

		// Token: 0x040008BD RID: 2237
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_3;

		// Token: 0x040008BE RID: 2238
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_4;

		// Token: 0x040008BF RID: 2239
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_5;

		// Token: 0x040008C0 RID: 2240
		private StatusStrip statusStrip_0;

		// Token: 0x040008C1 RID: 2241
		private StatusStrip statusStrip_1;

		// Token: 0x040008C2 RID: 2242
		private ToolStripStatusLabel toolStripStatusLabel_0;

		// Token: 0x040008C3 RID: 2243
		private ToolStripStatusLabel toolStripStatusLabel_1;

		// Token: 0x040008C4 RID: 2244
		private Class288 class288_0;

		// Token: 0x040008C5 RID: 2245
		private Button button_0;

		// Token: 0x040008C6 RID: 2246
		private Label label_0;

		// Token: 0x040008C7 RID: 2247
		private ComboBox comboBox_0;

		// Token: 0x040008C8 RID: 2248
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_6;

		// Token: 0x040008C9 RID: 2249
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_2;

		// Token: 0x040008CA RID: 2250
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_3;

		// Token: 0x040008CB RID: 2251
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_7;

		// Token: 0x040008CC RID: 2252
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_2;

		// Token: 0x040008CD RID: 2253
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_3;

		// Token: 0x040008CE RID: 2254
		private DataGridViewDoubleInputColumn dataGridViewDoubleInputColumn_0;

		// Token: 0x040008CF RID: 2255
		private DataGridViewIntegerInputColumn dataGridViewIntegerInputColumn_0;

		// Token: 0x040008D0 RID: 2256
		private DataGridViewDoubleInputColumn dataGridViewDoubleInputColumn_1;

		// Token: 0x040008D1 RID: 2257
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_8;

		// Token: 0x040008D2 RID: 2258
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_9;

		// Token: 0x040008D3 RID: 2259
		private ToolStripProgressBar toolStripProgressBar_0;

		// Token: 0x040008D4 RID: 2260
		private GroupBox groupBox_0;

		// Token: 0x040008D5 RID: 2261
		private Button button_1;

		// Token: 0x040008D6 RID: 2262
		private Label label_1;

		// Token: 0x040008D7 RID: 2263
		private Label label_2;

		// Token: 0x040008D8 RID: 2264
		private Label label_3;

		// Token: 0x040008D9 RID: 2265
		private Label label_4;
	}
}
