﻿using System;
using System.Management;
using ns18;
using ns26;

namespace ns16
{
	// Token: 0x020001FA RID: 506
	internal static class Class283
	{
		// Token: 0x06001433 RID: 5171 RVA: 0x00089D98 File Offset: 0x00087F98
		public static string smethod_0()
		{
			string result;
			try
			{
				string str = Class521.smethod_0(15546);
				ManagementObject managementObject = new ManagementObject(Class521.smethod_0(49934) + str + Class521.smethod_0(49975));
				managementObject.Get();
				result = managementObject.GetPropertyValue(Class521.smethod_0(49980)).ToString().Trim();
			}
			catch (Exception exception_)
			{
				Class184.smethod_2(exception_, true);
				throw;
			}
			return result;
		}

		// Token: 0x06001434 RID: 5172 RVA: 0x00089E10 File Offset: 0x00088010
		public static string smethod_1(bool bool_0 = true)
		{
			string text = string.Empty;
			try
			{
				ManagementObjectCollection managementObjectCollection = Class283.smethod_2();
				if (managementObjectCollection != null && managementObjectCollection.Count > 0)
				{
					foreach (ManagementBaseObject managementBaseObject in managementObjectCollection)
					{
						object obj = ((ManagementObject)managementBaseObject)[Class521.smethod_0(50005)];
						if (obj != null)
						{
							text = obj.ToString().Trim();
							if (bool_0 && !string.IsNullOrEmpty(text))
							{
								break;
							}
						}
					}
				}
				if (string.IsNullOrEmpty(text))
				{
					text = Class283.smethod_0();
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_1(exception_, new bool?(false));
				text = Class283.smethod_0();
			}
			return text;
		}

		// Token: 0x06001435 RID: 5173 RVA: 0x00089ED0 File Offset: 0x000880D0
		private static ManagementObjectCollection smethod_2()
		{
			ManagementObjectCollection result;
			try
			{
				result = new ManagementObjectSearcher(Class521.smethod_0(50022)).Get();
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001436 RID: 5174 RVA: 0x00089F10 File Offset: 0x00088110
		public static string smethod_3()
		{
			string result = null;
			ManagementObjectCollection instances = new ManagementClass(Class521.smethod_0(50067)).GetInstances();
			if (instances != null)
			{
				foreach (ManagementBaseObject managementBaseObject in instances)
				{
					foreach (PropertyData propertyData in ((ManagementObject)managementBaseObject).Properties)
					{
						if (propertyData.Name == Class521.smethod_0(50088) || propertyData.Name == Class521.smethod_0(50105))
						{
							result = propertyData.Value.ToString();
							break;
						}
					}
				}
			}
			return result;
		}

		// Token: 0x06001437 RID: 5175 RVA: 0x00089FFC File Offset: 0x000881FC
		public static string smethod_4()
		{
			string result = Class521.smethod_0(50122);
			ManagementObjectCollection instances = new ManagementClass(Class521.smethod_0(50135)).GetInstances();
			if (instances != null)
			{
				foreach (ManagementBaseObject managementBaseObject in instances)
				{
					object propertyValue = ((ManagementObject)managementBaseObject).GetPropertyValue(Class521.smethod_0(1858));
					if (propertyValue != null)
					{
						result = propertyValue.ToString();
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x06001438 RID: 5176 RVA: 0x0008A088 File Offset: 0x00088288
		public static string smethod_5()
		{
			string text = string.Empty;
			string result;
			try
			{
				foreach (ManagementBaseObject managementBaseObject in new ManagementClass(Class521.smethod_0(50156)).GetInstances())
				{
					if ((bool)managementBaseObject[Class521.smethod_0(50201)])
					{
						text = managementBaseObject[Class521.smethod_0(50214)].ToString();
						break;
					}
				}
				result = text;
			}
			catch
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001439 RID: 5177 RVA: 0x0008A134 File Offset: 0x00088334
		public static string smethod_6()
		{
			return Class283.smethod_0();
		}

		// Token: 0x0600143A RID: 5178 RVA: 0x0008A14C File Offset: 0x0008834C
		public static void smethod_7()
		{
			for (int i = 1; i < Class283.int_0.Length; i++)
			{
				Class283.int_0[i] = i % 9;
			}
		}

		// Token: 0x0600143B RID: 5179 RVA: 0x0008A178 File Offset: 0x00088378
		public static string smethod_8()
		{
			Class283.smethod_7();
			string text = Class283.smethod_6();
			for (int i = 1; i < Class283.char_0.Length; i++)
			{
				Class283.char_0[i] = Convert.ToChar(text.Substring(i - 1, 1));
			}
			for (int j = 1; j < Class283.int_1.Length; j++)
			{
				Class283.int_1[j] = Convert.ToInt32(Class283.char_0[j]) + Class283.int_0[Convert.ToInt32(Class283.char_0[j])];
			}
			string text2 = Class521.smethod_0(1449);
			for (int k = 1; k < Class283.int_1.Length; k++)
			{
				if ((Class283.int_1[k] >= 48 && Class283.int_1[k] <= 57) || (Class283.int_1[k] >= 65 && Class283.int_1[k] <= 90) || (Class283.int_1[k] >= 97 && Class283.int_1[k] <= 122))
				{
					text2 += Convert.ToChar(Class283.int_1[k]).ToString();
				}
				else if (Class283.int_1[k] > 122)
				{
					text2 += Convert.ToChar(Class283.int_1[k] - 10).ToString();
				}
				else
				{
					text2 += Convert.ToChar(Class283.int_1[k] - 9).ToString();
				}
			}
			return text2;
		}

		// Token: 0x04000A79 RID: 2681
		public static int[] int_0 = new int[127];

		// Token: 0x04000A7A RID: 2682
		public static char[] char_0 = new char[25];

		// Token: 0x04000A7B RID: 2683
		public static int[] int_1 = new int[25];
	}
}
