﻿using System;
using System.ComponentModel;

namespace TEx.Trading
{
	// Token: 0x020003B6 RID: 950
	[Serializable]
	internal sealed class ShownOpenTrans : Transaction
	{
		// Token: 0x170006A2 RID: 1698
		// (get) Token: 0x0600269F RID: 9887 RVA: 0x00105BA0 File Offset: 0x00103DA0
		// (set) Token: 0x060026A0 RID: 9888 RVA: 0x0000EDB7 File Offset: 0x0000CFB7
		[DisplayName("品种")]
		public string SymblCode
		{
			get
			{
				return this._SymblCode;
			}
			set
			{
				this._SymblCode = value;
			}
		}

		// Token: 0x170006A3 RID: 1699
		// (get) Token: 0x060026A1 RID: 9889 RVA: 0x00105BB8 File Offset: 0x00103DB8
		// (set) Token: 0x060026A2 RID: 9890 RVA: 0x0000EDC2 File Offset: 0x0000CFC2
		[DisplayName("买卖")]
		public string LongOrShort
		{
			get
			{
				return this._LongOrShort;
			}
			set
			{
				this._LongOrShort = value;
			}
		}

		// Token: 0x170006A4 RID: 1700
		// (get) Token: 0x060026A3 RID: 9891 RVA: 0x00105BD0 File Offset: 0x00103DD0
		// (set) Token: 0x060026A4 RID: 9892 RVA: 0x0000EDCD File Offset: 0x0000CFCD
		[DisplayName("今仓")]
		public long TodayUnits
		{
			get
			{
				return this._TodayUnits;
			}
			set
			{
				this._TodayUnits = value;
			}
		}

		// Token: 0x170006A5 RID: 1701
		// (get) Token: 0x060026A5 RID: 9893 RVA: 0x00105BE8 File Offset: 0x00103DE8
		// (set) Token: 0x060026A6 RID: 9894 RVA: 0x0000EDD8 File Offset: 0x0000CFD8
		[DisplayName("可用")]
		public long UsableUnits
		{
			get
			{
				return this._UsableUnits;
			}
			set
			{
				this._UsableUnits = value;
			}
		}

		// Token: 0x170006A6 RID: 1702
		// (get) Token: 0x060026A7 RID: 9895 RVA: 0x00105C00 File Offset: 0x00103E00
		// (set) Token: 0x060026A8 RID: 9896 RVA: 0x0000EDE3 File Offset: 0x0000CFE3
		[DisplayName("现价")]
		public double CurrPrice
		{
			get
			{
				return this._CurrPrice;
			}
			set
			{
				this._CurrPrice = value;
			}
		}

		// Token: 0x170006A7 RID: 1703
		// (get) Token: 0x060026A9 RID: 9897 RVA: 0x00105C18 File Offset: 0x00103E18
		// (set) Token: 0x060026AA RID: 9898 RVA: 0x0000EDEE File Offset: 0x0000CFEE
		[DisplayName("盈亏比率")]
		public decimal ProfitRatio
		{
			get
			{
				return this._ProfitRatio;
			}
			set
			{
				this._ProfitRatio = value;
			}
		}

		// Token: 0x060026AB RID: 9899 RVA: 0x00105C30 File Offset: 0x00103E30
		public decimal? method_0(double double_0, int int_4, decimal decimal_1)
		{
			decimal value = 0m;
			if (base.TransType == 1)
			{
				value = (Convert.ToDecimal(double_0) - base.Price) * (int)base.OpenUnits.Value * int_4;
			}
			else if (base.TransType == 3)
			{
				value = (base.Price - Convert.ToDecimal(double_0)) * (int)base.OpenUnits.Value * int_4;
			}
			base.Profit = new decimal?(value);
			this.method_1(int_4, decimal_1);
			return new decimal?(value);
		}

		// Token: 0x060026AC RID: 9900 RVA: 0x00105CE4 File Offset: 0x00103EE4
		private decimal method_1(int int_4, decimal decimal_1)
		{
			decimal num = 0m;
			if (base.Profit != null && base.OpenUnits.Value > 0L && base.Price != 0m)
			{
				num = base.Profit.Value / (base.Price * base.OpenUnits.Value * int_4 * decimal_1);
			}
			this.ProfitRatio = num;
			return num;
		}

		// Token: 0x0400129A RID: 4762
		private string _SymblCode;

		// Token: 0x0400129B RID: 4763
		private string _LongOrShort;

		// Token: 0x0400129C RID: 4764
		private long _TodayUnits;

		// Token: 0x0400129D RID: 4765
		private long _UsableUnits;

		// Token: 0x0400129E RID: 4766
		private double _CurrPrice;

		// Token: 0x0400129F RID: 4767
		private decimal _ProfitRatio;
	}
}
