﻿using System;
using ns12;
using ns28;
using ns7;
using TEx.SIndicator;

namespace ns21
{
	// Token: 0x02000341 RID: 833
	internal sealed class Class424 : Class415
	{
		// Token: 0x06002314 RID: 8980 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class424(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x06002315 RID: 8981 RVA: 0x000F7040 File Offset: 0x000F5240
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object result;
			if (this.Left.Token.Symbol.HSymbolType != Enum26.const_31)
			{
				result = this.Left.vmethod_1(parserEnvironment_0);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002316 RID: 8982 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}
	}
}
