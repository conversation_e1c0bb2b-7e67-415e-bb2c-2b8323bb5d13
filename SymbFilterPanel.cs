﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns1;
using ns10;
using ns12;
using ns15;
using ns16;
using ns18;
using ns21;
using ns26;
using ns27;
using ns31;
using ns4;
using ns9;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000223 RID: 547
	[Docking(DockingBehavior.AutoDock)]
	public sealed class SymbFilterPanel : UserControl
	{
		// Token: 0x14000082 RID: 130
		// (add) Token: 0x060016A2 RID: 5794 RVA: 0x0009C8E4 File Offset: 0x0009AAE4
		// (remove) Token: 0x060016A3 RID: 5795 RVA: 0x0009C91C File Offset: 0x0009AB1C
		public event MsgEventHandler MsgNotifyNeeded
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060016A4 RID: 5796 RVA: 0x00009455 File Offset: 0x00007655
		protected void method_0(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x060016A5 RID: 5797 RVA: 0x00009472 File Offset: 0x00007672
		public SymbFilterPanel()
		{
			this.method_27();
		}

		// Token: 0x060016A6 RID: 5798 RVA: 0x0009C954 File Offset: 0x0009AB54
		public void method_1()
		{
			this.method_5();
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9.3f, true);
				Font font = new Font(Class521.smethod_0(7183), emSize, FontStyle.Bold);
				this.superTabControl_0.SelectedTabFont = font;
				this.superTabControl_0.TabFont = font;
				emSize = TApp.smethod_4(9f, true);
				font = new Font(Class521.smethod_0(7183), emSize, FontStyle.Bold, GraphicsUnit.Point, 134);
				this.label_5.Font = font;
				this.label_4.Font = font;
				this.label_6.Font = font;
				font = new Font(Class521.smethod_0(7183), emSize);
				this.button_0.Font = font;
				this.button_4.Font = font;
				this.button_1.Font = font;
				this.button_2.Font = font;
				this.button_3.Font = font;
			}
			this.method_8();
			Base.Data.CurrSymblChanging += this.method_6;
			Base.Data.CurrSymblChanged += this.method_7;
			Base.Data.DateSelectionChanged += this.method_11;
			Base.UI.ChartThemeChanged += this.method_3;
			this.button_0.Click += this.button_0_Click;
			this.comboBox_0.Items.AddRange(new object[]
			{
				Class521.smethod_0(55055),
				Class521.smethod_0(55072),
				Class521.smethod_0(23135),
				Class521.smethod_0(23177),
				Class521.smethod_0(23093)
			});
			this.comboBox_0.SelectedIndex = 0;
			List<string> list = TApp.SrvParams.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(SymbFilterPanel.<>c.<>9.method_0)).Select(new Func<StkSymbol, string>(SymbFilterPanel.<>c.<>9.method_1)).Distinct<string>().ToList<string>();
			list.Insert(0, Class521.smethod_0(55089));
			this.comboBox_1.DataSource = list;
			this.comboBox_1.SelectedIndex = 0;
			this.method_2();
			this.button_4.Click += this.button_4_Click;
			this.button_1.Click += this.button_1_Click;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Click += this.button_3_Click;
			this.method_4();
			this.symbFilterApiWorker_0 = new SymbFilterApiWorker();
			this.symbFilterApiWorker_0.SendingRequest += this.method_16;
			this.symbFilterApiWorker_0.ResultReceived += this.symbFilterApiWorker_0_ResultReceived;
			this.symbFilterApiWorker_0.RequestError += this.symbFilterApiWorker_0_RequestError;
		}

		// Token: 0x060016A7 RID: 5799 RVA: 0x0009CC48 File Offset: 0x0009AE48
		private void method_2()
		{
			SymbFilterPanel.dictionary_0 = new Dictionary<string, List<FilterCond>>();
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(55106), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(55123), Class521.smethod_0(55144), ValueUnitType.None, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(55157), Class521.smethod_0(53816), ValueUnitType.Wan, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55178), Class521.smethod_0(55219), ValueUnitType.Percent, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55240), Class521.smethod_0(55269), ValueUnitType.None, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55298), Class521.smethod_0(55331), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(55356), Class521.smethod_0(53485), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55377), Class521.smethod_0(55414), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55443), Class521.smethod_0(1507), Enum20.const_1, ComparisonOpt.Less, null, null)
			});
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(55468), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(55178), Class521.smethod_0(55219), ValueUnitType.Percent, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55485), Class521.smethod_0(55526), ValueUnitType.Percent, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55547), Class521.smethod_0(53447), ValueUnitType.Wan, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55157), Class521.smethod_0(53816), ValueUnitType.Wan, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55576), Class521.smethod_0(55609), ValueUnitType.Wan, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55634), Class521.smethod_0(55667), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55704), Class521.smethod_0(55733), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55774), Class521.smethod_0(55803), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55356), Class521.smethod_0(53485), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55840), Class521.smethod_0(55873), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55898), Class521.smethod_0(55919), Enum20.const_3, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(55944), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(55961), Class521.smethod_0(55986), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56023), Class521.smethod_0(56060), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56097), Class521.smethod_0(56126), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56163), Class521.smethod_0(56196), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55961), Class521.smethod_0(55986), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56241), Class521.smethod_0(56270), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56319), Class521.smethod_0(56348), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56397), Class521.smethod_0(56430), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56475), Class521.smethod_0(56508), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56541), Class521.smethod_0(56566), Enum20.const_2, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(56583), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(56600), Class521.smethod_0(53531), Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56641), Class521.smethod_0(56674), Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56695), Class521.smethod_0(56732), Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56749), Class521.smethod_0(56782), Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56799), Class521.smethod_0(53748), Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56820), Class521.smethod_0(56857), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56874), Class521.smethod_0(56907), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(56924), Class521.smethod_0(56957), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55298), Class521.smethod_0(55331), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(56982), Class521.smethod_0(57011), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57032), Class521.smethod_0(57065), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(55240), Class521.smethod_0(55269), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57098), Class521.smethod_0(57127), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57156), Class521.smethod_0(57185), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55840), Class521.smethod_0(55873), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57214), Class521.smethod_0(57251), Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57292), Class521.smethod_0(57329), Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57370), Class521.smethod_0(57407), Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57448), Class521.smethod_0(57485), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57506), Class521.smethod_0(57539), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57572), Class521.smethod_0(57605), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57638), Class521.smethod_0(57687), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57720), Class521.smethod_0(57749), Enum20.const_2, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(57770), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(57787), Class521.smethod_0(57808), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55123), Class521.smethod_0(55144), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57821), Class521.smethod_0(57846), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57867), Class521.smethod_0(57888), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57901), Class521.smethod_0(57926), Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(57947), Class521.smethod_0(57976), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(57989), Class521.smethod_0(58014), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(58035), Class521.smethod_0(1537), Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(58068), Class521.smethod_0(1550), Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(58101), Class521.smethod_0(1524), Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond(Class521.smethod_0(55443), Class521.smethod_0(1507), Enum20.const_1, ComparisonOpt.Less, null, null)
			});
			SymbFilterPanel.dictionary_0.Add(Class521.smethod_0(58130), new List<FilterCond>
			{
				new FilterCond(Class521.smethod_0(58147), Class521.smethod_0(52020), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(58172), Class521.smethod_0(58205), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55377), Class521.smethod_0(55414), Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(58218), Class521.smethod_0(58251), Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond(Class521.smethod_0(55123), Class521.smethod_0(55144), Enum20.const_0, ComparisonOpt.Less, null, null)
			});
			this.superTabControl_0.SuspendLayout();
			List<string> list = SymbFilterPanel.dictionary_0.Keys.ToList<string>();
			for (int i = 0; i < list.Count; i++)
			{
				string text = list[i];
				SuperTabItem superTabItem = new SuperTabItem();
				SuperTabControlPanel superTabControlPanel = new SuperTabControlPanel();
				this.superTabControl_0.Controls.Add(superTabControlPanel);
				this.superTabControl_0.Tabs.Add(superTabItem);
				superTabControlPanel.Dock = DockStyle.Fill;
				superTabControlPanel.TabItem = superTabItem;
				Panel panel = new Panel();
				panel.Dock = DockStyle.Fill;
				panel.Margin = new System.Windows.Forms.Padding(0);
				panel.Padding = new System.Windows.Forms.Padding(3, 6, 3, 0);
				superTabControlPanel.Controls.Add(panel);
				superTabItem.Text = text;
				superTabItem.GlobalItem = false;
				superTabItem.Margin = new DevComponents.DotNetBar.Padding(0, 0, 6, 0);
				superTabItem.AttachedControl = superTabControlPanel;
			}
			float emSize = 9f;
			if (!TApp.IsHighDpiScreen)
			{
				emSize = (float)(11.25 / TApp.DpiScale);
			}
			this.class291_0 = new Class291();
			this.class291_0.BorderStyle = BorderStyle.None;
			this.class291_0.DrawMode = DrawMode.OwnerDrawFixed;
			this.class291_0.Font = new Font(Class521.smethod_0(7183), emSize, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.class291_0.FormattingEnabled = true;
			this.class291_0.ItemHeight = 28;
			this.class291_0.SelectionAddtionalString = null;
			this.class291_0.SelectionBackColor = null;
			this.class291_0.Dock = DockStyle.Fill;
			this.class291_0.MouseDoubleClick += this.class291_0_MouseDoubleClick;
			this.method_21();
			this.superTabControl_0.SelectedTabChanged += this.method_20;
			this.superTabControl_0.ResumeLayout();
			List<FilterCond> list2 = null;
			Class302 @class = Class302.smethod_1();
			if (@class != null)
			{
				if (@class.LastConds != null && @class.LastConds.Any<FilterCond>())
				{
					list2 = @class.LastConds;
				}
				else
				{
					list2 = @class.method_0();
				}
				if (!string.IsNullOrEmpty(@class.CurrGrpName))
				{
					this.CurrUserCfgCondGrpName = @class.CurrGrpName;
				}
			}
			if (list2 == null)
			{
				list2 = SymbFilterPanel.dictionary_0.Values.First<List<FilterCond>>();
			}
			this.filterConditionPanel.SetFilterConditions(list2, default(System.Windows.Forms.Padding));
			string csv;
			if (@class != null && !string.IsNullOrEmpty(@class.LastRsltCsv))
			{
				csv = @class.LastRsltCsv;
			}
			else
			{
				string[] value = list2.Select(new Func<FilterCond, string>(SymbFilterPanel.<>c.<>9.method_2)).ToArray<string>();
				csv = Class521.smethod_0(58260) + string.Join(Class521.smethod_0(4736), value);
			}
			try
			{
				DataTable dataTableFromCsv = Utility.GetDataTableFromCsv(csv, new int[]
				{
					0,
					1
				});
				this.method_17(dataTableFromCsv);
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x060016A8 RID: 5800 RVA: 0x00009482 File Offset: 0x00007682
		private void method_3(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060016A9 RID: 5801 RVA: 0x0009DB80 File Offset: 0x0009BD80
		private void method_4()
		{
			this.smethod_0();
			this.button_4.ForeColor = Color.Black;
			this.button_2.ForeColor = Color.Black;
			this.button_3.ForeColor = Color.Black;
			this.button_1.ForeColor = Color.Black;
			this.button_0.ForeColor = Color.Black;
			Color backColor;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				backColor = Class181.color_3;
			}
			else
			{
				backColor = Class181.color_9;
			}
			Color foreColor = Base.UI.smethod_35();
			this.tableLayoutPanel_2.BackColor = backColor;
			this.tableLayoutPanel_1.BackColor = Base.UI.smethod_34();
			this.tableLayoutPanel_0.BackColor = backColor;
			this.tableLayoutPanel_4.BackColor = backColor;
			this.tableLayoutPanel_3.BackColor = backColor;
			this.label_3.ForeColor = foreColor;
			this.label_1.ForeColor = foreColor;
			this.label_0.ForeColor = foreColor;
			this.label_2.ForeColor = foreColor;
			this.label_5.ForeColor = foreColor;
			this.label_4.ForeColor = foreColor;
			this.label_6.ForeColor = foreColor;
			this.class298_1.BackColor = backColor;
			this.class298_0.BackColor = backColor;
			eSuperTabStyle tabStyle = Base.UI.smethod_71();
			this.superTabControl_0.TabStyle = tabStyle;
			foreach (object obj in this.superTabControl_0.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					((obj as SuperTabControlPanel).Controls[0] as Panel).BackColor = backColor;
				}
			}
			this.class291_0.method_0();
			this.filterConditionPanel.ApplyTheme();
			this.smethod_1();
		}

		// Token: 0x060016AA RID: 5802 RVA: 0x0009DD50 File Offset: 0x0009BF50
		public void method_5()
		{
			string text = string.Empty;
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null)
			{
				text = dateTime.Value.ToString(Class521.smethod_0(1702));
			}
			if (!string.IsNullOrEmpty(text))
			{
				this.label_1.Text = text;
			}
			else
			{
				this.label_1.Text = Class521.smethod_0(1449);
			}
		}

		// Token: 0x060016AB RID: 5803 RVA: 0x0000948C File Offset: 0x0000768C
		private void method_6(EventArgs1 eventArgs1_0)
		{
			this.method_9();
		}

		// Token: 0x060016AC RID: 5804 RVA: 0x00009496 File Offset: 0x00007696
		private void method_7(EventArgs1 eventArgs1_0)
		{
			this.method_8();
		}

		// Token: 0x060016AD RID: 5805 RVA: 0x000094A0 File Offset: 0x000076A0
		private void method_8()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged += this.method_10;
			}
		}

		// Token: 0x060016AE RID: 5806 RVA: 0x000094C1 File Offset: 0x000076C1
		private void method_9()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged -= this.method_10;
			}
		}

		// Token: 0x060016AF RID: 5807 RVA: 0x000094E2 File Offset: 0x000076E2
		private void method_10(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060016B0 RID: 5808 RVA: 0x000094E2 File Offset: 0x000076E2
		private void method_11(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060016B1 RID: 5809 RVA: 0x00008FDF File Offset: 0x000071DF
		private void button_0_Click(object sender, EventArgs e)
		{
			Base.UI.MainForm.method_81();
		}

		// Token: 0x060016B2 RID: 5810 RVA: 0x0009DDBC File Offset: 0x0009BFBC
		private void method_12(List<FilterCond> list_0)
		{
			foreach (FilterCond filterCond in list_0)
			{
				this.class291_0.Items.Add(new TEx.Util.ComboBoxItem(filterCond.Name, filterCond));
			}
			this.class291_0.SelectedIndex = 0;
		}

		// Token: 0x060016B3 RID: 5811 RVA: 0x0009DE30 File Offset: 0x0009C030
		private void button_4_Click(object sender, EventArgs e)
		{
			SymbFilterPanel.Class300 @class = new SymbFilterPanel.Class300();
			@class.filterCond_0 = this.method_19();
			if (@class.filterCond_0 != null)
			{
				if (this.CondItemList.Exists(new Predicate<FilterCondItem>(@class.method_0)))
				{
					MessageBox.Show(Class521.smethod_0(58281), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
				else
				{
					this.filterConditionPanel.AddFilterCondition(@class.filterCond_0);
				}
			}
		}

		// Token: 0x060016B4 RID: 5812 RVA: 0x0009DEA4 File Offset: 0x0009C0A4
		private void button_1_Click(object sender, EventArgs e)
		{
			List<FilterCond> list = this.filterConditionPanel.GetFilterConditions();
			if (list.Count > 0)
			{
				List<FilterCond> list2 = list.Where(new Func<FilterCond, bool>(SymbFilterPanel.<>c.<>9.method_3)).ToList<FilterCond>();
				if (list2.Count > 0)
				{
					this.method_15(list2);
				}
				else
				{
					MessageBox.Show(Class521.smethod_0(58310), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
		}

		// Token: 0x060016B5 RID: 5813 RVA: 0x0009DF24 File Offset: 0x0009C124
		private string method_13()
		{
			string result = string.Empty;
			int selectedIndex = this.comboBox_0.SelectedIndex;
			if (selectedIndex > 0)
			{
				if (selectedIndex == 1)
				{
					result = Class521.smethod_0(58371);
				}
				else if (selectedIndex == 2)
				{
					result = Class521.smethod_0(58376);
				}
				else if (selectedIndex == 3)
				{
					result = Class521.smethod_0(58381);
				}
				else if (selectedIndex == 4)
				{
					result = Class521.smethod_0(58386);
				}
			}
			return result;
		}

		// Token: 0x060016B6 RID: 5814 RVA: 0x0009DF90 File Offset: 0x0009C190
		private string method_14()
		{
			string result = string.Empty;
			List<string> list = this.comboBox_1.DataSource as List<string>;
			int selectedIndex = this.comboBox_1.SelectedIndex;
			if (selectedIndex > 0)
			{
				result = list[selectedIndex];
			}
			return result;
		}

		// Token: 0x060016B7 RID: 5815 RVA: 0x0009DFD4 File Offset: 0x0009C1D4
		private void method_15(List<FilterCond> list_0)
		{
			new Dictionary<string, object>();
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null)
			{
				string string_ = this.method_13();
				string string_2 = this.method_14();
				this.symbFilterApiWorker_0.method_3(list_0, dateTime.Value, string_, string_2);
			}
		}

		// Token: 0x060016B8 RID: 5816 RVA: 0x000094EC File Offset: 0x000076EC
		private void method_16(object sender, EventArgs e)
		{
			this.label_6.Text = Class521.smethod_0(58391);
		}

		// Token: 0x060016B9 RID: 5817 RVA: 0x0009E01C File Offset: 0x0009C21C
		private void symbFilterApiWorker_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				Dictionary<string, object> requestDict = e.RequestDict;
				if (requestDict.ContainsKey(Class521.smethod_0(1423)) && requestDict.ContainsKey(Class521.smethod_0(1581)))
				{
					DataTable dataTable = requestDict[Class521.smethod_0(1581)] as DataTable;
					this.method_17(dataTable);
					string str = dataTable.Rows.Count.ToString();
					if (dataTable.Rows.Count == 300)
					{
						str = Class521.smethod_0(58428);
					}
					this.label_6.Text = Class521.smethod_0(58437) + str + Class521.smethod_0(5046);
				}
			}
		}

		// Token: 0x060016BA RID: 5818 RVA: 0x0009E0D8 File Offset: 0x0009C2D8
		private void method_17(DataTable dataTable_0)
		{
			this.class298_0.SuspendLayout();
			Class286 @class = this.method_18();
			if (@class == null)
			{
				@class = new Class286();
				this.class298_0.Controls.Clear();
				this.class298_0.Controls.Add(@class);
			}
			@class.method_1(dataTable_0);
			this.class298_0.ResumeLayout();
		}

		// Token: 0x060016BB RID: 5819 RVA: 0x0009E138 File Offset: 0x0009C338
		private Class286 method_18()
		{
			Class286 result = null;
			if (this.class298_0.Controls.Count > 0 && this.class298_0.Controls[0] is Class286)
			{
				result = (this.class298_0.Controls[0] as Class286);
			}
			return result;
		}

		// Token: 0x060016BC RID: 5820 RVA: 0x00009505 File Offset: 0x00007705
		private void symbFilterApiWorker_0_RequestError(object sender, ErrorEventArgs e)
		{
			Class184.smethod_0(e.Exception);
			this.label_6.Text = Class521.smethod_0(58458);
		}

		// Token: 0x060016BD RID: 5821 RVA: 0x0009E190 File Offset: 0x0009C390
		private FilterCond method_19()
		{
			return (this.class291_0.SelectedItem as TEx.Util.ComboBoxItem).Value as FilterCond;
		}

		// Token: 0x060016BE RID: 5822 RVA: 0x00009529 File Offset: 0x00007729
		private void method_20(object sender, SuperTabStripSelectedTabChangedEventArgs e)
		{
			this.method_21();
		}

		// Token: 0x060016BF RID: 5823 RVA: 0x0009E1BC File Offset: 0x0009C3BC
		private void method_21()
		{
			SuperTabItem selectedTab = this.superTabControl_0.SelectedTab;
			Panel panel = (selectedTab.AttachedControl as SuperTabControlPanel).Controls[0] as Panel;
			panel.SuspendLayout();
			this.class291_0.Items.Clear();
			if (this.class291_0.Parent != null)
			{
				this.class291_0.Parent.Controls.Remove(this.class291_0);
			}
			List<FilterCond> list_ = SymbFilterPanel.dictionary_0[selectedTab.Text];
			this.method_12(list_);
			panel.Controls.Add(this.class291_0);
			panel.ResumeLayout();
		}

		// Token: 0x060016C0 RID: 5824 RVA: 0x0009E260 File Offset: 0x0009C460
		private void class291_0_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			int num = this.class291_0.IndexFromPoint(e.Location);
			if (num != -1)
			{
				SymbFilterPanel.Class301 @class = new SymbFilterPanel.Class301();
				TEx.Util.ComboBoxItem comboBoxItem = this.class291_0.Items[num] as TEx.Util.ComboBoxItem;
				@class.filterCond_0 = (comboBoxItem.Value as FilterCond);
				if (@class.filterCond_0 != null && !this.CondItemList.Exists(new Predicate<FilterCondItem>(@class.method_0)))
				{
					this.filterConditionPanel.AddFilterCondition(@class.filterCond_0);
				}
			}
		}

		// Token: 0x060016C1 RID: 5825 RVA: 0x0009E2E8 File Offset: 0x0009C4E8
		private void button_2_Click(object sender, EventArgs e)
		{
			Form4 form = new Form4(this.filterConditionPanel.GetFilterConditions(), this.CurrUserCfgCondGrpName);
			form.CondGroupSaved += this.method_22;
			form.FormClosed += this.method_23;
			form.ShowDialog();
		}

		// Token: 0x060016C2 RID: 5826 RVA: 0x00009533 File Offset: 0x00007733
		private void method_22(object sender, MsgEventArgs e)
		{
			this.CurrUserCfgCondGrpName = e.Msg;
			this.method_0(Class521.smethod_0(58475));
		}

		// Token: 0x060016C3 RID: 5827 RVA: 0x00009553 File Offset: 0x00007753
		private void method_23(object sender, FormClosedEventArgs e)
		{
			Form4 form = sender as Form4;
			form.CondGroupSaved -= this.method_22;
			form.FormClosed -= this.method_23;
		}

		// Token: 0x060016C4 RID: 5828 RVA: 0x0009E338 File Offset: 0x0009C538
		private void button_3_Click(object sender, EventArgs e)
		{
			Class302 @class = Class302.smethod_1();
			if (@class != null)
			{
				Form3 form = new Form3(@class);
				form.CondGroupSelected += this.method_24;
				form.FormClosed += this.method_25;
				form.ShowDialog();
			}
			else
			{
				MessageBox.Show(Class521.smethod_0(58512), Class521.smethod_0(7587), MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x060016C5 RID: 5829 RVA: 0x0009E3A0 File Offset: 0x0009C5A0
		private void method_24(object sender, MsgEventArgs e)
		{
			string msg = e.Msg;
			List<FilterCond> list_ = e.Data as List<FilterCond>;
			this.CurrUserCfgCondGrpName = msg;
			this.filterConditionPanel.SetFilterConditions(list_, default(System.Windows.Forms.Padding));
			this.method_0(Class521.smethod_0(58561));
		}

		// Token: 0x060016C6 RID: 5830 RVA: 0x00009580 File Offset: 0x00007780
		private void method_25(object sender, FormClosedEventArgs e)
		{
			Form3 form = sender as Form3;
			form.CondGroupSelected -= this.method_24;
			form.FormClosed -= this.method_25;
		}

		// Token: 0x060016C7 RID: 5831 RVA: 0x0009E3F0 File Offset: 0x0009C5F0
		public void method_26()
		{
			Class302 @class = Class302.smethod_1();
			if (@class == null)
			{
				@class = new Class302();
			}
			@class.LastConds = this.filterConditionPanel.GetFilterConditions();
			Class286 class2 = this.method_18();
			if (class2 != null)
			{
				DataTable dataTable = class2.DataSource as DataTable;
				if (dataTable != null)
				{
					string csvFromDataTable = Utility.GetCsvFromDataTable(dataTable);
					@class.LastRsltCsv = csvFromDataTable;
				}
			}
			if (@class.LastConds.Count > 0 && !string.IsNullOrEmpty(@class.LastRsltCsv))
			{
				@class.method_1();
			}
		}

		// Token: 0x170003AE RID: 942
		// (get) Token: 0x060016C8 RID: 5832 RVA: 0x0009E468 File Offset: 0x0009C668
		public List<FilterCondItem> CondItemList
		{
			get
			{
				List<FilterCondItem> list = new List<FilterCondItem>();
				foreach (object obj in this.filterConditionPanel.Controls)
				{
					FilterCondItem item = obj as FilterCondItem;
					list.Add(item);
				}
				return list;
			}
		}

		// Token: 0x170003AF RID: 943
		// (get) Token: 0x060016C9 RID: 5833 RVA: 0x0009E4D4 File Offset: 0x0009C6D4
		public bool IsInInputState
		{
			get
			{
				return this.CondItemList.Exists(new Predicate<FilterCondItem>(SymbFilterPanel.<>c.<>9.method_4));
			}
		}

		// Token: 0x170003B0 RID: 944
		// (get) Token: 0x060016CA RID: 5834 RVA: 0x0009E510 File Offset: 0x0009C710
		public Button AcceptButton
		{
			get
			{
				return this.button_1;
			}
		}

		// Token: 0x170003B1 RID: 945
		// (get) Token: 0x060016CB RID: 5835 RVA: 0x0009E528 File Offset: 0x0009C728
		// (set) Token: 0x060016CC RID: 5836 RVA: 0x000095AD File Offset: 0x000077AD
		public string CurrUserCfgCondGrpName { get; set; }

		// Token: 0x170003B2 RID: 946
		// (get) Token: 0x060016CD RID: 5837 RVA: 0x0009E540 File Offset: 0x0009C740
		public static Dictionary<string, List<FilterCond>> CondDict
		{
			get
			{
				return SymbFilterPanel.dictionary_0;
			}
		}

		// Token: 0x060016CE RID: 5838 RVA: 0x000095B8 File Offset: 0x000077B8
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060016CF RID: 5839 RVA: 0x0009E558 File Offset: 0x0009C758
		private void method_27()
		{
			this.tableLayoutPanel_0 = new TableLayoutPanel();
			this.tableLayoutPanel_1 = new TableLayoutPanel();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.comboBox_0 = new ComboBox();
			this.label_2 = new Label();
			this.comboBox_1 = new ComboBox();
			this.label_3 = new Label();
			this.button_0 = new Button();
			this.tableLayoutPanel_2 = new TableLayoutPanel();
			this.label_6 = new Label();
			this.tableLayoutPanel_3 = new TableLayoutPanel();
			this.button_1 = new Button();
			this.button_3 = new Button();
			this.button_2 = new Button();
			this.label_4 = new Label();
			this.tableLayoutPanel_4 = new TableLayoutPanel();
			this.button_4 = new Button();
			this.label_5 = new Label();
			this.filterConditionPanel = new FilterConditionPanel();
			this.class298_0 = new Class298();
			this.class298_1 = new Class298();
			this.superTabControl_0 = new SuperTabControl();
			this.tableLayoutPanel_0.SuspendLayout();
			this.tableLayoutPanel_1.SuspendLayout();
			this.tableLayoutPanel_2.SuspendLayout();
			this.tableLayoutPanel_3.SuspendLayout();
			this.tableLayoutPanel_4.SuspendLayout();
			this.class298_1.SuspendLayout();
			((ISupportInitialize)this.superTabControl_0).BeginInit();
			base.SuspendLayout();
			this.tableLayoutPanel_0.ColumnCount = 1;
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Controls.Add(this.tableLayoutPanel_1, 0, 0);
			this.tableLayoutPanel_0.Controls.Add(this.tableLayoutPanel_2, 0, 1);
			this.tableLayoutPanel_0.Dock = DockStyle.Fill;
			this.tableLayoutPanel_0.Location = new Point(0, 0);
			this.tableLayoutPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_0.Name = Class521.smethod_0(53215);
			this.tableLayoutPanel_0.RowCount = 2;
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Absolute, 32f));
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Size = new Size(1450, 362);
			this.tableLayoutPanel_0.TabIndex = 0;
			this.tableLayoutPanel_1.ColumnCount = 8;
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 160f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 160f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Controls.Add(this.label_0, 0, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_1, 5, 0);
			this.tableLayoutPanel_1.Controls.Add(this.comboBox_0, 1, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_2, 2, 0);
			this.tableLayoutPanel_1.Controls.Add(this.comboBox_1, 3, 0);
			this.tableLayoutPanel_1.Controls.Add(this.label_3, 4, 0);
			this.tableLayoutPanel_1.Controls.Add(this.button_0, 6, 0);
			this.tableLayoutPanel_1.Dock = DockStyle.Fill;
			this.tableLayoutPanel_1.Location = new Point(0, 0);
			this.tableLayoutPanel_1.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_1.Name = Class521.smethod_0(58598);
			this.tableLayoutPanel_1.RowCount = 1;
			this.tableLayoutPanel_1.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Size = new Size(1450, 32);
			this.tableLayoutPanel_1.TabIndex = 1;
			this.label_0.Anchor = AnchorStyles.Right;
			this.label_0.AutoSize = true;
			this.label_0.BackColor = Color.Transparent;
			this.label_0.Location = new Point(17, 8);
			this.label_0.Name = Class521.smethod_0(58623);
			this.label_0.Size = new Size(60, 15);
			this.label_0.TabIndex = 3;
			this.label_0.Text = Class521.smethod_0(58644);
			this.label_1.Anchor = AnchorStyles.Left;
			this.label_1.AutoSize = true;
			this.label_1.BackColor = Color.Transparent;
			this.label_1.Location = new Point(563, 8);
			this.label_1.Name = Class521.smethod_0(54301);
			this.label_1.Size = new Size(87, 15);
			this.label_1.TabIndex = 1;
			this.label_1.Text = Class521.smethod_0(54322);
			this.comboBox_0.Anchor = (AnchorStyles.Left | AnchorStyles.Right);
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(80, 4);
			this.comboBox_0.Margin = new System.Windows.Forms.Padding(0);
			this.comboBox_0.Name = Class521.smethod_0(58661);
			this.comboBox_0.Size = new Size(160, 23);
			this.comboBox_0.TabIndex = 6;
			this.label_2.Anchor = AnchorStyles.Right;
			this.label_2.AutoSize = true;
			this.label_2.BackColor = Color.Transparent;
			this.label_2.Location = new Point(272, 8);
			this.label_2.Name = Class521.smethod_0(58678);
			this.label_2.Size = new Size(45, 15);
			this.label_2.TabIndex = 5;
			this.label_2.Text = Class521.smethod_0(58695);
			this.comboBox_1.Anchor = (AnchorStyles.Left | AnchorStyles.Right);
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Location = new Point(320, 4);
			this.comboBox_1.Margin = new System.Windows.Forms.Padding(0);
			this.comboBox_1.Name = Class521.smethod_0(58708);
			this.comboBox_1.Size = new Size(160, 23);
			this.comboBox_1.TabIndex = 7;
			this.label_3.Anchor = AnchorStyles.Right;
			this.label_3.AutoSize = true;
			this.label_3.BackColor = Color.Transparent;
			this.label_3.Location = new Point(512, 8);
			this.label_3.Name = Class521.smethod_0(58725);
			this.label_3.Size = new Size(45, 15);
			this.label_3.TabIndex = 7;
			this.label_3.Text = Class521.smethod_0(58742);
			this.button_0.Anchor = AnchorStyles.Left;
			this.button_0.BackColor = Color.Transparent;
			this.button_0.Location = new Point(662, 2);
			this.button_0.Margin = new System.Windows.Forms.Padding(2);
			this.button_0.MinimumSize = new Size(80, 25);
			this.button_0.Name = Class521.smethod_0(54339);
			this.button_0.Size = new Size(87, 27);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(54360);
			this.button_0.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.button_0.UseVisualStyleBackColor = false;
			this.tableLayoutPanel_2.ColumnCount = 5;
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 290f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 0.1f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 460f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 0.1f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 99.8f));
			this.tableLayoutPanel_2.Controls.Add(this.label_6, 4, 0);
			this.tableLayoutPanel_2.Controls.Add(this.tableLayoutPanel_3, 2, 0);
			this.tableLayoutPanel_2.Controls.Add(this.tableLayoutPanel_4, 0, 0);
			this.tableLayoutPanel_2.Controls.Add(this.filterConditionPanel, 2, 1);
			this.tableLayoutPanel_2.Controls.Add(this.class298_0, 4, 1);
			this.tableLayoutPanel_2.Controls.Add(this.class298_1, 0, 1);
			this.tableLayoutPanel_2.Dock = DockStyle.Fill;
			this.tableLayoutPanel_2.Location = new Point(0, 32);
			this.tableLayoutPanel_2.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_2.Name = Class521.smethod_0(58755);
			this.tableLayoutPanel_2.RowCount = 2;
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Absolute, 31f));
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_2.Size = new Size(1450, 330);
			this.tableLayoutPanel_2.TabIndex = 0;
			this.label_6.Anchor = AnchorStyles.None;
			this.label_6.AutoSize = true;
			this.label_6.BackColor = Color.Transparent;
			this.label_6.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_6.Location = new Point(1064, 8);
			this.label_6.Name = Class521.smethod_0(58776);
			this.label_6.Size = new Size(71, 15);
			this.label_6.TabIndex = 2;
			this.label_6.Text = Class521.smethod_0(58458);
			this.tableLayoutPanel_3.ColumnCount = 5;
			this.tableLayoutPanel_3.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 15f));
			this.tableLayoutPanel_3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30f));
			this.tableLayoutPanel_3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30f));
			this.tableLayoutPanel_3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
			this.tableLayoutPanel_3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
			this.tableLayoutPanel_3.Controls.Add(this.button_1, 2, 0);
			this.tableLayoutPanel_3.Controls.Add(this.button_3, 4, 0);
			this.tableLayoutPanel_3.Controls.Add(this.button_2, 3, 0);
			this.tableLayoutPanel_3.Controls.Add(this.label_4, 1, 0);
			this.tableLayoutPanel_3.Dock = DockStyle.Fill;
			this.tableLayoutPanel_3.Location = new Point(290, 0);
			this.tableLayoutPanel_3.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_3.Name = Class521.smethod_0(58801);
			this.tableLayoutPanel_3.RowCount = 1;
			this.tableLayoutPanel_3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_3.Size = new Size(460, 31);
			this.tableLayoutPanel_3.TabIndex = 5;
			this.button_1.Anchor = AnchorStyles.Left;
			this.button_1.BackColor = Color.Transparent;
			this.button_1.Image = Class375.flash_16x16;
			this.button_1.ImageAlign = ContentAlignment.MiddleLeft;
			this.button_1.Location = new Point(150, 2);
			this.button_1.Margin = new System.Windows.Forms.Padding(2);
			this.button_1.MinimumSize = new Size(106, 25);
			this.button_1.Name = Class521.smethod_0(58834);
			this.button_1.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.button_1.Size = new Size(116, 27);
			this.button_1.TabIndex = 1;
			this.button_1.Text = Class521.smethod_0(58851);
			this.button_1.TextAlign = ContentAlignment.MiddleRight;
			this.button_1.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.button_1.UseVisualStyleBackColor = false;
			this.button_3.Anchor = AnchorStyles.Right;
			this.button_3.BackColor = Color.Transparent;
			this.button_3.Image = Class375.folder_up_16x16;
			this.button_3.ImageAlign = ContentAlignment.MiddleLeft;
			this.button_3.Location = new Point(382, 2);
			this.button_3.Margin = new System.Windows.Forms.Padding(2);
			this.button_3.MinimumSize = new Size(66, 25);
			this.button_3.Name = Class521.smethod_0(58872);
			this.button_3.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.button_3.Size = new Size(76, 27);
			this.button_3.TabIndex = 3;
			this.button_3.Text = Class521.smethod_0(51116);
			this.button_3.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.button_3.UseVisualStyleBackColor = false;
			this.button_2.Anchor = AnchorStyles.Right;
			this.button_2.BackColor = Color.Transparent;
			this.button_2.Image = Class375.floppy_disc_16x16;
			this.button_2.ImageAlign = ContentAlignment.MiddleLeft;
			this.button_2.Location = new Point(292, 2);
			this.button_2.Margin = new System.Windows.Forms.Padding(2);
			this.button_2.MinimumSize = new Size(66, 25);
			this.button_2.Name = Class521.smethod_0(58893);
			this.button_2.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.button_2.Size = new Size(76, 27);
			this.button_2.TabIndex = 2;
			this.button_2.Text = Class521.smethod_0(58914);
			this.button_2.TextAlign = ContentAlignment.MiddleRight;
			this.button_2.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.button_2.UseVisualStyleBackColor = false;
			this.label_4.Anchor = AnchorStyles.None;
			this.label_4.AutoSize = true;
			this.label_4.BackColor = Color.Transparent;
			this.label_4.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_4.Location = new Point(46, 8);
			this.label_4.Name = Class521.smethod_0(58923);
			this.label_4.Size = new Size(71, 15);
			this.label_4.TabIndex = 0;
			this.label_4.Text = Class521.smethod_0(58948);
			this.tableLayoutPanel_4.ColumnCount = 3;
			this.tableLayoutPanel_4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 6.122449f));
			this.tableLayoutPanel_4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 46.93877f));
			this.tableLayoutPanel_4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 46.93877f));
			this.tableLayoutPanel_4.Controls.Add(this.button_4, 2, 0);
			this.tableLayoutPanel_4.Controls.Add(this.label_5, 1, 0);
			this.tableLayoutPanel_4.Dock = DockStyle.Fill;
			this.tableLayoutPanel_4.Location = new Point(0, 0);
			this.tableLayoutPanel_4.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_4.Name = Class521.smethod_0(58965);
			this.tableLayoutPanel_4.RowCount = 1;
			this.tableLayoutPanel_4.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_4.Size = new Size(290, 31);
			this.tableLayoutPanel_4.TabIndex = 6;
			this.button_4.Anchor = AnchorStyles.Left;
			this.button_4.BackColor = Color.Transparent;
			this.button_4.Image = Class375.add_16x16;
			this.button_4.ImageAlign = ContentAlignment.MiddleLeft;
			this.button_4.Location = new Point(155, 2);
			this.button_4.Margin = new System.Windows.Forms.Padding(2);
			this.button_4.MinimumSize = new Size(66, 25);
			this.button_4.Name = Class521.smethod_0(58998);
			this.button_4.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.button_4.Size = new Size(76, 27);
			this.button_4.TabIndex = 5;
			this.button_4.Text = Class521.smethod_0(59015);
			this.button_4.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.button_4.UseVisualStyleBackColor = false;
			this.label_5.Anchor = AnchorStyles.None;
			this.label_5.AutoSize = true;
			this.label_5.BackColor = Color.Transparent;
			this.label_5.Font = new Font(Class521.smethod_0(7183), 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_5.Location = new Point(49, 8);
			this.label_5.Name = Class521.smethod_0(59024);
			this.label_5.Size = new Size(71, 15);
			this.label_5.TabIndex = 1;
			this.label_5.Text = Class521.smethod_0(59049);
			this.filterConditionPanel.AutoScroll = true;
			this.filterConditionPanel.AutoSize = true;
			this.filterConditionPanel.BackColor = Color.Transparent;
			this.filterConditionPanel.BorderColor = Color.Empty;
			this.filterConditionPanel.Dock = DockStyle.Fill;
			this.filterConditionPanel.FlowDirection = FlowDirection.TopDown;
			this.filterConditionPanel.Location = new Point(293, 34);
			this.filterConditionPanel.Name = Class521.smethod_0(59066);
			this.filterConditionPanel.Padding = new System.Windows.Forms.Padding(3, 6, 3, 6);
			this.filterConditionPanel.Size = new Size(454, 293);
			this.filterConditionPanel.TabIndex = 3;
			this.filterConditionPanel.WrapContents = false;
			this.class298_0.BorderColor = Color.Empty;
			this.class298_0.Dock = DockStyle.Fill;
			this.class298_0.DrawCustomBorder = false;
			this.class298_0.Location = new Point(753, 34);
			this.class298_0.Name = Class521.smethod_0(59091);
			this.class298_0.Padding = new System.Windows.Forms.Padding(1);
			this.class298_0.Size = new Size(694, 293);
			this.class298_0.TabIndex = 7;
			this.class298_1.BorderColor = Color.Empty;
			this.class298_1.Controls.Add(this.superTabControl_0);
			this.class298_1.Dock = DockStyle.Fill;
			this.class298_1.DrawCustomBorder = false;
			this.class298_1.Location = new Point(3, 34);
			this.class298_1.Name = Class521.smethod_0(59112);
			this.class298_1.Size = new Size(284, 293);
			this.class298_1.TabIndex = 8;
			this.superTabControl_0.ControlBox.CloseBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.MenuBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.Name = Class521.smethod_0(1449);
			this.superTabControl_0.ControlBox.SubItems.AddRange(new BaseItem[]
			{
				this.superTabControl_0.ControlBox.MenuBox,
				this.superTabControl_0.ControlBox.CloseBox
			});
			this.superTabControl_0.Dock = DockStyle.Fill;
			this.superTabControl_0.Location = new Point(0, 0);
			this.superTabControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.superTabControl_0.Name = Class521.smethod_0(59133);
			this.superTabControl_0.ReorderTabsEnabled = true;
			this.superTabControl_0.SelectedTabFont = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Bold);
			this.superTabControl_0.SelectedTabIndex = 0;
			this.superTabControl_0.Size = new Size(284, 293);
			this.superTabControl_0.TabAlignment = eTabStripAlignment.Left;
			this.superTabControl_0.TabFont = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.superTabControl_0.TabIndex = 2;
			this.superTabControl_0.TabStyle = eSuperTabStyle.Office2010BackstageBlue;
			this.superTabControl_0.Text = Class521.smethod_0(54644);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tableLayoutPanel_0);
			this.DoubleBuffered = true;
			base.Name = Class521.smethod_0(59166);
			base.Size = new Size(1450, 362);
			this.tableLayoutPanel_0.ResumeLayout(false);
			this.tableLayoutPanel_1.ResumeLayout(false);
			this.tableLayoutPanel_1.PerformLayout();
			this.tableLayoutPanel_2.ResumeLayout(false);
			this.tableLayoutPanel_2.PerformLayout();
			this.tableLayoutPanel_3.ResumeLayout(false);
			this.tableLayoutPanel_3.PerformLayout();
			this.tableLayoutPanel_4.ResumeLayout(false);
			this.tableLayoutPanel_4.PerformLayout();
			this.class298_1.ResumeLayout(false);
			((ISupportInitialize)this.superTabControl_0).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000B86 RID: 2950
		private SymbFilterApiWorker symbFilterApiWorker_0;

		// Token: 0x04000B87 RID: 2951
		private Class291 class291_0;

		// Token: 0x04000B88 RID: 2952
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x04000B89 RID: 2953
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000B8A RID: 2954
		private static Dictionary<string, List<FilterCond>> dictionary_0;

		// Token: 0x04000B8B RID: 2955
		private IContainer icontainer_0;

		// Token: 0x04000B8C RID: 2956
		private TableLayoutPanel tableLayoutPanel_0;

		// Token: 0x04000B8D RID: 2957
		private TableLayoutPanel tableLayoutPanel_1;

		// Token: 0x04000B8E RID: 2958
		private Label label_0;

		// Token: 0x04000B8F RID: 2959
		private Button button_0;

		// Token: 0x04000B90 RID: 2960
		private Label label_1;

		// Token: 0x04000B91 RID: 2961
		private ComboBox comboBox_0;

		// Token: 0x04000B92 RID: 2962
		private Label label_2;

		// Token: 0x04000B93 RID: 2963
		private ComboBox comboBox_1;

		// Token: 0x04000B94 RID: 2964
		private Label label_3;

		// Token: 0x04000B95 RID: 2965
		private TableLayoutPanel tableLayoutPanel_2;

		// Token: 0x04000B96 RID: 2966
		private Label label_4;

		// Token: 0x04000B97 RID: 2967
		private Label label_5;

		// Token: 0x04000B98 RID: 2968
		private Button button_1;

		// Token: 0x04000B99 RID: 2969
		private Label label_6;

		// Token: 0x04000B9A RID: 2970
		private FilterConditionPanel filterConditionPanel;

		// Token: 0x04000B9B RID: 2971
		private TableLayoutPanel tableLayoutPanel_3;

		// Token: 0x04000B9C RID: 2972
		private Button button_2;

		// Token: 0x04000B9D RID: 2973
		private Button button_3;

		// Token: 0x04000B9E RID: 2974
		private TableLayoutPanel tableLayoutPanel_4;

		// Token: 0x04000B9F RID: 2975
		private Button button_4;

		// Token: 0x04000BA0 RID: 2976
		private Class298 class298_0;

		// Token: 0x04000BA1 RID: 2977
		private Class298 class298_1;

		// Token: 0x04000BA2 RID: 2978
		private SuperTabControl superTabControl_0;

		// Token: 0x02000225 RID: 549
		[CompilerGenerated]
		private sealed class Class300
		{
			// Token: 0x060016D8 RID: 5848 RVA: 0x0009FCDC File Offset: 0x0009DEDC
			internal bool method_0(FilterCondItem filterCondItem_0)
			{
				return filterCondItem_0.FilterCond.Name == this.filterCond_0.Name;
			}

			// Token: 0x04000BA9 RID: 2985
			public FilterCond filterCond_0;
		}

		// Token: 0x02000226 RID: 550
		[CompilerGenerated]
		private sealed class Class301
		{
			// Token: 0x060016DA RID: 5850 RVA: 0x0009FD08 File Offset: 0x0009DF08
			internal bool method_0(FilterCondItem filterCondItem_0)
			{
				return filterCondItem_0.FilterCond.Name == this.filterCond_0.Name;
			}

			// Token: 0x04000BAA RID: 2986
			public FilterCond filterCond_0;
		}
	}
}
