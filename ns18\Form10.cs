﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns6;
using ns9;
using TEx;

namespace ns18
{
	// Token: 0x02000169 RID: 361
	internal sealed partial class Form10 : Form
	{
		// Token: 0x14000072 RID: 114
		// (add) Token: 0x06000DAC RID: 3500 RVA: 0x000588B0 File Offset: 0x00056AB0
		// (remove) Token: 0x06000DAD RID: 3501 RVA: 0x000588E8 File Offset: 0x00056AE8
		public event Delegate9 DrawTxtSet
		{
			[CompilerGenerated]
			add
			{
				Delegate9 @delegate = this.delegate9_0;
				Delegate9 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate9 value2 = (Delegate9)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate9>(ref this.delegate9_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate9 @delegate = this.delegate9_0;
				Delegate9 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate9 value2 = (Delegate9)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate9>(ref this.delegate9_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000DAE RID: 3502 RVA: 0x00058920 File Offset: 0x00056B20
		protected void method_0(EventArgs8 eventArgs8_0)
		{
			Delegate9 @delegate = this.delegate9_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs8_0);
			}
		}

		// Token: 0x14000073 RID: 115
		// (add) Token: 0x06000DAF RID: 3503 RVA: 0x00058944 File Offset: 0x00056B44
		// (remove) Token: 0x06000DB0 RID: 3504 RVA: 0x0005897C File Offset: 0x00056B7C
		public event EventHandler FormClosedWithoutTxtSet
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000DB1 RID: 3505 RVA: 0x000589B4 File Offset: 0x00056BB4
		protected void method_1(object sender, EventArgs e)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06000DB2 RID: 3506 RVA: 0x000589D8 File Offset: 0x00056BD8
		public Form10()
		{
			this.method_2();
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font(Class521.smethod_0(7183), (float)(11.25 / TApp.DpiScale));
				this.textBox_0.Font = font;
				this.button_1.Font = font;
				this.button_0.Font = font;
				this.button_2.Font = font;
				this.button_3.Font = font;
			}
			base.FormClosing += this.Form10_FormClosing;
			if (this.object_0 == null)
			{
				this.object_0 = Base.UI.Form.LastSelectedFont;
			}
			if (this.nullable_0 == null)
			{
				this.nullable_0 = Base.UI.Form.LastSelectedFontColor;
			}
		}

		// Token: 0x06000DB3 RID: 3507 RVA: 0x00006153 File Offset: 0x00004353
		public Form10(TransArrow transArrow_0) : this()
		{
			this.TextInput = transArrow_0.Notes;
			this.object_0 = transArrow_0.NoteBoxFont;
			this.nullable_0 = transArrow_0.NoteBoxFontColor;
		}

		// Token: 0x06000DB4 RID: 3508 RVA: 0x00006181 File Offset: 0x00004381
		public Form10(DrawText drawText_1) : this()
		{
			this.drawText_0 = drawText_1;
			this.TextInput = drawText_1.Text;
			this.object_0 = drawText_1.Font;
			this.nullable_0 = drawText_1.LineColor;
		}

		// Token: 0x06000DB5 RID: 3509 RVA: 0x000061B6 File Offset: 0x000043B6
		private void Form10_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (!this.bool_0)
			{
				this.method_1(this, new EventArgs());
			}
		}

		// Token: 0x06000DB6 RID: 3510 RVA: 0x00058AAC File Offset: 0x00056CAC
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (keyData == Keys.Escape)
			{
				base.Close();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x17000223 RID: 547
		// (get) Token: 0x06000DB7 RID: 3511 RVA: 0x00058AD0 File Offset: 0x00056CD0
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				createParams.ExStyle |= 33554432;
				return createParams;
			}
		}

		// Token: 0x06000DB8 RID: 3512 RVA: 0x00058AFC File Offset: 0x00056CFC
		private void button_0_Click(object sender, EventArgs e)
		{
			string textInput = this.TextInput;
			bool bool_ = this.drawText_0 == null;
			this.method_0(new EventArgs8(textInput, this.object_0, this.nullable_0, bool_));
			this.bool_0 = true;
			base.Close();
		}

		// Token: 0x06000DB9 RID: 3513 RVA: 0x00058B48 File Offset: 0x00056D48
		private void button_3_Click(object sender, EventArgs e)
		{
			if (this.nullable_0 != null)
			{
				this.colorDialog_0.Color = this.nullable_0.Value;
			}
			if (this.colorDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.nullable_0 = new Color?(this.colorDialog_0.Color);
				Base.UI.Form.LastSelectedFontColor = this.nullable_0;
			}
		}

		// Token: 0x06000DBA RID: 3514 RVA: 0x00058BB0 File Offset: 0x00056DB0
		private void button_2_Click(object sender, EventArgs e)
		{
			if (this.object_0 != null)
			{
				this.fontDialog_0.Font = (this.object_0 as Font);
			}
			if (this.fontDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.object_0 = this.fontDialog_0.Font;
				Base.UI.Form.LastSelectedFont = (this.object_0 as Font);
			}
		}

		// Token: 0x06000DBB RID: 3515 RVA: 0x00004273 File Offset: 0x00002473
		private void button_1_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000224 RID: 548
		// (get) Token: 0x06000DBC RID: 3516 RVA: 0x00058C14 File Offset: 0x00056E14
		// (set) Token: 0x06000DBD RID: 3517 RVA: 0x000061CE File Offset: 0x000043CE
		public string TextInput
		{
			get
			{
				return this.textBox_0.Text;
			}
			set
			{
				this.textBox_0.Text = value;
			}
		}

		// Token: 0x06000DBE RID: 3518 RVA: 0x000061DE File Offset: 0x000043DE
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000DBF RID: 3519 RVA: 0x00058C30 File Offset: 0x00056E30
		private void method_2()
		{
			this.textBox_0 = new TextBox();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.button_2 = new Button();
			this.fontDialog_0 = new FontDialog();
			this.button_3 = new Button();
			this.colorDialog_0 = new ColorDialog();
			base.SuspendLayout();
			this.textBox_0.AcceptsReturn = true;
			this.textBox_0.Location = new Point(25, 22);
			this.textBox_0.MaxLength = 512;
			this.textBox_0.Multiline = true;
			this.textBox_0.Name = Class521.smethod_0(7596);
			this.textBox_0.Size = new Size(419, 69);
			this.textBox_0.TabIndex = 0;
			this.button_0.Location = new Point(255, 106);
			this.button_0.Name = Class521.smethod_0(24224);
			this.button_0.Size = new Size(91, 32);
			this.button_0.TabIndex = 1;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Location = new Point(353, 106);
			this.button_1.Name = Class521.smethod_0(24207);
			this.button_1.Size = new Size(91, 32);
			this.button_1.TabIndex = 2;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.button_1.Click += this.button_1_Click;
			this.button_2.Location = new Point(123, 106);
			this.button_2.Name = Class521.smethod_0(24322);
			this.button_2.Size = new Size(91, 32);
			this.button_2.TabIndex = 3;
			this.button_2.Text = Class521.smethod_0(24335);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_2.Click += this.button_2_Click;
			this.button_3.Location = new Point(25, 106);
			this.button_3.Name = Class521.smethod_0(24185);
			this.button_3.Size = new Size(91, 32);
			this.button_3.TabIndex = 4;
			this.button_3.Text = Class521.smethod_0(24198);
			this.button_3.UseVisualStyleBackColor = true;
			this.button_3.Click += this.button_3_Click;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(470, 152);
			base.Controls.Add(this.button_3);
			base.Controls.Add(this.button_2);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.textBox_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(24344);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = FormStartPosition.CenterScreen;
			this.Text = Class521.smethod_0(24361);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040006FE RID: 1790
		[CompilerGenerated]
		private Delegate9 delegate9_0;

		// Token: 0x040006FF RID: 1791
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000700 RID: 1792
		private Color? nullable_0;

		// Token: 0x04000701 RID: 1793
		private object object_0;

		// Token: 0x04000702 RID: 1794
		private bool bool_0;

		// Token: 0x04000703 RID: 1795
		private DrawText drawText_0;

		// Token: 0x04000704 RID: 1796
		private IContainer icontainer_0;

		// Token: 0x04000705 RID: 1797
		private TextBox textBox_0;

		// Token: 0x04000706 RID: 1798
		private Button button_0;

		// Token: 0x04000707 RID: 1799
		private Button button_1;

		// Token: 0x04000708 RID: 1800
		private Button button_2;

		// Token: 0x04000709 RID: 1801
		private FontDialog fontDialog_0;

		// Token: 0x0400070A RID: 1802
		private Button button_3;

		// Token: 0x0400070B RID: 1803
		private ColorDialog colorDialog_0;
	}
}
