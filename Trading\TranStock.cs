﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns18;

namespace TEx.Trading
{
	// Token: 0x020003AE RID: 942
	[Serializable]
	internal sealed class TranStock : Transaction
	{
		// Token: 0x0600261A RID: 9754 RVA: 0x0000E916 File Offset: 0x0000CB16
		public TranStock()
		{
		}

		// Token: 0x0600261B RID: 9755 RVA: 0x001043BC File Offset: 0x001025BC
		protected TranStock(SerializationInfo info, StreamingContext context)
		{
			base.ID = info.GetInt32(Class521.smethod_0(109986));
			base.AcctID = info.GetInt32(Class521.smethod_0(110007));
			base.TransType = info.GetInt32(Class521.smethod_0(110036));
			base.SymbolID = info.GetInt32(Class521.smethod_0(110069));
			base.Price = info.GetDecimal(Class521.smethod_0(110098));
			base.Units = Convert.ToInt64(info.GetInt32(Class521.smethod_0(110123)));
			int? num = (int?)info.GetValue(Class521.smethod_0(110148), typeof(int?));
			if (num != null)
			{
				base.OpenUnits = new long?(Convert.ToInt64(num.Value));
			}
			base.Fee = (decimal?)info.GetValue(Class521.smethod_0(110181), typeof(decimal?));
			base.Profit = (decimal?)info.GetValue(Class521.smethod_0(110206), typeof(decimal?));
			base.ClosedTransID = (int?)info.GetValue(Class521.smethod_0(110235), typeof(int?));
			base.CreateTime = info.GetDateTime(Class521.smethod_0(110272));
			base.UpdateTime = (DateTime?)info.GetValue(Class521.smethod_0(110305), typeof(DateTime?));
			try
			{
				base.Notes = info.GetString(Class521.smethod_0(110338));
			}
			catch
			{
			}
			try
			{
				base.CreateTimeN = info.GetDateTime(Class521.smethod_0(110363));
				base.UpdateTimeN = (DateTime?)info.GetValue(Class521.smethod_0(110396), typeof(DateTime?));
			}
			catch
			{
			}
			this._OriPrice = info.GetDecimal(Class521.smethod_0(110429));
			this._OriUnits = Convert.ToInt64(info.GetInt32(Class521.smethod_0(110442)));
			this._IsRational = info.GetBoolean(Class521.smethod_0(110455));
			this._ProcessedStSplts = (List<StSplit>)info.GetValue(Class521.smethod_0(110472), typeof(List<StSplit>));
		}

		// Token: 0x0600261C RID: 9756 RVA: 0x00104628 File Offset: 0x00102828
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(109986), base.ID);
			info.AddValue(Class521.smethod_0(110007), base.AcctID);
			info.AddValue(Class521.smethod_0(110036), base.TransType);
			info.AddValue(Class521.smethod_0(110069), base.SymbolID);
			info.AddValue(Class521.smethod_0(110098), base.Price);
			int value;
			if (base.Units > 2147483647L)
			{
				value = int.MaxValue;
			}
			else
			{
				value = Convert.ToInt32(base.Units);
			}
			info.AddValue(Class521.smethod_0(110123), value);
			long? openUnits = base.OpenUnits;
			int value2;
			if (openUnits.GetValueOrDefault() > 2147483647L & openUnits != null)
			{
				value2 = int.MaxValue;
			}
			else
			{
				value2 = Convert.ToInt32(base.OpenUnits);
			}
			info.AddValue(Class521.smethod_0(110148), value2);
			info.AddValue(Class521.smethod_0(110181), base.Fee);
			info.AddValue(Class521.smethod_0(110206), base.Profit);
			info.AddValue(Class521.smethod_0(110235), base.ClosedTransID);
			info.AddValue(Class521.smethod_0(110338), base.Notes);
			info.AddValue(Class521.smethod_0(110272), base.CreateTime);
			info.AddValue(Class521.smethod_0(110305), base.UpdateTime);
			info.AddValue(Class521.smethod_0(110363), base.CreateTimeN);
			info.AddValue(Class521.smethod_0(110396), base.UpdateTimeN);
			int value3;
			if (this._OriUnits > 2147483647L)
			{
				value3 = int.MaxValue;
			}
			else
			{
				value3 = Convert.ToInt32(this._OriUnits);
			}
			info.AddValue(Class521.smethod_0(110442), value3);
			info.AddValue(Class521.smethod_0(110429), this._OriPrice);
			info.AddValue(Class521.smethod_0(110455), this._IsRational);
			info.AddValue(Class521.smethod_0(110472), this._ProcessedStSplts);
		}

		// Token: 0x1700066D RID: 1645
		// (get) Token: 0x0600261D RID: 9757 RVA: 0x00104874 File Offset: 0x00102A74
		// (set) Token: 0x0600261E RID: 9758 RVA: 0x0000E91E File Offset: 0x0000CB1E
		public decimal OriPrice
		{
			get
			{
				return this._OriPrice;
			}
			set
			{
				this._OriPrice = value;
			}
		}

		// Token: 0x1700066E RID: 1646
		// (get) Token: 0x0600261F RID: 9759 RVA: 0x0010488C File Offset: 0x00102A8C
		// (set) Token: 0x06002620 RID: 9760 RVA: 0x0000E929 File Offset: 0x0000CB29
		public long OriUnits
		{
			get
			{
				return this._OriUnits;
			}
			set
			{
				this._OriUnits = value;
			}
		}

		// Token: 0x1700066F RID: 1647
		// (get) Token: 0x06002621 RID: 9761 RVA: 0x001048A4 File Offset: 0x00102AA4
		// (set) Token: 0x06002622 RID: 9762 RVA: 0x0000E934 File Offset: 0x0000CB34
		public bool IsRational
		{
			get
			{
				return this._IsRational;
			}
			set
			{
				this._IsRational = value;
			}
		}

		// Token: 0x17000670 RID: 1648
		// (get) Token: 0x06002623 RID: 9763 RVA: 0x001048BC File Offset: 0x00102ABC
		// (set) Token: 0x06002624 RID: 9764 RVA: 0x0000E93F File Offset: 0x0000CB3F
		public List<StSplit> ProcessedStSplts
		{
			get
			{
				return this._ProcessedStSplts;
			}
			set
			{
				this._ProcessedStSplts = value;
			}
		}

		// Token: 0x06002625 RID: 9765 RVA: 0x001048D4 File Offset: 0x00102AD4
		public void method_0()
		{
			decimal d = (base.OpenUnits != null) ? (base.OpenUnits.Value / Convert.ToDecimal(base.Units)) : 1m;
			base.Units = Convert.ToInt64(Math.Round(this.OriUnits * d));
			base.OpenUnits = new long?(base.Units);
			base.Price = this.OriPrice;
			this.ProcessedStSplts = null;
		}

		// Token: 0x0400125F RID: 4703
		private decimal _OriPrice;

		// Token: 0x04001260 RID: 4704
		private long _OriUnits;

		// Token: 0x04001261 RID: 4705
		private bool _IsRational;

		// Token: 0x04001262 RID: 4706
		private List<StSplit> _ProcessedStSplts;
	}
}
