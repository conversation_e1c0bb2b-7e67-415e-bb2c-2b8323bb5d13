﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using ns30;
using TEx.Comn;

namespace ns10
{
	// Token: 0x0200013A RID: 314
	internal sealed class Class193
	{
		// Token: 0x06000CC1 RID: 3265 RVA: 0x00005B4A File Offset: 0x00003D4A
		public Class193(int int_1, IEnumerable<HDFileInfo> ienumerable_1, Class194 class194_1)
		{
			this.StkId = this.StkId;
			this.HDFileInfos = ienumerable_1;
			this.ApiParam = class194_1;
			this.FetchTime = DateTime.Now;
		}

		// Token: 0x17000204 RID: 516
		// (get) Token: 0x06000CC2 RID: 3266 RVA: 0x0004C2FC File Offset: 0x0004A4FC
		// (set) Token: 0x06000CC3 RID: 3267 RVA: 0x00005B79 File Offset: 0x00003D79
		public int StkId { get; set; }

		// Token: 0x17000205 RID: 517
		// (get) Token: 0x06000CC4 RID: 3268 RVA: 0x0004C314 File Offset: 0x0004A514
		// (set) Token: 0x06000CC5 RID: 3269 RVA: 0x00005B84 File Offset: 0x00003D84
		public IEnumerable<HDFileInfo> HDFileInfos { get; set; }

		// Token: 0x17000206 RID: 518
		// (get) Token: 0x06000CC6 RID: 3270 RVA: 0x0004C32C File Offset: 0x0004A52C
		// (set) Token: 0x06000CC7 RID: 3271 RVA: 0x00005B8F File Offset: 0x00003D8F
		public Class194 ApiParam { get; set; }

		// Token: 0x17000207 RID: 519
		// (get) Token: 0x06000CC8 RID: 3272 RVA: 0x0004C344 File Offset: 0x0004A544
		// (set) Token: 0x06000CC9 RID: 3273 RVA: 0x00005B9A File Offset: 0x00003D9A
		public DateTime FetchTime { get; set; }

		// Token: 0x04000541 RID: 1345
		[CompilerGenerated]
		private int int_0;

		// Token: 0x04000542 RID: 1346
		[CompilerGenerated]
		private IEnumerable<HDFileInfo> ienumerable_0;

		// Token: 0x04000543 RID: 1347
		[CompilerGenerated]
		private Class194 class194_0;

		// Token: 0x04000544 RID: 1348
		[CompilerGenerated]
		private DateTime dateTime_0;
	}
}
