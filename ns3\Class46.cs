﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using DevComponents.DotNetBar;
using ns17;
using ns18;

namespace ns3
{
	// Token: 0x02000059 RID: 89
	internal sealed class Class46
	{
		// Token: 0x06000318 RID: 792 RVA: 0x0001E9E4 File Offset: 0x0001CBE4
		public Class46(SuperTooltip superTooltip_0, string string_1, IComponent icomponent_1, SuperTooltipInfo superTooltipInfo_1, Enum0 enum0_1)
		{
			this.Name = string_1;
			this.Control = icomponent_1;
			this.SuperTooltipInfo = superTooltipInfo_1;
			Class47 @class = new Class47(icomponent_1);
			superTooltip_0.SetSuperTooltip(@class, superTooltipInfo_1);
			this.SuperTooltipProvider = @class;
			this.Location = enum0_1;
		}

		// Token: 0x170000BD RID: 189
		// (get) Token: 0x06000319 RID: 793 RVA: 0x0001EA30 File Offset: 0x0001CC30
		// (set) Token: 0x0600031A RID: 794 RVA: 0x00003574 File Offset: 0x00001774
		public string Name { get; set; }

		// Token: 0x170000BE RID: 190
		// (get) Token: 0x0600031B RID: 795 RVA: 0x0001EA48 File Offset: 0x0001CC48
		// (set) Token: 0x0600031C RID: 796 RVA: 0x0000357F File Offset: 0x0000177F
		public IComponent Control { get; set; }

		// Token: 0x170000BF RID: 191
		// (get) Token: 0x0600031D RID: 797 RVA: 0x0001EA60 File Offset: 0x0001CC60
		// (set) Token: 0x0600031E RID: 798 RVA: 0x0000358A File Offset: 0x0000178A
		public SuperTooltipInfo SuperTooltipInfo { get; set; }

		// Token: 0x170000C0 RID: 192
		// (get) Token: 0x0600031F RID: 799 RVA: 0x0001EA78 File Offset: 0x0001CC78
		// (set) Token: 0x06000320 RID: 800 RVA: 0x00003595 File Offset: 0x00001795
		public Class47 SuperTooltipProvider { get; set; }

		// Token: 0x170000C1 RID: 193
		// (get) Token: 0x06000321 RID: 801 RVA: 0x0001EA90 File Offset: 0x0001CC90
		// (set) Token: 0x06000322 RID: 802 RVA: 0x000035A0 File Offset: 0x000017A0
		public Enum0 Location { get; set; }

		// Token: 0x04000115 RID: 277
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000116 RID: 278
		[CompilerGenerated]
		private IComponent icomponent_0;

		// Token: 0x04000117 RID: 279
		[CompilerGenerated]
		private SuperTooltipInfo superTooltipInfo_0;

		// Token: 0x04000118 RID: 280
		[CompilerGenerated]
		private Class47 class47_0;

		// Token: 0x04000119 RID: 281
		[CompilerGenerated]
		private Enum0 enum0_0;
	}
}
