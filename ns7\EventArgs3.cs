﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using TEx;

namespace ns7
{
	// Token: 0x02000088 RID: 136
	internal sealed class EventArgs3 : EventArgs
	{
		// Token: 0x06000483 RID: 1155 RVA: 0x00003F18 File Offset: 0x00002118
		public EventArgs3(Indicator indicator_1, List<IndParam> list_1, ChartKLine chartKLine_1, ToolStripMenuItem toolStripMenuItem_1)
		{
			this.list_0 = list_1;
			this.indicator_0 = indicator_1;
			this.chartKLine_0 = chartKLine_1;
			this.toolStripMenuItem_0 = toolStripMenuItem_1;
		}

		// Token: 0x170000EA RID: 234
		// (get) Token: 0x06000484 RID: 1156 RVA: 0x0002491C File Offset: 0x00022B1C
		public List<IndParam> IndParamList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x170000EB RID: 235
		// (get) Token: 0x06000485 RID: 1157 RVA: 0x00024934 File Offset: 0x00022B34
		public Indicator Ind
		{
			get
			{
				return this.indicator_0;
			}
		}

		// Token: 0x170000EC RID: 236
		// (get) Token: 0x06000486 RID: 1158 RVA: 0x0002494C File Offset: 0x00022B4C
		public ChartKLine Chart
		{
			get
			{
				return this.chartKLine_0;
			}
		}

		// Token: 0x170000ED RID: 237
		// (get) Token: 0x06000487 RID: 1159 RVA: 0x00024964 File Offset: 0x00022B64
		public ToolStripMenuItem OwnerItem
		{
			get
			{
				return this.toolStripMenuItem_0;
			}
		}

		// Token: 0x040001B8 RID: 440
		private readonly List<IndParam> list_0;

		// Token: 0x040001B9 RID: 441
		private readonly Indicator indicator_0;

		// Token: 0x040001BA RID: 442
		private readonly ChartKLine chartKLine_0;

		// Token: 0x040001BB RID: 443
		private readonly ToolStripMenuItem toolStripMenuItem_0;
	}
}
