﻿using System;

namespace TEx.ImportTrans
{
	// Token: 0x02000369 RID: 873
	public sealed class CWrongUserName : IStoreElement
	{
		// Token: 0x17000639 RID: 1593
		// (get) Token: 0x06002486 RID: 9350 RVA: 0x000FE238 File Offset: 0x000FC438
		public string ID
		{
			get
			{
				return this.string_0 + this.string_1;
			}
		}

		// Token: 0x06002487 RID: 9351 RVA: 0x0000E3B9 File Offset: 0x0000C5B9
		public CWrongUserName(string name, string password, DateTime date)
		{
			this.string_0 = name;
			this.string_1 = password;
			this.dateTime_0 = date;
		}

		// Token: 0x040011A5 RID: 4517
		public string string_0;

		// Token: 0x040011A6 RID: 4518
		public string string_1;

		// Token: 0x040011A7 RID: 4519
		public DateTime dateTime_0;
	}
}
