﻿using System;
using ns18;
using TEx;
using TEx.Util;

namespace ns4
{
	// Token: 0x0200003C RID: 60
	internal sealed class Class20 : WebApiWorker
	{
		// Token: 0x060001B8 RID: 440 RVA: 0x0000315D File Offset: 0x0000135D
		public Class20() : base(Class20.string_0, 15000, false)
		{
		}

		// Token: 0x040000AF RID: 175
		private const int int_0 = 15000;

		// Token: 0x040000B0 RID: 176
		private static readonly string string_0 = TApp.FULLHOST + Class521.smethod_0(3606);
	}
}
