﻿using System;

namespace TEx.Trading
{
	// Token: 0x020003B8 RID: 952
	internal sealed class ShownSLOrder
	{
		// Token: 0x170006AB RID: 1707
		// (get) Token: 0x060026B5 RID: 9909 RVA: 0x00105DCC File Offset: 0x00103FCC
		// (set) Token: 0x060026B6 RID: 9910 RVA: 0x0000EE22 File Offset: 0x0000D022
		public int? ID
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x170006AC RID: 1708
		// (get) Token: 0x060026B7 RID: 9911 RVA: 0x00105DE4 File Offset: 0x00103FE4
		// (set) Token: 0x060026B8 RID: 9912 RVA: 0x0000EE2D File Offset: 0x0000D02D
		public int SymbID
		{
			get
			{
				return this.int_1;
			}
			set
			{
				this.int_1 = value;
			}
		}

		// Token: 0x170006AD RID: 1709
		// (get) Token: 0x060026B9 RID: 9913 RVA: 0x00105DFC File Offset: 0x00103FFC
		// (set) Token: 0x060026BA RID: 9914 RVA: 0x0000EE38 File Offset: 0x0000D038
		public string SymbCode
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170006AE RID: 1710
		// (get) Token: 0x060026BB RID: 9915 RVA: 0x00105E14 File Offset: 0x00104014
		// (set) Token: 0x060026BC RID: 9916 RVA: 0x0000EE43 File Offset: 0x0000D043
		public int TransID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170006AF RID: 1711
		// (get) Token: 0x060026BD RID: 9917 RVA: 0x00105E2C File Offset: 0x0010402C
		// (set) Token: 0x060026BE RID: 9918 RVA: 0x0000EE4E File Offset: 0x0000D04E
		public long Units
		{
			get
			{
				return this.long_0;
			}
			set
			{
				this.long_0 = value;
			}
		}

		// Token: 0x170006B0 RID: 1712
		// (get) Token: 0x060026BF RID: 9919 RVA: 0x00105E44 File Offset: 0x00104044
		// (set) Token: 0x060026C0 RID: 9920 RVA: 0x0000EE59 File Offset: 0x0000D059
		public decimal? StopPrice
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x170006B1 RID: 1713
		// (get) Token: 0x060026C1 RID: 9921 RVA: 0x00105E5C File Offset: 0x0010405C
		// (set) Token: 0x060026C2 RID: 9922 RVA: 0x0000EE64 File Offset: 0x0000D064
		public decimal? LimitPrice
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x170006B2 RID: 1714
		// (get) Token: 0x060026C3 RID: 9923 RVA: 0x00105E74 File Offset: 0x00104074
		// (set) Token: 0x060026C4 RID: 9924 RVA: 0x0000EE6F File Offset: 0x0000D06F
		public int? StopCondOdrId
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				this.nullable_3 = value;
			}
		}

		// Token: 0x170006B3 RID: 1715
		// (get) Token: 0x060026C5 RID: 9925 RVA: 0x00105E8C File Offset: 0x0010408C
		// (set) Token: 0x060026C6 RID: 9926 RVA: 0x0000EE7A File Offset: 0x0000D07A
		public int? LimitCondOdrId
		{
			get
			{
				return this.nullable_4;
			}
			set
			{
				this.nullable_4 = value;
			}
		}

		// Token: 0x170006B4 RID: 1716
		// (get) Token: 0x060026C7 RID: 9927 RVA: 0x00105EA4 File Offset: 0x001040A4
		// (set) Token: 0x060026C8 RID: 9928 RVA: 0x0000EE85 File Offset: 0x0000D085
		public int? TrailingStopCondOdrId
		{
			get
			{
				return this.nullable_5;
			}
			set
			{
				this.nullable_5 = value;
			}
		}

		// Token: 0x170006B5 RID: 1717
		// (get) Token: 0x060026C9 RID: 9929 RVA: 0x00105EBC File Offset: 0x001040BC
		// (set) Token: 0x060026CA RID: 9930 RVA: 0x0000EE90 File Offset: 0x0000D090
		public OrderType OrderType
		{
			get
			{
				return this.orderType_0;
			}
			set
			{
				this.orderType_0 = value;
			}
		}

		// Token: 0x170006B6 RID: 1718
		// (get) Token: 0x060026CB RID: 9931 RVA: 0x00105ED4 File Offset: 0x001040D4
		// (set) Token: 0x060026CC RID: 9932 RVA: 0x0000EE9B File Offset: 0x0000D09B
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				return this.comparisonOpt_0;
			}
			set
			{
				if (this.comparisonOpt_0 != value)
				{
					this.comparisonOpt_0 = value;
				}
			}
		}

		// Token: 0x170006B7 RID: 1719
		// (get) Token: 0x060026CD RID: 9933 RVA: 0x00105EEC File Offset: 0x001040EC
		// (set) Token: 0x060026CE RID: 9934 RVA: 0x00105F04 File Offset: 0x00104104
		public decimal? TrailingStopPts
		{
			get
			{
				return this.nullable_6;
			}
			set
			{
				decimal? num = this.nullable_6;
				decimal? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_6 = value;
				}
			}
		}

		// Token: 0x040012A3 RID: 4771
		private int? nullable_0;

		// Token: 0x040012A4 RID: 4772
		private int int_0;

		// Token: 0x040012A5 RID: 4773
		private int int_1;

		// Token: 0x040012A6 RID: 4774
		private string string_0;

		// Token: 0x040012A7 RID: 4775
		private long long_0;

		// Token: 0x040012A8 RID: 4776
		private decimal? nullable_1;

		// Token: 0x040012A9 RID: 4777
		private decimal? nullable_2;

		// Token: 0x040012AA RID: 4778
		private int? nullable_3;

		// Token: 0x040012AB RID: 4779
		private int? nullable_4;

		// Token: 0x040012AC RID: 4780
		private int? nullable_5;

		// Token: 0x040012AD RID: 4781
		private OrderType orderType_0;

		// Token: 0x040012AE RID: 4782
		private ComparisonOpt comparisonOpt_0;

		// Token: 0x040012AF RID: 4783
		private decimal? nullable_6;
	}
}
