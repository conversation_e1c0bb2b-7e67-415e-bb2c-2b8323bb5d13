﻿using System;
using ns10;
using ns12;
using ns18;
using ns20;
using ns26;
using ns7;
using ns9;
using TEx.SIndicator;

namespace ns29
{
	// Token: 0x02000311 RID: 785
	internal sealed class Class413 : Class412
	{
		// Token: 0x060021DD RID: 8669 RVA: 0x0000D9BF File Offset: 0x0000BBBF
		public Class413(HToken htoken_1) : base(htoken_1)
		{
		}

		// Token: 0x060021DE RID: 8670 RVA: 0x000F0658 File Offset: 0x000EE858
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			return base.vmethod_1(parserEnvironment_0);
		}

		// Token: 0x060021DF RID: 8671 RVA: 0x000F0670 File Offset: 0x000EE870
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			if (htoken.Symbol.HSymbolType != Enum26.const_5 && htoken.Symbol.HSymbolType != Enum26.const_2 && htoken.Symbol.HSymbolType != Enum26.const_4 && htoken.Symbol.HSymbolType != Enum26.const_34)
			{
				if (htoken.Symbol.HSymbolType != Enum26.const_38)
				{
					if (htoken.Symbol.HSymbolType == Enum26.const_0)
					{
						return new Class414(htoken);
					}
					if (htoken.Symbol.HSymbolType == Enum26.const_1)
					{
						return TreeFunction.smethod_0(tokenes_0);
					}
					if (htoken.Symbol.HSymbolType == Enum26.const_23)
					{
						tokenes_0.method_1();
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
						{
							throw new Exception(htoken.method_0(Class521.smethod_0(101624)));
						}
						Class411 @class = Class416.smethod_0(tokenes_0);
						tokenes_0.method_1();
						if (@class == null)
						{
							throw new Exception(htoken.method_0(Class521.smethod_0(101695)));
						}
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_24)
						{
							return @class;
						}
						throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(101670)));
					}
					else
					{
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
						{
							tokenes_0.Current.Symbol.method_0(Enum26.const_39);
							return Class420.smethod_0(tokenes_0);
						}
						throw new Exception(htoken.method_0(Class521.smethod_0(101720)));
					}
				}
			}
			return new Class413(htoken);
		}

		// Token: 0x060021E0 RID: 8672 RVA: 0x000F07FC File Offset: 0x000EE9FC
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x170005D4 RID: 1492
		// (get) Token: 0x060021E1 RID: 8673 RVA: 0x000F0814 File Offset: 0x000EEA14
		// (set) Token: 0x060021E2 RID: 8674 RVA: 0x0000D9C8 File Offset: 0x0000BBC8
		public override Class411 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005D5 RID: 1493
		// (get) Token: 0x060021E3 RID: 8675 RVA: 0x000F082C File Offset: 0x000EEA2C
		// (set) Token: 0x060021E4 RID: 8676 RVA: 0x0000D9D3 File Offset: 0x0000BBD3
		public override Class411 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005D6 RID: 1494
		// (get) Token: 0x060021E5 RID: 8677 RVA: 0x000F0844 File Offset: 0x000EEA44
		// (set) Token: 0x060021E6 RID: 8678 RVA: 0x0000D9DE File Offset: 0x0000BBDE
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}

		// Token: 0x060021E7 RID: 8679 RVA: 0x000F05F8 File Offset: 0x000EE7F8
		public string ToString()
		{
			return base.ToString();
		}
	}
}
