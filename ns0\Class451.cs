﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using TEx.ImportTrans;

namespace ns0
{
	// Token: 0x02000342 RID: 834
	internal sealed class Class451
	{
		// Token: 0x17000604 RID: 1540
		// (get) Token: 0x06002317 RID: 8983 RVA: 0x000F707C File Offset: 0x000F527C
		// (set) Token: 0x06002318 RID: 8984 RVA: 0x0000DD8A File Offset: 0x0000BF8A
		public string From { get; set; }

		// Token: 0x17000605 RID: 1541
		// (get) Token: 0x06002319 RID: 8985 RVA: 0x000F7094 File Offset: 0x000F5294
		// (set) Token: 0x0600231A RID: 8986 RVA: 0x0000DD95 File Offset: 0x0000BF95
		public string CfmmcAcctID { get; set; }

		// Token: 0x17000606 RID: 1542
		// (get) Token: 0x0600231B RID: 8987 RVA: 0x000F70AC File Offset: 0x000F52AC
		public List<TransData> Data
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x0600231C RID: 8988 RVA: 0x0000DDA0 File Offset: 0x0000BFA0
		public Class451(string string_3, string string_4)
		{
			this.From = string_3;
			this.CfmmcAcctID = string_4;
			this.list_0 = new List<TransData>();
		}

		// Token: 0x0600231D RID: 8989 RVA: 0x0000DDC3 File Offset: 0x0000BFC3
		public Class451(string string_3)
		{
			this.From = string_3;
			this.list_0 = new List<TransData>();
		}

		// Token: 0x17000607 RID: 1543
		// (get) Token: 0x0600231E RID: 8990 RVA: 0x000F70C4 File Offset: 0x000F52C4
		public bool HasData
		{
			get
			{
				bool result;
				if (this.Data != null)
				{
					result = this.Data.Any<TransData>();
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x040010F3 RID: 4339
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040010F4 RID: 4340
		[CompilerGenerated]
		private string string_1;

		// Token: 0x040010F5 RID: 4341
		private List<TransData> list_0;

		// Token: 0x040010F6 RID: 4342
		public static readonly string string_2 = Class521.smethod_0(104490);
	}
}
