﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using System.Xml.Linq;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Common;
using NAppUpdate.Framework.Sources;
using NAppUpdate.Framework.Tasks;
using Newtonsoft.Json;
using ns14;
using ns15;
using ns16;
using ns18;
using ns20;
using ns21;
using ns26;
using ns3;
using ns4;
using ns7;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200019B RID: 411
	internal sealed partial class LoginForm : Form1
	{
		// Token: 0x14000078 RID: 120
		// (add) Token: 0x06000FF2 RID: 4082 RVA: 0x00067AE0 File Offset: 0x00065CE0
		// (remove) Token: 0x06000FF3 RID: 4083 RVA: 0x00067B18 File Offset: 0x00065D18
		public event Delegate12 LoggedIn
		{
			[CompilerGenerated]
			add
			{
				Delegate12 @delegate = this.delegate12_0;
				Delegate12 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate12 value2 = (Delegate12)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate12>(ref this.delegate12_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate12 @delegate = this.delegate12_0;
				Delegate12 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate12 value2 = (Delegate12)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate12>(ref this.delegate12_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000FF4 RID: 4084 RVA: 0x00006CD2 File Offset: 0x00004ED2
		protected void method_1(EventArgs9 eventArgs9_0)
		{
			Delegate12 @delegate = this.delegate12_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs9_0);
			}
		}

		// Token: 0x14000079 RID: 121
		// (add) Token: 0x06000FF5 RID: 4085 RVA: 0x00067B50 File Offset: 0x00065D50
		// (remove) Token: 0x06000FF6 RID: 4086 RVA: 0x00067B88 File Offset: 0x00065D88
		public event EventHandler Quit
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000FF7 RID: 4087 RVA: 0x00006CE9 File Offset: 0x00004EE9
		protected void method_2(EventArgs eventArgs_0)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, eventArgs_0);
			}
		}

		// Token: 0x06000FF8 RID: 4088 RVA: 0x00067BC0 File Offset: 0x00065DC0
		public LoginForm()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_28();
			base.FormBorderStyle = FormBorderStyle.None;
			this.linkLabel_0.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_1.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_2.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_3.LinkBehavior = LinkBehavior.NeverUnderline;
			this.label_2.ForeColor = Class181.color_10;
			this.label_0.ForeColor = Class181.color_3;
			this.label_1.ForeColor = Class181.color_3;
			this.checkBox_0.ForeColor = Class181.color_3;
			this.class299_0 = new Class299(Color.White);
			this.class299_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
			this.class299_0.BackColor = Color.White;
			this.class299_0.Size = new Size(base.Width, 4);
			this.class299_0.Style = ProgressBarStyle.Continuous;
			this.class299_0.TabIndex = 12;
			base.Controls.Add(this.class299_0);
			this.class299_0.BarColor = Color.FromArgb(59, 148, 208);
			this.pictureBox_0.Image = Class375.LoginBannerNew;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9f, false);
				Font font = new Font(Class521.smethod_0(7183), emSize);
				this.label_0.Font = font;
				this.label_1.Font = font;
				this.linkLabel_0.Font = font;
				this.linkLabel_2.Font = font;
				this.linkLabel_3.Font = font;
				this.button_0.Font = font;
				this.checkBox_0.Font = font;
				font = new Font(Class521.smethod_0(4683), emSize);
				this.textBox_0.Font = font;
				this.maskedTextBox_0.Font = font;
				Font font2 = new Font(Class521.smethod_0(24023), TApp.smethod_4(8f, false));
				this.label_3.Font = font2;
				this.label_2.Font = font2;
				this.linkLabel_1.Font = new Font(Class521.smethod_0(24023), emSize);
			}
			this.Text = TApp.AppName;
		}

		// Token: 0x06000FF9 RID: 4089 RVA: 0x00067DF8 File Offset: 0x00065FF8
		private void LoginForm_Load(object sender, EventArgs e)
		{
			this.splashScreen_0 = new SplashScreen();
			this.splashScreen_0.UpdateDispMsg(Class521.smethod_0(29032));
			base.Icon = Class375.TExIcoBlue;
			this.label_2.Visible = false;
			this.class299_0.Visible = false;
			this.backgroundWorker_0 = new BackgroundWorker();
			this.backgroundWorker_0.WorkerReportsProgress = true;
			this.backgroundWorker_0.WorkerSupportsCancellation = true;
			this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
			this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
			this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
			UpdateManager instance = UpdateManager.Instance;
			instance.UpdateSource = new SimpleWebSource();
			instance.Config.TempFolder = Path.Combine(TApp.string_10, Class521.smethod_0(29061));
			instance.Config.BackupFolder = Path.Combine(TApp.string_10, Class521.smethod_0(9704) + DateTime.Now.Ticks);
			instance.ReinstateIfRestarted();
			this.label_3.Text = Class521.smethod_0(29074) + TApp.Ver;
			for (int i = 0; i < 2; i++)
			{
				if (this.label_3.Text.EndsWith(Class521.smethod_0(27670)))
				{
					this.label_3.Text = this.label_3.Text.Substring(0, this.label_3.Text.Length - 2);
				}
			}
			this.pictureBox_0.Location = new Point(0, 0);
			this.pictureBox_0.Width = base.Width;
			this.pictureBox_0.MouseDown += this.pictureBox_0_MouseDown;
			this.linkLabel_0.LinkClicked += this.linkLabel_0_LinkClicked;
			this.linkLabel_2.LinkClicked += this.linkLabel_2_LinkClicked;
			this.linkLabel_3.LinkClicked += this.linkLabel_3_LinkClicked;
			this.linkLabel_3.Parent = this.pictureBox_0;
			this.linkLabel_3.LinkColor = Class181.color_6;
			int x = this.pictureBox_0.Width - 230;
			if (TApp.IsHighDpiScreen)
			{
				x = this.pictureBox_0.Width - 190;
			}
			this.linkLabel_3.Location = new Point(x, 14);
			this.linkLabel_2.Parent = this.pictureBox_0;
			this.linkLabel_2.LinkColor = Class181.color_6;
			x = this.pictureBox_0.Width - 140;
			if (TApp.IsHighDpiScreen)
			{
				x = this.pictureBox_0.Width - 115;
			}
			this.linkLabel_2.Location = new Point(x, 14);
			this.label_3.Parent = this.pictureBox_0;
			this.label_3.BackColor = Color.Transparent;
			this.label_3.ForeColor = Class181.color_8;
			this.label_3.Location = new Point(this.pictureBox_0.Width - this.label_3.Width, this.pictureBox_0.Height - this.label_3.Height - this.linkLabel_1.Height + 1);
			this.linkLabel_1.Parent = this.pictureBox_0;
			this.linkLabel_1.BackColor = Color.Transparent;
			this.linkLabel_1.LinkColor = Class181.color_19;
			this.linkLabel_1.Location = new Point(this.pictureBox_0.Width - this.linkLabel_1.Width, this.pictureBox_0.Height - this.linkLabel_1.Height - 4);
			this.linkLabel_1.LinkClicked += this.linkLabel_1_LinkClicked;
			this.label_2.Parent = this.pictureBox_0;
			this.label_2.Location = new Point(39, this.pictureBox_0.Height - this.label_2.Height - 5);
			this.class299_0.Width = base.Width;
			this.class299_0.Location = new Point(0, this.pictureBox_0.Height);
			this.maskedTextBox_0.GotFocus += this.maskedTextBox_0_GotFocus;
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = 1500;
			this.timer_0.Tick += this.timer_0_Tick;
			this.class52_0.MouseClick += this.class52_0_MouseClick;
			base.KeyPreview = true;
			base.KeyDown += this.LoginForm_KeyDown;
			base.Activate();
			this.splashScreen_0.Close();
			this.splashScreen_0 = null;
			TApp.smethod_2();
			TApp.StartedUp = true;
		}

		// Token: 0x06000FFA RID: 4090 RVA: 0x00006D00 File Offset: 0x00004F00
		private void class52_0_MouseClick(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Left)
			{
				base.Close();
			}
		}

		// Token: 0x06000FFB RID: 4091 RVA: 0x0000407A File Offset: 0x0000227A
		private void pictureBox_0_MouseDown(object sender, MouseEventArgs e)
		{
			base.method_0(e);
		}

		// Token: 0x06000FFC RID: 4092 RVA: 0x00006D17 File Offset: 0x00004F17
		private void maskedTextBox_0_GotFocus(object sender, EventArgs e)
		{
			if (this.keys_0 == Keys.Tab)
			{
				this.maskedTextBox_0.SelectAll();
			}
		}

		// Token: 0x06000FFD RID: 4093 RVA: 0x000682D8 File Offset: 0x000664D8
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			Keys keys = (Keys)msg.WParam.ToInt32();
			this.keys_0 = keys;
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000FFE RID: 4094 RVA: 0x00068308 File Offset: 0x00066508
		private void LoginForm_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.button_0.Enabled)
			{
				if (e.KeyCode == Keys.F5)
				{
					if (MessageBox.Show(Class521.smethod_0(29079), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_52();
					}
				}
				else if (e.KeyCode == Keys.F6)
				{
					if (MessageBox.Show(Class521.smethod_0(29144), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_87();
					}
				}
				else if (e.KeyCode == Keys.F7)
				{
					if (MessageBox.Show(Class521.smethod_0(29209), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_140();
					}
				}
				else if (e.KeyCode == Keys.F8)
				{
					if (MessageBox.Show(Class521.smethod_0(29258), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						TApp.smethod_3(this.textBox_0.Text);
					}
				}
				else if (e.KeyCode == Keys.F9)
				{
					if (!string.IsNullOrEmpty(this.textBox_0.Text) && MessageBox.Show(Class521.smethod_0(29323), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_4();
					}
				}
				else if (e.Control && e.KeyCode == Keys.I)
				{
					MessageBox.Show(TApp.smethod_9(), Class521.smethod_0(27719), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
		}

		// Token: 0x06000FFF RID: 4095 RVA: 0x00068474 File Offset: 0x00066674
		private void LoginForm_Shown(object sender, EventArgs e)
		{
			string text = Class521.smethod_0(27874);
			this.linkLabel_1.Text = text;
			if (new DirectoryInfo(TApp.string_8).Exists)
			{
				LoginForm.Class233 @class = new LoginForm.Class233();
				@class.list_0 = TApp.smethod_6();
				if (@class.list_0 != null && @class.list_0.Any<KeyValuePair<string, DateTime>>())
				{
					string key = @class.list_0.SingleOrDefault(new Func<KeyValuePair<string, DateTime>, bool>(@class.method_0)).Key;
					if (!string.IsNullOrEmpty(key))
					{
						TApp.UserName = key;
					}
				}
			}
			else
			{
				DirectoryInfo directoryInfo = new DirectoryInfo(TApp.string_6);
				if (directoryInfo.Exists)
				{
					FileInfo fileInfo = new FileInfo(Path.Combine(directoryInfo.FullName, Class521.smethod_0(9316)));
					if (!fileInfo.Exists)
					{
						goto IL_338;
					}
					object obj = null;
					try
					{
						obj = Utility.DeserializeFile(fileInfo.FullName);
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
					if (obj == null)
					{
						goto IL_338;
					}
					try
					{
						FormUISettings formUISettings = (FormUISettings)obj;
						if (formUISettings != null)
						{
							TApp.UserName = formUISettings.UserID;
							Utility.CreateDir(TApp.string_8);
							string text2 = Path.Combine(TApp.string_8, TApp.UserName);
							Utility.CreateDir(text2);
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9444)), Path.Combine(text2, Class521.smethod_0(9444)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9457)), Path.Combine(text2, Class521.smethod_0(9457)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9329)), Path.Combine(text2, Class521.smethod_0(9329)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(4741)), Path.Combine(text2, Class521.smethod_0(4741)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9474)), Path.Combine(text2, Class521.smethod_0(9474)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9363)), Path.Combine(text2, Class521.smethod_0(9363)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9427)), Path.Combine(text2, Class521.smethod_0(9427)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9316)), Path.Combine(text2, Class521.smethod_0(9316)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9376)), Path.Combine(text2, Class521.smethod_0(9376)));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, Class521.smethod_0(9346)), Path.Combine(text2, Class521.smethod_0(9346)));
							Utility.DirectoryCopy(Path.Combine(TApp.string_6, Class521.smethod_0(9495)), Path.Combine(text2, Class521.smethod_0(9495)), true);
							this.textBox_0.Text = formUISettings.UserID;
							this.maskedTextBox_0.Text = formUISettings.Pswd;
						}
						goto IL_338;
					}
					catch (Exception exception_2)
					{
						Class184.smethod_0(exception_2);
						goto IL_338;
					}
				}
				TApp.IsFirstRun = true;
			}
			IL_338:
			if (Base.UI.Form.IfSaveUsrID && string.IsNullOrEmpty(this.textBox_0.Text) && !string.IsNullOrEmpty(Base.UI.Form.UserID))
			{
				this.textBox_0.Text = Base.UI.Form.UserID;
			}
			if (Base.UI.Form.IfSavePswd)
			{
				this.checkBox_0.Checked = true;
				if (string.IsNullOrEmpty(this.maskedTextBox_0.Text) && !string.IsNullOrEmpty(Base.UI.Form.Pswd))
				{
					this.maskedTextBox_0.Text = Base.UI.Form.Pswd;
				}
			}
			if (string.IsNullOrEmpty(this.textBox_0.Text))
			{
				this.textBox_0.Focus();
			}
			else if (string.IsNullOrEmpty(this.maskedTextBox_0.Text))
			{
				this.maskedTextBox_0.Focus();
			}
			else
			{
				this.button_0.Focus();
			}
		}

		// Token: 0x06001000 RID: 4096 RVA: 0x000688D0 File Offset: 0x00066AD0
		private void linkLabel_0_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			try
			{
				Process.Start(new ProcessStartInfo(Class521.smethod_0(29525)));
			}
			catch
			{
			}
		}

		// Token: 0x06001001 RID: 4097 RVA: 0x00006D30 File Offset: 0x00004F30
		private void linkLabel_3_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			Form5 form = new Form5(this.textBox_0.Text);
			form.ResetAllDataConfirmed += this.method_3;
			form.ShowDialog();
		}

		// Token: 0x06001002 RID: 4098 RVA: 0x00006D5C File Offset: 0x00004F5C
		private void method_3(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x06001003 RID: 4099 RVA: 0x0006890C File Offset: 0x00066B0C
		private void method_4()
		{
			string text = this.textBox_0.Text;
			if (!string.IsNullOrEmpty(text))
			{
				text = text.Trim();
				TApp.smethod_3(text);
				Utility.DeleteDir(TApp.string_8 + text);
				this.bool_0 = true;
			}
		}

		// Token: 0x06001004 RID: 4100 RVA: 0x00006D66 File Offset: 0x00004F66
		private void timer_0_Tick(object sender, EventArgs e)
		{
			if (!this.backgroundWorker_0.IsBusy)
			{
				this.method_23();
			}
			this.timer_0.Stop();
		}

		// Token: 0x06001005 RID: 4101 RVA: 0x00006D88 File Offset: 0x00004F88
		private void button_0_Click(object sender, EventArgs e)
		{
			this.textBox_0.Enabled = false;
			this.maskedTextBox_0.Enabled = false;
			this.button_0.Enabled = false;
			this.timer_0.Start();
			this.method_5();
		}

		// Token: 0x06001006 RID: 4102 RVA: 0x00068954 File Offset: 0x00066B54
		private void method_5()
		{
			if (string.IsNullOrEmpty(this.textBox_0.Text))
			{
				this.textBox_0.Focus();
			}
			else if (string.IsNullOrEmpty(this.maskedTextBox_0.Text))
			{
				this.maskedTextBox_0.Focus();
			}
			else
			{
				Keys modifierKeys = Control.ModifierKeys;
				this.button_0.Enabled = false;
				string text = this.textBox_0.Text.Trim();
				if (!string.IsNullOrEmpty(TApp.UserName) && !TApp.UserName.Equals(text, StringComparison.InvariantCultureIgnoreCase))
				{
					TApp.UserName = text;
					Base.UI.smethod_172();
				}
				this.backgroundWorker_0.RunWorkerAsync(this);
			}
		}

		// Token: 0x06001007 RID: 4103 RVA: 0x000689F8 File Offset: 0x00066BF8
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			LoginForm.Class234 @class = new LoginForm.Class234();
			string text = this.textBox_0.Text.Trim();
			TApp.UserName = text;
			Class48.smethod_3(Class24.AppLoggingIn, Class521.smethod_0(29574) + text);
			@class.backgroundWorker_0 = (sender as BackgroundWorker);
			object argument = e.Argument;
			@class.backgroundWorker_0.ReportProgress(2, Class521.smethod_0(29587));
			this.itexSrv_0 = ConnMgr.smethod_0();
			try
			{
				if (this.itexSrv_0 != null)
				{
					@class.backgroundWorker_0.ReportProgress(5, Class521.smethod_0(29624));
					string text2 = this.maskedTextBox_0.Text;
					string app = TApp.App;
					string text3 = string.Empty;
					bool flag;
					if (!(flag = (text.Equals(Class521.smethod_0(9268), StringComparison.InvariantCultureIgnoreCase) || text.Equals(Class521.smethod_0(9277), StringComparison.InvariantCultureIgnoreCase))))
					{
						this.method_6(@class.backgroundWorker_0, 5, Class521.smethod_0(29653));
						text3 = Class283.smethod_1(true);
						if (!string.IsNullOrEmpty(text3))
						{
							this.method_6(@class.backgroundWorker_0, 6, Class521.smethod_0(29678));
							text3 = Utility.ReplaceLowOrderASCIICharacters(text3);
						}
					}
					this.method_6(@class.backgroundWorker_0, 6, Class521.smethod_0(29707));
					string text4 = Class521.smethod_0(29732);
					try
					{
						text4 = Utility.GetOSDesc(true, false);
						TApp.OS = text4;
					}
					catch (Exception exception_)
					{
						Class48.smethod_4(exception_, true, null);
					}
					this.method_6(@class.backgroundWorker_0, 7, Class521.smethod_0(29749));
					string text5 = string.Empty;
					string text6 = string.Empty;
					try
					{
						foreach (ManagementBaseObject managementBaseObject in new ManagementObjectSearcher(new SelectQuery(Class521.smethod_0(29782))).Get())
						{
							ManagementObject managementObject = (ManagementObject)managementBaseObject;
							managementObject.Get();
							string text7 = managementObject.GetPropertyValue(Class521.smethod_0(29831)).ToString().Trim();
							if (!string.IsNullOrEmpty(text7))
							{
								text5 = text7;
								text6 = managementObject.GetPropertyValue(Class521.smethod_0(29848)).ToString().TrimEnd(new char[0]);
							}
							bool flag2 = text5.StartsWith(Class521.smethod_0(29857)) || text5.StartsWith(Class521.smethod_0(29862));
							if (text5 != string.Empty && !flag2)
							{
								break;
							}
						}
					}
					catch
					{
					}
					string text8 = Class283.smethod_4();
					string text9 = Class283.smethod_5();
					string userName = Environment.UserName;
					string text10 = this.method_27();
					Size size = TApp.smethod_5();
					int num = 0;
					int num2 = 0;
					try
					{
						Rectangle bounds = Screen.PrimaryScreen.Bounds;
						num = bounds.Width;
						num2 = bounds.Height;
					}
					catch (Exception exception_2)
					{
						Class184.smethod_0(exception_2);
					}
					object obj = string.Concat(new string[]
					{
						Class521.smethod_0(29891),
						text,
						Class521.smethod_0(29936),
						Utility.Encrypt(text2, TApp.string_12),
						Class521.smethod_0(29997),
						app,
						Class521.smethod_0(30030),
						text8,
						Class521.smethod_0(30063),
						text10,
						Class521.smethod_0(30096),
						TApp.DpiScale.ToString(Class521.smethod_0(30137), new NumberFormatInfo
						{
							PercentPositivePattern = 1,
							PercentNegativePattern = 1
						}),
						Class521.smethod_0(30142),
						TApp.WindowWidth.ToString(),
						Class521.smethod_0(30191),
						TApp.WindowHeight.ToString(),
						Class521.smethod_0(30248),
						(num == TApp.WindowWidth) ? Class521.smethod_0(1449) : (Class521.smethod_0(30273) + num.ToString() + Class521.smethod_0(30306)),
						(num2 == TApp.WindowHeight) ? Class521.smethod_0(1449) : (Class521.smethod_0(30331) + num2.ToString() + Class521.smethod_0(30364)),
						(TApp.WindowWidth == size.Width) ? Class521.smethod_0(1449) : (Class521.smethod_0(30389) + size.Width.ToString() + Class521.smethod_0(30426)),
						(TApp.WindowHeight == size.Height) ? Class521.smethod_0(1449) : (Class521.smethod_0(30455) + size.Height.ToString() + Class521.smethod_0(30492)),
						TApp.IsHighDpiScreen ? Class521.smethod_0(30521) : Class521.smethod_0(1449),
						Class521.smethod_0(30586),
						text5,
						Class521.smethod_0(30619),
						text6,
						Class521.smethod_0(30668),
						Enum.GetName(typeof(ComputerType), TApp.ComputerType),
						Class521.smethod_0(30717),
						text9,
						Class521.smethod_0(30770),
						text3,
						Class521.smethod_0(30823),
						userName,
						Class521.smethod_0(30876),
						text4,
						Class521.smethod_0(30905)
					});
					this.method_6(@class.backgroundWorker_0, 9, Class521.smethod_0(30934));
					byte[] values = Utility.GenCompressedBinaryArray(obj, CompressAlgm.LZMA, null, true);
					@class.backgroundWorker_0.ReportProgress(10, Class521.smethod_0(30975));
					string text11 = this.itexSrv_0.ValidateUser(TApp.Ver, values);
					if (!string.IsNullOrEmpty(text11))
					{
						if (text11.Contains(Class521.smethod_0(31004)))
						{
							ConnMgr.smethod_3();
							@class.backgroundWorker_0.ReportProgress(-1, text11);
						}
						else
						{
							LoginRslt loginRslt = JsonConvert.DeserializeObject<LoginRslt>(Utility.DecompressString(text11, CompressAlgm.LZMA));
							if (loginRslt != null && loginRslt.Validated)
							{
								@class.backgroundWorker_0.ReportProgress(15, Class521.smethod_0(31086));
								TApp.SrvParams = new SrvParams();
								TApp.UserName = text;
								TApp.LoginCode = loginRslt.LoginCode;
								Utility.CreateDir(TApp.UserAcctFolder);
								this.method_6(@class.backgroundWorker_0, 15, Class521.smethod_0(31115));
								XDocument xdocument = new XDocument();
								XElement xelement = new XElement(Class521.smethod_0(31180));
								xelement.Add(new XElement(Class521.smethod_0(9250), text));
								this.method_6(@class.backgroundWorker_0, 15, Class521.smethod_0(31193));
								string content = string.Empty;
								string text12 = Path.Combine(TApp.string_6, Class521.smethod_0(31238));
								FileInfo fi = new FileInfo(text12);
								this.method_6(@class.backgroundWorker_0, 15, Class521.smethod_0(31255));
								byte[] bytesFromFile = Utility.GetBytesFromFile(fi);
								this.method_6(@class.backgroundWorker_0, 16, Class521.smethod_0(31324));
								if (bytesFromFile != null && bytesFromFile.Length != 0)
								{
									content = Utility.GetChecksum(bytesFromFile);
								}
								xelement.Add(new XElement(Class521.smethod_0(31389), content));
								@class.backgroundWorker_0.ReportProgress(16, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 16, Class521.smethod_0(31402));
								string content2 = string.Empty;
								string text13 = Path.Combine(TApp.string_6, Class521.smethod_0(31443));
								byte[] bytesFromFile2 = Utility.GetBytesFromFile(new FileInfo(text13));
								if (bytesFromFile2 != null && bytesFromFile2.Length != 0)
								{
									content2 = Utility.GetChecksum(bytesFromFile2);
								}
								xelement.Add(new XElement(Class521.smethod_0(31460), content2));
								string content3 = string.Empty;
								string text14 = Path.Combine(TApp.string_6, Class521.smethod_0(31477));
								byte[] bytesFromFile3 = Utility.GetBytesFromFile(new FileInfo(text14));
								if (bytesFromFile3 != null && bytesFromFile3.Length != 0)
								{
									content3 = Utility.GetChecksum(bytesFromFile3);
								}
								xelement.Add(new XElement(Class521.smethod_0(31494), content3));
								@class.backgroundWorker_0.ReportProgress(17, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 17, Class521.smethod_0(31511));
								string content4 = string.Empty;
								string text15 = Path.Combine(TApp.string_6, Class521.smethod_0(31556));
								byte[] bytesFromFile4 = Utility.GetBytesFromFile(new FileInfo(text15));
								if (bytesFromFile4 != null && bytesFromFile4.Length != 0)
								{
									content4 = Utility.GetChecksum(bytesFromFile4);
								}
								xelement.Add(new XElement(Class521.smethod_0(31573), content4));
								this.method_6(@class.backgroundWorker_0, 17, Class521.smethod_0(31590));
								string content5 = string.Empty;
								string text16 = Path.Combine(TApp.string_6, Class521.smethod_0(31635));
								byte[] bytesFromFile5 = Utility.GetBytesFromFile(new FileInfo(text16));
								if (bytesFromFile5 != null && bytesFromFile5.Length != 0)
								{
									content5 = Utility.GetChecksum(bytesFromFile5);
								}
								xelement.Add(new XElement(Class521.smethod_0(31652), content5));
								@class.backgroundWorker_0.ReportProgress(18, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 18, Class521.smethod_0(31677));
								string content6 = string.Empty;
								string text17 = Path.Combine(TApp.string_6, Class521.smethod_0(31734));
								byte[] bytesFromFile6 = Utility.GetBytesFromFile(new FileInfo(text17));
								if (bytesFromFile6 != null && bytesFromFile6.Length != 0)
								{
									content6 = Utility.GetChecksum(bytesFromFile6);
								}
								xelement.Add(new XElement(Class521.smethod_0(31751), content6));
								@class.backgroundWorker_0.ReportProgress(19, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 19, Class521.smethod_0(31768));
								string content7 = string.Empty;
								string text18 = Path.Combine(TApp.string_6, Class521.smethod_0(31825));
								byte[] bytesFromFile7 = Utility.GetBytesFromFile(new FileInfo(text18));
								if (bytesFromFile7 != null && bytesFromFile7.Length != 0)
								{
									content7 = Utility.GetChecksum(bytesFromFile7);
								}
								xelement.Add(new XElement(Class521.smethod_0(31842), content7));
								@class.backgroundWorker_0.ReportProgress(20, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 20, Class521.smethod_0(31859));
								string content8 = string.Empty;
								string text19 = Path.Combine(TApp.string_6, Class521.smethod_0(31904));
								byte[] bytesFromFile8 = Utility.GetBytesFromFile(new FileInfo(text19));
								if (bytesFromFile8 != null && bytesFromFile8.Length != 0)
								{
									content8 = Utility.GetChecksum(bytesFromFile8);
								}
								xelement.Add(new XElement(Class521.smethod_0(31921), content8));
								@class.backgroundWorker_0.ReportProgress(21, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 21, Class521.smethod_0(31942));
								string content9 = string.Empty;
								string text20 = Path.Combine(TApp.string_6, Class521.smethod_0(31987));
								byte[] bytesFromFile9 = Utility.GetBytesFromFile(new FileInfo(text20));
								if (bytesFromFile9 != null && bytesFromFile9.Length != 0)
								{
									content9 = Utility.GetChecksum(bytesFromFile9);
								}
								xelement.Add(new XElement(Class521.smethod_0(32004), content9));
								this.method_6(@class.backgroundWorker_0, 21, Class521.smethod_0(32025));
								string content10 = string.Empty;
								string text21 = Path.Combine(TApp.string_6, Class521.smethod_0(32070));
								byte[] bytesFromFile10 = Utility.GetBytesFromFile(new FileInfo(text21));
								if (bytesFromFile10 != null && bytesFromFile10.Length != 0)
								{
									content10 = Utility.GetChecksum(bytesFromFile10);
								}
								xelement.Add(new XElement(Class521.smethod_0(32087), content10));
								this.method_6(@class.backgroundWorker_0, 21, Class521.smethod_0(32108));
								string content11 = string.Empty;
								string text22 = Path.Combine(TApp.string_6, Class521.smethod_0(32153));
								byte[] bytesFromFile11 = Utility.GetBytesFromFile(new FileInfo(text22));
								if (bytesFromFile11 != null && bytesFromFile11.Length != 0)
								{
									content11 = Utility.GetChecksum(bytesFromFile11);
								}
								xelement.Add(new XElement(Class521.smethod_0(32170), content11));
								@class.backgroundWorker_0.ReportProgress(22, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 22, Class521.smethod_0(32191));
								string content12 = string.Empty;
								string text23 = Path.Combine(TApp.string_6, Class521.smethod_0(32236));
								byte[] bytesFromFile12 = Utility.GetBytesFromFile(new FileInfo(text23));
								if (bytesFromFile12 != null && bytesFromFile12.Length != 0)
								{
									content12 = Utility.GetChecksum(bytesFromFile12);
								}
								xelement.Add(new XElement(Class521.smethod_0(32253), content12));
								@class.backgroundWorker_0.ReportProgress(23, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 23, Class521.smethod_0(32274));
								string content13 = string.Empty;
								string text24 = Path.Combine(TApp.string_6, Class521.smethod_0(32319));
								byte[] bytesFromFile13 = Utility.GetBytesFromFile(new FileInfo(text24));
								if (bytesFromFile13 != null && bytesFromFile13.Length != 0)
								{
									content13 = Utility.GetChecksum(bytesFromFile13);
								}
								xelement.Add(new XElement(Class521.smethod_0(32336), content13));
								this.method_6(@class.backgroundWorker_0, 23, Class521.smethod_0(32357));
								string content14 = string.Empty;
								string text25 = Path.Combine(TApp.string_6, Class521.smethod_0(32398));
								byte[] bytesFromFile14 = Utility.GetBytesFromFile(new FileInfo(text25));
								if (bytesFromFile14 != null && bytesFromFile14.Length != 0)
								{
									content14 = Utility.GetChecksum(bytesFromFile14);
								}
								xelement.Add(new XElement(Class521.smethod_0(32411), content14));
								@class.backgroundWorker_0.ReportProgress(24, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 24, Class521.smethod_0(32428));
								string content15 = string.Empty;
								string text26 = Path.Combine(TApp.string_6, Class521.smethod_0(32469));
								byte[] bytesFromFile15 = Utility.GetBytesFromFile(new FileInfo(text26));
								if (bytesFromFile15 != null && bytesFromFile15.Length != 0)
								{
									content15 = Utility.GetChecksum(bytesFromFile15);
								}
								xelement.Add(new XElement(Class521.smethod_0(32482), content15));
								this.method_6(@class.backgroundWorker_0, 24, Class521.smethod_0(32499));
								string content16 = string.Empty;
								string text27 = Path.Combine(TApp.string_6, Class521.smethod_0(32540));
								byte[] bytesFromFile16 = Utility.GetBytesFromFile(new FileInfo(text27));
								if (bytesFromFile16 != null && bytesFromFile16.Length != 0)
								{
									content16 = Utility.GetChecksum(bytesFromFile16);
								}
								xelement.Add(new XElement(Class521.smethod_0(32553), content16));
								@class.backgroundWorker_0.ReportProgress(25, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 25, Class521.smethod_0(32570));
								string content17 = string.Empty;
								string text28 = Path.Combine(TApp.string_6, Class521.smethod_0(32611));
								byte[] bytesFromFile17 = Utility.GetBytesFromFile(new FileInfo(text28));
								if (bytesFromFile17 != null && bytesFromFile17.Length != 0)
								{
									content17 = Utility.GetChecksum(bytesFromFile17);
								}
								xelement.Add(new XElement(Class521.smethod_0(32624), content17));
								@class.backgroundWorker_0.ReportProgress(26, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 26, Class521.smethod_0(32641));
								string content18 = string.Empty;
								string text29 = Path.Combine(TApp.string_6, Class521.smethod_0(32682));
								byte[] bytesFromFile18 = Utility.GetBytesFromFile(new FileInfo(text29));
								if (bytesFromFile18 != null && bytesFromFile18.Length != 0)
								{
									content18 = Utility.GetChecksum(bytesFromFile18);
								}
								xelement.Add(new XElement(Class521.smethod_0(32695), content18));
								this.method_6(@class.backgroundWorker_0, 26, Class521.smethod_0(32712));
								string content19 = string.Empty;
								string text30 = Path.Combine(TApp.string_6, Class521.smethod_0(32753));
								byte[] bytesFromFile19 = Utility.GetBytesFromFile(new FileInfo(text30));
								if (bytesFromFile19 != null && bytesFromFile19.Length != 0)
								{
									content19 = Utility.GetChecksum(bytesFromFile19);
								}
								xelement.Add(new XElement(Class521.smethod_0(32766), content19));
								@class.backgroundWorker_0.ReportProgress(27, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 27, Class521.smethod_0(32783));
								string content20 = string.Empty;
								string text31 = Path.Combine(TApp.string_6, Class521.smethod_0(32840));
								byte[] bytesFromFile20 = Utility.GetBytesFromFile(new FileInfo(text31));
								if (bytesFromFile20 != null && bytesFromFile20.Length != 0)
								{
									content20 = Utility.GetChecksum(bytesFromFile20);
								}
								xelement.Add(new XElement(Class521.smethod_0(32857), content20));
								@class.backgroundWorker_0.ReportProgress(28, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 28, Class521.smethod_0(32870));
								string content21 = string.Empty;
								string string_ = Path.Combine(TApp.string_6, Class521.smethod_0(32915));
								byte[] bytesFromFile21 = Utility.GetBytesFromFile(new FileInfo(text31));
								if (bytesFromFile21 != null && bytesFromFile21.Length != 0)
								{
									content21 = Utility.GetChecksum(bytesFromFile21);
								}
								xelement.Add(new XElement(Class521.smethod_0(32932), content21));
								this.method_6(@class.backgroundWorker_0, 28, Class521.smethod_0(32949));
								string content22 = string.Empty;
								string text32 = Path.Combine(TApp.string_6, Class521.smethod_0(32994));
								byte[] bytesFromFile22 = Utility.GetBytesFromFile(new FileInfo(text32));
								if (bytesFromFile22 != null && bytesFromFile22.Length != 0)
								{
									content22 = Utility.GetChecksum(bytesFromFile22);
								}
								xelement.Add(new XElement(Class521.smethod_0(33011), content22));
								this.method_6(@class.backgroundWorker_0, 28, Class521.smethod_0(33024));
								bool flag3 = false;
								if (this.bool_0)
								{
									XElement content23 = new XElement(Class521.smethod_0(33089));
									xelement.Add(content23);
								}
								else if (flag3 = (!flag && !Base.UI.Form.BackupSyncDisabled && !Base.UI.Form.BackupSyncNotOnStartup))
								{
									XElement xelement2 = new XElement(Class521.smethod_0(9237));
									xelement2 = BkupSyncMgr.smethod_12(xelement2);
									xelement.Add(xelement2);
								}
								xdocument.Add(xelement);
								@class.backgroundWorker_0.ReportProgress(29, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 29, Class521.smethod_0(33106));
								byte[] info = Utility.GenCompressedBinaryArray(Utility.ConvertXMLDocToString(xdocument), CompressAlgm.LZMA, null, true);
								@class.backgroundWorker_0.ReportProgress(30, Class521.smethod_0(33155));
								List<SrvParam> list = Utility.DeserializeBytes(this.itexSrv_0.GetSrvParams(TApp.Ver, info), SerializationType.BinaryFormatter) as List<SrvParam>;
								@class.backgroundWorker_0.ReportProgress(35, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 35, Class521.smethod_0(33200));
								TExPackage? texPackage = new TExPackage?(TExPackage.TRL);
								object obj2 = this.method_13(list, Class521.smethod_0(33269), Class521.smethod_0(33286));
								if (obj2 != null)
								{
									texPackage = new TExPackage?((TExPackage)Convert.ToInt32(obj2));
								}
								TApp.SrvParams.TExPkg = texPackage;
								XDocument xdocument2 = this.method_12(list, Class521.smethod_0(33291), Class521.smethod_0(33286));
								AppUpdInfo appUpdInfo = new AppUpdInfo();
								if (xdocument2 != null)
								{
									XElement xelement3 = xdocument2.Element(Class521.smethod_0(33291));
									appUpdInfo.Feed = xelement3.Element(Class521.smethod_0(11816)).Value;
									appUpdInfo.DateTime = Convert.ToDateTime(xelement3.Element(Class521.smethod_0(33308)).Value);
									appUpdInfo.Ver = xelement3.Element(Class521.smethod_0(33321)).Value;
									appUpdInfo.Notes = xelement3.Element(Class521.smethod_0(33326)).Value;
								}
								TApp.SrvParams.AppUpdInfo = appUpdInfo;
								XDocument xdocument3 = this.method_12(list, Class521.smethod_0(33335), Class521.smethod_0(33356));
								LogonNoticeInfo logonNoticeInfo = new LogonNoticeInfo();
								if (xdocument3 != null)
								{
									XElement xelement4 = xdocument3.Element(Class521.smethod_0(33335));
									logonNoticeInfo.HTMLUrl = xelement4.Element(Class521.smethod_0(33361)).Value.Replace(TApp.string_13, TApp.FULLHOST);
									logonNoticeInfo.DateTime = Convert.ToDateTime(xelement4.Element(Class521.smethod_0(33308)).Value);
									logonNoticeInfo.Notes = xelement4.Element(Class521.smethod_0(33326)).Value;
								}
								TApp.SrvParams.LogonNoticeInfo = logonNoticeInfo;
								string infoHTML = this.method_13(list, Class521.smethod_0(33374), Class521.smethod_0(33286)) as string;
								bool isNewMachine = Convert.ToBoolean(Convert.ToInt32(this.method_13(list, Class521.smethod_0(33391), Class521.smethod_0(33286))));
								bool isFirstLogin = Convert.ToBoolean(Convert.ToInt32(this.method_13(list, Class521.smethod_0(33408), Class521.smethod_0(33286))));
								TApp.SrvParams.InfoHTML = infoHTML;
								TApp.SrvParams.IsNewMachine = isNewMachine;
								TApp.SrvParams.IsFirstLogin = isFirstLogin;
								object obj3 = this.method_13(list, Class521.smethod_0(33425), Class521.smethod_0(33356));
								if (obj3 != null)
								{
									TApp.SrvParams.MinHDExpPeriodUnits = Convert.ToInt32(obj3);
								}
								else
								{
									TApp.SrvParams.MinHDExpPeriodUnits = 5;
								}
								bool isTrialUser = TApp.IsTrialUser;
								@class.backgroundWorker_0.ReportProgress(36, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 36, Class521.smethod_0(33454));
								XDocument xdocument4 = this.method_10(list, Class521.smethod_0(33523), Class521.smethod_0(33286), text12, bytesFromFile);
								List<ExchgHouse> list2 = new List<ExchgHouse>();
								if (xdocument4 != null)
								{
									foreach (XElement xelement5 in xdocument4.Element(Class521.smethod_0(33523)).Elements(Class521.smethod_0(33540)))
									{
										list2.Add(new ExchgHouse
										{
											ID = Convert.ToInt32(xelement5.Attribute(Class521.smethod_0(33549)).Value),
											Name_EN = xelement5.Attribute(Class521.smethod_0(33554)).Value,
											Name_CN = xelement5.Attribute(Class521.smethod_0(16303)).Value,
											AbbrName_EN = xelement5.Attribute(Class521.smethod_0(33567)).Value,
											AbbrName_CN = xelement5.Attribute(Class521.smethod_0(33584)).Value,
											TableNamePrefix = xelement5.Attribute(Class521.smethod_0(33601)).Value,
											Country = xelement5.Attribute(Class521.smethod_0(33622)).Value
										});
									}
								}
								TApp.SrvParams.ExchgHsList = list2;
								this.method_6(@class.backgroundWorker_0, 36, Class521.smethod_0(33635));
								XDocument xdocument_ = this.method_10(list, Class521.smethod_0(33704), Class521.smethod_0(33286), text13, bytesFromFile2);
								XDocument xdocument_2 = this.method_10(list, Class521.smethod_0(33725), Class521.smethod_0(33746), text14, bytesFromFile3);
								List<TradingSymbol> list3 = new List<TradingSymbol>();
								List<TradingSymbol> list4 = this.method_18(xdocument_);
								if (list4 != null && list4.Any<TradingSymbol>())
								{
									list3.AddRange(list4);
								}
								List<TradingSymbol> list5 = this.method_18(xdocument_2);
								if (list5 != null && list5.Any<TradingSymbol>())
								{
									list3.AddRange(list5);
								}
								TApp.SrvParams.MstSymblList = list3;
								@class.backgroundWorker_0.ReportProgress(37, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 37, Class521.smethod_0(33751));
								XDocument xdocument5 = this.method_10(list, Class521.smethod_0(33820), Class521.smethod_0(33286), text15, bytesFromFile4);
								List<ExchgOBT> list6 = new List<ExchgOBT>();
								if (xdocument5 != null)
								{
									foreach (XElement xelement6 in xdocument5.Element(Class521.smethod_0(33820)).Elements(Class521.smethod_0(33833)))
									{
										ExchgOBT exchgOBT = new ExchgOBT();
										exchgOBT.ID = Convert.ToInt32(xelement6.Attribute(Class521.smethod_0(33549)).Value);
										exchgOBT.ExchgID = Convert.ToInt32(xelement6.Attribute(Class521.smethod_0(33846)).Value);
										string value = xelement6.Attribute(Class521.smethod_0(33859)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.FromDate = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33872)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.ToDate = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33881)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.DayOpenTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33898)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.DayCloseTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33915)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.AMRestStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33936)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.AMRestEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33957)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NoonBreakStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(33982)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NoonBreakEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(34007)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.PMRestStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(34028)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.PMRestEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(34049)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NightOpenTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute(Class521.smethod_0(34070)).Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NightCloseTime = new DateTime?(Convert.ToDateTime(value));
										}
										list6.Add(exchgOBT);
									}
								}
								TApp.SrvParams.ExchgOBTList = list6;
								this.method_6(@class.backgroundWorker_0, 37, Class521.smethod_0(34091));
								XDocument xdocument6 = this.method_10(list, Class521.smethod_0(34164), Class521.smethod_0(33286), text16, bytesFromFile5);
								List<SymbNtTrDate> list7 = new List<SymbNtTrDate>();
								if (xdocument6 != null)
								{
									foreach (XElement xelement7 in xdocument6.Element(Class521.smethod_0(34164)).Elements(Class521.smethod_0(34185)))
									{
										SymbNtTrDate symbNtTrDate = new SymbNtTrDate();
										symbNtTrDate.SymbID = Convert.ToInt32(xelement7.Attribute(Class521.smethod_0(34202)).Value);
										symbNtTrDate.ExchgOBTID = Convert.ToInt32(xelement7.Attribute(Class521.smethod_0(34211)).Value);
										string value2 = xelement7.Attribute(Class521.smethod_0(33859)).Value;
										if (!string.IsNullOrEmpty(value2))
										{
											symbNtTrDate.FromDate = new DateTime?(Convert.ToDateTime(value2));
										}
										value2 = xelement7.Attribute(Class521.smethod_0(33872)).Value;
										if (!string.IsNullOrEmpty(value2))
										{
											symbNtTrDate.ToDate = new DateTime?(Convert.ToDateTime(value2));
										}
										list7.Add(symbNtTrDate);
									}
								}
								TApp.SrvParams.SymbNtTrDateList = list7;
								@class.backgroundWorker_0.ReportProgress(38, Class521.smethod_0(1449));
								string string_2 = null;
								string string_3 = null;
								string string_4 = null;
								string string_5 = null;
								string string_6 = null;
								string string_7 = null;
								@class.backgroundWorker_0.ReportProgress(38, Class521.smethod_0(34228));
								this.method_6(@class.backgroundWorker_0, 38, Class521.smethod_0(34273));
								if (isTrialUser)
								{
									string_2 = this.method_11(list, Class521.smethod_0(34318), Class521.smethod_0(34335), text19, bytesFromFile8, true);
									@class.backgroundWorker_0.ReportProgress(41, Class521.smethod_0(1449));
									string_5 = this.method_11(list, Class521.smethod_0(34340), Class521.smethod_0(34335), text22, bytesFromFile11, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_2 = this.method_11(list, Class521.smethod_0(34357), Class521.smethod_0(34335), text25, bytesFromFile14, true);
									}
									@class.backgroundWorker_0.ReportProgress(41, Class521.smethod_0(1449));
									if (TApp.IsStIncluded)
									{
										string_5 = this.method_11(list, Class521.smethod_0(34370), Class521.smethod_0(34335), text28, bytesFromFile17, true);
									}
								}
								@class.backgroundWorker_0.ReportProgress(46, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 46, Class521.smethod_0(34383));
								if (isTrialUser)
								{
									string_3 = this.method_11(list, Class521.smethod_0(34428), Class521.smethod_0(34335), text20, bytesFromFile9, true);
									@class.backgroundWorker_0.ReportProgress(47, Class521.smethod_0(1449));
									string_6 = this.method_11(list, Class521.smethod_0(34445), Class521.smethod_0(34335), text23, bytesFromFile12, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_3 = this.method_11(list, Class521.smethod_0(34462), Class521.smethod_0(34335), text26, bytesFromFile15, true);
									}
									@class.backgroundWorker_0.ReportProgress(47, Class521.smethod_0(1449));
									if (TApp.IsStIncluded)
									{
										string_6 = this.method_11(list, Class521.smethod_0(34475), Class521.smethod_0(34335), text29, bytesFromFile18, true);
									}
								}
								@class.backgroundWorker_0.ReportProgress(49, Class521.smethod_0(34488));
								this.method_6(@class.backgroundWorker_0, 49, Class521.smethod_0(34541));
								if (isTrialUser)
								{
									string_4 = this.method_11(list, Class521.smethod_0(34586), Class521.smethod_0(34335), text21, bytesFromFile10, true);
									string_7 = this.method_11(list, Class521.smethod_0(34603), Class521.smethod_0(34335), text24, bytesFromFile13, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_4 = this.method_11(list, Class521.smethod_0(34620), Class521.smethod_0(34335), text27, bytesFromFile16, true);
									}
									if (TApp.IsStIncluded)
									{
										string_7 = this.method_11(list, Class521.smethod_0(34637), Class521.smethod_0(34335), text30, bytesFromFile19, true);
									}
								}
								List<HDFileInfo> list8 = new List<HDFileInfo>();
								@class.backgroundWorker_0.ReportProgress(50, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 50, Class521.smethod_0(34654));
								HDFileMgr.smethod_14(string_2, list8);
								HDFileMgr.smethod_14(string_5, list8);
								@class.backgroundWorker_0.ReportProgress(54, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 54, Class521.smethod_0(34711));
								HDFileMgr.smethod_14(string_3, list8);
								HDFileMgr.smethod_14(string_6, list8);
								@class.backgroundWorker_0.ReportProgress(56, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 56, Class521.smethod_0(34768));
								HDFileMgr.smethod_14(string_4, list8);
								HDFileMgr.smethod_14(string_7, list8);
								TApp.SrvParams.HFileInfoList = list8;
								object obj4 = this.method_13(list, Class521.smethod_0(34825), Class521.smethod_0(34335));
								if (obj4 != null)
								{
									TApp.SrvParams.GetHDFileInfoFromSrvApi = Convert.ToBoolean(obj4);
								}
								@class.backgroundWorker_0.ReportProgress(57, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 57, Class521.smethod_0(34858));
								List<UsrStkMeta> list9;
								if (isTrialUser)
								{
									int[] int_ = this.method_13(list, Class521.smethod_0(34919), Class521.smethod_0(33356)) as int[];
									DateTime? nullable_ = null;
									object obj5 = this.method_13(list, Class521.smethod_0(34944), Class521.smethod_0(33356));
									if (obj5 != null)
									{
										nullable_ = new DateTime?(Convert.ToDateTime(obj5));
									}
									DateTime dateTime = Convert.ToDateTime(this.method_13(list, Class521.smethod_0(34973), Class521.smethod_0(34335)));
									if (nullable_ != null)
									{
										DateTime now = DateTime.Now;
										if (dateTime.Year != now.Year || dateTime.Month != now.Month)
										{
											int num3 = now.Year * 12 + now.Month - (dateTime.Year * 12 + dateTime.Month);
											if (num3 > 0)
											{
												nullable_ = new DateTime?(nullable_.Value.AddMonths(-num3));
											}
										}
									}
									DateTime? nullable_2 = null;
									object obj6 = this.method_13(list, Class521.smethod_0(35002), Class521.smethod_0(33356));
									if (obj6 != null)
									{
										nullable_2 = new DateTime?(Convert.ToDateTime(obj6));
									}
									list9 = this.method_16(int_, list8, nullable_, nullable_2);
									int[] int_2 = this.method_13(list, Class521.smethod_0(35031), Class521.smethod_0(33746)) as int[];
									List<UsrStkMeta> collection = this.method_16(int_2, list8, nullable_, nullable_2);
									list9.AddRange(collection);
								}
								else
								{
									XDocument xdocument_3 = this.method_12(list, Class521.smethod_0(35056), Class521.smethod_0(33356));
									list9 = this.method_15(xdocument_3);
								}
								TApp.SrvParams.UsrStkMetaList = list9;
								@class.backgroundWorker_0.ReportProgress(59, Class521.smethod_0(35073));
								this.method_6(@class.backgroundWorker_0, 59, Class521.smethod_0(35126));
								if (TApp.IsFtIncluded)
								{
									string string_8 = this.method_11(list, Class521.smethod_0(35199), Class521.smethod_0(34335), text17, bytesFromFile6, true);
									Dictionary<int, StkSymbol> dictionary_ = this.method_8(string_8);
									this.method_9(dictionary_);
									this.method_7(dictionary_, texPackage, list9, list3, true);
								}
								if (TApp.IsStIncluded)
								{
									string string_9 = this.method_11(list, Class521.smethod_0(35212), Class521.smethod_0(34335), text18, bytesFromFile7, true);
									Dictionary<int, StkSymbol> dictionary_2 = this.method_8(string_9);
									this.method_9(dictionary_2);
									this.method_7(dictionary_2, texPackage, list9, list3, false);
								}
								TApp.SrvParams.StaticSrvAddr = (this.method_13(list, Class521.smethod_0(35225), Class521.smethod_0(33356)) as string);
								TApp.SrvParams.HdDatFileBaseUrlRawString = (this.method_13(list, Class521.smethod_0(35246), Class521.smethod_0(33356)) as string);
								this.method_13(list, Class521.smethod_0(35246), Class521.smethod_0(33356));
								TApp.SrvParams.method_1();
								@class.backgroundWorker_0.ReportProgress(61, Class521.smethod_0(1449));
								this.method_6(@class.backgroundWorker_0, 61, Class521.smethod_0(35271));
								if (TApp.IsStIncluded)
								{
									string string_10 = null;
									if (isTrialUser)
									{
										string_10 = this.method_11(list, Class521.smethod_0(35340), Class521.smethod_0(34335), string_, bytesFromFile21, true);
									}
									else
									{
										TExPackage? texPackage2 = texPackage;
										if (!(texPackage2.GetValueOrDefault() == TExPackage.SVIP_St & texPackage2 != null))
										{
											texPackage2 = texPackage;
											if (!(texPackage2.GetValueOrDefault() == TExPackage.SVIP_StFt & texPackage2 != null))
											{
												goto IL_256E;
											}
										}
										string_10 = this.method_11(list, Class521.smethod_0(35353), Class521.smethod_0(34335), text31, bytesFromFile20, true);
									}
									IL_256E:
									List<StSplit> stSplitList = this.method_19(string_10);
									TApp.SrvParams.StSplitList = stSplitList;
								}
								this.method_6(@class.backgroundWorker_0, 63, Class521.smethod_0(35366));
								this.method_10(list, Class521.smethod_0(35423), Class521.smethod_0(35432), text32, bytesFromFile22);
								if (!isTrialUser && flag3)
								{
									@class.backgroundWorker_0.ReportProgress(63, Class521.smethod_0(35437));
									IEnumerable<SrvParam> source = list.Where(new Func<SrvParam, bool>(LoginForm.<>c.<>9.method_1));
									if (source.Any<SrvParam>())
									{
										TApp.ReqSyncFileSpms = BkupSyncMgr.smethod_15(source.First<SrvParam>().Value as List<SrvParam>);
										if (TApp.ReqSyncFileSpms != null && TApp.ReqSyncFileSpms.Any<SrvParam>())
										{
											@class.backgroundWorker_0.ReportProgress(65, Class521.smethod_0(9196));
											BkupSyncMgr.smethod_20(TApp.ReqSyncFileSpms, this.itexSrv_0, text, TApp.Ver);
										}
									}
								}
								@class.backgroundWorker_0.ReportProgress(67, Class521.smethod_0(35478));
								string text33 = string.Empty;
								if (!TApp.smethod_1(texPackage))
								{
									object obj7 = this.method_13(list, Class521.smethod_0(35515), Class521.smethod_0(33356));
									if (obj7 != null)
									{
										text33 = (obj7 as string);
									}
								}
								if (string.IsNullOrEmpty(text33))
								{
									StkSymbol startUpSymbl = SymbMgr.StartUpSymbl;
									List<UsrStkMeta> list10 = new List<UsrStkMeta>();
									UsrStkMeta item = SymbMgr.smethod_17(TApp.SrvParams.UsrStkSymbols, list9, TApp.SrvParams.StkBegEndDtLst, texPackage, startUpSymbl);
									list10.Add(item);
									if (TApp.SrvParams.GetHDFileInfoFromSrvApi)
									{
										text33 = HDFileMgr.smethod_4(list10, TApp.SrvParams.HdDatFileBaseUrl);
									}
									else
									{
										text33 = HDFileMgr.smethod_0(list10, list8, TApp.SrvParams.HdDatFileBaseUrl);
									}
								}
								if (!string.IsNullOrEmpty(text33))
								{
									LoginForm.Class235 class2 = new LoginForm.Class235();
									class2.class234_0 = @class;
									class2.class234_0.backgroundWorker_0.ReportProgress(68, Class521.smethod_0(35536));
									UpdateManager instance = UpdateManager.Instance;
									Utility.CreateDir(TApp.string_7);
									IUpdateSource iupdateSource_ = new MemorySource(text33.Replace(Class521.smethod_0(11789), TApp.string_7));
									class2.int_0 = 0;
									try
									{
										class2.int_0 = Class185.smethod_0(iupdateSource_);
									}
									catch
									{
										throw;
									}
									if (class2.int_0 > 0)
									{
										class2.class234_0.backgroundWorker_0.ReportProgress(69, Class521.smethod_0(35573));
										try
										{
											instance.ReportProgress += class2.method_0;
											instance.PrepareUpdates();
											List<string> list11 = new List<string>();
											new List<string>();
											foreach (IUpdateTask updateTask in instance.Tasks)
											{
												string fileName = Path.GetFileName(((FileUpdateTask)updateTask).LocalPath);
												string item2 = fileName.Substring(0, fileName.Length - 6);
												if (!list11.Contains(item2))
												{
													list11.Add(item2);
												}
											}
											instance.ApplyUpdates(false);
											instance.CleanUp();
										}
										catch (Exception)
										{
											instance.CleanUp();
											throw;
										}
									}
									class2.class234_0.backgroundWorker_0.ReportProgress(90, Class521.smethod_0(1449));
									this.method_6(class2.class234_0.backgroundWorker_0, 90, Class521.smethod_0(35610));
									try
									{
										if (Directory.Exists(instance.Config.TempFolder))
										{
											Utility.DeleteDir(instance.Config.TempFolder);
										}
									}
									catch
									{
									}
									try
									{
										if (Directory.Exists(instance.Config.BackupFolder))
										{
											Utility.DeleteDir(instance.Config.BackupFolder);
										}
									}
									catch
									{
									}
									instance.CleanUp();
								}
								else
								{
									@class.backgroundWorker_0.ReportProgress(91, Class521.smethod_0(35573));
								}
								SymbMgr.smethod_0();
								Base.Data.GettingLocalHisDataStarted += this.method_21;
								@class.backgroundWorker_0.ReportProgress(100, Class521.smethod_0(35655));
								TApp.IsLoggedIn = true;
								TApp.SrvParams.EnableRefreshHdDatUrlTimer = true;
								Thread.Sleep(100);
								ConnMgr.smethod_3();
								e.Result = new EventArgs9(Class521.smethod_0(1449));
								base.DialogResult = DialogResult.OK;
							}
							else
							{
								ConnMgr.smethod_3();
								@class.backgroundWorker_0.ReportProgress(-1, (loginRslt != null) ? loginRslt.Msg : Class521.smethod_0(31021));
							}
						}
					}
					else
					{
						ConnMgr.smethod_3();
						@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(31021));
					}
				}
			}
			catch (Exception ex)
			{
				Class48.smethod_4(ex, true, Class521.smethod_0(35676));
				if (ex is WebException)
				{
					if (!ConnMgr.smethod_5())
					{
						@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(35693));
					}
					else
					{
						@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(35782));
					}
				}
				else if (ex is NAppUpdateException)
				{
					MessageBox.Show(string.Concat(new string[]
					{
						ex.InnerException.Message,
						Environment.NewLine,
						ex.InnerException.Source,
						Environment.NewLine,
						ex.InnerException.StackTrace
					}));
					if (MessageBox.Show(Class521.smethod_0(35843), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
					{
						@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(35912));
						return;
					}
				}
				else if (!(ex is SqlException) && (ex.InnerException == null || ex.InnerException.InnerException == null || !(ex.InnerException.InnerException.Source == Class521.smethod_0(35973))))
				{
					@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(36087) + ex.Message);
				}
				else
				{
					@class.backgroundWorker_0.ReportProgress(-1, Class521.smethod_0(36014));
				}
				ConnMgr.smethod_3();
			}
		}

		// Token: 0x06001008 RID: 4104 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_6(BackgroundWorker backgroundWorker_1, int int_0, string string_0)
		{
		}

		// Token: 0x06001009 RID: 4105 RVA: 0x0006B6B0 File Offset: 0x000698B0
		private void method_7(Dictionary<int, StkSymbol> dictionary_0, TExPackage? nullable_0, List<UsrStkMeta> list_0, List<TradingSymbol> list_1, bool bool_1)
		{
			Dictionary<int, StkSymbol> dictionary = new Dictionary<int, StkSymbol>();
			if (dictionary_0 != null)
			{
				using (Dictionary<int, StkSymbol>.Enumerator enumerator = dictionary_0.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						LoginForm.Class236 @class = new LoginForm.Class236();
						@class.keyValuePair_0 = enumerator.Current;
						bool flag = false;
						if (nullable_0 != null && nullable_0.Value > TExPackage.TRL && list_0 != null && list_0.Count == 1)
						{
							UsrStkMeta usrStkMeta = list_0.First<UsrStkMeta>();
							int exchangeID = @class.keyValuePair_0.Value.ExchangeID;
							if (usrStkMeta.StkId == -100)
							{
								if (exchangeID < 5)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -70)
							{
								if (exchangeID == 1)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -80)
							{
								if (exchangeID > 1 && exchangeID < 5)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -200)
							{
								if (exchangeID == 5 || exchangeID == 6)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -300)
							{
								flag = true;
							}
						}
						else
						{
							IEnumerable<UsrStkMeta> source = list_0.Where(new Func<UsrStkMeta, bool>(@class.method_0));
							if (source.Any<UsrStkMeta>())
							{
								source.First<UsrStkMeta>().StkCode = @class.keyValuePair_0.Value.Code;
								flag = true;
							}
							else if (bool_1)
							{
								LoginForm.Class237 class2 = new LoginForm.Class237();
								class2.string_0 = @class.keyValuePair_0.Value.Code;
								class2.string_0 = class2.string_0.Substring(0, class2.string_0.Length - 2) + Class521.smethod_0(36100);
								if (list_0.Exists(new Predicate<UsrStkMeta>(class2.method_0)))
								{
									flag = true;
								}
							}
						}
						if (flag)
						{
							DateTime? dateTime = null;
							StkBegEndDate stkBegEndDate = TApp.SrvParams.StkBegEndDtLst.SingleOrDefault(new Func<StkBegEndDate, bool>(@class.method_1));
							if (stkBegEndDate != null)
							{
								dateTime = new DateTime?(stkBegEndDate.MinBegDate_1m);
							}
							if (dateTime != null && (TApp.smethod_1(nullable_0) || !(list_0.First<UsrStkMeta>().EndDate < dateTime.Value)))
							{
								StkSymbol stkSymbol = new StkSymbol();
								stkSymbol.ID = @class.keyValuePair_0.Key;
								stkSymbol.Code = @class.keyValuePair_0.Value.Code;
								stkSymbol.ExchangeID = @class.keyValuePair_0.Value.ExchangeID;
								stkSymbol.CNName = @class.keyValuePair_0.Value.CNName;
								stkSymbol.IdxClassLv1 = @class.keyValuePair_0.Value.IdxClassLv1;
								stkSymbol.IdxClassLv2 = @class.keyValuePair_0.Value.IdxClassLv2;
								stkSymbol.IdxClassLv3 = @class.keyValuePair_0.Value.IdxClassLv3;
								stkSymbol.MstSymbol = SymbMgr.smethod_35(list_1, stkSymbol.ExchangeID, stkSymbol.Code);
								if (bool_1 && stkSymbol.MstSymbol.BeginDate == null)
								{
									SymbMgr.smethod_15(dictionary, list_0, TApp.SrvParams.StkBegEndDtLst, nullable_0, stkSymbol, stkSymbol.MstSymbol);
								}
								if (stkSymbol.MstSymbol.IsStock || stkSymbol.MstSymbol.BeginDate != null)
								{
									dictionary.Add(stkSymbol.ID, stkSymbol);
								}
							}
						}
					}
				}
			}
			if (TApp.SrvParams.UsrStkSymbols == null)
			{
				TApp.SrvParams.UsrStkSymbols = new Dictionary<int, StkSymbol>();
			}
			if (bool_1)
			{
				using (IEnumerator<StkSymbol> enumerator2 = dictionary.Values.OrderBy(new Func<StkSymbol, int>(LoginForm.<>c.<>9.method_2)).ThenBy(new Func<StkSymbol, DateTime?>(LoginForm.<>c.<>9.method_3)).GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						StkSymbol stkSymbol2 = enumerator2.Current;
						TApp.SrvParams.UsrStkSymbols.Add(stkSymbol2.ID, stkSymbol2);
					}
					return;
				}
			}
			foreach (StkSymbol stkSymbol3 in dictionary.Values.OrderBy(new Func<StkSymbol, int>(LoginForm.<>c.<>9.method_4)).ThenBy(new Func<StkSymbol, string>(LoginForm.<>c.<>9.method_5)))
			{
				TApp.SrvParams.UsrStkSymbols.Add(stkSymbol3.ID, stkSymbol3);
			}
		}

		// Token: 0x0600100A RID: 4106 RVA: 0x0006BBC8 File Offset: 0x00069DC8
		private Dictionary<int, StkSymbol> method_8(string string_0)
		{
			Dictionary<int, StkSymbol> result;
			if (string_0 != null)
			{
				Dictionary<int, StkSymbol> dictionary = new Dictionary<int, StkSymbol>();
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[]
					{
						','
					});
					StkSymbol stkSymbol = new StkSymbol();
					stkSymbol.ID = Convert.ToInt32(array2[0].Trim());
					stkSymbol.ExchangeID = Convert.ToInt32(array2[1]);
					stkSymbol.Code = array2[2];
					stkSymbol.CNName = array2[3];
					if (array2.Length > 4)
					{
						stkSymbol.IdxClassLv1 = array2[4];
						stkSymbol.IdxClassLv2 = array2[5];
						stkSymbol.IdxClassLv3 = array2[6];
					}
					dictionary.Add(stkSymbol.ID, stkSymbol);
				}
				result = dictionary;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600100B RID: 4107 RVA: 0x0006BC9C File Offset: 0x00069E9C
		private void method_9(Dictionary<int, StkSymbol> dictionary_0)
		{
			if (TApp.SrvParams.StkSymbols == null)
			{
				TApp.SrvParams.StkSymbols = dictionary_0;
			}
			else
			{
				foreach (KeyValuePair<int, StkSymbol> keyValuePair in dictionary_0)
				{
					TApp.SrvParams.StkSymbols.Add(keyValuePair.Key, keyValuePair.Value);
				}
			}
		}

		// Token: 0x0600100C RID: 4108 RVA: 0x0006BD1C File Offset: 0x00069F1C
		private XDocument method_10(List<SrvParam> list_0, string string_0, string string_1, string string_2, byte[] byte_0)
		{
			XDocument xdocument = null;
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			if (srvParam != null)
			{
				if (srvParam.Value == null)
				{
					if (srvParam.Note != null && srvParam.Note.Equals(Class521.smethod_0(36105)))
					{
						return null;
					}
				}
				else
				{
					try
					{
						byte[] bytes = srvParam.Value as byte[];
						Utility.GenFileFromBytes(bytes, string_2);
						xdocument = Utility.GetXDocFromBytes(bytes);
					}
					catch
					{
					}
				}
			}
			if (xdocument == null && byte_0 != null && byte_0.Length != 0)
			{
				xdocument = XDocument.Load(string_2);
			}
			return xdocument;
		}

		// Token: 0x0600100D RID: 4109 RVA: 0x0006BDAC File Offset: 0x00069FAC
		private string method_11(List<SrvParam> list_0, string string_0, string string_1, string string_2, byte[] byte_0, bool bool_1 = true)
		{
			string text = null;
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			if (srvParam != null)
			{
				if (srvParam.Value == null)
				{
					if (srvParam.Note != null && srvParam.Note.Equals(Class521.smethod_0(36105)))
					{
						return null;
					}
				}
				else
				{
					try
					{
						byte[] bytes = srvParam.Value as byte[];
						Utility.GenFileFromBytes(bytes, string_2);
						text = Utility.GetStringFromBytes(bytes, bool_1, null, CompressAlgm.LZMA);
					}
					catch
					{
					}
				}
			}
			if (text == null && byte_0 != null && byte_0.Length != 0)
			{
				try
				{
					text = Utility.GetStringFromBytes(byte_0, bool_1, null, CompressAlgm.LZMA);
				}
				catch (Exception ex)
				{
					throw ex;
				}
			}
			return text;
		}

		// Token: 0x0600100E RID: 4110 RVA: 0x0006BE54 File Offset: 0x0006A054
		private XDocument method_12(List<SrvParam> list_0, string string_0, string string_1)
		{
			XDocument result = null;
			object obj = this.method_13(list_0, string_0, string_1);
			if (obj != null)
			{
				try
				{
					result = XDocument.Parse(obj as string);
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x0600100F RID: 4111 RVA: 0x0006BE98 File Offset: 0x0006A098
		private object method_13(List<SrvParam> list_0, string string_0, string string_1)
		{
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			object result;
			if (srvParam != null)
			{
				result = srvParam.Value;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001010 RID: 4112 RVA: 0x0006BEC0 File Offset: 0x0006A0C0
		private SrvParam method_14(List<SrvParam> list_0, string string_0, string string_1)
		{
			LoginForm.Class238 @class = new LoginForm.Class238();
			@class.string_0 = string_0;
			@class.string_1 = string_1;
			return list_0.FirstOrDefault(new Func<SrvParam, bool>(@class.method_0));
		}

		// Token: 0x06001011 RID: 4113 RVA: 0x0006BEF8 File Offset: 0x0006A0F8
		private List<UsrStkMeta> method_15(XDocument xdocument_0)
		{
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (xdocument_0 != null)
			{
				foreach (XElement xelement in xdocument_0.Element(Class521.smethod_0(35056)).Elements(Class521.smethod_0(36126)))
				{
					int stkId = Convert.ToInt32(xelement.Element(Class521.smethod_0(36143)).Value);
					XElement xelement2 = xelement.Element(Class521.smethod_0(36152));
					DateTime? beginDate = null;
					if (xelement2 != null && !string.IsNullOrEmpty(xelement2.Value))
					{
						beginDate = new DateTime?(Convert.ToDateTime(xelement2.Value));
					}
					XElement xelement3 = xelement.Element(Class521.smethod_0(36165));
					DateTime? endDate = null;
					if (xelement3 != null && !string.IsNullOrEmpty(xelement3.Value))
					{
						endDate = new DateTime?(Convert.ToDateTime(xelement3.Value));
					}
					List<DatInfo> list2 = null;
					XElement xelement4 = xelement.Element(Class521.smethod_0(36178));
					if (xelement4 != null)
					{
						IEnumerable<XElement> enumerable = xelement4.Elements(Class521.smethod_0(36195));
						if (enumerable != null)
						{
							list2 = new List<DatInfo>();
							foreach (XElement xelement5 in enumerable)
							{
								DatInfo datInfo = new DatInfo();
								datInfo.BeginDate = new DateTime?(Convert.ToDateTime(xelement5.Attribute(Class521.smethod_0(36152)).Value));
								datInfo.EndDate = new DateTime?(Convert.ToDateTime(xelement5.Attribute(Class521.smethod_0(36165)).Value));
								datInfo.PeriodType = (PeriodType)Convert.ToInt32(xelement5.Attribute(Class521.smethod_0(2262)).Value);
								int? periodUnits = null;
								XAttribute xattribute = xelement5.Attribute(Class521.smethod_0(2279));
								if (xattribute != null && !string.IsNullOrEmpty(xattribute.Value))
								{
									periodUnits = new int?(Convert.ToInt32(xattribute.Value));
								}
								datInfo.PeriodUnits = periodUnits;
								list2.Add(datInfo);
							}
						}
					}
					UsrStkMeta item = new UsrStkMeta(stkId, beginDate, endDate, list2);
					list.Add(item);
				}
			}
			return list;
		}

		// Token: 0x06001012 RID: 4114 RVA: 0x0006C1B0 File Offset: 0x0006A3B0
		private List<UsrStkMeta> method_16(int[] int_0, List<HDFileInfo> list_0, DateTime? nullable_0, DateTime? nullable_1)
		{
			LoginForm.Class239 @class = new LoginForm.Class239();
			@class.nullable_0 = nullable_1;
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (int_0 != null)
			{
				for (int i = 0; i < int_0.Length; i++)
				{
					LoginForm.Class240 class2 = new LoginForm.Class240();
					class2.class239_0 = @class;
					class2.int_0 = int_0[i];
					IEnumerable<HDFileInfo> source = list_0.Where(new Func<HDFileInfo, bool>(class2.method_0));
					UsrStkMeta usrStkMeta = new UsrStkMeta();
					usrStkMeta.StkId = class2.int_0;
					usrStkMeta.BeginDate = nullable_0;
					if (source.Any<HDFileInfo>())
					{
						DateTime dateTime = source.Min(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_6));
						if (nullable_0 == null || dateTime > nullable_0.Value)
						{
							usrStkMeta.BeginDate = new DateTime?(dateTime);
						}
						usrStkMeta.EndDate = new DateTime?(source.Max(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_7)));
					}
					else
					{
						usrStkMeta.EndDate = new DateTime?(DateTime.Now.Date.AddDays(-1.0));
					}
					List<DatInfo> list2 = new List<DatInfo>();
					DatInfo datInfo = new DatInfo();
					datInfo.PeriodType = PeriodType.ByMins;
					datInfo.PeriodUnits = new int?(1);
					if (class2.class239_0.nullable_0 != null)
					{
						IEnumerable<HDFileInfo> source2 = list_0.Where(new Func<HDFileInfo, bool>(class2.method_1));
						if (source2.Any<HDFileInfo>())
						{
							datInfo.BeginDate = new DateTime?(source2.Min(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_8)));
						}
						else
						{
							datInfo.BeginDate = class2.class239_0.nullable_0;
						}
					}
					else
					{
						datInfo.BeginDate = usrStkMeta.BeginDate;
					}
					datInfo.EndDate = usrStkMeta.EndDate;
					list2.Add(datInfo);
					usrStkMeta.DatInfoList = list2;
					list.Add(usrStkMeta);
				}
			}
			return list;
		}

		// Token: 0x06001013 RID: 4115 RVA: 0x0006C3D0 File Offset: 0x0006A5D0
		private List<UsrStkMeta> method_17(int[] int_0, DateTime dateTime_0, DateTime dateTime_1, DateTime dateTime_2)
		{
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (int_0 != null)
			{
				foreach (int stkId in int_0)
				{
					list.Add(new UsrStkMeta
					{
						StkId = stkId,
						BeginDate = new DateTime?(dateTime_0),
						EndDate = new DateTime?(dateTime_1),
						DatInfoList = new List<DatInfo>
						{
							new DatInfo
							{
								PeriodType = PeriodType.ByMins,
								PeriodUnits = new int?(1),
								BeginDate = new DateTime?(dateTime_2),
								EndDate = new DateTime?(dateTime_1)
							}
						}
					});
				}
			}
			return list;
		}

		// Token: 0x06001014 RID: 4116 RVA: 0x0006C48C File Offset: 0x0006A68C
		private List<TradingSymbol> method_18(XDocument xdocument_0)
		{
			List<TradingSymbol> list = new List<TradingSymbol>();
			if (xdocument_0 != null)
			{
				foreach (XElement xelement in xdocument_0.Element(Class521.smethod_0(36208)).Elements(Class521.smethod_0(36229)))
				{
					TradingSymbol tradingSymbol = new TradingSymbol();
					tradingSymbol.ID = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(33549)).Value);
					tradingSymbol.Code = xelement.Attribute(Class521.smethod_0(36238)).Value;
					tradingSymbol.CNName = xelement.Attribute(Class521.smethod_0(16316)).Value;
					tradingSymbol.ENName = xelement.Attribute(Class521.smethod_0(36247)).Value;
					tradingSymbol.ExchangeID = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(36256)).Value);
					tradingSymbol.Type = (TradingSymbolType)Convert.ToInt32(xelement.Attribute(Class521.smethod_0(2690)).Value);
					string value = xelement.Attribute(Class521.smethod_0(36273)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.TonsPerUnit = new int?(Convert.ToInt32(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36290)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.LeastPriceVar = new decimal?(Convert.ToDecimal(value));
					}
					tradingSymbol.DigitNb = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(36311)).Value);
					tradingSymbol.FeeType = (FeeType)Convert.ToInt32(xelement.Attribute(Class521.smethod_0(36324)).Value);
					value = xelement.Attribute(Class521.smethod_0(36337)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.IsOneSideFee = new bool?(Convert.ToBoolean(Convert.ToInt32(value)));
					}
					value = xelement.Attribute(Class521.smethod_0(36354)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.MarginRate = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36371)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AvgSlipg = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36384)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.FeePerUnit = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36401)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.FeeRate = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36414)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AutoLimitTakePoints = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36443)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AutoStopLossPoints = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute(Class521.smethod_0(36468)).Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.DefaultUnits = new int?(Convert.ToInt32(value));
					}
					list.Add(tradingSymbol);
				}
			}
			return list;
		}

		// Token: 0x06001015 RID: 4117 RVA: 0x0006C86C File Offset: 0x0006AA6C
		private List<StSplit> method_19(string string_0)
		{
			List<StSplit> list = null;
			if (string_0 != null)
			{
				list = new List<StSplit>();
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[]
					{
						','
					});
					list.Add(new StSplit
					{
						StockId = Convert.ToInt32(array2[0].Trim()),
						Date = Convert.ToDateTime(array2[1]),
						BonusShares = this.method_20(array2[2]),
						RationedShares = this.method_20(array2[3]),
						RationedSharePrice = this.method_20(array2[4]),
						Divident = this.method_20(array2[5])
					});
				}
			}
			return list;
		}

		// Token: 0x06001016 RID: 4118 RVA: 0x0006C940 File Offset: 0x0006AB40
		private decimal? method_20(string string_0)
		{
			decimal? result = null;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = new decimal?(0m);
				try
				{
					result = new decimal?(Convert.ToDecimal(string_0.Trim()));
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x06001017 RID: 4119 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_21(object sender, EventArgs e)
		{
		}

		// Token: 0x06001018 RID: 4120 RVA: 0x0006C998 File Offset: 0x0006AB98
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			int num = e.ProgressPercentage;
			if (num >= 0)
			{
				if (num > 100)
				{
					num = 100;
				}
				this.class299_0.Value = num;
				if (!this.class299_0.Visible)
				{
					this.class299_0.Visible = true;
				}
			}
			else
			{
				this.class299_0.Visible = false;
			}
			if (!this.label_2.Visible)
			{
				this.label_2.Visible = true;
			}
			if (e.UserState != null)
			{
				string text = e.UserState.ToString();
				if (!string.IsNullOrEmpty(text))
				{
					this.label_2.Text = text;
					this.textBox_0.Enabled = true;
					this.maskedTextBox_0.Enabled = true;
				}
			}
			this.Refresh();
		}

		// Token: 0x06001019 RID: 4121 RVA: 0x0006CA4C File Offset: 0x0006AC4C
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			if (e.Error == null && !e.Cancelled)
			{
				if (this.class299_0.Visible)
				{
					this.class299_0.Visible = false;
				}
				if (!this.timer_0.Enabled)
				{
					this.method_23();
				}
				if (e.Result != null)
				{
					this.method_25();
					this.method_1((EventArgs9)e.Result);
				}
			}
		}

		// Token: 0x0600101A RID: 4122 RVA: 0x00006DC1 File Offset: 0x00004FC1
		private void method_22(object sender, EventArgs e)
		{
			this.method_2(new EventArgs());
			base.Close();
			Application.Exit();
		}

		// Token: 0x0600101B RID: 4123 RVA: 0x00006DDB File Offset: 0x00004FDB
		private void method_23()
		{
			if (!this.button_0.Enabled)
			{
				this.button_0.Enabled = true;
			}
		}

		// Token: 0x0600101C RID: 4124 RVA: 0x00006DF8 File Offset: 0x00004FF8
		private void method_24()
		{
			this.class299_0.Visible = false;
			this.label_2.Visible = false;
		}

		// Token: 0x0600101D RID: 4125 RVA: 0x0006CAB8 File Offset: 0x0006ACB8
		public void method_25()
		{
			base.Hide();
			base.ShowInTaskbar = false;
			Base.UI.Form.UserID = this.textBox_0.Text;
			if (this.checkBox_0.Checked)
			{
				if (!Base.UI.Form.IfSavePswd)
				{
					Base.UI.Form.IfSavePswd = true;
				}
				Base.UI.Form.Pswd = this.maskedTextBox_0.Text;
			}
			else
			{
				if (Base.UI.Form.IfSavePswd)
				{
					Base.UI.Form.IfSavePswd = false;
				}
				Base.UI.Form.Pswd = string.Empty;
			}
		}

		// Token: 0x0600101E RID: 4126 RVA: 0x0006CB4C File Offset: 0x0006AD4C
		protected void WndProc(ref Message m)
		{
			if (m.Msg == 274 && (int)m.WParam == 61536)
			{
				try
				{
					base.CancelButton.PerformClick();
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
			else
			{
				base.WndProc(ref m);
			}
		}

		// Token: 0x0600101F RID: 4127 RVA: 0x00006E14 File Offset: 0x00005014
		private void linkLabel_1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			this.method_26(this.linkLabel_1.Text);
		}

		// Token: 0x06001020 RID: 4128 RVA: 0x0006CBA8 File Offset: 0x0006ADA8
		private void linkLabel_2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (MessageBox.Show(Class521.smethod_0(36485) + Environment.NewLine + Environment.NewLine + Class521.smethod_0(36570), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_26(this.linkLabel_1.Text + Class521.smethod_0(36675));
			}
		}

		// Token: 0x06001021 RID: 4129 RVA: 0x0006CC10 File Offset: 0x0006AE10
		private void method_26(string string_0)
		{
			try
			{
				string str = Class521.smethod_0(27697);
				if (!string_0.StartsWith(Class521.smethod_0(36692)))
				{
					str = Class521.smethod_0(9769);
				}
				Process.Start(new ProcessStartInfo(str + string_0));
			}
			catch
			{
			}
		}

		// Token: 0x06001022 RID: 4130 RVA: 0x00006E29 File Offset: 0x00005029
		private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (this.backgroundWorker_0.IsBusy)
			{
				this.backgroundWorker_0.CancelAsync();
			}
		}

		// Token: 0x06001023 RID: 4131 RVA: 0x0006CC70 File Offset: 0x0006AE70
		private string method_27()
		{
			string result = Class521.smethod_0(1449);
			try
			{
				using (Graphics graphics = base.CreateGraphics())
				{
					result = graphics.DpiX.ToString();
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x06001024 RID: 4132 RVA: 0x00006E45 File Offset: 0x00005045
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001025 RID: 4133 RVA: 0x0006CCD4 File Offset: 0x0006AED4
		private void method_28()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(LoginForm));
			this.pictureBox_0 = new PictureBox();
			this.textBox_0 = new TextBox();
			this.maskedTextBox_0 = new MaskedTextBox();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.button_0 = new Button();
			this.linkLabel_0 = new LinkLabel();
			this.linkLabel_1 = new LinkLabel();
			this.label_2 = new Label();
			this.checkBox_0 = new CheckBox();
			this.label_3 = new Label();
			this.class52_0 = new Class52();
			this.linkLabel_2 = new LinkLabel();
			this.linkLabel_3 = new LinkLabel();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			((ISupportInitialize)this.class52_0).BeginInit();
			base.SuspendLayout();
			this.pictureBox_0.BackColor = Color.Transparent;
			this.pictureBox_0.Dock = DockStyle.Top;
			this.pictureBox_0.Image = Class375.LoginBannerNew;
			this.pictureBox_0.Location = new Point(0, 0);
			this.pictureBox_0.MaximumSize = new Size(690, 300);
			this.pictureBox_0.MinimumSize = new Size(690, 300);
			this.pictureBox_0.Name = Class521.smethod_0(27820);
			this.pictureBox_0.Size = new Size(690, 300);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 0;
			this.pictureBox_0.TabStop = false;
			this.textBox_0.Font = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.textBox_0.Location = new Point(91, 341);
			this.textBox_0.Name = Class521.smethod_0(36697);
			this.textBox_0.Size = new Size(130, 27);
			this.textBox_0.TabIndex = 1;
			this.maskedTextBox_0.Font = new Font(Class521.smethod_0(7183), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.maskedTextBox_0.Location = new Point(283, 341);
			this.maskedTextBox_0.Name = Class521.smethod_0(36718);
			this.maskedTextBox_0.PasswordChar = '*';
			this.maskedTextBox_0.Size = new Size(131, 27);
			this.maskedTextBox_0.TabIndex = 2;
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(39, 346);
			this.label_0.Name = Class521.smethod_0(36743);
			this.label_0.Size = new Size(35, 15);
			this.label_0.TabIndex = 3;
			this.label_0.Text = Class521.smethod_0(36760);
			this.label_0.TextAlign = ContentAlignment.MiddleLeft;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(231, 346);
			this.label_1.Name = Class521.smethod_0(36769);
			this.label_1.Size = new Size(39, 15);
			this.label_1.TabIndex = 4;
			this.label_1.Text = Class521.smethod_0(36786);
			this.label_1.TextAlign = ContentAlignment.MiddleLeft;
			this.button_0.Location = new Point(541, 338);
			this.button_0.Name = Class521.smethod_0(36795);
			this.button_0.Size = new Size(110, 35);
			this.button_0.TabIndex = 0;
			this.button_0.Text = Class521.smethod_0(36808);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.linkLabel_0.AutoSize = true;
			this.linkLabel_0.LinkColor = Color.FromArgb(230, 50, 18);
			this.linkLabel_0.Location = new Point(221, 311);
			this.linkLabel_0.Name = Class521.smethod_0(36817);
			this.linkLabel_0.Size = new Size(67, 15);
			this.linkLabel_0.TabIndex = 6;
			this.linkLabel_0.TabStop = true;
			this.linkLabel_0.Text = Class521.smethod_0(36842);
			this.linkLabel_0.Visible = false;
			this.linkLabel_1.Font = new Font(Class521.smethod_0(24023), 9.5f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.linkLabel_1.Location = new Point(480, 276);
			this.linkLabel_1.Name = Class521.smethod_0(27845);
			this.linkLabel_1.Size = new Size(235, 24);
			this.linkLabel_1.TabIndex = 7;
			this.linkLabel_1.TabStop = true;
			this.linkLabel_1.Text = Class521.smethod_0(27874);
			this.linkLabel_1.TextAlign = ContentAlignment.MiddleRight;
			this.label_2.AutoSize = true;
			this.label_2.Font = new Font(Class521.smethod_0(24023), 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_2.Location = new Point(0, 308);
			this.label_2.Name = Class521.smethod_0(36859);
			this.label_2.Size = new Size(165, 20);
			this.label_2.TabIndex = 11;
			this.label_2.Text = Class521.smethod_0(36872);
			this.label_2.TextAlign = ContentAlignment.MiddleLeft;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(421, 344);
			this.checkBox_0.Name = Class521.smethod_0(36913);
			this.checkBox_0.Size = new Size(89, 19);
			this.checkBox_0.TabIndex = 5;
			this.checkBox_0.Text = Class521.smethod_0(36934);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.label_3.Anchor = (AnchorStyles.Bottom | AnchorStyles.Right);
			this.label_3.BackColor = Color.Transparent;
			this.label_3.Font = new Font(Class521.smethod_0(24023), 8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_3.ForeColor = Color.DarkGray;
			this.label_3.Location = new Point(500, 246);
			this.label_3.Name = Class521.smethod_0(36951);
			this.label_3.RightToLeft = RightToLeft.No;
			this.label_3.Size = new Size(190, 21);
			this.label_3.TabIndex = 14;
			this.label_3.Text = Class521.smethod_0(36964);
			this.label_3.TextAlign = ContentAlignment.BottomRight;
			this.class52_0.Cursor = Cursors.Hand;
			this.class52_0.HoverImage = (Image)componentResourceManager.GetObject(Class521.smethod_0(36977));
			this.class52_0.Image = (Image)componentResourceManager.GetObject(Class521.smethod_0(37010));
			this.class52_0.IsTransparent = true;
			this.class52_0.Location = new Point(657, 12);
			this.class52_0.Name = Class521.smethod_0(37035);
			this.class52_0.NormalImage = (Image)componentResourceManager.GetObject(Class521.smethod_0(37052));
			this.class52_0.Size = new Size(18, 18);
			this.class52_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.class52_0.TabIndex = 17;
			this.class52_0.TabStop = false;
			this.linkLabel_2.AutoSize = true;
			this.linkLabel_2.LinkColor = Color.FromArgb(0, 120, 215);
			this.linkLabel_2.Location = new Point(272, 387);
			this.linkLabel_2.Name = Class521.smethod_0(37085);
			this.linkLabel_2.Size = new Size(82, 15);
			this.linkLabel_2.TabIndex = 18;
			this.linkLabel_2.TabStop = true;
			this.linkLabel_2.Text = Class521.smethod_0(37110);
			this.linkLabel_3.AutoSize = true;
			this.linkLabel_3.LinkColor = Color.FromArgb(0, 120, 215);
			this.linkLabel_3.Location = new Point(76, 387);
			this.linkLabel_3.Name = Class521.smethod_0(37131);
			this.linkLabel_3.Size = new Size(67, 15);
			this.linkLabel_3.TabIndex = 19;
			this.linkLabel_3.TabStop = true;
			this.linkLabel_3.Text = Class521.smethod_0(8975);
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = Color.White;
			base.ClientSize = new Size(688, 406);
			base.ControlBox = false;
			base.Controls.Add(this.linkLabel_3);
			base.Controls.Add(this.linkLabel_2);
			base.Controls.Add(this.class52_0);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.checkBox_0);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.linkLabel_1);
			base.Controls.Add(this.linkLabel_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.maskedTextBox_0);
			base.Controls.Add(this.textBox_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			this.MaximumSize = new Size(690, 408);
			base.MinimizeBox = false;
			this.MinimumSize = new Size(690, 408);
			base.Name = Class521.smethod_0(37160);
			base.FormClosing += this.LoginForm_FormClosing;
			base.Load += this.LoginForm_Load;
			base.Shown += this.LoginForm_Shown;
			((ISupportInitialize)this.pictureBox_0).EndInit();
			((ISupportInitialize)this.class52_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040007F6 RID: 2038
		private Class299 class299_0;

		// Token: 0x040007F7 RID: 2039
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x040007F8 RID: 2040
		private ITExSrv itexSrv_0;

		// Token: 0x040007F9 RID: 2041
		private SplashScreen splashScreen_0;

		// Token: 0x040007FA RID: 2042
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x040007FB RID: 2043
		private bool bool_0;

		// Token: 0x040007FC RID: 2044
		private Keys keys_0;

		// Token: 0x040007FD RID: 2045
		[CompilerGenerated]
		private Delegate12 delegate12_0;

		// Token: 0x040007FE RID: 2046
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040007FF RID: 2047
		private IContainer icontainer_0;

		// Token: 0x04000800 RID: 2048
		private PictureBox pictureBox_0;

		// Token: 0x04000801 RID: 2049
		private TextBox textBox_0;

		// Token: 0x04000802 RID: 2050
		private MaskedTextBox maskedTextBox_0;

		// Token: 0x04000803 RID: 2051
		private Label label_0;

		// Token: 0x04000804 RID: 2052
		private Label label_1;

		// Token: 0x04000805 RID: 2053
		private Button button_0;

		// Token: 0x04000806 RID: 2054
		private LinkLabel linkLabel_0;

		// Token: 0x04000807 RID: 2055
		private LinkLabel linkLabel_1;

		// Token: 0x04000808 RID: 2056
		private Label label_2;

		// Token: 0x04000809 RID: 2057
		private CheckBox checkBox_0;

		// Token: 0x0400080A RID: 2058
		private Label label_3;

		// Token: 0x0400080B RID: 2059
		private Class52 class52_0;

		// Token: 0x0400080C RID: 2060
		private LinkLabel linkLabel_2;

		// Token: 0x0400080D RID: 2061
		private LinkLabel linkLabel_3;

		// Token: 0x0200019C RID: 412
		[CompilerGenerated]
		private sealed class Class233
		{
			// Token: 0x06001027 RID: 4135 RVA: 0x0006D80C File Offset: 0x0006BA0C
			internal bool method_0(KeyValuePair<string, DateTime> keyValuePair_0)
			{
				return keyValuePair_0.Value == this.list_0.Max(new Func<KeyValuePair<string, DateTime>, DateTime>(LoginForm.<>c.<>9.method_0));
			}

			// Token: 0x0400080E RID: 2062
			public List<KeyValuePair<string, DateTime>> list_0;
		}

		// Token: 0x0200019E RID: 414
		[CompilerGenerated]
		private sealed class Class234
		{
			// Token: 0x04000819 RID: 2073
			public BackgroundWorker backgroundWorker_0;
		}

		// Token: 0x0200019F RID: 415
		[CompilerGenerated]
		private sealed class Class235
		{
			// Token: 0x06001035 RID: 4149 RVA: 0x0006D8FC File Offset: 0x0006BAFC
			internal void method_0(UpdateProgressInfo updateProgressInfo_0)
			{
				if (updateProgressInfo_0.StillWorking)
				{
					try
					{
						if (this.class234_0.backgroundWorker_0.IsBusy)
						{
							this.class234_0.backgroundWorker_0.ReportProgress(69 + Convert.ToInt32(Math.Floor(21.0 * ((double)updateProgressInfo_0.TaskId / (double)this.int_0))), Class521.smethod_0(115824));
						}
					}
					catch
					{
					}
				}
			}

			// Token: 0x0400081A RID: 2074
			public int int_0;

			// Token: 0x0400081B RID: 2075
			public LoginForm.Class234 class234_0;
		}

		// Token: 0x020001A0 RID: 416
		[CompilerGenerated]
		private sealed class Class236
		{
			// Token: 0x06001037 RID: 4151 RVA: 0x0006D97C File Offset: 0x0006BB7C
			internal bool method_0(UsrStkMeta usrStkMeta_0)
			{
				return usrStkMeta_0.StkId == this.keyValuePair_0.Key;
			}

			// Token: 0x06001038 RID: 4152 RVA: 0x0006D9A0 File Offset: 0x0006BBA0
			internal bool method_1(StkBegEndDate stkBegEndDate_0)
			{
				return stkBegEndDate_0.StkId == this.keyValuePair_0.Value.ID;
			}

			// Token: 0x0400081C RID: 2076
			public KeyValuePair<int, StkSymbol> keyValuePair_0;
		}

		// Token: 0x020001A1 RID: 417
		[CompilerGenerated]
		private sealed class Class237
		{
			// Token: 0x0600103A RID: 4154 RVA: 0x0006D9CC File Offset: 0x0006BBCC
			internal bool method_0(UsrStkMeta usrStkMeta_0)
			{
				return usrStkMeta_0.StkCode == this.string_0;
			}

			// Token: 0x0400081D RID: 2077
			public string string_0;
		}

		// Token: 0x020001A2 RID: 418
		[CompilerGenerated]
		private sealed class Class238
		{
			// Token: 0x0600103C RID: 4156 RVA: 0x0006D9F0 File Offset: 0x0006BBF0
			internal bool method_0(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.Name == this.string_0)
				{
					result = (srvParam_0.Ver == this.string_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400081E RID: 2078
			public string string_0;

			// Token: 0x0400081F RID: 2079
			public string string_1;
		}

		// Token: 0x020001A3 RID: 419
		[CompilerGenerated]
		private sealed class Class239
		{
			// Token: 0x04000820 RID: 2080
			public DateTime? nullable_0;
		}

		// Token: 0x020001A4 RID: 420
		[CompilerGenerated]
		private sealed class Class240
		{
			// Token: 0x0600103F RID: 4159 RVA: 0x0006DA2C File Offset: 0x0006BC2C
			internal bool method_0(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.int_0)
				{
					result = hdfileInfo_0.IsPeriod_1m();
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001040 RID: 4160 RVA: 0x0006DA58 File Offset: 0x0006BC58
			internal bool method_1(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.int_0 && hdfileInfo_0.IsPeriod_1m())
				{
					result = (hdfileInfo_0.BeginDate.Year == this.class239_0.nullable_0.Value.Year);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000821 RID: 2081
			public int int_0;

			// Token: 0x04000822 RID: 2082
			public LoginForm.Class239 class239_0;
		}
	}
}
