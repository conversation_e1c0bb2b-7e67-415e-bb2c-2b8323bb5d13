﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns6
{
	// Token: 0x0200025C RID: 604
	internal sealed class Class326
	{
		// Token: 0x17000458 RID: 1112
		// (get) Token: 0x06001A8A RID: 6794 RVA: 0x000BC014 File Offset: 0x000BA214
		// (set) Token: 0x06001A8B RID: 6795 RVA: 0x0000B1BB File Offset: 0x000093BB
		public string UserName { get; set; }

		// Token: 0x17000459 RID: 1113
		// (get) Token: 0x06001A8C RID: 6796 RVA: 0x000BC02C File Offset: 0x000BA22C
		// (set) Token: 0x06001A8D RID: 6797 RVA: 0x0000B1C6 File Offset: 0x000093C6
		public TExPackage? TExPkg { get; set; }

		// Token: 0x1700045A RID: 1114
		// (get) Token: 0x06001A8E RID: 6798 RVA: 0x000BC044 File Offset: 0x000BA244
		// (set) Token: 0x06001A8F RID: 6799 RVA: 0x0000B1D1 File Offset: 0x000093D1
		public DateTime CurrDate { get; set; }

		// Token: 0x1700045B RID: 1115
		// (get) Token: 0x06001A90 RID: 6800 RVA: 0x000BC05C File Offset: 0x000BA25C
		// (set) Token: 0x06001A91 RID: 6801 RVA: 0x0000B1DC File Offset: 0x000093DC
		public Dictionary<string, SortableBindingList<ShowMktSymb>> DgvDataSources { get; set; }

		// Token: 0x1700045C RID: 1116
		// (get) Token: 0x06001A92 RID: 6802 RVA: 0x000BC074 File Offset: 0x000BA274
		// (set) Token: 0x06001A93 RID: 6803 RVA: 0x0000B1E7 File Offset: 0x000093E7
		public Dictionary<int, ShowMktSymb> ShowMktSymbDict { get; set; }

		// Token: 0x1700045D RID: 1117
		// (get) Token: 0x06001A94 RID: 6804 RVA: 0x000BC08C File Offset: 0x000BA28C
		// (set) Token: 0x06001A95 RID: 6805 RVA: 0x0000B1F2 File Offset: 0x000093F2
		public string ShowMktSymbCsv { get; set; }

		// Token: 0x04000D64 RID: 3428
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000D65 RID: 3429
		[CompilerGenerated]
		private TExPackage? nullable_0;

		// Token: 0x04000D66 RID: 3430
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x04000D67 RID: 3431
		[CompilerGenerated]
		private Dictionary<string, SortableBindingList<ShowMktSymb>> dictionary_0;

		// Token: 0x04000D68 RID: 3432
		[CompilerGenerated]
		private Dictionary<int, ShowMktSymb> dictionary_1;

		// Token: 0x04000D69 RID: 3433
		[CompilerGenerated]
		private string string_1;
	}
}
