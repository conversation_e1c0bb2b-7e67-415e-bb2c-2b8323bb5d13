using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns10;
using ns4;
using TEx;

namespace ns1
{
	// Token: 0x02000218 RID: 536
	[DesignerCategory("Code")]
	internal sealed class Class296 : FlowLayoutPanel
	{
		// Token: 0x06001607 RID: 5639 RVA: 0x00008EE0 File Offset: 0x000070E0
		public Class296()
		{
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
			base.ControlRemoved += this.Class296_ControlRemoved;
		}

		// Token: 0x06001608 RID: 5640 RVA: 0x00097BE0 File Offset: 0x00095DE0
		protected void OnPaint(PaintEventArgs e)
		{
			using (SolidBrush solidBrush = new SolidBrush(this.BackColor))
			{
				e.Graphics.FillRectangle(solidBrush, base.ClientRectangle);
			}
			if (this.BorderColor != default(Color))
			{
				Pen pen = new Pen(Class181.color_18);
				e.Graphics.DrawRectangle(pen, 0, 0, base.ClientSize.Width - 1, base.ClientSize.Height - 1);
			}
		}

		// Token: 0x06001609 RID: 5641 RVA: 0x00097C78 File Offset: 0x00095E78
		public void method_0()
		{
			Color backColor = Color.FromArgb(240, 240, 240);
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				backColor = Color.FromArgb(65, 65, 65);
			}
			this.BackColor = backColor;
			foreach (object obj in base.Controls)
			{
				((FilterCondItem)obj).method_3();
			}
		}

		// Token: 0x0600160A RID: 5642 RVA: 0x00097D08 File Offset: 0x00095F08
		private void Class296_ControlRemoved(object sender, ControlEventArgs e)
		{
			List<FilterCond> list_ = this.method_3();
			this.method_1(list_, new Padding(1));
		}

		// Token: 0x0600160B RID: 5643 RVA: 0x00097D2C File Offset: 0x00095F2C
		public void method_1(List<FilterCond> list_0, Padding padding_0 = default(Padding))
		{
			this.smethod_0();
			base.SuspendLayout();
			base.ControlRemoved -= this.Class296_ControlRemoved;
			base.Controls.Clear();
			if (padding_0 == default(Padding))
			{
				padding_0 = new Padding(1);
			}
			foreach (FilterCond cond in list_0)
			{
				FilterCondItem filterCondItem = new FilterCondItem(cond);
				filterCondItem.Margin = padding_0;
				filterCondItem.method_3();
				base.Controls.Add(filterCondItem);
			}
			base.ControlRemoved += this.Class296_ControlRemoved;
			base.ResumeLayout();
			this.smethod_1();
		}

		// Token: 0x0600160C RID: 5644 RVA: 0x00097DF4 File Offset: 0x00095FF4
		public void method_2(FilterCond filterCond_0)
		{
			List<FilterCond> list = this.method_3();
			list.Add(filterCond_0);
			this.method_1(list, new Padding(1));
		}

		// Token: 0x0600160D RID: 5645 RVA: 0x00097E20 File Offset: 0x00096020
		public List<FilterCond> method_3()
		{
			List<FilterCond> list = new List<FilterCond>();
			foreach (object obj in base.Controls)
			{
				FilterCondItem filterCondItem = obj as FilterCondItem;
				list.Add(filterCondItem.FilterCond);
			}
			return list;
		}

		// Token: 0x17000392 RID: 914
		// (get) Token: 0x0600160E RID: 5646 RVA: 0x00097E8C File Offset: 0x0009608C
		// (set) Token: 0x0600160F RID: 5647 RVA: 0x00008F08 File Offset: 0x00007108
		public Color BorderColor { get; set; }

		// Token: 0x04000B21 RID: 2849
		[CompilerGenerated]
		private Color color_0;
	}
}
