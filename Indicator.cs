﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.Serialization;
using ns13;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020002A0 RID: 672
	[Serializable]
	internal abstract class Indicator : ISerializable, Interface1
	{
		// Token: 0x06001DCC RID: 7628
		protected abstract double[] GetUpdatedLastOutputs(int itemIdx, HisData newHd);

		// Token: 0x06001DCD RID: 7629
		public abstract List<IndParam> GetIndParams();

		// Token: 0x06001DCE RID: 7630
		public abstract void SetIndParams(List<IndParam> indParamLst);

		// Token: 0x06001DCF RID: 7631
		public abstract Indicator GetIndNameByCurve(CurveItem curve);

		// Token: 0x1400009F RID: 159
		// (add) Token: 0x06001DD0 RID: 7632 RVA: 0x000D3280 File Offset: 0x000D1480
		// (remove) Token: 0x06001DD1 RID: 7633 RVA: 0x000D32B8 File Offset: 0x000D14B8
		public event IndicatorEventHandler IndicatorRemoved;

		// Token: 0x06001DD2 RID: 7634 RVA: 0x000D32F0 File Offset: 0x000D14F0
		private void OnIndicatorRemoved()
		{
			IndicatorEventHandler indicatorRemoved = this.IndicatorRemoved;
			EventArgs27 e = new EventArgs27(this, this.Chart);
			if (indicatorRemoved != null)
			{
				indicatorRemoved(this, e);
			}
		}

		// Token: 0x06001DD3 RID: 7635 RVA: 0x00002D25 File Offset: 0x00000F25
		public Indicator()
		{
		}

		// Token: 0x06001DD4 RID: 7636 RVA: 0x000D3320 File Offset: 0x000D1520
		public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue(Class521.smethod_0(2464), this._ChartType);
			info.AddValue(Class521.smethod_0(49515), this._ChtCtrl_KLineTag);
			info.AddValue(Class521.smethod_0(86530), this._IndInputDataType);
		}

		// Token: 0x06001DD5 RID: 7637 RVA: 0x000D337C File Offset: 0x000D157C
		protected void RestoreSerializationValues(SerializationInfo info)
		{
			this.ChartType = (ChartType)info.GetValue(Class521.smethod_0(2464), typeof(ChartType));
			this.ChtCtrl_KLineTag = info.GetString(Class521.smethod_0(49515));
			this.IndInputDataType = (IndInputDataType)info.GetValue(Class521.smethod_0(86530), typeof(IndInputDataType));
		}

		// Token: 0x06001DD6 RID: 7638 RVA: 0x000D33EC File Offset: 0x000D15EC
		public virtual bool InitInd(ChartKLine chart)
		{
			if (chart != null)
			{
				this.Chart = chart;
				this.ChtCtrl_KLineTag = chart.ChtCtrl_KLine.Tag.ToString();
				this.ChartType = chart.ChartType;
				if (this.Chart.GraphPane != null)
				{
					this.Chart.GraphPane.YAxis.Scale.IsSkipFirstLabel = true;
					this.Chart.GraphPane.YAxis.Scale.IsSkipLastLabel = true;
					this.Chart.GraphPane.YAxis.Scale.MajorStepAuto = true;
					this.Chart.GraphPane.YAxis.Scale.MaxAuto = true;
					this.Chart.GraphPane.YAxis.Scale.MinAuto = true;
					this.Chart.GraphPane.YAxis.Scale.MinorStepAuto = true;
				}
			}
			return true;
		}

		// Token: 0x06001DD7 RID: 7639 RVA: 0x0000C88A File Offset: 0x0000AA8A
		public virtual void InitItem()
		{
			if (this._CurveList != null && this._CurveList.Count > 0)
			{
				this._CurveList = new List<IndCurve>();
			}
		}

		// Token: 0x06001DD8 RID: 7640 RVA: 0x0000C8AF File Offset: 0x0000AAAF
		public virtual void AddNewItem(int itemIdx)
		{
			this.AddNewItem(itemIdx, null, true);
		}

		// Token: 0x06001DD9 RID: 7641 RVA: 0x0000C8BC File Offset: 0x0000AABC
		public virtual void AddNewItem(int itemIdx, HDTick tick)
		{
			this.AddNewItem(itemIdx, Base.Data.smethod_114(tick), false);
		}

		// Token: 0x06001DDA RID: 7642 RVA: 0x0000C8CE File Offset: 0x0000AACE
		public virtual void AddNewItem(int itemIdx, HisData newhd)
		{
			this.AddNewItem(itemIdx, newhd, false);
		}

		// Token: 0x06001DDB RID: 7643
		public abstract void AddNewItem(int itemIdx, HisData newhd, bool ifAddCompleteItem);

		// Token: 0x06001DDC RID: 7644
		public abstract void UpdateLastItem(int itemIdx, HisData hd);

		// Token: 0x06001DDD RID: 7645
		public abstract void UpdateLastItem(int itemIdx, HDTick tick);

		// Token: 0x06001DDE RID: 7646 RVA: 0x000D34E0 File Offset: 0x000D16E0
		public virtual void ResetCurve()
		{
			foreach (IndCurve indCurve in this.CurveList)
			{
				if (indCurve != null)
				{
					indCurve.Curve.Clear();
					indCurve.Curve.IsVisible = false;
				}
			}
			if (!this.IsMainChartInd)
			{
				this.Chart.HeaderTextObj.IsVisible = false;
				this.Chart.HeaderTextObj.Text = Class521.smethod_0(1449);
			}
		}

		// Token: 0x06001DDF RID: 7647 RVA: 0x000D357C File Offset: 0x000D177C
		public virtual void RemoveFromChart()
		{
			this.ResetCurve();
			this.Chart.IndList.Remove(this);
			foreach (IndCurve indCurve in this.CurveList)
			{
				this.Chart.GraphPane.CurveList.Remove(indCurve.Curve);
			}
			if (!this.IsMainChartInd && this.Chart.IfYAxisOnlyDispZeroLabel)
			{
				this.Chart.IfYAxisOnlyDispZeroLabel = false;
			}
			this.OnIndicatorRemoved();
		}

		// Token: 0x06001DE0 RID: 7648 RVA: 0x0000C8DB File Offset: 0x0000AADB
		public virtual void ApplyTheme(ChartTheme theme)
		{
			if (this.IsSelected)
			{
				this.RemoveSelectedBoxPts();
				this.AddSelectedBoxPts();
			}
		}

		// Token: 0x06001DE1 RID: 7649 RVA: 0x000D3628 File Offset: 0x000D1828
		public virtual string GetInfoText()
		{
			string result;
			if (this.CurveList.Count <= 0)
			{
				result = Class521.smethod_0(1449);
			}
			else
			{
				int count = this.CurveList[0].Curve.Points.Count;
				int textShowIdx = this.GetTextShowIdx(count);
				if (textShowIdx < 0)
				{
					result = Class521.smethod_0(1449);
				}
				else
				{
					result = this.Desc + Class521.smethod_0(3636) + this.GetFormatedInfoTxt(textShowIdx);
				}
			}
			return result;
		}

		// Token: 0x06001DE2 RID: 7650 RVA: 0x000D36A8 File Offset: 0x000D18A8
		protected int GetTextShowIdx(int count)
		{
			int result;
			if (this.Chart.ChtCtrl_KLine.IsInCrossReviewMode)
			{
				if (this.Chart.ChtCtrl_KLine.RevCrossXVal != null)
				{
					int num = Convert.ToInt32(Math.Floor(this.Chart.ChtCtrl_KLine.RevCrossXVal.Value)) - 1;
					if (num >= count)
					{
						num = count - 1;
					}
					else if (num < 0)
					{
						num = 0;
					}
					result = num;
				}
				else
				{
					result = count - 1;
				}
			}
			else
			{
				result = count - 1;
			}
			return result;
		}

		// Token: 0x06001DE3 RID: 7651 RVA: 0x000D372C File Offset: 0x000D192C
		protected string GetTextFromList(List<string> names, List<double> numbs)
		{
			if (names.Count != numbs.Count)
			{
				throw new Exception(string.Format(Class521.smethod_0(86555), names.Count, numbs.Count));
			}
			int num = 2;
			if (this.Chart != null && this.Chart.Symbol != null)
			{
				num = this.Chart.Symbol.DigitNb;
			}
			if (num < 2)
			{
				num = 2;
			}
			string text = Class521.smethod_0(1449);
			for (int i = 0; i < names.Count; i++)
			{
				double num2 = numbs[i];
				if (!double.IsNaN(num2))
				{
					double num3 = Math.Abs(num2);
					if (num3 != 0.0)
					{
						if (num3 > 100000.0)
						{
							num = 0;
						}
						else if (num3 > 10000.0)
						{
							num = 1;
						}
						else if (num3 < 0.001)
						{
							num = 5;
						}
						else if (num3 < 0.01)
						{
							num = 4;
						}
						else if (num3 < 0.1)
						{
							num = 3;
						}
					}
					string text2 = string.Empty;
					if (names.Count > 1)
					{
						text2 = names[i];
						if (text2.Contains(Class521.smethod_0(2712)))
						{
							text2 = text2.Substring(text2.IndexOf(Class521.smethod_0(2712)) + 1);
						}
						text2 += Class521.smethod_0(50733);
					}
					text = text + Class521.smethod_0(3636) + text2 + Utility.GetValidStringFromDoubleVal(num2, num, Class521.smethod_0(1449));
				}
			}
			return text;
		}

		// Token: 0x06001DE4 RID: 7652 RVA: 0x000D38C4 File Offset: 0x000D1AC4
		protected virtual string GetFormatedInfoTxt(int idx)
		{
			string result;
			if (idx < 0)
			{
				result = string.Empty;
			}
			else
			{
				List<string> list = new List<string>();
				List<double> list2 = new List<double>();
				for (int i = 0; i < this.CurveList.Count; i++)
				{
					IPointListEdit pointListEdit = this.CurveList[i].Curve.Points as IPointListEdit;
					if (idx < pointListEdit.Count && idx >= 0)
					{
						double y = pointListEdit[idx].Y;
						list2.Add(y);
						list.Add(this.CurveList[i].Desc);
					}
					else
					{
						Class184.smethod_0(new ArgumentOutOfRangeException(string.Concat(new object[]
						{
							Class521.smethod_0(86660),
							idx,
							Class521.smethod_0(86733),
							pointListEdit.Count
						})));
					}
				}
				result = this.GetTextFromList(list, list2);
			}
			return result;
		}

		// Token: 0x06001DE5 RID: 7653 RVA: 0x000D39B8 File Offset: 0x000D1BB8
		private void AddSelectedBoxPts()
		{
			GraphPane graphPane = this.Chart.GraphPane;
			new PointPairList();
			int maxSticksPerChart = this.Chart.ChtCtrl_KLine.MaxSticksPerChart;
			int num = Convert.ToInt32(maxSticksPerChart / 7);
			if (num < 1)
			{
				num = 1;
			}
			double num2 = 0.6;
			if (maxSticksPerChart > 200)
			{
				num2 = num2 * (double)maxSticksPerChart / 200.0;
			}
			double num3 = (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) * num2 / (double)(graphPane.Rect.Height / graphPane.Rect.Width * (float)maxSticksPerChart);
			foreach (IndCurve indCurve in this.CurveList)
			{
				if (indCurve.Curve is LineItem && indCurve.Curve.Tag != null)
				{
					LineItem lineItem = indCurve.Curve as LineItem;
					Color color = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black;
					for (int i = 1; i < lineItem.NPts; i += num)
					{
						PointPair pointPair = lineItem.Points[i];
						BoxObj boxObj = new BoxObj((double)i + 0.6, pointPair.Y + num3 / 2.0, num2, num3, color, color);
						boxObj.Tag = Indicator.SELCURVEPT_TAG;
						boxObj.ZOrder = ZOrder.E_BehindCurves;
						boxObj.Location.CoordinateFrame = CoordType.AxisXYScale;
						graphPane.GraphObjList.Add(boxObj);
					}
				}
			}
			this.Chart.ZedGraphControl.Refresh();
		}

		// Token: 0x06001DE6 RID: 7654 RVA: 0x000D3B88 File Offset: 0x000D1D88
		protected void RemoveSelectedBoxPts()
		{
			GraphPane graphPane = this.Chart.GraphPane;
			if (graphPane != null && graphPane.GraphObjList != null)
			{
				List<BoxObj> list = graphPane.GraphObjList.Where(delegate(GraphObj o)
				{
					bool result;
					if (o is BoxObj)
					{
						result = (o.Tag.ToString() == Indicator.SELCURVEPT_TAG);
					}
					else
					{
						result = false;
					}
					return result;
				}).Cast<BoxObj>().ToList<BoxObj>();
				if (list.Any<BoxObj>())
				{
					graphPane.GraphObjList.RemoveAll(delegate(GraphObj o)
					{
						bool result;
						if (o is BoxObj)
						{
							result = (o.Tag.ToString() == Indicator.SELCURVEPT_TAG);
						}
						else
						{
							result = false;
						}
						return result;
					});
					for (int i = 0; i < list.Count; i++)
					{
						list[i] = null;
					}
					this.Chart.ZedGraphControl.Refresh();
				}
			}
		}

		// Token: 0x06001DE7 RID: 7655 RVA: 0x000D3C48 File Offset: 0x000D1E48
		protected virtual string GetDesc()
		{
			return this._Desc;
		}

		// Token: 0x06001DE8 RID: 7656 RVA: 0x000041B9 File Offset: 0x000023B9
		public virtual void RescaleAxis()
		{
		}

		// Token: 0x06001DE9 RID: 7657 RVA: 0x000D3C60 File Offset: 0x000D1E60
		public static Indicator GetInd(string indClassName)
		{
			return Activator.CreateInstance(Type.GetType(indClassName)) as Indicator;
		}

		// Token: 0x170004AE RID: 1198
		// (get) Token: 0x06001DEA RID: 7658 RVA: 0x000D3C84 File Offset: 0x000D1E84
		// (set) Token: 0x06001DEB RID: 7659 RVA: 0x0000C8F3 File Offset: 0x0000AAF3
		public int StartIdx
		{
			get
			{
				return this._startIdx;
			}
			set
			{
				this._startIdx = value;
			}
		}

		// Token: 0x170004AF RID: 1199
		// (get) Token: 0x06001DEC RID: 7660 RVA: 0x000D3C9C File Offset: 0x000D1E9C
		// (set) Token: 0x06001DED RID: 7661 RVA: 0x0000C8FE File Offset: 0x0000AAFE
		public int EndIdx
		{
			get
			{
				return this._endIdx;
			}
			set
			{
				this._endIdx = value;
			}
		}

		// Token: 0x170004B0 RID: 1200
		// (get) Token: 0x06001DEE RID: 7662 RVA: 0x000D3CB4 File Offset: 0x000D1EB4
		// (set) Token: 0x06001DEF RID: 7663 RVA: 0x0000C909 File Offset: 0x0000AB09
		public int OutBegIdx
		{
			get
			{
				return this._outBegIdx;
			}
			set
			{
				this._outBegIdx = value;
			}
		}

		// Token: 0x170004B1 RID: 1201
		// (get) Token: 0x06001DF0 RID: 7664 RVA: 0x000D3CCC File Offset: 0x000D1ECC
		// (set) Token: 0x06001DF1 RID: 7665 RVA: 0x0000C914 File Offset: 0x0000AB14
		public int OutNbElement
		{
			get
			{
				return this._outNbElement;
			}
			set
			{
				this._outNbElement = value;
			}
		}

		// Token: 0x170004B2 RID: 1202
		// (get) Token: 0x06001DF2 RID: 7666 RVA: 0x000D3CE4 File Offset: 0x000D1EE4
		public double[] TALibInputClose
		{
			get
			{
				return this.Chart.HisDataPeriodSet.CloseDataArray;
			}
		}

		// Token: 0x170004B3 RID: 1203
		// (get) Token: 0x06001DF3 RID: 7667 RVA: 0x000D3D08 File Offset: 0x000D1F08
		public double[] TALibInputHigh
		{
			get
			{
				return this.Chart.HisDataPeriodSet.HighDataArray;
			}
		}

		// Token: 0x170004B4 RID: 1204
		// (get) Token: 0x06001DF4 RID: 7668 RVA: 0x000D3D2C File Offset: 0x000D1F2C
		public double[] TALibInputLow
		{
			get
			{
				return this.Chart.HisDataPeriodSet.LowDataArray;
			}
		}

		// Token: 0x170004B5 RID: 1205
		// (get) Token: 0x06001DF5 RID: 7669 RVA: 0x000D3D50 File Offset: 0x000D1F50
		public double[] TALibInputVol
		{
			get
			{
				return this.Chart.HisDataPeriodSet.VolDataArray;
			}
		}

		// Token: 0x170004B6 RID: 1206
		// (get) Token: 0x06001DF6 RID: 7670 RVA: 0x000D3D74 File Offset: 0x000D1F74
		// (set) Token: 0x06001DF7 RID: 7671 RVA: 0x0000C91F File Offset: 0x0000AB1F
		public int CSticksCount
		{
			get
			{
				return this._csticksCount;
			}
			set
			{
				this._csticksCount = value;
			}
		}

		// Token: 0x170004B7 RID: 1207
		// (get) Token: 0x06001DF8 RID: 7672 RVA: 0x000D3D8C File Offset: 0x000D1F8C
		// (set) Token: 0x06001DF9 RID: 7673 RVA: 0x0000C92A File Offset: 0x0000AB2A
		public ChartKLine Chart
		{
			get
			{
				return this._Chart;
			}
			set
			{
				this._Chart = value;
			}
		}

		// Token: 0x170004B8 RID: 1208
		// (get) Token: 0x06001DFA RID: 7674 RVA: 0x000D3DA4 File Offset: 0x000D1FA4
		// (set) Token: 0x06001DFB RID: 7675 RVA: 0x0000C935 File Offset: 0x0000AB35
		public virtual string CnName
		{
			get
			{
				return this._CnName;
			}
			protected set
			{
				this._CnName = value;
			}
		}

		// Token: 0x170004B9 RID: 1209
		// (get) Token: 0x06001DFC RID: 7676 RVA: 0x000D3DBC File Offset: 0x000D1FBC
		// (set) Token: 0x06001DFD RID: 7677 RVA: 0x0000C940 File Offset: 0x0000AB40
		public virtual string EnName
		{
			get
			{
				return this._EnName;
			}
			protected set
			{
				this._EnName = value;
			}
		}

		// Token: 0x170004BA RID: 1210
		// (get) Token: 0x06001DFE RID: 7678 RVA: 0x000D3DD4 File Offset: 0x000D1FD4
		public virtual string Desc
		{
			get
			{
				return this.GetDesc();
			}
		}

		// Token: 0x170004BB RID: 1211
		// (get) Token: 0x06001DFF RID: 7679 RVA: 0x000D3DEC File Offset: 0x000D1FEC
		// (set) Token: 0x06001E00 RID: 7680 RVA: 0x0000C94B File Offset: 0x0000AB4B
		public bool IsMainChartInd
		{
			get
			{
				return this._IsMainChartInd;
			}
			protected set
			{
				this._IsMainChartInd = value;
			}
		}

		// Token: 0x170004BC RID: 1212
		// (get) Token: 0x06001E01 RID: 7681 RVA: 0x000D3E04 File Offset: 0x000D2004
		// (set) Token: 0x06001E02 RID: 7682 RVA: 0x0000C956 File Offset: 0x0000AB56
		public ChartType ChartType
		{
			get
			{
				return this._ChartType;
			}
			set
			{
				this._ChartType = value;
			}
		}

		// Token: 0x170004BD RID: 1213
		// (get) Token: 0x06001E03 RID: 7683 RVA: 0x000D3E1C File Offset: 0x000D201C
		// (set) Token: 0x06001E04 RID: 7684 RVA: 0x0000C961 File Offset: 0x0000AB61
		public string ChtCtrl_KLineTag
		{
			get
			{
				string result;
				if (this._ChtCtrl_KLineTag.Length == 0)
				{
					result = this.Chart.ChtCtrl_KLine.Tag.ToString();
				}
				else
				{
					result = this._ChtCtrl_KLineTag;
				}
				return result;
			}
			set
			{
				this._ChtCtrl_KLineTag = value;
			}
		}

		// Token: 0x170004BE RID: 1214
		// (get) Token: 0x06001E05 RID: 7685 RVA: 0x000D3E58 File Offset: 0x000D2058
		// (set) Token: 0x06001E06 RID: 7686 RVA: 0x0000C96C File Offset: 0x0000AB6C
		public IndInputDataType IndInputDataType
		{
			get
			{
				return this._IndInputDataType;
			}
			set
			{
				this._IndInputDataType = value;
			}
		}

		// Token: 0x170004BF RID: 1215
		// (get) Token: 0x06001E07 RID: 7687 RVA: 0x000D3E70 File Offset: 0x000D2070
		public int IndexOfLastItemShown
		{
			get
			{
				return this.Chart.ChtCtrl_KLine.IndexOfLastItemShown;
			}
		}

		// Token: 0x170004C0 RID: 1216
		// (get) Token: 0x06001E08 RID: 7688 RVA: 0x000D3E94 File Offset: 0x000D2094
		public int IndexOfFirstItemShown
		{
			get
			{
				return this.Chart.ChtCtrl_KLine.IndexOfFirstItemShown;
			}
		}

		// Token: 0x170004C1 RID: 1217
		// (get) Token: 0x06001E09 RID: 7689 RVA: 0x000D3EB8 File Offset: 0x000D20B8
		public int MaxSticksPerChart
		{
			get
			{
				return this.Chart.ChtCtrl_KLine.MaxSticksPerChart;
			}
		}

		// Token: 0x170004C2 RID: 1218
		// (get) Token: 0x06001E0A RID: 7690 RVA: 0x000D3EDC File Offset: 0x000D20DC
		// (set) Token: 0x06001E0B RID: 7691 RVA: 0x0000C977 File Offset: 0x0000AB77
		public List<IndCurve> CurveList
		{
			get
			{
				if (this._CurveList == null)
				{
					this._CurveList = new List<IndCurve>();
				}
				return this._CurveList;
			}
			set
			{
				this._CurveList = value;
			}
		}

		// Token: 0x170004C3 RID: 1219
		// (get) Token: 0x06001E0C RID: 7692 RVA: 0x000D3F08 File Offset: 0x000D2108
		// (set) Token: 0x06001E0D RID: 7693 RVA: 0x000D3F20 File Offset: 0x000D2120
		public bool IsSelected
		{
			get
			{
				return this._IsSelected;
			}
			set
			{
				if (value)
				{
					if (this.Chart.ChtCtrl_KLine.SelectedInd != null)
					{
						this.Chart.ChtCtrl_KLine.SelectedInd.IsSelected = false;
					}
					this.Chart.ChtCtrl_KLine.SelectedInd = this;
					this.AddSelectedBoxPts();
				}
				else
				{
					this.RemoveSelectedBoxPts();
					this.Chart.ChtCtrl_KLine.SelectedInd = null;
				}
				this._IsSelected = value;
			}
		}

		// Token: 0x170004C4 RID: 1220
		// (get) Token: 0x06001E0E RID: 7694 RVA: 0x000D3F94 File Offset: 0x000D2194
		public ChtCtrl_KLine ChtCtrl_KLine
		{
			get
			{
				return (ChtCtrl_KLine)this.Chart.ChtCtrl;
			}
		}

		// Token: 0x04000EB8 RID: 3768
		public static readonly string SELCURVEPT_TAG = Class521.smethod_0(86754);

		// Token: 0x04000EB9 RID: 3769
		private string _Desc;

		// Token: 0x04000EBA RID: 3770
		protected int _startIdx;

		// Token: 0x04000EBB RID: 3771
		protected int _endIdx;

		// Token: 0x04000EBC RID: 3772
		private int _outBegIdx;

		// Token: 0x04000EBD RID: 3773
		private int _outNbElement;

		// Token: 0x04000EBE RID: 3774
		private int _csticksCount;

		// Token: 0x04000EBF RID: 3775
		private ChartKLine _Chart;

		// Token: 0x04000EC0 RID: 3776
		private string _CnName;

		// Token: 0x04000EC1 RID: 3777
		private string _EnName;

		// Token: 0x04000EC2 RID: 3778
		private bool _IsMainChartInd;

		// Token: 0x04000EC3 RID: 3779
		private ChartType _ChartType;

		// Token: 0x04000EC4 RID: 3780
		private string _ChtCtrl_KLineTag;

		// Token: 0x04000EC5 RID: 3781
		private IndInputDataType _IndInputDataType;

		// Token: 0x04000EC6 RID: 3782
		private List<IndCurve> _CurveList;

		// Token: 0x04000EC7 RID: 3783
		private bool _IsSelected;
	}
}
