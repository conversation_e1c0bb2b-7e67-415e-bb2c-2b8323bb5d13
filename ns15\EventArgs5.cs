﻿using System;

namespace ns15
{
	// Token: 0x0200008B RID: 139
	internal sealed class EventArgs5 : EventArgs
	{
		// Token: 0x0600048F RID: 1167 RVA: 0x00003F57 File Offset: 0x00002157
		public EventArgs5(string string_1, bool bool_1)
		{
			this.string_0 = string_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x170000F0 RID: 240
		// (get) Token: 0x06000490 RID: 1168 RVA: 0x000249AC File Offset: 0x00022BAC
		public string Msg
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x170000F1 RID: 241
		// (get) Token: 0x06000491 RID: 1169 RVA: 0x000249C4 File Offset: 0x00022BC4
		public bool IsNewMsg
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x040001BE RID: 446
		private string string_0;

		// Token: 0x040001BF RID: 447
		private bool bool_0;
	}
}
