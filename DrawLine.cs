﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns13;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000074 RID: 116
	[Serializable]
	internal class DrawLine : DrawObj, ISerializable
	{
		// Token: 0x0600042D RID: 1069 RVA: 0x00003742 File Offset: 0x00001942
		public DrawLine()
		{
		}

		// Token: 0x0600042E RID: 1070 RVA: 0x00003CB8 File Offset: 0x00001EB8
		public DrawLine(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(27647);
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x0600042F RID: 1071 RVA: 0x00003779 File Offset: 0x00001979
		protected DrawLine(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000430 RID: 1072 RVA: 0x0000378A File Offset: 0x0000198A
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000431 RID: 1073 RVA: 0x000235E8 File Offset: 0x000217E8
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = this.method_40(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x06000432 RID: 1074 RVA: 0x00023618 File Offset: 0x00021818
		protected LineObj method_39(ChartCS chartCS_1, Location location_1, string string_5)
		{
			return this.method_40(chartCS_1, location_1.X1, location_1.Y1, location_1.X2, location_1.Y2, string_5);
		}

		// Token: 0x06000433 RID: 1075 RVA: 0x0002364C File Offset: 0x0002184C
		public static Class60 smethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			double num;
			double num2;
			double num3;
			double num4;
			if (double_1 == double_3)
			{
				num = double_1;
				num2 = double_3;
				num3 = chartCS_1.GraphPane.YAxis.Scale.Min;
				num4 = chartCS_1.GraphPane.YAxis.Scale.Max;
			}
			else if (double_2 != double_4)
			{
				if (double_4 > double_2)
				{
					num3 = chartCS_1.GraphPane.YAxis.Scale.Min;
					num4 = chartCS_1.GraphPane.YAxis.Scale.Max;
				}
				else
				{
					num3 = chartCS_1.GraphPane.YAxis.Scale.Max;
					num4 = chartCS_1.GraphPane.YAxis.Scale.Min;
				}
				num = double_3 - (double_4 - num3) / (double_4 - double_2) * (double_3 - double_1);
				if (num < chartCS_1.GraphPane.XAxis.Scale.Min)
				{
					num = chartCS_1.GraphPane.XAxis.Scale.Min;
					num3 = double_4 - (double_4 - double_2) * (double_3 - num) / (double_3 - double_1);
				}
				else if (num > chartCS_1.GraphPane.XAxis.Scale.Max)
				{
					num = chartCS_1.GraphPane.XAxis.Scale.Max;
					num3 = double_4 - (double_4 - double_2) * (double_3 - num) / (double_3 - double_1);
				}
				num2 = (num4 - double_2) / (double_4 - double_2) * (double_3 - double_1) + double_1;
				if (num2 < chartCS_1.GraphPane.XAxis.Scale.Min)
				{
					num2 = chartCS_1.GraphPane.XAxis.Scale.Min;
					num4 = (num2 - double_1) / (double_3 - double_1) * (double_4 - double_2) + double_2;
				}
				else if (num2 > chartCS_1.GraphPane.XAxis.Scale.Max)
				{
					num2 = chartCS_1.GraphPane.XAxis.Scale.Max;
					num4 = (num2 - double_1) / (double_3 - double_1) * (double_4 - double_2) + double_2;
				}
			}
			else
			{
				num = chartCS_1.GraphPane.XAxis.Scale.Min;
				num2 = chartCS_1.GraphPane.XAxis.Scale.Max;
				num3 = double_2;
				num4 = double_4;
			}
			return new Class60(num, num3, num2, num4);
		}

		// Token: 0x06000434 RID: 1076 RVA: 0x00023854 File Offset: 0x00021A54
		protected LineObj method_40(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			Class60 @class = DrawLine.smethod_0(chartCS_1, double_1, double_2, double_3, double_4);
			double x = @class.X1;
			double y = @class.Y1;
			double x2 = @class.X2;
			double y2 = @class.Y2;
			return base.method_23(x, y, x2, y2, string_5);
		}
	}
}
