﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;

namespace ns18
{
	// Token: 0x020003D5 RID: 981
	internal sealed class StringResourceManager
	{
		// Token: 0x06002705 RID: 9989 RVA: 0x00106990 File Offset: 0x00104B90
		public static string GetString(int stringId)
		{
			stringId -= StringResourceManager.baseOffset;
			string result;
			if (!StringResourceManager.useCaching)
			{
				result = StringResourceManager.LoadStringFromResource(stringId);
			}
			else
			{
				result = StringResourceManager.GetCachedString(stringId);
			}
			return result;
		}

		// Token: 0x06002706 RID: 9990 RVA: 0x001069C0 File Offset: 0x00104BC0
		public static string GetCachedString(int stringId)
		{
			object obj = StringResourceManager.cacheLock;
			string result;
			lock (obj)
			{
				string text;
				StringResourceManager.stringCache.TryGetValue(stringId, out text);
				if (text != null)
				{
					result = text;
					goto IL_33;
				}
			}
			return StringResourceManager.LoadStringFromResource(stringId);
			IL_33:
			return result;
		}

		// Token: 0x06002707 RID: 9991 RVA: 0x00106A18 File Offset: 0x00104C18
		public static string LoadStringFromResource(int stringId)
		{
			byte[] array = Class521.byte_0;
			int index = int_1 + 1;
			int num = array[int_1];
			int num2;
			if ((num & 128) == 0)
			{
				num2 = num;
				if (num2 == 0)
				{
					return string.Empty;
				}
			}
			else if ((num & 64) == 0)
			{
				num2 = ((num & 63) << 8) + (int)Class521.byte_0[index++];
			}
			else
			{
				num2 = ((num & 31) << 24) + ((int)Class521.byte_0[index++] << 16) + ((int)Class521.byte_0[index++] << 8) + (int)Class521.byte_0[index++];
			}
			string result;
			try
			{
				byte[] array2 = Convert.FromBase64String(Encoding.UTF8.GetString(Class521.byte_0, index, num2));
				string text = string.Intern(Encoding.UTF8.GetString(array2, 0, array2.Length));
				if (Class521.bool_0)
				{
					Class521.smethod_3(int_1, text);
				}
				result = text;
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002708 RID: 9992 RVA: 0x00106AFC File Offset: 0x00104CFC
		public static void smethod_3(int int_1, string string_2)
		{
			try
			{
				object obj = Class521.object_0;
				lock (obj)
				{
					Class521.dictionary_0.Add(int_1, string_2);
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002709 RID: 9993 RVA: 0x00106B50 File Offset: 0x00104D50
		static Class521()
		{
			if (Class521.string_0 == "1")
			{
				Class521.bool_0 = true;
				Class521.dictionary_0 = new Dictionary<int, string>();
			}
			Class521.int_0 = Convert.ToInt32(Class521.string_1);
			using (Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream("{c01b6ddb-68ca-48e0-87b3-8499e5b3ee30}"))
			{
				int num = Convert.ToInt32(manifestResourceStream.Length);
				Class521.byte_0 = new byte[num];
				manifestResourceStream.Read(Class521.byte_0, 0, num);
			}
		}

		// Token: 0x040012ED RID: 4845
		private static readonly string string_0 = "0";

		// Token: 0x040012EE RID: 4846
		private static readonly string string_1 = "93";

		// Token: 0x040012EF RID: 4847
		private static readonly byte[] byte_0 = null;

		// Token: 0x040012F0 RID: 4848
		private static readonly Dictionary<int, string> stringCache;

		// Token: 0x040012F1 RID: 4849
		private static readonly object cacheLock = new object();

		// Token: 0x040012F2 RID: 4850
		private static readonly bool useCaching = false;

		// Token: 0x040012F3 RID: 4851
		private static readonly int baseOffset = 0;
	}
}
