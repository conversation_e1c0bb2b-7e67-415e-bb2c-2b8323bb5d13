﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;

namespace ns30
{
	// Token: 0x0200022B RID: 555
	[DesignerCategory("code")]
	internal sealed class Class304 : NumericUpDown
	{
		// Token: 0x060016FD RID: 5885 RVA: 0x000A00D0 File Offset: 0x0009E2D0
		public Class304()
		{
			base.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
			base.SetStyle(ControlStyles.Opaque, true);
			this.textBox_0 = Class304.smethod_0<TextBox>(this, Class521.smethod_0(59214));
			if (this.textBox_0 == null)
			{
				throw new ArgumentNullException(base.GetType().FullName + Class521.smethod_0(59231));
			}
			this.control_0 = Class304.smethod_0<Control>(this, Class521.smethod_0(59280));
			if (this.control_0 == null)
			{
				throw new ArgumentNullException(base.GetType().FullName + Class521.smethod_0(59301));
			}
			this.textBox_0.MouseEnter += this.Class304_MouseLeave;
			this.textBox_0.MouseLeave += this.Class304_MouseLeave;
			this.control_0.MouseEnter += this.Class304_MouseLeave;
			this.control_0.MouseLeave += this.Class304_MouseLeave;
			base.MouseEnter += this.Class304_MouseLeave;
			base.MouseLeave += this.Class304_MouseLeave;
		}

		// Token: 0x060016FE RID: 5886 RVA: 0x000A01F8 File Offset: 0x0009E3F8
		protected internal static T smethod_0<T>(Class304 class304_0, string string_0) where T : Control
		{
			FieldInfo field = typeof(NumericUpDown).GetField(string_0, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy);
			T result;
			if (field == null)
			{
				result = default(T);
			}
			else
			{
				result = (field.GetValue(class304_0) as T);
			}
			return result;
		}

		// Token: 0x060016FF RID: 5887 RVA: 0x000096A2 File Offset: 0x000078A2
		protected void OnPaint(PaintEventArgs e)
		{
			if (!this.control_0.Visible)
			{
				e.Graphics.Clear(this.BackColor);
			}
			base.OnPaint(e);
		}

		// Token: 0x06001700 RID: 5888 RVA: 0x000A023C File Offset: 0x0009E43C
		protected void WndProc(ref Message m)
		{
			if (m.Msg == 522)
			{
				switch (this.enum21_0)
				{
				case Class304.Enum21.const_0:
					base.WndProc(ref m);
					break;
				case Class304.Enum21.const_1:
					if (this.bool_2)
					{
						base.WndProc(ref m);
					}
					break;
				}
			}
			else
			{
				base.WndProc(ref m);
			}
		}

		// Token: 0x170003BF RID: 959
		// (get) Token: 0x06001701 RID: 5889 RVA: 0x000A0298 File Offset: 0x0009E498
		// (set) Token: 0x06001702 RID: 5890 RVA: 0x000096CB File Offset: 0x000078CB
		[Description("Automatically select control text when it receives focus.")]
		[Category("Behavior")]
		[DefaultValue(false)]
		public bool AutoSelect
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170003C0 RID: 960
		// (get) Token: 0x06001703 RID: 5891 RVA: 0x000A02B0 File Offset: 0x0009E4B0
		// (set) Token: 0x06001704 RID: 5892 RVA: 0x000096D6 File Offset: 0x000078D6
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public int SelectionStart
		{
			get
			{
				return this.textBox_0.SelectionStart;
			}
			set
			{
				this.textBox_0.SelectionStart = value;
			}
		}

		// Token: 0x170003C1 RID: 961
		// (get) Token: 0x06001705 RID: 5893 RVA: 0x000A02CC File Offset: 0x0009E4CC
		// (set) Token: 0x06001706 RID: 5894 RVA: 0x000096E6 File Offset: 0x000078E6
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public int SelectionLength
		{
			get
			{
				return this.textBox_0.SelectionLength;
			}
			set
			{
				this.textBox_0.SelectionLength = value;
			}
		}

		// Token: 0x170003C2 RID: 962
		// (get) Token: 0x06001707 RID: 5895 RVA: 0x000A02E8 File Offset: 0x0009E4E8
		// (set) Token: 0x06001708 RID: 5896 RVA: 0x000096F6 File Offset: 0x000078F6
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public string SelectedText
		{
			get
			{
				return this.textBox_0.SelectedText;
			}
			set
			{
				this.textBox_0.SelectedText = value;
			}
		}

		// Token: 0x170003C3 RID: 963
		// (get) Token: 0x06001709 RID: 5897 RVA: 0x000A0304 File Offset: 0x0009E504
		// (set) Token: 0x0600170A RID: 5898 RVA: 0x00009706 File Offset: 0x00007906
		[Description("Enables MouseWheel only under certain conditions.")]
		[Category("Behavior")]
		[DefaultValue(typeof(Class304.Enum21), "Always")]
		public Class304.Enum21 InterceptMouseWheel
		{
			get
			{
				return this.enum21_0;
			}
			set
			{
				this.enum21_0 = value;
			}
		}

		// Token: 0x170003C4 RID: 964
		// (get) Token: 0x0600170B RID: 5899 RVA: 0x000A031C File Offset: 0x0009E51C
		// (set) Token: 0x0600170C RID: 5900 RVA: 0x00009711 File Offset: 0x00007911
		[Description("Set UpDownButtons visibility mode.")]
		[Category("Behavior")]
		[DefaultValue(typeof(Class304.Enum22), "Always")]
		public Class304.Enum22 ShowUpDownButtons
		{
			get
			{
				return this.enum22_0;
			}
			set
			{
				this.enum22_0 = value;
				this.method_0();
			}
		}

		// Token: 0x170003C5 RID: 965
		// (get) Token: 0x0600170D RID: 5901 RVA: 0x000A0334 File Offset: 0x0009E534
		// (set) Token: 0x0600170E RID: 5902 RVA: 0x00009722 File Offset: 0x00007922
		[Description("If set, incrementing value will cause it to restart from Minimum when Maximum is reached (and viceversa).")]
		[Category("Behavior")]
		[DefaultValue(false)]
		public bool WrapValue
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x0600170F RID: 5903 RVA: 0x0000972D File Offset: 0x0000792D
		protected void OnGotFocus(EventArgs e)
		{
			this.bool_3 = true;
			if (this.bool_0)
			{
				this.textBox_0.SelectAll();
			}
			if (this.enum22_0 == Class304.Enum22.const_2 | this.enum22_0 == Class304.Enum22.const_3)
			{
				this.method_0();
			}
			base.OnGotFocus(e);
		}

		// Token: 0x06001710 RID: 5904 RVA: 0x0000976D File Offset: 0x0000796D
		protected void OnLostFocus(EventArgs e)
		{
			this.bool_3 = false;
			if (this.enum22_0 == Class304.Enum22.const_2 | this.enum22_0 == Class304.Enum22.const_3)
			{
				this.method_0();
			}
			base.OnLostFocus(e);
		}

		// Token: 0x06001711 RID: 5905 RVA: 0x0000979A File Offset: 0x0000799A
		protected void OnMouseUp(MouseEventArgs mevent)
		{
			if (this.bool_0 && this.textBox_0.SelectionLength == 0)
			{
				this.textBox_0.SelectAll();
			}
			base.OnMouseUp(mevent);
		}

		// Token: 0x14000083 RID: 131
		// (add) Token: 0x06001712 RID: 5906 RVA: 0x000A034C File Offset: 0x0009E54C
		// (remove) Token: 0x06001713 RID: 5907 RVA: 0x000A0384 File Offset: 0x0009E584
		public new event EventHandler<EventArgs> MouseEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_0;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_0;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x14000084 RID: 132
		// (add) Token: 0x06001714 RID: 5908 RVA: 0x000A03BC File Offset: 0x0009E5BC
		// (remove) Token: 0x06001715 RID: 5909 RVA: 0x000A03F4 File Offset: 0x0009E5F4
		public new event EventHandler<EventArgs> MouseLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_1;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_1;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x14000085 RID: 133
		// (add) Token: 0x06001716 RID: 5910 RVA: 0x000A042C File Offset: 0x0009E62C
		// (remove) Token: 0x06001717 RID: 5911 RVA: 0x000A0464 File Offset: 0x0009E664
		public event CancelEventHandler BeforeValueDecrement
		{
			[CompilerGenerated]
			add
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_0;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Combine(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_0, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_0;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Remove(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_0, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
		}

		// Token: 0x14000086 RID: 134
		// (add) Token: 0x06001718 RID: 5912 RVA: 0x000A049C File Offset: 0x0009E69C
		// (remove) Token: 0x06001719 RID: 5913 RVA: 0x000A04D4 File Offset: 0x0009E6D4
		public event CancelEventHandler BeforeValueIncrement
		{
			[CompilerGenerated]
			add
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_1;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Combine(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_1, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_1;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Remove(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_1, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
		}

		// Token: 0x0600171A RID: 5914 RVA: 0x000A050C File Offset: 0x0009E70C
		private void Class304_MouseLeave(object sender, EventArgs e)
		{
			Rectangle rectangle = base.RectangleToScreen(base.ClientRectangle);
			Point mousePosition = Control.MousePosition;
			bool flag = rectangle.Contains(mousePosition);
			if (this.bool_2 ^ flag)
			{
				this.bool_2 = flag;
				if (this.bool_2)
				{
					if (this.eventHandler_0 != null)
					{
						this.eventHandler_0(this, EventArgs.Empty);
					}
				}
				else if (this.eventHandler_1 != null)
				{
					this.eventHandler_1(this, EventArgs.Empty);
				}
			}
			if (this.enum22_0 != Class304.Enum22.const_0)
			{
				this.method_0();
			}
		}

		// Token: 0x0600171B RID: 5915 RVA: 0x000A0594 File Offset: 0x0009E794
		public void DownButton()
		{
			if (!base.ReadOnly)
			{
				CancelEventArgs cancelEventArgs = new CancelEventArgs();
				if (this.cancelEventHandler_0 != null)
				{
					this.cancelEventHandler_0(this, cancelEventArgs);
				}
				if (!cancelEventArgs.Cancel)
				{
					if (this.bool_1 && base.Value - base.Increment < base.Minimum)
					{
						base.Value = base.Maximum;
					}
					else
					{
						base.DownButton();
					}
				}
			}
		}

		// Token: 0x0600171C RID: 5916 RVA: 0x000A060C File Offset: 0x0009E80C
		public void UpButton()
		{
			if (!base.ReadOnly)
			{
				CancelEventArgs cancelEventArgs = new CancelEventArgs();
				if (this.cancelEventHandler_1 != null)
				{
					this.cancelEventHandler_1(this, cancelEventArgs);
				}
				if (!cancelEventArgs.Cancel)
				{
					if (this.bool_1 && base.Value + base.Increment > base.Maximum)
					{
						base.Value = base.Minimum;
					}
					else
					{
						base.UpButton();
					}
				}
			}
		}

		// Token: 0x0600171D RID: 5917 RVA: 0x000A0684 File Offset: 0x0009E884
		public void method_0()
		{
			bool flag;
			switch (this.enum22_0)
			{
			case Class304.Enum22.const_1:
				flag = this.bool_2;
				break;
			case Class304.Enum22.const_2:
				flag = this.bool_3;
				break;
			case Class304.Enum22.const_3:
				flag = (this.bool_2 | this.bool_3);
				break;
			default:
				flag = true;
				break;
			}
			if (this.control_0.Visible != flag)
			{
				if (flag)
				{
					this.textBox_0.Width = base.ClientRectangle.Width - this.control_0.Width;
				}
				else
				{
					this.textBox_0.Width = base.ClientRectangle.Width;
				}
				this.control_0.Visible = flag;
				this.OnTextBoxResize(this.textBox_0, EventArgs.Empty);
				base.Invalidate();
			}
		}

		// Token: 0x0600171E RID: 5918 RVA: 0x000A0748 File Offset: 0x0009E948
		protected void OnTextBoxResize(object source, EventArgs e)
		{
			if (this.textBox_0 != null)
			{
				if (this.enum22_0 == Class304.Enum22.const_0)
				{
					base.OnTextBoxResize(source, e);
				}
				else
				{
					bool flag = this.RightToLeft == RightToLeft.Yes ^ base.UpDownAlign == LeftRightAlignment.Left;
					if (this.bool_2)
					{
						this.textBox_0.Width = base.ClientSize.Width - this.textBox_0.Left - this.control_0.Width - 2;
						if (flag)
						{
							this.textBox_0.Location = new Point(16, this.textBox_0.Location.Y);
						}
					}
					else
					{
						if (flag)
						{
							this.textBox_0.Location = new Point(2, this.textBox_0.Location.Y);
						}
						this.textBox_0.Width = base.ClientSize.Width - this.textBox_0.Left - 2;
					}
				}
			}
		}

		// Token: 0x04000BBC RID: 3004
		private TextBox textBox_0;

		// Token: 0x04000BBD RID: 3005
		private Control control_0;

		// Token: 0x04000BBE RID: 3006
		private bool bool_0;

		// Token: 0x04000BBF RID: 3007
		private Class304.Enum21 enum21_0;

		// Token: 0x04000BC0 RID: 3008
		private Class304.Enum22 enum22_0;

		// Token: 0x04000BC1 RID: 3009
		private bool bool_1;

		// Token: 0x04000BC2 RID: 3010
		[CompilerGenerated]
		private EventHandler<EventArgs> eventHandler_0;

		// Token: 0x04000BC3 RID: 3011
		[CompilerGenerated]
		private EventHandler<EventArgs> eventHandler_1;

		// Token: 0x04000BC4 RID: 3012
		[CompilerGenerated]
		private CancelEventHandler cancelEventHandler_0;

		// Token: 0x04000BC5 RID: 3013
		[CompilerGenerated]
		private CancelEventHandler cancelEventHandler_1;

		// Token: 0x04000BC6 RID: 3014
		private bool bool_2;

		// Token: 0x04000BC7 RID: 3015
		private bool bool_3;

		// Token: 0x0200022C RID: 556
		public enum Enum21
		{
			// Token: 0x04000BC9 RID: 3017
			const_0,
			// Token: 0x04000BCA RID: 3018
			const_1,
			// Token: 0x04000BCB RID: 3019
			const_2
		}

		// Token: 0x0200022D RID: 557
		public enum Enum22
		{
			// Token: 0x04000BCD RID: 3021
			const_0,
			// Token: 0x04000BCE RID: 3022
			const_1,
			// Token: 0x04000BCF RID: 3023
			const_2,
			// Token: 0x04000BD0 RID: 3024
			const_3
		}
	}
}
