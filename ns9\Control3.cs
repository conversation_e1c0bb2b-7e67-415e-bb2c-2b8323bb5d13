﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns12;
using ns18;
using ns30;
using TEx;

namespace ns9
{
	// Token: 0x02000233 RID: 563
	internal sealed class Control3 : UserControl
	{
		// Token: 0x0600177E RID: 6014 RVA: 0x000A31A0 File Offset: 0x000A13A0
		public Control3()
		{
			this.method_2();
			this.textBox_0.KeyDown += this.textBox_0_KeyDown;
			this.textBox_0.KeyPress += this.textBox_0_KeyPress;
			this.textBox_0.KeyUp += this.textBox_0_KeyUp;
			this.textBox_0.LostFocus += this.textBox_0_LostFocus;
		}

		// Token: 0x0600177F RID: 6015 RVA: 0x000A3218 File Offset: 0x000A1418
		public Control3(Class280 class280_1) : this()
		{
			this.HotKey = class280_1;
			this.label_0.Text = this.HotKey.CnName;
			this.textBox_0.Text = ((this.HotKey.KeyModifier == KeyModifiers.None) ? Class521.smethod_0(1449) : (this.HotKey.KeyModifier.ToString().Replace(Class521.smethod_0(2712), Class521.smethod_0(59548)) + Class521.smethod_0(59548))) + this.method_1(this.HotKey.Key);
			this.Modifier = this.HotKey.KeyModifier;
			this.Key = this.HotKey.Key;
		}

		// Token: 0x06001780 RID: 6016 RVA: 0x00009992 File Offset: 0x00007B92
		public Control3(Class266 class266_1) : this()
		{
			this.FnKey = class266_1;
			this.label_0.Text = this.FnKey.CnName;
			this.textBox_0.Text = this.FnKey.KeyStr;
		}

		// Token: 0x170003DC RID: 988
		// (get) Token: 0x06001781 RID: 6017 RVA: 0x000A32E8 File Offset: 0x000A14E8
		public TextBox InputBox
		{
			get
			{
				return this.textBox_0;
			}
		}

		// Token: 0x170003DD RID: 989
		// (get) Token: 0x06001782 RID: 6018 RVA: 0x000A3300 File Offset: 0x000A1500
		// (set) Token: 0x06001783 RID: 6019 RVA: 0x000099CF File Offset: 0x00007BCF
		public Class280 HotKey
		{
			get
			{
				return this.class280_0;
			}
			set
			{
				this.class280_0 = value;
			}
		}

		// Token: 0x170003DE RID: 990
		// (get) Token: 0x06001784 RID: 6020 RVA: 0x000A3318 File Offset: 0x000A1518
		// (set) Token: 0x06001785 RID: 6021 RVA: 0x000099DA File Offset: 0x00007BDA
		public Class266 FnKey
		{
			get
			{
				return this.class266_0;
			}
			set
			{
				this.class266_0 = value;
			}
		}

		// Token: 0x170003DF RID: 991
		// (get) Token: 0x06001786 RID: 6022 RVA: 0x000A3330 File Offset: 0x000A1530
		// (set) Token: 0x06001787 RID: 6023 RVA: 0x000099E5 File Offset: 0x00007BE5
		public KeyModifiers Modifier
		{
			get
			{
				return this.keyModifiers_0;
			}
			set
			{
				this.keyModifiers_0 = value;
			}
		}

		// Token: 0x170003E0 RID: 992
		// (get) Token: 0x06001788 RID: 6024 RVA: 0x000A3348 File Offset: 0x000A1548
		// (set) Token: 0x06001789 RID: 6025 RVA: 0x000099F0 File Offset: 0x00007BF0
		public Keys Key
		{
			get
			{
				return this.keys_0;
			}
			set
			{
				this.keys_0 = value;
			}
		}

		// Token: 0x170003E1 RID: 993
		// (get) Token: 0x0600178A RID: 6026 RVA: 0x000A3360 File Offset: 0x000A1560
		public string FnKeyStr
		{
			get
			{
				string result;
				if (this.IsFnKey)
				{
					result = this.InputBox.Text.Trim();
				}
				else
				{
					result = string.Empty;
				}
				return result;
			}
		}

		// Token: 0x170003E2 RID: 994
		// (get) Token: 0x0600178B RID: 6027 RVA: 0x000A3394 File Offset: 0x000A1594
		public bool IsHotKey
		{
			get
			{
				return this.HotKey != null;
			}
		}

		// Token: 0x170003E3 RID: 995
		// (get) Token: 0x0600178C RID: 6028 RVA: 0x000A33B0 File Offset: 0x000A15B0
		public bool IsFnKey
		{
			get
			{
				return this.FnKey != null;
			}
		}

		// Token: 0x0600178D RID: 6029 RVA: 0x000A33CC File Offset: 0x000A15CC
		private void textBox_0_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.IsHotKey)
			{
				int num = 0;
				string text = Class521.smethod_0(1449);
				e.SuppressKeyPress = false;
				e.Handled = true;
				if (e.Modifiers != Keys.None)
				{
					Keys modifiers = e.Modifiers;
					if (modifiers <= (Keys.Shift | Keys.Control))
					{
						if (modifiers != Keys.Shift)
						{
							if (modifiers != Keys.Control)
							{
								if (modifiers == (Keys.Shift | Keys.Control))
								{
									text += Class521.smethod_0(59609);
									num = (int)e.Modifiers;
									this.keyModifiers_0 = KeyModifiers.Ctrl_Shift;
								}
							}
							else
							{
								text += Class521.smethod_0(59553);
								num = (int)e.Modifiers;
								this.keyModifiers_0 = KeyModifiers.Ctrl;
							}
						}
						else
						{
							text += Class521.smethod_0(59575);
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Shift;
						}
					}
					else if (modifiers <= (Keys.Shift | Keys.Alt))
					{
						if (modifiers != Keys.Alt)
						{
							if (modifiers == (Keys.Shift | Keys.Alt))
							{
								text += Class521.smethod_0(59630);
								num = (int)e.Modifiers;
								this.keyModifiers_0 = KeyModifiers.Alt_Shift;
							}
						}
						else
						{
							text += Class521.smethod_0(59566);
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Alt;
						}
					}
					else if (modifiers != (Keys.Control | Keys.Alt))
					{
						if (modifiers == (Keys.Shift | Keys.Control | Keys.Alt))
						{
							text += Class521.smethod_0(59651);
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Ctrl_Alt_Shift;
						}
					}
					else
					{
						text += Class521.smethod_0(59588);
						num = (int)e.Modifiers;
						this.keyModifiers_0 = KeyModifiers.Ctrl_Alt;
					}
					if (e.KeyCode != Keys.None && e.KeyCode != Keys.ControlKey && e.KeyCode != Keys.Menu && e.KeyCode != Keys.ShiftKey && e.KeyCode != Keys.ProcessKey)
					{
						text += this.method_1(e.KeyCode);
						num = (int)(num + e.KeyCode);
						this.keys_0 = e.KeyCode;
					}
					else
					{
						this.keyModifiers_0 = KeyModifiers.None;
						this.keys_0 = Keys.None;
					}
				}
				else
				{
					if (e.KeyCode != Keys.Delete && e.KeyCode != Keys.Back && e.KeyCode != Keys.None && e.KeyCode != Keys.Left && e.KeyCode != Keys.Right && e.KeyCode != Keys.Up && e.KeyCode != Keys.Down && e.KeyCode != Keys.Escape)
					{
						if (e.KeyCode != Keys.ProcessKey)
						{
							text = this.method_1(e.KeyCode);
							num = (int)e.KeyCode;
							this.keys_0 = e.KeyCode;
							goto IL_27D;
						}
					}
					text = string.Empty;
					num = -1;
					this.keys_0 = Keys.None;
					IL_27D:
					this.keyModifiers_0 = KeyModifiers.None;
				}
				if (num == 0)
				{
					num = -1;
				}
				TextBox textBox = (TextBox)sender;
				if (!string.IsNullOrEmpty(text))
				{
					textBox.Text = text;
				}
				textBox.Tag = num;
				textBox.SelectionStart = textBox.Text.Length;
			}
			else
			{
				e.SuppressKeyPress = false;
				e.Handled = false;
			}
		}

		// Token: 0x0600178E RID: 6030 RVA: 0x000099FB File Offset: 0x00007BFB
		private void textBox_0_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (this.IsHotKey)
			{
				e.Handled = true;
			}
			else if (!char.IsControl(e.KeyChar) && !char.IsLetterOrDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600178F RID: 6031 RVA: 0x00009A31 File Offset: 0x00007C31
		private void textBox_0_KeyUp(object sender, KeyEventArgs e)
		{
			if (this.IsHotKey)
			{
				this.method_0(sender);
			}
		}

		// Token: 0x06001790 RID: 6032 RVA: 0x00009A31 File Offset: 0x00007C31
		private void textBox_0_LostFocus(object sender, EventArgs e)
		{
			if (this.IsHotKey)
			{
				this.method_0(sender);
			}
		}

		// Token: 0x06001791 RID: 6033 RVA: 0x000A36A8 File Offset: 0x000A18A8
		private void method_0(object object_0)
		{
			TextBox textBox = (TextBox)object_0;
			if (textBox.Text.EndsWith(Class521.smethod_0(59548)) || string.IsNullOrEmpty(textBox.Text))
			{
				textBox.Text = Class521.smethod_0(1449);
				textBox.Tag = -1;
				textBox.SelectionStart = textBox.Text.Length;
				this.keyModifiers_0 = KeyModifiers.None;
				this.keys_0 = Keys.None;
			}
		}

		// Token: 0x06001792 RID: 6034 RVA: 0x000A3720 File Offset: 0x000A1920
		private string method_1(Keys keys_1)
		{
			string result;
			if (keys_1 >= Keys.D0 && keys_1 <= Keys.D9)
			{
				result = keys_1.ToString().Remove(0, 1);
			}
			else if (keys_1 >= Keys.NumPad0 && keys_1 <= Keys.NumPad9)
			{
				result = keys_1.ToString().Replace(Class521.smethod_0(59680), Class521.smethod_0(1449));
			}
			else
			{
				string text;
				if (keys_1 == Keys.OemMinus)
				{
					text = Class521.smethod_0(3210);
				}
				else if (keys_1 == Keys.Oemplus)
				{
					text = Class521.smethod_0(59685);
				}
				else if (keys_1 == Keys.OemOpenBrackets)
				{
					text = Class521.smethod_0(47686);
				}
				else if (keys_1 == Keys.OemCloseBrackets)
				{
					text = Class521.smethod_0(59690);
				}
				else if (keys_1 == Keys.OemPipe)
				{
					text = Class521.smethod_0(45611);
				}
				else if (keys_1 == Keys.OemSemicolon)
				{
					text = Class521.smethod_0(51510);
				}
				else if (keys_1 == Keys.OemQuotes)
				{
					text = Class521.smethod_0(59695);
				}
				else if (keys_1 == Keys.Oemcomma)
				{
					text = Class521.smethod_0(4736);
				}
				else if (keys_1 == Keys.OemPeriod)
				{
					text = Class521.smethod_0(1733);
				}
				else if (keys_1 == Keys.OemQuestion)
				{
					text = Class521.smethod_0(24570);
				}
				else if (keys_1 == Keys.Oemtilde)
				{
					text = Class521.smethod_0(59700);
				}
				else if (keys_1 == Keys.Next)
				{
					text = Class521.smethod_0(59705);
				}
				else if (keys_1 == Keys.None)
				{
					text = Class521.smethod_0(1449);
				}
				else
				{
					text = keys_1.ToString();
				}
				result = text;
			}
			return result;
		}

		// Token: 0x06001793 RID: 6035 RVA: 0x00009A44 File Offset: 0x00007C44
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001794 RID: 6036 RVA: 0x000A38B8 File Offset: 0x000A1AB8
		private void method_2()
		{
			this.label_0 = new Label();
			this.textBox_0 = new TextBox();
			base.SuspendLayout();
			this.label_0.Location = new Point(7, 2);
			this.label_0.Name = Class521.smethod_0(59714);
			this.label_0.Size = new Size(181, 23);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(5871);
			this.label_0.TextAlign = ContentAlignment.MiddleLeft;
			this.textBox_0.Location = new Point(194, 1);
			this.textBox_0.Name = Class521.smethod_0(59731);
			this.textBox_0.Size = new Size(221, 25);
			this.textBox_0.TabIndex = 1;
			this.textBox_0.TextAlign = HorizontalAlignment.Center;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.label_0);
			base.Name = Class521.smethod_0(59744);
			base.Size = new Size(418, 27);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000BEA RID: 3050
		private Class280 class280_0;

		// Token: 0x04000BEB RID: 3051
		private Class266 class266_0;

		// Token: 0x04000BEC RID: 3052
		private KeyModifiers keyModifiers_0;

		// Token: 0x04000BED RID: 3053
		private Keys keys_0;

		// Token: 0x04000BEE RID: 3054
		private IContainer icontainer_0;

		// Token: 0x04000BEF RID: 3055
		private Label label_0;

		// Token: 0x04000BF0 RID: 3056
		private TextBox textBox_0;
	}
}
