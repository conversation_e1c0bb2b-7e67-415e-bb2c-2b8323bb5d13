﻿using System;
using System.Linq;
using System.Runtime.CompilerServices;
using ns12;
using ns18;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns26
{
	// Token: 0x0200030C RID: 780
	internal class Class412 : Class411
	{
		// Token: 0x170005CB RID: 1483
		// (get) Token: 0x060021B0 RID: 8624 RVA: 0x000EFCC0 File Offset: 0x000EDEC0
		// (set) Token: 0x060021B1 RID: 8625 RVA: 0x0000D8E8 File Offset: 0x0000BAE8
		public override HToken Token
		{
			get
			{
				return this.htoken_0;
			}
			protected set
			{
				this.htoken_0 = value;
			}
		}

		// Token: 0x060021B2 RID: 8626 RVA: 0x0000D8F3 File Offset: 0x0000BAF3
		public Class412(HToken htoken_1)
		{
			this.Token = htoken_1;
		}

		// Token: 0x170005CC RID: 1484
		// (get) Token: 0x060021B3 RID: 8627 RVA: 0x0000D904 File Offset: 0x0000BB04
		// (set) Token: 0x060021B4 RID: 8628 RVA: 0x0000D904 File Offset: 0x0000BB04
		public override Class411 Left
		{
			get
			{
				throw new Exception(Class521.smethod_0(101329));
			}
			protected set
			{
				throw new Exception(Class521.smethod_0(101329));
			}
		}

		// Token: 0x170005CD RID: 1485
		// (get) Token: 0x060021B5 RID: 8629 RVA: 0x0000D915 File Offset: 0x0000BB15
		// (set) Token: 0x060021B6 RID: 8630 RVA: 0x0000D915 File Offset: 0x0000BB15
		public override Class411 Right
		{
			get
			{
				throw new Exception(Class521.smethod_0(101346));
			}
			protected set
			{
				throw new Exception(Class521.smethod_0(101346));
			}
		}

		// Token: 0x060021B7 RID: 8631 RVA: 0x000EFCD8 File Offset: 0x000EDED8
		public override string vmethod_0()
		{
			return this.Token.Symbol.Name;
		}

		// Token: 0x060021B8 RID: 8632 RVA: 0x000EFCFC File Offset: 0x000EDEFC
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object result;
			if (this.Token.Symbol.HSymbolType == Enum26.const_5)
			{
				result = double.Parse(this.Token.Symbol.Name);
			}
			else if (this.Token.Symbol.HSymbolType == Enum26.const_38)
			{
				DataArray dataArray = null;
				if (parserEnvironment_0.method_0(this.Token.Symbol.Name, out dataArray))
				{
					result = dataArray;
				}
				else
				{
					result = this.Token.Symbol.Name;
				}
			}
			else
			{
				if (this.Token.Symbol.HSymbolType != Enum26.const_3 && this.Token.Symbol.HSymbolType != Enum26.const_1)
				{
					if (this.Token.Symbol.HSymbolType != Enum26.const_34)
					{
						if (this.Token.Symbol.HSymbolType == Enum26.const_4)
						{
							Class412.Class429 @class = new Class412.Class429();
							@class.string_0 = this.Token.Symbol.Name;
							DataArray dataArray2 = parserEnvironment_0.DataArrays.SingleOrDefault(new Func<DataArray, bool>(@class.method_0));
							if (dataArray2 != null)
							{
								return dataArray2.Clone();
							}
							NameDoubleValue nameDoubleValue = parserEnvironment_0.list_0.SingleOrDefault(new Func<NameDoubleValue, bool>(@class.method_1));
							if (nameDoubleValue != null)
							{
								return nameDoubleValue.Value;
							}
						}
						else
						{
							if (this.Token.Symbol.HSymbolType == Enum26.const_2)
							{
								return parserEnvironment_0.UserDefineParams.Single(new Func<UserDefineParam, bool>(this.method_0)).Value;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_22)
							{
								return null;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_10)
							{
								return null;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_11)
							{
								return null;
							}
						}
						throw new Exception(this.Token.method_0(Class521.smethod_0(101363)));
					}
				}
				result = this.Token.Symbol.Name;
			}
			return result;
		}

		// Token: 0x060021B9 RID: 8633 RVA: 0x000EFF0C File Offset: 0x000EE10C
		[CompilerGenerated]
		private bool method_0(UserDefineParam userDefineParam_0)
		{
			return userDefineParam_0.Name == this.Token.Symbol.Name;
		}

		// Token: 0x04001062 RID: 4194
		private HToken htoken_0;

		// Token: 0x0200030D RID: 781
		[CompilerGenerated]
		private sealed class Class429
		{
			// Token: 0x060021BB RID: 8635 RVA: 0x000EFF38 File Offset: 0x000EE138
			internal bool method_0(DataArray dataArray_0)
			{
				return dataArray_0.Name == this.string_0;
			}

			// Token: 0x060021BC RID: 8636 RVA: 0x000EFF5C File Offset: 0x000EE15C
			internal bool method_1(NameDoubleValue nameDoubleValue_0)
			{
				return nameDoubleValue_0.Name == this.string_0;
			}

			// Token: 0x04001063 RID: 4195
			public string string_0;
		}
	}
}
