﻿using System;
using System.IO;
using System.Security;
using System.Windows.Forms;
using ns11;
using ns14;
using ns18;
using ns22;
using ns28;
using ns29;
using ns9;

namespace ns21
{
	// Token: 0x0200041C RID: 1052
	internal sealed class Class545 : Class544
	{
		// Token: 0x06002876 RID: 10358 RVA: 0x001101E0 File Offset: 0x0010E3E0
		protected override Guid vmethod_3()
		{
			Guid result;
			try
			{
				string text = Class551.smethod_0(Class521.smethod_0(122333));
				if (text.Length == 0)
				{
					Guid guid = Guid.NewGuid();
					Class551.smethod_1(Class521.smethod_0(122333), guid.ToString(Class521.smethod_0(15541)));
					if (Class551.smethod_0(Class521.smethod_0(122333)).Length > 0)
					{
						result = guid;
					}
					else
					{
						result = Guid.Empty;
					}
				}
				else
				{
					result = new Guid(text);
				}
			}
			catch
			{
				result = Guid.Empty;
			}
			return result;
		}

		// Token: 0x06002877 RID: 10359 RVA: 0x0000FE09 File Offset: 0x0000E009
		protected override void vmethod_2(EventArgs36 eventArgs36_0)
		{
			new Form28(eventArgs36_0).ShowDialog();
		}

		// Token: 0x06002878 RID: 10360 RVA: 0x00110274 File Offset: 0x0010E474
		protected override void vmethod_0(EventArgs35 eventArgs35_0)
		{
			string text = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + Class521.smethod_0(122350);
			if (new FileInfo(text).Exists)
			{
				eventArgs35_0.method_8(Class521.smethod_0(122383), text);
			}
			new Form27(this, eventArgs35_0).ShowDialog();
		}

		// Token: 0x06002879 RID: 10361 RVA: 0x0000FE17 File Offset: 0x0000E017
		protected override void vmethod_1(EventArgs34 eventArgs34_0)
		{
			MessageBox.Show(eventArgs34_0.FatalException.ToString(), string.Format(Class521.smethod_0(122400), Class521.smethod_0(121215)), MessageBoxButtons.OK, MessageBoxIcon.Hand);
		}

		// Token: 0x0600287A RID: 10362 RVA: 0x001102C4 File Offset: 0x0010E4C4
		public static bool smethod_5()
		{
			bool result;
			try
			{
				Class544.smethod_0(new Class545());
				result = true;
			}
			catch (SecurityException)
			{
				try
				{
					Application.EnableVisualStyles();
					new Form28(new EventArgs36(string.Format(Class521.smethod_0(122421), Class521.smethod_0(121215)), false))
					{
						ShowInTaskbar = true
					}.ShowDialog();
				}
				catch (Exception ex)
				{
					MessageBox.Show(ex.ToString(), string.Format(Class521.smethod_0(122400), Class521.smethod_0(121215)), MessageBoxButtons.OK, MessageBoxIcon.Hand);
				}
				result = false;
			}
			return result;
		}
	}
}
