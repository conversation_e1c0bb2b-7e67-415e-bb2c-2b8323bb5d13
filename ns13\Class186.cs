﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns12;
using ns18;
using ns21;
using ns26;
using TEx;

namespace ns13
{
	// Token: 0x02000131 RID: 305
	internal sealed class FunctionKeyManager
	{
		// Token: 0x06000C90 RID: 3216 RVA: 0x0004AB84 File Offset: 0x00048D84
		public static List<FunctionKeyItem> GetDefaultFunctionKeys()
		{
			List<FunctionKeyItem> list = new List<FunctionKeyItem>();
			FunctionKeyItem item = new FunctionKeyItem(250, Class521.smethod_0(4933), Class521.smethod_0(11989), Class521.smethod_0(11994));
			FunctionKeyItem item2 = new FunctionKeyItem(251, Class521.smethod_0(2841), Class521.smethod_0(12011), Class521.smethod_0(12016));
			FunctionKeyItem item3 = new FunctionKeyItem(252, Class521.smethod_0(12033), Class521.smethod_0(12038), Class521.smethod_0(12043));
			FunctionKeyItem item4 = new FunctionKeyItem(253, Class521.smethod_0(12060), Class521.smethod_0(12065), Class521.smethod_0(12070));
			FunctionKeyItem item5 = new FunctionKeyItem(254, Class521.smethod_0(12091), Class521.smethod_0(12096), Class521.smethod_0(12101));
			FunctionKeyItem item6 = new FunctionKeyItem(255, Class521.smethod_0(12122), Class521.smethod_0(12127), Class521.smethod_0(12132));
			FunctionKeyItem item7 = new FunctionKeyItem(256, Class521.smethod_0(12145), Class521.smethod_0(12150), Class521.smethod_0(12155));
			FunctionKeyItem item8 = new FunctionKeyItem(257, Class521.smethod_0(12172), Class521.smethod_0(12177), Class521.smethod_0(12182));
			FunctionKeyItem item9 = new FunctionKeyItem(258, Class521.smethod_0(12199), Class521.smethod_0(12204), Class521.smethod_0(12209));
			FunctionKeyItem item10 = new FunctionKeyItem(259, Class521.smethod_0(12218), Class521.smethod_0(12223), Class521.smethod_0(12228));
			FunctionKeyItem item11 = new FunctionKeyItem(260, Class521.smethod_0(12237), Class521.smethod_0(12242), Class521.smethod_0(12247));
			FunctionKeyItem item12 = new FunctionKeyItem(261, Class521.smethod_0(12256), Class521.smethod_0(12261), Class521.smethod_0(12266));
			FunctionKeyItem item13 = new FunctionKeyItem(270, Class521.smethod_0(12283), Class521.smethod_0(12288), Class521.smethod_0(12297));
			FunctionKeyItem item14 = new FunctionKeyItem(271, Class521.smethod_0(12314), Class521.smethod_0(12319), Class521.smethod_0(12332));
			FunctionKeyItem item15 = new FunctionKeyItem(272, Class521.smethod_0(12349), Class521.smethod_0(12354), Class521.smethod_0(12363));
			list.Add(item);
			list.Add(item2);
			list.Add(item3);
			list.Add(item12);
			list.Add(item4);
			list.Add(item5);
			list.Add(item6);
			list.Add(item7);
			list.Add(item8);
			list.Add(item9);
			list.Add(item10);
			list.Add(item11);
			list.Add(item13);
			list.Add(item14);
			list.Add(item15);
			return list;
		}

		// Token: 0x06000C91 RID: 3217 RVA: 0x0004AE84 File Offset: 0x00049084
		public static List<FunctionKeyItem> GetUserFunctionKeys()
		{
			List<FunctionKeyItem> list = FunctionKeyManager.LoadFromXml(Base.UI.smethod_53());
			if (list == null)
			{
				list = FunctionKeyManager.GetDefaultFunctionKeys();
			}
			else
			{
				using (List<FunctionKeyItem>.Enumerator enumerator = FunctionKeyManager.DefaultUsrFnKeyList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						FunctionKeyManager.KeyItemComparer @class = new FunctionKeyManager.KeyItemComparer();
						@class.targetItem = enumerator.Current;
						if (!list.Exists(new Predicate<FunctionKeyItem>(@class.IsMatch)))
						{
							list.Add(@class.targetItem);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000C92 RID: 3218 RVA: 0x0004AF18 File Offset: 0x00049118
		public static List<FunctionKeyItem> LoadFromXml(XDocument xmlDocument)
		{
			List<FunctionKeyItem> list = null;
			if (xmlDocument != null)
			{
				try
				{
					list = new List<FunctionKeyItem>();
					foreach (XElement xelement in xmlDocument.Element(Class521.smethod_0(12376)).Element(Class521.smethod_0(12393)).Elements(Class521.smethod_0(12402)))
					{
						FunctionKeyItem item = new FunctionKeyItem();
						item.Id = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(12411)).Value);
						item.KeyStr = xelement.Attribute(Class521.smethod_0(12416)).Value;
						FunctionKeyItem defaultItem = FunctionKeyManager.FindDefaultKeyById(item.Id);
						item.EnName = defaultItem.EnName;
						item.CnName = defaultItem.CnName;
						list.Add(item);
					}
				}
				catch (Exception exception_)
				{
					ExceptionHandler.HandleException(exception_);
				}
			}
			return list;
		}

		// Token: 0x06000C93 RID: 3219 RVA: 0x0004B040 File Offset: 0x00049240
		private static Class266 smethod_3(int int_0)
		{
			Class186.Class188 @class = new Class186.Class188();
			@class.int_0 = int_0;
			return Class186.DefaultUsrFnKeyList.Find(new Predicate<Class266>(@class.method_0));
		}

		// Token: 0x06000C94 RID: 3220 RVA: 0x0004B074 File Offset: 0x00049274
		public static FunctionKeyItem GetFunctionKeyByEnum(FunctionKeyId functionKeyId)
		{
			FunctionKeyManager.FunctionKeyFinder @class = new FunctionKeyManager.FunctionKeyFinder();
			@class.targetKeyId = functionKeyId;
			return FunctionKeyManager.UsrFnKeyList.Find(new Predicate<FunctionKeyItem>(@class.IsMatch));
		}

		// Token: 0x06000C95 RID: 3221 RVA: 0x0004B0A8 File Offset: 0x000492A8
		public static string GetKeyStringByEnum(FunctionKeyId functionKeyId)
		{
			FunctionKeyItem item = FunctionKeyManager.GetFunctionKeyByEnum(functionKeyId);
			string result;
			if (item != null)
			{
				result = item.KeyStr;
			}
			else
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x17000201 RID: 513
		// (get) Token: 0x06000C96 RID: 3222 RVA: 0x0004B0D4 File Offset: 0x000492D4
		public static List<Class266> DefaultUsrFnKeyList
		{
			get
			{
				if (Class186.list_0 == null || Class186.list_0.Count == 0)
				{
					Class186.list_0 = Class186.smethod_0();
				}
				return Class186.list_0;
			}
		}

		// Token: 0x17000202 RID: 514
		// (get) Token: 0x06000C97 RID: 3223 RVA: 0x0004B108 File Offset: 0x00049308
		// (set) Token: 0x06000C98 RID: 3224 RVA: 0x00005B12 File Offset: 0x00003D12
		public static List<Class266> UsrFnKeyList
		{
			get
			{
				if (Class186.list_1 == null || Class186.list_1.Count == 0)
				{
					Class186.list_1 = Class186.smethod_1();
				}
				return Class186.list_1;
			}
			set
			{
				Class186.list_1 = value;
			}
		}

		// Token: 0x0400052E RID: 1326
		private static List<Class266> list_0;

		// Token: 0x0400052F RID: 1327
		private static List<Class266> list_1;

		// Token: 0x02000132 RID: 306
		[CompilerGenerated]
		private sealed class KeyItemComparer
		{
			// Token: 0x06000C9B RID: 3227 RVA: 0x0004B13C File Offset: 0x0004933C
			internal bool IsMatch(FunctionKeyItem item)
			{
				return item.Id == this.targetItem.Id;
			}

			// Token: 0x04000530 RID: 1328
			public FunctionKeyItem targetItem;
		}

		// Token: 0x02000133 RID: 307
		[CompilerGenerated]
		private sealed class Class188
		{
			// Token: 0x06000C9D RID: 3229 RVA: 0x0004B160 File Offset: 0x00049360
			internal bool method_0(Class266 class266_0)
			{
				return class266_0.Id == this.int_0;
			}

			// Token: 0x04000531 RID: 1329
			public int int_0;
		}

		// Token: 0x02000134 RID: 308
		[CompilerGenerated]
		private sealed class FunctionKeyFinder
		{
			// Token: 0x06000C9F RID: 3231 RVA: 0x0004B180 File Offset: 0x00049380
			internal bool IsMatch(FunctionKeyItem item)
			{
				return item.Id == (int)this.targetKeyId;
			}

			// Token: 0x04000532 RID: 1330
			public FunctionKeyId targetKeyId;
		}
	}
}
