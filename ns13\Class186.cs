﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns12;
using ns18;
using ns21;
using ns26;
using TEx;

namespace ns13
{
	// Token: 0x02000131 RID: 305
	internal sealed class Class186
	{
		// Token: 0x06000C90 RID: 3216 RVA: 0x0004AB84 File Offset: 0x00048D84
		public static List<Class266> smethod_0()
		{
			List<Class266> list = new List<Class266>();
			Class266 item = new Class266(250, Class521.smethod_0(4933), Class521.smethod_0(11989), Class521.smethod_0(11994));
			Class266 item2 = new Class266(251, Class521.smethod_0(2841), Class521.smethod_0(12011), Class521.smethod_0(12016));
			Class266 item3 = new Class266(252, Class521.smethod_0(12033), Class521.smethod_0(12038), Class521.smethod_0(12043));
			Class266 item4 = new Class266(253, Class521.smethod_0(12060), Class521.smethod_0(12065), Class521.smethod_0(12070));
			Class266 item5 = new Class266(254, Class521.smethod_0(12091), Class521.smethod_0(12096), Class521.smethod_0(12101));
			Class266 item6 = new Class266(255, Class521.smethod_0(12122), Class521.smethod_0(12127), Class521.smethod_0(12132));
			Class266 item7 = new Class266(256, Class521.smethod_0(12145), Class521.smethod_0(12150), Class521.smethod_0(12155));
			Class266 item8 = new Class266(257, Class521.smethod_0(12172), Class521.smethod_0(12177), Class521.smethod_0(12182));
			Class266 item9 = new Class266(258, Class521.smethod_0(12199), Class521.smethod_0(12204), Class521.smethod_0(12209));
			Class266 item10 = new Class266(259, Class521.smethod_0(12218), Class521.smethod_0(12223), Class521.smethod_0(12228));
			Class266 item11 = new Class266(260, Class521.smethod_0(12237), Class521.smethod_0(12242), Class521.smethod_0(12247));
			Class266 item12 = new Class266(261, Class521.smethod_0(12256), Class521.smethod_0(12261), Class521.smethod_0(12266));
			Class266 item13 = new Class266(270, Class521.smethod_0(12283), Class521.smethod_0(12288), Class521.smethod_0(12297));
			Class266 item14 = new Class266(271, Class521.smethod_0(12314), Class521.smethod_0(12319), Class521.smethod_0(12332));
			Class266 item15 = new Class266(272, Class521.smethod_0(12349), Class521.smethod_0(12354), Class521.smethod_0(12363));
			list.Add(item);
			list.Add(item2);
			list.Add(item3);
			list.Add(item12);
			list.Add(item4);
			list.Add(item5);
			list.Add(item6);
			list.Add(item7);
			list.Add(item8);
			list.Add(item9);
			list.Add(item10);
			list.Add(item11);
			list.Add(item13);
			list.Add(item14);
			list.Add(item15);
			return list;
		}

		// Token: 0x06000C91 RID: 3217 RVA: 0x0004AE84 File Offset: 0x00049084
		public static List<Class266> smethod_1()
		{
			List<Class266> list = Class186.smethod_2(Base.UI.smethod_53());
			if (list == null)
			{
				list = Class186.smethod_0();
			}
			else
			{
				using (List<Class266>.Enumerator enumerator = Class186.DefaultUsrFnKeyList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Class186.Class187 @class = new Class186.Class187();
						@class.class266_0 = enumerator.Current;
						if (!list.Exists(new Predicate<Class266>(@class.method_0)))
						{
							list.Add(@class.class266_0);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000C92 RID: 3218 RVA: 0x0004AF18 File Offset: 0x00049118
		public static List<Class266> smethod_2(XDocument xdocument_0)
		{
			List<Class266> list = null;
			if (xdocument_0 != null)
			{
				try
				{
					list = new List<Class266>();
					foreach (XElement xelement in xdocument_0.Element(Class521.smethod_0(12376)).Element(Class521.smethod_0(12393)).Elements(Class521.smethod_0(12402)))
					{
						Class266 @class = new Class266();
						@class.Id = Convert.ToInt32(xelement.Attribute(Class521.smethod_0(12411)).Value);
						@class.KeyStr = xelement.Attribute(Class521.smethod_0(12416)).Value;
						Class266 class2 = Class186.smethod_3(@class.Id);
						@class.EnName = class2.EnName;
						@class.CnName = class2.CnName;
						list.Add(@class);
					}
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
			return list;
		}

		// Token: 0x06000C93 RID: 3219 RVA: 0x0004B040 File Offset: 0x00049240
		private static Class266 smethod_3(int int_0)
		{
			Class186.Class188 @class = new Class186.Class188();
			@class.int_0 = int_0;
			return Class186.DefaultUsrFnKeyList.Find(new Predicate<Class266>(@class.method_0));
		}

		// Token: 0x06000C94 RID: 3220 RVA: 0x0004B074 File Offset: 0x00049274
		public static Class266 smethod_4(Enum3 enum3_0)
		{
			Class186.Class189 @class = new Class186.Class189();
			@class.enum3_0 = enum3_0;
			return Class186.UsrFnKeyList.Find(new Predicate<Class266>(@class.method_0));
		}

		// Token: 0x06000C95 RID: 3221 RVA: 0x0004B0A8 File Offset: 0x000492A8
		public static string smethod_5(Enum3 enum3_0)
		{
			Class266 @class = Class186.smethod_4(enum3_0);
			string result;
			if (@class != null)
			{
				result = @class.KeyStr;
			}
			else
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x17000201 RID: 513
		// (get) Token: 0x06000C96 RID: 3222 RVA: 0x0004B0D4 File Offset: 0x000492D4
		public static List<Class266> DefaultUsrFnKeyList
		{
			get
			{
				if (Class186.list_0 == null || Class186.list_0.Count == 0)
				{
					Class186.list_0 = Class186.smethod_0();
				}
				return Class186.list_0;
			}
		}

		// Token: 0x17000202 RID: 514
		// (get) Token: 0x06000C97 RID: 3223 RVA: 0x0004B108 File Offset: 0x00049308
		// (set) Token: 0x06000C98 RID: 3224 RVA: 0x00005B12 File Offset: 0x00003D12
		public static List<Class266> UsrFnKeyList
		{
			get
			{
				if (Class186.list_1 == null || Class186.list_1.Count == 0)
				{
					Class186.list_1 = Class186.smethod_1();
				}
				return Class186.list_1;
			}
			set
			{
				Class186.list_1 = value;
			}
		}

		// Token: 0x0400052E RID: 1326
		private static List<Class266> list_0;

		// Token: 0x0400052F RID: 1327
		private static List<Class266> list_1;

		// Token: 0x02000132 RID: 306
		[CompilerGenerated]
		private sealed class Class187
		{
			// Token: 0x06000C9B RID: 3227 RVA: 0x0004B13C File Offset: 0x0004933C
			internal bool method_0(Class266 class266_1)
			{
				return class266_1.Id == this.class266_0.Id;
			}

			// Token: 0x04000530 RID: 1328
			public Class266 class266_0;
		}

		// Token: 0x02000133 RID: 307
		[CompilerGenerated]
		private sealed class Class188
		{
			// Token: 0x06000C9D RID: 3229 RVA: 0x0004B160 File Offset: 0x00049360
			internal bool method_0(Class266 class266_0)
			{
				return class266_0.Id == this.int_0;
			}

			// Token: 0x04000531 RID: 1329
			public int int_0;
		}

		// Token: 0x02000134 RID: 308
		[CompilerGenerated]
		private sealed class Class189
		{
			// Token: 0x06000C9F RID: 3231 RVA: 0x0004B180 File Offset: 0x00049380
			internal bool method_0(Class266 class266_0)
			{
				return class266_0.Id == (int)this.enum3_0;
			}

			// Token: 0x04000532 RID: 1330
			public Enum3 enum3_0;
		}
	}
}
