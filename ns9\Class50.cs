﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns26;

namespace ns9
{
	// Token: 0x02000092 RID: 146
	[DesignerCategory("Code")]
	internal abstract class Class50 : PictureBox
	{
		// Token: 0x060004C3 RID: 1219 RVA: 0x000269C0 File Offset: 0x00024BC0
		public Class50()
		{
			this.DoubleBuffered = true;
			this.IsTransparent = true;
			base.Click += this.Class50_Click;
			base.MouseEnter += this.Class50_MouseEnter;
			base.MouseLeave += this.Class50_MouseLeave;
			base.MouseDown += new MouseEventHandler(this.Class50_MouseDown);
			base.MouseUp += new MouseEventHandler(this.Class50_MouseUp);
			base.SizeMode = PictureBoxSizeMode.StretchImage;
			base.TabStop = false;
		}

		// Token: 0x060004C4 RID: 1220 RVA: 0x00026A50 File Offset: 0x00024C50
		public Class50(Panel panel_1, int int_4, int int_5, bool bool_3) : this()
		{
			this.panel_0 = panel_1;
			this.int_0 = int_5;
			this.int_1 = int_4;
			this.bool_0 = bool_3;
			this.int_2 = int_4;
			this.int_3 = int_5;
			base.SuspendLayout();
			this.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
			base.Size = new Size(14, 14);
			base.Location = new Point(this.panel_0.Width - this.int_1, this.int_0);
			this.panel_0.Controls.Add(this);
			base.BringToFront();
			base.ResumeLayout(false);
		}

		// Token: 0x060004C5 RID: 1221
		protected abstract void Class50_Click(object sender, EventArgs e);

		// Token: 0x060004C6 RID: 1222 RVA: 0x000041BB File Offset: 0x000023BB
		protected virtual void Class50_MouseEnter(object sender, EventArgs e)
		{
			this.bool_1 = true;
			if (!base.Visible)
			{
				base.Visible = true;
				base.BringToFront();
			}
			if (this.HoverImage != null)
			{
				base.Image = this.HoverImage;
			}
		}

		// Token: 0x060004C7 RID: 1223 RVA: 0x000041EF File Offset: 0x000023EF
		protected virtual void Class50_MouseLeave(object sender, EventArgs e)
		{
			this.bool_1 = false;
			if (this.HoverImage != null)
			{
				base.Image = this.NormalImage;
			}
		}

		// Token: 0x060004C8 RID: 1224 RVA: 0x0000420E File Offset: 0x0000240E
		protected virtual void Class50_MouseDown(object sender, EventArgs e)
		{
			this.bool_2 = true;
		}

		// Token: 0x060004C9 RID: 1225 RVA: 0x00004219 File Offset: 0x00002419
		protected virtual void Class50_MouseUp(object sender, EventArgs e)
		{
			this.bool_2 = false;
		}

		// Token: 0x060004CA RID: 1226 RVA: 0x00026AF0 File Offset: 0x00024CF0
		protected void method_0(Bitmap bitmap_0)
		{
			try
			{
				base.Image = bitmap_0;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x170000F8 RID: 248
		// (get) Token: 0x060004CB RID: 1227 RVA: 0x00026B20 File Offset: 0x00024D20
		// (set) Token: 0x060004CC RID: 1228 RVA: 0x00004224 File Offset: 0x00002424
		public Image NormalImage { get; set; }

		// Token: 0x170000F9 RID: 249
		// (get) Token: 0x060004CD RID: 1229 RVA: 0x00026B38 File Offset: 0x00024D38
		// (set) Token: 0x060004CE RID: 1230 RVA: 0x0000422F File Offset: 0x0000242F
		public Image HoverImage { get; set; }

		// Token: 0x170000FA RID: 250
		// (get) Token: 0x060004CF RID: 1231 RVA: 0x00026B50 File Offset: 0x00024D50
		public Panel Panel
		{
			get
			{
				return this.panel_0;
			}
		}

		// Token: 0x170000FB RID: 251
		// (get) Token: 0x060004D0 RID: 1232 RVA: 0x00026B68 File Offset: 0x00024D68
		// (set) Token: 0x060004D1 RID: 1233 RVA: 0x0000423A File Offset: 0x0000243A
		public bool IsTransparent
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170000FC RID: 252
		// (get) Token: 0x060004D2 RID: 1234 RVA: 0x00026B80 File Offset: 0x00024D80
		public bool IsMouseEnter
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x170000FD RID: 253
		// (get) Token: 0x060004D3 RID: 1235 RVA: 0x00026B98 File Offset: 0x00024D98
		public bool IsMouseDown
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x170000FE RID: 254
		// (get) Token: 0x060004D4 RID: 1236 RVA: 0x00026BB0 File Offset: 0x00024DB0
		public int OriginRightMargin
		{
			get
			{
				return this.int_2;
			}
		}

		// Token: 0x170000FF RID: 255
		// (get) Token: 0x060004D5 RID: 1237 RVA: 0x00026BC8 File Offset: 0x00024DC8
		public int OriginTopMargin
		{
			get
			{
				return this.int_3;
			}
		}

		// Token: 0x060004D6 RID: 1238 RVA: 0x00026BE0 File Offset: 0x00024DE0
		protected override void OnPaintBackground(PaintEventArgs e)
		{
			base.OnPaintBackground(e);
			try
			{
				if (this.bool_0)
				{
					Graphics graphics = e.Graphics;
					if (base.Parent != null)
					{
						int childIndex = base.Parent.Controls.GetChildIndex(this);
						for (int i = base.Parent.Controls.Count - 1; i > childIndex; i--)
						{
							Control control = base.Parent.Controls[i];
							if (control.Bounds.IntersectsWith(base.Bounds) && control.Visible)
							{
								Bitmap bitmap = new Bitmap(control.Width, control.Height, graphics);
								control.DrawToBitmap(bitmap, control.ClientRectangle);
								graphics.TranslateTransform((float)(control.Left - base.Left), (float)(control.Top - base.Top));
								graphics.DrawImageUnscaled(bitmap, Point.Empty);
								graphics.TranslateTransform((float)(base.Left - control.Left), (float)(base.Top - control.Top));
								bitmap.Dispose();
							}
						}
					}
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x040001EC RID: 492
		private Panel panel_0;

		// Token: 0x040001ED RID: 493
		private int int_0;

		// Token: 0x040001EE RID: 494
		private int int_1;

		// Token: 0x040001EF RID: 495
		private bool bool_0;

		// Token: 0x040001F0 RID: 496
		private bool bool_1;

		// Token: 0x040001F1 RID: 497
		private bool bool_2;

		// Token: 0x040001F2 RID: 498
		private int int_2;

		// Token: 0x040001F3 RID: 499
		private int int_3;

		// Token: 0x040001F4 RID: 500
		[CompilerGenerated]
		private Image image_0;

		// Token: 0x040001F5 RID: 501
		[CompilerGenerated]
		private Image image_1;
	}
}
