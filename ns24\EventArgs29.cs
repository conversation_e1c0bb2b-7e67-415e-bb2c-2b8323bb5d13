﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx.SIndicator;

namespace ns24
{
	// Token: 0x020002DA RID: 730
	internal sealed class EventArgs29 : EventArgs
	{
		// Token: 0x170005B3 RID: 1459
		// (get) Token: 0x06002090 RID: 8336 RVA: 0x000E8550 File Offset: 0x000E6750
		// (set) Token: 0x06002091 RID: 8337 RVA: 0x0000D354 File Offset: 0x0000B554
		public List<IndEx> IndExList { get; set; }

		// Token: 0x06002092 RID: 8338 RVA: 0x0000D35F File Offset: 0x0000B55F
		public EventArgs29(List<IndEx> list_1)
		{
			this.IndExList = list_1;
		}

		// Token: 0x04001006 RID: 4102
		[CompilerGenerated]
		private List<IndEx> list_0;
	}
}
