﻿using System;
using TEx.SIndicator;

namespace ns12
{
	// Token: 0x02000305 RID: 773
	internal abstract class Class411
	{
		// Token: 0x0600215D RID: 8541
		public abstract string vmethod_0();

		// Token: 0x0600215E RID: 8542
		public abstract object vmethod_1(ParserEnvironment parserEnvironment_0);

		// Token: 0x170005C5 RID: 1477
		// (get) Token: 0x0600215F RID: 8543
		// (set) Token: 0x06002160 RID: 8544
		public abstract HToken Token { get; protected set; }

		// Token: 0x170005C6 RID: 1478
		// (get) Token: 0x06002161 RID: 8545
		// (set) Token: 0x06002162 RID: 8546
		public abstract Class411 Left { get; protected set; }

		// Token: 0x170005C7 RID: 1479
		// (get) Token: 0x06002163 RID: 8547
		// (set) Token: 0x06002164 RID: 8548
		public abstract Class411 Right { get; protected set; }
	}
}
