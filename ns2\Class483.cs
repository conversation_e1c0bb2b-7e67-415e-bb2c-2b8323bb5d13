﻿using System;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Win32;
using ns18;

namespace ns2
{
	// Token: 0x0200037C RID: 892
	internal sealed class Class483
	{
		// Token: 0x060024EB RID: 9451 RVA: 0x000FFE80 File Offset: 0x000FE080
		private static int smethod_0()
		{
			int result;
			try
			{
				result = (int)Convert.ToDecimal(Registry.LocalMachine.OpenSubKey(Class521.smethod_0(109592), true).GetValue(Class521.smethod_0(109641)).ToString().Trim().Split(new char[]
				{
					'.'
				}).First<string>());
			}
			catch (Exception)
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x060024EC RID: 9452 RVA: 0x000FFEF8 File Offset: 0x000FE0F8
		private static int smethod_1()
		{
			int result;
			try
			{
				result = new WebBrowser().Version.Major;
			}
			catch (Exception)
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x060024ED RID: 9453 RVA: 0x000FFF34 File Offset: 0x000FE134
		public static int smethod_2()
		{
			return Class483.smethod_1();
		}
	}
}
