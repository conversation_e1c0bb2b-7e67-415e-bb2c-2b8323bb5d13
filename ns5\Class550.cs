﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace ns5
{
	// Token: 0x02000416 RID: 1046
	[CompilerGenerated]
	internal sealed class Class550
	{
		// Token: 0x04001425 RID: 5157 RVA: 0x00002960 File Offset: 0x00000B60
		internal static readonly Class550.Struct33 struct33_0;

		// Token: 0x04001426 RID: 5158 RVA: 0x000029E0 File Offset: 0x00000BE0
		internal static readonly Class550.Struct32 struct32_0;

		// Token: 0x02000417 RID: 1047
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 116)]
		private struct Struct32
		{
		}

		// Token: 0x02000418 RID: 1048
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 124)]
		private struct Struct33
		{
		}
	}
}
