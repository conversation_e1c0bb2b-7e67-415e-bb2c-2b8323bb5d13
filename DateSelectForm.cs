﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.Editors;
using DevComponents.Editors.DateTimeAdv;
using ns18;
using ns2;
using ns26;
using ns4;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000249 RID: 585
	internal sealed partial class DateSelectForm : Form
	{
		// Token: 0x060018D3 RID: 6355 RVA: 0x0000A3F2 File Offset: 0x000085F2
		public DateSelectForm()
		{
			this.method_8();
			base.AutoScaleMode = AutoScaleMode.Dpi;
			this.method_1();
		}

		// Token: 0x060018D4 RID: 6356 RVA: 0x000AAFE8 File Offset: 0x000A91E8
		private void DateSelectForm_Load(object sender, EventArgs e)
		{
			this.labelX_0.ForeColor = Class181.color_4;
			this.comboBox_0.Enabled = false;
			this.comboBox_1.Enabled = false;
			this.radioButton_2.Enabled = this.method_4();
			this.radioButton_1.Checked = true;
			Base.UI.smethod_80(this.dateTimeInput_0);
			Base.UI.smethod_80(this.dateTimeInput_1);
			if (Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_0)).Any<SymbDataSet>())
			{
				if (Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_1)).Any<SymbDataSet>())
				{
					DateTime dateTime = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_2)).Min(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_3));
					DateTime dateTime2 = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_4)).Max(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_5));
					while (!this.method_0(dateTime))
					{
						dateTime = dateTime.AddDays(1.0);
					}
					while (!this.method_0(dateTime2))
					{
						dateTime2 = dateTime2.AddDays(-1.0);
					}
					this.dateTimeInput_0.MinDate = dateTime;
					this.dateTimeInput_0.MaxDate = dateTime2;
					this.dateTimeInput_1.MinDate = dateTime;
					this.dateTimeInput_1.MaxDate = dateTime2;
					if (Base.Data.CurrDate >= dateTime && Base.Data.CurrDate.Date <= dateTime2.Date)
					{
						this.dateTimeInput_0.Value = Base.Data.CurrDate;
					}
					else
					{
						this.dateTimeInput_0.Value = dateTime;
					}
					this.dateTimeInput_1.Value = dateTime2;
					this.labelX_0.Text = string.Concat(new string[]
					{
						TApp.IsTrialUser ? Class521.smethod_0(65025) : Class521.smethod_0(65008),
						Class521.smethod_0(65074),
						dateTime.ToLongDateString(),
						Class521.smethod_0(65103),
						dateTime2.ToLongDateString(),
						Class521.smethod_0(65108)
					});
					for (int i = 0; i <= 24; i++)
					{
						this.comboBox_0.Items.Add(i);
					}
					for (int j = 0; j <= 60; j++)
					{
						this.comboBox_1.Items.Add(j);
					}
					if (dateTime != DateTime.MinValue)
					{
						this.comboBox_0.Text = dateTime.Hour.ToString();
					}
					if (dateTime2 != DateTime.MaxValue)
					{
						this.comboBox_1.Text = dateTime2.Minute.ToString();
						goto IL_360;
					}
					goto IL_360;
				}
			}
			this.dateTimeInput_0.Enabled = false;
			this.dateTimeInput_0.Enabled = false;
			this.labelX_0.Text = Class521.smethod_0(65242);
			this.button_0.Enabled = false;
			IL_360:
			this.dateTimeInput_0.ValueChanged += this.dateTimeInput_0_ValueChanged;
			this.dateTimeInput_1.ValueChanged += this.dateTimeInput_1_ValueChanged;
		}

		// Token: 0x060018D5 RID: 6357 RVA: 0x000AB388 File Offset: 0x000A9588
		private void DateSelectForm_Shown(object sender, EventArgs e)
		{
			List<DateTime> list = new List<DateTime>();
			DateTime dateTime;
			DateTime dateTime2;
			if (Base.Data.SymbDataSets.Exists(new Predicate<SymbDataSet>(DateSelectForm.<>c.<>9.method_6)))
			{
				dateTime = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_7)).Min(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_8));
				dateTime2 = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_9)).Max(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_10));
			}
			else
			{
				dateTime = this.dateTimeInput_0.Value;
				dateTime2 = this.dateTimeInput_1.Value;
			}
			while (!this.method_0(dateTime2))
			{
				dateTime2 = dateTime2.AddDays(-1.0);
			}
			while (dateTime <= dateTime2)
			{
				if (this.method_0(dateTime))
				{
					list.Add(dateTime);
				}
				dateTime = dateTime.AddDays(1.0);
			}
			this.rangeSlider_0.RangeValues = list;
			this.rangeSlider_0.Range2 = this.dateTimeInput_1.Value;
			this.rangeSlider_0.Range1 = this.dateTimeInput_0.Value;
			this.rangeSlider_0.RangeChanged += this.method_2;
		}

		// Token: 0x060018D6 RID: 6358 RVA: 0x000AB518 File Offset: 0x000A9718
		private bool method_0(DateTime dateTime_0)
		{
			bool result;
			if (dateTime_0.DayOfWeek != DayOfWeek.Saturday && dateTime_0.DayOfWeek != DayOfWeek.Sunday && (dateTime_0.Day != 1 || (dateTime_0.Month != 1 && dateTime_0.Month != 5 && dateTime_0.Month != 10)) && ((dateTime_0.Day != 2 && dateTime_0.Day != 3) || (dateTime_0.Month != 5 && dateTime_0.Month != 10)))
			{
				if (dateTime_0.Day != 4 && dateTime_0.Day != 5 && dateTime_0.Day != 6)
				{
					if (dateTime_0.Day != 7)
					{
						return true;
					}
				}
				result = (dateTime_0.Month != 10);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060018D7 RID: 6359 RVA: 0x000AB5D4 File Offset: 0x000A97D4
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font(Class521.smethod_0(6998), num);
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				if (control.Font.Size != num)
				{
					control.Font = new Font(Class521.smethod_0(6998), num);
				}
			}
			foreach (object obj2 in this.panel_0.Controls)
			{
				Control control2 = (Control)obj2;
				if (control2.Font.Size != num)
				{
					control2.Font = new Font(Class521.smethod_0(6998), num);
				}
			}
			if (this.rangeSlider_0.LabelFont.Size != num)
			{
				this.rangeSlider_0.LabelFont = new Font(Class521.smethod_0(24023), num);
			}
		}

		// Token: 0x060018D8 RID: 6360 RVA: 0x0000A40F File Offset: 0x0000860F
		private void method_2(object sender, EventArgs11 e)
		{
			this.dateTimeInput_0.Value = e.Range1;
			this.dateTimeInput_1.Value = e.Range2;
		}

		// Token: 0x060018D9 RID: 6361 RVA: 0x000AB71C File Offset: 0x000A991C
		private void dateTimeInput_0_ValueChanged(object sender, EventArgs e)
		{
			if (!this.method_4())
			{
				if (this.radioButton_2.Checked)
				{
					this.radioButton_1.Checked = true;
				}
				this.radioButton_2.Enabled = false;
			}
			else
			{
				this.radioButton_2.Enabled = true;
			}
			if (this.dateTimeInput_1.Value < this.dateTimeInput_0.Value)
			{
				if (!this.rangeSlider_0.IsDraggingThumb)
				{
					this.method_3();
				}
				this.dateTimeInput_0.Value = this.dateTimeInput_1.Value;
			}
			this.rangeSlider_0.Range1 = this.dateTimeInput_0.Value;
		}

		// Token: 0x060018DA RID: 6362 RVA: 0x000AB7C4 File Offset: 0x000A99C4
		private void dateTimeInput_1_ValueChanged(object sender, EventArgs e)
		{
			if (this.dateTimeInput_1.Value < this.dateTimeInput_0.Value)
			{
				if (!this.rangeSlider_0.IsDraggingThumb)
				{
					this.method_3();
				}
				this.dateTimeInput_1.Value = this.dateTimeInput_0.Value;
			}
			this.rangeSlider_0.Range2 = this.dateTimeInput_1.Value;
		}

		// Token: 0x060018DB RID: 6363 RVA: 0x0000A435 File Offset: 0x00008635
		private void method_3()
		{
			MessageBox.Show(Class521.smethod_0(65331), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x060018DC RID: 6364 RVA: 0x000AB830 File Offset: 0x000A9A30
		private bool method_4()
		{
			bool result = false;
			foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
			{
				if (symbDataSet.CurrSymbol.IsFutures)
				{
					DateTime value = this.dateTimeInput_0.Value;
					if (Base.Data.smethod_112(symbDataSet.CurrSymbol, value) != null)
					{
						result = true;
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x060018DD RID: 6365 RVA: 0x000AB8B0 File Offset: 0x000A9AB0
		private void button_0_Click(object sender, EventArgs e)
		{
			if (!this.dateTimeInput_0.Enabled || !this.dateTimeInput_1.Enabled)
			{
				base.Close();
			}
			if (this.dateTimeInput_0.Value >= this.dateTimeInput_1.Value.Date.AddHours(16.0))
			{
				this.method_3();
			}
			else
			{
				TimeSpan? timeSpan = this.method_6();
				if (timeSpan != null)
				{
					if (!Base.UI.smethod_122(this.dateTimeInput_0.Value, Class521.smethod_0(65388)))
					{
						if (Base.UI.smethod_123())
						{
							if (Base.Data.smethod_129(this.dateTimeInput_0.Value.Date, this.dateTimeInput_1.Value.Date))
							{
								this.method_5();
								Base.Data.smethod_128(this.dateTimeInput_0.Value.Date.Add(timeSpan.Value), new DateTime?(this.dateTimeInput_1.Value));
								base.Dispose();
							}
							else
							{
								this.method_7();
							}
						}
					}
				}
			}
		}

		// Token: 0x060018DE RID: 6366 RVA: 0x000AB9D8 File Offset: 0x000A9BD8
		private void method_5()
		{
			this.labelX_0.Text = Class521.smethod_0(65530);
			this.Refresh();
			this.Cursor = Cursors.WaitCursor;
			this.button_0.Enabled = false;
			this.button_1.Enabled = false;
		}

		// Token: 0x060018DF RID: 6367 RVA: 0x000ABA28 File Offset: 0x000A9C28
		private TimeSpan? method_6()
		{
			int hours = 0;
			int minutes = 0;
			TimeSpan? result;
			if (this.radioButton_1.Checked)
			{
				result = new TimeSpan?(Base.Data.SymbDataSets.Select(new Func<SymbDataSet, ExchgOBT>(this.method_9)).Min(new Func<ExchgOBT, TimeSpan>(DateSelectForm.<>c.<>9.method_11)));
			}
			else
			{
				TimeSpan? timeSpan;
				if (this.radioButton_2.Checked)
				{
					try
					{
						timeSpan = new TimeSpan?(Base.Data.SymbDataSets.Select(new Func<SymbDataSet, ExchgOBT>(this.method_10)).Where(new Func<ExchgOBT, bool>(DateSelectForm.<>c.<>9.method_12)).Min(new Func<ExchgOBT, TimeSpan>(DateSelectForm.<>c.<>9.method_13)));
						goto IL_176;
					}
					catch (Exception)
					{
						MessageBox.Show(Class521.smethod_0(65591), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						timeSpan = null;
						goto IL_176;
					}
				}
				try
				{
					hours = Convert.ToInt32(this.comboBox_0.Text);
				}
				catch
				{
					MessageBox.Show(Class521.smethod_0(65684), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					timeSpan = null;
					goto IL_176;
				}
				try
				{
					minutes = Convert.ToInt32(this.comboBox_1.Text);
					goto IL_17B;
				}
				catch
				{
					MessageBox.Show(Class521.smethod_0(65741), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					timeSpan = null;
				}
				goto IL_176;
				IL_17B:
				return new TimeSpan?(new TimeSpan(hours, minutes, 0));
				IL_176:
				result = timeSpan;
			}
			return result;
		}

		// Token: 0x060018E0 RID: 6368 RVA: 0x00005F66 File Offset: 0x00004166
		private void method_7()
		{
			MessageBox.Show(Class521.smethod_0(22701), Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x060018E1 RID: 6369 RVA: 0x000ABBEC File Offset: 0x000A9DEC
		private void radioButton_0_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_0.Checked)
			{
				this.comboBox_0.Enabled = true;
				this.comboBox_1.Enabled = true;
			}
			else
			{
				this.comboBox_0.Enabled = false;
				this.comboBox_1.Enabled = false;
			}
		}

		// Token: 0x060018E2 RID: 6370 RVA: 0x0000A456 File Offset: 0x00008656
		private void DateSelectForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			this.Cursor = Cursors.Default;
		}

		// Token: 0x060018E3 RID: 6371 RVA: 0x0000A465 File Offset: 0x00008665
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060018E4 RID: 6372 RVA: 0x000ABC3C File Offset: 0x000A9E3C
		private void method_8()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(DateSelectForm));
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.dateTimeInput_0 = new DateTimeInput();
			this.dateTimeInput_1 = new DateTimeInput();
			this.labelX_0 = new LabelX();
			this.label_2 = new Label();
			this.panel_0 = new Panel();
			this.radioButton_2 = new RadioButton();
			this.comboBox_1 = new ComboBox();
			this.label_4 = new Label();
			this.comboBox_0 = new ComboBox();
			this.label_3 = new Label();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.pictureBox_0 = new PictureBox();
			this.rangeSlider_0 = new RangeSlider();
			((ISupportInitialize)this.dateTimeInput_0).BeginInit();
			((ISupportInitialize)this.dateTimeInput_1).BeginInit();
			this.panel_0.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_0.Location = new Point(42, 46);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(84, 20);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(19821);
			this.label_1.AutoSize = true;
			this.label_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_1.Location = new Point(386, 46);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(84, 20);
			this.label_1.TabIndex = 1;
			this.label_1.Text = Class521.smethod_0(19950);
			this.button_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.button_0.Location = new Point(417, 361);
			this.button_0.Name = Class521.smethod_0(10838);
			this.button_0.Size = new Size(120, 32);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.button_1.Location = new Point(551, 361);
			this.button_1.Name = Class521.smethod_0(10825);
			this.button_1.Size = new Size(120, 32);
			this.button_1.TabIndex = 5;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.dateTimeInput_0.BackgroundStyle.Class = Class521.smethod_0(19842);
			this.dateTimeInput_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.ButtonClear.Text = Class521.smethod_0(19908);
			this.dateTimeInput_0.ButtonDropDown.Shortcut = eShortcut.AltDown;
			this.dateTimeInput_0.ButtonDropDown.Visible = true;
			this.dateTimeInput_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.dateTimeInput_0.Format = eDateTimePickerFormat.Long;
			this.dateTimeInput_0.IsPopupCalendarOpen = false;
			this.dateTimeInput_0.Location = new Point(148, 43);
			this.dateTimeInput_0.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.DaySize = new Size(30, 20);
			this.dateTimeInput_0.MonthCalendar.DisplayMonth = new DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_0.MonthCalendar.MarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dateTimeInput_0.Name = Class521.smethod_0(65798);
			this.dateTimeInput_0.Size = new Size(180, 27);
			this.dateTimeInput_0.TabIndex = 6;
			this.dateTimeInput_1.BackgroundStyle.Class = Class521.smethod_0(19842);
			this.dateTimeInput_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.ButtonDropDown.Shortcut = eShortcut.AltDown;
			this.dateTimeInput_1.ButtonDropDown.Visible = true;
			this.dateTimeInput_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.dateTimeInput_1.Format = eDateTimePickerFormat.Long;
			this.dateTimeInput_1.IsPopupCalendarOpen = false;
			this.dateTimeInput_1.Location = new Point(492, 43);
			this.dateTimeInput_1.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.DaySize = new Size(30, 20);
			this.dateTimeInput_1.MonthCalendar.DisplayMonth = new DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_1.MonthCalendar.MarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dateTimeInput_1.Name = Class521.smethod_0(65827);
			this.dateTimeInput_1.Size = new Size(180, 27);
			this.dateTimeInput_1.TabIndex = 7;
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.labelX_0.Location = new Point(60, 251);
			this.labelX_0.Name = Class521.smethod_0(16201);
			this.labelX_0.Size = new Size(612, 97);
			this.labelX_0.Style = eDotNetBarStyle.Office2003;
			this.labelX_0.TabIndex = 8;
			this.labelX_0.Text = Class521.smethod_0(16218);
			this.labelX_0.TextLineAlignment = StringAlignment.Near;
			this.labelX_0.WordWrap = true;
			this.label_2.AutoSize = true;
			this.label_2.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_2.Location = new Point(43, 191);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(84, 20);
			this.label_2.TabIndex = 9;
			this.label_2.Text = Class521.smethod_0(22780);
			this.panel_0.Controls.Add(this.radioButton_2);
			this.panel_0.Controls.Add(this.comboBox_1);
			this.panel_0.Controls.Add(this.label_4);
			this.panel_0.Controls.Add(this.comboBox_0);
			this.panel_0.Controls.Add(this.label_3);
			this.panel_0.Controls.Add(this.radioButton_0);
			this.panel_0.Controls.Add(this.radioButton_1);
			this.panel_0.Location = new Point(140, 171);
			this.panel_0.Name = Class521.smethod_0(8903);
			this.panel_0.Size = new Size(547, 57);
			this.panel_0.TabIndex = 10;
			this.radioButton_2.AutoSize = true;
			this.radioButton_2.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.radioButton_2.Location = new Point(128, 19);
			this.radioButton_2.Name = Class521.smethod_0(65852);
			this.radioButton_2.Size = new Size(90, 24);
			this.radioButton_2.TabIndex = 22;
			this.radioButton_2.TabStop = true;
			this.radioButton_2.Text = Class521.smethod_0(65881);
			this.radioButton_2.UseVisualStyleBackColor = true;
			this.comboBox_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Location = new Point(452, 18);
			this.comboBox_1.Name = Class521.smethod_0(65898);
			this.comboBox_1.Size = new Size(53, 28);
			this.comboBox_1.TabIndex = 7;
			this.label_4.AutoSize = true;
			this.label_4.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_4.Location = new Point(508, 21);
			this.label_4.Name = Class521.smethod_0(5893);
			this.label_4.Size = new Size(24, 20);
			this.label_4.TabIndex = 6;
			this.label_4.Text = Class521.smethod_0(65915);
			this.comboBox_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(363, 18);
			this.comboBox_0.Name = Class521.smethod_0(65920);
			this.comboBox_0.Size = new Size(53, 28);
			this.comboBox_0.TabIndex = 5;
			this.label_3.AutoSize = true;
			this.label_3.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_3.Location = new Point(420, 21);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(24, 20);
			this.label_3.TabIndex = 3;
			this.label_3.Text = Class521.smethod_0(24972);
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.radioButton_0.Location = new Point(251, 19);
			this.radioButton_0.Name = Class521.smethod_0(65941);
			this.radioButton_0.Size = new Size(105, 24);
			this.radioButton_0.TabIndex = 1;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(65970);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_0.CheckedChanged += this.radioButton_0_CheckedChanged;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Font = new Font(Class521.smethod_0(6998), 9f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.radioButton_1.Location = new Point(5, 19);
			this.radioButton_1.Name = Class521.smethod_0(65991);
			this.radioButton_1.Size = new Size(90, 24);
			this.radioButton_1.TabIndex = 0;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(66020);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(34, 255);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 21;
			this.pictureBox_0.TabStop = false;
			this.rangeSlider_0.BackColor = SystemColors.Control;
			this.rangeSlider_0.DisabledBarColor = Color.LightSteelBlue;
			this.rangeSlider_0.DisabledRangeLabelColor = Color.Gray;
			this.rangeSlider_0.GapFromLeftMargin = 20U;
			this.rangeSlider_0.GapFromRightMargin = 20U;
			this.rangeSlider_0.HeightOfThumb = 20f;
			this.rangeSlider_0.InFocusBarColor = Color.SkyBlue;
			this.rangeSlider_0.InFocusRangeLabelColor = Color.SteelBlue;
			this.rangeSlider_0.LabelFont = new Font(Class521.smethod_0(24023), 8.25f);
			this.rangeSlider_0.LeftThumbImagePath = null;
			this.rangeSlider_0.Location = new Point(40, 92);
			this.rangeSlider_0.MiddleBarWidth = 3U;
			this.rangeSlider_0.Name = Class521.smethod_0(66037);
			this.rangeSlider_0.OutputStringFontColor = Color.Black;
			this.rangeSlider_0.Range1 = new DateTime(0L);
			this.rangeSlider_0.Range2 = new DateTime(0L);
			this.rangeSlider_0.RangeValues = (List<DateTime>)componentResourceManager.GetObject(Class521.smethod_0(66054));
			this.rangeSlider_0.RightThumbImagePath = null;
			this.rangeSlider_0.Size = new Size(643, 54);
			this.rangeSlider_0.TabIndex = 22;
			this.rangeSlider_0.ThumbColor = Color.CornflowerBlue;
			this.rangeSlider_0.ThumbColorHighlighted = Color.LightCyan;
			this.rangeSlider_0.WidthOfThumb = 10f;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(726, 408);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.label_0);
			base.Controls.Add(this.rangeSlider_0);
			base.Controls.Add(this.dateTimeInput_1);
			base.Controls.Add(this.dateTimeInput_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.labelX_0);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(66087);
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(66108);
			base.FormClosing += this.DateSelectForm_FormClosing;
			base.Load += this.DateSelectForm_Load;
			base.Shown += this.DateSelectForm_Shown;
			((ISupportInitialize)this.dateTimeInput_0).EndInit();
			((ISupportInitialize)this.dateTimeInput_1).EndInit();
			this.panel_0.ResumeLayout(false);
			this.panel_0.PerformLayout();
			((ISupportInitialize)this.pictureBox_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x060018E5 RID: 6373 RVA: 0x000ACDB4 File Offset: 0x000AAFB4
		[CompilerGenerated]
		private ExchgOBT method_9(SymbDataSet symbDataSet_0)
		{
			return Base.Data.smethod_110(symbDataSet_0.CurrSymbol, this.dateTimeInput_0.Value);
		}

		// Token: 0x060018E6 RID: 6374 RVA: 0x000ACDB4 File Offset: 0x000AAFB4
		[CompilerGenerated]
		private ExchgOBT method_10(SymbDataSet symbDataSet_0)
		{
			return Base.Data.smethod_110(symbDataSet_0.CurrSymbol, this.dateTimeInput_0.Value);
		}

		// Token: 0x04000C69 RID: 3177
		private IContainer icontainer_0;

		// Token: 0x04000C6A RID: 3178
		private Label label_0;

		// Token: 0x04000C6B RID: 3179
		private Label label_1;

		// Token: 0x04000C6C RID: 3180
		private Button button_0;

		// Token: 0x04000C6D RID: 3181
		private Button button_1;

		// Token: 0x04000C6E RID: 3182
		private DateTimeInput dateTimeInput_0;

		// Token: 0x04000C6F RID: 3183
		private DateTimeInput dateTimeInput_1;

		// Token: 0x04000C70 RID: 3184
		private LabelX labelX_0;

		// Token: 0x04000C71 RID: 3185
		private Label label_2;

		// Token: 0x04000C72 RID: 3186
		private Panel panel_0;

		// Token: 0x04000C73 RID: 3187
		private Label label_3;

		// Token: 0x04000C74 RID: 3188
		private RadioButton radioButton_0;

		// Token: 0x04000C75 RID: 3189
		private RadioButton radioButton_1;

		// Token: 0x04000C76 RID: 3190
		private ComboBox comboBox_0;

		// Token: 0x04000C77 RID: 3191
		private ComboBox comboBox_1;

		// Token: 0x04000C78 RID: 3192
		private Label label_4;

		// Token: 0x04000C79 RID: 3193
		private PictureBox pictureBox_0;

		// Token: 0x04000C7A RID: 3194
		private RadioButton radioButton_2;

		// Token: 0x04000C7B RID: 3195
		private RangeSlider rangeSlider_0;
	}
}
