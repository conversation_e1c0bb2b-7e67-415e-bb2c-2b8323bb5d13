﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns18;
using ns26;

namespace TEx
{
	// Token: 0x02000197 RID: 407
	[Serializable]
	internal sealed class DrawGrnArwRDn : DrawRedArwUp, ISerializable
	{
		// Token: 0x06000FB4 RID: 4020 RVA: 0x00006B2F File Offset: 0x00004D2F
		public DrawGrnArwRDn()
		{
		}

		// Token: 0x06000FB5 RID: 4021 RVA: 0x00006BC5 File Offset: 0x00004DC5
		public DrawGrnArwRDn(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(27630);
		}

		// Token: 0x06000FB6 RID: 4022 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawGrnArwRDn(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FB7 RID: 4023 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FB8 RID: 4024 RVA: 0x00064FD4 File Offset: 0x000631D4
		protected override Image vmethod_24()
		{
			return Class375.GreenArrow_RDown;
		}
	}
}
