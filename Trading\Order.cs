﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using ns18;

namespace TEx.Trading
{
	// Token: 0x020003B3 RID: 947
	[Serializable]
	internal class Order : INotifyPropertyChanging, INotifyPropertyChanged
	{
		// Token: 0x17000696 RID: 1686
		// (get) Token: 0x0600267F RID: 9855 RVA: 0x00105934 File Offset: 0x00103B34
		// (set) Token: 0x06002680 RID: 9856 RVA: 0x0000EBCA File Offset: 0x0000CDCA
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if (this._ID != value)
				{
					this.vmethod_0();
					this._ID = value;
					this.vmethod_1(Class521.smethod_0(33549));
				}
			}
		}

		// Token: 0x17000697 RID: 1687
		// (get) Token: 0x06002681 RID: 9857 RVA: 0x0010594C File Offset: 0x00103B4C
		// (set) Token: 0x06002682 RID: 9858 RVA: 0x0000EBF4 File Offset: 0x0000CDF4
		public int AcctID
		{
			get
			{
				return this._AcctID;
			}
			set
			{
				if (this._AcctID != value)
				{
					this.vmethod_0();
					this._AcctID = value;
					this.vmethod_1(Class521.smethod_0(48921));
				}
			}
		}

		// Token: 0x17000698 RID: 1688
		// (get) Token: 0x06002683 RID: 9859 RVA: 0x00105964 File Offset: 0x00103B64
		// (set) Token: 0x06002684 RID: 9860 RVA: 0x0000EC1E File Offset: 0x0000CE1E
		public int SymbolID
		{
			get
			{
				return this._SymbolID;
			}
			set
			{
				if (this._SymbolID != value)
				{
					this.vmethod_0();
					this._SymbolID = value;
					this.vmethod_1(Class521.smethod_0(110654));
				}
			}
		}

		// Token: 0x17000699 RID: 1689
		// (get) Token: 0x06002685 RID: 9861 RVA: 0x0010597C File Offset: 0x00103B7C
		// (set) Token: 0x06002686 RID: 9862 RVA: 0x0000EC48 File Offset: 0x0000CE48
		public int OrderType
		{
			get
			{
				return this._OrderType;
			}
			set
			{
				if (this._OrderType != value)
				{
					this.vmethod_0();
					this._OrderType = value;
					this.vmethod_1(Class521.smethod_0(110667));
				}
			}
		}

		// Token: 0x1700069A RID: 1690
		// (get) Token: 0x06002687 RID: 9863 RVA: 0x00105994 File Offset: 0x00103B94
		// (set) Token: 0x06002688 RID: 9864 RVA: 0x0000EC72 File Offset: 0x0000CE72
		public int OrderStatus
		{
			get
			{
				return this._OrderStatus;
			}
			set
			{
				if (this._OrderStatus != value)
				{
					this.vmethod_0();
					this._OrderStatus = value;
					this.vmethod_1(Class521.smethod_0(110680));
				}
			}
		}

		// Token: 0x1700069B RID: 1691
		// (get) Token: 0x06002689 RID: 9865 RVA: 0x001059AC File Offset: 0x00103BAC
		// (set) Token: 0x0600268A RID: 9866 RVA: 0x0000EC9C File Offset: 0x0000CE9C
		public long Units
		{
			get
			{
				return this._Units;
			}
			set
			{
				if (this._Units != value)
				{
					this.vmethod_0();
					this._Units = value;
					this.vmethod_1(Class521.smethod_0(110697));
				}
			}
		}

		// Token: 0x1700069C RID: 1692
		// (get) Token: 0x0600268B RID: 9867 RVA: 0x001059C4 File Offset: 0x00103BC4
		// (set) Token: 0x0600268C RID: 9868 RVA: 0x0000ECC6 File Offset: 0x0000CEC6
		public decimal Price
		{
			get
			{
				return this._Price;
			}
			set
			{
				if (this._Price != value)
				{
					this.vmethod_0();
					this._Price = value;
					this.vmethod_1(Class521.smethod_0(44907));
				}
			}
		}

		// Token: 0x1700069D RID: 1693
		// (get) Token: 0x0600268D RID: 9869 RVA: 0x001059DC File Offset: 0x00103BDC
		// (set) Token: 0x0600268E RID: 9870 RVA: 0x0000ECF5 File Offset: 0x0000CEF5
		public DateTime CreateTime
		{
			get
			{
				return this._CreateTime;
			}
			set
			{
				if (this._CreateTime != value)
				{
					this.vmethod_0();
					this._CreateTime = value;
					this.vmethod_1(Class521.smethod_0(110706));
				}
			}
		}

		// Token: 0x1700069E RID: 1694
		// (get) Token: 0x0600268F RID: 9871 RVA: 0x001059F4 File Offset: 0x00103BF4
		// (set) Token: 0x06002690 RID: 9872 RVA: 0x00105A0C File Offset: 0x00103C0C
		public DateTime? UpdateTime
		{
			get
			{
				return this._UpdateTime;
			}
			set
			{
				if (this._UpdateTime != value)
				{
					this.vmethod_0();
					this._UpdateTime = value;
					this.vmethod_1(Class521.smethod_0(110583));
				}
			}
		}

		// Token: 0x1700069F RID: 1695
		// (get) Token: 0x06002691 RID: 9873 RVA: 0x00105A78 File Offset: 0x00103C78
		// (set) Token: 0x06002692 RID: 9874 RVA: 0x0000ED24 File Offset: 0x0000CF24
		public string Notes
		{
			get
			{
				return this._Notes;
			}
			set
			{
				if (this._Notes != value)
				{
					this.vmethod_0();
					this._Notes = value;
					this.vmethod_1(Class521.smethod_0(33326));
				}
			}
		}

		// Token: 0x140000BC RID: 188
		// (add) Token: 0x06002693 RID: 9875 RVA: 0x00105A90 File Offset: 0x00103C90
		// (remove) Token: 0x06002694 RID: 9876 RVA: 0x00105AC8 File Offset: 0x00103CC8
		public event PropertyChangingEventHandler PropertyChanging;

		// Token: 0x140000BD RID: 189
		// (add) Token: 0x06002695 RID: 9877 RVA: 0x00105B00 File Offset: 0x00103D00
		// (remove) Token: 0x06002696 RID: 9878 RVA: 0x00105B38 File Offset: 0x00103D38
		public event PropertyChangedEventHandler PropertyChanged;

		// Token: 0x06002697 RID: 9879 RVA: 0x0000ED53 File Offset: 0x0000CF53
		protected virtual void vmethod_0()
		{
			if (this.PropertyChanging != null)
			{
				this.PropertyChanging(this, Order.emptyChangingEventArgs);
			}
		}

		// Token: 0x06002698 RID: 9880 RVA: 0x0000ED70 File Offset: 0x0000CF70
		protected virtual void vmethod_1(string string_0)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(string_0));
			}
		}

		// Token: 0x04001289 RID: 4745
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(string.Empty);

		// Token: 0x0400128A RID: 4746
		private int _ID;

		// Token: 0x0400128B RID: 4747
		private int _AcctID;

		// Token: 0x0400128C RID: 4748
		private int _SymbolID;

		// Token: 0x0400128D RID: 4749
		private int _OrderType;

		// Token: 0x0400128E RID: 4750
		private int _OrderStatus;

		// Token: 0x0400128F RID: 4751
		private long _Units;

		// Token: 0x04001290 RID: 4752
		private decimal _Price;

		// Token: 0x04001291 RID: 4753
		private DateTime _CreateTime;

		// Token: 0x04001292 RID: 4754
		private DateTime? _UpdateTime;

		// Token: 0x04001293 RID: 4755
		private string _Notes;

		// Token: 0x04001294 RID: 4756
		[CompilerGenerated]
		private PropertyChangingEventHandler PropertyChanging;

		// Token: 0x04001295 RID: 4757
		[CompilerGenerated]
		private PropertyChangedEventHandler PropertyChanged;
	}
}
