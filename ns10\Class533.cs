﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace ns10
{
	// Token: 0x020003E5 RID: 997
	[CompilerGenerated]
	internal sealed class Class533
	{
		// Token: 0x0400137A RID: 4986 RVA: 0x000022D0 File Offset: 0x000004D0
		internal static readonly Class533.Struct22 struct22_0;

		// Token: 0x0400137B RID: 4987 RVA: 0x00002320 File Offset: 0x00000520
		internal static readonly Class533.Struct21 struct21_0;

		// Token: 0x0400137C RID: 4988 RVA: 0x00002330 File Offset: 0x00000530
		internal static readonly Class533.Struct25 struct25_0;

		// Token: 0x0400137D RID: 4989 RVA: 0x00002730 File Offset: 0x00000930
		internal static readonly Class533.Struct23 struct23_0;

		// Token: 0x0400137E RID: 4990 RVA: 0x000027A8 File Offset: 0x000009A8
		internal static readonly Class533.Struct24 struct24_0;

		// Token: 0x0400137F RID: 4991 RVA: 0x00002820 File Offset: 0x00000A20
		internal static readonly Class533.Struct21 struct21_1;

		// Token: 0x04001380 RID: 4992 RVA: 0x00002830 File Offset: 0x00000A30
		internal static readonly Class533.Struct21 struct21_2;

		// Token: 0x04001381 RID: 4993 RVA: 0x00002840 File Offset: 0x00000A40
		internal static readonly Class533.Struct20 struct20_0;

		// Token: 0x04001382 RID: 4994 RVA: 0x00002850 File Offset: 0x00000A50
		internal static readonly Class533.Struct20 struct20_1;

		// Token: 0x04001383 RID: 4995 RVA: 0x00002860 File Offset: 0x00000A60
		internal static readonly long long_0;

		// Token: 0x04001384 RID: 4996 RVA: 0x00002868 File Offset: 0x00000A68
		internal static readonly long long_1;

		// Token: 0x04001385 RID: 4997 RVA: 0x00002870 File Offset: 0x00000A70
		internal static readonly Class533.Struct24 struct24_1;

		// Token: 0x04001386 RID: 4998 RVA: 0x000028E8 File Offset: 0x00000AE8
		internal static readonly Class533.Struct23 struct23_1;

		// Token: 0x020003E6 RID: 998
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 12)]
		private struct Struct20
		{
		}

		// Token: 0x020003E7 RID: 999
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 16)]
		private struct Struct21
		{
		}

		// Token: 0x020003E8 RID: 1000
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 76)]
		private struct Struct22
		{
		}

		// Token: 0x020003E9 RID: 1001
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 116)]
		private struct Struct23
		{
		}

		// Token: 0x020003EA RID: 1002
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 120)]
		private struct Struct24
		{
		}

		// Token: 0x020003EB RID: 1003
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 1024)]
		private struct Struct25
		{
		}
	}
}
