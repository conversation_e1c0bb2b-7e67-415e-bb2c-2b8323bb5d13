﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using ns18;
using ns26;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002E6 RID: 742
	internal class ShapeCurve : Class383
	{
		// Token: 0x060020EB RID: 8427 RVA: 0x0000D4F2 File Offset: 0x0000B6F2
		public ShapeCurve(DataArray data, DataProvider dp, IndEx indEx) : base(data, dp, indEx)
		{
			if (this.rollingPointPairList_0 != null)
			{
				this.rollingPointPairList_0.Clear();
			}
		}

		// Token: 0x060020EC RID: 8428 RVA: 0x000EA2B8 File Offset: 0x000E84B8
		protected PointPair method_1(int int_0)
		{
			return this.vmethod_0(int_0, base.IndData);
		}

		// Token: 0x060020ED RID: 8429 RVA: 0x000EA2D8 File Offset: 0x000E84D8
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				Class184.smethod_0(new Exception(this.indEx_0.EnName + Class521.smethod_0(98161) + int_0));
				int_0 = dataArray_1.Data.Length - 1;
			}
			double x = new XDate(base.method_0(int_0));
			double y = dataArray_1.Data[int_0];
			return new PointPair(x, y);
		}

		// Token: 0x060020EE RID: 8430 RVA: 0x000EA34C File Offset: 0x000E854C
		protected int method_2()
		{
			int result;
			try
			{
				int length = Class521.smethod_0(98206).Length;
				if (base.IndData.LineWithStr != null && base.IndData.LineWithStr.Length > length && base.IndData.LineWithStr.Substring(0, length).ToUpper() == Class521.smethod_0(98206))
				{
					string text = base.IndData.LineWithStr.Substring(length, base.IndData.LineWithStr.Length - length);
					bool flag = true;
					int i = 0;
					while (i < text.Length)
					{
						if (char.IsDigit(text[i]))
						{
							i++;
						}
						else
						{
							flag = false;
							IL_A4:
							if (flag)
							{
								result = int.Parse(text);
								goto IL_BF;
							}
							goto IL_B1;
						}
					}
					goto IL_A4;
				}
				IL_B1:;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return 1;
			IL_BF:
			return result;
		}

		// Token: 0x060020EF RID: 8431 RVA: 0x000EA430 File Offset: 0x000E8630
		protected void method_3(string string_0, LineItem lineItem_0)
		{
			this.curveItem_0 = lineItem_0;
			lineItem_0.Tag = string_0 + (string.IsNullOrEmpty(base.IndData.Name) ? Class521.smethod_0(1449) : (Class521.smethod_0(2712) + base.IndData.Name)) + (base.IndData.NumVisibleLineNot ? Class521.smethod_0(98219) : Class521.smethod_0(1449));
			lineItem_0.Line.Width = (float)this.method_2();
			if (base.IndData.LineTypeStr != null)
			{
				if (!(base.IndData.LineTypeStr == Class521.smethod_0(98232)) && !(base.IndData.LineTypeStr == Class521.smethod_0(98237)) && !(base.IndData.LineTypeStr == Class521.smethod_0(98250)) && !(base.IndData.LineTypeStr == Class521.smethod_0(98263)))
				{
					if (base.IndData.LineTypeStr == Class521.smethod_0(98276))
					{
						lineItem_0.Line.Style = DashStyle.Dash;
					}
					else if (base.IndData.LineTypeStr == Class521.smethod_0(98285))
					{
						lineItem_0.Line.Style = DashStyle.DashDot;
					}
				}
				else
				{
					lineItem_0.Line.Style = DashStyle.Custom;
					lineItem_0.Line.DashOn = 1f;
					lineItem_0.Line.DashOff = 10f;
				}
			}
			lineItem_0.Line.IsAntiAlias = true;
			if (base.IndData.NumVisibleLineNot)
			{
				lineItem_0.Line.IsVisible = false;
			}
			if (base.IndData.ShapeStr == Class521.smethod_0(97386) || base.IndData.ShapeStr == Class521.smethod_0(97369) || base.IndData.ShapeStr == Class521.smethod_0(97412))
			{
				lineItem_0.Line.IsVisible = false;
			}
		}

		// Token: 0x060020F0 RID: 8432 RVA: 0x000EA64C File Offset: 0x000E884C
		public virtual void vmethod_8(ref double double_0, ref double double_1)
		{
			for (int i = 0; i < this.rollingPointPairList_0.Count; i++)
			{
				double y = this.rollingPointPairList_0[i].Y;
				if (!double.IsNaN(y))
				{
					if (y > double_0)
					{
						double_0 = y;
					}
					if (y < double_1)
					{
						double_1 = y;
					}
				}
			}
		}

		// Token: 0x060020F1 RID: 8433 RVA: 0x0000D512 File Offset: 0x0000B712
		public override void vmethod_1(ZedGraphControl zedGraphControl_0)
		{
			if (this.Curve != null && zedGraphControl_0.GraphPane != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.Curve);
			}
		}

		// Token: 0x060020F2 RID: 8434 RVA: 0x0000D53D File Offset: 0x0000B73D
		public override void vmethod_2(int int_0)
		{
			if (base.KCount != base.IndData.Data.Count<double>())
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(97762)));
			}
			else
			{
				this.vmethod_3(int_0, base.IndData);
			}
		}

		// Token: 0x060020F3 RID: 8435 RVA: 0x000EA69C File Offset: 0x000E889C
		public override void vmethod_3(int int_0, DataArray dataArray_1)
		{
			if (this.method_5(dataArray_1))
			{
				this.method_4(int_0, dataArray_1);
			}
			PointPair point = this.vmethod_0(int_0, dataArray_1);
			((IPointListEdit)this.rollingPointPairList_0).Add(point);
		}

		// Token: 0x060020F4 RID: 8436 RVA: 0x000EA6D4 File Offset: 0x000E88D4
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			if (this.method_5(dataArray_1))
			{
				this.method_4(int_0, dataArray_1);
			}
			PointPair value = this.vmethod_0(int_0, dataArray_1);
			IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
			if (rollingPointPairList_.Count > 0)
			{
				rollingPointPairList_[rollingPointPairList_.Count - 1] = value;
			}
		}

		// Token: 0x060020F5 RID: 8437 RVA: 0x0000D40D File Offset: 0x0000B60D
		public override double vmethod_5(int int_0, HisData hisData_0)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060020F6 RID: 8438 RVA: 0x000041B9 File Offset: 0x000023B9
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
		}

		// Token: 0x060020F7 RID: 8439 RVA: 0x000EA71C File Offset: 0x000E891C
		protected void method_4(int int_0, DataArray dataArray_1)
		{
			int count = this.rollingPointPairList_0.Count;
			if (count < int_0)
			{
				IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
				int num = 0;
				for (int i = count - 1; i >= 0; i--)
				{
					num++;
					rollingPointPairList_[i] = this.vmethod_0(int_0 - num, dataArray_1);
				}
			}
		}

		// Token: 0x060020F8 RID: 8440 RVA: 0x000EA768 File Offset: 0x000E8968
		public bool method_5(DataArray dataArray_1)
		{
			bool result;
			if (dataArray_1.HasLast)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x170005BF RID: 1471
		// (get) Token: 0x060020F9 RID: 8441 RVA: 0x000EA788 File Offset: 0x000E8988
		public CurveItem Curve
		{
			get
			{
				return this.curveItem_0;
			}
		}

		// Token: 0x04001021 RID: 4129
		protected CurveItem curveItem_0;
	}
}
