﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using ns18;
using SmartAssembly.SmartExceptionsCore;

namespace ns6
{
	// Token: 0x02000404 RID: 1028
	internal sealed class Class542
	{
		// Token: 0x060027DF RID: 10207 RVA: 0x0000F5FA File Offset: 0x0000D7FA
		public static void smethod_0(Exception exception_0)
		{
			Class542.smethod_11(exception_0, new object[0]);
		}

		// Token: 0x060027E0 RID: 10208 RVA: 0x0000F608 File Offset: 0x0000D808
		public static void smethod_1(Exception exception_0, object object_0)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0
			});
		}

		// Token: 0x060027E1 RID: 10209 RVA: 0x0000F61A File Offset: 0x0000D81A
		public static void smethod_2(Exception exception_0, object object_0, object object_1)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1
			});
		}

		// Token: 0x060027E2 RID: 10210 RVA: 0x0000F630 File Offset: 0x0000D830
		public static void smethod_3(Exception exception_0, object object_0, object object_1, object object_2)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2
			});
		}

		// Token: 0x060027E3 RID: 10211 RVA: 0x0000F64A File Offset: 0x0000D84A
		public static void smethod_4(Exception exception_0, object object_0, object object_1, object object_2, object object_3)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3
			});
		}

		// Token: 0x060027E4 RID: 10212 RVA: 0x0000F669 File Offset: 0x0000D869
		public static void smethod_5(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4
			});
		}

		// Token: 0x060027E5 RID: 10213 RVA: 0x0000F68D File Offset: 0x0000D88D
		public static void smethod_6(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5
			});
		}

		// Token: 0x060027E6 RID: 10214 RVA: 0x0000F6B6 File Offset: 0x0000D8B6
		public static void smethod_7(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6
			});
		}

		// Token: 0x060027E7 RID: 10215 RVA: 0x0000F6E4 File Offset: 0x0000D8E4
		public static void smethod_8(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7
			});
		}

		// Token: 0x060027E8 RID: 10216 RVA: 0x0000F717 File Offset: 0x0000D917
		public static void smethod_9(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7, object object_8)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7,
				object_8
			});
		}

		// Token: 0x060027E9 RID: 10217 RVA: 0x0000F750 File Offset: 0x0000D950
		public static void smethod_10(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7, object object_8, object object_9)
		{
			Class542.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7,
				object_8,
				object_9
			});
		}

		// Token: 0x060027EA RID: 10218 RVA: 0x0010CD48 File Offset: 0x0010AF48
		public static void smethod_11(Exception exception_0, object[] object_0)
		{
			int methodID = -1;
			int ilOffset = -1;
			int num = 0;
			StackTrace stackTrace = new StackTrace(exception_0);
			try
			{
				if (exception_0.StackTrace != null)
				{
					string[] array = exception_0.StackTrace.Split(new char[]
					{
						'\r',
						'\n'
					});
					for (int i = 0; i < array.Length; i++)
					{
						if (array[i].Length > 0)
						{
							num++;
						}
					}
				}
			}
			catch
			{
				num = -1;
			}
			try
			{
				if (stackTrace.FrameCount > 0)
				{
					StackFrame frame = stackTrace.GetFrame(stackTrace.FrameCount - 1);
					methodID = (frame.GetMethod().MetadataToken & 16777215) - 1;
					ilOffset = frame.GetILOffset();
				}
			}
			catch
			{
			}
			try
			{
				SmartStackFrame value = new SmartStackFrame(methodID, object_0, ilOffset, num);
				LinkedList<object> linkedList;
				if (!exception_0.Data.Contains(Class521.smethod_0(117811)))
				{
					linkedList = new LinkedList<object>();
					exception_0.Data[Class521.smethod_0(117811)] = linkedList;
				}
				else
				{
					linkedList = (LinkedList<object>)exception_0.Data[Class521.smethod_0(117811)];
				}
				linkedList.AddLast(value);
			}
			catch
			{
			}
		}

		// Token: 0x040013D2 RID: 5074
		public const string string_0 = "SmartStackFrames";
	}
}
