﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns17
{
	// Token: 0x020002FB RID: 763
	internal sealed class Class399 : ShapeCurve
	{
		// Token: 0x06002137 RID: 8503 RVA: 0x000EBE9C File Offset: 0x000EA09C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			JapaneseCandleStickItem japaneseCandleStickItem = zedGraphControl_0.GraphPane.AddJapaneseCandleStick(base.IndData.Name, base.DataView);
			japaneseCandleStickItem.Label.IsVisible = false;
			japaneseCandleStickItem.Stick.Width = 1f;
			japaneseCandleStickItem.Stick.Color = color_0;
			japaneseCandleStickItem.Stick.RisingFill.Color = color_0;
			japaneseCandleStickItem.Stick.RisingBorder.Color = color_0;
			japaneseCandleStickItem.Stick.FallingColor = color_0;
			japaneseCandleStickItem.Stick.FallingBorder.Color = color_0;
			japaneseCandleStickItem.Stick.FallingFill.Color = color_0;
			zedGraphControl_0.GraphPane.BarSettings.Type = BarType.Overlay;
			if (base.IndData.SingleData.Contains(Class521.smethod_0(98691)) && (int)base.IndData.SingleData[Class521.smethod_0(98691)] == 1)
			{
				japaneseCandleStickItem.Stick.RisingFill.Color = zedGraphControl_0.GraphPane.Fill.Color;
			}
			if (base.IndData.SingleData.Contains(Class521.smethod_0(98700)))
			{
				double num = (double)base.IndData.SingleData[Class521.smethod_0(98700)];
				japaneseCandleStickItem.Stick.Width = (float)((int)num);
			}
			this.curveItem_0 = japaneseCandleStickItem;
			japaneseCandleStickItem.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			japaneseCandleStickItem.Stick.Width = (float)base.method_2();
		}

		// Token: 0x06002138 RID: 8504 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class399(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002139 RID: 8505 RVA: 0x0000D40D File Offset: 0x0000B60D
		public override double vmethod_5(int int_0, HisData hisData_0)
		{
			throw new NotImplementedException();
		}

		// Token: 0x0600213A RID: 8506 RVA: 0x000EA2B8 File Offset: 0x000E84B8
		protected PointPair method_6(int int_0)
		{
			return this.vmethod_0(int_0, base.IndData);
		}

		// Token: 0x0600213B RID: 8507 RVA: 0x000EC054 File Offset: 0x000EA254
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			Class399.Class410 @class = new Class399.Class410();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_0);
			PointPair result;
			if (@class.dataArray_0.Data.Length == 0)
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(98709)));
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			else
			{
				if (@class.dataArray_0.Data.Length < int_0 + 1)
				{
					Class184.smethod_0(new Exception(Class521.smethod_0(98750)));
					int_0 = @class.dataArray_0.Data.Length - 1;
					if (int_0 < 0)
					{
						int_0 = 0;
					}
				}
				if (@class.dataArray_0.OtherDataArrayList.Count != 2)
				{
					throw new Exception(Class521.smethod_0(98421));
				}
				if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
				{
					throw new Exception(Class521.smethod_0(98474));
				}
				double num = @class.dataArray_0.Data[int_0];
				double num2 = @class.dataArray_0.OtherDataArrayList[1].Data[int_0];
				if (@class.dataArray_0.OtherDataArrayList[0].Data[int_0] == 1.0)
				{
					result = new StockPt(new XDate(dateTime), num, num2, num2, num, 0.0);
				}
				else
				{
					result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
				}
			}
			return result;
		}

		// Token: 0x0600213C RID: 8508 RVA: 0x000EC1E4 File Offset: 0x000EA3E4
		public override void vmethod_8(ref double double_0, ref double double_1)
		{
			base.vmethod_8(ref double_0, ref double_1);
			for (int i = 0; i < this.rollingPointPairList_0.Count; i++)
			{
				double z = this.rollingPointPairList_0[i].Z;
				if (!double.IsNaN(z))
				{
					if (z > double_0)
					{
						double_0 = z;
					}
					if (z < double_1)
					{
						double_1 = z;
					}
				}
			}
		}

		// Token: 0x0600213D RID: 8509 RVA: 0x0000D685 File Offset: 0x0000B885
		public override void vmethod_2(int int_0)
		{
			if (base.KCount != base.IndData.Data.Count<double>())
			{
				throw new Exception(Class521.smethod_0(97762));
			}
			((IPointListEdit)this.rollingPointPairList_0).Add(this.method_6(int_0));
		}

		// Token: 0x0600213E RID: 8510 RVA: 0x000EC23C File Offset: 0x000EA43C
		public override void vmethod_3(int int_0, DataArray dataArray_1)
		{
			PointPair point = this.vmethod_0(int_0, dataArray_1);
			((IPointListEdit)this.rollingPointPairList_0).Add(point);
		}

		// Token: 0x0600213F RID: 8511 RVA: 0x000EC260 File Offset: 0x000EA460
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			PointPair value = this.vmethod_0(int_0, dataArray_1);
			IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
			if (rollingPointPairList_.Count > 0)
			{
				rollingPointPairList_[rollingPointPairList_.Count - 1] = value;
			}
		}

		// Token: 0x020002FC RID: 764
		[CompilerGenerated]
		private sealed class Class410
		{
			// Token: 0x06002141 RID: 8513 RVA: 0x000EC298 File Offset: 0x000EA498
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x04001032 RID: 4146
			public DataArray dataArray_0;
		}
	}
}
