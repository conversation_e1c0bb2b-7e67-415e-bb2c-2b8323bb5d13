﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns22;
using ns23;
using ns26;

namespace ns21
{
	// Token: 0x02000412 RID: 1042
	[DesignerCategory("Code")]
	internal sealed class Control15 : Control
	{
		// Token: 0x170006E6 RID: 1766
		// (get) Token: 0x0600283A RID: 10298 RVA: 0x0000FA9D File Offset: 0x0000DC9D
		// (set) Token: 0x0600283B RID: 10299 RVA: 0x0010DC08 File Offset: 0x0010BE08
		public Enum36 IconState
		{
			get
			{
				return this.enum36_0;
			}
			set
			{
				if (this.enum36_0 != value)
				{
					this.enum36_0 = value;
					Enum36 @enum = this.enum36_0;
					if (@enum != Enum36.const_1)
					{
						if (@enum != Enum36.const_2)
						{
							this.bitmap_0 = null;
						}
						else
						{
							this.bitmap_0 = Class541.smethod_0(Class521.smethod_0(119569));
						}
					}
					else
					{
						this.bitmap_0 = Class541.smethod_0(Class521.smethod_0(119556));
					}
					this.Refresh();
				}
			}
		}

		// Token: 0x170006E7 RID: 1767
		// (get) Token: 0x0600283C RID: 10300 RVA: 0x0000FAA5 File Offset: 0x0000DCA5
		// (set) Token: 0x0600283D RID: 10301 RVA: 0x0000FAB2 File Offset: 0x0000DCB2
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Browsable(true)]
		public string Text
		{
			get
			{
				return this.label_0.Text;
			}
			set
			{
				this.label_0.Text = value;
			}
		}

		// Token: 0x170006E8 RID: 1768
		// (get) Token: 0x0600283E RID: 10302 RVA: 0x0000FAC0 File Offset: 0x0000DCC0
		// (set) Token: 0x0600283F RID: 10303 RVA: 0x0000FAC8 File Offset: 0x0000DCC8
		public Image Image
		{
			get
			{
				return this.image_0;
			}
			set
			{
				this.image_0 = value;
				this.Refresh();
			}
		}

		// Token: 0x06002840 RID: 10304 RVA: 0x0010DC70 File Offset: 0x0010BE70
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.icon_0 != null)
				{
					this.icon_0.Dispose();
					this.icon_0 = null;
				}
				if (this.image_0 != null)
				{
					this.image_0.Dispose();
					this.image_0 = null;
				}
				if (this.bitmap_0 != null)
				{
					this.bitmap_0.Dispose();
					this.bitmap_0 = null;
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002841 RID: 10305 RVA: 0x0010DCD8 File Offset: 0x0010BED8
		protected void OnResize(EventArgs e)
		{
			this.label_0.SetBounds(Convert.ToInt32(13f * this.float_0), Convert.ToInt32(15f * this.float_1), base.Width - Convert.ToInt32(69f * this.float_0), base.Height - Convert.ToInt32(18f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x06002842 RID: 10306 RVA: 0x0000FAD7 File Offset: 0x0000DCD7
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002843 RID: 10307 RVA: 0x0010DD4C File Offset: 0x0010BF4C
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			e.Graphics.DrawLine(SystemPens.ControlDark, 0, base.ClientSize.Height - 2, base.ClientSize.Width, base.ClientSize.Height - 2);
			e.Graphics.DrawLine(SystemPens.ControlLightLight, 0, base.ClientSize.Height - 1, base.ClientSize.Width, base.ClientSize.Height - 1);
			Rectangle rectangle = new Rectangle(base.ClientSize.Width - Convert.ToInt32(48f * this.float_0), Convert.ToInt32(11f * this.float_1), Convert.ToInt32(32f * this.float_0), Convert.ToInt32(32f * this.float_1));
			if (this.image_0 != null)
			{
				e.Graphics.DrawImage(this.image_0, rectangle, new Rectangle(0, 0, 32, 32), GraphicsUnit.Pixel);
				return;
			}
			if (this.icon_0 != null)
			{
				e.Graphics.DrawIcon(this.icon_0, rectangle);
				if (this.bitmap_0 != null)
				{
					e.Graphics.DrawImage(this.bitmap_0, new Rectangle(rectangle.Right - Convert.ToInt32(12f * this.float_0), rectangle.Bottom - Convert.ToInt32(12f * this.float_1), Convert.ToInt32(16f * this.float_0), Convert.ToInt32(16f * this.float_1)), new Rectangle(0, 0, 16, 16), GraphicsUnit.Pixel);
				}
			}
		}

		// Token: 0x06002844 RID: 10308 RVA: 0x0010DEFC File Offset: 0x0010C0FC
		protected void OnFontChanged(EventArgs e)
		{
			try
			{
				this.label_0.Font = new Font(this.Font, FontStyle.Bold);
				base.OnFontChanged(e);
			}
			catch
			{
			}
		}

		// Token: 0x06002845 RID: 10309 RVA: 0x0010DF3C File Offset: 0x0010C13C
		public Control15()
		{
			try
			{
				this.label_0.FlatStyle = FlatStyle.System;
				this.label_0.Font = new Font(this.Font, FontStyle.Bold);
			}
			catch
			{
			}
			base.Controls.Add(this.label_0);
			this.BackColor = SystemColors.Window;
			base.TabStop = false;
			this.Dock = DockStyle.Top;
			base.Height = 58;
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
			this.icon_0 = Class547.smethod_0();
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002846 RID: 10310 RVA: 0x0000FAFA File Offset: 0x0000DCFA
		public Control15(string string_0) : this()
		{
			this.label_0.Text = string_0;
		}

		// Token: 0x0400140D RID: 5133
		private Label label_0 = new Label();

		// Token: 0x0400140E RID: 5134
		private Image image_0;

		// Token: 0x0400140F RID: 5135
		private Icon icon_0;

		// Token: 0x04001410 RID: 5136
		private Bitmap bitmap_0;

		// Token: 0x04001411 RID: 5137
		private Enum36 enum36_0;

		// Token: 0x04001412 RID: 5138
		private float float_0 = 1f;

		// Token: 0x04001413 RID: 5139
		private float float_1 = 1f;
	}
}
