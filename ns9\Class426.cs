﻿using System;
using System.CodeDom.Compiler;
using System.IO;
using System.Text;
using Microsoft.CSharp;
using ns18;

namespace ns9
{
	// Token: 0x02000307 RID: 775
	internal sealed class Class426
	{
		// Token: 0x0600216D RID: 8557 RVA: 0x000ED208 File Offset: 0x000EB408
		public static void smethod_0()
		{
			CodeDomProvider codeDomProvider = new CSharpCodeProvider();
			CompilerParameters compilerParameters = new CompilerParameters();
			compilerParameters.ReferencedAssemblies.Add(Class521.smethod_0(99590));
			string value = Directory.GetCurrentDirectory() + Class521.smethod_0(99607);
			compilerParameters.ReferencedAssemblies.Add(value);
			compilerParameters.GenerateExecutable = false;
			compilerParameters.OutputAssembly = Directory.GetCurrentDirectory() + Class521.smethod_0(99632);
			string text = Class426.smethod_1();
			CompilerResults compilerResults = codeDomProvider.CompileAssemblyFromSource(compilerParameters, new string[]
			{
				text
			});
			if (compilerResults.Errors.HasErrors)
			{
				throw new Exception(compilerResults.Errors.ToString());
			}
		}

		// Token: 0x0600216E RID: 8558 RVA: 0x000ED2B0 File Offset: 0x000EB4B0
		private static string smethod_1()
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(Class521.smethod_0(99657));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99678));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99699));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99024));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99728));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99777));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99786));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99847));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99872));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99925));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99942));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(99987));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100016));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100057));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100074));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100115));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100128));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100205));
			stringBuilder.Append(Class521.smethod_0(100262));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(100279));
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Class521.smethod_0(47398));
			return stringBuilder.ToString();
		}
	}
}
