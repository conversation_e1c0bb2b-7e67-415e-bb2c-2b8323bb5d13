﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns26;
using TEx;

namespace ns21
{
	// Token: 0x02000199 RID: 409
	internal sealed partial class Form2 : Form1
	{
		// Token: 0x06000FBE RID: 4030 RVA: 0x00065018 File Offset: 0x00063218
		public Form2()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_2();
			this.BackColor = Color.White;
			this.pictureBox_0.Image = Class375.aboutBanner;
			this.label_0.Text = Base.UI.smethod_114() + Class521.smethod_0(27665) + TApp.Ver;
			for (int i = 0; i < 2; i++)
			{
				if (this.label_0.Text.EndsWith(Class521.smethod_0(27670)))
				{
					this.label_0.Text = this.label_0.Text.Substring(0, this.label_0.Text.Length - 2);
				}
			}
			this.label_1.Text = Class521.smethod_0(27675) + DateTime.Now.Year + Class521.smethod_0(27680);
			this.label_6.Text = Class521.smethod_0(1449);
			this.label_2.Text = TApp.OS;
			this.pictureBox_0.DoubleClick += this.pictureBox_0_DoubleClick;
			this.linkLabel_1.LinkClicked += this.linkLabel_1_LinkClicked;
			this.linkLabel_2.LinkClicked += this.linkLabel_2_LinkClicked;
			this.label_3.Location = new Point(this.pictureBox_1.Location.X + (this.pictureBox_1.Width - this.label_3.Width) / 2, this.pictureBox_1.Location.Y + this.pictureBox_1.Height + 2);
		}

		// Token: 0x06000FBF RID: 4031 RVA: 0x00004273 File Offset: 0x00002473
		private void button_0_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000FC0 RID: 4032 RVA: 0x000651D4 File Offset: 0x000633D4
		private void linkLabel_0_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC1 RID: 4033 RVA: 0x000651D4 File Offset: 0x000633D4
		private void linkLabel_1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC2 RID: 4034 RVA: 0x000651D4 File Offset: 0x000633D4
		private void linkLabel_2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC3 RID: 4035 RVA: 0x000651F4 File Offset: 0x000633F4
		private void method_1(LinkLabel linkLabel_3)
		{
			try
			{
				Process.Start(new ProcessStartInfo(Class521.smethod_0(27697) + linkLabel_3.Text));
			}
			catch
			{
			}
		}

		// Token: 0x06000FC4 RID: 4036 RVA: 0x00006C15 File Offset: 0x00004E15
		private void pictureBox_0_DoubleClick(object sender, EventArgs e)
		{
			this.label_6.Text = Class521.smethod_0(27710) + TApp.HOST;
			MessageBox.Show(TApp.smethod_9(), Class521.smethod_0(27719), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06000FC5 RID: 4037 RVA: 0x00006C50 File Offset: 0x00004E50
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000FC6 RID: 4038 RVA: 0x00065238 File Offset: 0x00063438
		private void method_2()
		{
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.button_0 = new Button();
			this.pictureBox_0 = new PictureBox();
			this.linkLabel_0 = new LinkLabel();
			this.label_2 = new Label();
			this.pictureBox_1 = new PictureBox();
			this.label_3 = new Label();
			this.label_4 = new Label();
			this.label_5 = new Label();
			this.linkLabel_1 = new LinkLabel();
			this.linkLabel_2 = new LinkLabel();
			this.label_6 = new Label();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			((ISupportInitialize)this.pictureBox_1).BeginInit();
			base.SuspendLayout();
			this.label_0.Location = new Point(341, 148);
			this.label_0.Name = Class521.smethod_0(27728);
			this.label_0.Size = new Size(274, 20);
			this.label_0.TabIndex = 2;
			this.label_0.Text = Class521.smethod_0(27741);
			this.label_0.TextAlign = ContentAlignment.TopRight;
			this.label_1.Location = new Point(341, 211);
			this.label_1.Name = Class521.smethod_0(27782);
			this.label_1.Size = new Size(274, 20);
			this.label_1.TabIndex = 3;
			this.label_1.Text = Class521.smethod_0(27795);
			this.label_1.TextAlign = ContentAlignment.TopRight;
			this.button_0.Location = new Point(484, 370);
			this.button_0.Name = Class521.smethod_0(7442);
			this.button_0.Size = new Size(131, 35);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.pictureBox_0.BackColor = Color.Transparent;
			this.pictureBox_0.Location = new Point(402, 36);
			this.pictureBox_0.Name = Class521.smethod_0(27820);
			this.pictureBox_0.Size = new Size(227, 79);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 5;
			this.pictureBox_0.TabStop = false;
			this.linkLabel_0.Location = new Point(341, 177);
			this.linkLabel_0.Name = Class521.smethod_0(27845);
			this.linkLabel_0.Size = new Size(274, 20);
			this.linkLabel_0.TabIndex = 10;
			this.linkLabel_0.TabStop = true;
			this.linkLabel_0.Text = Class521.smethod_0(27874);
			this.linkLabel_0.TextAlign = ContentAlignment.TopRight;
			this.linkLabel_0.LinkClicked += this.linkLabel_0_LinkClicked;
			this.label_2.Location = new Point(308, 241);
			this.label_2.Name = Class521.smethod_0(27903);
			this.label_2.Size = new Size(307, 31);
			this.label_2.TabIndex = 11;
			this.label_2.Text = Class521.smethod_0(27903);
			this.label_2.TextAlign = ContentAlignment.TopRight;
			this.pictureBox_1.BackColor = Color.Transparent;
			this.pictureBox_1.Image = Class375.SupportWxQrCode;
			this.pictureBox_1.Location = new Point(120, 76);
			this.pictureBox_1.Name = Class521.smethod_0(27924);
			this.pictureBox_1.Size = new Size(160, 182);
			this.pictureBox_1.SizeMode = PictureBoxSizeMode.AutoSize;
			this.pictureBox_1.TabIndex = 12;
			this.pictureBox_1.TabStop = false;
			this.label_3.Location = new Point(90, 266);
			this.label_3.Name = Class521.smethod_0(27949);
			this.label_3.Size = new Size(230, 24);
			this.label_3.TabIndex = 13;
			this.label_3.Text = Class521.smethod_0(27966);
			this.label_3.TextAlign = ContentAlignment.MiddleCenter;
			this.label_4.Location = new Point(1, 342);
			this.label_4.Name = Class521.smethod_0(5827);
			this.label_4.Size = new Size(151, 26);
			this.label_4.TabIndex = 14;
			this.label_4.Text = Class521.smethod_0(28003);
			this.label_4.TextAlign = ContentAlignment.TopRight;
			this.label_5.Location = new Point(4, 372);
			this.label_5.Name = Class521.smethod_0(5849);
			this.label_5.Size = new Size(148, 26);
			this.label_5.TabIndex = 15;
			this.label_5.Text = Class521.smethod_0(28032);
			this.label_5.TextAlign = ContentAlignment.TopRight;
			this.linkLabel_1.Location = new Point(158, 340);
			this.linkLabel_1.Name = Class521.smethod_0(28061);
			this.linkLabel_1.Size = new Size(274, 20);
			this.linkLabel_1.TabIndex = 16;
			this.linkLabel_1.TabStop = true;
			this.linkLabel_1.Text = Class521.smethod_0(28086);
			this.linkLabel_2.Location = new Point(158, 370);
			this.linkLabel_2.Name = Class521.smethod_0(28119);
			this.linkLabel_2.Size = new Size(274, 20);
			this.linkLabel_2.TabIndex = 17;
			this.linkLabel_2.TabStop = true;
			this.linkLabel_2.Text = Class521.smethod_0(28148);
			this.label_6.Location = new Point(308, 272);
			this.label_6.Name = Class521.smethod_0(28189);
			this.label_6.Size = new Size(307, 31);
			this.label_6.TabIndex = 18;
			this.label_6.Text = Class521.smethod_0(28189);
			this.label_6.TextAlign = ContentAlignment.TopRight;
			this.label_6.UseWaitCursor = true;
			base.AcceptButton = this.button_0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(680, 435);
			base.Controls.Add(this.label_6);
			base.Controls.Add(this.linkLabel_2);
			base.Controls.Add(this.linkLabel_1);
			base.Controls.Add(this.label_5);
			base.Controls.Add(this.label_4);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.pictureBox_1);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.linkLabel_0);
			base.Controls.Add(this.pictureBox_0);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(28210);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			this.Text = Class521.smethod_0(28223);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			((ISupportInitialize)this.pictureBox_1).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040007C7 RID: 1991
		private IContainer icontainer_0;

		// Token: 0x040007C8 RID: 1992
		private Label label_0;

		// Token: 0x040007C9 RID: 1993
		private Label label_1;

		// Token: 0x040007CA RID: 1994
		private Button button_0;

		// Token: 0x040007CB RID: 1995
		private PictureBox pictureBox_0;

		// Token: 0x040007CC RID: 1996
		private LinkLabel linkLabel_0;

		// Token: 0x040007CD RID: 1997
		private Label label_2;

		// Token: 0x040007CE RID: 1998
		private PictureBox pictureBox_1;

		// Token: 0x040007CF RID: 1999
		private Label label_3;

		// Token: 0x040007D0 RID: 2000
		private Label label_4;

		// Token: 0x040007D1 RID: 2001
		private Label label_5;

		// Token: 0x040007D2 RID: 2002
		private LinkLabel linkLabel_1;

		// Token: 0x040007D3 RID: 2003
		private LinkLabel linkLabel_2;

		// Token: 0x040007D4 RID: 2004
		private Label label_6;
	}
}
