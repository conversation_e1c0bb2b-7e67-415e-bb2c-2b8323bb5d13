﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns26;

namespace ns28
{
	// Token: 0x02000414 RID: 1044
	[DesignerCategory("Code")]
	internal sealed class Control16 : Control
	{
		// Token: 0x06002847 RID: 10311 RVA: 0x0000FB0E File Offset: 0x0000DD0E
		protected void OnResize(EventArgs e)
		{
			base.Size = new Size(Convert.ToInt32(112f * this.float_0), Convert.ToInt32(32f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x06002848 RID: 10312 RVA: 0x0000FB44 File Offset: 0x0000DD44
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002849 RID: 10313 RVA: 0x0000FB67 File Offset: 0x0000DD67
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.toolTip_0 != null)
				{
					this.toolTip_0.Dispose();
				}
				if (this.pictureBox_0 != null)
				{
					this.pictureBox_0.Dispose();
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600284A RID: 10314 RVA: 0x0010DFFC File Offset: 0x0010C1FC
		private void Control16_Click(object sender, EventArgs e)
		{
			try
			{
				Process.Start(Class521.smethod_0(119582));
			}
			catch
			{
			}
		}

		// Token: 0x0600284B RID: 10315 RVA: 0x0010E030 File Offset: 0x0010C230
		public Control16()
		{
			base.SuspendLayout();
			this.label_0.FlatStyle = FlatStyle.System;
			this.label_0.Location = new Point(0, 10);
			this.label_0.Size = new Size(62, 24);
			this.label_0.Text = Class521.smethod_0(119808);
			this.pictureBox_0.Image = Class541.smethod_0(Class521.smethod_0(119825));
			this.pictureBox_0.Location = new Point(72, 0);
			this.pictureBox_0.Size = new Size(32, 32);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.label_0.Click += this.Control16_Click;
			this.pictureBox_0.Click += this.Control16_Click;
			base.Click += this.Control16_Click;
			this.Cursor = Cursors.Hand;
			base.TabStop = false;
			base.Size = new Size(112, 32);
			base.Controls.AddRange(new Control[]
			{
				this.pictureBox_0,
				this.label_0
			});
			this.toolTip_0.SetToolTip(this, Class521.smethod_0(119834));
			this.toolTip_0.SetToolTip(this.label_0, Class521.smethod_0(119834));
			this.toolTip_0.SetToolTip(this.pictureBox_0, Class521.smethod_0(119834));
			base.ResumeLayout(true);
		}

		// Token: 0x04001418 RID: 5144
		private const string string_0 = "Powered by SmartAssembly";

		// Token: 0x04001419 RID: 5145
		private const string string_1 = "http://www.red-gate.com/products/dotnet-development/smartassembly/?utm_source=smartassemblyui&utm_medium=supportlink&utm_content=aerdialogbox&utm_campaign=smartassembly";

		// Token: 0x0400141A RID: 5146
		private Label label_0 = new Label();

		// Token: 0x0400141B RID: 5147
		private PictureBox pictureBox_0 = new PictureBox();

		// Token: 0x0400141C RID: 5148
		private ToolTip toolTip_0 = new ToolTip();

		// Token: 0x0400141D RID: 5149
		private float float_0 = 1f;

		// Token: 0x0400141E RID: 5150
		private float float_1 = 1f;
	}
}
