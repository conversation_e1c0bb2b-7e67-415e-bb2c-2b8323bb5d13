﻿using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using ns18;
using ns26;

namespace ns23
{
	// Token: 0x0200040B RID: 1035
	internal sealed class Class547
	{
		// Token: 0x06002814 RID: 10260
		[DllImport("shell32")]
		private static extern int ExtractIconEx(string string_0, int int_6, out IntPtr intptr_0, out IntPtr intptr_1, int int_7);

		// Token: 0x06002815 RID: 10261
		[DllImport("user32", CharSet = CharSet.Unicode)]
		private static extern int DrawText(IntPtr intptr_0, string string_0, int int_6, ref Class547.Struct29 struct29_0, int int_7);

		// Token: 0x06002816 RID: 10262
		[DllImport("gdi32.dll")]
		private static extern IntPtr SelectObject(IntPtr intptr_0, IntPtr intptr_1);

		// Token: 0x06002817 RID: 10263
		[DllImport("kernel32.Dll")]
		private static extern short GetVersionEx(ref Class547.Struct30 struct30_1);

		// Token: 0x06002818 RID: 10264
		[DllImport("user32.dll")]
		private static extern int GetSystemMetrics(int int_6);

		// Token: 0x06002819 RID: 10265
		[DllImport("kernel32.dll")]
		private static extern void GetSystemInfo(ref Class547.Struct31 struct31_0);

		// Token: 0x170006E0 RID: 1760
		// (get) Token: 0x0600281A RID: 10266 RVA: 0x0010D6E8 File Offset: 0x0010B8E8
		private static Class547.Struct30 VersionInfo
		{
			get
			{
				if (!Class547.bool_0)
				{
					Class547.struct30_0 = default(Class547.Struct30);
					try
					{
						Class547.struct30_0.int_0 = Marshal.SizeOf(typeof(Class547.Struct30));
						Class547.GetVersionEx(ref Class547.struct30_0);
						Class547.bool_0 = true;
					}
					catch
					{
					}
				}
				return Class547.struct30_0;
			}
		}

		// Token: 0x170006E1 RID: 1761
		// (get) Token: 0x0600281B RID: 10267 RVA: 0x0010D74C File Offset: 0x0010B94C
		internal static bool IsX64
		{
			get
			{
				bool result;
				try
				{
					Class547.Struct31 @struct = default(Class547.Struct31);
					Class547.GetSystemInfo(ref @struct);
					result = (@struct.ushort_0 == 9);
				}
				catch
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170006E2 RID: 1762
		// (get) Token: 0x0600281C RID: 10268 RVA: 0x0010D78C File Offset: 0x0010B98C
		internal static bool IsServerR2
		{
			get
			{
				bool result;
				try
				{
					result = (Class547.GetSystemMetrics(89) != 0);
				}
				catch
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170006E3 RID: 1763
		// (get) Token: 0x0600281D RID: 10269 RVA: 0x0000F8D6 File Offset: 0x0000DAD6
		internal static bool IsWorkstation
		{
			get
			{
				return Class547.VersionInfo.byte_0 == 1;
			}
		}

		// Token: 0x170006E4 RID: 1764
		// (get) Token: 0x0600281E RID: 10270 RVA: 0x0000F8E5 File Offset: 0x0000DAE5
		internal static string ServicePack
		{
			get
			{
				return Class547.VersionInfo.string_0;
			}
		}

		// Token: 0x0600281F RID: 10271 RVA: 0x0010D7BC File Offset: 0x0010B9BC
		public static Icon smethod_0()
		{
			Icon result;
			try
			{
				result = Class547.smethod_1();
			}
			catch (Exception)
			{
				result = Class541.smethod_1(Class521.smethod_0(119521));
			}
			return result;
		}

		// Token: 0x06002820 RID: 10272 RVA: 0x0010D7F8 File Offset: 0x0010B9F8
		private static Icon smethod_1()
		{
			IntPtr zero = IntPtr.Zero;
			IntPtr zero2 = IntPtr.Zero;
			if (Class547.ExtractIconEx(Application.ExecutablePath, -1, out zero2, out zero2, 1) > 0)
			{
				Class547.ExtractIconEx(Application.ExecutablePath, 0, out zero, out zero2, 1);
				if (zero != IntPtr.Zero)
				{
					return Icon.FromHandle(zero);
				}
			}
			return null;
		}

		// Token: 0x06002821 RID: 10273 RVA: 0x0010D84C File Offset: 0x0010BA4C
		internal static int smethod_2(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			try
			{
				return Class547.smethod_4(graphics_0, string_0, font_0, int_6);
			}
			catch (Exception)
			{
				try
				{
					return Convert.ToInt32((double)Class547.smethod_3(graphics_0, string_0, font_0, int_6) * 1.1);
				}
				catch (Exception)
				{
				}
			}
			return 0;
		}

		// Token: 0x06002822 RID: 10274 RVA: 0x0010D8A8 File Offset: 0x0010BAA8
		private static int smethod_3(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			return Size.Ceiling(graphics_0.MeasureString(string_0, font_0, int_6)).Height;
		}

		// Token: 0x06002823 RID: 10275 RVA: 0x0010D8CC File Offset: 0x0010BACC
		private static int smethod_4(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			Class547.Struct29 @struct = new Class547.Struct29(new Rectangle(0, 0, int_6, 10000));
			IntPtr hdc = graphics_0.GetHdc();
			IntPtr intptr_ = font_0.ToHfont();
			IntPtr intptr_2 = Class547.SelectObject(hdc, intptr_);
			Class547.DrawText(hdc, string_0, -1, ref @struct, 3088);
			Class547.SelectObject(hdc, intptr_2);
			graphics_0.ReleaseHdc(hdc);
			return @struct.int_3 - @struct.int_1;
		}

		// Token: 0x040013E2 RID: 5090
		private const int int_0 = 16;

		// Token: 0x040013E3 RID: 5091
		private const int int_1 = 1024;

		// Token: 0x040013E4 RID: 5092
		private const int int_2 = 2048;

		// Token: 0x040013E5 RID: 5093
		private const int int_3 = 1;

		// Token: 0x040013E6 RID: 5094
		private const int int_4 = 89;

		// Token: 0x040013E7 RID: 5095
		private const int int_5 = 9;

		// Token: 0x040013E8 RID: 5096
		private static bool bool_0;

		// Token: 0x040013E9 RID: 5097
		private static Class547.Struct30 struct30_0;

		// Token: 0x0200040C RID: 1036
		private struct Struct29
		{
			// Token: 0x06002825 RID: 10277 RVA: 0x0000F8F1 File Offset: 0x0000DAF1
			public Struct29(Rectangle rectangle_0)
			{
				this.int_0 = rectangle_0.Left;
				this.int_1 = rectangle_0.Top;
				this.int_3 = rectangle_0.Bottom;
				this.int_2 = rectangle_0.Right;
			}

			// Token: 0x040013EA RID: 5098
			public int int_0;

			// Token: 0x040013EB RID: 5099
			public int int_1;

			// Token: 0x040013EC RID: 5100
			public int int_2;

			// Token: 0x040013ED RID: 5101
			public int int_3;
		}

		// Token: 0x0200040D RID: 1037
		private struct Struct30
		{
			// Token: 0x040013EE RID: 5102
			public int int_0;

			// Token: 0x040013EF RID: 5103
			public uint uint_0;

			// Token: 0x040013F0 RID: 5104
			public uint uint_1;

			// Token: 0x040013F1 RID: 5105
			public uint uint_2;

			// Token: 0x040013F2 RID: 5106
			public uint uint_3;

			// Token: 0x040013F3 RID: 5107
			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
			public string string_0;

			// Token: 0x040013F4 RID: 5108
			public ushort ushort_0;

			// Token: 0x040013F5 RID: 5109
			public ushort ushort_1;

			// Token: 0x040013F6 RID: 5110
			public ushort ushort_2;

			// Token: 0x040013F7 RID: 5111
			public byte byte_0;

			// Token: 0x040013F8 RID: 5112
			private byte byte_1;
		}

		// Token: 0x0200040E RID: 1038
		public struct Struct31
		{
			// Token: 0x040013F9 RID: 5113
			public ushort ushort_0;

			// Token: 0x040013FA RID: 5114
			private ushort ushort_1;

			// Token: 0x040013FB RID: 5115
			public uint uint_0;

			// Token: 0x040013FC RID: 5116
			public IntPtr intptr_0;

			// Token: 0x040013FD RID: 5117
			public IntPtr intptr_1;

			// Token: 0x040013FE RID: 5118
			public IntPtr intptr_2;

			// Token: 0x040013FF RID: 5119
			public uint uint_1;

			// Token: 0x04001400 RID: 5120
			public uint uint_2;

			// Token: 0x04001401 RID: 5121
			public uint uint_3;

			// Token: 0x04001402 RID: 5122
			public ushort ushort_2;

			// Token: 0x04001403 RID: 5123
			public ushort ushort_3;
		}
	}
}
