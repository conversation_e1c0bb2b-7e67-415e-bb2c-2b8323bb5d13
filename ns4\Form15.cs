﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns18;
using ns26;
using TEx;

namespace ns4
{
	// Token: 0x0200019A RID: 410
	internal sealed partial class Form15 : Form
	{
		// Token: 0x06000FC7 RID: 4039 RVA: 0x00006C71 File Offset: 0x00004E71
		public Form15()
		{
			this.method_4();
		}

		// Token: 0x06000FC8 RID: 4040 RVA: 0x00065AB8 File Offset: 0x00063CB8
		private void Form15_Load(object sender, EventArgs e)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					(obj as ButtonX).AutoCheckOnClick = true;
				}
			}
			this.buttonX_0.Click += this.buttonX_0_Click;
			this.buttonX_1.Click += this.buttonX_1_Click;
			this.buttonX_12.Click += this.buttonX_12_Click;
			this.buttonX_13.Click += this.buttonX_13_Click;
			this.buttonX_3.Click += this.buttonX_3_Click;
			this.buttonX_4.Click += this.buttonX_4_Click;
			this.buttonX_20.Click += this.buttonX_20_Click;
			this.buttonX_22.Click += this.buttonX_22_Click;
			this.buttonX_23.Click += this.buttonX_23_Click;
			this.buttonX_21.Click += this.buttonX_21_Click;
			this.buttonX_25.Click += this.buttonX_25_Click;
			this.buttonX_28.Click += this.buttonX_28_Click;
			this.buttonX_26.Click += this.buttonX_26_Click;
			this.buttonX_27.Click += this.buttonX_27_Click;
			this.buttonX_24.Click += this.buttonX_24_Click;
			this.buttonX_16.Click += this.buttonX_16_Click;
			this.buttonX_17.Click += this.buttonX_17_Click;
			this.buttonX_18.Click += this.buttonX_18_Click;
			this.buttonX_19.Click += this.buttonX_19_Click;
			this.buttonX_31.Click += this.buttonX_31_Click;
			this.buttonX_30.Click += this.buttonX_30_Click;
			this.buttonX_29.Click += this.buttonX_29_Click;
			this.buttonX_7.Click += this.buttonX_7_Click;
			this.buttonX_6.Click += this.buttonX_6_Click;
			this.buttonX_2.Click += this.buttonX_2_Click;
			this.buttonX_5.Click += this.buttonX_5_Click;
			this.buttonX_10.Click += this.buttonX_10_Click;
			this.buttonX_11.Click += this.buttonX_11_Click;
			this.buttonX_8.Click += this.buttonX_8_Click;
			this.buttonX_9.Click += this.buttonX_9_Click;
			Base.UI.DrawModeSetOff += this.method_3;
			Base.UI.DrawToolWnd = this;
			base.FormClosed += this.Form15_FormClosed;
			base.Disposed += this.Form15_Disposed;
			base.Focus();
		}

		// Token: 0x06000FC9 RID: 4041 RVA: 0x00056CD8 File Offset: 0x00054ED8
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (msg.WParam.ToInt32() == 27)
			{
				Base.UI.smethod_156();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000FCA RID: 4042 RVA: 0x00065E10 File Offset: 0x00064010
		private void buttonX_14_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Off);
		}

		// Token: 0x06000FCB RID: 4043 RVA: 0x00065E30 File Offset: 0x00064030
		private void buttonX_15_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.ArwLine);
		}

		// Token: 0x06000FCC RID: 4044 RVA: 0x00065E50 File Offset: 0x00064050
		private void buttonX_0_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Line);
		}

		// Token: 0x06000FCD RID: 4045 RVA: 0x00065E70 File Offset: 0x00064070
		private void buttonX_1_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineD);
		}

		// Token: 0x06000FCE RID: 4046 RVA: 0x00065E90 File Offset: 0x00064090
		private void buttonX_12_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineV);
		}

		// Token: 0x06000FCF RID: 4047 RVA: 0x00065EB0 File Offset: 0x000640B0
		private void buttonX_13_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineH);
		}

		// Token: 0x06000FD0 RID: 4048 RVA: 0x00065ED0 File Offset: 0x000640D0
		private void buttonX_3_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineP);
		}

		// Token: 0x06000FD1 RID: 4049 RVA: 0x00065EF0 File Offset: 0x000640F0
		private void buttonX_4_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Square);
		}

		// Token: 0x06000FD2 RID: 4050 RVA: 0x00065F10 File Offset: 0x00064110
		private void buttonX_24_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.PeriodLines);
		}

		// Token: 0x06000FD3 RID: 4051 RVA: 0x00065F30 File Offset: 0x00064130
		private void buttonX_25_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.FibonacciLines);
		}

		// Token: 0x06000FD4 RID: 4052 RVA: 0x00065F50 File Offset: 0x00064150
		private void buttonX_28_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.FibonacciExtLines);
		}

		// Token: 0x06000FD5 RID: 4053 RVA: 0x00065F70 File Offset: 0x00064170
		private void buttonX_26_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GannLines);
		}

		// Token: 0x06000FD6 RID: 4054 RVA: 0x00065F90 File Offset: 0x00064190
		private void buttonX_27_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.MeasureObj);
		}

		// Token: 0x06000FD7 RID: 4055 RVA: 0x00065FB0 File Offset: 0x000641B0
		private void buttonX_29_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineHExt);
		}

		// Token: 0x06000FD8 RID: 4056 RVA: 0x00065FD0 File Offset: 0x000641D0
		private void buttonX_30_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineDH);
		}

		// Token: 0x06000FD9 RID: 4057 RVA: 0x00065FF0 File Offset: 0x000641F0
		private void buttonX_31_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineDExt);
		}

		// Token: 0x06000FDA RID: 4058 RVA: 0x00066010 File Offset: 0x00064210
		private void buttonX_21_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GannFan);
		}

		// Token: 0x06000FDB RID: 4059 RVA: 0x00066030 File Offset: 0x00064230
		private void buttonX_23_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Degree45Dn);
		}

		// Token: 0x06000FDC RID: 4060 RVA: 0x00066050 File Offset: 0x00064250
		private void buttonX_22_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Degree45Up);
		}

		// Token: 0x06000FDD RID: 4061 RVA: 0x00066070 File Offset: 0x00064270
		private void buttonX_20_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Ellipse);
		}

		// Token: 0x06000FDE RID: 4062 RVA: 0x00066090 File Offset: 0x00064290
		private void buttonX_19_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Range);
		}

		// Token: 0x06000FDF RID: 4063 RVA: 0x000660B0 File Offset: 0x000642B0
		private void buttonX_18_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Ratio);
		}

		// Token: 0x06000FE0 RID: 4064 RVA: 0x000660D0 File Offset: 0x000642D0
		private void buttonX_17_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GoldenRatio);
		}

		// Token: 0x06000FE1 RID: 4065 RVA: 0x000660F0 File Offset: 0x000642F0
		private void buttonX_16_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.TrendSpeed);
		}

		// Token: 0x06000FE2 RID: 4066 RVA: 0x00066110 File Offset: 0x00064310
		private void buttonX_2_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwUp);
		}

		// Token: 0x06000FE3 RID: 4067 RVA: 0x00066130 File Offset: 0x00064330
		private void buttonX_7_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwLUp);
		}

		// Token: 0x06000FE4 RID: 4068 RVA: 0x00066150 File Offset: 0x00064350
		private void buttonX_6_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwRUp);
		}

		// Token: 0x06000FE5 RID: 4069 RVA: 0x00066170 File Offset: 0x00064370
		private void buttonX_5_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwDn);
		}

		// Token: 0x06000FE6 RID: 4070 RVA: 0x00066190 File Offset: 0x00064390
		private void buttonX_10_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwLDn);
		}

		// Token: 0x06000FE7 RID: 4071 RVA: 0x000661B0 File Offset: 0x000643B0
		private void buttonX_11_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwRDn);
		}

		// Token: 0x06000FE8 RID: 4072 RVA: 0x000661D0 File Offset: 0x000643D0
		private void buttonX_8_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Text);
		}

		// Token: 0x06000FE9 RID: 4073 RVA: 0x000661F0 File Offset: 0x000643F0
		private void buttonX_9_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.EraseAllDrawObj);
		}

		// Token: 0x06000FEA RID: 4074 RVA: 0x00066210 File Offset: 0x00064410
		private void method_0(ButtonX buttonX_32, TEx.DrawMode drawMode_0)
		{
			Base.UI.DrawingObj1stPt = null;
			if (buttonX_32.Checked)
			{
				this.method_1(buttonX_32);
				Base.UI.DrawMode = drawMode_0;
			}
			else
			{
				Base.UI.DrawMode = TEx.DrawMode.Off;
			}
		}

		// Token: 0x06000FEB RID: 4075 RVA: 0x00056F4C File Offset: 0x0005514C
		private void method_1(ButtonX buttonX_32)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX && obj != buttonX_32)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000FEC RID: 4076 RVA: 0x00056FC0 File Offset: 0x000551C0
		private void method_2()
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000FED RID: 4077 RVA: 0x00006C81 File Offset: 0x00004E81
		private void Form15_FormClosed(object sender, FormClosedEventArgs e)
		{
			Base.UI.DrawMode = TEx.DrawMode.Off;
			Base.UI.DrawToolWnd = null;
		}

		// Token: 0x06000FEE RID: 4078 RVA: 0x00006C92 File Offset: 0x00004E92
		private void Form15_Disposed(object sender, EventArgs e)
		{
			Base.UI.DrawModeSetOff -= this.method_3;
		}

		// Token: 0x06000FEF RID: 4079 RVA: 0x00006CA7 File Offset: 0x00004EA7
		private void method_3(object sender, EventArgs e)
		{
			this.method_2();
		}

		// Token: 0x06000FF0 RID: 4080 RVA: 0x00006CB1 File Offset: 0x00004EB1
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000FF1 RID: 4081 RVA: 0x0006624C File Offset: 0x0006444C
		private void method_4()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(Form15));
			this.buttonX_0 = new ButtonX();
			this.buttonX_1 = new ButtonX();
			this.buttonX_2 = new ButtonX();
			this.buttonX_3 = new ButtonX();
			this.buttonX_4 = new ButtonX();
			this.buttonX_5 = new ButtonX();
			this.buttonX_6 = new ButtonX();
			this.buttonX_7 = new ButtonX();
			this.buttonX_8 = new ButtonX();
			this.buttonX_9 = new ButtonX();
			this.buttonX_10 = new ButtonX();
			this.buttonX_11 = new ButtonX();
			this.buttonX_12 = new ButtonX();
			this.buttonX_13 = new ButtonX();
			this.buttonX_14 = new ButtonX();
			this.buttonX_15 = new ButtonX();
			this.buttonX_16 = new ButtonX();
			this.buttonX_17 = new ButtonX();
			this.buttonX_18 = new ButtonX();
			this.buttonX_19 = new ButtonX();
			this.buttonX_20 = new ButtonX();
			this.buttonX_21 = new ButtonX();
			this.buttonX_22 = new ButtonX();
			this.buttonX_23 = new ButtonX();
			this.buttonX_24 = new ButtonX();
			this.buttonX_25 = new ButtonX();
			this.buttonX_26 = new ButtonX();
			this.buttonX_27 = new ButtonX();
			this.buttonX_28 = new ButtonX();
			this.buttonX_29 = new ButtonX();
			this.buttonX_30 = new ButtonX();
			this.buttonX_31 = new ButtonX();
			base.SuspendLayout();
			this.buttonX_0.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_0.ColorTable = eButtonColor.Flat;
			this.buttonX_0.Image = Class375.Line;
			this.buttonX_0.Location = new Point(4, 42);
			this.buttonX_0.Name = Class521.smethod_0(28232);
			this.buttonX_0.ShowSubItems = false;
			this.buttonX_0.Size = new Size(27, 28);
			this.buttonX_0.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_0.TabIndex = 4;
			this.buttonX_0.Tooltip = Class521.smethod_0(27647);
			this.buttonX_1.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_1.ColorTable = eButtonColor.Flat;
			this.buttonX_1.Image = Class375.LineD;
			this.buttonX_1.Location = new Point(73, 43);
			this.buttonX_1.Name = Class521.smethod_0(28245);
			this.buttonX_1.ShowSubItems = false;
			this.buttonX_1.Size = new Size(27, 28);
			this.buttonX_1.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_1.TabIndex = 6;
			this.buttonX_1.Tooltip = Class521.smethod_0(27656);
			this.buttonX_2.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_2.ColorTable = eButtonColor.Flat;
			this.buttonX_2.Image = Class375.RedArrow_Up;
			this.buttonX_2.Location = new Point(6, 289);
			this.buttonX_2.Name = Class521.smethod_0(28262);
			this.buttonX_2.ShowSubItems = false;
			this.buttonX_2.Size = new Size(27, 28);
			this.buttonX_2.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_2.TabIndex = 25;
			this.buttonX_2.Tooltip = Class521.smethod_0(28283);
			this.buttonX_3.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_3.ColorTable = eButtonColor.Flat;
			this.buttonX_3.Image = Class375.ParalLine;
			this.buttonX_3.Location = new Point(40, 115);
			this.buttonX_3.Name = Class521.smethod_0(28296);
			this.buttonX_3.ShowSubItems = false;
			this.buttonX_3.Size = new Size(27, 28);
			this.buttonX_3.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_3.TabIndex = 11;
			this.buttonX_3.Tooltip = Class521.smethod_0(28317);
			this.buttonX_4.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_4.ColorTable = eButtonColor.Flat;
			this.buttonX_4.Image = Class375.Square;
			this.buttonX_4.Location = new Point(73, 7);
			this.buttonX_4.Name = Class521.smethod_0(28330);
			this.buttonX_4.ShowSubItems = false;
			this.buttonX_4.Size = new Size(27, 28);
			this.buttonX_4.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_4.TabIndex = 3;
			this.buttonX_4.Tooltip = Class521.smethod_0(28347);
			this.buttonX_5.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_5.ColorTable = eButtonColor.Flat;
			this.buttonX_5.Image = Class375.GreenArrow_Down;
			this.buttonX_5.Location = new Point(6, 323);
			this.buttonX_5.Name = Class521.smethod_0(28356);
			this.buttonX_5.ShowSubItems = false;
			this.buttonX_5.Size = new Size(27, 28);
			this.buttonX_5.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_5.TabIndex = 28;
			this.buttonX_5.Tooltip = Class521.smethod_0(27600);
			this.buttonX_6.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_6.ColorTable = eButtonColor.Flat;
			this.buttonX_6.Image = Class375.RedArrow_RUp;
			this.buttonX_6.Location = new Point(40, 289);
			this.buttonX_6.Name = Class521.smethod_0(28377);
			this.buttonX_6.ShowSubItems = false;
			this.buttonX_6.Size = new Size(27, 28);
			this.buttonX_6.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_6.TabIndex = 26;
			this.buttonX_6.Tooltip = Class521.smethod_0(28398);
			this.buttonX_7.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_7.ColorTable = eButtonColor.Flat;
			this.buttonX_7.Image = Class375.RedArrow_LUp;
			this.buttonX_7.Location = new Point(73, 289);
			this.buttonX_7.Name = Class521.smethod_0(28415);
			this.buttonX_7.ShowSubItems = false;
			this.buttonX_7.Size = new Size(27, 28);
			this.buttonX_7.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_7.TabIndex = 27;
			this.buttonX_7.Tooltip = Class521.smethod_0(28436);
			this.buttonX_8.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_8.ColorTable = eButtonColor.Flat;
			this.buttonX_8.Image = Class375.Text;
			this.buttonX_8.Location = new Point(4, 7);
			this.buttonX_8.Name = Class521.smethod_0(28453);
			this.buttonX_8.ShowSubItems = false;
			this.buttonX_8.Size = new Size(27, 28);
			this.buttonX_8.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_8.TabIndex = 1;
			this.buttonX_8.Tooltip = Class521.smethod_0(28466);
			this.buttonX_9.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_9.ColorTable = eButtonColor.Flat;
			this.buttonX_9.Image = Class375.DelDraw;
			this.buttonX_9.Location = new Point(73, 357);
			this.buttonX_9.Name = Class521.smethod_0(28475);
			this.buttonX_9.ShowSubItems = false;
			this.buttonX_9.Size = new Size(27, 28);
			this.buttonX_9.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_9.TabIndex = 31;
			this.buttonX_9.Tooltip = Class521.smethod_0(28492);
			this.buttonX_10.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_10.ColorTable = eButtonColor.Flat;
			this.buttonX_10.Image = Class375.GreenArrow_LDown;
			this.buttonX_10.Location = new Point(73, 323);
			this.buttonX_10.Name = Class521.smethod_0(28509);
			this.buttonX_10.ShowSubItems = false;
			this.buttonX_10.Size = new Size(27, 28);
			this.buttonX_10.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_10.TabIndex = 30;
			this.buttonX_10.Tooltip = Class521.smethod_0(27613);
			this.buttonX_11.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_11.ColorTable = eButtonColor.Flat;
			this.buttonX_11.Image = Class375.GreenArrow_RDown;
			this.buttonX_11.Location = new Point(40, 323);
			this.buttonX_11.Name = Class521.smethod_0(28530);
			this.buttonX_11.ShowSubItems = false;
			this.buttonX_11.Size = new Size(27, 28);
			this.buttonX_11.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_11.TabIndex = 29;
			this.buttonX_11.Tooltip = Class521.smethod_0(27630);
			this.buttonX_12.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_12.ColorTable = eButtonColor.Flat;
			this.buttonX_12.Image = Class375.LineV;
			this.buttonX_12.Location = new Point(6, 115);
			this.buttonX_12.Name = Class521.smethod_0(28551);
			this.buttonX_12.ShowSubItems = false;
			this.buttonX_12.Size = new Size(27, 28);
			this.buttonX_12.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_12.TabIndex = 10;
			this.buttonX_12.Tooltip = Class521.smethod_0(28568);
			this.buttonX_13.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_13.ColorTable = eButtonColor.Flat;
			this.buttonX_13.Image = Class375.LineH;
			this.buttonX_13.Location = new Point(6, 78);
			this.buttonX_13.Name = Class521.smethod_0(28581);
			this.buttonX_13.ShowSubItems = false;
			this.buttonX_13.Size = new Size(27, 28);
			this.buttonX_13.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_13.TabIndex = 7;
			this.buttonX_13.Tooltip = Class521.smethod_0(28598);
			this.buttonX_14.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_14.ColorTable = eButtonColor.Flat;
			this.buttonX_14.Image = (Image)componentResourceManager.GetObject(Class521.smethod_0(28611));
			this.buttonX_14.Location = new Point(6, 357);
			this.buttonX_14.Name = Class521.smethod_0(28636);
			this.buttonX_14.ShowSubItems = false;
			this.buttonX_14.Size = new Size(27, 28);
			this.buttonX_14.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_14.TabIndex = 0;
			this.buttonX_14.Tooltip = Class521.smethod_0(28653);
			this.buttonX_14.Click += this.buttonX_14_Click;
			this.buttonX_15.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_15.ColorTable = eButtonColor.Flat;
			this.buttonX_15.Image = Class375.Arrow;
			this.buttonX_15.Location = new Point(40, 7);
			this.buttonX_15.Name = Class521.smethod_0(28678);
			this.buttonX_15.ShowSubItems = false;
			this.buttonX_15.Size = new Size(27, 28);
			this.buttonX_15.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_15.TabIndex = 2;
			this.buttonX_15.Tooltip = Class521.smethod_0(27587);
			this.buttonX_15.Click += this.buttonX_15_Click;
			this.buttonX_16.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_16.ColorTable = eButtonColor.Flat;
			this.buttonX_16.Image = Class375.TrendSpeed;
			this.buttonX_16.Location = new Point(73, 151);
			this.buttonX_16.Name = Class521.smethod_0(28699);
			this.buttonX_16.ShowSubItems = false;
			this.buttonX_16.Size = new Size(27, 28);
			this.buttonX_16.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_16.TabIndex = 15;
			this.buttonX_16.Tooltip = Class521.smethod_0(5626);
			this.buttonX_17.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_17.ColorTable = eButtonColor.Flat;
			this.buttonX_17.Image = Class375.GoldenRatio;
			this.buttonX_17.Location = new Point(6, 254);
			this.buttonX_17.Name = Class521.smethod_0(28720);
			this.buttonX_17.ShowSubItems = false;
			this.buttonX_17.Size = new Size(27, 28);
			this.buttonX_17.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_17.TabIndex = 22;
			this.buttonX_17.Tooltip = Class521.smethod_0(5225);
			this.buttonX_18.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_18.ColorTable = eButtonColor.Flat;
			this.buttonX_18.Image = Class375.Ratio;
			this.buttonX_18.Location = new Point(40, 254);
			this.buttonX_18.Name = Class521.smethod_0(28745);
			this.buttonX_18.ShowSubItems = false;
			this.buttonX_18.Size = new Size(27, 28);
			this.buttonX_18.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_18.TabIndex = 23;
			this.buttonX_18.Tooltip = Class521.smethod_0(5566);
			this.buttonX_19.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_19.ColorTable = eButtonColor.Flat;
			this.buttonX_19.Image = Class375.Range;
			this.buttonX_19.Location = new Point(73, 254);
			this.buttonX_19.Name = Class521.smethod_0(28762);
			this.buttonX_19.ShowSubItems = false;
			this.buttonX_19.Size = new Size(27, 28);
			this.buttonX_19.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_19.TabIndex = 24;
			this.buttonX_19.Tooltip = Class521.smethod_0(5324);
			this.buttonX_20.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_20.ColorTable = eButtonColor.Flat;
			this.buttonX_20.Image = Class375.Ellipse;
			this.buttonX_20.Location = new Point(73, 115);
			this.buttonX_20.Name = Class521.smethod_0(28779);
			this.buttonX_20.ShowSubItems = false;
			this.buttonX_20.Size = new Size(27, 28);
			this.buttonX_20.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_20.TabIndex = 12;
			this.buttonX_20.Tooltip = Class521.smethod_0(5094);
			this.buttonX_21.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_21.ColorTable = eButtonColor.Flat;
			this.buttonX_21.Image = Class375.GannFan;
			this.buttonX_21.Location = new Point(73, 220);
			this.buttonX_21.Name = Class521.smethod_0(28796);
			this.buttonX_21.ShowSubItems = false;
			this.buttonX_21.Size = new Size(27, 28);
			this.buttonX_21.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_21.TabIndex = 21;
			this.buttonX_21.Tooltip = Class521.smethod_0(5179);
			this.buttonX_22.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_22.ColorTable = eButtonColor.Flat;
			this.buttonX_22.Image = Class375._45DgreeUp;
			this.buttonX_22.Location = new Point(6, 151);
			this.buttonX_22.Name = Class521.smethod_0(28813);
			this.buttonX_22.ShowSubItems = false;
			this.buttonX_22.Size = new Size(27, 28);
			this.buttonX_22.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_22.TabIndex = 13;
			this.buttonX_22.Tooltip = Class521.smethod_0(5081);
			this.buttonX_23.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_23.ColorTable = eButtonColor.Flat;
			this.buttonX_23.Image = Class375._45DgreeDn;
			this.buttonX_23.Location = new Point(39, 151);
			this.buttonX_23.Name = Class521.smethod_0(28834);
			this.buttonX_23.ShowSubItems = false;
			this.buttonX_23.Size = new Size(27, 28);
			this.buttonX_23.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_23.TabIndex = 14;
			this.buttonX_23.Tooltip = Class521.smethod_0(5068);
			this.buttonX_24.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_24.ColorTable = eButtonColor.Flat;
			this.buttonX_24.Image = Class375.PeriodLines;
			this.buttonX_24.Location = new Point(6, 185);
			this.buttonX_24.Name = Class521.smethod_0(28855);
			this.buttonX_24.ShowSubItems = false;
			this.buttonX_24.Size = new Size(27, 28);
			this.buttonX_24.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_24.TabIndex = 16;
			this.buttonX_24.Tooltip = Class521.smethod_0(5311);
			this.buttonX_25.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_25.ColorTable = eButtonColor.Flat;
			this.buttonX_25.Image = Class375.FbLines;
			this.buttonX_25.Location = new Point(39, 185);
			this.buttonX_25.Name = Class521.smethod_0(28880);
			this.buttonX_25.ShowSubItems = false;
			this.buttonX_25.Size = new Size(27, 28);
			this.buttonX_25.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_25.TabIndex = 17;
			this.buttonX_25.Tooltip = Class521.smethod_0(5146);
			this.buttonX_26.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_26.ColorTable = eButtonColor.Flat;
			this.buttonX_26.Image = Class375.GannLines;
			this.buttonX_26.Location = new Point(6, 220);
			this.buttonX_26.Name = Class521.smethod_0(28897);
			this.buttonX_26.ShowSubItems = false;
			this.buttonX_26.Size = new Size(27, 28);
			this.buttonX_26.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_26.TabIndex = 19;
			this.buttonX_26.Tooltip = Class521.smethod_0(5200);
			this.buttonX_27.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_27.ColorTable = eButtonColor.Flat;
			this.buttonX_27.Image = Class375.MeasureObj;
			this.buttonX_27.Location = new Point(39, 220);
			this.buttonX_27.Name = Class521.smethod_0(28918);
			this.buttonX_27.ShowSubItems = false;
			this.buttonX_27.Size = new Size(27, 28);
			this.buttonX_27.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_27.TabIndex = 20;
			this.buttonX_27.Tooltip = Class521.smethod_0(5289);
			this.buttonX_28.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_28.ColorTable = eButtonColor.Flat;
			this.buttonX_28.Image = Class375.FbLines_Ext;
			this.buttonX_28.Location = new Point(73, 185);
			this.buttonX_28.Name = Class521.smethod_0(28939);
			this.buttonX_28.ShowSubItems = false;
			this.buttonX_28.Size = new Size(27, 28);
			this.buttonX_28.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_28.TabIndex = 18;
			this.buttonX_28.Tooltip = Class521.smethod_0(5103);
			this.buttonX_29.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_29.ColorTable = eButtonColor.Flat;
			this.buttonX_29.Image = Class375.LineHExt;
			this.buttonX_29.Location = new Point(73, 78);
			this.buttonX_29.Name = Class521.smethod_0(28960);
			this.buttonX_29.ShowSubItems = false;
			this.buttonX_29.Size = new Size(27, 28);
			this.buttonX_29.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_29.TabIndex = 9;
			this.buttonX_29.Tooltip = Class521.smethod_0(5272);
			this.buttonX_30.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_30.ColorTable = eButtonColor.Flat;
			this.buttonX_30.Image = Class375.LineDH;
			this.buttonX_30.Location = new Point(40, 78);
			this.buttonX_30.Name = Class521.smethod_0(28981);
			this.buttonX_30.ShowSubItems = false;
			this.buttonX_30.Size = new Size(27, 28);
			this.buttonX_30.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_30.TabIndex = 8;
			this.buttonX_30.Tooltip = Class521.smethod_0(5255);
			this.buttonX_31.AccessibleRole = AccessibleRole.PushButton;
			this.buttonX_31.ColorTable = eButtonColor.Flat;
			this.buttonX_31.Image = Class375.LineDExt;
			this.buttonX_31.Location = new Point(40, 43);
			this.buttonX_31.Name = Class521.smethod_0(28998);
			this.buttonX_31.ShowSubItems = false;
			this.buttonX_31.Size = new Size(27, 28);
			this.buttonX_31.Style = eDotNetBarStyle.StyleManagerControlled;
			this.buttonX_31.TabIndex = 5;
			this.buttonX_31.Tooltip = Class521.smethod_0(5246);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(105, 392);
			base.Controls.Add(this.buttonX_29);
			base.Controls.Add(this.buttonX_30);
			base.Controls.Add(this.buttonX_31);
			base.Controls.Add(this.buttonX_28);
			base.Controls.Add(this.buttonX_27);
			base.Controls.Add(this.buttonX_26);
			base.Controls.Add(this.buttonX_25);
			base.Controls.Add(this.buttonX_24);
			base.Controls.Add(this.buttonX_23);
			base.Controls.Add(this.buttonX_22);
			base.Controls.Add(this.buttonX_21);
			base.Controls.Add(this.buttonX_20);
			base.Controls.Add(this.buttonX_19);
			base.Controls.Add(this.buttonX_18);
			base.Controls.Add(this.buttonX_17);
			base.Controls.Add(this.buttonX_16);
			base.Controls.Add(this.buttonX_15);
			base.Controls.Add(this.buttonX_14);
			base.Controls.Add(this.buttonX_13);
			base.Controls.Add(this.buttonX_12);
			base.Controls.Add(this.buttonX_11);
			base.Controls.Add(this.buttonX_10);
			base.Controls.Add(this.buttonX_9);
			base.Controls.Add(this.buttonX_8);
			base.Controls.Add(this.buttonX_7);
			base.Controls.Add(this.buttonX_6);
			base.Controls.Add(this.buttonX_5);
			base.Controls.Add(this.buttonX_4);
			base.Controls.Add(this.buttonX_3);
			base.Controls.Add(this.buttonX_2);
			base.Controls.Add(this.buttonX_1);
			base.Controls.Add(this.buttonX_0);
			base.FormBorderStyle = FormBorderStyle.FixedToolWindow;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(29019);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(11276);
			base.Load += this.Form15_Load;
			base.ResumeLayout(false);
		}

		// Token: 0x040007D5 RID: 2005
		private IContainer icontainer_0;

		// Token: 0x040007D6 RID: 2006
		private ButtonX buttonX_0;

		// Token: 0x040007D7 RID: 2007
		private ButtonX buttonX_1;

		// Token: 0x040007D8 RID: 2008
		private ButtonX buttonX_2;

		// Token: 0x040007D9 RID: 2009
		private ButtonX buttonX_3;

		// Token: 0x040007DA RID: 2010
		private ButtonX buttonX_4;

		// Token: 0x040007DB RID: 2011
		private ButtonX buttonX_5;

		// Token: 0x040007DC RID: 2012
		private ButtonX buttonX_6;

		// Token: 0x040007DD RID: 2013
		private ButtonX buttonX_7;

		// Token: 0x040007DE RID: 2014
		private ButtonX buttonX_8;

		// Token: 0x040007DF RID: 2015
		private ButtonX buttonX_9;

		// Token: 0x040007E0 RID: 2016
		private ButtonX buttonX_10;

		// Token: 0x040007E1 RID: 2017
		private ButtonX buttonX_11;

		// Token: 0x040007E2 RID: 2018
		private ButtonX buttonX_12;

		// Token: 0x040007E3 RID: 2019
		private ButtonX buttonX_13;

		// Token: 0x040007E4 RID: 2020
		private ButtonX buttonX_14;

		// Token: 0x040007E5 RID: 2021
		private ButtonX buttonX_15;

		// Token: 0x040007E6 RID: 2022
		private ButtonX buttonX_16;

		// Token: 0x040007E7 RID: 2023
		private ButtonX buttonX_17;

		// Token: 0x040007E8 RID: 2024
		private ButtonX buttonX_18;

		// Token: 0x040007E9 RID: 2025
		private ButtonX buttonX_19;

		// Token: 0x040007EA RID: 2026
		private ButtonX buttonX_20;

		// Token: 0x040007EB RID: 2027
		private ButtonX buttonX_21;

		// Token: 0x040007EC RID: 2028
		private ButtonX buttonX_22;

		// Token: 0x040007ED RID: 2029
		private ButtonX buttonX_23;

		// Token: 0x040007EE RID: 2030
		private ButtonX buttonX_24;

		// Token: 0x040007EF RID: 2031
		private ButtonX buttonX_25;

		// Token: 0x040007F0 RID: 2032
		private ButtonX buttonX_26;

		// Token: 0x040007F1 RID: 2033
		private ButtonX buttonX_27;

		// Token: 0x040007F2 RID: 2034
		private ButtonX buttonX_28;

		// Token: 0x040007F3 RID: 2035
		private ButtonX buttonX_29;

		// Token: 0x040007F4 RID: 2036
		private ButtonX buttonX_30;

		// Token: 0x040007F5 RID: 2037
		private ButtonX buttonX_31;
	}
}
