﻿using System;
using System.Drawing;
using ns18;
using ns22;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns31
{
	// Token: 0x020002EE RID: 750
	internal sealed class Class391 : Class390
	{
		// Token: 0x06002114 RID: 8468 RVA: 0x000EAFA4 File Offset: 0x000E91A4
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_PartLine lineItem_ = zedGraphControl_0.GraphPane.AddPartLine(base.IndData.Name, base.DataView, color_0);
			base.method_3(string_0, lineItem_);
		}

		// Token: 0x06002115 RID: 8469 RVA: 0x000EAFF8 File Offset: 0x000E91F8
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				throw new Exception(Class521.smethod_0(97541));
			}
			if (dataArray_1.OtherDataArrayList.Count != 1)
			{
				throw new Exception(Class521.smethod_0(98079));
			}
			double x = new XDate(base.method_0(int_0));
			double y = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList[0].Length <= 0)
			{
				throw new Exception(Class521.smethod_0(98112));
			}
			PointPair result;
			if (dataArray_1.OtherDataArrayList[0].Length == dataArray_1.Data.Length)
			{
				result = new PointPair(x, y, dataArray_1.OtherDataArrayList[0].Data[int_0]);
			}
			else
			{
				result = new PointPair(x, y, dataArray_1.OtherDataArrayList[0].Data[0]);
			}
			return result;
		}

		// Token: 0x06002116 RID: 8470 RVA: 0x0000D61B File Offset: 0x0000B81B
		public Class391(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
