﻿using System;
using TEx.Trading;

namespace ns19
{
	// Token: 0x02000273 RID: 627
	internal sealed class EventArgs16 : EventArgs
	{
		// Token: 0x06001B74 RID: 7028 RVA: 0x0000B5C2 File Offset: 0x000097C2
		public EventArgs16(ShownHisTrans shownHisTrans_1)
		{
			this.shownHisTrans_0 = shownHisTrans_1;
		}

		// Token: 0x17000477 RID: 1143
		// (get) Token: 0x06001B75 RID: 7029 RVA: 0x000BFDBC File Offset: 0x000BDFBC
		public ShownHisTrans ShownHisTrans
		{
			get
			{
				return this.shownHisTrans_0;
			}
		}

		// Token: 0x04000D92 RID: 3474
		private readonly ShownHisTrans shownHisTrans_0;

		// Token: 0x04000D93 RID: 3475
		private readonly string string_0;
	}
}
