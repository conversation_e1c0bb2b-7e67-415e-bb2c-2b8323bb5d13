﻿using System;

namespace ns24
{
	// Token: 0x02000086 RID: 134
	internal sealed class EventArgs2 : EventArgs
	{
		// Token: 0x0600047B RID: 1147 RVA: 0x00003EF9 File Offset: 0x000020F9
		public EventArgs2(int int_2, int int_3, string string_1)
		{
			this.int_0 = int_2;
			this.int_1 = int_3;
			this.string_0 = string_1;
		}

		// Token: 0x170000E7 RID: 231
		// (get) Token: 0x0600047C RID: 1148 RVA: 0x000248D4 File Offset: 0x00022AD4
		public int TaskIndex
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x170000E8 RID: 232
		// (get) Token: 0x0600047D RID: 1149 RVA: 0x000248EC File Offset: 0x00022AEC
		public int TaskCount
		{
			get
			{
				return this.int_1;
			}
		}

		// Token: 0x170000E9 RID: 233
		// (get) Token: 0x0600047E RID: 1150 RVA: 0x00024904 File Offset: 0x00022B04
		public string FileName
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x040001B5 RID: 437
		private int int_0;

		// Token: 0x040001B6 RID: 438
		private int int_1;

		// Token: 0x040001B7 RID: 439
		private string string_0;
	}
}
