﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns10;
using ns18;
using TEx;

namespace ns28
{
	// Token: 0x02000203 RID: 515
	[DesignerCategory("Code")]
	internal class Class284 : DataGridView
	{
		// Token: 0x06001507 RID: 5383 RVA: 0x000086DA File Offset: 0x000068DA
		public Class284(bool bool_2 = false)
		{
			this.ShowRowNumber = bool_2;
			this.vmethod_0();
			if (bool_2)
			{
				base.RowPostPaint += this.Class284_RowPostPaint;
			}
			Base.UI.ChartThemeChanged += this.method_0;
		}

		// Token: 0x06001508 RID: 5384 RVA: 0x0008E830 File Offset: 0x0008CA30
		protected virtual void vmethod_0()
		{
			base.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
			base.ReadOnly = true;
			base.MultiSelect = false;
			this.Dock = DockStyle.Fill;
			base.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			base.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.EnableHeadersVisualStyles = false;
			base.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			base.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.ColumnHeadersDefaultCellStyle.Padding = new Padding(0);
			base.AllowUserToOrderColumns = false;
			base.RowHeadersVisible = this.ShowRowNumber;
			base.AllowUserToAddRows = false;
			base.AllowUserToDeleteRows = false;
			base.AllowUserToResizeRows = false;
			this.vmethod_1();
			this.vmethod_2();
		}

		// Token: 0x06001509 RID: 5385 RVA: 0x0008E8D8 File Offset: 0x0008CAD8
		protected virtual void vmethod_1()
		{
			float emSize = TApp.smethod_4(8.5f, false);
			Font font = new Font(Class521.smethod_0(50738), emSize, FontStyle.Regular);
			base.ColumnHeadersDefaultCellStyle.Font = font;
			base.DefaultCellStyle.Font = font;
		}

		// Token: 0x0600150A RID: 5386 RVA: 0x00008717 File Offset: 0x00006917
		private void method_0(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.smethod_0();
			this.vmethod_2();
			this.smethod_1();
			base.ResumeLayout();
		}

		// Token: 0x0600150B RID: 5387 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_2()
		{
		}

		// Token: 0x0600150C RID: 5388 RVA: 0x0008E920 File Offset: 0x0008CB20
		public void method_1(object object_0)
		{
			this.IsSettingDataSource = true;
			int num = -1;
			if (base.SelectedRows != null && base.SelectedRows.Count > 0)
			{
				num = base.SelectedRows[0].Index;
			}
			base.DataSource = null;
			base.DataSource = new BindingSource
			{
				DataSource = object_0
			};
			if (base.Columns.Count > 0)
			{
				this.vmethod_3();
			}
			if (num > 0 && base.Rows.Count > num)
			{
				base.Rows[num].Selected = true;
			}
			this.IsSettingDataSource = false;
		}

		// Token: 0x0600150D RID: 5389 RVA: 0x000041B9 File Offset: 0x000023B9
		protected virtual void vmethod_3()
		{
		}

		// Token: 0x0600150E RID: 5390 RVA: 0x0008E9BC File Offset: 0x0008CBBC
		protected void method_2(DataGridViewCell dataGridViewCell_0)
		{
			lock (dataGridViewCell_0)
			{
				if (dataGridViewCell_0.Value != null && dataGridViewCell_0.Value.ToString() != Class521.smethod_0(3210))
				{
					try
					{
						double num = Convert.ToDouble(dataGridViewCell_0.Value);
						if (double.IsNaN(num))
						{
							dataGridViewCell_0.Value = Class521.smethod_0(1449);
						}
						else if (num > 0.0 & dataGridViewCell_0.Style.ForeColor != Color.Red)
						{
							dataGridViewCell_0.Style.ForeColor = Color.Red;
						}
						else if (num < 0.0 & dataGridViewCell_0.Style.ForeColor != Color.Green)
						{
							dataGridViewCell_0.Style.ForeColor = Color.Green;
						}
						else if (num == 0.0 & dataGridViewCell_0.Style.ForeColor != base.DefaultCellStyle.ForeColor)
						{
							dataGridViewCell_0.Style.ForeColor = base.DefaultCellStyle.ForeColor;
						}
					}
					catch
					{
					}
				}
			}
		}

		// Token: 0x0600150F RID: 5391 RVA: 0x0008EB14 File Offset: 0x0008CD14
		public DataGridViewRow method_3()
		{
			DataGridViewRow result = null;
			if (base.SelectedRows.Count > 0)
			{
				result = base.SelectedRows[0];
			}
			else if (base.Rows.Count > 0)
			{
				result = base.Rows[0];
			}
			return result;
		}

		// Token: 0x06001510 RID: 5392 RVA: 0x0008EB60 File Offset: 0x0008CD60
		public object method_4()
		{
			object result = null;
			DataGridViewSelectedRowCollection selectedRows = base.SelectedRows;
			if (selectedRows != null && selectedRows.Count > 0)
			{
				result = selectedRows[0].DataBoundItem;
			}
			return result;
		}

		// Token: 0x1700037F RID: 895
		// (get) Token: 0x06001511 RID: 5393 RVA: 0x0008EB94 File Offset: 0x0008CD94
		// (set) Token: 0x06001512 RID: 5394 RVA: 0x00008739 File Offset: 0x00006939
		public bool IsSettingDataSource { get; set; }

		// Token: 0x17000380 RID: 896
		// (get) Token: 0x06001513 RID: 5395 RVA: 0x0008EBAC File Offset: 0x0008CDAC
		// (set) Token: 0x06001514 RID: 5396 RVA: 0x00008744 File Offset: 0x00006944
		public bool ShowRowNumber { get; set; }

		// Token: 0x06001515 RID: 5397 RVA: 0x0008EBC4 File Offset: 0x0008CDC4
		[CompilerGenerated]
		private void Class284_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
		{
			using (SolidBrush solidBrush = new SolidBrush(base.ColumnHeadersDefaultCellStyle.ForeColor))
			{
				e.Graphics.DrawString((e.RowIndex + 1).ToString(), e.InheritedRowStyle.Font, solidBrush, (float)(e.RowBounds.Location.X + 12), (float)(e.RowBounds.Location.Y + 4));
			}
		}

		// Token: 0x04000AE5 RID: 2789
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000AE6 RID: 2790
		[CompilerGenerated]
		private bool bool_1;
	}
}
