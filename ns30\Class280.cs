﻿using System;
using System.Windows.Forms;
using ns18;
using TEx;

namespace ns30
{
	// Token: 0x020001F1 RID: 497
	internal sealed class Class280
	{
		// Token: 0x0600137C RID: 4988 RVA: 0x00002D25 File Offset: 0x00000F25
		public Class280()
		{
		}

		// Token: 0x0600137D RID: 4989 RVA: 0x00007E42 File Offset: 0x00006042
		public Class280(int int_1, string string_2, string string_3, KeyModifiers keyModifiers_1, Keys keys_1) : this(int_1, string_2, string_3, keyModifiers_1, keys_1, false)
		{
		}

		// Token: 0x0600137E RID: 4990 RVA: 0x00007E52 File Offset: 0x00006052
		public Class280(int int_1, string string_2, string string_3, KeyModifiers keyModifiers_1, Keys keys_1, bool bool_1)
		{
			this.Id = int_1;
			this.EnName = string_2;
			this.CnName = string_3;
			this.KeyModifier = keyModifiers_1;
			this.Key = keys_1;
			this.IfDispInQuickWnd = bool_1;
		}

		// Token: 0x0600137F RID: 4991 RVA: 0x00087DA0 File Offset: 0x00085FA0
		private Keys method_0()
		{
			Keys result;
			if (this.keyModifiers_0 == KeyModifiers.None)
			{
				result = this.keys_0;
			}
			else
			{
				Keys keys = Keys.None;
				string text = this.keyModifiers_0.ToString();
				if (text.Contains(Class521.smethod_0(49744)))
				{
					keys = Keys.Control;
				}
				if (text.Contains(Class521.smethod_0(49753)))
				{
					if (keys == Keys.None)
					{
						keys = Keys.Alt;
					}
					else
					{
						keys |= Keys.Alt;
					}
				}
				if (text.Contains(Class521.smethod_0(49758)))
				{
					if (keys == Keys.None)
					{
						keys = Keys.Shift;
					}
					else
					{
						keys |= Keys.Shift;
					}
				}
				if (text.Contains(Class521.smethod_0(49767)))
				{
					if (keys == Keys.None)
					{
						keys = Keys.LWin;
					}
					else
					{
						keys |= Keys.LWin;
					}
				}
				keys |= this.keys_0;
				result = keys;
			}
			return result;
		}

		// Token: 0x170002E8 RID: 744
		// (get) Token: 0x06001380 RID: 4992 RVA: 0x00087E64 File Offset: 0x00086064
		// (set) Token: 0x06001381 RID: 4993 RVA: 0x00007E89 File Offset: 0x00006089
		public int Id
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002E9 RID: 745
		// (get) Token: 0x06001382 RID: 4994 RVA: 0x00087E7C File Offset: 0x0008607C
		// (set) Token: 0x06001383 RID: 4995 RVA: 0x00007E94 File Offset: 0x00006094
		public string EnName
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002EA RID: 746
		// (get) Token: 0x06001384 RID: 4996 RVA: 0x00087E94 File Offset: 0x00086094
		// (set) Token: 0x06001385 RID: 4997 RVA: 0x00007E9F File Offset: 0x0000609F
		public string CnName
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x170002EB RID: 747
		// (get) Token: 0x06001386 RID: 4998 RVA: 0x00087EAC File Offset: 0x000860AC
		// (set) Token: 0x06001387 RID: 4999 RVA: 0x00007EAA File Offset: 0x000060AA
		public KeyModifiers KeyModifier
		{
			get
			{
				return this.keyModifiers_0;
			}
			set
			{
				this.keyModifiers_0 = value;
			}
		}

		// Token: 0x170002EC RID: 748
		// (get) Token: 0x06001388 RID: 5000 RVA: 0x00087EC4 File Offset: 0x000860C4
		// (set) Token: 0x06001389 RID: 5001 RVA: 0x00007EB5 File Offset: 0x000060B5
		public Keys Key
		{
			get
			{
				return this.keys_0;
			}
			set
			{
				this.keys_0 = value;
			}
		}

		// Token: 0x170002ED RID: 749
		// (get) Token: 0x0600138A RID: 5002 RVA: 0x00087EDC File Offset: 0x000860DC
		// (set) Token: 0x0600138B RID: 5003 RVA: 0x00007EC0 File Offset: 0x000060C0
		public bool IfDispInQuickWnd
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170002EE RID: 750
		// (get) Token: 0x0600138C RID: 5004 RVA: 0x00087EF4 File Offset: 0x000860F4
		public Keys KeyCode
		{
			get
			{
				return this.method_0();
			}
		}

		// Token: 0x170002EF RID: 751
		// (get) Token: 0x0600138D RID: 5005 RVA: 0x00087F0C File Offset: 0x0008610C
		public string ShortCutKeyString
		{
			get
			{
				string str = Class521.smethod_0(1449);
				if (this.KeyModifier != KeyModifiers.None)
				{
					str = str + this.KeyModifier.ToString() + Class521.smethod_0(49784);
				}
				return str + this.Key.ToString();
			}
		}

		// Token: 0x04000A2F RID: 2607
		private int int_0;

		// Token: 0x04000A30 RID: 2608
		private string string_0;

		// Token: 0x04000A31 RID: 2609
		private string string_1;

		// Token: 0x04000A32 RID: 2610
		private KeyModifiers keyModifiers_0;

		// Token: 0x04000A33 RID: 2611
		private Keys keys_0;

		// Token: 0x04000A34 RID: 2612
		private bool bool_0;
	}
}
