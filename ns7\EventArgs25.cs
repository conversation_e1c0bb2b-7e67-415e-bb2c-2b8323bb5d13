﻿using System;

namespace ns7
{
	// Token: 0x0200027C RID: 636
	internal sealed class EventArgs25 : EventArgs
	{
		// Token: 0x06001B98 RID: 7064 RVA: 0x0000B6EF File Offset: 0x000098EF
		public EventArgs25(int int_1, bool bool_1, string string_1)
		{
			this.int_0 = int_1;
			this.bool_0 = bool_1;
			this.string_0 = string_1;
		}

		// Token: 0x17000489 RID: 1161
		// (get) Token: 0x06001B99 RID: 7065 RVA: 0x000BFF6C File Offset: 0x000BE16C
		public int TotalRecs
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x1700048A RID: 1162
		// (get) Token: 0x06001B9A RID: 7066 RVA: 0x000BFF84 File Offset: 0x000BE184
		public bool Result
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x1700048B RID: 1163
		// (get) Token: 0x06001B9B RID: 7067 RVA: 0x000BFF9C File Offset: 0x000BE19C
		public string Msg
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x04000DA5 RID: 3493
		private readonly int int_0;

		// Token: 0x04000DA6 RID: 3494
		private readonly bool bool_0;

		// Token: 0x04000DA7 RID: 3495
		private readonly string string_0;
	}
}
