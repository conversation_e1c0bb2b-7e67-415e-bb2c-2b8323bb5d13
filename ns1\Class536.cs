﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using ns11;
using ns16;
using ns17;
using ns18;
using ns20;
using ns22;
using ns23;
using ns26;
using ns28;
using ns9;
using SmartAssembly.Shared.ReportHelper;

namespace ns1
{
	// Token: 0x020003F1 RID: 1009
	internal sealed class Class536 : Class535
	{
		// Token: 0x06002773 RID: 10099 RVA: 0x0010A2A0 File Offset: 0x001084A0
		public Class536(Guid guid_1, Exception exception_1, IWebProxy iwebProxy_1)
		{
			this.guid_0 = guid_1;
			this.exception_0 = exception_1;
			base.method_0(iwebProxy_1);
			string a = Class521.smethod_0(117020).ToUpper();
			if (a == Class521.smethod_0(117033))
			{
				this.char_0 = new char[]
				{
					'a',
					'b',
					'c',
					'd',
					'e',
					'f',
					'g',
					'h',
					'i',
					'j',
					'k',
					'l',
					'm',
					'n',
					'o',
					'p',
					'q',
					'r',
					's',
					't',
					'u',
					'v',
					'w',
					'x',
					'y',
					'z',
					'A',
					'B',
					'C',
					'D',
					'E',
					'F',
					'G',
					'H',
					'I',
					'J',
					'K',
					'L',
					'M',
					'N',
					'O',
					'P',
					'Q',
					'R',
					'S',
					'T',
					'U',
					'V',
					'W',
					'X',
					'Y',
					'Z',
					'0',
					'1',
					'2',
					'3',
					'4',
					'5',
					'6',
					'7',
					'8',
					'9'
				};
				return;
			}
			if (!(a == Class521.smethod_0(117020)))
			{
				return;
			}
			this.char_0 = new char[]
			{
				'\u0001',
				'\u0002',
				'\u0003',
				'\u0004',
				'\u0005',
				'\u0006',
				'\a',
				'\b',
				'\u000e',
				'\u000f',
				'\u0010',
				'\u0011',
				'\u0012',
				'\u0013',
				'\u0014',
				'\u0015',
				'\u0016',
				'\u0017',
				'\u0018',
				'\u0019',
				'\u001a',
				'\u001b',
				'\u001c',
				'\u001d',
				'\u001e',
				'\u001f',
				'\u007f',
				'\u0080',
				'\u0081',
				'\u0082',
				'\u0083',
				'\u0084',
				'\u0086',
				'\u0087',
				'\u0088',
				'\u0089',
				'\u008a',
				'\u008b',
				'\u008c',
				'\u008d',
				'\u008e',
				'\u008f',
				'\u0090',
				'\u0091',
				'\u0092',
				'\u0093',
				'\u0094',
				'\u0095',
				'\u0096',
				'\u0097',
				'\u0098',
				'\u0099',
				'\u009a',
				'\u009b',
				'\u009c',
				'\u009d',
				'\u009e',
				'\u009f'
			};
		}

		// Token: 0x06002774 RID: 10100 RVA: 0x0010A388 File Offset: 0x00108588
		private static string smethod_0(object object_0)
		{
			try
			{
				if (object_0 == null)
				{
					return string.Empty;
				}
				if (object_0 is int)
				{
					return ((int)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is long)
				{
					return ((long)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is short)
				{
					return ((short)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is uint)
				{
					return ((uint)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is ulong)
				{
					return ((ulong)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is ushort)
				{
					return ((ushort)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is byte)
				{
					return ((byte)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is sbyte)
				{
					return ((sbyte)object_0).ToString(Class521.smethod_0(117042));
				}
				if (object_0 is IntPtr)
				{
					return ((IntPtr)object_0).ToInt64().ToString(Class521.smethod_0(117042));
				}
				if (object_0 is UIntPtr)
				{
					return ((UIntPtr)object_0).ToUInt64().ToString(Class521.smethod_0(117042));
				}
			}
			catch
			{
			}
			return string.Empty;
		}

		// Token: 0x06002775 RID: 10101 RVA: 0x0000F298 File Offset: 0x0000D498
		private static string smethod_1(string string_10)
		{
			if (string_10.StartsWith(Class521.smethod_0(117047)) && string_10.EndsWith(Class521.smethod_0(117068)))
			{
				return Class521.smethod_0(117089);
			}
			return string_10;
		}

		// Token: 0x06002776 RID: 10102 RVA: 0x0010A558 File Offset: 0x00108758
		private void method_5(Class540 class540_0, FieldInfo fieldInfo_0)
		{
			string text = (fieldInfo_0 == null) ? null : fieldInfo_0.Name;
			string string_ = (fieldInfo_0 == null) ? Class521.smethod_0(117171) : Class521.smethod_0(117162);
			object obj = class540_0.method_0();
			if (obj == null)
			{
				using (new Class548(this.xmlWriter_0, string_))
				{
					if (fieldInfo_0 != null)
					{
						if (fieldInfo_0.IsStatic)
						{
							this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117180), Class521.smethod_0(4933));
						}
						Type fieldType = fieldInfo_0.FieldType;
						if (fieldType != null && fieldType.HasElementType)
						{
							this.method_9(fieldType.GetElementType());
							if (fieldType.IsByRef)
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117189), Class521.smethod_0(4933));
							}
							if (fieldType.IsPointer)
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117198), Class521.smethod_0(4933));
							}
							if (fieldType.IsArray)
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117211), fieldType.GetArrayRank().ToString());
							}
						}
						else
						{
							this.method_9(fieldType);
						}
					}
					if (text != null)
					{
						this.method_7(text);
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117220), Class521.smethod_0(4933));
				}
				return;
			}
			Type type = class540_0.method_0().GetType();
			string text2 = null;
			string text3 = null;
			if (obj is string)
			{
				text2 = Class521.smethod_0(117229);
			}
			if (text2 == null)
			{
				if (!type.IsPrimitive && !(obj is IntPtr) && !(obj is UIntPtr))
				{
					if (type.IsValueType && type.Module != base.GetType().Module)
					{
						text2 = type.FullName;
					}
				}
				else
				{
					text2 = type.FullName;
					if (obj is char)
					{
						int num = (int)((char)obj);
						StringBuilder stringBuilder = new StringBuilder();
						if (num >= 32)
						{
							stringBuilder.Append('\'');
							stringBuilder.Append((char)obj);
							stringBuilder.Append(Class521.smethod_0(117250));
						}
						stringBuilder.Append(Class521.smethod_0(117255));
						stringBuilder.Append(num.ToString(Class521.smethod_0(117042)));
						stringBuilder.Append(')');
						text3 = stringBuilder.ToString();
					}
					if (obj is bool)
					{
						text3 = obj.ToString().ToLower();
					}
					if (text3 == null)
					{
						string text4 = Class536.smethod_0(obj);
						if (text4.Length > 0)
						{
							StringBuilder stringBuilder2 = new StringBuilder();
							stringBuilder2.Append(obj.ToString());
							stringBuilder2.Append(Class521.smethod_0(117260));
							stringBuilder2.Append(text4);
							stringBuilder2.Append(')');
							text3 = stringBuilder2.ToString();
						}
						else
						{
							text3 = obj.ToString();
						}
					}
				}
			}
			using (new Class548(this.xmlWriter_0, string_))
			{
				if (fieldInfo_0 != null && fieldInfo_0.IsStatic)
				{
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117180), Class521.smethod_0(4933));
				}
				if (text2 != null)
				{
					this.method_9(type);
					if (text != null)
					{
						this.method_7(text);
					}
					if (type.IsEnum)
					{
						text3 = obj.ToString();
					}
					if (obj is Guid)
					{
						text3 = Class521.smethod_0(99024) + obj + Class521.smethod_0(47398);
					}
					if (text3 == null)
					{
						text3 = Class521.smethod_0(105109) + obj + Class521.smethod_0(105109);
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(48861), Class536.smethod_1(text3));
				}
				else
				{
					if (fieldInfo_0 != null)
					{
						this.method_9(fieldInfo_0.FieldType);
					}
					this.method_6(class540_0);
					if (text != null)
					{
						this.method_7(text);
					}
				}
			}
		}

		// Token: 0x06002777 RID: 10103 RVA: 0x0010A954 File Offset: 0x00108B54
		private void method_6(Class540 class540_0)
		{
			object obj = class540_0.method_0();
			int num = -1;
			for (int i = 0; i < this.list_0.Count; i++)
			{
				if (this.list_0[i].method_0() == obj)
				{
					num = i;
					IL_39:
					if (num == -1)
					{
						num = this.list_0.Count;
						this.list_0.Add(class540_0);
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(33549), num.ToString());
					return;
				}
			}
			goto IL_39;
		}

		// Token: 0x06002778 RID: 10104 RVA: 0x0010A9D4 File Offset: 0x00108BD4
		private void method_7(string string_10)
		{
			int num = this.method_10(string_10);
			if (num != -1)
			{
				this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117269), num.ToString());
				return;
			}
			this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(1858), string_10);
		}

		// Token: 0x06002779 RID: 10105 RVA: 0x0010AA20 File Offset: 0x00108C20
		private static Class536.Struct28 smethod_2(Type type_0)
		{
			Class536.Struct28 empty = Class536.Struct28.Empty;
			if (type_0 != null && type_0.Assembly.GetType(Class521.smethod_0(117278)) != null)
			{
				empty.string_0 = ((type_0.MetadataToken & 16777215) - 1).ToString();
				Assembly assembly = type_0.Assembly;
				empty.struct27_0 = new Class536.Struct27(assembly.ManifestModule.ModuleVersionId.ToString(Class521.smethod_0(15541)), assembly.FullName);
			}
			return empty;
		}

		// Token: 0x0600277A RID: 10106 RVA: 0x0010AAA4 File Offset: 0x00108CA4
		private int method_8(Class536.Struct28 struct28_0)
		{
			string key = struct28_0.struct27_0.string_0.ToUpper();
			if (this.dictionary_3.ContainsKey(key))
			{
				return this.dictionary_3[key];
			}
			int count = this.list_2.Count;
			this.list_2.Add(struct28_0.struct27_0);
			this.dictionary_3.Add(key, count);
			return count;
		}

		// Token: 0x0600277B RID: 10107 RVA: 0x0010AB08 File Offset: 0x00108D08
		private void method_9(Type type_0)
		{
			if (type_0 == null)
			{
				return;
			}
			try
			{
				Class536.Struct28 @struct = Class536.smethod_2(type_0);
				if (!@struct.IsEmpty)
				{
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117339), @struct.string_0);
					int num = this.method_8(@struct);
					if (num > 0)
					{
						this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117352), num.ToString());
					}
				}
				else
				{
					string fullName = type_0.FullName;
					int value;
					if (this.dictionary_2.ContainsKey(fullName))
					{
						value = this.dictionary_2[fullName];
					}
					else
					{
						StringBuilder stringBuilder = new StringBuilder();
						string name = type_0.Assembly.GetName().Name;
						if (name.Length > 0 && name != Class521.smethod_0(117365))
						{
							stringBuilder.Append('[');
							stringBuilder.Append(name);
							stringBuilder.Append(']');
						}
						string @namespace = type_0.Namespace;
						if (@namespace.Length > 0)
						{
							stringBuilder.Append(@namespace);
							stringBuilder.Append('.');
						}
						if (type_0.HasElementType)
						{
							type_0 = type_0.GetElementType();
						}
						int num2 = fullName.LastIndexOf(Class521.smethod_0(49784));
						if (num2 > 0)
						{
							string value2 = fullName.Substring(@namespace.Length + 1, num2 - @namespace.Length).Replace(Class521.smethod_0(49784), Class521.smethod_0(24570));
							stringBuilder.Append(value2);
						}
						stringBuilder.Append(type_0.Name);
						value = this.list_1.Count;
						this.list_1.Add(stringBuilder.ToString());
						this.dictionary_2.Add(fullName, value);
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117378), value.ToString());
				}
			}
			catch
			{
			}
		}

		// Token: 0x0600277C RID: 10108 RVA: 0x0010ACF4 File Offset: 0x00108EF4
		private int method_10(string string_10)
		{
			int result;
			try
			{
				bool flag = this.char_0[0] == '\u0001';
				if (string_10 != null && string_10.Length != 0 && (!flag || string_10.Length <= 4))
				{
					if (flag || string_10[0] == '#')
					{
						int num = 0;
						int num2 = string_10.Length - 1;
						IL_99:
						while (num2 >= 0 && (flag || num2 != 0))
						{
							char c = string_10[num2];
							bool flag2 = false;
							int i = 0;
							while (i < this.char_0.Length)
							{
								if (this.char_0[i] == c)
								{
									num = num * this.char_0.Length + i;
									flag2 = true;
									IL_91:
									if (flag2)
									{
										num2--;
										goto IL_99;
									}
									return -1;
								}
								else
								{
									i++;
								}
							}
							goto IL_91;
						}
						return num;
					}
				}
				result = -1;
			}
			catch
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x0600277D RID: 10109 RVA: 0x0010ADC0 File Offset: 0x00108FC0
		private static string smethod_3()
		{
			string result;
			try
			{
				result = Application.ExecutablePath;
			}
			catch
			{
				result = Class521.smethod_0(18686);
			}
			return result;
		}

		// Token: 0x0600277E RID: 10110 RVA: 0x0010ADF4 File Offset: 0x00108FF4
		private Assembly[] method_11()
		{
			Assembly[] result;
			try
			{
				result = AppDomain.CurrentDomain.GetAssemblies();
			}
			catch
			{
				result = new Assembly[]
				{
					Class536.smethod_4()
				};
			}
			return result;
		}

		// Token: 0x0600277F RID: 10111 RVA: 0x0010AE34 File Offset: 0x00109034
		private static Assembly smethod_4()
		{
			Assembly result;
			try
			{
				result = Assembly.GetExecutingAssembly();
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002780 RID: 10112 RVA: 0x0000F2CA File Offset: 0x0000D4CA
		internal byte[] method_12()
		{
			return this.method_13();
		}

		// Token: 0x06002781 RID: 10113 RVA: 0x0010AE60 File Offset: 0x00109060
		private byte[] method_13()
		{
			if (this.byte_0 != null)
			{
				return this.byte_0;
			}
			this.memoryStream_0 = new MemoryStream();
			this.xmlWriter_0 = new XmlTextWriter(this.memoryStream_0, new UTF8Encoding(false));
			this.xmlWriter_0.WriteStartDocument();
			using (new Class548(this.xmlWriter_0, Class521.smethod_0(117391)))
			{
				this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117424), Class521.smethod_0(117441).ToUpper());
				this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(33308), DateTime.Now.ToString(Class521.smethod_0(117494)));
				this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(116109), Class536.smethod_3());
				if (this.guid_0 != Guid.Empty)
				{
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117499), this.guid_0.ToString(Class521.smethod_0(15541)));
				}
				this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117508), Guid.NewGuid().ToString(Class521.smethod_0(15541)));
				if (this.list_2.Count > 0)
				{
					this.list_2.Clear();
				}
				this.list_2.Add(new Class536.Struct27(Class521.smethod_0(117441), string.Empty));
				if (this.dictionary_3.Count > 0)
				{
					this.dictionary_3.Clear();
				}
				this.dictionary_3.Add(Class521.smethod_0(117441), 0);
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117521)))
				{
					Assembly assembly = Class536.smethod_4();
					foreach (Assembly assembly2 in this.method_11())
					{
						if (assembly2 != null)
						{
							using (new Class548(this.xmlWriter_0, Class521.smethod_0(117352)))
							{
								try
								{
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(1858), assembly2.FullName);
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117538), assembly2.CodeBase);
									if (assembly2 == assembly)
									{
										this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117551), Class521.smethod_0(4933));
									}
								}
								catch
								{
								}
							}
						}
					}
				}
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117560)))
				{
					if (this.dictionary_0 != null && this.dictionary_0.Count > 0)
					{
						foreach (string text in this.dictionary_0.Keys)
						{
							using (new Class548(this.xmlWriter_0, Class521.smethod_0(117585)))
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(1858), text);
								string text2 = (string)this.dictionary_0[text];
								if (text2 == null)
								{
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117220), Class521.smethod_0(4933));
								}
								else
								{
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(48861), Class521.smethod_0(105109) + text2 + Class521.smethod_0(105109));
								}
							}
						}
					}
				}
				if (this.dictionary_1 != null && this.dictionary_1.Count > 0)
				{
					using (new Class548(this.xmlWriter_0, Class521.smethod_0(117606)))
					{
						foreach (string text3 in this.dictionary_1.Keys)
						{
							using (new Class548(this.xmlWriter_0, Class521.smethod_0(117627)))
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(27356), text3);
								Class536.Struct26 @struct = this.dictionary_1[text3];
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117644), @struct.string_0);
								XmlWriter xmlWriter = this.xmlWriter_0;
								string localName = Class521.smethod_0(117657);
								int i = @struct.int_0;
								xmlWriter.WriteAttributeString(localName, i.ToString());
								if (@struct.string_2.Length > 0)
								{
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(3672), @struct.string_2);
								}
								else
								{
									this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(45140), @struct.string_1);
								}
							}
						}
					}
				}
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117666)))
				{
					try
					{
						Version version = Environment.Version;
						Version version2 = Environment.OSVersion.Version;
						string value = Environment.OSVersion.Platform.ToString();
						Enum31 @enum;
						string value2;
						bool flag;
						OsInformation.smethod_3(out @enum, ref version, ref version2, ref value, out value2, out flag);
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117691), version.ToString());
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117708), version2.ToString());
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117721), value);
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117738), value2);
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117759), Class547.ServicePack);
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117776), Class547.IsServerR2 ? Class521.smethod_0(4933) : Class521.smethod_0(2841));
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117789), OsVersionInformation.IsX64 ? Class521.smethod_0(4933) : Class521.smethod_0(2841));
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(12832), Class547.IsWorkstation ? Class521.smethod_0(4933) : Class521.smethod_0(2841));
					}
					catch
					{
					}
				}
				List<Exception> list = new List<Exception>();
				for (Exception innerException = this.exception_0; innerException != null; innerException = innerException.InnerException)
				{
					list.Add(innerException);
				}
				list.Reverse();
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117794)))
				{
					foreach (Exception ex in list)
					{
						try
						{
							this.method_16(ex);
							if (ex.Data.Contains(Class521.smethod_0(117811)))
							{
								ICollection collection = (ICollection)ex.Data[Class521.smethod_0(117811)];
								int count = collection.Count;
								int num = 0;
								foreach (object obj in collection)
								{
									try
									{
										Type type = obj.GetType();
										num++;
										if (num > 100 && num == count - 100)
										{
											using (new Class548(this.xmlWriter_0, Class521.smethod_0(117836)))
											{
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117857), count.ToString());
											}
										}
										else if (num <= 100 || num > count - 100)
										{
											int num2 = (int)type.GetField(Class521.smethod_0(117882)).GetValue(obj);
											int num3 = (int)type.GetField(Class521.smethod_0(117895)).GetValue(obj);
											int num4 = (int)type.GetField(Class521.smethod_0(117908)).GetValue(obj);
											object[] array2 = (object[])type.GetField(Class521.smethod_0(117937)).GetValue(obj);
											Class536.Struct28 struct28_ = Class536.smethod_2(type);
											if (!struct28_.IsEmpty)
											{
												using (new Class548(this.xmlWriter_0, Class521.smethod_0(117950)))
												{
													this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117882), num2.ToString());
													this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117908), num4.ToString());
													int num5 = this.method_8(struct28_);
													if (num5 > 0)
													{
														this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117352), num5.ToString());
													}
													if (num3 != -1)
													{
														this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117895), num3.ToString());
													}
													foreach (object object_ in array2)
													{
														try
														{
															this.method_5(new Class540(object_, true), null);
														}
														catch
														{
														}
													}
												}
											}
										}
									}
									catch
									{
									}
								}
							}
						}
						catch
						{
						}
					}
				}
				this.method_14();
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117967)))
				{
					XmlWriter xmlWriter2 = this.xmlWriter_0;
					string localName2 = Class521.smethod_0(44953);
					int i = this.list_1.Count;
					xmlWriter2.WriteAttributeString(localName2, i.ToString());
					for (int j = 0; j < this.list_1.Count; j++)
					{
						string value3;
						try
						{
							value3 = this.list_1[j].ToString();
						}
						catch (Exception ex2)
						{
							value3 = Class521.smethod_0(105109) + ex2.Message + Class521.smethod_0(105109);
						}
						this.xmlWriter_0.WriteElementString(Class521.smethod_0(117378), value3);
					}
				}
				using (new Class548(this.xmlWriter_0, Class521.smethod_0(117980)))
				{
					XmlWriter xmlWriter3 = this.xmlWriter_0;
					string localName3 = Class521.smethod_0(44953);
					int i = this.list_2.Count;
					xmlWriter3.WriteAttributeString(localName3, i.ToString());
					for (int k = 0; k < this.list_2.Count; k++)
					{
						using (new Class548(this.xmlWriter_0, Class521.smethod_0(117424)))
						{
							Class536.Struct27 struct2 = this.list_2[k];
							this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(33549), struct2.string_0);
							if (struct2.string_1.Length > 0)
							{
								this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117997), struct2.string_1);
							}
						}
					}
				}
			}
			this.xmlWriter_0.WriteEndDocument();
			this.xmlWriter_0.Flush();
			this.memoryStream_0.Flush();
			this.byte_0 = this.memoryStream_0.ToArray();
			return this.byte_0;
		}

		// Token: 0x06002782 RID: 10114 RVA: 0x0010BBEC File Offset: 0x00109DEC
		private void method_14()
		{
			using (new Class548(this.xmlWriter_0, Class521.smethod_0(117937)))
			{
				for (int i = 0; i < this.list_0.Count; i++)
				{
					Class540 class2 = this.list_0[i];
					object obj = class2.method_0();
					Type type = class2.method_1();
					using (new Class548(this.xmlWriter_0, Class521.smethod_0(118010)))
					{
						this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(33549), i.ToString());
						string text = null;
						bool flag = true;
						string[] array = Class521.smethod_0(1449).Split(new char[]
						{
							','
						});
						for (int j = 0; j < array.Length; j++)
						{
							string text2 = array[j];
							if (text2 != Class521.smethod_0(1449) && type.FullName.StartsWith(text2))
							{
								flag = false;
								IL_DC:
								object[] customAttributes = type.GetCustomAttributes(true);
								for (j = 0; j < customAttributes.Length; j++)
								{
									string name = ((Attribute)customAttributes[j]).GetType().Name;
									if (!(name != Class521.smethod_0(118023)) || !(name != Class521.smethod_0(118060)))
									{
										flag = false;
										IL_13A:
										if (flag)
										{
											try
											{
												text = obj.ToString();
												if (text == type.FullName)
												{
													text = null;
												}
												else if (type.IsEnum)
												{
													text = Enum.Format(type, obj, Class521.smethod_0(12349));
												}
												else if (obj is Guid)
												{
													text = Class521.smethod_0(99024) + text + Class521.smethod_0(47398);
												}
												else
												{
													text = Class521.smethod_0(105109) + text + Class521.smethod_0(105109);
												}
											}
											catch
											{
											}
											if (text != null)
											{
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(48861), Class536.smethod_1(text));
											}
										}
										if (type.HasElementType)
										{
											this.method_9(type.GetElementType());
											if (type.IsByRef)
											{
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117189), Class521.smethod_0(4933));
											}
											if (type.IsPointer)
											{
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117198), Class521.smethod_0(4933));
											}
											if (type.IsArray)
											{
												Array array2 = (Array)obj;
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117211), array2.Rank.ToString());
												StringBuilder stringBuilder = new StringBuilder();
												for (int k = 0; k < array2.Rank; k++)
												{
													if (k > 0)
													{
														stringBuilder.Append(',');
													}
													stringBuilder.Append(array2.GetLength(k));
												}
												this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(117657), stringBuilder.ToString());
												if (array2.Rank == 1)
												{
													int length = array2.Length;
													for (int l = 0; l < length; l++)
													{
														if (l == 10 && length > 16)
														{
															l = length - 5;
														}
														try
														{
															this.method_5(new Class540(array2.GetValue(l), false), null);
														}
														catch
														{
														}
													}
												}
											}
										}
										else
										{
											this.method_9(type);
											if (class2.FirstLevel && flag)
											{
												try
												{
													if (obj is IEnumerable)
													{
														using (new Class548(this.xmlWriter_0, Class521.smethod_0(118089)))
														{
															int num = 0;
															foreach (object object_ in ((IEnumerable)obj))
															{
																if (num > 20)
																{
																	this.xmlWriter_0.WriteElementString(Class521.smethod_0(118106), string.Empty);
																	break;
																}
																this.method_5(new Class540(object_, false), null);
																num++;
															}
														}
													}
												}
												catch
												{
												}
												this.method_15(class2);
											}
										}
										goto IL_412;
									}
								}
								goto IL_13A;
							}
						}
						goto IL_DC;
					}
					IL_412:;
				}
			}
		}

		// Token: 0x06002783 RID: 10115 RVA: 0x0010C0CC File Offset: 0x0010A2CC
		private void method_15(Class540 class540_0)
		{
			foreach (FieldInfo fieldInfo in class540_0.method_1().GetFields(BindingFlags.DeclaredOnly | BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic))
			{
				try
				{
					if (!fieldInfo.IsLiteral)
					{
						if (!fieldInfo.IsStatic || !fieldInfo.IsInitOnly)
						{
							bool flag = true;
							object[] customAttributes = fieldInfo.GetCustomAttributes(true);
							int j = 0;
							while (j < customAttributes.Length)
							{
								if (!(((Attribute)customAttributes[j]).GetType().Name == Class521.smethod_0(118060)))
								{
									j++;
								}
								else
								{
									flag = false;
									IL_80:
									if (!flag)
									{
										goto IL_A3;
									}
									this.method_5(new Class540(fieldInfo.GetValue(class540_0.method_0()), false), fieldInfo);
									goto IL_A3;
								}
							}
							goto IL_80;
						}
					}
				}
				catch
				{
				}
				IL_A3:;
			}
			class540_0 = new Class540(class540_0.method_0(), class540_0.method_1().BaseType, class540_0.FirstLevel);
			if (class540_0.method_1() == null)
			{
				return;
			}
			using (new Class548(this.xmlWriter_0, Class521.smethod_0(117162)))
			{
				this.method_7(Class521.smethod_0(118115));
				XmlWriter xmlWriter = this.xmlWriter_0;
				string localName = Class521.smethod_0(33549);
				int i = this.list_0.Count;
				xmlWriter.WriteAttributeString(localName, i.ToString());
			}
			this.list_0.Add(class540_0);
		}

		// Token: 0x06002784 RID: 10116 RVA: 0x0010C234 File Offset: 0x0010A434
		private void method_16(Exception exception_1)
		{
			using (new Class548(this.xmlWriter_0, Class521.smethod_0(118124)))
			{
				try
				{
					Type type = exception_1.GetType();
					this.method_9(type);
					string value = Class521.smethod_0(18686);
					try
					{
						value = exception_1.Message;
					}
					catch
					{
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(118137), value);
					string text = exception_1.StackTrace.Trim();
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(118150), text);
					int num = text.IndexOf(' ');
					text = text.Substring(num + 1);
					num = text.IndexOf(Class521.smethod_0(95967));
					if (num != -1)
					{
						text = text.Substring(0, num);
					}
					this.xmlWriter_0.WriteAttributeString(Class521.smethod_0(118179), text);
					this.method_6(new Class540(exception_1, true));
				}
				catch
				{
				}
			}
		}

		// Token: 0x06002785 RID: 10117 RVA: 0x0000F2D2 File Offset: 0x0000D4D2
		internal void method_17(string string_10, object object_0)
		{
			this.dictionary_0.Add(string_10, object_0);
		}

		// Token: 0x06002786 RID: 10118 RVA: 0x0010C344 File Offset: 0x0010A544
		internal void method_18(string string_10, string string_11)
		{
			if (!File.Exists(string_11))
			{
				return;
			}
			Class536.Struct26 value = new Class536.Struct26(string_11);
			this.dictionary_1.Add(string_10, value);
		}

		// Token: 0x06002787 RID: 10119 RVA: 0x0010C370 File Offset: 0x0010A570
		internal bool method_19()
		{
			bool result;
			try
			{
				base.method_4(Enum35.const_0);
				byte[] array;
				try
				{
					array = this.method_13();
				}
				catch (Exception ex)
				{
					int num = -1;
					try
					{
						StackTrace stackTrace = new StackTrace(ex);
						if (stackTrace.FrameCount > 0)
						{
							num = stackTrace.GetFrame(stackTrace.FrameCount - 1).GetILOffset();
						}
					}
					catch
					{
					}
					base.method_3(Enum35.const_0, string.Format(Class521.smethod_0(118188), ex.Message, num));
					return false;
				}
				Class535.Class539 class539_ = new Class535.Class539(Class521.smethod_0(118221), Class521.smethod_0(46540), Class521.smethod_0(118246));
				result = base.method_1(array, class539_);
			}
			catch (ThreadAbortException)
			{
				result = false;
			}
			catch (Exception exception_)
			{
				this.method_20(new EventArgs34(exception_));
				result = false;
			}
			return result;
		}

		// Token: 0x140000BE RID: 190
		// (add) Token: 0x06002788 RID: 10120 RVA: 0x0010C464 File Offset: 0x0010A664
		// (remove) Token: 0x06002789 RID: 10121 RVA: 0x0010C49C File Offset: 0x0010A69C
		public event Delegate37 FatalException
		{
			[CompilerGenerated]
			add
			{
				Delegate37 @delegate = this.delegate37_0;
				Delegate37 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate37 value2 = (Delegate37)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate37>(ref this.delegate37_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate37 @delegate = this.delegate37_0;
				Delegate37 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate37 value2 = (Delegate37)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate37>(ref this.delegate37_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600278A RID: 10122 RVA: 0x0010C4D4 File Offset: 0x0010A6D4
		public void method_20(EventArgs34 eventArgs34_0)
		{
			Delegate37 @delegate = this.delegate37_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs34_0);
			}
		}

		// Token: 0x140000BF RID: 191
		// (add) Token: 0x0600278B RID: 10123 RVA: 0x0010C4F4 File Offset: 0x0010A6F4
		// (remove) Token: 0x0600278C RID: 10124 RVA: 0x0010C52C File Offset: 0x0010A72C
		public event EventHandler DebuggerLaunched
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600278D RID: 10125 RVA: 0x0010C564 File Offset: 0x0010A764
		private void method_21()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, EventArgs.Empty);
			}
		}

		// Token: 0x0600278E RID: 10126 RVA: 0x0010C588 File Offset: 0x0010A788
		internal void method_22()
		{
			try
			{
				string tempFileName = Path.GetTempFileName();
				this.method_23(tempFileName);
				Process.Start(Path.Combine(Class513.smethod_0(), Class521.smethod_0(118291)), Class521.smethod_0(118316) + tempFileName + Class521.smethod_0(105109));
				if (this.eventHandler_0 != null)
				{
					this.eventHandler_0(this, EventArgs.Empty);
				}
			}
			catch (ThreadAbortException)
			{
			}
			catch (Exception exception_)
			{
				this.method_20(new EventArgs34(exception_));
			}
		}

		// Token: 0x0600278F RID: 10127 RVA: 0x0010C620 File Offset: 0x0010A820
		internal bool method_23(string string_10)
		{
			bool result;
			try
			{
				byte[] array = this.method_13();
				byte[] array2;
				try
				{
					array2 = Class522.smethod_4(array);
				}
				catch
				{
					array2 = null;
				}
				byte[] array3 = Class534.smethod_0(array2, Class521.smethod_0(118345));
				FileStream fileStream = File.OpenWrite(string_10);
				byte[] bytes = Encoding.ASCII.GetBytes(Class521.smethod_0(117441));
				fileStream.Write(bytes, 0, bytes.Length);
				fileStream.Write(array3, 0, array3.Length);
				fileStream.Close();
				result = true;
			}
			catch (ThreadAbortException)
			{
				result = false;
			}
			catch (Exception)
			{
				result = false;
			}
			return result;
		}

		// Token: 0x04001388 RID: 5000
		private const string string_3 = "{bf13b64c-b3d2-4165-b3f5-7f852d4744cf}";

		// Token: 0x04001389 RID: 5001
		private const string string_4 = "{07572d6f-5375-47d5-8a8c-b5f0cbe5bad0}";

		// Token: 0x0400138A RID: 5002
		private const string string_5 = "{6d3806d4-1193-4601-a7df-2249c7f0014b}";

		// Token: 0x0400138B RID: 5003
		private const string string_6 = "{d316c294-ed40-4778-8b7b-29800a2dcbc3}";

		// Token: 0x0400138C RID: 5004
		private const string string_7 = "{a9035fc5-7ed1-4e0c-8962-dfcb1d508afc}";

		// Token: 0x0400138D RID: 5005
		private const string string_8 = "{73fbfb9b-41e7-4744-bf74-74b7c6c117c1}";

		// Token: 0x0400138E RID: 5006
		private readonly Exception exception_0;

		// Token: 0x0400138F RID: 5007
		private readonly Guid guid_0;

		// Token: 0x04001390 RID: 5008
		private readonly char[] char_0 = new char[0];

		// Token: 0x04001391 RID: 5009
		private readonly Dictionary<string, object> dictionary_0 = new Dictionary<string, object>();

		// Token: 0x04001392 RID: 5010
		private readonly Dictionary<string, Class536.Struct26> dictionary_1 = new Dictionary<string, Class536.Struct26>();

		// Token: 0x04001393 RID: 5011
		private XmlWriter xmlWriter_0;

		// Token: 0x04001394 RID: 5012
		private readonly List<Class540> list_0 = new List<Class540>();

		// Token: 0x04001395 RID: 5013
		private readonly List<string> list_1 = new List<string>();

		// Token: 0x04001396 RID: 5014
		private readonly Dictionary<string, int> dictionary_2 = new Dictionary<string, int>();

		// Token: 0x04001397 RID: 5015
		private readonly List<Class536.Struct27> list_2 = new List<Class536.Struct27>();

		// Token: 0x04001398 RID: 5016
		private readonly Dictionary<string, int> dictionary_3 = new Dictionary<string, int>();

		// Token: 0x04001399 RID: 5017
		private MemoryStream memoryStream_0;

		// Token: 0x0400139A RID: 5018
		private byte[] byte_0;

		// Token: 0x0400139B RID: 5019
		private const string string_9 = "SmartAssembly.exe";

		// Token: 0x0400139C RID: 5020
		[CompilerGenerated]
		private Delegate37 delegate37_0;

		// Token: 0x0400139D RID: 5021
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x020003F2 RID: 1010
		private struct Struct26
		{
			// Token: 0x06002790 RID: 10128 RVA: 0x0010C6C4 File Offset: 0x0010A8C4
			public Struct26(string string_3)
			{
				this.string_0 = string.Empty;
				this.string_1 = string.Empty;
				this.string_2 = string.Empty;
				this.int_0 = 0;
				try
				{
					FileInfo fileInfo = new FileInfo(string_3);
					this.string_0 = Path.GetFileName(string_3);
					this.int_0 = (int)fileInfo.Length;
					byte[] array = new byte[this.int_0];
					using (FileStream fileStream = File.Open(string_3, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
					{
						fileStream.Read(array, 0, this.int_0);
						fileStream.Close();
					}
					byte[] inArray;
					try
					{
						inArray = Class522.smethod_4(array);
					}
					catch
					{
						inArray = null;
					}
					this.string_1 = Convert.ToBase64String(inArray);
				}
				catch (Exception ex)
				{
					this.string_2 = ex.Message;
				}
			}

			// Token: 0x0400139E RID: 5022
			public readonly string string_0;

			// Token: 0x0400139F RID: 5023
			public readonly string string_1;

			// Token: 0x040013A0 RID: 5024
			public readonly string string_2;

			// Token: 0x040013A1 RID: 5025
			public readonly int int_0;
		}

		// Token: 0x020003F3 RID: 1011
		private struct Struct27
		{
			// Token: 0x06002791 RID: 10129 RVA: 0x0000F2E1 File Offset: 0x0000D4E1
			public Struct27(string string_2, string string_3)
			{
				this.string_0 = string_2;
				this.string_1 = string_3;
			}

			// Token: 0x040013A2 RID: 5026
			public readonly string string_0;

			// Token: 0x040013A3 RID: 5027
			public readonly string string_1;
		}

		// Token: 0x020003F4 RID: 1012
		private struct Struct28
		{
			// Token: 0x170006C9 RID: 1737
			// (get) Token: 0x06002792 RID: 10130 RVA: 0x0000F2F1 File Offset: 0x0000D4F1
			public bool IsEmpty
			{
				get
				{
					return this.string_0.Length == 0;
				}
			}

			// Token: 0x170006CA RID: 1738
			// (get) Token: 0x06002793 RID: 10131 RVA: 0x0000F301 File Offset: 0x0000D501
			public static Class536.Struct28 Empty
			{
				get
				{
					return new Class536.Struct28(string.Empty, string.Empty, string.Empty);
				}
			}

			// Token: 0x06002794 RID: 10132 RVA: 0x0000F317 File Offset: 0x0000D517
			private Struct28(string string_1, string string_2, string string_3)
			{
				this.string_0 = string_1;
				this.struct27_0 = new Class536.Struct27(string_2, string_3);
			}

			// Token: 0x040013A4 RID: 5028
			public string string_0;

			// Token: 0x040013A5 RID: 5029
			public Class536.Struct27 struct27_0;
		}
	}
}
