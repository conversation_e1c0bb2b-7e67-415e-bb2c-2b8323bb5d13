﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000038 RID: 56
	internal sealed class HiLowMarkTextObj : TextObj
	{
		// Token: 0x0600018A RID: 394 RVA: 0x000173C4 File Offset: 0x000155C4
		public HiLowMarkTextObj(ChartKLine chart, bool isHigh)
		{
			this.Chart = chart;
			this.IsHigh = isHigh;
			base.FontSpec.Size = this.method_1();
			base.FontSpec.FontColor = this.method_0();
			base.FontSpec.Border.IsVisible = false;
			base.FontSpec.Fill.IsVisible = false;
			base.FontSpec.StringAlignment = StringAlignment.Near;
			base.IsClippedToChartRect = true;
			base.ZOrder = ZOrder.A_InFront;
			this.Tag = HiLowMarkTextObj.string_0 + Class521.smethod_0(3210) + Utility.GetUniqueString(8);
			this.method_7();
		}

		// Token: 0x0600018B RID: 395 RVA: 0x00017484 File Offset: 0x00015684
		protected Color method_0()
		{
			Color result;
			if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
			{
				result = Color.Black;
			}
			else
			{
				result = Color.White;
			}
			return result;
		}

		// Token: 0x0600018C RID: 396 RVA: 0x000174B0 File Offset: 0x000156B0
		private float method_1()
		{
			float result = 11f;
			if (this.Chart != null && this.Chart.ChtCtrl != null)
			{
				result = this.Chart.vmethod_16();
			}
			return result;
		}

		// Token: 0x0600018D RID: 397 RVA: 0x000030C4 File Offset: 0x000012C4
		public void method_2()
		{
			base.FontSpec.FontColor = this.method_0();
		}

		// Token: 0x0600018E RID: 398 RVA: 0x000174EC File Offset: 0x000156EC
		public void method_3()
		{
			if (this.Chart != null && this.Chart.GraphPane != null)
			{
				for (int i = 0; i < this.Chart.GraphPane.GraphObjList.Count; i++)
				{
					GraphObj graphObj = this.Chart.GraphPane.GraphObjList[i];
					if (graphObj is TextObj && (string)graphObj.Tag == (string)this.Tag)
					{
						this.Chart.GraphPane.GraphObjList.Remove(graphObj);
					}
				}
			}
		}

		// Token: 0x0600018F RID: 399 RVA: 0x00017588 File Offset: 0x00015788
		private List<HisData> method_4()
		{
			List<HisData> list = new List<HisData>();
			if (this.Chart != null && this.Chart.SymbDataSet != null && this.Chart.SymbDataSet.HasValidDataSet)
			{
				int num = this.Chart.FirstItemIndex;
				int num2 = this.Chart.LastItemIndex;
				if (Base.UI.Form.IsJustSpanMoved && this.Chart.ChtCtrl != Base.UI.SelectedChtCtrl && this.Chart.IsInRetroMode)
				{
					num = this.Chart.ChtCtrl_KLine.IndexOfFirstItemShown;
					num2 = this.Chart.ChtCtrl_KLine.IndexOfLastItemShown;
				}
				SortedList<DateTime, HisData> periodHisDataList = this.Chart.HisDataPeriodSet.PeriodHisDataList;
				if (num > periodHisDataList.Count)
				{
					num = periodHisDataList.Count - 1;
					Class184.smethod_0(new Exception(Class521.smethod_0(3215)));
				}
				else if (num < 0)
				{
					num = 0;
					Class184.smethod_0(new Exception(Class521.smethod_0(3300)));
				}
				if (num2 > periodHisDataList.Count)
				{
					num2 = periodHisDataList.Count - 1;
					Class184.smethod_0(new Exception(Class521.smethod_0(3321)));
				}
				else if (num2 < 0)
				{
					num2 = 0;
					Class184.smethod_0(new Exception(Class521.smethod_0(3406)));
				}
				if (num2 >= num)
				{
					int num3 = num2 - num + 1;
					if (num3 > this.Chart.MaxSticksPerChart)
					{
						num3 = this.Chart.MaxSticksPerChart;
					}
					list = periodHisDataList.Values.Skip(num).Take(num3).ToList<HisData>();
					if (this.Chart.ChtCtrl.IsLastItemShown && this.Chart.ChtCtrl.method_36(new PeriodType?(Base.UI.CurrPeriodType), Base.UI.CurrPeriodUnits))
					{
						ChtCtrl chtCtrl = Base.UI.ChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(this.method_8));
						if (chtCtrl != null)
						{
							if (chtCtrl.SymbDataSet.HasValidDataSet && chtCtrl.LastItemShown != null)
							{
								int num4 = 0;
								SortedList<DateTime, HisData> periodHisDataList2 = chtCtrl.HisDataPeriodSet.PeriodHisDataList;
								if (list.Count > 1)
								{
									DateTime date = list[list.Count - 2].Date;
									int num5 = periodHisDataList2.IndexOfKey(date);
									if (num5 >= 0)
									{
										num4 = num5 + 1;
									}
								}
								if (chtCtrl.IndexOfLastItemShown >= num4)
								{
									List<HisData> source = periodHisDataList2.Values.Skip(num4).Take(chtCtrl.IndexOfLastItemShown - num4 + 1).ToList<HisData>();
									HisData hisData = list.Last<HisData>().Clone();
									hisData.High = source.Max(new Func<HisData, double>(HiLowMarkTextObj.<>c.<>9.method_0));
									hisData.Low = source.Min(new Func<HisData, double>(HiLowMarkTextObj.<>c.<>9.method_1));
									list.RemoveAt(list.Count - 1);
									list.Add(hisData);
								}
							}
						}
						else if (list.Count > 0)
						{
							HiLowMarkTextObj.Class18 @class = new HiLowMarkTextObj.Class18();
							@class.chtCtrl_0 = this.Chart.ChtCtrl;
							DateTime? dateTime = null;
							if (Base.UI.Form.IsJustSpanMoved)
							{
								@class.chtCtrl_0 = Base.UI.Form.SpanMoveChtCtrl;
								dateTime = new DateTime?(Base.UI.Form.LastSpanMoveDT.Value);
							}
							else
							{
								SymbDataSet symbDataSet;
								if (@class.chtCtrl_0.SymbDataSet.HasValidDataSet && @class.chtCtrl_0.SymbDataSet.CurrHisDataSet.ComingHisData != null)
								{
									symbDataSet = @class.chtCtrl_0.SymbDataSet;
								}
								else
								{
									symbDataSet = Base.Data.SymbDataSets.FirstOrDefault(new Func<SymbDataSet, bool>(@class.method_0));
								}
								if (symbDataSet != null)
								{
									dateTime = new DateTime?(symbDataSet.CurrHisDataSet.ComingHisData.Date);
								}
							}
							SortedList<DateTime, HisData> sortedList = null;
							if (this.Chart != null && this.Chart.ChtCtrl != null)
							{
								if (@class.chtCtrl_0 != null && @class.chtCtrl_0.Symbol == this.Chart.ChtCtrl.Symbol)
								{
									sortedList = @class.chtCtrl_0.HisDataList;
								}
								else
								{
									sortedList = this.Chart.ChtCtrl.HisDataList;
								}
							}
							if (dateTime != null && sortedList != null && sortedList.Count > 0)
							{
								bool flag = true;
								DateTime key;
								if (list.Count > 1)
								{
									key = list[list.Count - 2].Date;
								}
								else
								{
									key = sortedList.First<KeyValuePair<DateTime, HisData>>().Key;
									flag = false;
								}
								int num6 = sortedList.IndexOfKey(key);
								if (num6 >= 0)
								{
									int num7 = num6 + (flag ? 1 : 0);
									int num8 = sortedList.IndexOfKey(dateTime.Value);
									if (num8 >= 0 && num8 >= num7)
									{
										try
										{
											List<HisData> source2 = sortedList.Values.Skip(num7).Take(num8 - num7 + 1).ToList<HisData>();
											HisData hisData2 = list.Last<HisData>().Clone();
											hisData2.High = source2.Max(new Func<HisData, double>(HiLowMarkTextObj.<>c.<>9.method_2));
											hisData2.Low = source2.Min(new Func<HisData, double>(HiLowMarkTextObj.<>c.<>9.method_3));
											list.RemoveAt(list.Count - 1);
											list.Add(hisData2);
										}
										catch (Exception exception_)
										{
											Class184.smethod_0(exception_);
										}
									}
								}
							}
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000190 RID: 400 RVA: 0x00017B08 File Offset: 0x00015D08
		private int method_5()
		{
			int result;
			if (this.RangeHdList.Any<HisData>())
			{
				result = this.RangeHdList.Select(new Func<HisData, int, <>f__AnonymousType8<HisData, int>>(HiLowMarkTextObj.<>c.<>9.method_4)).Aggregate(new Func<<>f__AnonymousType8<HisData, int>, <>f__AnonymousType8<HisData, int>, <>f__AnonymousType8<HisData, int>>(HiLowMarkTextObj.<>c.<>9.method_5)).Index;
			}
			else
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x06000191 RID: 401 RVA: 0x00017B80 File Offset: 0x00015D80
		private int method_6()
		{
			int result;
			if (this.RangeHdList.Any<HisData>())
			{
				result = this.RangeHdList.Select(new Func<HisData, int, <>f__AnonymousType8<HisData, int>>(HiLowMarkTextObj.<>c.<>9.method_6)).Aggregate(new Func<<>f__AnonymousType8<HisData, int>, <>f__AnonymousType8<HisData, int>, <>f__AnonymousType8<HisData, int>>(HiLowMarkTextObj.<>c.<>9.method_7)).Index;
			}
			else
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x06000192 RID: 402 RVA: 0x00017BF8 File Offset: 0x00015DF8
		private bool method_7()
		{
			if (this.Chart != null && this.Chart.GraphPane != null && this.Chart.SymbDataSet != null && this.Chart.SymbDataSet.HasValidDataSet)
			{
				double num = double.NaN;
				SortedList<DateTime, HisData> periodHisDataList = this.Chart.HisDataPeriodSet.PeriodHisDataList;
				int num2;
				if (this.IsHigh)
				{
					num2 = this.MaxYIndexInChart;
					if (num2 >= 0)
					{
						int num3 = num2 + this.Chart.FirstItemIndex;
						if (num3 >= 0 && num3 < periodHisDataList.Count)
						{
							HisData hisData = this.RangeHdList[num2];
							num = hisData.High;
							DateTime date = hisData.Date;
						}
					}
				}
				else
				{
					num2 = this.MinYIndexInChart;
					if (num2 >= 0)
					{
						int num4 = num2 + this.Chart.FirstItemIndex;
						if (num4 >= 0 && num4 < periodHisDataList.Count)
						{
							HisData hisData2 = this.RangeHdList[num2];
							num = hisData2.Low;
							DateTime date2 = hisData2.Date;
						}
					}
				}
				if (!double.IsNaN(num))
				{
					AlignV alignV = this.IsHigh ? AlignV.Bottom : AlignV.Top;
					base.Location = new Location((double)num2, num, CoordType.AxisXYScale, AlignH.Left, alignV);
					base.Text = num.ToString(string.Format(Class521.smethod_0(3423), this.Chart.Symbol.DigitNb));
					this.Chart.GraphPane.GraphObjList.Add(this);
					return true;
				}
			}
			return false;
		}

		// Token: 0x1700006C RID: 108
		// (get) Token: 0x06000193 RID: 403 RVA: 0x00017D68 File Offset: 0x00015F68
		// (set) Token: 0x06000194 RID: 404 RVA: 0x000030D9 File Offset: 0x000012D9
		public ChartKLine Chart { get; set; }

		// Token: 0x1700006D RID: 109
		// (get) Token: 0x06000195 RID: 405 RVA: 0x00017D80 File Offset: 0x00015F80
		// (set) Token: 0x06000196 RID: 406 RVA: 0x000030E4 File Offset: 0x000012E4
		public bool IsHigh { get; set; }

		// Token: 0x1700006E RID: 110
		// (get) Token: 0x06000197 RID: 407 RVA: 0x00017D98 File Offset: 0x00015F98
		// (set) Token: 0x06000198 RID: 408 RVA: 0x000030EF File Offset: 0x000012EF
		private List<HisData> RangeHdList
		{
			get
			{
				if (this.list_0 == null)
				{
					this.list_0 = this.method_4();
				}
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x1700006F RID: 111
		// (get) Token: 0x06000199 RID: 409 RVA: 0x00017DC4 File Offset: 0x00015FC4
		// (set) Token: 0x0600019A RID: 410 RVA: 0x000030FA File Offset: 0x000012FA
		public int MaxYIndexInChart
		{
			get
			{
				if (this.int_0 == -2147483648)
				{
					this.int_0 = this.method_5();
				}
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x17000070 RID: 112
		// (get) Token: 0x0600019B RID: 411 RVA: 0x00017DF4 File Offset: 0x00015FF4
		// (set) Token: 0x0600019C RID: 412 RVA: 0x00003105 File Offset: 0x00001305
		public int MinYIndexInChart
		{
			get
			{
				if (this.int_1 == -2147483648)
				{
					this.int_1 = this.method_6();
				}
				return this.int_1;
			}
			set
			{
				this.int_1 = value;
			}
		}

		// Token: 0x0600019E RID: 414 RVA: 0x00017E24 File Offset: 0x00016024
		[CompilerGenerated]
		private bool method_8(ChtCtrl chtCtrl_0)
		{
			bool result;
			if (chtCtrl_0.Symbol == this.Chart.Symbol)
			{
				if (!chtCtrl_0.method_34(new PeriodType?(Base.UI.CurrPeriodType), Base.UI.CurrPeriodUnits))
				{
					result = chtCtrl_0.method_32(new PeriodType?(Base.UI.CurrPeriodType), Base.UI.CurrPeriodUnits);
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0400009B RID: 155
		public static readonly string string_0 = Class521.smethod_0(3432);

		// Token: 0x0400009C RID: 156
		[CompilerGenerated]
		private ChartKLine chartKLine_0;

		// Token: 0x0400009D RID: 157
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x0400009E RID: 158
		private List<HisData> list_0;

		// Token: 0x0400009F RID: 159
		private int int_0 = int.MinValue;

		// Token: 0x040000A0 RID: 160
		private int int_1 = int.MinValue;

		// Token: 0x02000039 RID: 57
		[CompilerGenerated]
		private sealed class Class18
		{
			// Token: 0x060001A0 RID: 416 RVA: 0x00017E7C File Offset: 0x0001607C
			internal bool method_0(SymbDataSet symbDataSet_0)
			{
				bool result;
				if (symbDataSet_0.CurrSymbol == this.chtCtrl_0.Symbol && symbDataSet_0.CurrHisDataSet != null)
				{
					result = (symbDataSet_0.CurrHisDataSet.ComingHisData != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040000A1 RID: 161
			public ChtCtrl chtCtrl_0;
		}
	}
}
