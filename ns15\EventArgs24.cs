﻿using System;

namespace ns15
{
	// Token: 0x0200027B RID: 635
	internal sealed class EventArgs24 : EventArgs
	{
		// Token: 0x06001B93 RID: 7059 RVA: 0x0000B6C1 File Offset: 0x000098C1
		public EventArgs24(DateTime dateTime_2, DateTime dateTime_3)
		{
			this.dateTime_0 = dateTime_2;
			this.dateTime_1 = dateTime_3;
		}

		// Token: 0x17000487 RID: 1159
		// (get) Token: 0x06001B94 RID: 7060 RVA: 0x000BFF3C File Offset: 0x000BE13C
		// (set) Token: 0x06001B95 RID: 7061 RVA: 0x0000B6D9 File Offset: 0x000098D9
		public DateTime OldDate
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				this.dateTime_0 = value;
			}
		}

		// Token: 0x17000488 RID: 1160
		// (get) Token: 0x06001B96 RID: 7062 RVA: 0x000BFF54 File Offset: 0x000BE154
		// (set) Token: 0x06001B97 RID: 7063 RVA: 0x0000B6E4 File Offset: 0x000098E4
		public DateTime NewDate
		{
			get
			{
				return this.dateTime_1;
			}
			set
			{
				this.dateTime_1 = value;
			}
		}

		// Token: 0x04000DA3 RID: 3491
		private DateTime dateTime_0;

		// Token: 0x04000DA4 RID: 3492
		private DateTime dateTime_1;
	}
}
