﻿using System;
using System.Runtime.CompilerServices;
using System.Threading;
using ns11;
using ns18;
using ns9;

namespace TEx.SIndicator
{
	// Token: 0x02000329 RID: 809
	public sealed class HToken
	{
		// Token: 0x170005E2 RID: 1506
		// (get) Token: 0x06002255 RID: 8789 RVA: 0x000F3334 File Offset: 0x000F1534
		// (set) Token: 0x06002256 RID: 8790 RVA: 0x0000DAA1 File Offset: 0x0000BCA1
		public int Col { get; private set; }

		// Token: 0x170005E3 RID: 1507
		// (get) Token: 0x06002257 RID: 8791 RVA: 0x000F334C File Offset: 0x000F154C
		// (set) Token: 0x06002258 RID: 8792 RVA: 0x0000DAAC File Offset: 0x0000BCAC
		public int Line { get; private set; }

		// Token: 0x170005E4 RID: 1508
		// (get) Token: 0x06002259 RID: 8793 RVA: 0x000F3364 File Offset: 0x000F1564
		// (set) Token: 0x0600225A RID: 8794 RVA: 0x0000DAB7 File Offset: 0x0000BCB7
		public Class442 Symbol { get; private set; }

		// Token: 0x0600225B RID: 8795 RVA: 0x0000DAC2 File Offset: 0x0000BCC2
		public HToken(int col, int line, Class442 symbol)
		{
			this.Col = col;
			this.Line = line;
			this.Symbol = symbol;
		}

		// Token: 0x140000A4 RID: 164
		// (add) Token: 0x0600225C RID: 8796 RVA: 0x000F337C File Offset: 0x000F157C
		// (remove) Token: 0x0600225D RID: 8797 RVA: 0x000F33B4 File Offset: 0x000F15B4
		public static event EventHandler WrongToken
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = HToken.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref HToken.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = HToken.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref HToken.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600225E RID: 8798 RVA: 0x000F33EC File Offset: 0x000F15EC
		public string method_0(string string_0)
		{
			this.method_1();
			return string.Format(Class521.smethod_0(102944), new object[]
			{
				this.Line,
				this.Col,
				this.Symbol.Name,
				string_0
			});
		}

		// Token: 0x0600225F RID: 8799 RVA: 0x0000DAE1 File Offset: 0x0000BCE1
		public void method_1()
		{
			if (HToken.eventHandler_0 != null)
			{
				HToken.eventHandler_0(this, new EventArgs31(this));
			}
		}

		// Token: 0x040010BA RID: 4282
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040010BB RID: 4283
		[CompilerGenerated]
		private int int_1;

		// Token: 0x040010BC RID: 4284
		[CompilerGenerated]
		private Class442 class442_0;

		// Token: 0x040010BD RID: 4285
		[CompilerGenerated]
		private static EventHandler eventHandler_0;
	}
}
