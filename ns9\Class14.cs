﻿using System;
using System.Runtime.CompilerServices;

namespace ns9
{
	// Token: 0x02000034 RID: 52
	internal sealed class Class14
	{
		// Token: 0x0600016D RID: 365 RVA: 0x00002FDF File Offset: 0x000011DF
		public Class14(DateTime dateTime_2, DateTime dateTime_3)
		{
			this.BeginDate = dateTime_2;
			this.EndDate = dateTime_3;
		}

		// Token: 0x17000063 RID: 99
		// (get) Token: 0x0600016E RID: 366 RVA: 0x000170F0 File Offset: 0x000152F0
		// (set) Token: 0x0600016F RID: 367 RVA: 0x00002FF7 File Offset: 0x000011F7
		public DateTime BeginDate { get; set; }

		// Token: 0x17000064 RID: 100
		// (get) Token: 0x06000170 RID: 368 RVA: 0x00017108 File Offset: 0x00015308
		// (set) Token: 0x06000171 RID: 369 RVA: 0x00003002 File Offset: 0x00001202
		public DateTime EndDate { get; set; }

		// Token: 0x0400008F RID: 143
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x04000090 RID: 144
		[CompilerGenerated]
		private DateTime dateTime_1;
	}
}
