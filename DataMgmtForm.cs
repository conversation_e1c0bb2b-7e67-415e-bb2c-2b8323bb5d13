﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.Editors;
using DevComponents.Editors.DateTimeAdv;
using ns11;
using ns12;
using ns18;
using ns20;
using ns26;
using ns30;
using ns7;
using TEx.Comn;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000156 RID: 342
	public sealed partial class DataMgmtForm : Form
	{
		// Token: 0x06000CF8 RID: 3320 RVA: 0x0004DBCC File Offset: 0x0004BDCC
		public DataMgmtForm()
		{
			this.method_35();
			base.Load += this.DataMgmtForm_Load;
			base.FormClosed += this.DataMgmtForm_FormClosed;
			this.button_3.Click += this.button_3_Click;
		}

		// Token: 0x06000CF9 RID: 3321 RVA: 0x0004DC24 File Offset: 0x0004BE24
		private void DataMgmtForm_Load(object sender, EventArgs e)
		{
			DataMgmtForm.Class196 @class = new DataMgmtForm.Class196();
			this.list_0 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			if (string.IsNullOrEmpty(Base.UI.Form.LastHisDataExportDir))
			{
				this.string_0 = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			}
			else
			{
				this.string_0 = Base.UI.Form.LastHisDataExportDir;
			}
			List<ExchgHouse> list = Base.Data.smethod_87();
			this.comboBox_0.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_0.DisplayMember = Class521.smethod_0(16303);
			this.comboBox_0.DataSource = list;
			this.comboBox_0.SelectedIndexChanged += this.comboBox_0_SelectedIndexChanged;
			@class.stkSymbol_0 = null;
			@class.exchgHouse_0 = null;
			if (Base.Data.SymbDataSets.Exists(new Predicate<SymbDataSet>(DataMgmtForm.<>c.<>9.method_0)))
			{
				SymbDataSet symbDataSet = Base.Data.SymbDataSets.FirstOrDefault(new Func<SymbDataSet, bool>(DataMgmtForm.<>c.<>9.method_1));
				@class.stkSymbol_0 = symbDataSet.CurrSymbol;
				@class.exchgHouse_0 = list.SingleOrDefault(new Func<ExchgHouse, bool>(@class.method_0));
				this.comboBox_0.SelectedIndex = list.IndexOf(@class.exchgHouse_0);
			}
			if (@class.exchgHouse_0 == null)
			{
				@class.exchgHouse_0 = list.First<ExchgHouse>();
			}
			List<StkSymbol> list2 = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1)).ToList<StkSymbol>();
			this.comboBox_1.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_1.DisplayMember = Class521.smethod_0(16316);
			this.comboBox_1.DataSource = list2;
			this.comboBox_1.SelectedIndexChanged += this.comboBox_1_SelectedIndexChanged;
			if (@class.stkSymbol_0 != null)
			{
				int num = list2.IndexOf(@class.stkSymbol_0);
				if (num >= 0)
				{
					this.comboBox_1.SelectedIndex = num;
				}
			}
			else
			{
				@class.stkSymbol_0 = list2.First<StkSymbol>();
			}
			this.method_17(@class.stkSymbol_0);
			this.comboBox_2.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_2.SelectedIndexChanged += this.comboBox_2_SelectedIndexChanged;
			this.comboBox_3.DropDownStyle = ComboBoxStyle.DropDownList;
			Base.UI.smethod_80(this.dateTimeInput_1);
			Base.UI.smethod_80(this.dateTimeInput_0);
			this.dateTimeInput_0.ValueChanged += this.dateTimeInput_0_ValueChanged;
			this.dateTimeInput_0.Leave += this.dateTimeInput_0_Leave;
			this.button_1.Click += this.button_1_Click;
			this.button_0.Click += this.button_0_Click;
			this.comboBox_3.DropDownStyle = ComboBoxStyle.DropDownList;
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 1)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16325));
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 3)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16338));
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 5)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16351));
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 15)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16364));
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 30)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16377));
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 60)
			{
				this.comboBox_3.Items.Add(Class521.smethod_0(16390));
			}
			this.comboBox_3.Items.Add(Class521.smethod_0(12209));
			this.comboBox_3.Items.Add(Class521.smethod_0(12228));
			this.comboBox_3.Items.Add(Class521.smethod_0(12247));
			this.comboBox_3.SelectedIndex = 0;
			this.comboBox_2.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_2.Items.Add(Class521.smethod_0(16403));
			this.comboBox_2.Items.Add(Class521.smethod_0(16436));
			this.comboBox_2.SelectedIndex = 0;
			this.checkBox_0.Checked = true;
			this.textBox_0.Text = this.string_0 + Class521.smethod_0(16469);
			this.label_7.Text = string.Empty;
			this.progressBar_0.Visible = false;
			this.backgroundWorker_0 = new BackgroundWorker();
			this.backgroundWorker_0.WorkerReportsProgress = true;
			this.backgroundWorker_0.WorkerSupportsCancellation = true;
			this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
			this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
			this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
			List<Account> currAccounts = Base.Acct.CurrAccounts;
			this.comboBox_5.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_5.DisplayMember = Class521.smethod_0(16490);
			this.comboBox_5.DataSource = currAccounts;
			for (int i = 0; i < currAccounts.Count; i++)
			{
				if (currAccounts[i] == Base.Acct.CurrAccount)
				{
					this.comboBox_5.SelectedIndex = i;
				}
			}
			this.comboBox_5.SelectedIndexChanged += this.comboBox_5_SelectedIndexChanged;
			this.comboBox_4.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_4.DisplayMember = Class521.smethod_0(16316);
			this.comboBox_4.SelectedIndexChanged += this.comboBox_4_SelectedIndexChanged;
			this.method_18();
			this.checkBox_2.CheckedChanged += this.checkBox_2_CheckedChanged;
			this.comboBox_6.DisplayMember = Class521.smethod_0(16490);
			this.comboBox_6.DropDownStyle = ComboBoxStyle.DropDownList;
			this.button_8.Click += this.button_8_Click;
			this.button_7.Click += this.button_7_Click;
			this.button_6.Click += this.button_6_Click;
			this.button_5.Click += this.button_5_Click;
			this.button_4.Click += this.button_4_Click;
			if (string.IsNullOrEmpty(Base.UI.Form.LastAcctTransExportDir))
			{
				this.string_1 = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			}
			else
			{
				this.string_1 = Base.UI.Form.LastAcctTransExportDir;
			}
			if (!string.IsNullOrEmpty(Base.UI.Form.LastTransImportFilePath))
			{
				this.string_4 = Base.UI.Form.LastTransImportFilePath;
			}
			this.textBox_1.Text = this.string_1 + Class521.smethod_0(16503);
			this.textBox_2.Text = this.string_4;
			this.button_11.Click += this.button_11_Click;
			this.button_9.Click += this.button_9_Click;
			this.method_2();
			this.method_34(true);
			this.method_32();
			this.label_13.Text = string.Empty;
			CfmmcRecImporter.LoggingIn += this.method_23;
			CfmmcRecImporter.LoginSuccess += this.method_24;
			CfmmcRecImporter.StartDownloadRecs += this.method_25;
			CfmmcRecImporter.NotifyDnRecIndex += this.method_26;
			CfmmcRecImporter.RecImportSuccess += this.method_27;
			CfmmcRecImporter.RecImportFailed += this.method_28;
			CfmmcRecImporter.NoRecNeedToDnldDetected += this.method_30;
			this.button_12.Click += this.button_12_Click;
			this.button_14.Click += this.button_14_Click;
			this.button_13.Click += this.button_13_Click;
			this.button_10.Click += this.button_10_Click;
			this.comboBox_7.SelectedIndexChanged += this.comboBox_7_SelectedIndexChanged;
			this.dateTimePicker_0.ValueChanged += this.dateTimePicker_0_ValueChanged;
			if (this.list_2 == null)
			{
				this.list_2 = new List<string>();
			}
			if (this.list_3 == null)
			{
				this.list_3 = new List<string>();
			}
			List<string> list3 = Class466.smethod_12();
			if (list3 != null && list3.Any<string>())
			{
				this.list_2.Clear();
				this.list_3.Clear();
				for (int j = 0; j < list3.Count; j++)
				{
					string[] array = list3[j].Split(new char[]
					{
						' '
					});
					if (array.Length == 2)
					{
						this.list_2.Add(array[0]);
						this.list_3.Add(array[1]);
					}
				}
			}
			this.method_3();
			this.labelX_0.ForeColor = Color.Black;
			if (TApp.IsTrialUser)
			{
				this.labelX_0.Text = Class521.smethod_0(16524);
			}
			LabelX labelX = this.labelX_0;
			labelX.Text = labelX.Text + Environment.NewLine + Environment.NewLine + Class521.smethod_0(16954);
		}

		// Token: 0x06000CFA RID: 3322 RVA: 0x0004E558 File Offset: 0x0004C758
		private void DataMgmtForm_FormClosed(object sender, FormClosedEventArgs e)
		{
			CfmmcRecImporter.LoggingIn -= this.method_23;
			CfmmcRecImporter.LoginSuccess -= this.method_24;
			CfmmcRecImporter.StartDownloadRecs -= this.method_25;
			CfmmcRecImporter.NotifyDnRecIndex -= this.method_26;
			CfmmcRecImporter.RecImportSuccess -= this.method_27;
			CfmmcRecImporter.RecImportFailed -= this.method_28;
			CfmmcRecImporter.NoRecNeedToDnldDetected -= this.method_30;
			CfmmcRecImporter.smethod_44(true);
			this.button_10.Click -= this.button_10_Click;
		}

		// Token: 0x06000CFB RID: 3323 RVA: 0x0004E5FC File Offset: 0x0004C7FC
		private void button_3_Click(object sender, EventArgs e)
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (this.radioButton_3.Checked)
			{
				cfmmcAutoDnldConfig.AutoDownOnStartup = true;
			}
			else
			{
				cfmmcAutoDnldConfig.AutoDownOnStartup = false;
			}
			if (this.radioButton_2.Checked)
			{
				cfmmcAutoDnldConfig.AutoDownPeriodly = true;
				if (this.radioButton_1.Checked)
				{
					cfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每天;
				}
				else
				{
					cfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每周;
				}
				int num = this.comboBox_8.SelectedIndex - 1;
				if (num < 0)
				{
					num = 6;
				}
				cfmmcAutoDnldConfig.WklyDnldDayOfWeek = (DayOfWeek)num;
			}
			else
			{
				cfmmcAutoDnldConfig.AutoDownPeriodly = false;
			}
			Base.UI.smethod_47();
			CfmmcRecImporter.smethod_12();
		}

		// Token: 0x06000CFC RID: 3324 RVA: 0x0004E694 File Offset: 0x0004C894
		private void button_12_Click(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser && this.comboBox_7.Items.Count > 0)
			{
				MessageBox.Show(Class521.smethod_0(17244), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				Form7 form = new Form7();
				form.RecAdded += this.method_0;
				form.ShowDialog();
			}
		}

		// Token: 0x06000CFD RID: 3325 RVA: 0x0004E6FC File Offset: 0x0004C8FC
		private void button_14_Click(object sender, EventArgs e)
		{
			if (this.comboBox_7.Items.Count > 0)
			{
				Form7 form = new Form7(Class466.smethod_11(((this.comboBox_7.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct).ID));
				form.RecUpdated += this.method_1;
				form.ShowDialog();
			}
		}

		// Token: 0x06000CFE RID: 3326 RVA: 0x0004E760 File Offset: 0x0004C960
		private void button_13_Click(object sender, EventArgs e)
		{
			if (this.comboBox_7.Items.Count > 0)
			{
				CfmmcAcct cfmmcAcct = (this.comboBox_7.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct;
				if (MessageBox.Show(Class521.smethod_0(17382) + cfmmcAcct.ID + Class521.smethod_0(17431), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					Class466.smethod_7(cfmmcAcct);
					this.method_2();
					this.method_3();
				}
			}
		}

		// Token: 0x06000CFF RID: 3327 RVA: 0x00005C92 File Offset: 0x00003E92
		private void method_0(object sender, EventArgs e)
		{
			this.method_2();
			this.method_3();
		}

		// Token: 0x06000D00 RID: 3328 RVA: 0x00005CA2 File Offset: 0x00003EA2
		private void method_1(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06000D01 RID: 3329 RVA: 0x0004E7E4 File Offset: 0x0004C9E4
		private void method_2()
		{
			ComboBox comboBox = this.comboBox_7;
			comboBox.Items.Clear();
			List<CfmmcAcct> list = Class466.smethod_9();
			if (list != null && list.Any<CfmmcAcct>())
			{
				foreach (CfmmcAcct cfmmcAcct in list)
				{
					if (cfmmcAcct.BindingAccts != null)
					{
						if (cfmmcAcct.BindingAccts.Exists(new Predicate<BindingAcct>(DataMgmtForm.<>c.<>9.method_2)))
						{
							TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
							comboBoxItem.Text = cfmmcAcct.ID;
							comboBoxItem.Value = cfmmcAcct;
							comboBox.Items.Add(comboBoxItem);
						}
					}
				}
				if (comboBox.Items.Count > 0)
				{
					comboBox.SelectedIndex = 0;
				}
			}
		}

		// Token: 0x06000D02 RID: 3330 RVA: 0x0004E8CC File Offset: 0x0004CACC
		private void method_3()
		{
			string str = Class521.smethod_0(17444);
			int num = 0;
			if (this.comboBox_7.Items.Count > 0)
			{
				CfmmcAcct cfmmcAcct = Class466.smethod_11(((this.comboBox_7.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct).ID);
				if (!string.IsNullOrEmpty(cfmmcAcct.Note))
				{
					str = cfmmcAcct.Note;
				}
				List<List<string>> list = Class466.smethod_15(cfmmcAcct.ID);
				if (list != null)
				{
					num = list.Count;
				}
			}
			this.label_15.Text = Class521.smethod_0(5836) + str;
			this.label_16.Text = Class521.smethod_0(17457) + num.ToString();
		}

		// Token: 0x06000D03 RID: 3331 RVA: 0x0004E988 File Offset: 0x0004CB88
		private void button_1_Click(object sender, EventArgs e)
		{
			if (this.button_1.Text == Class521.smethod_0(17486))
			{
				try
				{
					string text = this.textBox_0.Text;
					if (string.IsNullOrEmpty(text))
					{
						return;
					}
					if (!text.ToLower().EndsWith(Class521.smethod_0(17495)))
					{
						text += Class521.smethod_0(17495);
					}
					FileInfo fileInfo;
					try
					{
						fileInfo = new FileInfo(text);
					}
					catch
					{
						MessageBox.Show(Class521.smethod_0(17504), Class521.smethod_0(7730), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						return;
					}
					if (!fileInfo.Directory.Exists)
					{
						if (MessageBox.Show(Class521.smethod_0(17585), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						fileInfo.Directory.Create();
					}
					if (fileInfo.Exists && MessageBox.Show(Class521.smethod_0(17658), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
					{
						return;
					}
					this.method_16(false);
					Struct4 @struct = default(Struct4);
					@struct.stkSymbol_0 = (this.comboBox_1.SelectedItem as StkSymbol);
					@struct.dateTime_0 = this.dateTimeInput_1.Value;
					@struct.dateTime_1 = this.dateTimeInput_0.Value;
					@struct.string_1 = text;
					@struct.string_0 = ((this.comboBox_2.SelectedIndex == 0) ? Class521.smethod_0(4736) : Class521.smethod_0(3636));
					@struct.bool_0 = this.checkBox_0.Checked;
					@struct.bool_1 = this.checkBox_1.Checked;
					@struct.periodType_0 = this.method_5();
					@struct.nullable_0 = this.method_6();
					while (!this.backgroundWorker_0.IsBusy)
					{
						this.backgroundWorker_0.RunWorkerAsync(@struct);
					}
					return;
				}
				catch (Exception ex)
				{
					this.method_4(Class521.smethod_0(17715), Class521.smethod_0(17760) + ex.Message);
					return;
				}
			}
			this.backgroundWorker_0.CancelAsync();
		}

		// Token: 0x06000D04 RID: 3332 RVA: 0x00005CAC File Offset: 0x00003EAC
		private void method_4(string string_5, string string_6)
		{
			MessageBox.Show(string_5 + Environment.NewLine + Environment.NewLine + string_6, Class521.smethod_0(17781), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06000D05 RID: 3333 RVA: 0x0004EBD4 File Offset: 0x0004CDD4
		private PeriodType method_5()
		{
			string text = this.comboBox_3.Text;
			PeriodType result;
			if (text == Class521.smethod_0(12209))
			{
				result = PeriodType.ByDay;
			}
			else if (text == Class521.smethod_0(12228))
			{
				result = PeriodType.ByWeek;
			}
			else if (text == Class521.smethod_0(12247))
			{
				result = PeriodType.ByMonth;
			}
			else
			{
				result = PeriodType.ByMins;
			}
			return result;
		}

		// Token: 0x06000D06 RID: 3334 RVA: 0x0004EC38 File Offset: 0x0004CE38
		private int? method_6()
		{
			string text = this.comboBox_3.Text;
			int? result = null;
			if (text.EndsWith(Class521.smethod_0(17790)))
			{
				result = new int?(Convert.ToInt32(text.Replace(Class521.smethod_0(17790), Class521.smethod_0(1449))));
			}
			return result;
		}

		// Token: 0x06000D07 RID: 3335 RVA: 0x0004EC98 File Offset: 0x0004CE98
		private void method_7(TabPage tabPage_3, bool bool_0)
		{
			foreach (object obj in tabPage_3.Controls)
			{
				((Control)obj).Enabled = bool_0;
			}
		}

		// Token: 0x06000D08 RID: 3336 RVA: 0x0004ECF4 File Offset: 0x0004CEF4
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			BackgroundWorker backgroundWorker = sender as BackgroundWorker;
			Struct4 @struct = (Struct4)e.Argument;
			DateTime dateTime_ = @struct.dateTime_0;
			DateTime dateTime_2 = @struct.dateTime_1;
			StkSymbol stkSymbol_ = @struct.stkSymbol_0;
			string text = @struct.string_1;
			bool bool_ = @struct.bool_0;
			bool bool_2 = @struct.bool_1;
			string text2 = @struct.string_0;
			PeriodType periodType_ = @struct.periodType_0;
			int? nullable_ = @struct.nullable_0;
			if (DataMgmtForm.smethod_0(text))
			{
				MessageBox.Show(Class521.smethod_0(17799), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				this.method_10(backgroundWorker, 0, Class521.smethod_0(4654));
				SortedList<DateTime, HisData> sortedList = Base.Data.smethod_59(stkSymbol_, dateTime_, dateTime_2);
				if (backgroundWorker.CancellationPending)
				{
					e.Cancel = true;
				}
				else if (sortedList != null && sortedList.Count >= 1)
				{
					this.method_10(backgroundWorker, 100, Class521.smethod_0(17929));
					SortedList<DateTime, HisData> sortedList2 = new SortedList<DateTime, HisData>();
					if (periodType_ == PeriodType.ByMins)
					{
						if (nullable_.Value == 1)
						{
							sortedList2 = sortedList;
						}
						else
						{
							if (nullable_.Value < 60)
							{
								this.method_10(backgroundWorker, 0, Class521.smethod_0(17958));
								int num = Convert.ToInt32((dateTime_2.Date - dateTime_.Date).TotalDays);
								int num2 = 0;
								DateTime dateTime = dateTime_;
								for (;;)
								{
									num2 += 30;
									DateTime dateTime_3 = dateTime.AddDays(30.0);
									if (dateTime_3.Date > dateTime_2.Date)
									{
										dateTime_3 = dateTime_2;
									}
									SortedList<DateTime, HisData> sortedList3 = null;
									try
									{
										sortedList3 = Base.Data.smethod_46(sortedList, stkSymbol_, dateTime, dateTime_3, nullable_.Value, null);
									}
									catch
									{
										continue;
									}
									if (sortedList3 != null && sortedList3.Any<KeyValuePair<DateTime, HisData>>())
									{
										foreach (KeyValuePair<DateTime, HisData> keyValuePair in new HisDataPeriodSet(Base.Data.smethod_50(stkSymbol_.ID, true, false), sortedList3, periodType_, nullable_).PeriodHisDataList)
										{
											sortedList2.Add(keyValuePair.Key, keyValuePair.Value);
										}
										if (num2 > num)
										{
											num2 = num;
										}
										if (backgroundWorker.CancellationPending)
										{
											goto IL_288;
										}
										this.method_11(backgroundWorker, Class521.smethod_0(17958), new int?(num2), new int?(num));
										dateTime = dateTime_3.AddDays(1.0);
										if (dateTime > dateTime_2)
										{
											break;
										}
									}
									else
									{
										dateTime = dateTime_3.AddDays(1.0);
										if (dateTime > dateTime_2)
										{
											break;
										}
									}
								}
								goto IL_303;
								IL_288:
								e.Cancel = true;
								return;
							}
							if (nullable_.Value == 60)
							{
								sortedList2 = this.method_8(stkSymbol_.ID);
							}
						}
					}
					else
					{
						this.method_10(backgroundWorker, 0, Class521.smethod_0(17958));
						SortedList<DateTime, HisData> hisDataList = this.method_8(stkSymbol_.ID);
						if (backgroundWorker.CancellationPending)
						{
							e.Cancel = true;
							return;
						}
						sortedList2 = new HisDataPeriodSet(stkSymbol_.ID, hisDataList, periodType_, null).PeriodHisDataList;
					}
					IL_303:
					if (backgroundWorker.CancellationPending)
					{
						e.Cancel = true;
					}
					else
					{
						using (StreamWriter streamWriter = new StreamWriter(text, false, Encoding.GetEncoding(Class521.smethod_0(17987))))
						{
							if (bool_)
							{
								streamWriter.WriteLine(string.Concat(new string[]
								{
									Class521.smethod_0(9617),
									text2,
									Class521.smethod_0(17996),
									text2,
									Class521.smethod_0(18005),
									text2,
									Class521.smethod_0(18014),
									text2,
									Class521.smethod_0(18023),
									text2,
									Class521.smethod_0(18032),
									text2,
									Class521.smethod_0(18037),
									text2,
									Class521.smethod_0(18046)
								}));
							}
							for (int i = 0; i < sortedList2.Count; i++)
							{
								HisData hisData = sortedList2.Values[i];
								string text3 = bool_2 ? hisData.Date.ToString(Class521.smethod_0(4938)) : (hisData.Date.ToString(Class521.smethod_0(1702)) + text2 + hisData.Date.ToString(Class521.smethod_0(18055)));
								string value = string.Concat(new object[]
								{
									text3,
									text2,
									hisData.Open,
									text2,
									hisData.Close,
									text2,
									hisData.High,
									text2,
									hisData.Low,
									text2,
									hisData.Volume,
									text2,
									hisData.Amount
								});
								streamWriter.WriteLine(value);
								if (backgroundWorker.CancellationPending)
								{
									e.Cancel = true;
									goto IL_592;
								}
								if (Utility.CanExactDiv(i + 1, 100))
								{
									this.method_11(backgroundWorker, Class521.smethod_0(18068), new int?(i + 1), new int?(sortedList2.Count));
								}
							}
						}
						this.method_11(backgroundWorker, Class521.smethod_0(18097) + sortedList2.Count + Class521.smethod_0(18130), new int?(sortedList2.Count), new int?(sortedList2.Count));
						IL_592:;
					}
				}
				else
				{
					this.method_12(backgroundWorker, Class521.smethod_0(17872), Class521.smethod_0(1449));
				}
			}
		}

		// Token: 0x06000D09 RID: 3337 RVA: 0x0004F300 File Offset: 0x0004D500
		private SortedList<DateTime, HisData> method_8(int int_0)
		{
			UsrStkMeta usrStkMeta_ = Base.Data.smethod_90(int_0);
			return Base.Data.smethod_50(int_0, true, false).method_52(usrStkMeta_).PeriodHisDataList;
		}

		// Token: 0x06000D0A RID: 3338 RVA: 0x00005CD4 File Offset: 0x00003ED4
		private void method_9(BackgroundWorker backgroundWorker_1, string string_5)
		{
			this.method_10(backgroundWorker_1, -1, string_5);
		}

		// Token: 0x06000D0B RID: 3339 RVA: 0x00005CE1 File Offset: 0x00003EE1
		private void method_10(BackgroundWorker backgroundWorker_1, int int_0, string string_5)
		{
			backgroundWorker_1.ReportProgress(int_0, this.method_14(string.Empty, string_5));
		}

		// Token: 0x06000D0C RID: 3340 RVA: 0x00005CFD File Offset: 0x00003EFD
		private void method_11(BackgroundWorker backgroundWorker_1, string string_5, int? nullable_0, int? nullable_1)
		{
			this.method_13(backgroundWorker_1, string.Empty, string_5, nullable_0, nullable_1);
		}

		// Token: 0x06000D0D RID: 3341 RVA: 0x00005D11 File Offset: 0x00003F11
		private void method_12(BackgroundWorker backgroundWorker_1, string string_5, string string_6)
		{
			backgroundWorker_1.ReportProgress(-1, this.method_14(string_5, string_6));
		}

		// Token: 0x06000D0E RID: 3342 RVA: 0x00005D29 File Offset: 0x00003F29
		private void method_13(BackgroundWorker backgroundWorker_1, string string_5, string string_6, int? nullable_0, int? nullable_1)
		{
			backgroundWorker_1.ReportProgress(-1, this.method_15(string_5, string_6, nullable_0, nullable_1));
		}

		// Token: 0x06000D0F RID: 3343 RVA: 0x0004F32C File Offset: 0x0004D52C
		private Struct5 method_14(string string_5, string string_6)
		{
			return this.method_15(string_5, string_6, null, null);
		}

		// Token: 0x06000D10 RID: 3344 RVA: 0x0004F358 File Offset: 0x0004D558
		private Struct5 method_15(string string_5, string string_6, int? nullable_0, int? nullable_1)
		{
			return new Struct5
			{
				string_0 = string_5,
				string_1 = string_6,
				nullable_0 = nullable_0,
				nullable_1 = nullable_1
			};
		}

		// Token: 0x06000D11 RID: 3345 RVA: 0x0004F394 File Offset: 0x0004D594
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			if (e.UserState != null)
			{
				Struct5 @struct = (Struct5)e.UserState;
				if (!string.IsNullOrEmpty(@struct.string_0))
				{
					MessageBox.Show(@struct.string_0, Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					if (this.progressBar_0.Visible)
					{
						this.progressBar_0.Visible = false;
					}
					this.label_7.Text = string.Empty;
					return;
				}
				if (!string.IsNullOrEmpty(@struct.string_1))
				{
					this.label_7.Text = @struct.string_1;
					if (!this.label_7.Visible)
					{
						this.label_7.Visible = true;
					}
					this.label_7.Refresh();
				}
				if (@struct.nullable_0 != null)
				{
					if (this.progressBar_0.Minimum != 0)
					{
						this.progressBar_0.Minimum = 0;
					}
					if (this.progressBar_0.Maximum != @struct.nullable_1.Value)
					{
						this.progressBar_0.Maximum = @struct.nullable_1.Value;
					}
					this.progressBar_0.Value = @struct.nullable_0.Value;
					if (!this.progressBar_0.Visible)
					{
						this.progressBar_0.Visible = true;
					}
				}
			}
			int progressPercentage = e.ProgressPercentage;
			if (progressPercentage >= 0 && this.progressBar_0.Value != progressPercentage)
			{
				if (this.progressBar_0.Maximum != 100)
				{
					this.progressBar_0.Maximum = 100;
				}
				this.progressBar_0.Value = progressPercentage;
				if (!this.progressBar_0.Visible)
				{
					this.progressBar_0.Visible = true;
				}
			}
		}

		// Token: 0x06000D12 RID: 3346 RVA: 0x0004F534 File Offset: 0x0004D734
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			Exception error = e.Error;
			if (e.Cancelled)
			{
				this.method_16(true);
				this.label_7.Text = string.Empty;
				this.progressBar_0.Visible = false;
			}
			else
			{
				this.method_16(true);
				Base.UI.Form.LastHisDataExportDir = Path.GetDirectoryName(this.textBox_0.Text);
			}
		}

		// Token: 0x06000D13 RID: 3347 RVA: 0x0004F598 File Offset: 0x0004D798
		private void method_16(bool bool_0)
		{
			if (bool_0)
			{
				this.button_1.Text = Class521.smethod_0(17486);
			}
			else
			{
				this.button_1.Text = Class521.smethod_0(5783);
			}
			this.groupBox_0.Enabled = bool_0;
			this.groupBox_1.Enabled = bool_0;
			this.groupBox_3.Enabled = bool_0;
			this.button_0.Enabled = bool_0;
			this.button_3.Enabled = bool_0;
			this.method_7(this.tabPage_0, bool_0);
		}

		// Token: 0x06000D14 RID: 3348 RVA: 0x0004F620 File Offset: 0x0004D820
		private void comboBox_0_SelectedIndexChanged(object sender, EventArgs e)
		{
			DataMgmtForm.Class197 @class = new DataMgmtForm.Class197();
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_0.SelectedItem;
			this.comboBox_1.DataSource = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_0)).ToList<StkSymbol>();
		}

		// Token: 0x06000D15 RID: 3349 RVA: 0x0004F678 File Offset: 0x0004D878
		private void comboBox_1_SelectedIndexChanged(object sender, EventArgs e)
		{
			StkSymbol stkSymbol_ = (StkSymbol)this.comboBox_1.SelectedItem;
			this.method_17(stkSymbol_);
			this.label_7.Text = string.Empty;
			this.progressBar_0.Visible = false;
		}

		// Token: 0x06000D16 RID: 3350 RVA: 0x0004F6BC File Offset: 0x0004D8BC
		private void method_17(StkSymbol stkSymbol_0)
		{
			UsrStkMeta usrStkMeta = Base.Data.smethod_90(stkSymbol_0.ID);
			if (usrStkMeta == null)
			{
				this.dateTimeInput_1.Enabled = false;
				this.dateTimeInput_0.Enabled = false;
				this.button_1.Enabled = false;
			}
			else
			{
				this.dateTimeInput_1.Enabled = true;
				this.dateTimeInput_0.Enabled = true;
				this.button_1.Enabled = true;
				DateTime value = this.dateTimeInput_1.Value;
				if (this.dateTimeInput_1.Value == DateTime.MinValue || this.dateTimeInput_1.Value.Date < usrStkMeta.BeginDate.Value.Date)
				{
					this.dateTimeInput_1.Value = usrStkMeta.BeginDate.Value;
				}
				this.dateTimeInput_1.MinDate = usrStkMeta.BeginDate.Value;
				this.dateTimeInput_1.MaxDate = usrStkMeta.EndDate.Value;
				DateTime value2 = this.dateTimeInput_0.Value;
				if (this.dateTimeInput_0.Value == DateTime.MinValue || this.dateTimeInput_0.Value.Date > usrStkMeta.EndDate.Value.Date)
				{
					this.dateTimeInput_0.Value = usrStkMeta.EndDate.Value;
				}
				this.dateTimeInput_0.MinDate = usrStkMeta.BeginDate.Value;
				this.dateTimeInput_0.MaxDate = usrStkMeta.EndDate.Value;
				if (this.dateTimeInput_0.Value < this.dateTimeInput_1.Value)
				{
					this.dateTimeInput_0.Value = usrStkMeta.EndDate.Value;
				}
			}
		}

		// Token: 0x06000D17 RID: 3351 RVA: 0x00005D45 File Offset: 0x00003F45
		private void comboBox_5_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_18();
		}

		// Token: 0x06000D18 RID: 3352 RVA: 0x00005D4F File Offset: 0x00003F4F
		private void comboBox_4_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_19();
		}

		// Token: 0x06000D19 RID: 3353 RVA: 0x0004F89C File Offset: 0x0004DA9C
		private void method_18()
		{
			DataMgmtForm.Class198 @class = new DataMgmtForm.Class198();
			@class.account_0 = (Account)this.comboBox_5.SelectedItem;
			this.list_1 = Base.Trading.smethod_124(@class.account_0.ID);
			this.label_12.Text = Class521.smethod_0(18147) + this.list_1.Count;
			List<StkSymbol> list = Base.Trading.smethod_125(this.list_1);
			this.comboBox_4.DataSource = list;
			this.method_19();
			List<Account> list2 = (this.comboBox_5.DataSource as List<Account>).Where(new Func<Account, bool>(@class.method_0)).ToList<Account>();
			this.comboBox_6.DataSource = list2;
			if (list2.Count < 1)
			{
				this.comboBox_6.Enabled = false;
				this.button_7.Enabled = false;
				this.button_8.Enabled = false;
			}
			else
			{
				this.comboBox_6.Enabled = true;
				this.button_7.Enabled = true;
				this.button_8.Enabled = true;
			}
			if (this.checkBox_2.Checked && list.Count < 1)
			{
				this.comboBox_4.Text = string.Empty;
			}
		}

		// Token: 0x06000D1A RID: 3354 RVA: 0x0004F9D4 File Offset: 0x0004DBD4
		private void method_19()
		{
			try
			{
				DataMgmtForm.Class199 @class = new DataMgmtForm.Class199();
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_4.SelectedItem;
				this.label_11.Text = Class521.smethod_0(18147) + this.list_1.Where(new Func<Transaction, bool>(@class.method_0)).Count<Transaction>();
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06000D1B RID: 3355 RVA: 0x0004FA54 File Offset: 0x0004DC54
		private void checkBox_2_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_2.Checked)
			{
				this.comboBox_4.Enabled = false;
				this.label_11.Text = this.label_12.Text;
			}
			else
			{
				this.comboBox_4.Enabled = true;
				this.method_19();
			}
		}

		// Token: 0x06000D1C RID: 3356 RVA: 0x00005D59 File Offset: 0x00003F59
		private void button_8_Click(object sender, EventArgs e)
		{
			this.method_20(Class521.smethod_0(18164), false);
		}

		// Token: 0x06000D1D RID: 3357 RVA: 0x00005D6E File Offset: 0x00003F6E
		private void button_7_Click(object sender, EventArgs e)
		{
			this.method_20(Class521.smethod_0(18225), true);
		}

		// Token: 0x06000D1E RID: 3358 RVA: 0x0004FAA8 File Offset: 0x0004DCA8
		private void method_20(string string_5, bool bool_0)
		{
			DataMgmtForm.Class200 @class = new DataMgmtForm.Class200();
			Account account = (Account)this.comboBox_5.SelectedItem;
			Account account2 = (Account)this.comboBox_6.SelectedItem;
			bool @checked = this.checkBox_2.Checked;
			List<Transaction> list = Base.Trading.smethod_124(account.ID);
			List<Transaction> list2 = new List<Transaction>();
			@class.tradingSymbol_0 = null;
			if (@checked)
			{
				list2 = list;
			}
			else
			{
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_4.SelectedItem;
				list2 = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list2.Count > 0 && MessageBox.Show(string_5, Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				if (list2.Where(new Func<Transaction, bool>(DataMgmtForm.<>c.<>9.method_3)).Any<Transaction>())
				{
					MessageBox.Show(Class521.smethod_0(18286), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				else
				{
					List<Transaction> list3 = Base.Trading.smethod_124(account2.ID);
					for (int i = 0; i < list2.Count; i++)
					{
						DataMgmtForm.Class201 class2 = new DataMgmtForm.Class201();
						class2.transaction_0 = list2[i];
						class2.transaction_0.AcctID = account2.ID;
						if (!list3.Where(new Func<Transaction, bool>(class2.method_0)).Any<Transaction>())
						{
							list3.Add(class2.transaction_0);
						}
					}
					Base.Trading.smethod_128(account2.ID, list3);
					List<Order> list4 = Base.Trading.smethod_24(account2.ID);
					List<Order> list5 = Base.Trading.smethod_24(account.ID);
					List<Order> list6 = new List<Order>();
					if (@checked)
					{
						list6 = list5;
					}
					else
					{
						list6 = list5.Where(new Func<Order, bool>(@class.method_1)).ToList<Order>();
					}
					for (int j = 0; j < list6.Count; j++)
					{
						DataMgmtForm.Class202 class3 = new DataMgmtForm.Class202();
						class3.order_0 = list6[j];
						class3.order_0.AcctID = account2.ID;
						if (!list4.Where(new Func<Order, bool>(class3.method_0)).Any<Order>())
						{
							list4.Add(class3.order_0);
						}
					}
					Base.Trading.smethod_26(account2.ID, list4);
					if (Base.Acct.CurrAccount.ID == account2.ID)
					{
						Base.Trading.smethod_17();
						Base.UI.smethod_155();
					}
					if (bool_0)
					{
						if (@checked)
						{
							list = new List<Transaction>();
						}
						else
						{
							list.RemoveAll(new Predicate<Transaction>(@class.method_2));
						}
						Base.Trading.smethod_128(account.ID, list);
						list5 = Base.Trading.smethod_24(account.ID);
						if (list5.Count > 0)
						{
							if (@checked)
							{
								list5 = new List<Order>();
							}
							else
							{
								list5.RemoveAll(new Predicate<Order>(@class.method_3));
							}
						}
						Base.Trading.smethod_26(account.ID, list5);
						List<CondOrder> list7 = Base.Trading.smethod_78(account.ID);
						if (list7.Any<CondOrder>())
						{
							list7.RemoveAll(new Predicate<CondOrder>(@class.method_4));
						}
						Base.Trading.smethod_80();
						if (Base.Acct.CurrAccount.ID == account.ID)
						{
							Base.Trading.smethod_17();
							Base.UI.smethod_155();
						}
					}
					this.method_18();
				}
			}
		}

		// Token: 0x06000D1F RID: 3359 RVA: 0x0004FDD4 File Offset: 0x0004DFD4
		private void button_6_Click(object sender, EventArgs e)
		{
			DataMgmtForm.Class203 @class = new DataMgmtForm.Class203();
			Account account = (Account)this.comboBox_5.SelectedItem;
			List<Transaction> list = Base.Trading.smethod_124(account.ID);
			bool @checked = this.checkBox_2.Checked;
			@class.tradingSymbol_0 = null;
			List<Transaction> list2 = list;
			if (!@checked)
			{
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_4.SelectedItem;
				list2 = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list2.Count > 0 && MessageBox.Show(Class521.smethod_0(18375), Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				if (@checked)
				{
					list = new List<Transaction>();
				}
				else
				{
					list.RemoveAll(new Predicate<Transaction>(@class.method_1));
				}
				Base.Trading.smethod_128(account.ID, list);
				this.method_18();
				if (Base.Acct.CurrAccount.ID == account.ID)
				{
					Base.Trading.smethod_17();
					Base.UI.smethod_155();
					if (Base.UI.ChtCtrl_KLineList != null)
					{
						foreach (ChtCtrl_KLine chtCtrl_KLine in Base.UI.ChtCtrl_KLineList)
						{
							chtCtrl_KLine.Chart_CS.method_200(true);
							chtCtrl_KLine.Chart_CS.method_207();
						}
					}
				}
			}
		}

		// Token: 0x06000D20 RID: 3360 RVA: 0x0004FF28 File Offset: 0x0004E128
		private void button_4_Click(object sender, EventArgs e)
		{
			List<Transaction> list = Base.Trading.smethod_124(((Account)this.comboBox_5.SelectedItem).ID);
			if (!this.checkBox_2.Checked)
			{
				DataMgmtForm.Class204 @class = new DataMgmtForm.Class204();
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_4.SelectedItem;
				list = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			string text = string.Empty;
			if (list.Count > 0)
			{
				try
				{
					string text2 = this.textBox_1.Text;
					if (string.IsNullOrEmpty(text2))
					{
						return;
					}
					if (!text2.ToLower().EndsWith(Class521.smethod_0(17495)))
					{
						text2 += Class521.smethod_0(17495);
					}
					FileInfo fileInfo;
					try
					{
						fileInfo = new FileInfo(text2);
					}
					catch
					{
						MessageBox.Show(Class521.smethod_0(17504), Class521.smethod_0(7730), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						return;
					}
					text = fileInfo.FullName;
					if (!fileInfo.Directory.Exists)
					{
						if (MessageBox.Show(Class521.smethod_0(17585), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						fileInfo.Directory.Create();
					}
					if (fileInfo.Exists)
					{
						if (MessageBox.Show(Class521.smethod_0(17658), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						if (DataMgmtForm.smethod_0(text))
						{
							MessageBox.Show(Class521.smethod_0(17799), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							return;
						}
					}
				}
				catch (Exception ex)
				{
					this.method_4(Class521.smethod_0(18537), Class521.smethod_0(17760) + ex.Message);
					return;
				}
				using (StreamWriter streamWriter = new StreamWriter(text, false, Encoding.GetEncoding(Class521.smethod_0(17987))))
				{
					streamWriter.WriteLine(Class521.smethod_0(18582));
					foreach (Transaction transaction in list)
					{
						string text3 = (transaction.TransType == 1 || transaction.TransType == 4) ? Class521.smethod_0(18676) : Class521.smethod_0(18671);
						string text4 = (transaction.TransType == 1 || transaction.TransType == 3) ? Class521.smethod_0(11757) : Class521.smethod_0(18681);
						string text5 = Class521.smethod_0(18686);
						StkSymbol stkSymbol = SymbMgr.smethod_4(transaction.SymbolID, false);
						if (stkSymbol != null)
						{
							text5 = stkSymbol.Code;
						}
						string value = string.Concat(new object[]
						{
							text5,
							Class521.smethod_0(4736),
							text3,
							Class521.smethod_0(4736),
							text4,
							Class521.smethod_0(4736),
							transaction.CreateTime,
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(new decimal?(transaction.Price)),
							Class521.smethod_0(4736),
							transaction.Units,
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(transaction.Profit),
							Class521.smethod_0(4736),
							Utility.GetStringWithoutEndZero(transaction.Fee)
						});
						streamWriter.WriteLine(value);
					}
				}
				MessageBox.Show(Class521.smethod_0(18691) + list.Count + Class521.smethod_0(18130), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				Base.UI.Form.LastAcctTransExportDir = Path.GetDirectoryName(this.textBox_1.Text);
			}
		}

		// Token: 0x06000D21 RID: 3361 RVA: 0x00050358 File Offset: 0x0004E558
		public static bool smethod_0(string string_5)
		{
			bool result = true;
			FileStream fileStream = null;
			try
			{
				fileStream = new FileStream(string_5, FileMode.Open, FileAccess.Read, FileShare.None);
				result = false;
			}
			catch (Exception ex)
			{
				result = !(ex is FileNotFoundException);
			}
			finally
			{
				if (fileStream != null)
				{
					fileStream.Close();
				}
			}
			return result;
		}

		// Token: 0x06000D22 RID: 3362 RVA: 0x000503B4 File Offset: 0x0004E5B4
		private void comboBox_2_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (!string.IsNullOrEmpty(this.textBox_0.Text))
			{
				string text = Class521.smethod_0(17495);
				string text2 = Class521.smethod_0(18740);
				if (this.comboBox_2.SelectedIndex == 0)
				{
					if (this.textBox_0.Text.EndsWith(text2))
					{
						this.textBox_0.Text = this.textBox_0.Text.Replace(text2, text);
					}
					this.checkBox_1.Enabled = true;
				}
				else
				{
					if (this.textBox_0.Text.EndsWith(text))
					{
						this.textBox_0.Text = this.textBox_0.Text.Replace(text, text2);
					}
					this.checkBox_1.Checked = false;
					this.checkBox_1.Enabled = false;
				}
			}
		}

		// Token: 0x06000D23 RID: 3363 RVA: 0x00005D83 File Offset: 0x00003F83
		private void dateTimeInput_0_ValueChanged(object sender, EventArgs e)
		{
			this.method_21();
		}

		// Token: 0x06000D24 RID: 3364 RVA: 0x00005D83 File Offset: 0x00003F83
		private void dateTimeInput_0_Leave(object sender, EventArgs e)
		{
			this.method_21();
		}

		// Token: 0x06000D25 RID: 3365 RVA: 0x00050484 File Offset: 0x0004E684
		private void method_21()
		{
			if (this.dateTimeInput_0.Value.Date < this.dateTimeInput_1.Value.Date)
			{
				this.dateTimeInput_0.Value = this.dateTimeInput_1.Value;
			}
		}

		// Token: 0x06000D26 RID: 3366 RVA: 0x000504D8 File Offset: 0x0004E6D8
		private void button_5_Click(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = Class521.smethod_0(18749);
			saveFileDialog.Filter = Class521.smethod_0(18774);
			if (!string.IsNullOrEmpty(this.textBox_1.Text))
			{
				try
				{
					FileInfo fileInfo = new FileInfo(this.textBox_1.Text);
					saveFileDialog.InitialDirectory = fileInfo.DirectoryName;
					saveFileDialog.FileName = fileInfo.Name;
				}
				catch
				{
					saveFileDialog.InitialDirectory = this.string_0;
				}
			}
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_1.Text = saveFileDialog.FileName;
				Base.UI.Form.LastAcctTransExportDir = Path.GetDirectoryName(this.textBox_1.Text);
			}
		}

		// Token: 0x06000D27 RID: 3367 RVA: 0x000505A0 File Offset: 0x0004E7A0
		private void button_0_Click(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = Class521.smethod_0(18749);
			saveFileDialog.Filter = ((this.comboBox_2.SelectedIndex == 0) ? Class521.smethod_0(18774) : Class521.smethod_0(18803));
			if (!string.IsNullOrEmpty(this.textBox_0.Text))
			{
				try
				{
					FileInfo fileInfo = new FileInfo(this.textBox_0.Text);
					saveFileDialog.InitialDirectory = fileInfo.DirectoryName;
					saveFileDialog.FileName = fileInfo.Name;
				}
				catch
				{
					saveFileDialog.InitialDirectory = this.string_0;
				}
			}
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_0.Text = saveFileDialog.FileName;
				Base.UI.Form.LastHisDataExportDir = Path.GetDirectoryName(this.textBox_0.Text);
			}
		}

		// Token: 0x06000D28 RID: 3368 RVA: 0x00050680 File Offset: 0x0004E880
		private void button_9_Click(object sender, EventArgs e)
		{
			string text = this.textBox_2.Text;
			if (!string.IsNullOrEmpty(text) && File.Exists(text))
			{
				Base.UI.Form.LastTransImportFilePath = this.textBox_2.Text;
				new ImportTransForm(text)
				{
					StartPosition = FormStartPosition.CenterScreen
				}.ShowDialog();
			}
			else
			{
				MessageBox.Show(Class521.smethod_0(18832), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.method_22();
			}
		}

		// Token: 0x06000D29 RID: 3369 RVA: 0x00005D8D File Offset: 0x00003F8D
		private void button_11_Click(object sender, EventArgs e)
		{
			this.method_22();
		}

		// Token: 0x06000D2A RID: 3370 RVA: 0x000506F8 File Offset: 0x0004E8F8
		private void method_22()
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = Class521.smethod_0(18885);
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_2.Text = openFileDialog.FileName;
			}
		}

		// Token: 0x06000D2B RID: 3371 RVA: 0x00005D97 File Offset: 0x00003F97
		private void dateTimePicker_0_ValueChanged(object sender, EventArgs e)
		{
			Base.UI.Form.CfmmcAutoDnldConfig.BeginTime = this.dateTimePicker_0.Value;
		}

		// Token: 0x06000D2C RID: 3372 RVA: 0x00005CA2 File Offset: 0x00003EA2
		private void comboBox_7_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06000D2D RID: 3373 RVA: 0x00050738 File Offset: 0x0004E938
		public void button_10_Click(object sender, EventArgs e)
		{
			if (CfmmcRecImporter.IEMajor <= 6)
			{
				this.method_33(Class521.smethod_0(18962), 0);
			}
			else if (CfmmcRecImporter.IsDownloading)
			{
				this.method_33(Class521.smethod_0(19047), 0);
			}
			else
			{
				CfmmcAcct cfmmcAcct = this.method_31();
				if (cfmmcAcct != null && CfmmcRecImporter.smethod_31(cfmmcAcct))
				{
					this.method_34(false);
				}
			}
		}

		// Token: 0x06000D2E RID: 3374 RVA: 0x00005DB5 File Offset: 0x00003FB5
		private void method_23(object sender, EventArgs e)
		{
			this.label_13.Text = Class521.smethod_0(19088);
			this.toolTip_0 = null;
		}

		// Token: 0x06000D2F RID: 3375 RVA: 0x00005DD5 File Offset: 0x00003FD5
		private void method_24(object sender, EventArgs e)
		{
			this.label_13.Text = Class521.smethod_0(19109);
			this.toolTip_0 = null;
		}

		// Token: 0x06000D30 RID: 3376 RVA: 0x00050798 File Offset: 0x0004E998
		private void method_25(object sender, EventArgs25 e)
		{
			this.label_13.Text = Class521.smethod_0(19158);
			this.toolTip_0 = null;
			if (!this.progressBar_1.Visible)
			{
				this.progressBar_1.Visible = true;
			}
			this.progressBar_1.Value = 0;
			this.progressBar_1.Maximum = 0;
			int totalRecs = e.TotalRecs;
			if (totalRecs > 0)
			{
				this.progressBar_1.Maximum += totalRecs;
			}
		}

		// Token: 0x06000D31 RID: 3377 RVA: 0x00050814 File Offset: 0x0004EA14
		private void method_26(object sender, EventArgs25 e)
		{
			int totalRecs = e.TotalRecs;
			if (totalRecs > 0)
			{
				this.progressBar_1.Value = totalRecs + 1;
			}
			this.label_13.Text = Class521.smethod_0(19187);
			this.toolTip_0 = null;
		}

		// Token: 0x06000D32 RID: 3378 RVA: 0x00050858 File Offset: 0x0004EA58
		private void method_27(object sender, EventArgs25 e)
		{
			this.label_13.Text = string.Empty;
			this.toolTip_0 = null;
			MessageBox.Show(this, e.Msg, Class521.smethod_0(19216), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			this.method_34(true);
			this.progressBar_1.Visible = false;
			this.method_3();
			this.method_18();
			if ((Base.UI.Form.IfShowNoTransArrow || !Base.UI.Form.IfShowAllTransArrow) && MessageBox.Show(Class521.smethod_0(19225), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				Base.UI.Form.IfShowNoTransArrow = false;
				Base.UI.Form.IfShowAllTransArrow = true;
				Base.UI.smethod_36();
			}
		}

		// Token: 0x06000D33 RID: 3379 RVA: 0x0005090C File Offset: 0x0004EB0C
		private void method_28(object sender, EventArgs25 e)
		{
			string string_ = Class521.smethod_0(19290) + e.Msg;
			this.method_29(string_, 18);
			this.method_34(true);
			this.progressBar_1.Visible = false;
		}

		// Token: 0x06000D34 RID: 3380 RVA: 0x00050950 File Offset: 0x0004EB50
		private void method_29(string string_5, int int_0)
		{
			if (string_5.Length > int_0)
			{
				this.label_13.Text = string_5.Substring(0, int_0) + Class521.smethod_0(19311);
				this.toolTip_0 = new System.Windows.Forms.ToolTip();
				this.toolTip_0.SetToolTip(this.label_13, string_5);
			}
			else
			{
				this.label_13.Text = string_5;
				this.toolTip_0 = null;
			}
		}

		// Token: 0x06000D35 RID: 3381 RVA: 0x00005DF5 File Offset: 0x00003FF5
		private void method_30(object sender, EventArgs e)
		{
			this.label_13.Text = string.Empty;
			this.toolTip_0 = null;
			MessageBox.Show(this, Class521.smethod_0(19316), Class521.smethod_0(19216), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06000D36 RID: 3382 RVA: 0x000509BC File Offset: 0x0004EBBC
		private CfmmcAcct method_31()
		{
			if (this.comboBox_7.Items.Count > 0)
			{
				object selectedItem = this.comboBox_7.SelectedItem;
				CfmmcAcct cfmmcAcct = Class466.smethod_11(this.comboBox_7.Text);
				if (cfmmcAcct != null)
				{
					return cfmmcAcct;
				}
			}
			else
			{
				MessageBox.Show(this, Class521.smethod_0(19369), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			return null;
		}

		// Token: 0x06000D37 RID: 3383 RVA: 0x00050A24 File Offset: 0x0004EC24
		public void method_32()
		{
			if (Base.UI.Form.CfmmcAutoDnldConfig == null)
			{
				Base.UI.Form.CfmmcAutoDnldConfig = new CfmmcAutoDnldConfig();
				Base.UI.Form.CfmmcAutoDnldConfig.AutoDownOnStartup = false;
				Base.UI.Form.CfmmcAutoDnldConfig.AutoDownPeriodly = true;
				Base.UI.Form.CfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每周;
				Base.UI.Form.CfmmcAutoDnldConfig.BeginTime = new DateTime(DateTime.Now.Year, 1, 1, 17, 0, 0);
			}
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (cfmmcAutoDnldConfig.AutoDownOnStartup)
			{
				this.radioButton_3.Checked = true;
			}
			this.radioButton_2.CheckedChanged += this.radioButton_2_CheckedChanged;
			if (cfmmcAutoDnldConfig.AutoDownPeriodly)
			{
				this.radioButton_2.Checked = true;
				this.groupBox_11.Enabled = true;
				this.groupBox_10.Enabled = true;
			}
			else
			{
				this.radioButton_2.Checked = false;
				this.groupBox_11.Enabled = false;
				this.groupBox_10.Enabled = false;
			}
			if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
			{
				this.radioButton_1.Checked = true;
			}
			else if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每周)
			{
				this.radioButton_0.Checked = true;
			}
			int num = (int)(cfmmcAutoDnldConfig.WklyDnldDayOfWeek + 1);
			if (num > 6)
			{
				num = 0;
			}
			this.comboBox_8.SelectedIndex = num;
			if (this.dateTimePicker_0.MinDate <= cfmmcAutoDnldConfig.BeginTime && this.dateTimePicker_0.MaxDate >= cfmmcAutoDnldConfig.BeginTime)
			{
				this.dateTimePicker_0.Value = cfmmcAutoDnldConfig.BeginTime;
			}
			this.progressBar_1.Visible = false;
		}

		// Token: 0x06000D38 RID: 3384 RVA: 0x00050BC0 File Offset: 0x0004EDC0
		private void radioButton_2_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioButton_2.Checked)
			{
				this.groupBox_11.Enabled = true;
				this.groupBox_10.Enabled = true;
			}
			else
			{
				this.groupBox_11.Enabled = false;
				this.groupBox_10.Enabled = false;
			}
		}

		// Token: 0x06000D39 RID: 3385 RVA: 0x00005E2E File Offset: 0x0000402E
		public void method_33(string string_5, int int_0)
		{
			if (!(string_5 == Class521.smethod_0(1449)))
			{
				if (int_0 == 0)
				{
					this.method_29(string_5, 18);
				}
			}
		}

		// Token: 0x06000D3A RID: 3386 RVA: 0x00005E52 File Offset: 0x00004052
		private void method_34(bool bool_0)
		{
			this.button_12.Enabled = bool_0;
			this.button_14.Enabled = bool_0;
			this.button_13.Enabled = bool_0;
			this.button_10.Enabled = bool_0;
			this.comboBox_7.Enabled = bool_0;
		}

		// Token: 0x06000D3B RID: 3387 RVA: 0x00005E92 File Offset: 0x00004092
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000D3C RID: 3388 RVA: 0x00050C10 File Offset: 0x0004EE10
		private void method_35()
		{
			this.groupBox_0 = new GroupBox();
			this.label_0 = new Label();
			this.comboBox_1 = new ComboBox();
			this.label_1 = new Label();
			this.comboBox_0 = new ComboBox();
			this.groupBox_1 = new GroupBox();
			this.checkBox_1 = new CheckBox();
			this.checkBox_0 = new CheckBox();
			this.label_5 = new Label();
			this.comboBox_3 = new ComboBox();
			this.comboBox_2 = new ComboBox();
			this.label_2 = new Label();
			this.label_3 = new Label();
			this.dateTimeInput_0 = new DateTimeInput();
			this.dateTimeInput_1 = new DateTimeInput();
			this.label_4 = new Label();
			this.textBox_0 = new TextBox();
			this.button_0 = new Button();
			this.label_6 = new Label();
			this.progressBar_0 = new ProgressBar();
			this.button_1 = new Button();
			this.groupBox_2 = new GroupBox();
			this.label_7 = new Label();
			this.tabControl_0 = new System.Windows.Forms.TabControl();
			this.tabPage_0 = new TabPage();
			this.groupBox_5 = new GroupBox();
			this.groupBox_6 = new GroupBox();
			this.comboBox_6 = new ComboBox();
			this.button_7 = new Button();
			this.button_8 = new Button();
			this.label_9 = new Label();
			this.button_6 = new Button();
			this.comboBox_5 = new ComboBox();
			this.label_11 = new Label();
			this.comboBox_4 = new ComboBox();
			this.label_8 = new Label();
			this.label_12 = new Label();
			this.checkBox_2 = new CheckBox();
			this.groupBox_7 = new GroupBox();
			this.label_14 = new Label();
			this.textBox_2 = new TextBox();
			this.button_11 = new Button();
			this.button_9 = new Button();
			this.groupBox_4 = new GroupBox();
			this.label_10 = new Label();
			this.button_4 = new Button();
			this.textBox_1 = new TextBox();
			this.button_5 = new Button();
			this.tabPage_1 = new TabPage();
			this.groupBox_3 = new GroupBox();
			this.tabPage_2 = new TabPage();
			this.pictureBox_0 = new PictureBox();
			this.labelX_0 = new LabelX();
			this.groupBox_8 = new GroupBox();
			this.radioButton_2 = new RadioButton();
			this.radioButton_3 = new RadioButton();
			this.groupBox_10 = new GroupBox();
			this.dateTimePicker_0 = new DateTimePicker();
			this.groupBox_11 = new GroupBox();
			this.comboBox_8 = new ComboBox();
			this.radioButton_0 = new RadioButton();
			this.radioButton_1 = new RadioButton();
			this.groupBox_9 = new GroupBox();
			this.groupBox_12 = new GroupBox();
			this.button_10 = new Button();
			this.label_16 = new Label();
			this.progressBar_1 = new ProgressBar();
			this.label_13 = new Label();
			this.label_15 = new Label();
			this.button_14 = new Button();
			this.button_13 = new Button();
			this.button_12 = new Button();
			this.comboBox_7 = new ComboBox();
			this.button_2 = new Button();
			this.button_3 = new Button();
			this.groupBox_0.SuspendLayout();
			this.groupBox_1.SuspendLayout();
			((ISupportInitialize)this.dateTimeInput_0).BeginInit();
			((ISupportInitialize)this.dateTimeInput_1).BeginInit();
			this.groupBox_2.SuspendLayout();
			this.tabControl_0.SuspendLayout();
			this.tabPage_0.SuspendLayout();
			this.groupBox_5.SuspendLayout();
			this.groupBox_6.SuspendLayout();
			this.groupBox_7.SuspendLayout();
			this.groupBox_4.SuspendLayout();
			this.tabPage_1.SuspendLayout();
			this.groupBox_3.SuspendLayout();
			this.tabPage_2.SuspendLayout();
			((ISupportInitialize)this.pictureBox_0).BeginInit();
			this.groupBox_8.SuspendLayout();
			this.groupBox_10.SuspendLayout();
			this.groupBox_11.SuspendLayout();
			this.groupBox_9.SuspendLayout();
			base.SuspendLayout();
			this.groupBox_0.Controls.Add(this.label_0);
			this.groupBox_0.Controls.Add(this.comboBox_1);
			this.groupBox_0.Controls.Add(this.label_1);
			this.groupBox_0.Controls.Add(this.comboBox_0);
			this.groupBox_0.Location = new Point(19, 15);
			this.groupBox_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_0.Name = Class521.smethod_0(19430);
			this.groupBox_0.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_0.Size = new Size(300, 155);
			this.groupBox_0.TabIndex = 0;
			this.groupBox_0.TabStop = false;
			this.groupBox_0.Text = Class521.smethod_0(19455);
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(39, 82);
			this.label_0.Name = Class521.smethod_0(5827);
			this.label_0.Size = new Size(52, 15);
			this.label_0.TabIndex = 3;
			this.label_0.Text = Class521.smethod_0(5902);
			this.comboBox_1.FormattingEnabled = true;
			this.comboBox_1.Location = new Point(42, 102);
			this.comboBox_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_1.Name = Class521.smethod_0(19472);
			this.comboBox_1.Size = new Size(216, 23);
			this.comboBox_1.TabIndex = 2;
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(39, 28);
			this.label_1.Name = Class521.smethod_0(5871);
			this.label_1.Size = new Size(67, 15);
			this.label_1.TabIndex = 1;
			this.label_1.Text = Class521.smethod_0(19501);
			this.comboBox_0.FormattingEnabled = true;
			this.comboBox_0.Location = new Point(42, 48);
			this.comboBox_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_0.Name = Class521.smethod_0(19518);
			this.comboBox_0.Size = new Size(216, 23);
			this.comboBox_0.TabIndex = 0;
			this.groupBox_1.Controls.Add(this.checkBox_1);
			this.groupBox_1.Controls.Add(this.checkBox_0);
			this.groupBox_1.Controls.Add(this.label_5);
			this.groupBox_1.Controls.Add(this.comboBox_3);
			this.groupBox_1.Controls.Add(this.comboBox_2);
			this.groupBox_1.Controls.Add(this.label_2);
			this.groupBox_1.Location = new Point(342, 15);
			this.groupBox_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_1.Name = Class521.smethod_0(19539);
			this.groupBox_1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_1.Size = new Size(319, 155);
			this.groupBox_1.TabIndex = 13;
			this.groupBox_1.TabStop = false;
			this.groupBox_1.Text = Class521.smethod_0(19568);
			this.checkBox_1.AutoSize = true;
			this.checkBox_1.Location = new Point(113, 121);
			this.checkBox_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_1.Name = Class521.smethod_0(19585);
			this.checkBox_1.Size = new Size(164, 19);
			this.checkBox_1.TabIndex = 11;
			this.checkBox_1.Text = Class521.smethod_0(19614);
			this.checkBox_1.UseVisualStyleBackColor = true;
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(113, 96);
			this.checkBox_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_0.Name = Class521.smethod_0(19651);
			this.checkBox_0.Size = new Size(149, 19);
			this.checkBox_0.TabIndex = 10;
			this.checkBox_0.Text = Class521.smethod_0(19684);
			this.checkBox_0.UseVisualStyleBackColor = true;
			this.label_5.AutoSize = true;
			this.label_5.Location = new Point(24, 31);
			this.label_5.Name = Class521.smethod_0(5893);
			this.label_5.Size = new Size(82, 15);
			this.label_5.TabIndex = 5;
			this.label_5.Text = Class521.smethod_0(19717);
			this.comboBox_3.FormattingEnabled = true;
			this.comboBox_3.Location = new Point(112, 29);
			this.comboBox_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_3.Name = Class521.smethod_0(19738);
			this.comboBox_3.Size = new Size(177, 23);
			this.comboBox_3.TabIndex = 4;
			this.comboBox_2.FormattingEnabled = true;
			this.comboBox_2.Location = new Point(112, 61);
			this.comboBox_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_2.Name = Class521.smethod_0(19767);
			this.comboBox_2.Size = new Size(177, 23);
			this.comboBox_2.TabIndex = 9;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(24, 63);
			this.label_2.Name = Class521.smethod_0(7019);
			this.label_2.Size = new Size(82, 15);
			this.label_2.TabIndex = 8;
			this.label_2.Text = Class521.smethod_0(19800);
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(27, 35);
			this.label_3.Name = Class521.smethod_0(5849);
			this.label_3.Size = new Size(82, 15);
			this.label_3.TabIndex = 0;
			this.label_3.Text = Class521.smethod_0(19821);
			this.dateTimeInput_0.BackgroundStyle.Class = Class521.smethod_0(19842);
			this.dateTimeInput_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.ButtonDropDown.Shortcut = eShortcut.AltDown;
			this.dateTimeInput_0.ButtonDropDown.Visible = true;
			this.dateTimeInput_0.Format = eDateTimePickerFormat.Long;
			this.dateTimeInput_0.IsPopupCalendarOpen = false;
			this.dateTimeInput_0.Location = new Point(446, 30);
			this.dateTimeInput_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dateTimeInput_0.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.BackgroundStyle.BackColor = SystemColors.Window;
			this.dateTimeInput_0.MonthCalendar.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_0.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BackColor2SchemePart = eColorSchemePart.BarBackground2;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BackColorSchemePart = eColorSchemePart.BarBackground;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BorderTopColorSchemePart = eColorSchemePart.BarDockedBorder;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.BorderTopWidth = 1;
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_0.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.DaySize = new Size(30, 20);
			this.dateTimeInput_0.MonthCalendar.DisplayMonth = new DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_0.MonthCalendar.MarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.BackColor2SchemePart = eColorSchemePart.PanelBackground2;
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.BackColorSchemePart = eColorSchemePart.PanelBackground;
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_0.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_0.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dateTimeInput_0.Name = Class521.smethod_0(19875);
			this.dateTimeInput_0.Size = new Size(170, 26);
			this.dateTimeInput_0.Style = eDotNetBarStyle.StyleManagerControlled;
			this.dateTimeInput_0.TabIndex = 7;
			this.dateTimeInput_1.BackgroundStyle.Class = Class521.smethod_0(19842);
			this.dateTimeInput_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.ButtonClear.Text = Class521.smethod_0(19908);
			this.dateTimeInput_1.ButtonDropDown.Shortcut = eShortcut.AltDown;
			this.dateTimeInput_1.ButtonDropDown.Visible = true;
			this.dateTimeInput_1.Format = eDateTimePickerFormat.Long;
			this.dateTimeInput_1.IsPopupCalendarOpen = false;
			this.dateTimeInput_1.Location = new Point(113, 30);
			this.dateTimeInput_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dateTimeInput_1.MonthCalendar.AnnuallyMarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.BackgroundStyle.BackColor = SystemColors.Window;
			this.dateTimeInput_1.MonthCalendar.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_1.MonthCalendar.BackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BackColor2SchemePart = eColorSchemePart.BarBackground2;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BackColorSchemePart = eColorSchemePart.BarBackground;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BorderTopColorSchemePart = eColorSchemePart.BarDockedBorder;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.BorderTopWidth = 1;
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_1.MonthCalendar.CommandsBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.DaySize = new Size(30, 20);
			this.dateTimeInput_1.MonthCalendar.DisplayMonth = new DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_1.MonthCalendar.MarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.MonthlyMarkedDates = new DateTime[0];
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.BackColor2SchemePart = eColorSchemePart.PanelBackground2;
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.BackColorSchemePart = eColorSchemePart.PanelBackground;
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.Class = Class521.smethod_0(1449);
			this.dateTimeInput_1.MonthCalendar.NavigationBackgroundStyle.CornerType = eCornerType.Square;
			this.dateTimeInput_1.MonthCalendar.WeeklyMarkedDays = new DayOfWeek[0];
			this.dateTimeInput_1.Name = Class521.smethod_0(19917);
			this.dateTimeInput_1.Size = new Size(170, 26);
			this.dateTimeInput_1.Style = eDotNetBarStyle.StyleManagerControlled;
			this.dateTimeInput_1.TabIndex = 6;
			this.label_4.AutoSize = true;
			this.label_4.Location = new Point(353, 35);
			this.label_4.Name = Class521.smethod_0(7268);
			this.label_4.Size = new Size(82, 15);
			this.label_4.TabIndex = 1;
			this.label_4.Text = Class521.smethod_0(19950);
			this.textBox_0.Location = new Point(129, 29);
			this.textBox_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_0.Name = Class521.smethod_0(19971);
			this.textBox_0.Size = new Size(447, 25);
			this.textBox_0.TabIndex = 14;
			this.button_0.Image = Class375.openHS;
			this.button_0.Location = new Point(582, 27);
			this.button_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_0.Name = Class521.smethod_0(20000);
			this.button_0.Size = new Size(31, 29);
			this.button_0.TabIndex = 15;
			this.button_0.UseVisualStyleBackColor = true;
			this.label_6.AutoSize = true;
			this.label_6.Location = new Point(27, 34);
			this.label_6.Name = Class521.smethod_0(5915);
			this.label_6.Size = new Size(82, 15);
			this.label_6.TabIndex = 16;
			this.label_6.Text = Class521.smethod_0(20029);
			this.progressBar_0.Location = new Point(31, 91);
			this.progressBar_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.progressBar_0.Name = Class521.smethod_0(20050);
			this.progressBar_0.Size = new Size(479, 10);
			this.progressBar_0.TabIndex = 17;
			this.button_1.Location = new Point(525, 72);
			this.button_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_1.Name = Class521.smethod_0(20075);
			this.button_1.Size = new Size(88, 30);
			this.button_1.TabIndex = 18;
			this.button_1.Text = Class521.smethod_0(17486);
			this.button_1.UseVisualStyleBackColor = true;
			this.groupBox_2.Controls.Add(this.label_7);
			this.groupBox_2.Controls.Add(this.label_6);
			this.groupBox_2.Controls.Add(this.button_1);
			this.groupBox_2.Controls.Add(this.textBox_0);
			this.groupBox_2.Controls.Add(this.progressBar_0);
			this.groupBox_2.Controls.Add(this.button_0);
			this.groupBox_2.Location = new Point(19, 266);
			this.groupBox_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_2.Name = Class521.smethod_0(20092);
			this.groupBox_2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_2.Size = new Size(642, 117);
			this.groupBox_2.TabIndex = 19;
			this.groupBox_2.TabStop = false;
			this.groupBox_2.Text = Class521.smethod_0(20113);
			this.label_7.AutoSize = true;
			this.label_7.Font = new Font(Class521.smethod_0(7183), 8f);
			this.label_7.Location = new Point(28, 72);
			this.label_7.Name = Class521.smethod_0(20130);
			this.label_7.Size = new Size(112, 14);
			this.label_7.TabIndex = 19;
			this.label_7.Text = Class521.smethod_0(20155);
			this.tabControl_0.Controls.Add(this.tabPage_0);
			this.tabControl_0.Controls.Add(this.tabPage_1);
			this.tabControl_0.Controls.Add(this.tabPage_2);
			this.tabControl_0.Location = new Point(24, 12);
			this.tabControl_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabControl_0.Name = Class521.smethod_0(20184);
			this.tabControl_0.SelectedIndex = 0;
			this.tabControl_0.Size = new Size(690, 432);
			this.tabControl_0.TabIndex = 20;
			this.tabPage_0.BackColor = SystemColors.Control;
			this.tabPage_0.Controls.Add(this.groupBox_5);
			this.tabPage_0.Controls.Add(this.groupBox_7);
			this.tabPage_0.Controls.Add(this.groupBox_4);
			this.tabPage_0.Location = new Point(4, 25);
			this.tabPage_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_0.Name = Class521.smethod_0(20201);
			this.tabPage_0.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_0.Size = new Size(682, 403);
			this.tabPage_0.TabIndex = 0;
			this.tabPage_0.Text = Class521.smethod_0(20218);
			this.groupBox_5.Controls.Add(this.groupBox_6);
			this.groupBox_5.Controls.Add(this.label_9);
			this.groupBox_5.Controls.Add(this.button_6);
			this.groupBox_5.Controls.Add(this.comboBox_5);
			this.groupBox_5.Controls.Add(this.label_11);
			this.groupBox_5.Controls.Add(this.comboBox_4);
			this.groupBox_5.Controls.Add(this.label_8);
			this.groupBox_5.Controls.Add(this.label_12);
			this.groupBox_5.Controls.Add(this.checkBox_2);
			this.groupBox_5.Location = new Point(19, 16);
			this.groupBox_5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_5.Name = Class521.smethod_0(20235);
			this.groupBox_5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_5.Size = new Size(643, 179);
			this.groupBox_5.TabIndex = 0;
			this.groupBox_5.TabStop = false;
			this.groupBox_5.Text = Class521.smethod_0(20248);
			this.groupBox_6.Controls.Add(this.comboBox_6);
			this.groupBox_6.Controls.Add(this.button_7);
			this.groupBox_6.Controls.Add(this.button_8);
			this.groupBox_6.Location = new Point(354, 23);
			this.groupBox_6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_6.Name = Class521.smethod_0(20273);
			this.groupBox_6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_6.Size = new Size(254, 123);
			this.groupBox_6.TabIndex = 4;
			this.groupBox_6.TabStop = false;
			this.groupBox_6.Text = Class521.smethod_0(20286);
			this.comboBox_6.FormattingEnabled = true;
			this.comboBox_6.Location = new Point(33, 29);
			this.comboBox_6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_6.Name = Class521.smethod_0(20307);
			this.comboBox_6.Size = new Size(189, 23);
			this.comboBox_6.TabIndex = 0;
			this.button_7.Location = new Point(33, 73);
			this.button_7.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_7.Name = Class521.smethod_0(20344);
			this.button_7.Size = new Size(88, 30);
			this.button_7.TabIndex = 1;
			this.button_7.Text = Class521.smethod_0(20365);
			this.button_7.UseVisualStyleBackColor = true;
			this.button_8.Location = new Point(134, 73);
			this.button_8.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_8.Name = Class521.smethod_0(20374);
			this.button_8.Size = new Size(88, 30);
			this.button_8.TabIndex = 27;
			this.button_8.Text = Class521.smethod_0(20395);
			this.button_8.UseVisualStyleBackColor = true;
			this.label_9.AutoSize = true;
			this.label_9.Location = new Point(19, 30);
			this.label_9.Name = Class521.smethod_0(11267);
			this.label_9.Size = new Size(52, 15);
			this.label_9.TabIndex = 1;
			this.label_9.Text = Class521.smethod_0(20404);
			this.button_6.Location = new Point(191, 133);
			this.button_6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_6.Name = Class521.smethod_0(20417);
			this.button_6.Size = new Size(88, 30);
			this.button_6.TabIndex = 3;
			this.button_6.Text = Class521.smethod_0(20442);
			this.button_6.UseVisualStyleBackColor = true;
			this.comboBox_5.FormattingEnabled = true;
			this.comboBox_5.Location = new Point(21, 48);
			this.comboBox_5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_5.Name = Class521.smethod_0(20459);
			this.comboBox_5.Size = new Size(185, 23);
			this.comboBox_5.TabIndex = 0;
			this.label_11.AutoSize = true;
			this.label_11.Location = new Point(216, 102);
			this.label_11.Name = Class521.smethod_0(20484);
			this.label_11.Size = new Size(63, 14);
			this.label_11.TabIndex = 7;
			this.label_11.Text = Class521.smethod_0(20521);
			this.comboBox_4.FormattingEnabled = true;
			this.comboBox_4.Location = new Point(21, 97);
			this.comboBox_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_4.Name = Class521.smethod_0(20538);
			this.comboBox_4.Size = new Size(185, 23);
			this.comboBox_4.TabIndex = 1;
			this.label_8.AutoSize = true;
			this.label_8.Location = new Point(19, 79);
			this.label_8.Name = Class521.smethod_0(11233);
			this.label_8.Size = new Size(52, 15);
			this.label_8.TabIndex = 3;
			this.label_8.Text = Class521.smethod_0(5902);
			this.label_12.AutoSize = true;
			this.label_12.Location = new Point(216, 53);
			this.label_12.Name = Class521.smethod_0(20571);
			this.label_12.Size = new Size(63, 14);
			this.label_12.TabIndex = 6;
			this.label_12.Text = Class521.smethod_0(20521);
			this.checkBox_2.AutoSize = true;
			this.checkBox_2.Location = new Point(21, 140);
			this.checkBox_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_2.Name = Class521.smethod_0(20600);
			this.checkBox_2.Size = new Size(89, 19);
			this.checkBox_2.TabIndex = 2;
			this.checkBox_2.Text = Class521.smethod_0(20641);
			this.checkBox_2.UseVisualStyleBackColor = true;
			this.groupBox_7.Controls.Add(this.label_14);
			this.groupBox_7.Controls.Add(this.textBox_2);
			this.groupBox_7.Controls.Add(this.button_11);
			this.groupBox_7.Controls.Add(this.button_9);
			this.groupBox_7.Location = new Point(19, 300);
			this.groupBox_7.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_7.Name = Class521.smethod_0(20658);
			this.groupBox_7.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_7.Size = new Size(643, 80);
			this.groupBox_7.TabIndex = 2;
			this.groupBox_7.TabStop = false;
			this.groupBox_7.Text = Class521.smethod_0(20671);
			this.label_14.AutoSize = true;
			this.label_14.Location = new Point(9, 35);
			this.label_14.Name = Class521.smethod_0(20696);
			this.label_14.Size = new Size(82, 15);
			this.label_14.TabIndex = 25;
			this.label_14.Text = Class521.smethod_0(20709);
			this.textBox_2.Location = new Point(102, 32);
			this.textBox_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_2.Name = Class521.smethod_0(20730);
			this.textBox_2.Size = new Size(391, 25);
			this.textBox_2.TabIndex = 0;
			this.button_11.Image = Class375.openHS;
			this.button_11.Location = new Point(499, 30);
			this.button_11.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_11.Name = Class521.smethod_0(20763);
			this.button_11.Size = new Size(31, 29);
			this.button_11.TabIndex = 1;
			this.button_11.UseVisualStyleBackColor = true;
			this.button_9.Location = new Point(536, 29);
			this.button_9.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_9.Name = Class521.smethod_0(20796);
			this.button_9.Size = new Size(88, 30);
			this.button_9.TabIndex = 2;
			this.button_9.Text = Class521.smethod_0(20821);
			this.button_9.UseVisualStyleBackColor = true;
			this.groupBox_4.Controls.Add(this.label_10);
			this.groupBox_4.Controls.Add(this.button_4);
			this.groupBox_4.Controls.Add(this.textBox_1);
			this.groupBox_4.Controls.Add(this.button_5);
			this.groupBox_4.Location = new Point(19, 208);
			this.groupBox_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_4.Name = Class521.smethod_0(10647);
			this.groupBox_4.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_4.Size = new Size(643, 80);
			this.groupBox_4.TabIndex = 2;
			this.groupBox_4.TabStop = false;
			this.groupBox_4.Text = Class521.smethod_0(20834);
			this.label_10.AutoSize = true;
			this.label_10.Location = new Point(8, 35);
			this.label_10.Name = Class521.smethod_0(20859);
			this.label_10.Size = new Size(82, 15);
			this.label_10.TabIndex = 21;
			this.label_10.Text = Class521.smethod_0(20709);
			this.button_4.Location = new Point(536, 28);
			this.button_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_4.Name = Class521.smethod_0(20872);
			this.button_4.Size = new Size(88, 30);
			this.button_4.TabIndex = 2;
			this.button_4.Text = Class521.smethod_0(17486);
			this.button_4.UseVisualStyleBackColor = true;
			this.textBox_1.Location = new Point(102, 32);
			this.textBox_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_1.Name = Class521.smethod_0(20889);
			this.textBox_1.Size = new Size(391, 25);
			this.textBox_1.TabIndex = 0;
			this.button_5.Image = Class375.openHS;
			this.button_5.Location = new Point(498, 29);
			this.button_5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_5.Name = Class521.smethod_0(20922);
			this.button_5.Size = new Size(31, 29);
			this.button_5.TabIndex = 1;
			this.button_5.UseVisualStyleBackColor = true;
			this.tabPage_1.BackColor = SystemColors.Control;
			this.tabPage_1.Controls.Add(this.groupBox_3);
			this.tabPage_1.Controls.Add(this.groupBox_0);
			this.tabPage_1.Controls.Add(this.groupBox_2);
			this.tabPage_1.Controls.Add(this.groupBox_1);
			this.tabPage_1.Location = new Point(4, 25);
			this.tabPage_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_1.Name = Class521.smethod_0(20955);
			this.tabPage_1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_1.Size = new Size(682, 403);
			this.tabPage_1.TabIndex = 1;
			this.tabPage_1.Text = Class521.smethod_0(8941);
			this.groupBox_3.Controls.Add(this.dateTimeInput_1);
			this.groupBox_3.Controls.Add(this.label_3);
			this.groupBox_3.Controls.Add(this.label_4);
			this.groupBox_3.Controls.Add(this.dateTimeInput_0);
			this.groupBox_3.Location = new Point(19, 181);
			this.groupBox_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_3.Name = Class521.smethod_0(20976);
			this.groupBox_3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_3.Size = new Size(642, 75);
			this.groupBox_3.TabIndex = 20;
			this.groupBox_3.TabStop = false;
			this.groupBox_3.Text = Class521.smethod_0(21005);
			this.tabPage_2.BackColor = SystemColors.Control;
			this.tabPage_2.Controls.Add(this.pictureBox_0);
			this.tabPage_2.Controls.Add(this.labelX_0);
			this.tabPage_2.Controls.Add(this.groupBox_8);
			this.tabPage_2.Controls.Add(this.groupBox_9);
			this.tabPage_2.Location = new Point(4, 25);
			this.tabPage_2.Margin = new System.Windows.Forms.Padding(4);
			this.tabPage_2.Name = Class521.smethod_0(21022);
			this.tabPage_2.Padding = new System.Windows.Forms.Padding(4);
			this.tabPage_2.Size = new Size(682, 403);
			this.tabPage_2.TabIndex = 2;
			this.tabPage_2.Text = Class521.smethod_0(21043);
			this.pictureBox_0.BackgroundImageLayout = ImageLayout.None;
			this.pictureBox_0.Image = Class375._1683_Lightbulb_32x32;
			this.pictureBox_0.Location = new Point(22, 274);
			this.pictureBox_0.Name = Class521.smethod_0(5732);
			this.pictureBox_0.Size = new Size(20, 20);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.pictureBox_0.TabIndex = 23;
			this.pictureBox_0.TabStop = false;
			this.labelX_0.BackgroundStyle.Class = Class521.smethod_0(1449);
			this.labelX_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_0.Location = new Point(46, 275);
			this.labelX_0.Name = Class521.smethod_0(21068);
			this.labelX_0.Size = new Size(611, 121);
			this.labelX_0.TabIndex = 1;
			this.labelX_0.Text = Class521.smethod_0(21093);
			this.labelX_0.TextLineAlignment = StringAlignment.Near;
			this.labelX_0.WordWrap = true;
			this.groupBox_8.Controls.Add(this.radioButton_2);
			this.groupBox_8.Controls.Add(this.radioButton_3);
			this.groupBox_8.Controls.Add(this.groupBox_10);
			this.groupBox_8.Controls.Add(this.groupBox_11);
			this.groupBox_8.Location = new Point(363, 29);
			this.groupBox_8.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_8.Name = Class521.smethod_0(21427);
			this.groupBox_8.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_8.Size = new Size(294, 226);
			this.groupBox_8.TabIndex = 1;
			this.groupBox_8.TabStop = false;
			this.groupBox_8.Text = Class521.smethod_0(21440);
			this.radioButton_2.AutoSize = true;
			this.radioButton_2.Location = new Point(25, 65);
			this.radioButton_2.Name = Class521.smethod_0(21473);
			this.radioButton_2.Size = new Size(118, 19);
			this.radioButton_2.TabIndex = 3;
			this.radioButton_2.TabStop = true;
			this.radioButton_2.Text = Class521.smethod_0(21510);
			this.radioButton_2.UseVisualStyleBackColor = true;
			this.radioButton_3.AutoSize = true;
			this.radioButton_3.Location = new Point(25, 35);
			this.radioButton_3.Name = Class521.smethod_0(21535);
			this.radioButton_3.Size = new Size(148, 19);
			this.radioButton_3.TabIndex = 2;
			this.radioButton_3.TabStop = true;
			this.radioButton_3.Text = Class521.smethod_0(21572);
			this.radioButton_3.UseVisualStyleBackColor = true;
			this.groupBox_10.Controls.Add(this.dateTimePicker_0);
			this.groupBox_10.Location = new Point(48, 154);
			this.groupBox_10.Margin = new System.Windows.Forms.Padding(4);
			this.groupBox_10.Name = Class521.smethod_0(21605);
			this.groupBox_10.Padding = new System.Windows.Forms.Padding(4);
			this.groupBox_10.Size = new Size(221, 55);
			this.groupBox_10.TabIndex = 1;
			this.groupBox_10.TabStop = false;
			this.groupBox_10.Text = Class521.smethod_0(21638);
			this.dateTimePicker_0.Format = DateTimePickerFormat.Time;
			this.dateTimePicker_0.Location = new Point(52, 19);
			this.dateTimePicker_0.Margin = new System.Windows.Forms.Padding(4);
			this.dateTimePicker_0.Name = Class521.smethod_0(21647);
			this.dateTimePicker_0.ShowUpDown = true;
			this.dateTimePicker_0.Size = new Size(119, 25);
			this.dateTimePicker_0.TabIndex = 1;
			this.groupBox_11.Controls.Add(this.comboBox_8);
			this.groupBox_11.Controls.Add(this.radioButton_0);
			this.groupBox_11.Controls.Add(this.radioButton_1);
			this.groupBox_11.Location = new Point(48, 94);
			this.groupBox_11.Margin = new System.Windows.Forms.Padding(4);
			this.groupBox_11.Name = Class521.smethod_0(21676);
			this.groupBox_11.Padding = new System.Windows.Forms.Padding(4);
			this.groupBox_11.Size = new Size(221, 55);
			this.groupBox_11.TabIndex = 0;
			this.groupBox_11.TabStop = false;
			this.groupBox_11.Text = Class521.smethod_0(21713);
			this.comboBox_8.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_8.FormattingEnabled = true;
			this.comboBox_8.Items.AddRange(new object[]
			{
				Class521.smethod_0(21722),
				Class521.smethod_0(21727),
				Class521.smethod_0(21732),
				Class521.smethod_0(21737),
				Class521.smethod_0(21742),
				Class521.smethod_0(21747),
				Class521.smethod_0(21752)
			});
			this.comboBox_8.Location = new Point(81, 20);
			this.comboBox_8.Name = Class521.smethod_0(21757);
			this.comboBox_8.Size = new Size(48, 23);
			this.comboBox_8.TabIndex = 5;
			this.radioButton_0.AutoSize = true;
			this.radioButton_0.Location = new Point(21, 22);
			this.radioButton_0.Margin = new System.Windows.Forms.Padding(4);
			this.radioButton_0.Name = Class521.smethod_0(21794);
			this.radioButton_0.Size = new Size(58, 19);
			this.radioButton_0.TabIndex = 3;
			this.radioButton_0.TabStop = true;
			this.radioButton_0.Text = Class521.smethod_0(21815);
			this.radioButton_0.UseVisualStyleBackColor = true;
			this.radioButton_1.AutoSize = true;
			this.radioButton_1.Location = new Point(144, 22);
			this.radioButton_1.Margin = new System.Windows.Forms.Padding(4);
			this.radioButton_1.Name = Class521.smethod_0(21824);
			this.radioButton_1.Size = new Size(58, 19);
			this.radioButton_1.TabIndex = 4;
			this.radioButton_1.TabStop = true;
			this.radioButton_1.Text = Class521.smethod_0(21845);
			this.radioButton_1.UseVisualStyleBackColor = true;
			this.groupBox_9.Controls.Add(this.groupBox_12);
			this.groupBox_9.Controls.Add(this.button_10);
			this.groupBox_9.Controls.Add(this.label_16);
			this.groupBox_9.Controls.Add(this.progressBar_1);
			this.groupBox_9.Controls.Add(this.label_13);
			this.groupBox_9.Controls.Add(this.label_15);
			this.groupBox_9.Controls.Add(this.button_14);
			this.groupBox_9.Controls.Add(this.button_13);
			this.groupBox_9.Controls.Add(this.button_12);
			this.groupBox_9.Controls.Add(this.comboBox_7);
			this.groupBox_9.Location = new Point(22, 29);
			this.groupBox_9.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_9.Name = Class521.smethod_0(21854);
			this.groupBox_9.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_9.Size = new Size(321, 226);
			this.groupBox_9.TabIndex = 1;
			this.groupBox_9.TabStop = false;
			this.groupBox_9.Text = Class521.smethod_0(21867);
			this.groupBox_12.Location = new Point(22, 149);
			this.groupBox_12.Name = Class521.smethod_0(10705);
			this.groupBox_12.Size = new Size(279, 2);
			this.groupBox_12.TabIndex = 17;
			this.groupBox_12.TabStop = false;
			this.button_10.Location = new Point(227, 159);
			this.button_10.Margin = new System.Windows.Forms.Padding(4);
			this.button_10.Name = Class521.smethod_0(21916);
			this.button_10.Size = new Size(75, 29);
			this.button_10.TabIndex = 3;
			this.button_10.Text = Class521.smethod_0(21937);
			this.button_10.UseVisualStyleBackColor = true;
			this.label_16.AutoSize = true;
			this.label_16.Location = new Point(18, 120);
			this.label_16.Name = Class521.smethod_0(21946);
			this.label_16.Size = new Size(120, 15);
			this.label_16.TabIndex = 9;
			this.label_16.Text = Class521.smethod_0(21975);
			this.progressBar_1.Location = new Point(22, 166);
			this.progressBar_1.Margin = new System.Windows.Forms.Padding(4);
			this.progressBar_1.Name = Class521.smethod_0(22008);
			this.progressBar_1.Size = new Size(191, 10);
			this.progressBar_1.TabIndex = 2;
			this.label_13.AutoSize = true;
			this.label_13.Location = new Point(19, 196);
			this.label_13.Name = Class521.smethod_0(22033);
			this.label_13.Size = new Size(129, 15);
			this.label_13.TabIndex = 1;
			this.label_13.Text = Class521.smethod_0(22062);
			this.label_15.Location = new Point(17, 70);
			this.label_15.Name = Class521.smethod_0(22095);
			this.label_15.Size = new Size(169, 44);
			this.label_15.TabIndex = 8;
			this.label_15.Text = Class521.smethod_0(22124);
			this.button_14.Location = new Point(227, 71);
			this.button_14.Margin = new System.Windows.Forms.Padding(4);
			this.button_14.Name = Class521.smethod_0(22149);
			this.button_14.Size = new Size(75, 29);
			this.button_14.TabIndex = 7;
			this.button_14.Text = Class521.smethod_0(22174);
			this.button_14.UseVisualStyleBackColor = true;
			this.button_13.Location = new Point(227, 110);
			this.button_13.Margin = new System.Windows.Forms.Padding(4);
			this.button_13.Name = Class521.smethod_0(22183);
			this.button_13.Size = new Size(75, 29);
			this.button_13.TabIndex = 6;
			this.button_13.Text = Class521.smethod_0(22208);
			this.button_13.UseVisualStyleBackColor = true;
			this.button_12.Location = new Point(227, 33);
			this.button_12.Margin = new System.Windows.Forms.Padding(4);
			this.button_12.Name = Class521.smethod_0(22217);
			this.button_12.Size = new Size(75, 29);
			this.button_12.TabIndex = 5;
			this.button_12.Text = Class521.smethod_0(22242);
			this.button_12.UseVisualStyleBackColor = true;
			this.comboBox_7.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_7.FormattingEnabled = true;
			this.comboBox_7.Location = new Point(20, 36);
			this.comboBox_7.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_7.Name = Class521.smethod_0(22251);
			this.comboBox_7.Size = new Size(190, 23);
			this.comboBox_7.TabIndex = 0;
			this.button_2.DialogResult = DialogResult.Cancel;
			this.button_2.Location = new Point(590, 452);
			this.button_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_2.Name = Class521.smethod_0(7421);
			this.button_2.Size = new Size(120, 30);
			this.button_2.TabIndex = 22;
			this.button_2.Text = Class521.smethod_0(5783);
			this.button_2.UseVisualStyleBackColor = true;
			this.button_3.DialogResult = DialogResult.OK;
			this.button_3.Location = new Point(456, 452);
			this.button_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_3.Name = Class521.smethod_0(7442);
			this.button_3.Size = new Size(120, 30);
			this.button_3.TabIndex = 21;
			this.button_3.Text = Class521.smethod_0(5801);
			this.button_3.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_3;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.ClientSize = new Size(737, 493);
			base.Controls.Add(this.button_2);
			base.Controls.Add(this.button_3);
			base.Controls.Add(this.tabControl_0);
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(22276);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = Class521.smethod_0(22293);
			this.groupBox_0.ResumeLayout(false);
			this.groupBox_0.PerformLayout();
			this.groupBox_1.ResumeLayout(false);
			this.groupBox_1.PerformLayout();
			((ISupportInitialize)this.dateTimeInput_0).EndInit();
			((ISupportInitialize)this.dateTimeInput_1).EndInit();
			this.groupBox_2.ResumeLayout(false);
			this.groupBox_2.PerformLayout();
			this.tabControl_0.ResumeLayout(false);
			this.tabPage_0.ResumeLayout(false);
			this.groupBox_5.ResumeLayout(false);
			this.groupBox_5.PerformLayout();
			this.groupBox_6.ResumeLayout(false);
			this.groupBox_7.ResumeLayout(false);
			this.groupBox_7.PerformLayout();
			this.groupBox_4.ResumeLayout(false);
			this.groupBox_4.PerformLayout();
			this.tabPage_1.ResumeLayout(false);
			this.groupBox_3.ResumeLayout(false);
			this.groupBox_3.PerformLayout();
			this.tabPage_2.ResumeLayout(false);
			((ISupportInitialize)this.pictureBox_0).EndInit();
			this.groupBox_8.ResumeLayout(false);
			this.groupBox_8.PerformLayout();
			this.groupBox_10.ResumeLayout(false);
			this.groupBox_11.ResumeLayout(false);
			this.groupBox_11.PerformLayout();
			this.groupBox_9.ResumeLayout(false);
			this.groupBox_9.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000655 RID: 1621
		private List<TradingSymbol> list_0;

		// Token: 0x04000656 RID: 1622
		private List<Transaction> list_1;

		// Token: 0x04000657 RID: 1623
		private string string_0;

		// Token: 0x04000658 RID: 1624
		private string string_1;

		// Token: 0x04000659 RID: 1625
		private const string string_2 = "TExHisData.csv";

		// Token: 0x0400065A RID: 1626
		private const string string_3 = "TExTrans.csv";

		// Token: 0x0400065B RID: 1627
		private string string_4;

		// Token: 0x0400065C RID: 1628
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x0400065D RID: 1629
		private List<string> list_2;

		// Token: 0x0400065E RID: 1630
		private List<string> list_3;

		// Token: 0x0400065F RID: 1631
		private System.Windows.Forms.ToolTip toolTip_0;

		// Token: 0x04000660 RID: 1632
		private IContainer icontainer_0;

		// Token: 0x04000661 RID: 1633
		private GroupBox groupBox_0;

		// Token: 0x04000662 RID: 1634
		private ComboBox comboBox_0;

		// Token: 0x04000663 RID: 1635
		private Label label_0;

		// Token: 0x04000664 RID: 1636
		private ComboBox comboBox_1;

		// Token: 0x04000665 RID: 1637
		private Label label_1;

		// Token: 0x04000666 RID: 1638
		private GroupBox groupBox_1;

		// Token: 0x04000667 RID: 1639
		private ComboBox comboBox_2;

		// Token: 0x04000668 RID: 1640
		private Label label_2;

		// Token: 0x04000669 RID: 1641
		private Label label_3;

		// Token: 0x0400066A RID: 1642
		private DateTimeInput dateTimeInput_0;

		// Token: 0x0400066B RID: 1643
		private DateTimeInput dateTimeInput_1;

		// Token: 0x0400066C RID: 1644
		private Label label_4;

		// Token: 0x0400066D RID: 1645
		private Label label_5;

		// Token: 0x0400066E RID: 1646
		private ComboBox comboBox_3;

		// Token: 0x0400066F RID: 1647
		private CheckBox checkBox_0;

		// Token: 0x04000670 RID: 1648
		private TextBox textBox_0;

		// Token: 0x04000671 RID: 1649
		private Button button_0;

		// Token: 0x04000672 RID: 1650
		private Label label_6;

		// Token: 0x04000673 RID: 1651
		private ProgressBar progressBar_0;

		// Token: 0x04000674 RID: 1652
		private Button button_1;

		// Token: 0x04000675 RID: 1653
		private GroupBox groupBox_2;

		// Token: 0x04000676 RID: 1654
		private System.Windows.Forms.TabControl tabControl_0;

		// Token: 0x04000677 RID: 1655
		private TabPage tabPage_0;

		// Token: 0x04000678 RID: 1656
		private TabPage tabPage_1;

		// Token: 0x04000679 RID: 1657
		private Button button_2;

		// Token: 0x0400067A RID: 1658
		private Button button_3;

		// Token: 0x0400067B RID: 1659
		private Label label_7;

		// Token: 0x0400067C RID: 1660
		private CheckBox checkBox_1;

		// Token: 0x0400067D RID: 1661
		private GroupBox groupBox_3;

		// Token: 0x0400067E RID: 1662
		private GroupBox groupBox_4;

		// Token: 0x0400067F RID: 1663
		private Label label_8;

		// Token: 0x04000680 RID: 1664
		private ComboBox comboBox_4;

		// Token: 0x04000681 RID: 1665
		private Label label_9;

		// Token: 0x04000682 RID: 1666
		private ComboBox comboBox_5;

		// Token: 0x04000683 RID: 1667
		private Label label_10;

		// Token: 0x04000684 RID: 1668
		private Button button_4;

		// Token: 0x04000685 RID: 1669
		private TextBox textBox_1;

		// Token: 0x04000686 RID: 1670
		private Button button_5;

		// Token: 0x04000687 RID: 1671
		private CheckBox checkBox_2;

		// Token: 0x04000688 RID: 1672
		private Label label_11;

		// Token: 0x04000689 RID: 1673
		private Label label_12;

		// Token: 0x0400068A RID: 1674
		private Button button_6;

		// Token: 0x0400068B RID: 1675
		private Button button_7;

		// Token: 0x0400068C RID: 1676
		private ComboBox comboBox_6;

		// Token: 0x0400068D RID: 1677
		private Button button_8;

		// Token: 0x0400068E RID: 1678
		private GroupBox groupBox_5;

		// Token: 0x0400068F RID: 1679
		private GroupBox groupBox_6;

		// Token: 0x04000690 RID: 1680
		private GroupBox groupBox_7;

		// Token: 0x04000691 RID: 1681
		private Button button_9;

		// Token: 0x04000692 RID: 1682
		private TabPage tabPage_2;

		// Token: 0x04000693 RID: 1683
		private GroupBox groupBox_8;

		// Token: 0x04000694 RID: 1684
		private GroupBox groupBox_9;

		// Token: 0x04000695 RID: 1685
		private ComboBox comboBox_7;

		// Token: 0x04000696 RID: 1686
		private LabelX labelX_0;

		// Token: 0x04000697 RID: 1687
		private Button button_10;

		// Token: 0x04000698 RID: 1688
		private ProgressBar progressBar_1;

		// Token: 0x04000699 RID: 1689
		private Label label_13;

		// Token: 0x0400069A RID: 1690
		private RadioButton radioButton_0;

		// Token: 0x0400069B RID: 1691
		private RadioButton radioButton_1;

		// Token: 0x0400069C RID: 1692
		private DateTimePicker dateTimePicker_0;

		// Token: 0x0400069D RID: 1693
		private GroupBox groupBox_10;

		// Token: 0x0400069E RID: 1694
		private GroupBox groupBox_11;

		// Token: 0x0400069F RID: 1695
		private Label label_14;

		// Token: 0x040006A0 RID: 1696
		private TextBox textBox_2;

		// Token: 0x040006A1 RID: 1697
		private Button button_11;

		// Token: 0x040006A2 RID: 1698
		private Button button_12;

		// Token: 0x040006A3 RID: 1699
		private Button button_13;

		// Token: 0x040006A4 RID: 1700
		private PictureBox pictureBox_0;

		// Token: 0x040006A5 RID: 1701
		private Button button_14;

		// Token: 0x040006A6 RID: 1702
		private Label label_15;

		// Token: 0x040006A7 RID: 1703
		private ComboBox comboBox_8;

		// Token: 0x040006A8 RID: 1704
		private Label label_16;

		// Token: 0x040006A9 RID: 1705
		private GroupBox groupBox_12;

		// Token: 0x040006AA RID: 1706
		private RadioButton radioButton_2;

		// Token: 0x040006AB RID: 1707
		private RadioButton radioButton_3;

		// Token: 0x02000157 RID: 343
		[CompilerGenerated]
		private sealed class Class196
		{
			// Token: 0x06000D3E RID: 3390 RVA: 0x00054288 File Offset: 0x00052488
			internal bool method_0(ExchgHouse exchgHouse_1)
			{
				return exchgHouse_1.ID == this.stkSymbol_0.ExchangeID;
			}

			// Token: 0x06000D3F RID: 3391 RVA: 0x000542AC File Offset: 0x000524AC
			internal bool method_1(StkSymbol stkSymbol_1)
			{
				return stkSymbol_1.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x040006AC RID: 1708
			public StkSymbol stkSymbol_0;

			// Token: 0x040006AD RID: 1709
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x02000159 RID: 345
		[CompilerGenerated]
		private sealed class Class197
		{
			// Token: 0x06000D47 RID: 3399 RVA: 0x00054330 File Offset: 0x00052530
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x040006B3 RID: 1715
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x0200015A RID: 346
		[CompilerGenerated]
		private sealed class Class198
		{
			// Token: 0x06000D49 RID: 3401 RVA: 0x00054354 File Offset: 0x00052554
			internal bool method_0(Account account_1)
			{
				return account_1.ID != this.account_0.ID;
			}

			// Token: 0x040006B4 RID: 1716
			public Account account_0;
		}

		// Token: 0x0200015B RID: 347
		[CompilerGenerated]
		private sealed class Class199
		{
			// Token: 0x06000D4B RID: 3403 RVA: 0x0005437C File Offset: 0x0005257C
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B5 RID: 1717
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200015C RID: 348
		[CompilerGenerated]
		private sealed class Class200
		{
			// Token: 0x06000D4D RID: 3405 RVA: 0x000543A0 File Offset: 0x000525A0
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D4E RID: 3406 RVA: 0x000543C4 File Offset: 0x000525C4
			internal bool method_1(Order order_0)
			{
				return order_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D4F RID: 3407 RVA: 0x000543A0 File Offset: 0x000525A0
			internal bool method_2(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D50 RID: 3408 RVA: 0x000543C4 File Offset: 0x000525C4
			internal bool method_3(Order order_0)
			{
				return order_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D51 RID: 3409 RVA: 0x000543E8 File Offset: 0x000525E8
			internal bool method_4(CondOrder condOrder_0)
			{
				return condOrder_0.SymbID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B6 RID: 1718
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200015D RID: 349
		[CompilerGenerated]
		private sealed class Class201
		{
			// Token: 0x06000D53 RID: 3411 RVA: 0x0005440C File Offset: 0x0005260C
			internal bool method_0(Transaction transaction_1)
			{
				if (transaction_1.ID == this.transaction_0.ID && transaction_1.CreateTime == this.transaction_0.CreateTime)
				{
					decimal? num = transaction_1.Fee;
					decimal? num2 = this.transaction_0.Fee;
					if ((num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)) && transaction_1.Units == this.transaction_0.Units && transaction_1.TransType == this.transaction_0.TransType && transaction_1.Price == this.transaction_0.Price)
					{
						num2 = transaction_1.Profit;
						num = this.transaction_0.Profit;
						return num2.GetValueOrDefault() == num.GetValueOrDefault() & num2 != null == (num != null);
					}
				}
				return false;
			}

			// Token: 0x040006B7 RID: 1719
			public Transaction transaction_0;
		}

		// Token: 0x0200015E RID: 350
		[CompilerGenerated]
		private sealed class Class202
		{
			// Token: 0x06000D55 RID: 3413 RVA: 0x00054500 File Offset: 0x00052700
			internal bool method_0(Order order_1)
			{
				bool result;
				if (order_1.SymbolID == this.order_0.SymbolID && order_1.CreateTime == this.order_0.CreateTime && order_1.OrderType == this.order_0.OrderType && order_1.Price == this.order_0.Price && order_1.OrderStatus == order_1.OrderStatus)
				{
					result = (order_1.Units == this.order_0.Units);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040006B8 RID: 1720
			public Order order_0;
		}

		// Token: 0x0200015F RID: 351
		[CompilerGenerated]
		private sealed class Class203
		{
			// Token: 0x06000D57 RID: 3415 RVA: 0x0005458C File Offset: 0x0005278C
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D58 RID: 3416 RVA: 0x0005458C File Offset: 0x0005278C
			internal bool method_1(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B9 RID: 1721
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x02000160 RID: 352
		[CompilerGenerated]
		private sealed class Class204
		{
			// Token: 0x06000D5A RID: 3418 RVA: 0x000545B0 File Offset: 0x000527B0
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006BA RID: 1722
			public TradingSymbol tradingSymbol_0;
		}
	}
}
