﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns10;
using ns12;
using ns18;
using ns20;
using ns26;
using ns27;
using ns4;
using TEx;
using TEx.Util;
using Vlc.DotNet.Core;
using Vlc.DotNet.Forms;

namespace ns23
{
	// Token: 0x020002B6 RID: 694
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class Control12 : UserControl
	{
		// Token: 0x06001E9A RID: 7834 RVA: 0x000DE5F4 File Offset: 0x000DC7F4
		public Control12()
		{
			this.method_33();
			this.panel_1.MouseHover += this.panel_1_MouseHover;
			this.panel_1.MouseLeave += this.panel_1_MouseLeave;
			this.panel_1.Click += this.panel_1_Click;
			this.panel_2.MouseHover += this.panel_2_MouseHover;
			this.panel_2.MouseLeave += this.panel_2_MouseLeave;
			this.panel_2.Click += this.panel_2_Click;
			this.panel_3.MouseHover += this.panel_3_MouseHover;
			this.panel_3.MouseLeave += this.panel_3_MouseLeave;
			this.panel_3.Click += this.panel_3_Click;
			this.panel_4.MouseHover += this.panel_4_MouseHover;
			this.panel_4.MouseLeave += this.panel_4_MouseLeave;
			this.panel_4.Click += this.panel_4_Click;
			this.control0_0.MouseUp += this.control0_0_MouseUp;
			this.control0_1.ValueChanged += this.method_22;
			base.HandleCreated += this.Control12_HandleCreated;
			Base.UI.ChartThemeChanged += this.method_4;
		}

		// Token: 0x06001E9B RID: 7835 RVA: 0x0000CCDB File Offset: 0x0000AEDB
		private void method_0(bool bool_2 = false)
		{
			base.Invoke(new Action(this.method_34));
		}

		// Token: 0x06001E9C RID: 7836 RVA: 0x000DE798 File Offset: 0x000DC998
		private void method_1()
		{
			Color color = Base.UI.smethod_34();
			Color color2 = Base.UI.smethod_35();
			this.itemPanel_0.BackgroundStyle.BackColor = color;
			this.itemPanel_0.BackgroundStyle.BackColor2 = color;
			this.itemPanel_0.BackColor = color;
			this.itemPanel_0.BackgroundStyle.TextColor = color2;
			this.itemPanel_0.ForeColor = color2;
			eButtonColor colorTable = this.method_2();
			foreach (object obj in this.itemPanel_0.Items)
			{
				ButtonItem buttonItem = (ButtonItem)obj;
				buttonItem.ColorTable = colorTable;
				if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
				{
					buttonItem.ForeColor = Class181.color_3;
				}
				else
				{
					buttonItem.ForeColor = Color.White;
				}
			}
		}

		// Token: 0x06001E9D RID: 7837 RVA: 0x000DE884 File Offset: 0x000DCA84
		private eButtonColor method_2()
		{
			eButtonColor result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				result = eButtonColor.BlueOrb;
			}
			else
			{
				result = eButtonColor.Blue;
			}
			return result;
		}

		// Token: 0x06001E9E RID: 7838 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_3()
		{
		}

		// Token: 0x06001E9F RID: 7839 RVA: 0x0000CCF2 File Offset: 0x0000AEF2
		private void method_4(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001EA0 RID: 7840 RVA: 0x0000CCFC File Offset: 0x0000AEFC
		private void Control12_HandleCreated(object sender, EventArgs e)
		{
			this.method_1();
			this.method_0(false);
			this.method_3();
			this.method_5();
		}

		// Token: 0x06001EA1 RID: 7841 RVA: 0x000DE8A8 File Offset: 0x000DCAA8
		private void method_5()
		{
			ButtonItem buttonItem = this.itemPanel_0.Items[0] as ButtonItem;
			Class359 @class = buttonItem.Tag as Class359;
			this.string_0 = @class.VideoUrl;
			this.method_7(buttonItem);
		}

		// Token: 0x06001EA2 RID: 7842 RVA: 0x000DE8F0 File Offset: 0x000DCAF0
		private void method_6(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			Class359 @class = buttonItem.Tag as Class359;
			if (TApp.IsTrialUser && !@class.IsFree)
			{
				if (MessageBox.Show(Class521.smethod_0(90613), Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) != DialogResult.OK)
				{
					return;
				}
				try
				{
					Process.Start(new ProcessStartInfo(Class521.smethod_0(9769) + TApp.HOST + Class521.smethod_0(90734)));
					return;
				}
				catch
				{
					return;
				}
			}
			this.method_7(buttonItem);
		}

		// Token: 0x06001EA3 RID: 7843 RVA: 0x000DE988 File Offset: 0x000DCB88
		private void method_7(ButtonItem buttonItem_0)
		{
			this.IsPlaying = false;
			buttonItem_0.Checked = true;
			int num = 0;
			while (this.bool_0)
			{
				if (Utility.CanExactDiv(num, 100))
				{
					this.IsPlaying = false;
				}
				Thread.Sleep(10);
				num = num;
			}
			Class359 @class = buttonItem_0.Tag as Class359;
			this.string_0 = @class.VideoUrl;
			ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_35));
		}

		// Token: 0x06001EA4 RID: 7844 RVA: 0x0000CD19 File Offset: 0x0000AF19
		private void method_8(object sender, EventArgs e)
		{
			this.Cursor = Cursors.Hand;
		}

		// Token: 0x06001EA5 RID: 7845 RVA: 0x0000A456 File Offset: 0x00008656
		private void method_9(object sender, EventArgs e)
		{
			this.Cursor = Cursors.Default;
		}

		// Token: 0x06001EA6 RID: 7846 RVA: 0x0000CD28 File Offset: 0x0000AF28
		private void method_10(object sender, VlcMediaPlayerPlayingEventArgs e)
		{
			this.bool_0 = true;
		}

		// Token: 0x06001EA7 RID: 7847 RVA: 0x0000CD33 File Offset: 0x0000AF33
		private void vlcControl_0_MouseClick(object sender, MouseEventArgs e)
		{
			this.method_23();
		}

		// Token: 0x06001EA8 RID: 7848 RVA: 0x0000CD3D File Offset: 0x0000AF3D
		private void method_11(object sender, VlcMediaPlayerVideoOutChangedEventArgs e)
		{
			if (e.NewCount > 0)
			{
				this.method_20();
				if (this.IsStartPausing)
				{
					this.IsPlaying = false;
					this.IsStartPausing = false;
					base.Invoke(new Action(this.method_36));
				}
			}
		}

		// Token: 0x06001EA9 RID: 7849 RVA: 0x0000CD79 File Offset: 0x0000AF79
		private void method_12(object sender, VlcMediaPlayerPausedEventArgs e)
		{
			this.bool_0 = false;
		}

		// Token: 0x06001EAA RID: 7850 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_13(object sender, VlcMediaPlayerScrambledChangedEventArgs e)
		{
		}

		// Token: 0x06001EAB RID: 7851 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_14(object sender, VlcMediaPlayerPausableChangedEventArgs e)
		{
		}

		// Token: 0x06001EAC RID: 7852 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_15(object sender, VlcMediaPlayerSeekableChangedEventArgs e)
		{
		}

		// Token: 0x06001EAD RID: 7853 RVA: 0x0000CD79 File Offset: 0x0000AF79
		private void method_16(object sender, VlcMediaPlayerStoppedEventArgs e)
		{
			this.bool_0 = false;
		}

		// Token: 0x06001EAE RID: 7854 RVA: 0x000DE9F8 File Offset: 0x000DCBF8
		private void method_17(object sender, VlcMediaPlayerTimeChangedEventArgs e)
		{
			if (this.vlcControl_0.Length > 0L && this.vlcControl_0.Length - e.NewTime < 500L)
			{
				base.Invoke(new Action(this.method_37));
			}
		}

		// Token: 0x06001EAF RID: 7855 RVA: 0x000DEA50 File Offset: 0x000DCC50
		private void method_18(object sender, VlcMediaPlayerPositionChangedEventArgs e)
		{
			Control12.Class357 @class = new Control12.Class357();
			@class.control12_0 = this;
			double num = (double)Math.Abs(e.NewPosition - this.float_0);
			@class.decimal_0 = Convert.ToDecimal(e.NewPosition * 100f);
			if (@class.decimal_0 > 99m)
			{
				@class.decimal_0 = 100m;
			}
			base.Invoke(new Action(@class.method_0));
			this.float_0 = e.NewPosition;
		}

		// Token: 0x06001EB0 RID: 7856 RVA: 0x000DEAD8 File Offset: 0x000DCCD8
		private void method_19(object sender, VlcMediaPlayerLengthChangedEventArgs e)
		{
			if (this.vlcControl_0.Length >= 0L)
			{
				Control12.Class358 @class = new Control12.Class358();
				@class.control12_0 = this;
				@class.string_0 = this.method_21(this.vlcControl_0.Length);
				if (this.label_0.Text != @class.string_0)
				{
					base.Invoke(new Action(@class.method_0));
				}
			}
		}

		// Token: 0x06001EB1 RID: 7857 RVA: 0x000DEB4C File Offset: 0x000DCD4C
		private void method_20()
		{
			if (this.vlcControl_0.BackgroundImage != null)
			{
				base.Invoke(new Action(this.method_38));
				int num = Convert.ToInt32(this.control0_1.Value);
				if (this.vlcControl_0.Audio.Volume != num)
				{
					this.vlcControl_0.Audio.Volume = num;
				}
			}
		}

		// Token: 0x06001EB2 RID: 7858 RVA: 0x000DEBB0 File Offset: 0x000DCDB0
		private string method_21(long long_0)
		{
			TimeSpan timeSpan = TimeSpan.FromMilliseconds((double)long_0);
			return string.Concat(new string[]
			{
				timeSpan.Hours.ToString(Class521.smethod_0(87019)),
				Class521.smethod_0(50733),
				timeSpan.Minutes.ToString(Class521.smethod_0(87019)),
				Class521.smethod_0(50733),
				timeSpan.Seconds.ToString(Class521.smethod_0(87019))
			});
		}

		// Token: 0x06001EB3 RID: 7859 RVA: 0x0000CD84 File Offset: 0x0000AF84
		private void method_22(object sender, EventArgs e)
		{
			this.vlcControl_0.Audio.Volume = Convert.ToInt32(Math.Round(this.control0_1.Value));
		}

		// Token: 0x06001EB4 RID: 7860 RVA: 0x0000CD33 File Offset: 0x0000AF33
		private void panel_1_Click(object sender, EventArgs e)
		{
			this.method_23();
		}

		// Token: 0x06001EB5 RID: 7861 RVA: 0x000DEC44 File Offset: 0x000DCE44
		public void method_23()
		{
			if (!this.IsPlaying)
			{
				this.IsStartPausing = false;
				if ((double)this.vlcControl_0.Position > 0.99)
				{
					this.vlcControl_0.Position = 0.002f;
				}
			}
			this.IsPlaying = !this.IsPlaying;
		}

		// Token: 0x06001EB6 RID: 7862 RVA: 0x0000CDAD File Offset: 0x0000AFAD
		public void method_24()
		{
			if (this.IsPlaying)
			{
				this.IsPlaying = false;
			}
		}

		// Token: 0x06001EB7 RID: 7863 RVA: 0x0000CDC0 File Offset: 0x0000AFC0
		public void method_25(bool bool_2)
		{
			if (this.vlcControl_0 != null)
			{
				this.vlcControl_0.Visible = bool_2;
				this.vlcControl_0.Refresh();
			}
		}

		// Token: 0x06001EB8 RID: 7864 RVA: 0x000DEC98 File Offset: 0x000DCE98
		private void panel_1_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			Image image_;
			if (this.IsPlaying)
			{
				image_ = Class375.pause_blue2_28px;
			}
			else
			{
				image_ = Class375.play_blue2_28px;
			}
			this.method_29(control_, image_);
		}

		// Token: 0x06001EB9 RID: 7865 RVA: 0x000DECCC File Offset: 0x000DCECC
		private void panel_1_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			Image image_;
			if (this.IsPlaying)
			{
				image_ = Class375.pause_blue1_28px;
			}
			else
			{
				image_ = Class375.play_blue1_28px;
			}
			this.method_28(control_, image_);
		}

		// Token: 0x06001EBA RID: 7866 RVA: 0x000DED00 File Offset: 0x000DCF00
		private void panel_2_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class375.stop_blue2_28px);
		}

		// Token: 0x06001EBB RID: 7867 RVA: 0x000DED24 File Offset: 0x000DCF24
		private void panel_2_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class375.stop_blue1_28px);
		}

		// Token: 0x06001EBC RID: 7868 RVA: 0x0000CDE3 File Offset: 0x0000AFE3
		private void panel_2_Click(object sender, EventArgs e)
		{
			this.method_26();
		}

		// Token: 0x06001EBD RID: 7869 RVA: 0x0000CDED File Offset: 0x0000AFED
		private void method_26()
		{
			base.Invoke(new Action(this.method_39));
			this.method_27();
		}

		// Token: 0x06001EBE RID: 7870 RVA: 0x0000CE0A File Offset: 0x0000B00A
		private void method_27()
		{
			base.Invoke(new Action(this.method_40));
		}

		// Token: 0x06001EBF RID: 7871 RVA: 0x000DED48 File Offset: 0x000DCF48
		private void panel_3_Click(object sender, EventArgs e)
		{
			decimal num = this.control0_1.Value + 10m;
			if (num > 100m)
			{
				num = 100m;
			}
			this.control0_1.Value = num;
		}

		// Token: 0x06001EC0 RID: 7872 RVA: 0x000DED94 File Offset: 0x000DCF94
		private void panel_3_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class375.volume_inc_blue2_28px);
		}

		// Token: 0x06001EC1 RID: 7873 RVA: 0x000DEDB8 File Offset: 0x000DCFB8
		private void panel_3_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class375.volume_inc_blue1_28px);
		}

		// Token: 0x06001EC2 RID: 7874 RVA: 0x000DEDDC File Offset: 0x000DCFDC
		private void panel_4_Click(object sender, EventArgs e)
		{
			decimal num = this.control0_1.Value - 10m;
			if (num < 0m)
			{
				num = 0m;
			}
			this.control0_1.Value = num;
		}

		// Token: 0x06001EC3 RID: 7875 RVA: 0x000DEE24 File Offset: 0x000DD024
		private void panel_4_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class375.volume_dec_blue2_28px);
		}

		// Token: 0x06001EC4 RID: 7876 RVA: 0x000DEE48 File Offset: 0x000DD048
		private void panel_4_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class375.volume_dec_blue1_28px);
		}

		// Token: 0x06001EC5 RID: 7877 RVA: 0x0000CE21 File Offset: 0x0000B021
		private void method_28(Control control_0, Image image_0 = null)
		{
			this.method_30(control_0, this.color_1, image_0);
		}

		// Token: 0x06001EC6 RID: 7878 RVA: 0x0000CE33 File Offset: 0x0000B033
		private void method_29(Control control_0, Image image_0 = null)
		{
			this.method_30(control_0, this.color_0, image_0);
		}

		// Token: 0x06001EC7 RID: 7879 RVA: 0x0000CE45 File Offset: 0x0000B045
		private void method_30(Control control_0, Color color_2, Image image_0 = null)
		{
			if (control_0.BackColor != color_2)
			{
				control_0.BackColor = color_2;
			}
			if (image_0 != null && control_0.BackgroundImage != image_0)
			{
				control_0.BackgroundImage = image_0;
			}
		}

		// Token: 0x06001EC8 RID: 7880 RVA: 0x000DEE6C File Offset: 0x000DD06C
		private void control0_0_MouseUp(object sender, MouseEventArgs e)
		{
			Class48.smethod_2(Class521.smethod_0(90743));
			float num = Convert.ToSingle(this.control0_0.Value / this.control0_0.Maximum);
			if (this.vlcControl_0.Length > 0L)
			{
				if ((double)Math.Abs(num - this.vlcControl_0.Position) > 0.01)
				{
					this.vlcControl_0.Position = num;
				}
			}
			else
			{
				this.IsPlaying = true;
				if (this.vlcControl_0.Length > 0L)
				{
					this.vlcControl_0.Position = num;
				}
			}
		}

		// Token: 0x06001EC9 RID: 7881 RVA: 0x0000CE71 File Offset: 0x0000B071
		private void method_31()
		{
			if (!this.IsPlaying)
			{
				this.IsStartPausing = true;
				this.IsPlaying = true;
				this.vlcControl_0.Audio.Volume = 0;
				this.method_27();
			}
		}

		// Token: 0x06001ECA RID: 7882 RVA: 0x0000CEA2 File Offset: 0x0000B0A2
		public void method_32()
		{
			base.Invoke(new Action(this.method_41));
		}

		// Token: 0x170004CB RID: 1227
		// (get) Token: 0x06001ECB RID: 7883 RVA: 0x000DEF18 File Offset: 0x000DD118
		// (set) Token: 0x06001ECC RID: 7884 RVA: 0x000DEF34 File Offset: 0x000DD134
		public bool IsPlaying
		{
			get
			{
				return this.vlcControl_0.IsPlaying;
			}
			set
			{
				if (value)
				{
					base.Invoke(new Action(this.method_42));
					ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_43));
				}
				else
				{
					ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_44));
					base.Invoke(new Action(this.method_45));
				}
			}
		}

		// Token: 0x170004CC RID: 1228
		// (get) Token: 0x06001ECD RID: 7885 RVA: 0x000DEF94 File Offset: 0x000DD194
		// (set) Token: 0x06001ECE RID: 7886 RVA: 0x0000CEB9 File Offset: 0x0000B0B9
		private bool IsStartPausing { get; set; }

		// Token: 0x06001ECF RID: 7887 RVA: 0x0000CEC4 File Offset: 0x0000B0C4
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001ED0 RID: 7888 RVA: 0x000DEFAC File Offset: 0x000DD1AC
		private void method_33()
		{
			this.panel_0 = new Panel();
			this.itemPanel_0 = new ItemPanel();
			this.expandableSplitter_0 = new ExpandableSplitter();
			this.tableLayoutPanel_0 = new TableLayoutPanel();
			this.tableLayoutPanel_1 = new TableLayoutPanel();
			this.panel_4 = new Panel();
			this.panel_3 = new Panel();
			this.panel_2 = new Panel();
			this.control0_0 = new Control0();
			this.panel_1 = new Panel();
			this.tableLayoutPanel_2 = new TableLayoutPanel();
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.control0_1 = new Control0();
			this.panel_0.SuspendLayout();
			this.tableLayoutPanel_0.SuspendLayout();
			this.tableLayoutPanel_1.SuspendLayout();
			this.tableLayoutPanel_2.SuspendLayout();
			base.SuspendLayout();
			this.panel_0.Controls.Add(this.itemPanel_0);
			this.panel_0.Dock = DockStyle.Left;
			this.panel_0.Location = new Point(0, 0);
			this.panel_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_0.Name = Class521.smethod_0(90772);
			this.panel_0.Size = new Size(465, 414);
			this.panel_0.TabIndex = 3;
			this.itemPanel_0.AutoScroll = true;
			this.itemPanel_0.BackColor = SystemColors.Control;
			this.itemPanel_0.BackgroundStyle.BackColor = Color.FromArgb(59, 59, 59);
			this.itemPanel_0.BackgroundStyle.BackColor2 = Color.FromArgb(59, 59, 59);
			this.itemPanel_0.BackgroundStyle.Class = Class521.smethod_0(60877);
			this.itemPanel_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_0.ContainerControlProcessDialogKey = true;
			this.itemPanel_0.Dock = DockStyle.Fill;
			this.itemPanel_0.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_0.Location = new Point(0, 0);
			this.itemPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.itemPanel_0.Name = Class521.smethod_0(90793);
			this.itemPanel_0.Size = new Size(465, 414);
			this.itemPanel_0.TabIndex = 0;
			this.itemPanel_0.Text = Class521.smethod_0(90793);
			this.expandableSplitter_0.BackColor = SystemColors.ControlLight;
			this.expandableSplitter_0.BackColor2 = Color.Empty;
			this.expandableSplitter_0.BackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.BackColorSchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.ExpandableControl = this.panel_0;
			this.expandableSplitter_0.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expandableSplitter_0.HotBackColor2 = Color.Empty;
			this.expandableSplitter_0.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expandableSplitter_0.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expandableSplitter_0.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expandableSplitter_0.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expandableSplitter_0.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expandableSplitter_0.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expandableSplitter_0.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expandableSplitter_0.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expandableSplitter_0.Location = new Point(465, 0);
			this.expandableSplitter_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.expandableSplitter_0.Name = Class521.smethod_0(51438);
			this.expandableSplitter_0.Size = new Size(4, 414);
			this.expandableSplitter_0.Style = eSplitterStyle.Mozilla;
			this.expandableSplitter_0.TabIndex = 5;
			this.expandableSplitter_0.TabStop = false;
			this.tableLayoutPanel_0.ColumnCount = 1;
			this.tableLayoutPanel_0.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.Controls.Add(this.tableLayoutPanel_1, 0, 1);
			this.tableLayoutPanel_0.Dock = DockStyle.Fill;
			this.tableLayoutPanel_0.Location = new Point(469, 0);
			this.tableLayoutPanel_0.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_0.Name = Class521.smethod_0(90818);
			this.tableLayoutPanel_0.RowCount = 2;
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Absolute, 34f));
			this.tableLayoutPanel_0.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
			this.tableLayoutPanel_0.Size = new Size(841, 414);
			this.tableLayoutPanel_0.TabIndex = 0;
			this.tableLayoutPanel_1.ColumnCount = 7;
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 72f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 98f));
			this.tableLayoutPanel_1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tableLayoutPanel_1.Controls.Add(this.panel_4, 4, 0);
			this.tableLayoutPanel_1.Controls.Add(this.panel_3, 6, 0);
			this.tableLayoutPanel_1.Controls.Add(this.panel_2, 1, 0);
			this.tableLayoutPanel_1.Controls.Add(this.control0_0, 3, 0);
			this.tableLayoutPanel_1.Controls.Add(this.panel_1, 0, 0);
			this.tableLayoutPanel_1.Controls.Add(this.tableLayoutPanel_2, 2, 0);
			this.tableLayoutPanel_1.Controls.Add(this.control0_1, 5, 0);
			this.tableLayoutPanel_1.Dock = DockStyle.Fill;
			this.tableLayoutPanel_1.Location = new Point(0, 380);
			this.tableLayoutPanel_1.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_1.Name = Class521.smethod_0(90843);
			this.tableLayoutPanel_1.RowCount = 1;
			this.tableLayoutPanel_1.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_1.Size = new Size(841, 34);
			this.tableLayoutPanel_1.TabIndex = 1;
			this.panel_4.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_4.BackgroundImage = Class375.volume_dec_blue1_28px;
			this.panel_4.BackgroundImageLayout = ImageLayout.Center;
			this.panel_4.Dock = DockStyle.Fill;
			this.panel_4.Location = new Point(671, 0);
			this.panel_4.Margin = new System.Windows.Forms.Padding(0);
			this.panel_4.Name = Class521.smethod_0(90872);
			this.panel_4.Size = new Size(36, 34);
			this.panel_4.TabIndex = 4;
			this.panel_3.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_3.BackgroundImage = Class375.volume_inc_blue1_28px;
			this.panel_3.BackgroundImageLayout = ImageLayout.Center;
			this.panel_3.Dock = DockStyle.Fill;
			this.panel_3.Location = new Point(805, 0);
			this.panel_3.Margin = new System.Windows.Forms.Padding(0);
			this.panel_3.Name = Class521.smethod_0(90897);
			this.panel_3.Size = new Size(36, 34);
			this.panel_3.TabIndex = 3;
			this.panel_2.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_2.BackgroundImage = Class375.stop_blue1_28px;
			this.panel_2.BackgroundImageLayout = ImageLayout.Center;
			this.panel_2.Dock = DockStyle.Fill;
			this.panel_2.Location = new Point(36, 0);
			this.panel_2.Margin = new System.Windows.Forms.Padding(0);
			this.panel_2.Name = Class521.smethod_0(90922);
			this.panel_2.Size = new Size(36, 34);
			this.panel_2.TabIndex = 2;
			this.control0_0.BackColor = Color.FromArgb(70, 77, 95);
			this.control0_0.BarPenColorBottom = Color.FromArgb(87, 94, 110);
			this.control0_0.BarPenColorTop = Color.FromArgb(55, 60, 74);
			this.control0_0.BorderRoundRectSize = new Size(8, 8);
			this.control0_0.Dock = DockStyle.Fill;
			this.control0_0.ElapsedInnerColor = Color.FromArgb(21, 56, 152);
			this.control0_0.ElapsedPenColorBottom = Color.FromArgb(99, 130, 208);
			this.control0_0.ElapsedPenColorTop = Color.FromArgb(95, 140, 180);
			this.control0_0.Font = new Font(Class521.smethod_0(24023), 6f);
			this.control0_0.ForeColor = Color.White;
			Control0 control = this.control0_0;
			int[] array = new int[4];
			array[0] = 5;
			control.LargeChange = new decimal(array);
			this.control0_0.Location = new Point(144, 0);
			this.control0_0.Margin = new System.Windows.Forms.Padding(0);
			Control0 control2 = this.control0_0;
			int[] array2 = new int[4];
			array2[0] = 100;
			control2.Maximum = new decimal(array2);
			this.control0_0.Minimum = new decimal(new int[4]);
			this.control0_0.Name = Class521.smethod_0(90939);
			this.control0_0.Padding = 3;
			Control0 control3 = this.control0_0;
			int[] array3 = new int[4];
			array3[0] = 10;
			control3.ScaleDivisions = new decimal(array3);
			Control0 control4 = this.control0_0;
			int[] array4 = new int[4];
			array4[0] = 5;
			control4.ScaleSubDivisions = new decimal(array4);
			this.control0_0.ShowDivisionsText = false;
			this.control0_0.ShowSmallScale = false;
			this.control0_0.Size = new Size(527, 34);
			Control0 control5 = this.control0_0;
			int[] array5 = new int[4];
			array5[0] = 1;
			control5.SmallChange = new decimal(array5);
			this.control0_0.TabIndex = 0;
			this.control0_0.Text = Class521.smethod_0(90956);
			this.control0_0.ThumbInnerColor = Color.FromArgb(21, 56, 152);
			this.control0_0.ThumbPenColor = Color.FromArgb(21, 56, 152);
			this.control0_0.ThumbRoundRectSize = new Size(16, 16);
			this.control0_0.ThumbSize = new Size(16, 16);
			this.control0_0.TickAdd = 0f;
			this.control0_0.TickColor = Color.White;
			this.control0_0.TickDivide = 0f;
			this.control0_0.TickStyle = TickStyle.None;
			this.control0_0.Value = new decimal(new int[4]);
			this.panel_1.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_1.BackgroundImage = Class375.play_blue1_28px;
			this.panel_1.BackgroundImageLayout = ImageLayout.Center;
			this.panel_1.Dock = DockStyle.Fill;
			this.panel_1.Location = new Point(0, 0);
			this.panel_1.Margin = new System.Windows.Forms.Padding(0);
			this.panel_1.Name = Class521.smethod_0(90973);
			this.panel_1.Size = new Size(36, 34);
			this.panel_1.TabIndex = 1;
			this.tableLayoutPanel_2.ColumnCount = 1;
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tableLayoutPanel_2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
			this.tableLayoutPanel_2.Controls.Add(this.label_0, 0, 1);
			this.tableLayoutPanel_2.Controls.Add(this.label_1, 0, 0);
			this.tableLayoutPanel_2.Location = new Point(72, 0);
			this.tableLayoutPanel_2.Margin = new System.Windows.Forms.Padding(0);
			this.tableLayoutPanel_2.Name = Class521.smethod_0(90990);
			this.tableLayoutPanel_2.RowCount = 2;
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Percent, 50f));
			this.tableLayoutPanel_2.RowStyles.Add(new RowStyle(SizeType.Percent, 50f));
			this.tableLayoutPanel_2.Size = new Size(72, 34);
			this.tableLayoutPanel_2.TabIndex = 4;
			this.label_0.AutoSize = true;
			this.label_0.BackColor = Color.FromArgb(70, 77, 95);
			this.label_0.Dock = DockStyle.Fill;
			this.label_0.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_0.ForeColor = Color.FromArgb(79, 111, 189);
			this.label_0.Location = new Point(0, 17);
			this.label_0.Margin = new System.Windows.Forms.Padding(0);
			this.label_0.Name = Class521.smethod_0(91015);
			this.label_0.Size = new Size(72, 17);
			this.label_0.TabIndex = 2;
			this.label_0.Text = Class521.smethod_0(91036);
			this.label_0.TextAlign = ContentAlignment.TopCenter;
			this.label_1.AutoSize = true;
			this.label_1.BackColor = Color.FromArgb(70, 77, 95);
			this.label_1.Dock = DockStyle.Fill;
			this.label_1.Font = new Font(Class521.smethod_0(24023), 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_1.ForeColor = Color.FromArgb(137, 159, 220);
			this.label_1.Location = new Point(0, 0);
			this.label_1.Margin = new System.Windows.Forms.Padding(0);
			this.label_1.Name = Class521.smethod_0(91049);
			this.label_1.Size = new Size(72, 17);
			this.label_1.TabIndex = 0;
			this.label_1.Text = Class521.smethod_0(91036);
			this.label_1.TextAlign = ContentAlignment.BottomCenter;
			this.control0_1.BackColor = Color.FromArgb(70, 77, 95);
			this.control0_1.BarPenColorBottom = Color.FromArgb(87, 94, 110);
			this.control0_1.BarPenColorTop = Color.FromArgb(55, 60, 74);
			this.control0_1.BorderRoundRectSize = new Size(8, 8);
			this.control0_1.ElapsedInnerColor = Color.Olive;
			this.control0_1.ElapsedPenColorBottom = Color.Goldenrod;
			this.control0_1.ElapsedPenColorTop = Color.DarkGoldenrod;
			this.control0_1.Font = new Font(Class521.smethod_0(24023), 6f);
			this.control0_1.ForeColor = Color.White;
			Control0 control6 = this.control0_1;
			int[] array6 = new int[4];
			array6[0] = 5;
			control6.LargeChange = new decimal(array6);
			this.control0_1.Location = new Point(707, 0);
			this.control0_1.Margin = new System.Windows.Forms.Padding(0);
			Control0 control7 = this.control0_1;
			int[] array7 = new int[4];
			array7[0] = 100;
			control7.Maximum = new decimal(array7);
			this.control0_1.Minimum = new decimal(new int[4]);
			this.control0_1.Name = Class521.smethod_0(91070);
			Control0 control8 = this.control0_1;
			int[] array8 = new int[4];
			array8[0] = 10;
			control8.ScaleDivisions = new decimal(array8);
			Control0 control9 = this.control0_1;
			int[] array9 = new int[4];
			array9[0] = 5;
			control9.ScaleSubDivisions = new decimal(array9);
			this.control0_1.ShowDivisionsText = true;
			this.control0_1.ShowSmallScale = false;
			this.control0_1.Size = new Size(98, 34);
			Control0 control10 = this.control0_1;
			int[] array10 = new int[4];
			array10[0] = 1;
			control10.SmallChange = new decimal(array10);
			this.control0_1.TabIndex = 5;
			this.control0_1.Text = Class521.smethod_0(90956);
			this.control0_1.ThumbInnerColor = Color.FromArgb(21, 56, 152);
			this.control0_1.ThumbPenColor = Color.FromArgb(21, 56, 152);
			this.control0_1.ThumbRoundRectSize = new Size(16, 16);
			this.control0_1.ThumbSize = new Size(14, 14);
			this.control0_1.TickAdd = 0f;
			this.control0_1.TickColor = Color.White;
			this.control0_1.TickDivide = 0f;
			this.control0_1.TickStyle = TickStyle.None;
			Control0 control11 = this.control0_1;
			int[] array11 = new int[4];
			array11[0] = 90;
			control11.Value = new decimal(array11);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tableLayoutPanel_0);
			base.Controls.Add(this.expandableSplitter_0);
			base.Controls.Add(this.panel_0);
			base.Name = Class521.smethod_0(91091);
			base.Size = new Size(1310, 414);
			this.panel_0.ResumeLayout(false);
			this.tableLayoutPanel_0.ResumeLayout(false);
			this.tableLayoutPanel_1.ResumeLayout(false);
			this.tableLayoutPanel_2.ResumeLayout(false);
			this.tableLayoutPanel_2.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x06001ED1 RID: 7889 RVA: 0x000E02BC File Offset: 0x000DE4BC
		[CompilerGenerated]
		private void method_34()
		{
			if (this.vlcControl_0 != null)
			{
				VlcControl vlcControl = this.vlcControl_0;
				vlcControl.Playing -= this.method_10;
				vlcControl.Paused -= this.method_12;
				vlcControl.Stopped -= this.method_16;
				vlcControl.MouseClick -= this.vlcControl_0_MouseClick;
				vlcControl.PositionChanged -= this.method_18;
				vlcControl.TimeChanged -= this.method_17;
				vlcControl.LengthChanged -= this.method_19;
				vlcControl.ScrambledChanged -= this.method_13;
				vlcControl.VideoOutChanged -= this.method_11;
				vlcControl.SeekableChanged -= this.method_15;
				vlcControl.PausableChanged -= this.method_14;
				vlcControl.ResetMedia();
			}
			this.vlcControl_0 = new VlcControl();
			this.vlcControl_0.BeginInit();
			this.vlcControl_0.VlcLibDirectory = new DirectoryInfo(Path.Combine(Directory.GetCurrentDirectory(), Class521.smethod_0(91108)));
			this.vlcControl_0.VlcMediaplayerOptions = new string[]
			{
				Class521.smethod_0(91117)
			};
			this.vlcControl_0.CreateControl();
			this.vlcControl_0.Location = new Point(0, 0);
			this.vlcControl_0.Dock = DockStyle.Fill;
			this.vlcControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.vlcControl_0.EndInit();
			this.vlcControl_0.Playing += this.method_10;
			this.vlcControl_0.Paused += this.method_12;
			this.vlcControl_0.Stopped += this.method_16;
			this.vlcControl_0.MouseClick += this.vlcControl_0_MouseClick;
			this.vlcControl_0.PositionChanged += this.method_18;
			this.vlcControl_0.TimeChanged += this.method_17;
			this.vlcControl_0.LengthChanged += this.method_19;
			this.vlcControl_0.ScrambledChanged += this.method_13;
			this.vlcControl_0.VideoOutChanged += this.method_11;
			this.vlcControl_0.SeekableChanged += this.method_15;
			this.vlcControl_0.PausableChanged += this.method_14;
			this.tableLayoutPanel_0.Controls.Add(this.vlcControl_0, 0, 0);
		}

		// Token: 0x06001ED2 RID: 7890 RVA: 0x0000CEE5 File Offset: 0x0000B0E5
		[CompilerGenerated]
		private void method_35(object object_0)
		{
			this.vlcControl_0.SetMedia(new Uri(this.string_0), new string[0]);
			this.method_31();
		}

		// Token: 0x06001ED3 RID: 7891 RVA: 0x0000CF0B File Offset: 0x0000B10B
		[CompilerGenerated]
		private void method_36()
		{
			this.vlcControl_0.smethod_1();
			this.vlcControl_0.Refresh();
		}

		// Token: 0x06001ED4 RID: 7892 RVA: 0x000E055C File Offset: 0x000DE75C
		[CompilerGenerated]
		private void method_37()
		{
			this.method_26();
			this.panel_1.BackgroundImage = Class375.play_blue1_28px;
			this.control0_0.Value = 100m;
			string text = this.method_21(this.vlcControl_0.Length);
			this.label_1.Text = text;
		}

		// Token: 0x06001ED5 RID: 7893 RVA: 0x0000CF25 File Offset: 0x0000B125
		[CompilerGenerated]
		private void method_38()
		{
			this.vlcControl_0.BackgroundImage = null;
			this.vlcControl_0.smethod_1();
			this.vlcControl_0.Refresh();
		}

		// Token: 0x06001ED6 RID: 7894 RVA: 0x0000CF4B File Offset: 0x0000B14B
		[CompilerGenerated]
		private void method_39()
		{
			if (this.IsPlaying)
			{
				this.IsPlaying = false;
			}
			this.vlcControl_0.Position = 0.002f;
		}

		// Token: 0x06001ED7 RID: 7895 RVA: 0x0000CF6E File Offset: 0x0000B16E
		[CompilerGenerated]
		private void method_40()
		{
			this.control0_0.Value = 0m;
			this.label_1.Text = Class521.smethod_0(91036);
		}

		// Token: 0x06001ED8 RID: 7896 RVA: 0x0000CF97 File Offset: 0x0000B197
		[CompilerGenerated]
		private void method_41()
		{
			this.vlcControl_0.Stop();
			while (this.bool_0)
			{
				Thread.Sleep(20);
			}
			this.vlcControl_0.ResetMedia();
			this.vlcControl_0.Dispose();
		}

		// Token: 0x06001ED9 RID: 7897 RVA: 0x0000CFCD File Offset: 0x0000B1CD
		[CompilerGenerated]
		private void method_42()
		{
			if (this.panel_1.BackgroundImage != Class375.pause_blue1_28px)
			{
				this.panel_1.BackgroundImage = Class375.pause_blue1_28px;
			}
			this.panel_1.Refresh();
		}

		// Token: 0x06001EDA RID: 7898 RVA: 0x000E05B4 File Offset: 0x000DE7B4
		[CompilerGenerated]
		private void method_43(object object_0)
		{
			this.vlcControl_0.Play();
			int num = Convert.ToInt32(Math.Round(this.control0_1.Value));
			if (this.vlcControl_0.Audio.Volume != num)
			{
				this.vlcControl_0.Audio.Volume = num;
			}
		}

		// Token: 0x06001EDB RID: 7899 RVA: 0x0000CFFE File Offset: 0x0000B1FE
		[CompilerGenerated]
		private void method_44(object object_0)
		{
			this.vlcControl_0.Pause();
		}

		// Token: 0x06001EDC RID: 7900 RVA: 0x0000D00D File Offset: 0x0000B20D
		[CompilerGenerated]
		private void method_45()
		{
			if (this.panel_1.BackgroundImage != Class375.play_blue1_28px)
			{
				this.panel_1.BackgroundImage = Class375.play_blue1_28px;
			}
			this.panel_1.Refresh();
		}

		// Token: 0x04000F75 RID: 3957
		private Color color_0 = Color.FromArgb(85, 92, 110);

		// Token: 0x04000F76 RID: 3958
		private Color color_1 = Color.FromArgb(70, 77, 95);

		// Token: 0x04000F77 RID: 3959
		private VlcControl vlcControl_0;

		// Token: 0x04000F78 RID: 3960
		private float float_0;

		// Token: 0x04000F79 RID: 3961
		private bool bool_0;

		// Token: 0x04000F7A RID: 3962
		private string string_0;

		// Token: 0x04000F7B RID: 3963
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x04000F7C RID: 3964
		private IContainer icontainer_0;

		// Token: 0x04000F7D RID: 3965
		private Panel panel_0;

		// Token: 0x04000F7E RID: 3966
		private ExpandableSplitter expandableSplitter_0;

		// Token: 0x04000F7F RID: 3967
		private TableLayoutPanel tableLayoutPanel_0;

		// Token: 0x04000F80 RID: 3968
		private Control0 control0_0;

		// Token: 0x04000F81 RID: 3969
		private TableLayoutPanel tableLayoutPanel_1;

		// Token: 0x04000F82 RID: 3970
		private Panel panel_1;

		// Token: 0x04000F83 RID: 3971
		private Panel panel_2;

		// Token: 0x04000F84 RID: 3972
		private Panel panel_3;

		// Token: 0x04000F85 RID: 3973
		private TableLayoutPanel tableLayoutPanel_2;

		// Token: 0x04000F86 RID: 3974
		private Label label_0;

		// Token: 0x04000F87 RID: 3975
		private Label label_1;

		// Token: 0x04000F88 RID: 3976
		private Panel panel_4;

		// Token: 0x04000F89 RID: 3977
		private Control0 control0_1;

		// Token: 0x04000F8A RID: 3978
		private ItemPanel itemPanel_0;

		// Token: 0x020002B7 RID: 695
		[CompilerGenerated]
		private sealed class Class357
		{
			// Token: 0x06001EDE RID: 7902 RVA: 0x000E0608 File Offset: 0x000DE808
			internal void method_0()
			{
				this.control12_0.control0_0.Value = this.decimal_0;
				string text = this.control12_0.method_21(this.control12_0.vlcControl_0.Time);
				if (this.control12_0.label_1.Text != text)
				{
					this.control12_0.label_1.Text = text;
				}
			}

			// Token: 0x04000F8B RID: 3979
			public Control12 control12_0;

			// Token: 0x04000F8C RID: 3980
			public decimal decimal_0;
		}

		// Token: 0x020002B8 RID: 696
		[CompilerGenerated]
		private sealed class Class358
		{
			// Token: 0x06001EE0 RID: 7904 RVA: 0x0000D03E File Offset: 0x0000B23E
			internal void method_0()
			{
				this.control12_0.label_0.Text = this.string_0;
			}

			// Token: 0x04000F8D RID: 3981
			public string string_0;

			// Token: 0x04000F8E RID: 3982
			public Control12 control12_0;
		}
	}
}
