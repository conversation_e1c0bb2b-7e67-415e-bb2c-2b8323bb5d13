﻿using System;
using System.Runtime.CompilerServices;
using TEx.ImportTrans;

namespace ns0
{
	// Token: 0x02000377 RID: 887
	internal sealed class Class481
	{
		// Token: 0x1700063E RID: 1598
		// (get) Token: 0x060024C9 RID: 9417 RVA: 0x000FED9C File Offset: 0x000FCF9C
		// (set) Token: 0x060024CA RID: 9418 RVA: 0x0000E499 File Offset: 0x0000C699
		public bool TimerEnabled { get; set; }

		// Token: 0x1700063F RID: 1599
		// (get) Token: 0x060024CB RID: 9419 RVA: 0x000FEDB4 File Offset: 0x000FCFB4
		// (set) Token: 0x060024CC RID: 9420 RVA: 0x0000E4A4 File Offset: 0x0000C6A4
		public int TimerInterval { get; set; }

		// Token: 0x17000640 RID: 1600
		// (get) Token: 0x060024CD RID: 9421 RVA: 0x000FEDCC File Offset: 0x000FCFCC
		// (set) Token: 0x060024CE RID: 9422 RVA: 0x0000E4AF File Offset: 0x0000C6AF
		public bool DownEnable { get; set; }

		// Token: 0x060024CF RID: 9423 RVA: 0x000FEDE4 File Offset: 0x000FCFE4
		public Class481(CfmmcAutoDnldConfig cfmmcAutoDnldConfig_0)
		{
			DateTime now = DateTime.Now;
			if (cfmmcAutoDnldConfig_0 == null)
			{
				this.TimerEnabled = false;
				this.DownEnable = false;
			}
			else
			{
				if (cfmmcAutoDnldConfig_0.AutoDownOnStartup)
				{
					this.DownEnable = true;
				}
				if (cfmmcAutoDnldConfig_0.AutoDownPeriodly)
				{
					DateTime beginTime = cfmmcAutoDnldConfig_0.BeginTime;
					if (beginTime.TimeOfDay > now.TimeOfDay)
					{
						double totalMilliseconds = (beginTime.TimeOfDay - now.TimeOfDay).TotalMilliseconds;
						this.TimerInterval = Convert.ToInt32(totalMilliseconds);
						this.DownEnable = false;
					}
					else
					{
						DateTime d;
						if (cfmmcAutoDnldConfig_0.Frequency == AutoDownCfmmcFrequencyEnum.每天)
						{
							d = now.Date.AddDays(1.0).Add(cfmmcAutoDnldConfig_0.BeginTime.TimeOfDay);
						}
						else
						{
							int num = cfmmcAutoDnldConfig_0.WklyDnldDayOfWeek - now.DayOfWeek;
							if (num < 0)
							{
								num += 7;
							}
							d = now.Date.AddDays((double)num).Add(cfmmcAutoDnldConfig_0.BeginTime.TimeOfDay);
						}
						double totalMilliseconds2 = (d - now).TotalMilliseconds;
						this.TimerInterval = Convert.ToInt32(totalMilliseconds2);
						this.DownEnable = true;
					}
					this.TimerEnabled = true;
				}
			}
		}

		// Token: 0x040011B7 RID: 4535
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040011B8 RID: 4536
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040011B9 RID: 4537
		[CompilerGenerated]
		private bool bool_1;
	}
}
