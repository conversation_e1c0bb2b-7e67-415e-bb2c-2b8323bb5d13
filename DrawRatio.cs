﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using ns18;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000071 RID: 113
	[Serializable]
	internal class DrawRatio : DrawObj, ISerializable
	{
		// Token: 0x06000413 RID: 1043 RVA: 0x00003742 File Offset: 0x00001942
		public DrawRatio()
		{
		}

		// Token: 0x06000414 RID: 1044 RVA: 0x00003BE0 File Offset: 0x00001DE0
		public DrawRatio(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = Class521.smethod_0(5566);
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
			this.IfShowTopDashLine = true;
			this.bool_6 = true;
		}

		// Token: 0x06000415 RID: 1045 RVA: 0x00023090 File Offset: 0x00021290
		protected DrawRatio(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
			try
			{
				this.bool_7 = info.GetBoolean(Class521.smethod_0(5583));
			}
			catch
			{
				this.bool_7 = true;
			}
		}

		// Token: 0x06000416 RID: 1046 RVA: 0x00003C1D File Offset: 0x00001E1D
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
			info.AddValue(Class521.smethod_0(5583), this.bool_7);
		}

		// Token: 0x06000417 RID: 1047 RVA: 0x000230E0 File Offset: 0x000212E0
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<GraphObj> list = new List<GraphObj>();
			if (Base.UI.DrawingObj == this || base.MouseHovering || base.SelectBoxVisible)
			{
				LineObj lineObj = base.method_23(double_1, double_2, double_3, double_4, string_6);
				lineObj.Line.Style = DashStyle.Dash;
				lineObj.Tag = base.Tag + DrawRatio.string_5;
				list.Add(lineObj);
			}
			this.method_39(list, chartCS_1, double_1, double_2, double_3, double_4, string_6);
			this.vmethod_24(list, chartCS_1, double_1, double_2, double_3, double_4, string_6);
			return list;
		}

		// Token: 0x06000418 RID: 1048 RVA: 0x00023168 File Offset: 0x00021368
		private void method_39(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			double double_5 = Math.Min(double_1, double_3);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double max2 = chartCS_1.GraphPane.YAxis.Scale.Max;
			double double_6 = Math.Min(double_2, double_4);
			double num = Math.Max(double_2, double_4);
			this.method_40(list_2, double_5, max, double_6, string_6);
			if ((!this.bool_6 || this.IfShowTopDashLine) && num < max2)
			{
				this.method_40(list_2, double_5, max, num, string_6);
			}
		}

		// Token: 0x06000419 RID: 1049 RVA: 0x000231F0 File Offset: 0x000213F0
		private void method_40(List<GraphObj> list_2, double double_1, double double_2, double double_3, string string_6)
		{
			LineObj lineObj = base.method_23(double_1, double_3, double_2, double_3, string_6);
			lineObj.Line.Style = DashStyle.Dash;
			list_2.Add(lineObj);
		}

		// Token: 0x0600041A RID: 1050 RVA: 0x00023224 File Offset: 0x00021424
		protected virtual void vmethod_24(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<DrawSublineParam> sublineParamList = base.SublineParamList;
			for (int i = 0; i < sublineParamList.Count; i++)
			{
				DrawSublineParam drawSublineParam = sublineParamList[i];
				if (drawSublineParam.Enabled)
				{
					this.method_41(list_2, chartCS_1, double_1, double_2, double_3, double_4, drawSublineParam.Value, string_6);
				}
			}
		}

		// Token: 0x0600041B RID: 1051 RVA: 0x00023274 File Offset: 0x00021474
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				0.25,
				0.5,
				0.75
			});
			return base.method_28(list_, 0.01, 0.99, 2);
		}

		// Token: 0x0600041C RID: 1052 RVA: 0x000232B8 File Offset: 0x000214B8
		protected void method_41(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, double double_5, string string_6)
		{
			this.method_42(list_2, chartCS_1, double_1, double_2, double_3, double_4, double_5, string_6, false);
		}

		// Token: 0x0600041D RID: 1053 RVA: 0x000232DC File Offset: 0x000214DC
		protected void method_42(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, double double_5, string string_6, bool bool_8)
		{
			double double_6 = Math.Min(double_1, double_3);
			double max = chartCS_1.GraphPane.YAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			double num = Math.Max(double_2, double_4);
			double num2 = Math.Min(double_2, double_4);
			double num3 = Math.Abs(double_4 - double_2) * double_5;
			double? num4 = null;
			if (double_4 > double_2)
			{
				num4 = new double?(num2 + num3);
			}
			else
			{
				num4 = new double?(num - num3);
			}
			if (num4 != null)
			{
				double? num5 = num4;
				double num6 = max;
				if (num5.GetValueOrDefault() < num6 & num5 != null)
				{
					num5 = num4;
					num6 = min;
					if (num5.GetValueOrDefault() > num6 & num5 != null)
					{
						this.method_43(list_2, chartCS_1, double_6, num4.Value, double_5, string_6, bool_8);
					}
				}
			}
		}

		// Token: 0x0600041E RID: 1054 RVA: 0x000233C4 File Offset: 0x000215C4
		private void method_43(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, string string_6, bool bool_8)
		{
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			this.method_44(list_2, double_1, max, double_2, string_6, bool_8);
			double double_4 = double_1;
			if (double_1 < 0.0)
			{
				double_4 = 0.0;
			}
			TextObj textObj = base.method_27(chartCS_1, double_4, double_2, double_3.ToString(Class521.smethod_0(5608)), null, string_6);
			textObj.Location.AlignH = AlignH.Left;
			textObj.Location.AlignV = AlignV.Bottom;
			list_2.Add(textObj);
			int digitNb = base.Chart.Symbol.DigitNb;
			TextObj textObj2 = base.method_27(chartCS_1, max, double_2, Math.Round(double_2, digitNb).ToString(Class521.smethod_0(5338) + digitNb.ToString()), null, string_6);
			textObj2.Location.AlignH = AlignH.Right;
			textObj2.Location.AlignV = AlignV.Bottom;
			list_2.Add(textObj2);
		}

		// Token: 0x0600041F RID: 1055 RVA: 0x000234B8 File Offset: 0x000216B8
		private void method_44(List<GraphObj> list_2, double double_1, double double_2, double double_3, string string_6, bool bool_8)
		{
			LineObj lineObj = base.method_23(double_1, double_3, double_2, double_3, string_6);
			if (bool_8)
			{
				lineObj.Line.Style = DashStyle.Dash;
			}
			list_2.Add(lineObj);
		}

		// Token: 0x06000420 RID: 1056 RVA: 0x000234F0 File Offset: 0x000216F0
		protected override void vmethod_14(ChartCS chartCS_1)
		{
			base.vmethod_14(chartCS_1);
			if (chartCS_1 != null && chartCS_1.GraphPane != null && chartCS_1.GraphPane.GraphObjList != null)
			{
				chartCS_1.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(DrawRatio.<>c.<>9.method_0));
			}
		}

		// Token: 0x06000421 RID: 1057 RVA: 0x000203E0 File Offset: 0x0001E5E0
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}

		// Token: 0x170000DC RID: 220
		// (get) Token: 0x06000422 RID: 1058 RVA: 0x00023550 File Offset: 0x00021750
		// (set) Token: 0x06000423 RID: 1059 RVA: 0x00003C3F File Offset: 0x00001E3F
		public bool IfShowTopDashLine
		{
			get
			{
				return this.bool_7;
			}
			set
			{
				this.bool_7 = value;
			}
		}

		// Token: 0x0400014E RID: 334
		private static readonly string string_5 = Class521.smethod_0(5613);

		// Token: 0x0400014F RID: 335
		private bool bool_6;

		// Token: 0x04000150 RID: 336
		private bool bool_7;
	}
}
