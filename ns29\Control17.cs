﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns18;
using ns26;

namespace ns29
{
	// Token: 0x02000415 RID: 1045
	[DesignerCategory("Code")]
	internal sealed class Control17 : Control
	{
		// Token: 0x0600284C RID: 10316 RVA: 0x0000FB99 File Offset: 0x0000DD99
		protected void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);
			if (!base.DesignMode)
			{
				this.method_0(base.Visible);
			}
		}

		// Token: 0x0600284D RID: 10317 RVA: 0x0000FBB6 File Offset: 0x0000DDB6
		private void method_0(bool bool_0)
		{
			this.timer_0.Enabled = bool_0;
			this.int_0 = 0;
			this.Refresh();
		}

		// Token: 0x0600284E RID: 10318 RVA: 0x0000FBD1 File Offset: 0x0000DDD1
		protected void OnResize(EventArgs e)
		{
			base.Size = new Size(Convert.ToInt32(250f * this.float_0), Convert.ToInt32(42f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x0600284F RID: 10319 RVA: 0x0000FC07 File Offset: 0x0000DE07
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002850 RID: 10320 RVA: 0x0000FC2A File Offset: 0x0000DE2A
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.bitmap_0 != null)
				{
					this.bitmap_0.Dispose();
				}
				this.timer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002851 RID: 10321 RVA: 0x0010E1EC File Offset: 0x0010C3EC
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			if (this.bitmap_1 != null)
			{
				e.Graphics.DrawImage(this.bitmap_1, new Rectangle(0, 0, Convert.ToInt32(250f * this.float_0), Convert.ToInt32(42f * this.float_1)), new Rectangle(0, 0, 250, 42), GraphicsUnit.Pixel);
			}
			if (this.bitmap_0 != null && this.int_0 > 0)
			{
				e.Graphics.SetClip(new Rectangle(Convert.ToInt32(46f * this.float_0), 0, Convert.ToInt32(165f * this.float_0), Convert.ToInt32(34f * this.float_1)));
				e.Graphics.DrawImage(this.bitmap_0, new Rectangle(Convert.ToInt32((float)(this.int_0 - 6) * this.float_0), Convert.ToInt32(16f * this.float_1), Convert.ToInt32(40f * this.float_0), Convert.ToInt32(12f * this.float_1)), 0, 0, 40, 12, GraphicsUnit.Pixel);
			}
		}

		// Token: 0x06002852 RID: 10322 RVA: 0x0000FC54 File Offset: 0x0000DE54
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.int_0 += 11;
			if (this.int_0 > 198)
			{
				this.int_0 = 0;
			}
			this.Refresh();
		}

		// Token: 0x06002853 RID: 10323 RVA: 0x0010E314 File Offset: 0x0010C514
		public Control17()
		{
			this.timer_0.Interval = 85;
			this.timer_0.Tick += this.timer_0_Tick;
			base.Size = new Size(250, 42);
			base.TabStop = false;
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
		}

		// Token: 0x0400141F RID: 5151
		private int int_0 = 99;

		// Token: 0x04001420 RID: 5152
		private readonly Bitmap bitmap_0 = Class541.smethod_0(Class521.smethod_0(119867));

		// Token: 0x04001421 RID: 5153
		private readonly Bitmap bitmap_1 = Class541.smethod_0(Class521.smethod_0(119876));

		// Token: 0x04001422 RID: 5154
		private readonly Timer timer_0 = new Timer();

		// Token: 0x04001423 RID: 5155
		private float float_0 = 1f;

		// Token: 0x04001424 RID: 5156
		private float float_1 = 1f;
	}
}
