﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using ns18;

namespace TEx.ImportTrans
{
	// Token: 0x02000347 RID: 839
	public sealed class TransData
	{
		// Token: 0x06002339 RID: 9017 RVA: 0x00002D25 File Offset: 0x00000F25
		public TransData()
		{
		}

		// Token: 0x0600233A RID: 9018 RVA: 0x0000DEB9 File Offset: 0x0000C0B9
		public TransData(List<string> transFields, string usrName) : this()
		{
			this.method_3(transFields, usrName);
		}

		// Token: 0x1700060A RID: 1546
		// (get) Token: 0x0600233B RID: 9019 RVA: 0x000F76D0 File Offset: 0x000F58D0
		// (set) Token: 0x0600233C RID: 9020 RVA: 0x0000DECB File Offset: 0x0000C0CB
		public string CloseID
		{
			get
			{
				return this.string_3;
			}
			set
			{
				this.string_3 = value;
			}
		}

		// Token: 0x0600233D RID: 9021 RVA: 0x0000DECB File Offset: 0x0000C0CB
		public void method_0(string string_7)
		{
			this.string_3 = string_7;
		}

		// Token: 0x1700060B RID: 1547
		// (get) Token: 0x0600233E RID: 9022 RVA: 0x000F76E8 File Offset: 0x000F58E8
		// (set) Token: 0x0600233F RID: 9023 RVA: 0x0000DED6 File Offset: 0x0000C0D6
		public string Note
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x1700060C RID: 1548
		// (get) Token: 0x06002340 RID: 9024 RVA: 0x000F7700 File Offset: 0x000F5900
		// (set) Token: 0x06002341 RID: 9025 RVA: 0x0000DEE1 File Offset: 0x0000C0E1
		public string RecordID
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x1700060D RID: 1549
		// (get) Token: 0x06002342 RID: 9026 RVA: 0x000F7718 File Offset: 0x000F5918
		// (set) Token: 0x06002343 RID: 9027 RVA: 0x0000DEEC File Offset: 0x0000C0EC
		public string Contract
		{
			get
			{
				return this.string_4;
			}
			set
			{
				this.string_4 = value;
			}
		}

		// Token: 0x1700060E RID: 1550
		// (get) Token: 0x06002344 RID: 9028 RVA: 0x000F7730 File Offset: 0x000F5930
		// (set) Token: 0x06002345 RID: 9029 RVA: 0x0000DEF7 File Offset: 0x0000C0F7
		public string BuyOrSell
		{
			get
			{
				return this.string_5;
			}
			set
			{
				this.string_5 = value;
			}
		}

		// Token: 0x1700060F RID: 1551
		// (get) Token: 0x06002346 RID: 9030 RVA: 0x000F7748 File Offset: 0x000F5948
		// (set) Token: 0x06002347 RID: 9031 RVA: 0x0000DF02 File Offset: 0x0000C102
		public string OpenOrClose
		{
			get
			{
				return this.string_6;
			}
			set
			{
				this.string_6 = value;
			}
		}

		// Token: 0x17000610 RID: 1552
		// (get) Token: 0x06002348 RID: 9032 RVA: 0x000F7760 File Offset: 0x000F5960
		// (set) Token: 0x06002349 RID: 9033 RVA: 0x0000DF0D File Offset: 0x0000C10D
		public decimal? Profit
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x17000611 RID: 1553
		// (get) Token: 0x0600234A RID: 9034 RVA: 0x000F7778 File Offset: 0x000F5978
		// (set) Token: 0x0600234B RID: 9035 RVA: 0x0000DF18 File Offset: 0x0000C118
		public double? Price
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x17000612 RID: 1554
		// (get) Token: 0x0600234C RID: 9036 RVA: 0x000F7790 File Offset: 0x000F5990
		// (set) Token: 0x0600234D RID: 9037 RVA: 0x0000DF23 File Offset: 0x0000C123
		public int? Count
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x17000613 RID: 1555
		// (get) Token: 0x0600234E RID: 9038 RVA: 0x000F77A8 File Offset: 0x000F59A8
		// (set) Token: 0x0600234F RID: 9039 RVA: 0x0000DF2E File Offset: 0x0000C12E
		public double? Poundage
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				this.nullable_3 = value;
			}
		}

		// Token: 0x17000614 RID: 1556
		// (get) Token: 0x06002350 RID: 9040 RVA: 0x000F77C0 File Offset: 0x000F59C0
		// (set) Token: 0x06002351 RID: 9041 RVA: 0x0000DF39 File Offset: 0x0000C139
		public DateTime? Day
		{
			get
			{
				return this.nullable_4;
			}
			set
			{
				this.nullable_4 = value;
			}
		}

		// Token: 0x17000615 RID: 1557
		// (get) Token: 0x06002352 RID: 9042 RVA: 0x000F77D8 File Offset: 0x000F59D8
		// (set) Token: 0x06002353 RID: 9043 RVA: 0x0000DF44 File Offset: 0x0000C144
		public DateTime? Time
		{
			get
			{
				return this.nullable_5;
			}
			set
			{
				this.nullable_5 = value;
			}
		}

		// Token: 0x06002354 RID: 9044 RVA: 0x000F77F0 File Offset: 0x000F59F0
		public bool method_1(string string_7)
		{
			char[] array = new char[string_7.Length];
			array = string_7.ToCharArray();
			for (int i = 0; i < array.Length; i++)
			{
				if (array[i] < '0' || array[i] > '9')
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06002355 RID: 9045 RVA: 0x000F7838 File Offset: 0x000F5A38
		public static string smethod_0(string string_7)
		{
			string result;
			if (TransData.FieldOpenOrClose.Contains(string_7))
			{
				result = string_7;
			}
			else if (string_7 == Class521.smethod_0(25073))
			{
				result = Class521.smethod_0(18681);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002356 RID: 9046 RVA: 0x000F787C File Offset: 0x000F5A7C
		public void method_2(string string_7, object object_0)
		{
			if (string_7 == Class521.smethod_0(45008))
			{
				this.Poundage = new double?(Convert.ToDouble(object_0));
			}
			else if (string_7 == Class521.smethod_0(44605))
			{
				this.Day = new DateTime?(Convert.ToDateTime(object_0));
			}
			else if (string_7 == Class521.smethod_0(17996))
			{
				this.Time = new DateTime?(Convert.ToDateTime(object_0));
			}
			else if (string_7 == Class521.smethod_0(44709))
			{
				this.RecordID = Convert.ToString(object_0);
			}
			else if (string_7 == Class521.smethod_0(44510))
			{
				this.Contract = object_0.ToString();
			}
			else if (string_7 == Class521.smethod_0(44784))
			{
				this.BuyOrSell = object_0.ToString();
			}
			else if (string_7 == Class521.smethod_0(44823))
			{
				this.OpenOrClose = object_0.ToString();
			}
			else if (string_7 == Class521.smethod_0(44907))
			{
				this.Price = new double?(Convert.ToDouble(object_0));
			}
			else if (string_7 == Class521.smethod_0(44953))
			{
				this.Count = new int?(Convert.ToInt32(object_0));
			}
			else if (string_7 == Class521.smethod_0(45071))
			{
				this.CloseID = object_0.ToString();
			}
			else if (string_7 == Class521.smethod_0(45114))
			{
				this.Profit = new decimal?(Convert.ToDecimal(object_0));
			}
		}

		// Token: 0x17000616 RID: 1558
		// (get) Token: 0x06002357 RID: 9047 RVA: 0x000F7A1C File Offset: 0x000F5C1C
		public static string[] FieldBuyOrSell
		{
			get
			{
				return new string[]
				{
					Class521.smethod_0(18671),
					Class521.smethod_0(18676)
				};
			}
		}

		// Token: 0x17000617 RID: 1559
		// (get) Token: 0x06002358 RID: 9048 RVA: 0x000F7A50 File Offset: 0x000F5C50
		public static string[] FieldOpenOrClose
		{
			get
			{
				return new string[]
				{
					Class521.smethod_0(11757),
					Class521.smethod_0(18681),
					Class521.smethod_0(104683)
				};
			}
		}

		// Token: 0x06002359 RID: 9049 RVA: 0x000F7A90 File Offset: 0x000F5C90
		public void method_3(List<string> list_0, string string_7)
		{
			this.BuyOrSell = list_0[3];
			this.Contract = list_0[0];
			this.Count = new int?(Convert.ToInt32(list_0[6]));
			string str = list_0[12];
			string str2 = list_0[2];
			string value = str + Class521.smethod_0(3636) + str2;
			this.Day = new DateTime?(Convert.ToDateTime(value));
			this.OpenOrClose = list_0[8];
			this.Poundage = new double?(Convert.ToDouble(list_0[9]));
			this.Price = new double?(Convert.ToDouble(list_0[5]));
			this.RecordID = list_0[1];
			this.Note = this.method_4(string_7, list_0[1]);
			string text = list_0[10];
			if (!text.Contains(Class521.smethod_0(104692)))
			{
				this.Profit = new decimal?(Convert.ToDecimal(text));
			}
			string text2 = list_0[11].Trim();
			if (!string.IsNullOrEmpty(text2))
			{
				this.CloseID = text2;
			}
		}

		// Token: 0x0600235A RID: 9050 RVA: 0x000F7BA8 File Offset: 0x000F5DA8
		private string method_4(string string_7, string string_8)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement(Class521.smethod_0(59486));
			XElement xelement2 = new XElement(TransData.string_0);
			xelement2.SetAttributeValue(Class521.smethod_0(12411), string_7);
			xelement2.SetAttributeValue(Class521.smethod_0(104697), string_8);
			xdocument.Add(xelement);
			xelement.Add(xelement2);
			return xdocument.ToString();
		}

		// Token: 0x04001105 RID: 4357
		internal static readonly string string_0 = Class521.smethod_0(104702);

		// Token: 0x04001106 RID: 4358
		private string string_1;

		// Token: 0x04001107 RID: 4359
		private string string_2;

		// Token: 0x04001108 RID: 4360
		private string string_3;

		// Token: 0x04001109 RID: 4361
		private string string_4;

		// Token: 0x0400110A RID: 4362
		private string string_5;

		// Token: 0x0400110B RID: 4363
		private string string_6;

		// Token: 0x0400110C RID: 4364
		private decimal? nullable_0;

		// Token: 0x0400110D RID: 4365
		private double? nullable_1;

		// Token: 0x0400110E RID: 4366
		private int? nullable_2;

		// Token: 0x0400110F RID: 4367
		private double? nullable_3;

		// Token: 0x04001110 RID: 4368
		private DateTime? nullable_4;

		// Token: 0x04001111 RID: 4369
		private DateTime? nullable_5;
	}
}
