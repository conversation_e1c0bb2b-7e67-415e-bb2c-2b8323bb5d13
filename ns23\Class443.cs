﻿using System;

namespace ns23
{
	// Token: 0x0200032A RID: 810
	internal sealed class Class443
	{
		// Token: 0x06002260 RID: 8800 RVA: 0x0000DAFD File Offset: 0x0000BCFD
		public Class443(int int_2, int int_3, string string_1)
		{
			this.int_0 = int_2;
			this.int_1 = int_3;
			this.string_0 = string_1;
		}

		// Token: 0x040010BE RID: 4286
		public int int_0;

		// Token: 0x040010BF RID: 4287
		public int int_1;

		// Token: 0x040010C0 RID: 4288
		public string string_0;
	}
}
