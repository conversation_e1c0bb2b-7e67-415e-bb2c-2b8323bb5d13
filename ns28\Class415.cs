﻿using System;
using System.Runtime.CompilerServices;
using ns12;
using ns18;
using ns26;
using TEx.Inds;
using TEx.SIndicator;

namespace ns28
{
	// Token: 0x0200030E RID: 782
	internal class Class415 : Class411
	{
		// Token: 0x170005CE RID: 1486
		// (get) Token: 0x060021BD RID: 8637 RVA: 0x000EFF80 File Offset: 0x000EE180
		// (set) Token: 0x060021BE RID: 8638 RVA: 0x0000D926 File Offset: 0x0000BB26
		public override Class411 Left { get; protected set; }

		// Token: 0x170005CF RID: 1487
		// (get) Token: 0x060021BF RID: 8639 RVA: 0x000EFF98 File Offset: 0x000EE198
		// (set) Token: 0x060021C0 RID: 8640 RVA: 0x0000D931 File Offset: 0x0000BB31
		public override Class411 Right { get; protected set; }

		// Token: 0x170005D0 RID: 1488
		// (get) Token: 0x060021C1 RID: 8641 RVA: 0x000EFFB0 File Offset: 0x000EE1B0
		// (set) Token: 0x060021C2 RID: 8642 RVA: 0x0000D93C File Offset: 0x0000BB3C
		public override HToken Token { get; protected set; }

		// Token: 0x060021C3 RID: 8643 RVA: 0x0000D947 File Offset: 0x0000BB47
		public Class415(HToken htoken_1, Class411 class411_2, Class411 class411_3)
		{
			this.Token = htoken_1;
			this.Left = class411_2;
			this.Right = class411_3;
		}

		// Token: 0x060021C4 RID: 8644 RVA: 0x000EFFC8 File Offset: 0x000EE1C8
		protected DataArray method_0(int int_0, double double_0)
		{
			return new DataArray(int_0, double_0);
		}

		// Token: 0x060021C5 RID: 8645 RVA: 0x000EFFE0 File Offset: 0x000EE1E0
		protected virtual object vmethod_2(object object_0, object object_1)
		{
			object result;
			if (object_0.GetType() == typeof(double) && object_1.GetType() == typeof(double))
			{
				result = this.vmethod_3((double)object_0, (double)object_1);
			}
			else if (object_0.GetType() == typeof(DataArray) && object_1.GetType() == typeof(DataArray))
			{
				result = this.vmethod_4(object_0 as DataArray, object_1 as DataArray);
			}
			else if (object_0.GetType() == typeof(DataArray) && object_1.GetType() == typeof(double))
			{
				DataArray dataArray_ = this.method_0((object_0 as DataArray).Data.Length, (double)object_1);
				result = this.vmethod_4(object_0 as DataArray, dataArray_);
			}
			else
			{
				if (object_0.GetType() != typeof(double) || object_1.GetType() != typeof(DataArray))
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(101380)));
				}
				DataArray dataArray_2 = this.method_0((object_1 as DataArray).Data.Length, (double)object_0);
				result = this.vmethod_4(dataArray_2, object_1 as DataArray);
			}
			return result;
		}

		// Token: 0x060021C6 RID: 8646 RVA: 0x0000D966 File Offset: 0x0000BB66
		protected virtual double vmethod_3(double double_0, double double_1)
		{
			throw new Exception(Class521.smethod_0(101441));
		}

		// Token: 0x060021C7 RID: 8647 RVA: 0x000F0124 File Offset: 0x000EE324
		protected virtual DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			throw new Exception(Class521.smethod_0(101474));
		}

		// Token: 0x060021C8 RID: 8648 RVA: 0x000F0140 File Offset: 0x000EE340
		private string method_1(Class411 class411_2)
		{
			Type type = class411_2.GetType();
			string result;
			if (type == typeof(Class412))
			{
				result = ((Class412)class411_2).vmethod_0();
			}
			else
			{
				if (type != typeof(Class415))
				{
					throw new Exception(Class521.smethod_0(101503));
				}
				result = ((Class415)class411_2).vmethod_0();
			}
			return result;
		}

		// Token: 0x060021C9 RID: 8649 RVA: 0x000F019C File Offset: 0x000EE39C
		public override string vmethod_0()
		{
			return this.method_1(this.Left) + this.Token.Symbol.Name + this.method_1(this.Right);
		}

		// Token: 0x060021CA RID: 8650 RVA: 0x0000D977 File Offset: 0x0000BB77
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			throw new Exception(this.Token.method_0(Class521.smethod_0(101520)));
		}

		// Token: 0x04001064 RID: 4196
		[CompilerGenerated]
		private Class411 class411_0;

		// Token: 0x04001065 RID: 4197
		[CompilerGenerated]
		private Class411 class411_1;

		// Token: 0x04001066 RID: 4198
		[CompilerGenerated]
		private HToken htoken_0;
	}
}
