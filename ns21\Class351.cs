﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.Reflection;
using System.Resources;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns20;
using TEx;

namespace ns21
{
	// Token: 0x020002AA RID: 682
	internal static class Class351
	{
		// Token: 0x06001E30 RID: 7728 RVA: 0x000D4440 File Offset: 0x000D2640
		[STAThread]
		private static void Main()
		{
			if (Class545.smethod_5())
			{
				Application.ThreadException += Class351.smethod_0;
				Application.SetUnhandledExceptionMode(UnhandledExceptionMode.ThrowException);
				AppDomain.CurrentDomain.UnhandledException += Class351.smethod_1;
				bool flag = true;
				Process process = Class351.smethod_3();
				if (process != null && flag)
				{
					Class351.smethod_4(process);
				}
				else
				{
					CultureInfo cultureInfo = CultureInfo.CurrentCulture.Clone() as CultureInfo;
					cultureInfo.NumberFormat.PercentSymbol = Class521.smethod_0(1449);
					cultureInfo.NumberFormat.PerMilleSymbol = Class521.smethod_0(1449);
					Thread.CurrentThread.CurrentCulture = cultureInfo;
					Application.EnableVisualStyles();
					Application.SetCompatibleTextRenderingDefault(false);
					Class351.smethod_5(!TApp.IsHighDpiScreen);
					if (new LoginForm().ShowDialog() == DialogResult.OK)
					{
						Application.Run(new MainForm());
					}
					else
					{
						Application.Exit();
					}
				}
			}
		}

		// Token: 0x06001E31 RID: 7729 RVA: 0x0000CA51 File Offset: 0x0000AC51
		private static void smethod_0(object sender, ThreadExceptionEventArgs e)
		{
			if (e != null && e.Exception != null)
			{
				Exception exception = e.Exception;
				Class351.smethod_2(exception);
				throw exception;
			}
		}

		// Token: 0x06001E32 RID: 7730 RVA: 0x0000CA6D File Offset: 0x0000AC6D
		private static void smethod_1(object sender, UnhandledExceptionEventArgs e)
		{
			if (e != null && e.ExceptionObject != null)
			{
				Exception ex = (Exception)e.ExceptionObject;
				Class351.smethod_2(ex);
				throw ex;
			}
		}

		// Token: 0x06001E33 RID: 7731 RVA: 0x000D451C File Offset: 0x000D271C
		private static void smethod_2(Exception exception_0)
		{
			try
			{
				string string_ = string.Concat(new string[]
				{
					Environment.NewLine,
					Class521.smethod_0(86781),
					Base.Data.CurrDate.ToString(),
					Class521.smethod_0(5036),
					Base.Data.smethod_127()
				});
				Class48.smethod_4(exception_0, false, string_);
			}
			catch
			{
			}
		}

		// Token: 0x170004C9 RID: 1225
		// (get) Token: 0x06001E34 RID: 7732 RVA: 0x000D4590 File Offset: 0x000D2790
		public static ResourceManager Resources
		{
			get
			{
				return Class351.resourceManager_0;
			}
		}

		// Token: 0x06001E35 RID: 7733 RVA: 0x000D45A8 File Offset: 0x000D27A8
		public static Process smethod_3()
		{
			Process currentProcess = Process.GetCurrentProcess();
			foreach (Process process in Process.GetProcessesByName(currentProcess.ProcessName))
			{
				if (process.Id != currentProcess.Id && Assembly.GetExecutingAssembly().Location.Replace(Class521.smethod_0(24570), Class521.smethod_0(45611)) == currentProcess.MainModule.FileName)
				{
					return process;
				}
			}
			return null;
		}

		// Token: 0x06001E36 RID: 7734
		[DllImport("User32.dll")]
		private static extern bool ShowWindowAsync(IntPtr intptr_0, int int_1);

		// Token: 0x06001E37 RID: 7735
		[DllImport("User32.dll")]
		private static extern bool SetForegroundWindow(IntPtr intptr_0);

		// Token: 0x06001E38 RID: 7736 RVA: 0x0000CA8E File Offset: 0x0000AC8E
		private static void smethod_4(Process process_0)
		{
			Class351.ShowWindowAsync(process_0.MainWindowHandle, 1);
			Class351.SetForegroundWindow(process_0.MainWindowHandle);
		}

		// Token: 0x06001E39 RID: 7737
		[DllImport("user32.dll", SetLastError = true)]
		internal static extern bool SetProcessDpiAwarenessContext(int int_1);

		// Token: 0x06001E3A RID: 7738
		[DllImport("SHCore.dll", SetLastError = true)]
		internal static extern bool SetProcessDpiAwareness(Class351.Enum23 enum23_0);

		// Token: 0x06001E3B RID: 7739
		[DllImport("user32.dll")]
		internal static extern bool SetProcessDPIAware();

		// Token: 0x06001E3C RID: 7740 RVA: 0x000D4628 File Offset: 0x000D2828
		public static void smethod_5(bool bool_0)
		{
			try
			{
				if (Environment.OSVersion.Version >= new Version(6, 3, 0))
				{
					if (Environment.OSVersion.Version >= new Version(10, 0, 15063))
					{
						Class351.Enum24 processDpiAwarenessContext = Class351.Enum24.const_0;
						if (bool_0)
						{
							processDpiAwarenessContext = Class351.Enum24.const_2;
							if (Environment.OSVersion.Version >= new Version(10, 0, 1607))
							{
								processDpiAwarenessContext = Class351.Enum24.const_3;
							}
						}
						Class351.SetProcessDpiAwarenessContext((int)processDpiAwarenessContext);
					}
					else
					{
						Class351.Enum23 processDpiAwareness = Class351.Enum23.const_0;
						if (bool_0)
						{
							processDpiAwareness = Class351.Enum23.const_2;
						}
						Class351.SetProcessDpiAwareness(processDpiAwareness);
					}
				}
				else if (bool_0)
				{
					Class351.SetProcessDPIAware();
				}
			}
			catch (Exception exception_)
			{
				Class351.smethod_2(exception_);
			}
		}

		// Token: 0x04000ED9 RID: 3801
		private static ResourceManager resourceManager_0 = new ResourceManager(Class521.smethod_0(86826), Assembly.GetExecutingAssembly());

		// Token: 0x04000EDA RID: 3802
		private const int int_0 = 1;

		// Token: 0x020002AB RID: 683
		public enum Enum23
		{
			// Token: 0x04000EDC RID: 3804
			const_0,
			// Token: 0x04000EDD RID: 3805
			const_1,
			// Token: 0x04000EDE RID: 3806
			const_2
		}

		// Token: 0x020002AC RID: 684
		public enum Enum24
		{
			// Token: 0x04000EE0 RID: 3808
			const_0 = -1,
			// Token: 0x04000EE1 RID: 3809
			const_1 = -2,
			// Token: 0x04000EE2 RID: 3810
			const_2 = -3,
			// Token: 0x04000EE3 RID: 3811
			const_3 = -4,
			// Token: 0x04000EE4 RID: 3812
			const_4 = -5
		}
	}
}
