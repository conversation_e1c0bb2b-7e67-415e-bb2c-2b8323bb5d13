﻿using System;
using System.Collections.Generic;
using System.Linq;
using ns11;
using ns18;
using ns7;

namespace TEx.SIndicator
{
	// Token: 0x0200032C RID: 812
	public sealed class Tokenes
	{
		// Token: 0x06002266 RID: 8806 RVA: 0x0000DB38 File Offset: 0x0000BD38
		public Tokenes(List<HToken> tokenList)
		{
			this.list_0 = tokenList;
		}

		// Token: 0x06002267 RID: 8807 RVA: 0x000F34BC File Offset: 0x000F16BC
		public string[] method_0()
		{
			return this.list_0.Where(new Func<HToken, bool>(Tokenes.<>c.<>9.method_0)).Select(new Func<HToken, string>(Tokenes.<>c.<>9.method_1)).ToArray<string>();
		}

		// Token: 0x06002268 RID: 8808 RVA: 0x000F3520 File Offset: 0x000F1720
		public HToken method_1()
		{
			this.int_0++;
			HToken result;
			if (this.int_0 >= this.list_0.Count)
			{
				result = new HToken(this.list_0.Last<HToken>().Col + 1, this.list_0.Last<HToken>().Line, new Class442(Enum26.const_30, Class521.smethod_0(102981)));
			}
			else
			{
				result = this.list_0[this.int_0];
			}
			return result;
		}

		// Token: 0x06002269 RID: 8809 RVA: 0x000F35A0 File Offset: 0x000F17A0
		public HToken method_2()
		{
			this.int_0--;
			if (this.int_0 <= 0)
			{
				this.int_0 = 0;
			}
			return this.list_0[this.int_0];
		}

		// Token: 0x170005E6 RID: 1510
		// (get) Token: 0x0600226A RID: 8810 RVA: 0x000F35E0 File Offset: 0x000F17E0
		public int Count
		{
			get
			{
				return this.list_0.Count;
			}
		}

		// Token: 0x0600226B RID: 8811 RVA: 0x000F35FC File Offset: 0x000F17FC
		public HToken method_3(int int_1)
		{
			return this.list_0[int_1];
		}

		// Token: 0x0600226C RID: 8812 RVA: 0x0000DB54 File Offset: 0x0000BD54
		public void method_4()
		{
			this.int_0 = 0;
		}

		// Token: 0x170005E7 RID: 1511
		// (get) Token: 0x0600226D RID: 8813 RVA: 0x000F361C File Offset: 0x000F181C
		public HToken Current
		{
			get
			{
				HToken result;
				if (this.int_0 >= this.list_0.Count)
				{
					result = new HToken(this.list_0.Last<HToken>().Col + 1, this.list_0.Last<HToken>().Line, new Class442(Enum26.const_30, Class521.smethod_0(102981)));
				}
				else
				{
					result = this.list_0[this.int_0];
				}
				return result;
			}
		}

		// Token: 0x040010C2 RID: 4290
		private List<HToken> list_0 = new List<HToken>();

		// Token: 0x040010C3 RID: 4291
		private int int_0;
	}
}
