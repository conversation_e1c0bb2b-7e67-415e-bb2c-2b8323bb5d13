﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;

namespace ns9
{
	// Token: 0x020000A9 RID: 169
	internal class Class65 : ComboBox
	{
		// Token: 0x060005CB RID: 1483 RVA: 0x000046C5 File Offset: 0x000028C5
		public Class65(Type type_1, DrawLineStyle drawLineStyle_1 = null, IContainer icontainer_1 = null)
		{
			if (icontainer_1 != null)
			{
				icontainer_1.Add(this);
			}
			this.method_2();
			this.method_0(type_1, drawLineStyle_1);
		}

		// Token: 0x060005CC RID: 1484 RVA: 0x000046E7 File Offset: 0x000028E7
		private void method_0(Type type_1, DrawLineStyle drawLineStyle_1)
		{
			this.type_0 = type_1;
			this.drawLineStyle_0 = drawLineStyle_1;
			base.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
			base.DropDownStyle = ComboBoxStyle.DropDownList;
			base.Items.Clear();
		}

		// Token: 0x060005CD RID: 1485 RVA: 0x0002CD44 File Offset: 0x0002AF44
		protected override void OnDrawItem(DrawItemEventArgs e)
		{
			e.DrawBackground();
			if (e.Index >= 0)
			{
				int int_ = int.Parse(base.Items[e.Index].ToString());
				this.method_1(int_, e.Bounds, e.Graphics);
			}
			e.DrawFocusRectangle();
		}

		// Token: 0x060005CE RID: 1486 RVA: 0x0002CD98 File Offset: 0x0002AF98
		private void method_1(int int_0, Rectangle rectangle_0, Graphics graphics_0)
		{
			Pen pen = new Pen(this.vmethod_0(), this.vmethod_1(int_0));
			if (this.vmethod_2(int_0) != null)
			{
				pen.DashPattern = this.vmethod_2(int_0);
			}
			if (int_0 < Enum.GetNames(this.type_0).Length)
			{
				float num = (float)(rectangle_0.Height / 2);
				float y = (float)rectangle_0.Top + num;
				graphics_0.DrawLine(pen, new PointF(0f, y), new PointF((float)rectangle_0.Width, y));
			}
			pen.Dispose();
		}

		// Token: 0x060005CF RID: 1487 RVA: 0x0002CE1C File Offset: 0x0002B01C
		protected virtual Color vmethod_0()
		{
			return Color.Black;
		}

		// Token: 0x060005D0 RID: 1488 RVA: 0x0002CE34 File Offset: 0x0002B034
		protected virtual float vmethod_1(int int_0)
		{
			float result = 1f;
			if (this.drawLineStyle_0 != null)
			{
				result = this.drawLineStyle_0.PenWidth;
			}
			return result;
		}

		// Token: 0x060005D1 RID: 1489 RVA: 0x0002CE60 File Offset: 0x0002B060
		protected virtual float[] vmethod_2(int int_0)
		{
			return null;
		}

		// Token: 0x060005D2 RID: 1490 RVA: 0x00004712 File Offset: 0x00002912
		protected override void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060005D3 RID: 1491 RVA: 0x00004733 File Offset: 0x00002933
		private void method_2()
		{
			this.icontainer_0 = new Container();
		}

		// Token: 0x04000296 RID: 662
		private Type type_0;

		// Token: 0x04000297 RID: 663
		private DrawLineStyle drawLineStyle_0;

		// Token: 0x04000298 RID: 664
		private IContainer icontainer_0;
	}
}
