﻿using System;
using System.Drawing;
using ns18;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns21
{
	// Token: 0x02000300 RID: 768
	internal sealed class Class401 : ShapeCurve
	{
		// Token: 0x0600214D RID: 8525 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class401(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x0600214E RID: 8526 RVA: 0x000EC934 File Offset: 0x000EAB34
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			StickItem stickItem = zedGraphControl_0.GraphPane.AddStick(base.IndData.Name, base.DataView, color_0);
			stickItem.Color = color_0;
			this.curveItem_0 = stickItem;
			stickItem.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			base.method_3(string_0, stickItem);
		}
	}
}
