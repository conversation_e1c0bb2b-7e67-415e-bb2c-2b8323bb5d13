﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using ns18;
using ns26;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020002BA RID: 698
	internal static class SymbMgr
	{
		// Token: 0x06001EEB RID: 7915 RVA: 0x000E06D4 File Offset: 0x000DE8D4
		public static void smethod_0()
		{
			if (TApp.SrvParams.MstSymblList != null)
			{
				using (List<TradingSymbol>.Enumerator enumerator = TApp.SrvParams.MstSymblList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						SymbMgr.Class360 @class = new SymbMgr.Class360();
						@class.tradingSymbol_0 = enumerator.Current;
						IEnumerable<TradingSymbol> source = SymbMgr.list_1.Where(new Func<TradingSymbol, bool>(@class.method_0));
						if (source.Any<TradingSymbol>())
						{
							source.First<TradingSymbol>().GetBaseProperties(@class.tradingSymbol_0);
						}
						else if (SymbMgr.UsrMstStkSymbIdList.Contains(@class.tradingSymbol_0.ID))
						{
							TradingSymbol tradingSymbol = new TradingSymbol();
							tradingSymbol.GetBaseProperties(@class.tradingSymbol_0);
							tradingSymbol.GetExtProperties(@class.tradingSymbol_0);
							SymbMgr.list_1.Add(tradingSymbol);
						}
					}
				}
				SymbMgr.smethod_33();
			}
		}

		// Token: 0x06001EEC RID: 7916 RVA: 0x000E07C0 File Offset: 0x000DE9C0
		public static ExchgHouse smethod_1(int int_2)
		{
			return SymbMgr.smethod_2(Base.Data.ExchangeList, int_2);
		}

		// Token: 0x06001EED RID: 7917 RVA: 0x000E07DC File Offset: 0x000DE9DC
		public static ExchgHouse smethod_2(List<ExchgHouse> list_3, int int_2)
		{
			SymbMgr.Class361 @class = new SymbMgr.Class361();
			@class.int_0 = int_2;
			ExchgHouse result;
			try
			{
				result = list_3.Single(new Func<ExchgHouse, bool>(@class.method_0));
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001EEE RID: 7918 RVA: 0x000E0828 File Offset: 0x000DEA28
		public static StkSymbol smethod_3(int int_2)
		{
			return SymbMgr.smethod_5(TApp.SrvParams.UsrStkSymbols, int_2);
		}

		// Token: 0x06001EEF RID: 7919 RVA: 0x000E084C File Offset: 0x000DEA4C
		public static StkSymbol smethod_4(int int_2, bool bool_0)
		{
			Dictionary<int, StkSymbol> dictionary_;
			if (bool_0)
			{
				dictionary_ = TApp.SrvParams.UsrStkSymbols;
			}
			else
			{
				dictionary_ = TApp.SrvParams.StkSymbols;
			}
			return SymbMgr.smethod_5(dictionary_, int_2);
		}

		// Token: 0x06001EF0 RID: 7920 RVA: 0x000E0880 File Offset: 0x000DEA80
		public static StkSymbol smethod_5(Dictionary<int, StkSymbol> dictionary_0, int int_2)
		{
			StkSymbol stkSymbol;
			dictionary_0.TryGetValue(int_2, out stkSymbol);
			StkSymbol result;
			if (dictionary_0.TryGetValue(int_2, out stkSymbol))
			{
				result = stkSymbol;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001EF1 RID: 7921 RVA: 0x000E08AC File Offset: 0x000DEAAC
		public static StkSymbol smethod_6(string string_1, string string_2)
		{
			SymbMgr.Class362 @class = new SymbMgr.Class362();
			@class.string_0 = string_1;
			@class.string_1 = string_2;
			return TApp.SrvParams.UsrStkSymbols.Values.SingleOrDefault(new Func<StkSymbol, bool>(@class.method_0));
		}

		// Token: 0x06001EF2 RID: 7922 RVA: 0x000E08F4 File Offset: 0x000DEAF4
		public static StkSymbol smethod_7(string string_1, int int_2)
		{
			SymbMgr.Class363 @class = new SymbMgr.Class363();
			@class.string_0 = string_1;
			@class.int_0 = int_2;
			return TApp.SrvParams.UsrStkSymbols.Values.SingleOrDefault(new Func<StkSymbol, bool>(@class.method_0));
		}

		// Token: 0x06001EF3 RID: 7923 RVA: 0x000E093C File Offset: 0x000DEB3C
		public static StkSymbol smethod_8(string string_1)
		{
			string string_2 = SymbMgr.smethod_9(string_1);
			int int_ = SymbMgr.smethod_10(string_1);
			return SymbMgr.smethod_7(string_2, int_);
		}

		// Token: 0x06001EF4 RID: 7924 RVA: 0x000E0960 File Offset: 0x000DEB60
		public static string smethod_9(string string_1)
		{
			int length = string_1.IndexOf(Class521.smethod_0(1733));
			return string_1.Substring(0, length);
		}

		// Token: 0x06001EF5 RID: 7925 RVA: 0x000E098C File Offset: 0x000DEB8C
		public static int smethod_10(string string_1)
		{
			int num = string_1.IndexOf(Class521.smethod_0(1733));
			return TExRoutine.GetExchgIdFromAbbr(string_1.Substring(num + 1));
		}

		// Token: 0x06001EF6 RID: 7926 RVA: 0x000E09BC File Offset: 0x000DEBBC
		public static TradingSymbol smethod_11(int int_2)
		{
			SymbMgr.Class364 @class = new SymbMgr.Class364();
			@class.int_0 = int_2;
			TradingSymbol result;
			try
			{
				result = SymbMgr.LocalMstSymbolList.Single(new Func<TradingSymbol, bool>(@class.method_0));
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001EF7 RID: 7927 RVA: 0x000E0A0C File Offset: 0x000DEC0C
		public static TradingSymbol smethod_12(string string_1)
		{
			SymbMgr.Class365 @class = new SymbMgr.Class365();
			@class.string_0 = string_1;
			return SymbMgr.LocalMstSymbolList.SingleOrDefault(new Func<TradingSymbol, bool>(@class.method_0));
		}

		// Token: 0x06001EF8 RID: 7928 RVA: 0x000E0A40 File Offset: 0x000DEC40
		public static StkSymbol smethod_13(string string_1)
		{
			SymbMgr.Class366 @class = new SymbMgr.Class366();
			@class.string_0 = string_1;
			return Base.Data.UsrStkSymbols.Values.SingleOrDefault(new Func<StkSymbol, bool>(@class.method_0));
		}

		// Token: 0x06001EF9 RID: 7929 RVA: 0x000E0A7C File Offset: 0x000DEC7C
		public static List<TradingSymbol> smethod_14()
		{
			object obj = Utility.DeserializeFile(SymbMgr.string_0);
			List<TradingSymbol> result;
			if (obj != null)
			{
				result = (List<TradingSymbol>)obj;
			}
			else
			{
				result = new List<TradingSymbol>();
			}
			return result;
		}

		// Token: 0x06001EFA RID: 7930 RVA: 0x000E0AAC File Offset: 0x000DECAC
		public static TradingSymbol smethod_15(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, TExPackage? nullable_0, StkSymbol stkSymbol_1, TradingSymbol tradingSymbol_0)
		{
			StkSymbol stkSymbol = stkSymbol_1;
			if (!stkSymbol_1.IsFuturesMI)
			{
				stkSymbol = SymbMgr.smethod_43(dictionary_0, tradingSymbol_0);
			}
			if (stkSymbol != null)
			{
				if (stkSymbol.Code == Class521.smethod_0(91139))
				{
					tradingSymbol_0.BeginDate = new DateTime?(new DateTime(1994, 1, 1));
					return tradingSymbol_0;
				}
				if (stkSymbol.Code == Class521.smethod_0(91148))
				{
					tradingSymbol_0.BeginDate = new DateTime?(new DateTime(2015, 10, 1));
					return tradingSymbol_0;
				}
				if (stkSymbol.Code == Class521.smethod_0(91157))
				{
					tradingSymbol_0.BeginDate = new DateTime?(new DateTime(2015, 11, 1));
					return tradingSymbol_0;
				}
				UsrStkMeta usrStkMeta = SymbMgr.smethod_17(dictionary_0, list_3, ienumerable_0, nullable_0, stkSymbol);
				if (usrStkMeta != null && usrStkMeta.BeginDate != null)
				{
					tradingSymbol_0.BeginDate = new DateTime?(usrStkMeta.BeginDate.Value);
				}
				else
				{
					tradingSymbol_0.BeginDate = null;
				}
			}
			return tradingSymbol_0;
		}

		// Token: 0x06001EFB RID: 7931 RVA: 0x000E0BC8 File Offset: 0x000DEDC8
		public static UsrStkMeta smethod_16(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, TExPackage? nullable_0, int int_2)
		{
			UsrStkMeta result;
			if (nullable_0 != null && nullable_0.Value > TExPackage.TRL)
			{
				result = SymbMgr.smethod_20(list_3, ienumerable_0, int_2);
			}
			else if (TApp.smethod_1(nullable_0))
			{
				result = SymbMgr.smethod_22(dictionary_0, list_3, ienumerable_0, int_2);
			}
			else
			{
				result = SymbMgr.smethod_19(dictionary_0, list_3, ienumerable_0, int_2);
			}
			return result;
		}

		// Token: 0x06001EFC RID: 7932 RVA: 0x000E0C1C File Offset: 0x000DEE1C
		public static UsrStkMeta smethod_17(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, TExPackage? nullable_0, StkSymbol stkSymbol_1)
		{
			UsrStkMeta result;
			if (nullable_0 != null && nullable_0.Value > TExPackage.TRL)
			{
				result = SymbMgr.smethod_20(list_3, ienumerable_0, stkSymbol_1.ID);
			}
			else if (TApp.smethod_1(nullable_0))
			{
				result = SymbMgr.smethod_21(dictionary_0, list_3, ienumerable_0, stkSymbol_1);
			}
			else
			{
				result = SymbMgr.smethod_18(dictionary_0, list_3, ienumerable_0, stkSymbol_1);
			}
			return result;
		}

		// Token: 0x06001EFD RID: 7933 RVA: 0x000E0C74 File Offset: 0x000DEE74
		private static UsrStkMeta smethod_18(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, StkSymbol stkSymbol_1)
		{
			return SymbMgr.smethod_23(dictionary_0, list_3, ienumerable_0, stkSymbol_1, false);
		}

		// Token: 0x06001EFE RID: 7934 RVA: 0x000E0C90 File Offset: 0x000DEE90
		private static UsrStkMeta smethod_19(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, int int_2)
		{
			return SymbMgr.smethod_24(dictionary_0, list_3, ienumerable_0, int_2, false);
		}

		// Token: 0x06001EFF RID: 7935 RVA: 0x000E0CAC File Offset: 0x000DEEAC
		private static UsrStkMeta smethod_20(List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, int int_2)
		{
			SymbMgr.Class367 @class = new SymbMgr.Class367();
			@class.int_0 = int_2;
			UsrStkMeta usrStkMeta = null;
			DateTime dateTime = list_3.First<UsrStkMeta>().EndDate.Value.Date.AddDays(1.0).AddMinutes(-1.0);
			StkBegEndDate stkBegEndDate = ienumerable_0.SingleOrDefault(new Func<StkBegEndDate, bool>(@class.method_0));
			if (stkBegEndDate != null)
			{
				usrStkMeta = new UsrStkMeta();
				usrStkMeta.StkId = @class.int_0;
				usrStkMeta.BeginDate = new DateTime?(stkBegEndDate.MinBegDate_1m);
				DateTime maxEndDate_1m = stkBegEndDate.MaxEndDate_1m;
				usrStkMeta.EndDate = new DateTime?((maxEndDate_1m < dateTime) ? maxEndDate_1m : dateTime);
				List<DatInfo> list = new List<DatInfo>();
				list.Add(new DatInfo
				{
					PeriodType = PeriodType.ByMins,
					PeriodUnits = new int?(1),
					BeginDate = usrStkMeta.BeginDate,
					EndDate = usrStkMeta.EndDate
				});
				DatInfo datInfo = new DatInfo();
				datInfo.PeriodType = PeriodType.ByMins;
				datInfo.PeriodUnits = new int?(60);
				datInfo.BeginDate = new DateTime?(stkBegEndDate.MinBegDate_1h);
				DateTime maxEndDate_1h = stkBegEndDate.MaxEndDate_1h;
				datInfo.EndDate = new DateTime?((maxEndDate_1h < dateTime) ? maxEndDate_1h : dateTime);
				list.Add(datInfo);
				usrStkMeta.DatInfoList = list;
			}
			return usrStkMeta;
		}

		// Token: 0x06001F00 RID: 7936 RVA: 0x000E0E18 File Offset: 0x000DF018
		private static UsrStkMeta smethod_21(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, StkSymbol stkSymbol_1)
		{
			return SymbMgr.smethod_23(dictionary_0, list_3, ienumerable_0, stkSymbol_1, true);
		}

		// Token: 0x06001F01 RID: 7937 RVA: 0x000E0E34 File Offset: 0x000DF034
		private static UsrStkMeta smethod_22(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, int int_2)
		{
			return SymbMgr.smethod_24(dictionary_0, list_3, ienumerable_0, int_2, true);
		}

		// Token: 0x06001F02 RID: 7938 RVA: 0x000E0E50 File Offset: 0x000DF050
		private static UsrStkMeta smethod_23(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, StkSymbol stkSymbol_1, bool bool_0)
		{
			StkSymbol stkSymbol_2 = SymbMgr.smethod_41(dictionary_0, stkSymbol_1);
			return SymbMgr.smethod_26(dictionary_0, list_3, ienumerable_0, stkSymbol_2, stkSymbol_1.ID, bool_0);
		}

		// Token: 0x06001F03 RID: 7939 RVA: 0x000E0E7C File Offset: 0x000DF07C
		private static UsrStkMeta smethod_24(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, int int_2, bool bool_0)
		{
			StkSymbol stkSymbol_ = SymbMgr.smethod_38(dictionary_0, int_2);
			return SymbMgr.smethod_26(dictionary_0, list_3, ienumerable_0, stkSymbol_, int_2, bool_0);
		}

		// Token: 0x06001F04 RID: 7940 RVA: 0x000E0EA4 File Offset: 0x000DF0A4
		private static UsrStkMeta smethod_25(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, StkSymbol stkSymbol_1, int int_2)
		{
			return SymbMgr.smethod_26(dictionary_0, list_3, ienumerable_0, stkSymbol_1, int_2, true);
		}

		// Token: 0x06001F05 RID: 7941 RVA: 0x000E0EC4 File Offset: 0x000DF0C4
		private static UsrStkMeta smethod_26(Dictionary<int, StkSymbol> dictionary_0, List<UsrStkMeta> list_3, IEnumerable<StkBegEndDate> ienumerable_0, StkSymbol stkSymbol_1, int int_2, bool bool_0)
		{
			SymbMgr.Class368 @class = new SymbMgr.Class368();
			@class.stkSymbol_0 = stkSymbol_1;
			@class.int_0 = int_2;
			UsrStkMeta usrStkMeta = null;
			IEnumerable<UsrStkMeta> source = list_3.Where(new Func<UsrStkMeta, bool>(@class.method_0));
			if (source.Any<UsrStkMeta>())
			{
				UsrStkMeta usrStkMeta2 = source.First<UsrStkMeta>();
				usrStkMeta = new UsrStkMeta();
				usrStkMeta.BeginDate = usrStkMeta2.BeginDate;
				usrStkMeta.EndDate = usrStkMeta2.EndDate;
				StkBegEndDate stkBegEndDate = ienumerable_0.SingleOrDefault(new Func<StkBegEndDate, bool>(@class.method_1));
				if (stkBegEndDate == null)
				{
					return null;
				}
				DateTime minBegDate_1m = stkBegEndDate.MinBegDate_1m;
				DateTime maxEndDate_1m = stkBegEndDate.MaxEndDate_1m;
				if (usrStkMeta.BeginDate < minBegDate_1m)
				{
					usrStkMeta.BeginDate = new DateTime?(minBegDate_1m);
				}
				if (usrStkMeta.EndDate > maxEndDate_1m)
				{
					usrStkMeta.EndDate = new DateTime?(maxEndDate_1m);
				}
				usrStkMeta.StkId = @class.int_0;
				List<DatInfo> list = new List<DatInfo>();
				DatInfo datInfo = new DatInfo();
				datInfo.PeriodType = PeriodType.ByMins;
				datInfo.PeriodUnits = new int?(1);
				if (bool_0)
				{
					if (stkBegEndDate != null)
					{
						datInfo.BeginDate = new DateTime?(stkBegEndDate.MinBegDate_1m);
						datInfo.EndDate = new DateTime?(stkBegEndDate.MaxEndDate_1m);
					}
				}
				else
				{
					datInfo.BeginDate = usrStkMeta.BeginDate;
					datInfo.EndDate = usrStkMeta.EndDate;
				}
				list.Add(datInfo);
				DatInfo datInfo2 = new DatInfo();
				datInfo2.PeriodType = PeriodType.ByMins;
				datInfo2.PeriodUnits = new int?(60);
				if (stkBegEndDate != null)
				{
					datInfo2.BeginDate = new DateTime?(stkBegEndDate.MinBegDate_1h);
					datInfo2.EndDate = new DateTime?(stkBegEndDate.MaxEndDate_1h);
					list.Add(datInfo2);
				}
				usrStkMeta.DatInfoList = list;
			}
			return usrStkMeta;
		}

		// Token: 0x06001F06 RID: 7942 RVA: 0x000E10AC File Offset: 0x000DF2AC
		public static StkSymbol smethod_27(Dictionary<int, StkSymbol> dictionary_0, Account account_0)
		{
			StkSymbol result;
			if (account_0.LastSymbID != null)
			{
				StkSymbol stkSymbol = SymbMgr.smethod_5(dictionary_0, account_0.LastSymbID.Value);
				if (stkSymbol != null)
				{
					result = stkSymbol;
				}
				else
				{
					result = SymbMgr.smethod_28(dictionary_0);
				}
			}
			else
			{
				result = SymbMgr.smethod_28(dictionary_0);
			}
			return result;
		}

		// Token: 0x06001F07 RID: 7943 RVA: 0x000E10F8 File Offset: 0x000DF2F8
		private static StkSymbol smethod_28(Dictionary<int, StkSymbol> dictionary_0)
		{
			StkSymbol result;
			if (TApp.IsLoggedIn)
			{
				if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.HasValidDataSet)
				{
					result = Base.Data.CurrSymbDataSet.CurrSymbol;
				}
				else if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null && Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol != null)
				{
					result = Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol;
				}
				else
				{
					result = SymbMgr.smethod_29(dictionary_0);
				}
			}
			else
			{
				result = SymbMgr.smethod_29(dictionary_0);
			}
			return result;
		}

		// Token: 0x06001F08 RID: 7944 RVA: 0x000E1178 File Offset: 0x000DF378
		private static StkSymbol smethod_29(Dictionary<int, StkSymbol> dictionary_0)
		{
			StkSymbol stkSymbol = null;
			if (dictionary_0 != null && dictionary_0.Count > 0 && stkSymbol == null)
			{
				if (TApp.IsStIncluded)
				{
					stkSymbol = dictionary_0.Values.FirstOrDefault(new Func<StkSymbol, bool>(SymbMgr.<>c.<>9.method_0));
				}
				else
				{
					stkSymbol = dictionary_0.Values.First<StkSymbol>();
				}
			}
			return stkSymbol;
		}

		// Token: 0x06001F09 RID: 7945 RVA: 0x000E11DC File Offset: 0x000DF3DC
		public static StkSymbol smethod_30(Account account_0)
		{
			return SymbMgr.smethod_27(TApp.SrvParams.UsrStkSymbols, account_0);
		}

		// Token: 0x06001F0A RID: 7946 RVA: 0x000E1200 File Offset: 0x000DF400
		private static List<ExchgHouse> smethod_31()
		{
			return TApp.SrvParams.ExchgHsList;
		}

		// Token: 0x06001F0B RID: 7947 RVA: 0x0000D0E0 File Offset: 0x0000B2E0
		public static void smethod_32()
		{
			if (!File.Exists(SymbMgr.string_0))
			{
				SymbMgr.smethod_33();
			}
		}

		// Token: 0x06001F0C RID: 7948 RVA: 0x0000D0F5 File Offset: 0x0000B2F5
		public static void smethod_33()
		{
			Utility.GenSerializedFile(SymbMgr.LocalMstSymbolList, SymbMgr.string_0, true);
		}

		// Token: 0x06001F0D RID: 7949 RVA: 0x000E121C File Offset: 0x000DF41C
		public static TradingSymbol smethod_34(int int_2, string string_1)
		{
			return SymbMgr.smethod_35(TApp.SrvParams.MstSymblList, int_2, string_1);
		}

		// Token: 0x06001F0E RID: 7950 RVA: 0x000E1240 File Offset: 0x000DF440
		public static TradingSymbol smethod_35(List<TradingSymbol> list_3, int int_2, string string_1)
		{
			SymbMgr.Class369 @class = new SymbMgr.Class369();
			@class.int_0 = int_2;
			string_1 = string_1.Trim();
			TradingSymbol result = null;
			if (@class.int_0 < 5)
			{
				SymbMgr.Class370 class2 = new SymbMgr.Class370();
				class2.class369_0 = @class;
				int num = 0;
				string text = string_1;
				int num2 = 0;
				while (num2 < text.Length && !char.IsDigit(text[num2]))
				{
					num++;
					num2++;
				}
				class2.string_0 = string_1.Substring(0, num);
				if (!class2.string_0.Equals(Class521.smethod_0(91162), StringComparison.OrdinalIgnoreCase) && !class2.string_0.Equals(Class521.smethod_0(91167), StringComparison.OrdinalIgnoreCase))
				{
					if (!class2.string_0.Equals(Class521.smethod_0(91177), StringComparison.OrdinalIgnoreCase) && !class2.string_0.Equals(Class521.smethod_0(91182), StringComparison.OrdinalIgnoreCase))
					{
						if (class2.string_0.Equals(Class521.smethod_0(91192), StringComparison.OrdinalIgnoreCase) || class2.string_0.Equals(Class521.smethod_0(91197), StringComparison.OrdinalIgnoreCase))
						{
							class2.string_0 = Class521.smethod_0(91202);
						}
					}
					else
					{
						class2.string_0 = Class521.smethod_0(91187);
					}
				}
				else
				{
					class2.string_0 = Class521.smethod_0(91172);
				}
				try
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(class2.method_0));
					goto IL_324;
				}
				catch
				{
					throw;
				}
			}
			if (@class.int_0 == 5)
			{
				if (string_1.StartsWith(Class521.smethod_0(67601)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_1));
				}
				else if (string_1.StartsWith(Class521.smethod_0(12122)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_2));
				}
				else if (string_1.StartsWith(Class521.smethod_0(50245)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_3));
				}
				else
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_4));
				}
			}
			else if (@class.int_0 == 6)
			{
				if (string_1.StartsWith(Class521.smethod_0(67625)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_5));
				}
				else if (string_1.StartsWith(Class521.smethod_0(4933)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_6));
				}
				else if (string_1.StartsWith(Class521.smethod_0(50250)))
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_7));
				}
				else
				{
					result = list_3.Single(new Func<TradingSymbol, bool>(SymbMgr.<>c.<>9.method_8));
				}
			}
			IL_324:
			return result;
		}

		// Token: 0x06001F0F RID: 7951 RVA: 0x000E1588 File Offset: 0x000DF788
		public static TradingSymbol smethod_36(int int_2)
		{
			StkSymbol stkSymbol = SymbMgr.smethod_3(int_2);
			TradingSymbol result;
			if (stkSymbol != null)
			{
				result = SymbMgr.smethod_34(stkSymbol.ExchangeID, stkSymbol.Code);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001F10 RID: 7952 RVA: 0x000E15B8 File Offset: 0x000DF7B8
		public static TradingSymbol smethod_37(int int_2)
		{
			SymbMgr.Class371 @class = new SymbMgr.Class371();
			@class.int_0 = int_2;
			return SymbMgr.LocalMstSymbolList.Single(new Func<TradingSymbol, bool>(@class.method_0));
		}

		// Token: 0x06001F11 RID: 7953 RVA: 0x000E15EC File Offset: 0x000DF7EC
		public static StkSymbol smethod_38(Dictionary<int, StkSymbol> dictionary_0, int int_2)
		{
			StkSymbol stkSymbol_ = SymbMgr.smethod_5(dictionary_0, int_2);
			return SymbMgr.smethod_41(dictionary_0, stkSymbol_);
		}

		// Token: 0x06001F12 RID: 7954 RVA: 0x000E160C File Offset: 0x000DF80C
		public static StkSymbol smethod_39(int int_2)
		{
			return SymbMgr.smethod_40(SymbMgr.smethod_3(int_2));
		}

		// Token: 0x06001F13 RID: 7955 RVA: 0x000E1628 File Offset: 0x000DF828
		public static StkSymbol smethod_40(StkSymbol stkSymbol_1)
		{
			return SymbMgr.smethod_41(TApp.SrvParams.UsrStkSymbols, stkSymbol_1);
		}

		// Token: 0x06001F14 RID: 7956 RVA: 0x000E164C File Offset: 0x000DF84C
		public static StkSymbol smethod_41(Dictionary<int, StkSymbol> dictionary_0, StkSymbol stkSymbol_1)
		{
			StkSymbol result = stkSymbol_1;
			if (stkSymbol_1.IsFutures)
			{
				SymbMgr.Class372 @class = new SymbMgr.Class372();
				@class.string_0 = stkSymbol_1.Code;
				if (!@class.string_0.EndsWith(Class521.smethod_0(36100)))
				{
					result = dictionary_0.SingleOrDefault(new Func<KeyValuePair<int, StkSymbol>, bool>(@class.method_0)).Value;
				}
			}
			return result;
		}

		// Token: 0x06001F15 RID: 7957 RVA: 0x000E16AC File Offset: 0x000DF8AC
		public static string smethod_42(TradingSymbol tradingSymbol_0)
		{
			string code = tradingSymbol_0.Code;
			int num = code.LastIndexOf(Class521.smethod_0(2712));
			string text = code.Substring(num + 1, code.Length - num - 1);
			if (text.Equals(Class521.smethod_0(91172), StringComparison.OrdinalIgnoreCase))
			{
				text = Class521.smethod_0(91162);
			}
			else if (text.Equals(Class521.smethod_0(91207), StringComparison.OrdinalIgnoreCase))
			{
				text = Class521.smethod_0(91177);
			}
			else if (text.Equals(Class521.smethod_0(91212), StringComparison.OrdinalIgnoreCase))
			{
				text = Class521.smethod_0(91192);
			}
			return text + Class521.smethod_0(36100);
		}

		// Token: 0x06001F16 RID: 7958 RVA: 0x000E175C File Offset: 0x000DF95C
		public static StkSymbol smethod_43(Dictionary<int, StkSymbol> dictionary_0, TradingSymbol tradingSymbol_0)
		{
			SymbMgr.Class373 @class = new SymbMgr.Class373();
			@class.tradingSymbol_0 = tradingSymbol_0;
			StkSymbol result = null;
			try
			{
				result = dictionary_0.Single(new Func<KeyValuePair<int, StkSymbol>, bool>(@class.method_0)).Value;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x06001F17 RID: 7959 RVA: 0x000E17B4 File Offset: 0x000DF9B4
		public static StkSymbol smethod_44()
		{
			StkSymbol stkSymbol = null;
			if (Base.Acct.CurrAccount.LastSymbID != null)
			{
				stkSymbol = SymbMgr.smethod_45(Base.Acct.CurrAccount.LastSymbID.Value);
			}
			if (stkSymbol == null)
			{
				stkSymbol = SymbMgr.smethod_27(TApp.SrvParams.UsrStkSymbols, Base.Acct.CurrAccount);
			}
			return stkSymbol;
		}

		// Token: 0x06001F18 RID: 7960 RVA: 0x000E180C File Offset: 0x000DFA0C
		private static StkSymbol smethod_45(int int_2)
		{
			SymbMgr.Class374 @class = new SymbMgr.Class374();
			@class.int_0 = int_2;
			StkSymbol stkSymbol = SymbMgr.smethod_5(TApp.SrvParams.UsrStkSymbols, @class.int_0);
			if (stkSymbol == null && SymbMgr.smethod_14().Where(new Func<TradingSymbol, bool>(@class.method_0)).Any<TradingSymbol>())
			{
				try
				{
					stkSymbol = TApp.SrvParams.UsrStkSymbols.SingleOrDefault(new Func<KeyValuePair<int, StkSymbol>, bool>(@class.method_1)).Value;
					if (stkSymbol != null)
					{
						Base.Acct.CurrAccount.LastSymbID = new int?(stkSymbol.ID);
					}
				}
				catch
				{
					Base.Acct.CurrAccount.LastSymbID = null;
				}
			}
			return stkSymbol;
		}

		// Token: 0x170004D1 RID: 1233
		// (get) Token: 0x06001F19 RID: 7961 RVA: 0x000E18C8 File Offset: 0x000DFAC8
		// (set) Token: 0x06001F1A RID: 7962 RVA: 0x0000D109 File Offset: 0x0000B309
		public static StkSymbol StartUpSymbl
		{
			get
			{
				if (SymbMgr.stkSymbol_0 == null)
				{
					SymbMgr.stkSymbol_0 = SymbMgr.smethod_44();
				}
				return SymbMgr.stkSymbol_0;
			}
			set
			{
				SymbMgr.stkSymbol_0 = value;
			}
		}

		// Token: 0x06001F1B RID: 7963 RVA: 0x000E18F0 File Offset: 0x000DFAF0
		public static List<StkSymbol> smethod_46()
		{
			List<StkSymbol> list = new List<StkSymbol>();
			List<ChartPage> list2 = Base.UI.smethod_97();
			ChartUISettings? chartUISettings = Base.UI.smethod_96(list2, Base.UI.Form.CurrentPageName);
			if (chartUISettings == null)
			{
				chartUISettings = new ChartUISettings?(list2.First<ChartPage>().UISettings);
				Base.UI.Form.CurrentPageName = list2.First<ChartPage>().Name;
			}
			if (chartUISettings.Value.HasSyncCharts)
			{
				StkSymbol stkSymbol = SymbMgr.smethod_44();
				if (stkSymbol != null)
				{
					list.Add(stkSymbol);
				}
			}
			List<int> linkedSymbolIds = chartUISettings.Value.LinkedSymbolIds;
			if (linkedSymbolIds != null)
			{
				foreach (int int_ in linkedSymbolIds)
				{
					StkSymbol stkSymbol2 = SymbMgr.smethod_45(int_);
					if (stkSymbol2 != null && !list.Contains(stkSymbol2))
					{
						list.Add(stkSymbol2);
					}
				}
			}
			return list;
		}

		// Token: 0x170004D2 RID: 1234
		// (get) Token: 0x06001F1C RID: 7964 RVA: 0x000E19E4 File Offset: 0x000DFBE4
		// (set) Token: 0x06001F1D RID: 7965 RVA: 0x0000D113 File Offset: 0x0000B313
		public static List<StkSymbol> StartUpSymblList
		{
			get
			{
				if (SymbMgr.list_0 == null)
				{
					SymbMgr.list_0 = SymbMgr.smethod_46();
				}
				return SymbMgr.list_0;
			}
			set
			{
				SymbMgr.list_0 = value;
			}
		}

		// Token: 0x170004D3 RID: 1235
		// (get) Token: 0x06001F1E RID: 7966 RVA: 0x000E1A0C File Offset: 0x000DFC0C
		// (set) Token: 0x06001F1F RID: 7967 RVA: 0x0000D11D File Offset: 0x0000B31D
		public static List<TradingSymbol> LocalMstSymbolList
		{
			get
			{
				return SymbMgr.list_1;
			}
			set
			{
				SymbMgr.list_1 = value;
			}
		}

		// Token: 0x170004D4 RID: 1236
		// (get) Token: 0x06001F20 RID: 7968 RVA: 0x000E1A24 File Offset: 0x000DFC24
		public static List<int> UsrMstStkSymbIdList
		{
			get
			{
				if (SymbMgr.list_2 == null)
				{
					SymbMgr.list_2 = TApp.SrvParams.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(SymbMgr.<>c.<>9.method_9)).Select(new Func<StkSymbol, int>(SymbMgr.<>c.<>9.method_10)).Distinct<int>().ToList<int>();
				}
				return SymbMgr.list_2;
			}
		}

		// Token: 0x170004D5 RID: 1237
		// (get) Token: 0x06001F21 RID: 7969 RVA: 0x000E1AA8 File Offset: 0x000DFCA8
		public static int MinBlindTestDays
		{
			get
			{
				int result = SymbMgr.int_0;
				if (TApp.IsTrialUser)
				{
					result = SymbMgr.int_1;
				}
				return result;
			}
		}

		// Token: 0x04000F93 RID: 3987
		private static readonly string string_0 = TApp.UserAcctFolder + Class521.smethod_0(91122);

		// Token: 0x04000F94 RID: 3988
		private static readonly int int_0 = 90;

		// Token: 0x04000F95 RID: 3989
		private static readonly int int_1 = 30;

		// Token: 0x04000F96 RID: 3990
		private static StkSymbol stkSymbol_0;

		// Token: 0x04000F97 RID: 3991
		private static List<StkSymbol> list_0;

		// Token: 0x04000F98 RID: 3992
		private static List<TradingSymbol> list_1 = SymbMgr.smethod_14();

		// Token: 0x04000F99 RID: 3993
		private static List<int> list_2;

		// Token: 0x020002BB RID: 699
		[CompilerGenerated]
		private sealed class Class360
		{
			// Token: 0x06001F23 RID: 7971 RVA: 0x000E1AD0 File Offset: 0x000DFCD0
			internal bool method_0(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x04000F9A RID: 3994
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x020002BC RID: 700
		[CompilerGenerated]
		private sealed class Class361
		{
			// Token: 0x06001F25 RID: 7973 RVA: 0x000E1AF4 File Offset: 0x000DFCF4
			internal bool method_0(ExchgHouse exchgHouse_0)
			{
				return exchgHouse_0.ID == this.int_0;
			}

			// Token: 0x04000F9B RID: 3995
			public int int_0;
		}

		// Token: 0x020002BD RID: 701
		[CompilerGenerated]
		private sealed class Class362
		{
			// Token: 0x06001F27 RID: 7975 RVA: 0x000E1B14 File Offset: 0x000DFD14
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				bool result;
				if (stkSymbol_0.Code == this.string_0)
				{
					result = (stkSymbol_0.CNName == this.string_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000F9C RID: 3996
			public string string_0;

			// Token: 0x04000F9D RID: 3997
			public string string_1;
		}

		// Token: 0x020002BE RID: 702
		[CompilerGenerated]
		private sealed class Class363
		{
			// Token: 0x06001F29 RID: 7977 RVA: 0x000E1B50 File Offset: 0x000DFD50
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				bool result;
				if (stkSymbol_0.Code == this.string_0)
				{
					result = (stkSymbol_0.ExchangeID == this.int_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000F9E RID: 3998
			public string string_0;

			// Token: 0x04000F9F RID: 3999
			public int int_0;
		}

		// Token: 0x020002BF RID: 703
		[CompilerGenerated]
		private sealed class Class364
		{
			// Token: 0x06001F2B RID: 7979 RVA: 0x000E1B88 File Offset: 0x000DFD88
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ID == this.int_0;
			}

			// Token: 0x04000FA0 RID: 4000
			public int int_0;
		}

		// Token: 0x020002C0 RID: 704
		[CompilerGenerated]
		private sealed class Class365
		{
			// Token: 0x06001F2D RID: 7981 RVA: 0x000E1BA8 File Offset: 0x000DFDA8
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.Code == this.string_0;
			}

			// Token: 0x04000FA1 RID: 4001
			public string string_0;
		}

		// Token: 0x020002C1 RID: 705
		[CompilerGenerated]
		private sealed class Class366
		{
			// Token: 0x06001F2F RID: 7983 RVA: 0x000E1BCC File Offset: 0x000DFDCC
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.Code == this.string_0;
			}

			// Token: 0x04000FA2 RID: 4002
			public string string_0;
		}

		// Token: 0x020002C2 RID: 706
		[CompilerGenerated]
		private sealed class Class367
		{
			// Token: 0x06001F31 RID: 7985 RVA: 0x000E1BF0 File Offset: 0x000DFDF0
			internal bool method_0(StkBegEndDate stkBegEndDate_0)
			{
				return stkBegEndDate_0.StkId == this.int_0;
			}

			// Token: 0x04000FA3 RID: 4003
			public int int_0;
		}

		// Token: 0x020002C3 RID: 707
		[CompilerGenerated]
		private sealed class Class368
		{
			// Token: 0x06001F33 RID: 7987 RVA: 0x000E1C10 File Offset: 0x000DFE10
			internal bool method_0(UsrStkMeta usrStkMeta_0)
			{
				return usrStkMeta_0.StkId == this.stkSymbol_0.ID;
			}

			// Token: 0x06001F34 RID: 7988 RVA: 0x000E1C34 File Offset: 0x000DFE34
			internal bool method_1(StkBegEndDate stkBegEndDate_0)
			{
				return stkBegEndDate_0.StkId == this.int_0;
			}

			// Token: 0x04000FA4 RID: 4004
			public StkSymbol stkSymbol_0;

			// Token: 0x04000FA5 RID: 4005
			public int int_0;
		}

		// Token: 0x020002C5 RID: 709
		[CompilerGenerated]
		private sealed class Class369
		{
			// Token: 0x04000FB2 RID: 4018
			public int int_0;
		}

		// Token: 0x020002C6 RID: 710
		[CompilerGenerated]
		private sealed class Class370
		{
			// Token: 0x06001F44 RID: 8004 RVA: 0x000E1D94 File Offset: 0x000DFF94
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				bool result;
				if (tradingSymbol_0.ExchangeID == this.class369_0.int_0)
				{
					result = this.string_0.Equals(tradingSymbol_0.Code.Trim().Substring(3, tradingSymbol_0.Code.Trim().Length - 3), StringComparison.OrdinalIgnoreCase);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000FB3 RID: 4019
			public string string_0;

			// Token: 0x04000FB4 RID: 4020
			public SymbMgr.Class369 class369_0;
		}

		// Token: 0x020002C7 RID: 711
		[CompilerGenerated]
		private sealed class Class371
		{
			// Token: 0x06001F46 RID: 8006 RVA: 0x000E1DEC File Offset: 0x000DFFEC
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ID == this.int_0;
			}

			// Token: 0x04000FB5 RID: 4021
			public int int_0;
		}

		// Token: 0x020002C8 RID: 712
		[CompilerGenerated]
		private sealed class Class372
		{
			// Token: 0x06001F48 RID: 8008 RVA: 0x000E1E0C File Offset: 0x000E000C
			internal bool method_0(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				return keyValuePair_0.Value.Code.Equals(this.string_0.Substring(0, this.string_0.Length - 2) + Class521.smethod_0(36100));
			}

			// Token: 0x04000FB6 RID: 4022
			public string string_0;
		}

		// Token: 0x020002C9 RID: 713
		[CompilerGenerated]
		private sealed class Class373
		{
			// Token: 0x06001F4A RID: 8010 RVA: 0x000E1E58 File Offset: 0x000E0058
			internal bool method_0(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Value.IsFuturesMI)
				{
					result = (keyValuePair_0.Value.MstSymbol.ID == this.tradingSymbol_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000FB7 RID: 4023
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x020002CA RID: 714
		[CompilerGenerated]
		private sealed class Class374
		{
			// Token: 0x06001F4C RID: 8012 RVA: 0x000E1E9C File Offset: 0x000E009C
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ID == this.int_0;
			}

			// Token: 0x06001F4D RID: 8013 RVA: 0x000E1EBC File Offset: 0x000E00BC
			internal bool method_1(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Value.IsFuturesMI)
				{
					result = (keyValuePair_0.Value.MstSymbol.ID == this.int_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000FB8 RID: 4024
			public int int_0;
		}
	}
}
