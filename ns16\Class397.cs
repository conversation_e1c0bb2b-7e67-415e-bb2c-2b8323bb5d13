﻿using System;
using System.Drawing;
using ns18;
using ns26;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns16
{
	// Token: 0x020002F9 RID: 761
	internal sealed class Class397 : ShapeCurve
	{
		// Token: 0x06002131 RID: 8497 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class397(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002132 RID: 8498 RVA: 0x000EBB8C File Offset: 0x000E9D8C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_FillLineItem ind_FillLineItem = zedGraphControl_0.GraphPane.AddIndFillLineItem(base.IndData.Name, base.DataView, color_0, SymbolType.None);
			this.curveItem_0 = ind_FillLineItem;
			ind_FillLineItem.Tag = string_0 + Class521.smethod_0(2712) + base.IndData.Name;
			ind_FillLineItem.Line.Color = color_0;
			ind_FillLineItem.Line.IsVisible = false;
			ind_FillLineItem.Line.IsAntiAlias = true;
			base.method_3(string_0, ind_FillLineItem);
		}

		// Token: 0x06002133 RID: 8499 RVA: 0x000EBC30 File Offset: 0x000E9E30
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			double y;
			if (int_0 < dataArray_1.Data.Length && int_0 >= 0)
			{
				y = dataArray_1.Data[int_0];
			}
			else
			{
				string message = Class521.smethod_0(98562);
				if (int_0 < 0)
				{
					message = Class521.smethod_0(98591);
				}
				Exception ex = new Exception(message);
				Class184.smethod_0(ex);
				if (dataArray_1.Data.Length == 0)
				{
					throw ex;
				}
				y = dataArray_1.Data[dataArray_1.Data.Length - 1];
			}
			if (dataArray_1.OtherDataArrayList.Count != 2)
			{
				throw new Exception(Class521.smethod_0(98360));
			}
			double t = dataArray_1.OtherDataArrayList[0].Data[int_0];
			double z = dataArray_1.OtherDataArrayList[1].Data[int_0];
			return new PointPair4(new XDate(base.method_0(int_0)), y, z, t);
		}
	}
}
