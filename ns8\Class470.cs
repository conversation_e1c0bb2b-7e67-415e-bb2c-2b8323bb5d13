﻿using System;
using ns18;

namespace ns8
{
	// Token: 0x02000360 RID: 864
	internal static class Class470
	{
		// Token: 0x060023ED RID: 9197 RVA: 0x000041B9 File Offset: 0x000023B9
		public static void smethod_0(string string_0, string string_1)
		{
		}

		// Token: 0x060023EE RID: 9198 RVA: 0x0000E116 File Offset: 0x0000C316
		public static void smethod_1(string string_0)
		{
			Class470.smethod_0(Class521.smethod_0(105942), string_0);
		}

		// Token: 0x04001165 RID: 4453
		private static object object_0 = new object();
	}
}
