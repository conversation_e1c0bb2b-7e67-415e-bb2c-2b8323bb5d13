﻿using System;
using ns12;
using ns18;
using ns28;
using ns29;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns14
{
	// Token: 0x02000334 RID: 820
	internal sealed class Class423 : Class415
	{
		// Token: 0x0600228B RID: 8843 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class423(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x0600228C RID: 8844 RVA: 0x000F42D8 File Offset: 0x000F24D8
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			Class411 @class = Class413.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0(Class521.smethod_0(101720)));
			}
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = @class;
			}
			else
			{
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
				if (htoken.Symbol.HSymbolType == Enum26.const_8 | htoken.Symbol.HSymbolType == Enum26.const_9)
				{
					result = Class423.smethod_1(@class, tokenes_0);
				}
				else
				{
					tokenes_0.method_2();
					result = @class;
				}
			}
			return result;
		}

		// Token: 0x0600228D RID: 8845 RVA: 0x000F4368 File Offset: 0x000F2568
		private static Class411 smethod_1(Class411 class411_2, Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			if (!(htoken.Symbol.HSymbolType == Enum26.const_8 | htoken.Symbol.HSymbolType == Enum26.const_9))
			{
				throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(103438)));
			}
			HToken htoken2 = htoken;
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(htoken2.method_0(Class521.smethod_0(103409)));
			}
			Class411 @class = Class413.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken2.method_0(Class521.smethod_0(103409)));
			}
			Class423 class2 = new Class423(htoken2, class411_2, @class);
			tokenes_0.method_1();
			Class411 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_8 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_9)
			{
				result = Class423.smethod_1(class2, tokenes_0);
			}
			else if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = class2;
			}
			else
			{
				tokenes_0.method_2();
				result = class2;
			}
			return result;
		}

		// Token: 0x0600228E RID: 8846 RVA: 0x000F447C File Offset: 0x000F267C
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			return this.vmethod_2(this.Left.vmethod_1(parserEnvironment_0), this.Right.vmethod_1(parserEnvironment_0));
		}

		// Token: 0x0600228F RID: 8847 RVA: 0x000F44AC File Offset: 0x000F26AC
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_8)
			{
				if (hsymbolType != Enum26.const_9)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(103475)));
				}
				result = double_0 / double_1;
			}
			else
			{
				result = double_0 * double_1;
			}
			return result;
		}

		// Token: 0x06002290 RID: 8848 RVA: 0x000F44FC File Offset: 0x000F26FC
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_8)
			{
				if (hsymbolType != Enum26.const_9)
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(103475)));
				}
				result = dataArray_0 / dataArray_1;
			}
			else
			{
				result = dataArray_0 * dataArray_1;
			}
			return result;
		}

		// Token: 0x06002291 RID: 8849 RVA: 0x000F0E88 File Offset: 0x000EF088
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x170005E8 RID: 1512
		// (get) Token: 0x06002292 RID: 8850 RVA: 0x000F0610 File Offset: 0x000EE810
		// (set) Token: 0x06002293 RID: 8851 RVA: 0x0000D99E File Offset: 0x0000BB9E
		public override Class411 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005E9 RID: 1513
		// (get) Token: 0x06002294 RID: 8852 RVA: 0x000F0628 File Offset: 0x000EE828
		// (set) Token: 0x06002295 RID: 8853 RVA: 0x0000D9A9 File Offset: 0x0000BBA9
		public override Class411 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005EA RID: 1514
		// (get) Token: 0x06002296 RID: 8854 RVA: 0x000F0640 File Offset: 0x000EE840
		// (set) Token: 0x06002297 RID: 8855 RVA: 0x0000D9B4 File Offset: 0x0000BBB4
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}

		// Token: 0x06002298 RID: 8856 RVA: 0x000F05F8 File Offset: 0x000EE7F8
		public string ToString()
		{
			return base.ToString();
		}
	}
}
