﻿using System;
using ns12;
using ns18;
using ns28;
using ns29;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns20
{
	// Token: 0x02000317 RID: 791
	internal sealed class Class420 : Class415
	{
		// Token: 0x060021FE RID: 8702 RVA: 0x0000D993 File Offset: 0x0000BB93
		public Class420(HToken htoken_1, Class411 class411_2, Class411 class411_3) : base(htoken_1, class411_2, class411_3)
		{
		}

		// Token: 0x060021FF RID: 8703 RVA: 0x000F1514 File Offset: 0x000EF714
		public static Class411 smethod_0(Tokenes tokenes_0)
		{
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_39)
			{
				throw new Exception(tokenes_0.Current.method_0(Class521.smethod_0(102022)));
			}
			tokenes_0.method_1();
			Class411 class411_ = Class413.smethod_0(tokenes_0);
			return new Class420(tokenes_0.Current, class411_, null);
		}

		// Token: 0x06002200 RID: 8704 RVA: 0x000F1570 File Offset: 0x000EF770
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			object result;
			if (obj.GetType() == typeof(double))
			{
				result = 0.0 - (double)obj;
			}
			else
			{
				if (obj.GetType() != typeof(DataArray))
				{
					throw new Exception(this.Token.method_0(Class521.smethod_0(102039)));
				}
				result = 0.0 - (DataArray)obj;
			}
			return result;
		}
	}
}
