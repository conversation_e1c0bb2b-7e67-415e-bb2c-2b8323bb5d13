﻿using System;
using ns26;

namespace ns4
{
	// Token: 0x02000401 RID: 1025
	internal sealed class EventArgs37 : EventArgs
	{
		// Token: 0x170006DB RID: 1755
		// (get) Token: 0x060027D5 RID: 10197 RVA: 0x0000F598 File Offset: 0x0000D798
		public Enum35 Step
		{
			get
			{
				return this.enum35_0;
			}
		}

		// Token: 0x170006DC RID: 1756
		// (get) Token: 0x060027D6 RID: 10198 RVA: 0x0000F5A0 File Offset: 0x0000D7A0
		public bool Failed
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170006DD RID: 1757
		// (get) Token: 0x060027D7 RID: 10199 RVA: 0x0000F5A8 File Offset: 0x0000D7A8
		public string ErrorMessage
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x170006DE RID: 1758
		// (get) Token: 0x060027D8 RID: 10200 RVA: 0x0000F5B0 File Offset: 0x0000D7B0
		public string ReportID
		{
			get
			{
				return this.string_1;
			}
		}

		// Token: 0x060027D9 RID: 10201 RVA: 0x0000F5B8 File Offset: 0x0000D7B8
		internal EventArgs37(Enum35 enum35_1) : this(enum35_1, string.Empty)
		{
		}

		// Token: 0x060027DA RID: 10202 RVA: 0x0000F5C6 File Offset: 0x0000D7C6
		internal EventArgs37(Enum35 enum35_1, string string_2) : this(enum35_1, string_2, string.Empty)
		{
		}

		// Token: 0x060027DB RID: 10203 RVA: 0x0010CAE4 File Offset: 0x0010ACE4
		internal EventArgs37(Enum35 enum35_1, string string_2, string string_3)
		{
			this.enum35_0 = enum35_1;
			this.bool_0 = (string_2 != null && string_2.Length > 0);
			this.string_0 = string_2;
			this.string_1 = string_3;
		}

		// Token: 0x040013C5 RID: 5061
		private Enum35 enum35_0;

		// Token: 0x040013C6 RID: 5062
		private readonly bool bool_0;

		// Token: 0x040013C7 RID: 5063
		private readonly string string_0 = string.Empty;

		// Token: 0x040013C8 RID: 5064
		private readonly string string_1 = string.Empty;
	}
}
