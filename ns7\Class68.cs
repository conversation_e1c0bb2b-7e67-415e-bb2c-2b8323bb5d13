﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using ns18;
using TEx;
using TEx.Trading;

namespace ns7
{
	// Token: 0x020000AB RID: 171
	internal sealed class Class68 : TOdrLine
	{
		// Token: 0x060005D7 RID: 1495 RVA: 0x0002CEA4 File Offset: 0x0002B0A4
		public Class68(ChartCS chartCS_1, CondOrder condOrder_1) : base(chartCS_1, condOrder_1.CondPrice)
		{
			this.condOrder_0 = condOrder_1;
			this.vmethod_1();
			base.method_0();
			Base.UI.ChartThemeChanged += this.method_13;
		}

		// Token: 0x060005D8 RID: 1496 RVA: 0x0002CEF4 File Offset: 0x0002B0F4
		public override void vmethod_1()
		{
			if (this.CondOrder != null)
			{
				if (this.bool_1)
				{
					base.Text = Class521.smethod_0(11605) + base.method_5(base.Price);
				}
				else
				{
					base.Text = this.method_9();
				}
				if (base.TextBox != null)
				{
					base.TextBox.Text = base.Text;
				}
			}
		}

		// Token: 0x060005D9 RID: 1497 RVA: 0x0002CF5C File Offset: 0x0002B15C
		private string method_8()
		{
			string text = this.string_2;
			if (this.CondOrder != null)
			{
				text += Base.Trading.smethod_35(this.CondOrder.OrderType);
				text += this.method_12();
			}
			return text;
		}

		// Token: 0x060005DA RID: 1498 RVA: 0x0002CFA4 File Offset: 0x0002B1A4
		private string method_9()
		{
			string text = this.method_8();
			if (this.CondOrder != null)
			{
				decimal decimal_ = this.method_11();
				text += base.method_5(decimal_);
				if (this.CondOrder.TrailingStopPts != null)
				{
					text += Class521.smethod_0(11658);
				}
			}
			return text;
		}

		// Token: 0x060005DB RID: 1499 RVA: 0x0002D000 File Offset: 0x0002B200
		private string method_10()
		{
			string text = this.method_8();
			if (this.CondOrder != null)
			{
				decimal decimal_ = this.method_11();
				text = string.Concat(new string[]
				{
					text,
					Class521.smethod_0(11679),
					Base.Data.smethod_130(this.CondOrder.ComparisonOpt),
					base.method_5(base.Price),
					Class521.smethod_0(11692),
					base.method_5(decimal_),
					Class521.smethod_0(5046)
				});
				if (this.CondOrder.TrailingStopPts != null)
				{
					text = text.Remove(text.Length - 1, 1) + Class521.smethod_0(11705) + this.CondOrder.TrailingStopPts.Value.ToString() + Class521.smethod_0(5046);
				}
			}
			return text;
		}

		// Token: 0x060005DC RID: 1500 RVA: 0x0002D0EC File Offset: 0x0002B2EC
		private decimal method_11()
		{
			decimal result = -1m;
			if (this.CondOrder != null)
			{
				result = this.CondOrder.ExePrice;
				if (this.CondOrder.TrailingStopPts != null)
				{
					result = this.CondOrder.CondPrice;
				}
			}
			return result;
		}

		// Token: 0x060005DD RID: 1501 RVA: 0x0002D13C File Offset: 0x0002B33C
		private string method_12()
		{
			string result = string.Empty;
			if (this.CondOrder != null)
			{
				result = this.CondOrder.Units.ToString() + (base.Chart.Symbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734));
			}
			return result;
		}

		// Token: 0x060005DE RID: 1502 RVA: 0x0002D1A0 File Offset: 0x0002B3A0
		protected override Color vmethod_7()
		{
			Color result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				result = Color.LightGray;
			}
			else
			{
				result = Color.Gray;
			}
			return result;
		}

		// Token: 0x060005DF RID: 1503 RVA: 0x0002D1CC File Offset: 0x0002B3CC
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Dot;
		}

		// Token: 0x060005E0 RID: 1504 RVA: 0x0002D1E0 File Offset: 0x0002B3E0
		private void method_13(object sender, EventArgs e)
		{
			Color color = this.vmethod_7();
			if (base.Line != null && base.Line.Line != null)
			{
				base.Line.Line.Color = color;
			}
			if (base.TextBox != null && base.TextBox.FontSpec != null)
			{
				base.TextBox.FontSpec.FontColor = color;
			}
		}

		// Token: 0x060005E1 RID: 1505 RVA: 0x0002D244 File Offset: 0x0002B444
		public override void vmethod_4(double double_0)
		{
			if (this.CondOrder != null)
			{
				SymbDataSet symbDataSet = base.Chart.SymbDataSet;
				decimal num = symbDataSet.method_69(double_0);
				decimal? nullable_ = null;
				if (this.CondOrder.TrailingStopPts != null)
				{
					int orderType = (int)this.CondOrder.OrderType;
					decimal d = Convert.ToDecimal(symbDataSet.CurrHisData.Close);
					decimal value = this.CondOrder.TrailingStopPts.Value;
					decimal condPrice = this.CondOrder.CondPrice;
					if (orderType == 1)
					{
						decimal num2 = condPrice + value;
						if (num >= d)
						{
							return;
						}
						nullable_ = new decimal?(num2 - num);
					}
					else
					{
						decimal num2 = condPrice - value;
						if (num <= d)
						{
							return;
						}
						nullable_ = new decimal?(num - num2);
					}
				}
				if (Base.Trading.smethod_93(this.condOrder_0.ID, new decimal?(num), new decimal?(num), nullable_, this.condOrder_0.Units, false))
				{
					base.Price = num;
					base.vmethod_4(Convert.ToDouble(num));
				}
			}
		}

		// Token: 0x060005E2 RID: 1506 RVA: 0x0002D364 File Offset: 0x0002B564
		protected override double vmethod_3()
		{
			return Convert.ToDouble(this.condOrder_0.CondPrice);
		}

		// Token: 0x060005E3 RID: 1507 RVA: 0x00004756 File Offset: 0x00002956
		public override void vmethod_2()
		{
			base.vmethod_2();
			this.vmethod_1();
		}

		// Token: 0x060005E4 RID: 1508 RVA: 0x00004766 File Offset: 0x00002966
		protected override void vmethod_0(bool bool_2)
		{
			Base.UI.ChartThemeChanged -= this.method_13;
			base.vmethod_0(bool_2);
		}

		// Token: 0x17000125 RID: 293
		// (get) Token: 0x060005E5 RID: 1509 RVA: 0x0002D388 File Offset: 0x0002B588
		// (set) Token: 0x060005E6 RID: 1510 RVA: 0x00004782 File Offset: 0x00002982
		public CondOrder CondOrder
		{
			get
			{
				return this.condOrder_0;
			}
			set
			{
				this.condOrder_0 = value;
			}
		}

		// Token: 0x17000126 RID: 294
		// (get) Token: 0x060005E7 RID: 1511 RVA: 0x0002D3A0 File Offset: 0x0002B5A0
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.CondOrder.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.CondOrder.SymbID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000127 RID: 295
		// (get) Token: 0x060005E8 RID: 1512 RVA: 0x0002D3EC File Offset: 0x0002B5EC
		// (set) Token: 0x060005E9 RID: 1513 RVA: 0x0002D440 File Offset: 0x0002B640
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					result = (base.Line.Line.Color != Color.LightGray);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Gray);
				}
				return result;
			}
			set
			{
				Color color;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					color = (value ? Color.White : Color.LightGray);
				}
				else
				{
					color = (value ? Color.DarkGray : Color.Gray);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (this.bool_1)
				{
					this.vmethod_1();
				}
				else if (value)
				{
					base.TextBox.Text = this.method_10();
				}
				else
				{
					base.TextBox.Text = this.method_9();
				}
			}
		}

		// Token: 0x17000128 RID: 296
		// (get) Token: 0x060005EA RID: 1514 RVA: 0x0002D4D8 File Offset: 0x0002B6D8
		// (set) Token: 0x060005EB RID: 1515 RVA: 0x0000478D File Offset: 0x0000298D
		public bool IsInROpenState
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				if (this.bool_1 != value)
				{
					this.bool_1 = value;
					this.vmethod_1();
					base.Chart.ZedGraphControl.Refresh();
				}
			}
		}

		// Token: 0x04000299 RID: 665
		private CondOrder condOrder_0;

		// Token: 0x0400029A RID: 666
		private string string_2 = Class521.smethod_0(1449);

		// Token: 0x0400029B RID: 667
		private bool bool_1;
	}
}
