﻿using System;
using System.Linq;
using System.Windows.Forms;
using ns18;
using ns25;
using ns26;
using ns31;
using TEx;
using TEx.Trading;
using TEx.Util;

namespace ns23
{
	// Token: 0x02000212 RID: 530
	internal sealed class Class293 : Class292
	{
		// Token: 0x060015B2 RID: 5554 RVA: 0x00008B60 File Offset: 0x00006D60
		public Class293()
		{
			base.RowContextMenuStripNeeded += this.Class293_RowContextMenuStripNeeded;
		}

		// Token: 0x060015B3 RID: 5555 RVA: 0x00008B7C File Offset: 0x00006D7C
		public Class293(SortableBindingList<ShownOrder> sortableBindingList_1) : this()
		{
			this.sortableBindingList_0 = sortableBindingList_1;
		}

		// Token: 0x060015B4 RID: 5556 RVA: 0x00008B8D File Offset: 0x00006D8D
		protected override void vmethod_1()
		{
			this.sortableBindingList_0 = Base.Trading.CurrOrdersList;
			base.DataSource = this.sortableBindingList_0;
			this.method_9();
		}

		// Token: 0x060015B5 RID: 5557 RVA: 0x00008ABA File Offset: 0x00006CBA
		public void method_5()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015B6 RID: 5558 RVA: 0x00008BAE File Offset: 0x00006DAE
		public void method_6(SortableBindingList<ShownOrder> sortableBindingList_1)
		{
			this.sortableBindingList_0 = sortableBindingList_1;
			this.method_5();
		}

		// Token: 0x060015B7 RID: 5559 RVA: 0x00094608 File Offset: 0x00092808
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52483);
			toolStripMenuItem.Text = Class521.smethod_0(52504);
			toolStripMenuItem.Click += this.method_7;
			toolStripMenuItem.Paint += this.method_8;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015B8 RID: 5560 RVA: 0x00008BBF File Offset: 0x00006DBF
		private void method_7(object sender, EventArgs e)
		{
			Base.Trading.smethod_74();
			this.method_5();
		}

		// Token: 0x060015B9 RID: 5561 RVA: 0x00008BCE File Offset: 0x00006DCE
		private void method_8(object sender, PaintEventArgs e)
		{
			if (Base.Trading.smethod_37().Any<ShownOrder>())
			{
				(sender as ToolStripMenuItem).Enabled = false;
			}
			else
			{
				(sender as ToolStripMenuItem).Enabled = true;
			}
		}

		// Token: 0x060015BA RID: 5562 RVA: 0x00094684 File Offset: 0x00092884
		public void method_9()
		{
			if (base.Columns.Count > 0)
			{
				base.Columns[0].HeaderText = Class521.smethod_0(51577);
				base.Columns[1].HeaderText = Class521.smethod_0(52521);
				base.Columns[2].HeaderText = Class521.smethod_0(52530);
				base.Columns[3].Visible = false;
				base.Columns[4].Visible = false;
				base.Columns[5].Visible = false;
				base.Columns[6].Visible = false;
				base.Columns[7].Visible = false;
				base.Columns[8].HeaderText = Class521.smethod_0(52539);
				base.Columns[9].HeaderText = Class521.smethod_0(52548);
				base.Columns[10].HeaderText = Class521.smethod_0(52565);
				base.Columns[11].Visible = false;
				base.Columns[12].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						base.Columns[0].Visible = false;
					}
					base.Columns[10].Visible = false;
				}
				else
				{
					base.Columns[0].Visible = true;
					base.Columns[10].Visible = true;
				}
			}
		}

		// Token: 0x060015BB RID: 5563 RVA: 0x0009482C File Offset: 0x00092A2C
		private void Class293_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = Class521.smethod_0(52582);
			toolStripMenuItem.Text = Class521.smethod_0(47035);
			toolStripMenuItem.Click += this.method_10;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = Class521.smethod_0(52599);
			toolStripMenuItem2.Text = Class521.smethod_0(52620);
			toolStripMenuItem2.Click += this.method_11;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Name = Class521.smethod_0(52637);
			toolStripMenuItem3.Text = Class521.smethod_0(52504);
			toolStripMenuItem3.Click += this.method_7;
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			contextMenuStrip.Items.Add(toolStripMenuItem3);
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			ShownOrder shownOrder = dataGridViewRow.DataBoundItem as ShownOrder;
			e.ContextMenuStrip.Items[0].Enabled = true;
			e.ContextMenuStrip.Items[1].Enabled = true;
			e.ContextMenuStrip.Items[2].Enabled = true;
			if (Base.Trading.smethod_37().Count<ShownOrder>() < 1)
			{
				e.ContextMenuStrip.Items[0].Enabled = false;
				e.ContextMenuStrip.Items[1].Enabled = false;
				e.ContextMenuStrip.Items[2].Enabled = false;
			}
			else if (shownOrder.OrderStatus != 0)
			{
				e.ContextMenuStrip.Items[0].Enabled = false;
				e.ContextMenuStrip.Items[1].Enabled = true;
			}
			if (e.ContextMenuStrip.Items[0].Enabled)
			{
				e.ContextMenuStrip.Items[0].Tag = shownOrder.ID;
				e.ContextMenuStrip.Items[1].Tag = shownOrder.ID;
			}
		}

		// Token: 0x060015BC RID: 5564 RVA: 0x00008BF8 File Offset: 0x00006DF8
		private void method_10(object sender, EventArgs e)
		{
			new Form11((int)(sender as ToolStripMenuItem).Tag)
			{
				Owner = Base.UI.MainForm
			}.Show();
		}

		// Token: 0x060015BD RID: 5565 RVA: 0x00094A68 File Offset: 0x00092C68
		private void method_11(object sender, EventArgs e)
		{
			try
			{
				Base.Trading.smethod_73((int)(sender as ToolStripMenuItem).Tag);
				this.method_5();
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x04000B11 RID: 2833
		private SortableBindingList<ShownOrder> sortableBindingList_0;
	}
}
