﻿using System;

namespace TEx
{
	// Token: 0x020001D6 RID: 470
	[Serializable]
	public sealed class DTValLocation
	{
		// Token: 0x06001262 RID: 4706 RVA: 0x00002D25 File Offset: 0x00000F25
		public DTValLocation()
		{
		}

		// Token: 0x06001263 RID: 4707 RVA: 0x00007A9F File Offset: 0x00005C9F
		public DTValLocation(DateTime x1DT, double y1Val, DateTime x2DT, double y2Val)
		{
			this.X1DateTime = x1DT;
			this.Y1Value = y1Val;
			this.X2DateTime = x2DT;
			this.Y2Value = y2Val;
		}

		// Token: 0x170002B8 RID: 696
		// (get) Token: 0x06001264 RID: 4708 RVA: 0x00083318 File Offset: 0x00081518
		// (set) Token: 0x06001265 RID: 4709 RVA: 0x00007AC6 File Offset: 0x00005CC6
		public DateTime X1DateTime
		{
			get
			{
				return this._X1DateTime;
			}
			set
			{
				this._X1DateTime = value;
			}
		}

		// Token: 0x170002B9 RID: 697
		// (get) Token: 0x06001266 RID: 4710 RVA: 0x00083330 File Offset: 0x00081530
		// (set) Token: 0x06001267 RID: 4711 RVA: 0x00007AD1 File Offset: 0x00005CD1
		public double Y1Value
		{
			get
			{
				return this._Y1Value;
			}
			set
			{
				this._Y1Value = value;
			}
		}

		// Token: 0x170002BA RID: 698
		// (get) Token: 0x06001268 RID: 4712 RVA: 0x00083348 File Offset: 0x00081548
		// (set) Token: 0x06001269 RID: 4713 RVA: 0x00007ADC File Offset: 0x00005CDC
		public DateTime X2DateTime
		{
			get
			{
				return this._X2DateTime;
			}
			set
			{
				this._X2DateTime = value;
			}
		}

		// Token: 0x170002BB RID: 699
		// (get) Token: 0x0600126A RID: 4714 RVA: 0x00083360 File Offset: 0x00081560
		// (set) Token: 0x0600126B RID: 4715 RVA: 0x00007AE7 File Offset: 0x00005CE7
		public double Y2Value
		{
			get
			{
				return this._Y2Value;
			}
			set
			{
				this._Y2Value = value;
			}
		}

		// Token: 0x170002BC RID: 700
		// (get) Token: 0x0600126C RID: 4716 RVA: 0x00083378 File Offset: 0x00081578
		public bool IfPtsOverlap
		{
			get
			{
				bool result;
				if (this.X1DateTime == this.X2DateTime)
				{
					result = (this.Y1Value == this.Y2Value);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x0600126D RID: 4717 RVA: 0x000833B0 File Offset: 0x000815B0
		public DTValLocation method_0()
		{
			return new DTValLocation(this._X1DateTime, this._Y1Value, this._X2DateTime, this._Y2Value);
		}

		// Token: 0x04000998 RID: 2456
		private DateTime _X1DateTime;

		// Token: 0x04000999 RID: 2457
		private double _Y1Value;

		// Token: 0x0400099A RID: 2458
		private DateTime _X2DateTime;

		// Token: 0x0400099B RID: 2459
		private double _Y2Value;
	}
}
