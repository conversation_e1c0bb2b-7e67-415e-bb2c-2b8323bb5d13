﻿using System;
using System.Windows.Forms;
using ns5;
using TEx;

namespace ns26
{
	// Token: 0x0200029C RID: 668
	internal sealed class Class57 : Class53
	{
		// Token: 0x06001D9B RID: 7579 RVA: 0x0000C6B0 File Offset: 0x0000A8B0
		public Class57(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class375.zoomout);
		}

		// Token: 0x06001D9C RID: 7580 RVA: 0x0000C6CC File Offset: 0x0000A8CC
		public Class57(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D9D RID: 7581 RVA: 0x0000C6DA File Offset: 0x0000A8DA
		protected override void Class50_MouseEnter(object sender, EventArgs e)
		{
			base.Class50_MouseEnter(sender, e);
			base.method_0(Class375.zoomout_red);
		}

		// Token: 0x06001D9E RID: 7582 RVA: 0x0000C6F1 File Offset: 0x0000A8F1
		protected override void Class50_MouseLeave(object sender, EventArgs e)
		{
			base.Class50_MouseLeave(sender, e);
			base.method_0(Class375.zoomout);
		}

		// Token: 0x06001D9F RID: 7583 RVA: 0x0000C708 File Offset: 0x0000A908
		protected override void Class50_Click(object sender, EventArgs e)
		{
			base.Class50_Click(sender, e);
			((ChtCtrl_KLine)base.ChtCtrl).method_117(true);
		}
	}
}
