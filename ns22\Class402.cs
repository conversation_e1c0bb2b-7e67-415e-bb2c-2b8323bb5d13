﻿using System;
using System.Collections.Generic;
using System.Drawing;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns22
{
	// Token: 0x020002E8 RID: 744
	internal class Class402 : Class383
	{
		// Token: 0x06002103 RID: 8451 RVA: 0x0000D40D File Offset: 0x0000B60D
		public override double vmethod_5(int int_0, HisData hisData_0)
		{
			throw new NotImplementedException();
		}

		// Token: 0x06002104 RID: 8452 RVA: 0x000EAA84 File Offset: 0x000E8C84
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			throw new NotImplementedException();
		}

		// Token: 0x06002105 RID: 8453 RVA: 0x000EAA98 File Offset: 0x000E8C98
		public override void vmethod_1(ZedGraphControl zedGraphControl_1)
		{
			foreach (GraphObj graphObj in this.list_0)
			{
				foreach (GraphObj graphObj2 in zedGraphControl_1.GraphPane.GraphObjList)
				{
					if (graphObj2 == graphObj)
					{
						zedGraphControl_1.GraphPane.GraphObjList.Remove(graphObj2);
						break;
					}
				}
			}
			this.list_0.Clear();
		}

		// Token: 0x06002106 RID: 8454 RVA: 0x0000D5CF File Offset: 0x0000B7CF
		public Class402(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002107 RID: 8455 RVA: 0x0000D5E5 File Offset: 0x0000B7E5
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_1, Color color_1)
		{
			this.zedGraphControl_0 = zedGraphControl_1;
			this.string_0 = string_1;
			this.color_0 = color_1;
			this.vmethod_1(zedGraphControl_1);
		}

		// Token: 0x04001022 RID: 4130
		protected ZedGraphControl zedGraphControl_0;

		// Token: 0x04001023 RID: 4131
		protected List<GraphObj> list_0 = new List<GraphObj>();

		// Token: 0x04001024 RID: 4132
		protected Color color_0;

		// Token: 0x04001025 RID: 4133
		protected string string_0;
	}
}
