﻿using System;
using System.ComponentModel;

namespace TEx
{
	// Token: 0x020001D7 RID: 471
	[Serializable]
	public sealed class DrawSublineParam
	{
		// Token: 0x170002BD RID: 701
		// (get) Token: 0x0600126E RID: 4718 RVA: 0x000833E0 File Offset: 0x000815E0
		// (set) Token: 0x0600126F RID: 4719 RVA: 0x00007AF2 File Offset: 0x00005CF2
		[DisplayName("#")]
		public string Name { get; set; }

		// Token: 0x170002BE RID: 702
		// (get) Token: 0x06001270 RID: 4720 RVA: 0x000833F8 File Offset: 0x000815F8
		// (set) Token: 0x06001271 RID: 4721 RVA: 0x00007AFD File Offset: 0x00005CFD
		[DisplayName("显示")]
		public bool Enabled { get; set; }

		// Token: 0x170002BF RID: 703
		// (get) Token: 0x06001272 RID: 4722 RVA: 0x00083410 File Offset: 0x00081610
		// (set) Token: 0x06001273 RID: 4723 RVA: 0x00007B08 File Offset: 0x00005D08
		[DisplayName("分线参数值")]
		public double Value { get; set; }

		// Token: 0x170002C0 RID: 704
		// (get) Token: 0x06001274 RID: 4724 RVA: 0x00083428 File Offset: 0x00081628
		// (set) Token: 0x06001275 RID: 4725 RVA: 0x00007B13 File Offset: 0x00005D13
		public int DigitNb { get; set; }

		// Token: 0x170002C1 RID: 705
		// (get) Token: 0x06001276 RID: 4726 RVA: 0x00083440 File Offset: 0x00081640
		// (set) Token: 0x06001277 RID: 4727 RVA: 0x00007B1E File Offset: 0x00005D1E
		public double MinValue { get; set; }

		// Token: 0x170002C2 RID: 706
		// (get) Token: 0x06001278 RID: 4728 RVA: 0x00083458 File Offset: 0x00081658
		// (set) Token: 0x06001279 RID: 4729 RVA: 0x00007B29 File Offset: 0x00005D29
		public double? MaxValue { get; set; }
	}
}
