﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns0;
using ns18;
using ns26;
using TEx;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace ns11
{
	// Token: 0x02000359 RID: 857
	internal static class Class466
	{
		// Token: 0x060023CC RID: 9164 RVA: 0x000F9DFC File Offset: 0x000F7FFC
		public static bool smethod_0(CfmmcAcct cfmmcAcct_0)
		{
			return Class466.smethod_1(cfmmcAcct_0, Class466.string_0);
		}

		// Token: 0x060023CD RID: 9165 RVA: 0x000F9E18 File Offset: 0x000F8018
		public static bool smethod_1(CfmmcAcct cfmmcAcct_0, string string_1)
		{
			bool result;
			try
			{
				if (File.Exists(string_1))
				{
					XDocument xdocument = XDocument.Load(string_1);
					XElement root = xdocument.Root;
					IEnumerable<XElement> enumerable = root.Element(Class521.smethod_0(105688)).Elements(Class521.smethod_0(105697));
					bool flag = false;
					foreach (XElement xelement in enumerable)
					{
						if (xelement.Attribute(Class521.smethod_0(12411)).Value == cfmmcAcct_0.ID)
						{
							Class466.smethod_4(xelement, cfmmcAcct_0);
							XElement xelement2 = xelement.Element(Class521.smethod_0(105706));
							if (xelement2 != null)
							{
								xelement2.Elements().Remove<XElement>();
								xelement2.Remove();
							}
							Class466.smethod_6(xelement, cfmmcAcct_0.BindingAccts);
							flag = true;
							break;
						}
					}
					if (!flag)
					{
						XElement content = Class466.smethod_3(cfmmcAcct_0);
						root.Element(Class521.smethod_0(105688)).Add(content);
					}
					xdocument.Save(string_1);
				}
				else
				{
					Class466.smethod_2(cfmmcAcct_0).Save(string_1);
				}
				goto IL_127;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
				result = false;
			}
			return result;
			IL_127:
			return true;
		}

		// Token: 0x060023CE RID: 9166 RVA: 0x000F9F88 File Offset: 0x000F8188
		private static XDocument smethod_2(CfmmcAcct cfmmcAcct_0)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement(Class521.smethod_0(105723));
			xdocument.Add(xelement);
			XElement xelement2 = new XElement(Class521.smethod_0(105688));
			xelement.Add(xelement2);
			XElement content = Class466.smethod_3(cfmmcAcct_0);
			xelement2.Add(content);
			return xdocument;
		}

		// Token: 0x060023CF RID: 9167 RVA: 0x000F9FE4 File Offset: 0x000F81E4
		private static XElement smethod_3(CfmmcAcct cfmmcAcct_0)
		{
			XElement xelement = new XElement(Class521.smethod_0(105697));
			Class466.smethod_4(xelement, cfmmcAcct_0);
			Class466.smethod_6(xelement, cfmmcAcct_0.BindingAccts);
			return xelement;
		}

		// Token: 0x060023D0 RID: 9168 RVA: 0x000FA01C File Offset: 0x000F821C
		private static void smethod_4(XElement xelement_0, CfmmcAcct cfmmcAcct_0)
		{
			xelement_0.SetAttributeValue(Class521.smethod_0(12411), cfmmcAcct_0.ID);
			xelement_0.SetAttributeValue(Class521.smethod_0(105732), Utility.Encrypt(cfmmcAcct_0.Password, TApp.string_12));
			if (cfmmcAcct_0.BeginDate != null)
			{
				xelement_0.SetAttributeValue(Class521.smethod_0(36152), cfmmcAcct_0.BeginDate.Value.ToString(Class521.smethod_0(1702)));
			}
			if (cfmmcAcct_0.EndDate != null)
			{
				xelement_0.SetAttributeValue(Class521.smethod_0(36165), cfmmcAcct_0.EndDate.Value.ToString(Class521.smethod_0(1702)));
			}
			if (cfmmcAcct_0.LastDownloadTime != null)
			{
				xelement_0.SetAttributeValue(Class521.smethod_0(105745), cfmmcAcct_0.LastDownloadTime.Value.ToString(Class521.smethod_0(105762)));
			}
			if (!string.IsNullOrEmpty(cfmmcAcct_0.Note))
			{
				xelement_0.SetAttributeValue(Class521.smethod_0(59412), cfmmcAcct_0.Note);
			}
			else
			{
				Class466.smethod_5(xelement_0, Class521.smethod_0(59412));
			}
		}

		// Token: 0x060023D1 RID: 9169 RVA: 0x000FA174 File Offset: 0x000F8374
		private static void smethod_5(XElement xelement_0, string string_1)
		{
			XAttribute xattribute = xelement_0.Attribute(string_1);
			if (xattribute != null)
			{
				xattribute.Remove();
			}
		}

		// Token: 0x060023D2 RID: 9170 RVA: 0x000FA19C File Offset: 0x000F839C
		private static void smethod_6(XElement xelement_0, List<BindingAcct> list_0)
		{
			if (list_0 != null && list_0.Any<BindingAcct>())
			{
				XElement xelement = new XElement(Class521.smethod_0(105706));
				xelement_0.Add(xelement);
				for (int i = 0; i < list_0.Count; i++)
				{
					XElement xelement2 = new XElement(Class521.smethod_0(105791));
					xelement2.SetAttributeValue(Class521.smethod_0(12411), list_0[i].Id);
					xelement2.SetAttributeValue(Class521.smethod_0(9250), list_0[i].UsrName);
					if (list_0[i].BeginDate != null)
					{
						xelement2.SetAttributeValue(Class521.smethod_0(36152), list_0[i].BeginDate.Value.ToString(Class521.smethod_0(1702)));
					}
					if (list_0[i].EndDate != null)
					{
						xelement2.SetAttributeValue(Class521.smethod_0(36165), list_0[i].EndDate.Value.ToString(Class521.smethod_0(1702)));
					}
					xelement.Add(xelement2);
				}
			}
		}

		// Token: 0x060023D3 RID: 9171 RVA: 0x000FA2F8 File Offset: 0x000F84F8
		public static List<CfmmcAcct> smethod_7(CfmmcAcct cfmmcAcct_0)
		{
			return Class466.smethod_8(cfmmcAcct_0, Class466.string_0);
		}

		// Token: 0x060023D4 RID: 9172 RVA: 0x000FA314 File Offset: 0x000F8514
		public static List<CfmmcAcct> smethod_8(CfmmcAcct cfmmcAcct_0, string string_1)
		{
			List<CfmmcAcct> result;
			if (!File.Exists(string_1))
			{
				result = null;
			}
			else
			{
				try
				{
					XDocument xdocument = XDocument.Load(string_1);
					foreach (XElement xelement in xdocument.Root.Element(Class521.smethod_0(105688)).Elements(Class521.smethod_0(105697)))
					{
						if (xelement.Attribute(Class521.smethod_0(12411)).Value == cfmmcAcct_0.ID)
						{
							xelement.Remove();
							break;
						}
					}
					xdocument.Save(string_1);
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
				result = Class466.smethod_10(string_1);
			}
			return result;
		}

		// Token: 0x060023D5 RID: 9173 RVA: 0x000FA3F0 File Offset: 0x000F85F0
		public static List<CfmmcAcct> smethod_9()
		{
			return Class466.smethod_10(Class466.string_0);
		}

		// Token: 0x060023D6 RID: 9174 RVA: 0x000FA40C File Offset: 0x000F860C
		public static List<CfmmcAcct> smethod_10(string string_1)
		{
			List<CfmmcAcct> list = new List<CfmmcAcct>();
			List<CfmmcAcct> result;
			if (!File.Exists(string_1))
			{
				result = list;
			}
			else
			{
				List<CfmmcAcct> result2;
				try
				{
					foreach (XElement xelement in XDocument.Load(string_1).Root.Element(Class521.smethod_0(105688)).Elements(Class521.smethod_0(105697)))
					{
						string value = xelement.Attribute(Class521.smethod_0(12411)).Value;
						string password = Utility.Decrypt(xelement.Attribute(Class521.smethod_0(105732)).Value, TApp.string_12);
						DateTime? begDT = null;
						DateTime? endDT = null;
						XAttribute xattribute = xelement.Attribute(Class521.smethod_0(36152));
						if (xattribute != null)
						{
							begDT = new DateTime?(Convert.ToDateTime(xattribute.Value));
						}
						XAttribute xattribute2 = xelement.Attribute(Class521.smethod_0(36165));
						if (xattribute2 != null)
						{
							endDT = new DateTime?(Convert.ToDateTime(xattribute2.Value));
						}
						DateTime? lastDnldTime = null;
						XAttribute xattribute3 = xelement.Attribute(Class521.smethod_0(105745));
						if (xattribute3 != null)
						{
							try
							{
								lastDnldTime = new DateTime?(Convert.ToDateTime(xattribute3.Value));
							}
							catch (Exception exception_)
							{
								Class184.smethod_0(exception_);
							}
						}
						string note = string.Empty;
						XAttribute xattribute4 = xelement.Attribute(Class521.smethod_0(59412));
						if (xattribute4 != null)
						{
							note = xattribute4.Value;
						}
						CfmmcAcct cfmmcAcct = new CfmmcAcct(value, password, begDT, endDT, lastDnldTime, note);
						list.Add(cfmmcAcct);
						List<BindingAcct> list2 = new List<BindingAcct>();
						cfmmcAcct.BindingAccts = list2;
						XElement xelement2 = xelement.Element(Class521.smethod_0(105706));
						if (xelement2 != null)
						{
							foreach (XElement xelement3 in xelement2.Elements())
							{
								BindingAcct bindingAcct = new BindingAcct();
								bindingAcct.UsrName = xelement3.Attribute(Class521.smethod_0(9250)).Value;
								bindingAcct.Id = Convert.ToInt32(xelement3.Attribute(Class521.smethod_0(12411)).Value);
								XAttribute xattribute5 = xelement3.Attribute(Class521.smethod_0(36152));
								if (xattribute5 != null)
								{
									try
									{
										bindingAcct.BeginDate = new DateTime?(Convert.ToDateTime(xattribute5.Value));
										goto IL_29C;
									}
									catch (Exception exception_2)
									{
										Class184.smethod_0(exception_2);
										goto IL_29C;
									}
									goto Block_17;
								}
								goto IL_29C;
								IL_28E:
								list2.Add(bindingAcct);
								continue;
								Block_17:
								XAttribute xattribute6;
								try
								{
									IL_26D:
									bindingAcct.EndDate = new DateTime?(Convert.ToDateTime(xattribute6.Value));
								}
								catch (Exception exception_3)
								{
									Class184.smethod_0(exception_3);
								}
								goto IL_28E;
								IL_29C:
								xattribute6 = xelement3.Attribute(Class521.smethod_0(36165));
								if (xattribute6 != null)
								{
									goto IL_26D;
								}
								goto IL_28E;
							}
						}
					}
					goto IL_2F1;
				}
				catch (Exception exception_4)
				{
					Class184.smethod_0(exception_4);
					result2 = list;
				}
				return result2;
				IL_2F1:
				result = list;
			}
			return result;
		}

		// Token: 0x060023D7 RID: 9175 RVA: 0x000FA7A8 File Offset: 0x000F89A8
		public static CfmmcAcct smethod_11(string string_1)
		{
			Class466.Class467 @class = new Class466.Class467();
			@class.string_0 = string_1;
			CfmmcAcct result;
			if (!File.Exists(Class466.string_0))
			{
				result = null;
			}
			else
			{
				CfmmcAcct cfmmcAcct = null;
				List<CfmmcAcct> list = Class466.smethod_9();
				if (list != null && list.Any<CfmmcAcct>())
				{
					IEnumerable<CfmmcAcct> source = list.Where(new Func<CfmmcAcct, bool>(@class.method_0));
					if (source.Any<CfmmcAcct>())
					{
						cfmmcAcct = source.First<CfmmcAcct>();
					}
				}
				result = cfmmcAcct;
			}
			return result;
		}

		// Token: 0x060023D8 RID: 9176 RVA: 0x000FA810 File Offset: 0x000F8A10
		public static List<string> smethod_12()
		{
			return Class466.smethod_13(Class466.string_0);
		}

		// Token: 0x060023D9 RID: 9177 RVA: 0x000FA82C File Offset: 0x000F8A2C
		public static List<string> smethod_13(string string_1)
		{
			List<string> list = new List<string>();
			if (File.Exists(string_1))
			{
				try
				{
					foreach (XElement xelement in XDocument.Load(string_1).Root.Element(Class521.smethod_0(105688)).Elements(Class521.smethod_0(105697)))
					{
						string value = xelement.Attribute(Class521.smethod_0(12411)).Value;
						string value2 = xelement.Attribute(Class521.smethod_0(105732)).Value;
						string item = value + Class521.smethod_0(3636) + value2;
						list.Add(item);
					}
				}
				catch
				{
				}
			}
			return list;
		}

		// Token: 0x060023DA RID: 9178 RVA: 0x000FA918 File Offset: 0x000F8B18
		public static void smethod_14(List<List<string>> list_0, string string_1, string string_2)
		{
			FileInfo fileInfo = new FileInfo(string_1);
			if (fileInfo.Exists)
			{
				XDocument xdocument = XDocument.Load(string_1);
				foreach (XElement xelement in xdocument.Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(105808)))
				{
					if (xelement.Attribute(Class521.smethod_0(105821)).Value == string_2)
					{
						bool flag = false;
						XElement xelement2 = xelement.Elements().Last<XElement>();
						string text = xelement2.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString()).Value + Class521.smethod_0(3636) + xelement2.Attribute(CfmmcRecFieldsEnum.成交时间.ToString()).Value;
						int i = 0;
						while (i < list_0.Count)
						{
							if (list_0[i].Count != Convert.ToInt32(CfmmcRecFieldsEnum.实际成交日期) + 1)
							{
								throw new Exception(string.Format(Class521.smethod_0(105826), i));
							}
							string text2 = list_0[i][12] + Class521.smethod_0(3636) + list_0[i][2];
							try
							{
								if (Convert.ToDateTime(text2) <= Convert.ToDateTime(text))
								{
									goto IL_1D2;
								}
							}
							catch (Exception)
							{
								throw new Exception(string.Format(Class521.smethod_0(105875), text2, text));
							}
							goto IL_173;
							IL_1D2:
							i++;
							continue;
							IL_173:
							flag = true;
							List<string> list = list_0[i];
							XElement xelement3 = new XElement(Class521.smethod_0(105912));
							for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
							{
								xelement3.SetAttributeValue(cfmmcRecFieldsEnum.ToString(), list[(int)cfmmcRecFieldsEnum]);
							}
							xelement.Add(xelement3);
							goto IL_1D2;
						}
						if (flag)
						{
							xdocument.Save(string_1);
						}
						return;
					}
				}
				XElement xelement4 = new XElement(Class521.smethod_0(105808));
				xelement4.SetAttributeValue(Class521.smethod_0(105821), string_2);
				for (int j = 0; j < list_0.Count; j++)
				{
					List<string> list2 = list_0[j];
					XElement xelement5 = new XElement(Class521.smethod_0(105912));
					for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum2 = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum2 <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum2++)
					{
						xelement5.SetAttributeValue(cfmmcRecFieldsEnum2.ToString(), list2[(int)cfmmcRecFieldsEnum2]);
					}
					xelement4.Add(xelement5);
				}
				xdocument.Element(Class521.smethod_0(105723)).Add(xelement4);
				xdocument.Save(string_1);
			}
			else
			{
				XDocument xdocument2 = new XDocument();
				XElement xelement6 = new XElement(Class521.smethod_0(105723));
				xdocument2.Add(xelement6);
				XElement xelement7 = new XElement(Class521.smethod_0(105808));
				xelement7.SetAttributeValue(Class521.smethod_0(105821), string_2);
				xelement6.Add(xelement7);
				for (int k = 0; k < list_0.Count; k++)
				{
					XElement xelement8 = new XElement(Class521.smethod_0(105912));
					xelement7.Add(xelement8);
					List<string> list3 = list_0[k];
					for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum3 = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum3 <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum3++)
					{
						xelement8.SetAttributeValue(cfmmcRecFieldsEnum3.ToString(), list3[(int)cfmmcRecFieldsEnum3]);
					}
				}
				if (!fileInfo.Directory.Exists)
				{
					fileInfo.Directory.Create();
				}
				xdocument2.Save(string_1);
			}
		}

		// Token: 0x060023DB RID: 9179 RVA: 0x000FAD28 File Offset: 0x000F8F28
		public static List<List<string>> smethod_15(string string_1)
		{
			return Class466.smethod_16(string_1, null, null);
		}

		// Token: 0x060023DC RID: 9180 RVA: 0x000FAD54 File Offset: 0x000F8F54
		public static List<List<string>> smethod_16(string string_1, DateTime? nullable_0, DateTime? nullable_1)
		{
			Class466.Class468 @class = new Class466.Class468();
			@class.string_0 = string_1;
			List<List<string>> result;
			try
			{
				string text = Path.Combine(TApp.UserAcctFolder, CfmmcRecImporter.string_0);
				if (File.Exists(text))
				{
					IEnumerable<XElement> source = XDocument.Load(text).Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(105808)).Where(new Func<XElement, bool>(@class.method_0));
					if (!source.Any<XElement>())
					{
						result = null;
						goto IL_1CC;
					}
					List<List<string>> list = new List<List<string>>();
					foreach (XElement xelement in source.Elements(Class521.smethod_0(105912)))
					{
						if (nullable_0 != null)
						{
							bool flag = false;
							XAttribute xattribute = xelement.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString());
							if (nullable_0 != null)
							{
								if (Convert.ToDateTime(xattribute.Value) > nullable_0)
								{
									flag = true;
								}
							}
							else
							{
								flag = true;
							}
							if (flag && nullable_1 != null && Convert.ToDateTime(xattribute.Value) > nullable_1)
							{
								flag = false;
							}
							if (!flag)
							{
								continue;
							}
						}
						List<string> list2 = new List<string>();
						for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
						{
							XAttribute xattribute2 = xelement.Attribute(cfmmcRecFieldsEnum.ToString());
							list2.Add(xattribute2.Value);
						}
						list.Add(list2);
					}
					result = list;
					goto IL_1CC;
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return null;
			IL_1CC:
			return result;
		}

		// Token: 0x060023DD RID: 9181 RVA: 0x000FAF6C File Offset: 0x000F916C
		[Obsolete]
		public static List<List<string>> smethod_17(string string_1, string string_2, DateTime? nullable_0, DateTime? nullable_1)
		{
			List<List<string>> list = new List<List<string>>();
			List<List<string>> result;
			try
			{
				if (File.Exists(string_2))
				{
					foreach (XElement xelement in XDocument.Load(string_2).Element(Class521.smethod_0(105723)).Elements(Class521.smethod_0(105808)))
					{
						if (xelement.Attribute(Class521.smethod_0(105821)).Value == string_1)
						{
							foreach (XElement xelement2 in xelement.Elements(Class521.smethod_0(105912)))
							{
								bool flag = false;
								XAttribute xattribute = xelement2.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString());
								if (nullable_0 != null)
								{
									if (Convert.ToDateTime(xattribute.Value) > nullable_0)
									{
										flag = true;
									}
								}
								else
								{
									flag = true;
								}
								if (flag && nullable_1 != null && Convert.ToDateTime(xattribute.Value) > nullable_1)
								{
									flag = false;
								}
								if (flag)
								{
									List<string> list2 = new List<string>();
									for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
									{
										XAttribute xattribute2 = xelement2.Attribute(cfmmcRecFieldsEnum.ToString());
										list2.Add(xattribute2.Value);
									}
									list.Add(list2);
								}
							}
							break;
						}
					}
				}
				result = list;
			}
			catch
			{
				result = list;
			}
			return result;
		}

		// Token: 0x060023DE RID: 9182 RVA: 0x000FB18C File Offset: 0x000F938C
		public static string smethod_18(string string_1)
		{
			return XDocument.Parse(string_1).Element(Class521.smethod_0(59486)).Element(TransData.string_0).Attribute(Class521.smethod_0(12411)).Value;
		}

		// Token: 0x060023DF RID: 9183 RVA: 0x000FB1E0 File Offset: 0x000F93E0
		public static string smethod_19(string string_1)
		{
			return XDocument.Parse(string_1).Element(Class521.smethod_0(59486)).Element(TransData.string_0).Attribute(Class521.smethod_0(104697)).Value;
		}

		// Token: 0x060023E0 RID: 9184 RVA: 0x000FB234 File Offset: 0x000F9434
		public static DateTime? smethod_20(string string_1)
		{
			return Class466.smethod_21(Class466.smethod_11(string_1));
		}

		// Token: 0x060023E1 RID: 9185 RVA: 0x000FB250 File Offset: 0x000F9450
		public static DateTime? smethod_21(CfmmcAcct cfmmcAcct_0)
		{
			DateTime? result;
			if (cfmmcAcct_0 == null)
			{
				result = null;
			}
			else if (cfmmcAcct_0.LastDownloadTime == null)
			{
				result = null;
			}
			else
			{
				result = cfmmcAcct_0.LastDownloadTime;
			}
			return result;
		}

		// Token: 0x060023E2 RID: 9186 RVA: 0x000FB294 File Offset: 0x000F9494
		public static DateTime? smethod_22(string string_1)
		{
			return Class466.smethod_23(Class466.smethod_11(string_1));
		}

		// Token: 0x060023E3 RID: 9187 RVA: 0x000FB2B0 File Offset: 0x000F94B0
		public static DateTime? smethod_23(CfmmcAcct cfmmcAcct_0)
		{
			DateTime? result;
			if (cfmmcAcct_0 == null)
			{
				result = null;
			}
			else if (cfmmcAcct_0.EndDate == null)
			{
				result = null;
			}
			else
			{
				result = cfmmcAcct_0.EndDate;
			}
			return result;
		}

		// Token: 0x060023E4 RID: 9188 RVA: 0x000FB2F4 File Offset: 0x000F94F4
		public static void smethod_24(CfmmcAcct cfmmcAcct_0)
		{
			if (cfmmcAcct_0 != null)
			{
				List<List<string>> list = Class466.smethod_15(cfmmcAcct_0.ID);
				if (list != null && list.Any<List<string>>())
				{
					Class451 @class = new Class451(TransData.string_0, cfmmcAcct_0.ID);
					for (int i = 0; i < list.Count; i++)
					{
						TransData transData = new TransData();
						transData.method_3(list[i], cfmmcAcct_0.ID);
						@class.Data.Add(transData);
					}
					for (int j = 0; j < cfmmcAcct_0.BindingAccts.Count; j++)
					{
						Class466.smethod_26(@class, cfmmcAcct_0.BindingAccts[j].Id, cfmmcAcct_0.ID);
					}
				}
				else if (cfmmcAcct_0.EndDate != null || cfmmcAcct_0.LastDownloadTime != null)
				{
					cfmmcAcct_0.EndDate = null;
					cfmmcAcct_0.LastDownloadTime = null;
					Class466.smethod_0(cfmmcAcct_0);
				}
			}
		}

		// Token: 0x060023E5 RID: 9189 RVA: 0x000FB3F4 File Offset: 0x000F95F4
		public static void smethod_25(CfmmcAcct cfmmcAcct_0, int int_0)
		{
			if (cfmmcAcct_0 != null)
			{
				List<List<string>> list = Class466.smethod_15(cfmmcAcct_0.ID);
				Class451 @class = new Class451(TransData.string_0, cfmmcAcct_0.ID);
				for (int i = 0; i < list.Count; i++)
				{
					TransData transData = new TransData();
					transData.method_3(list[i], cfmmcAcct_0.ID);
					@class.Data.Add(transData);
				}
				Class466.smethod_26(@class, int_0, cfmmcAcct_0.ID);
			}
		}

		// Token: 0x060023E6 RID: 9190 RVA: 0x000FB468 File Offset: 0x000F9668
		public static void smethod_26(Class451 class451_0, int int_0, string string_1)
		{
			List<Transaction> list = Base.Trading.smethod_124(int_0);
			if (TransFileImporter.smethod_3(list, class451_0, int_0))
			{
				Base.Trading.smethod_128(int_0, list);
				Base.Trading.smethod_17();
			}
		}

		// Token: 0x04001145 RID: 4421
		private static readonly string string_0 = TApp.UserAcctFolder + Class521.smethod_0(105921);

		// Token: 0x0200035A RID: 858
		[CompilerGenerated]
		private sealed class Class467
		{
			// Token: 0x060023E9 RID: 9193 RVA: 0x000FB494 File Offset: 0x000F9694
			internal bool method_0(CfmmcAcct cfmmcAcct_0)
			{
				return cfmmcAcct_0.ID.Equals(this.string_0);
			}

			// Token: 0x04001146 RID: 4422
			public string string_0;
		}

		// Token: 0x0200035B RID: 859
		[CompilerGenerated]
		private sealed class Class468
		{
			// Token: 0x060023EB RID: 9195 RVA: 0x000FB4B8 File Offset: 0x000F96B8
			internal bool method_0(XElement xelement_0)
			{
				return xelement_0.Attribute(Class521.smethod_0(105821)).Value == this.string_0;
			}

			// Token: 0x04001147 RID: 4423
			public string string_0;
		}
	}
}
