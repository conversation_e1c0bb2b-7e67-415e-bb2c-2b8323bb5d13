﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using ns18;
using ns24;
using TEx;
using TEx.Trading;

namespace ns28
{
	// Token: 0x020000AF RID: 175
	internal sealed class Class70 : TOdrLine
	{
		// Token: 0x0600073E RID: 1854 RVA: 0x00004F47 File Offset: 0x00003147
		public Class70(ChartCS chartCS_1, Transaction transaction_1) : base(chartCS_1, transaction_1.Price)
		{
			this.OpenTrans = transaction_1;
			this.vmethod_1();
			base.method_0();
		}

		// Token: 0x0600073F RID: 1855 RVA: 0x00004F6B File Offset: 0x0000316B
		public override void vmethod_1()
		{
			base.Text = this.method_9();
			if (base.TextBox != null)
			{
				base.TextBox.Text = base.Text;
			}
		}

		// Token: 0x06000740 RID: 1856 RVA: 0x0002F17C File Offset: 0x0002D37C
		private string method_8()
		{
			return Base.Trading.smethod_149((Enum17)this.OpenTrans.TransType).Replace(Class521.smethod_0(11757), Class521.smethod_0(11762)) + this.OpenTrans.OpenUnits.ToString() + (base.Chart.Symbol.IsFutures ? Class521.smethod_0(11739) : Class521.smethod_0(11734));
		}

		// Token: 0x06000741 RID: 1857 RVA: 0x0002F1FC File Offset: 0x0002D3FC
		private string method_9()
		{
			return this.method_8() + base.method_5(this.OpenTrans.Price);
		}

		// Token: 0x06000742 RID: 1858 RVA: 0x0002F22C File Offset: 0x0002D42C
		private string method_10()
		{
			return string.Concat(new string[]
			{
				this.method_8(),
				Class521.smethod_0(11767),
				base.method_5(this.OpenTrans.Price),
				Class521.smethod_0(11776),
				Base.Trading.smethod_154(this.OpenTrans).ToString(),
				Class521.smethod_0(5046)
			});
		}

		// Token: 0x06000743 RID: 1859 RVA: 0x0002F2A4 File Offset: 0x0002D4A4
		protected override double vmethod_3()
		{
			double result;
			if (this.OpenTrans is TranStock)
			{
				TranStock tranStock_ = this.OpenTrans as TranStock;
				result = base.Chart.method_220(tranStock_);
			}
			else
			{
				result = Convert.ToDouble(this.OpenTrans.Price);
			}
			return result;
		}

		// Token: 0x06000744 RID: 1860 RVA: 0x0002F2F0 File Offset: 0x0002D4F0
		protected override Color vmethod_7()
		{
			Color result;
			if (this.OpenTrans.TransType == 1)
			{
				result = Color.Red;
			}
			else if (this.OpenTrans.TransType == 3)
			{
				result = Color.Green;
			}
			else
			{
				result = default(Color);
			}
			return result;
		}

		// Token: 0x06000745 RID: 1861 RVA: 0x0002F338 File Offset: 0x0002D538
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Solid;
		}

		// Token: 0x170001B0 RID: 432
		// (get) Token: 0x06000746 RID: 1862 RVA: 0x0002F34C File Offset: 0x0002D54C
		// (set) Token: 0x06000747 RID: 1863 RVA: 0x00004F94 File Offset: 0x00003194
		public Transaction OpenTrans
		{
			get
			{
				return this.transaction_0;
			}
			set
			{
				this.transaction_0 = value;
			}
		}

		// Token: 0x170001B1 RID: 433
		// (get) Token: 0x06000748 RID: 1864 RVA: 0x0002F364 File Offset: 0x0002D564
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.OpenTrans.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.OpenTrans.SymbolID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170001B2 RID: 434
		// (get) Token: 0x06000749 RID: 1865 RVA: 0x0002F3B0 File Offset: 0x0002D5B0
		// (set) Token: 0x0600074A RID: 1866 RVA: 0x0002F408 File Offset: 0x0002D608
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (this.OpenTrans.TransType == 1)
				{
					result = (base.Line.Line.Color != Color.Red);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Green);
				}
				return result;
			}
			set
			{
				Color color;
				if (this.OpenTrans.TransType == 1)
				{
					color = (value ? Color.Coral : Color.Red);
				}
				else
				{
					color = (value ? Color.LimeGreen : Color.Green);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (value)
				{
					base.TextBox.Text = this.method_10();
				}
				else
				{
					base.TextBox.Text = this.method_9();
				}
			}
		}

		// Token: 0x0400032B RID: 811
		private Transaction transaction_0;
	}
}
