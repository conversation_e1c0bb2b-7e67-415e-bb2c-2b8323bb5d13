﻿using System;

namespace ns28
{
	// Token: 0x0200027D RID: 637
	internal sealed class EventArgs26 : EventArgs
	{
		// Token: 0x06001B9C RID: 7068 RVA: 0x0000B70E File Offset: 0x0000990E
		public EventArgs26(string string_2, string string_3, bool bool_1)
		{
			this.string_0 = string_2;
			this.string_1 = string_3;
			this.bool_0 = bool_1;
		}

		// Token: 0x1700048C RID: 1164
		// (get) Token: 0x06001B9D RID: 7069 RVA: 0x000BFFB4 File Offset: 0x000BE1B4
		public string UsrId
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x1700048D RID: 1165
		// (get) Token: 0x06001B9E RID: 7070 RVA: 0x000BFFCC File Offset: 0x000BE1CC
		public string Password
		{
			get
			{
				return this.string_1;
			}
		}

		// Token: 0x1700048E RID: 1166
		// (get) Token: 0x06001B9F RID: 7071 RVA: 0x000BFFE4 File Offset: 0x000BE1E4
		public bool Passed
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x04000DA8 RID: 3496
		private readonly string string_0;

		// Token: 0x04000DA9 RID: 3497
		private readonly string string_1;

		// Token: 0x04000DAA RID: 3498
		private readonly bool bool_0;
	}
}
