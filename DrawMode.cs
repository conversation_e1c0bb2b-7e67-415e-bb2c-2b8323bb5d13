﻿using System;

namespace TEx
{
	// Token: 0x02000148 RID: 328
	public enum DrawMode
	{
		// Token: 0x040005DA RID: 1498
		Text,
		// Token: 0x040005DB RID: 1499
		ArwLine,
		// Token: 0x040005DC RID: 1500
		Line,
		// Token: 0x040005DD RID: 1501
		LineD,
		// Token: 0x040005DE RID: 1502
		LineV,
		// Token: 0x040005DF RID: 1503
		LineH,
		// Token: 0x040005E0 RID: 1504
		LineP,
		// Token: 0x040005E1 RID: 1505
		Square,
		// Token: 0x040005E2 RID: 1506
		Ellipse,
		// Token: 0x040005E3 RID: 1507
		Degree45Up,
		// Token: 0x040005E4 RID: 1508
		Degree45Dn,
		// Token: 0x040005E5 RID: 1509
		TrendSpeed,
		// Token: 0x040005E6 RID: 1510
		GannFan,
		// Token: 0x040005E7 RID: 1511
		PeriodLines,
		// Token: 0x040005E8 RID: 1512
		FibonacciLines,
		// Token: 0x040005E9 RID: 1513
		GoldenRatio,
		// Token: 0x040005EA RID: 1514
		Ratio,
		// Token: 0x040005EB RID: 1515
		Range,
		// Token: 0x040005EC RID: 1516
		FibonacciExtLines,
		// Token: 0x040005ED RID: 1517
		GannLines,
		// Token: 0x040005EE RID: 1518
		MeasureObj,
		// Token: 0x040005EF RID: 1519
		LineDExt,
		// Token: 0x040005F0 RID: 1520
		LineDH,
		// Token: 0x040005F1 RID: 1521
		LineHExt,
		// Token: 0x040005F2 RID: 1522
		RedArwUp,
		// Token: 0x040005F3 RID: 1523
		RedArwLUp,
		// Token: 0x040005F4 RID: 1524
		RedArwRUp,
		// Token: 0x040005F5 RID: 1525
		GrnArwDn,
		// Token: 0x040005F6 RID: 1526
		GrnArwLDn,
		// Token: 0x040005F7 RID: 1527
		GrnArwRDn,
		// Token: 0x040005F8 RID: 1528
		EraseAllDrawObj,
		// Token: 0x040005F9 RID: 1529
		OpenLongOdr,
		// Token: 0x040005FA RID: 1530
		OpenShrtOdr,
		// Token: 0x040005FB RID: 1531
		StopLimitOdr,
		// Token: 0x040005FC RID: 1532
		PrftTakeOdr,
		// Token: 0x040005FD RID: 1533
		RevTransOdr,
		// Token: 0x040005FE RID: 1534
		ROpenOdr,
		// Token: 0x040005FF RID: 1535
		EraseAllDrawOdr,
		// Token: 0x04000600 RID: 1536
		Off
	}
}
