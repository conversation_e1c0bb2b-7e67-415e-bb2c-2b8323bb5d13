﻿using System;
using System.Windows.Forms;
using ns26;
using ns5;
using TEx;

namespace ns9
{
	// Token: 0x0200029A RID: 666
	internal sealed class Class55 : Class53
	{
		// Token: 0x06001D91 RID: 7569 RVA: 0x0000C5E3 File Offset: 0x0000A7E3
		public Class55(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class375.fast_forward);
		}

		// Token: 0x06001D92 RID: 7570 RVA: 0x0000C5FF File Offset: 0x0000A7FF
		public Class55(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D93 RID: 7571 RVA: 0x0000C60D File Offset: 0x0000A80D
		protected override void Class50_MouseEnter(object sender, EventArgs e)
		{
			base.Class50_MouseEnter(sender, e);
			base.method_0(Class375.fast_forward_red);
		}

		// Token: 0x06001D94 RID: 7572 RVA: 0x0000C624 File Offset: 0x0000A824
		protected override void Class50_MouseLeave(object sender, EventArgs e)
		{
			base.Class50_MouseLeave(sender, e);
			base.method_0(Class375.fast_forward);
		}

		// Token: 0x06001D95 RID: 7573 RVA: 0x000D022C File Offset: 0x000CE42C
		protected override void Class50_Click(object sender, EventArgs e)
		{
			base.Class50_Click(sender, e);
			ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)base.ChtCtrl;
			if (chtCtrl_KLine.IsInCrossReviewMode)
			{
				chtCtrl_KLine.IsInCrossReviewMode = false;
			}
			Base.UI.smethod_117();
		}
	}
}
