﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns18;
using ns23;
using ns6;
using TEx;
using TEx.Trading;

namespace ns20
{
	// Token: 0x02000246 RID: 582
	internal sealed partial class Form20 : Form
	{
		// Token: 0x1400008F RID: 143
		// (add) Token: 0x060018C6 RID: 6342 RVA: 0x000AA6EC File Offset: 0x000A88EC
		// (remove) Token: 0x060018C7 RID: 6343 RVA: 0x000AA724 File Offset: 0x000A8924
		public event Delegate17 NewAcctCreated
		{
			[CompilerGenerated]
			add
			{
				Delegate17 @delegate = this.delegate17_0;
				Delegate17 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate17 value2 = (Delegate17)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate17>(ref this.delegate17_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate17 @delegate = this.delegate17_0;
				Delegate17 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate17 value2 = (Delegate17)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate17>(ref this.delegate17_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x060018C8 RID: 6344 RVA: 0x000AA75C File Offset: 0x000A895C
		protected void method_0(int int_0, bool bool_0)
		{
			EventArgs13 e = new EventArgs13(int_0, bool_0);
			Delegate17 @delegate = this.delegate17_0;
			if (@delegate != null)
			{
				@delegate(this, e);
			}
		}

		// Token: 0x060018C9 RID: 6345 RVA: 0x0000A3A9 File Offset: 0x000085A9
		public Form20()
		{
			this.method_1();
		}

		// Token: 0x060018CA RID: 6346 RVA: 0x000AA788 File Offset: 0x000A8988
		private void button_0_Click(object sender, EventArgs e)
		{
			Form20.Class314 @class = new Form20.Class314();
			@class.string_0 = this.textBox_0.Text;
			if (@class.string_0.Length == 0)
			{
				MessageBox.Show(Class521.smethod_0(64510), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else if (Base.Acct.Accounts.Where(new Func<Account, bool>(@class.method_0)).Any<Account>())
			{
				MessageBox.Show(Class521.smethod_0(64567), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_0.Focus();
			}
			else
			{
				int num = 0;
				try
				{
					num = Base.Acct.smethod_9(@class.string_0, this.numericUpDown_0.Value, this.textBox_1.Text, this.checkBox_0.Checked);
				}
				catch (Exception ex)
				{
					throw ex;
				}
				if (num > 0)
				{
					bool bool_ = false;
					if (MessageBox.Show(Class521.smethod_0(64628), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						bool_ = true;
					}
					this.method_0(num, bool_);
					base.Close();
				}
			}
		}

		// Token: 0x060018CB RID: 6347 RVA: 0x000041B9 File Offset: 0x000023B9
		private void Form20_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x060018CC RID: 6348 RVA: 0x0000A3B9 File Offset: 0x000085B9
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060018CD RID: 6349 RVA: 0x000AA89C File Offset: 0x000A8A9C
		private void method_1()
		{
			this.label_0 = new Label();
			this.label_1 = new Label();
			this.textBox_0 = new TextBox();
			this.numericUpDown_0 = new NumericUpDown();
			this.textBox_1 = new TextBox();
			this.label_2 = new Label();
			this.button_0 = new Button();
			this.button_1 = new Button();
			this.label_3 = new Label();
			this.checkBox_0 = new CheckBox();
			((ISupportInitialize)this.numericUpDown_0).BeginInit();
			base.SuspendLayout();
			this.label_0.AutoSize = true;
			this.label_0.Location = new Point(54, 29);
			this.label_0.Name = Class521.smethod_0(5871);
			this.label_0.Size = new Size(82, 15);
			this.label_0.TabIndex = 0;
			this.label_0.Text = Class521.smethod_0(64709);
			this.label_1.AutoSize = true;
			this.label_1.Location = new Point(54, 70);
			this.label_1.Name = Class521.smethod_0(5827);
			this.label_1.Size = new Size(82, 15);
			this.label_1.TabIndex = 0;
			this.label_1.Text = Class521.smethod_0(64730);
			this.textBox_0.BorderStyle = BorderStyle.FixedSingle;
			this.textBox_0.Location = new Point(147, 25);
			this.textBox_0.MaxLength = 16;
			this.textBox_0.Name = Class521.smethod_0(64751);
			this.textBox_0.Size = new Size(186, 25);
			this.textBox_0.TabIndex = 1;
			this.numericUpDown_0.BorderStyle = BorderStyle.FixedSingle;
			NumericUpDown numericUpDown = this.numericUpDown_0;
			int[] array = new int[4];
			array[0] = 100;
			numericUpDown.Increment = new decimal(array);
			this.numericUpDown_0.Location = new Point(147, 65);
			NumericUpDown numericUpDown2 = this.numericUpDown_0;
			int[] array2 = new int[4];
			array2[0] = 100000000;
			numericUpDown2.Maximum = new decimal(array2);
			NumericUpDown numericUpDown3 = this.numericUpDown_0;
			int[] array3 = new int[4];
			array3[0] = 20000;
			numericUpDown3.Minimum = new decimal(array3);
			this.numericUpDown_0.Name = Class521.smethod_0(64776);
			this.numericUpDown_0.Size = new Size(186, 25);
			this.numericUpDown_0.TabIndex = 2;
			this.numericUpDown_0.ThousandsSeparator = true;
			NumericUpDown numericUpDown4 = this.numericUpDown_0;
			int[] array4 = new int[4];
			array4[0] = 500000;
			numericUpDown4.Value = new decimal(array4);
			this.textBox_1.BorderStyle = BorderStyle.FixedSingle;
			this.textBox_1.Location = new Point(147, 107);
			this.textBox_1.MaxLength = 255;
			this.textBox_1.Multiline = true;
			this.textBox_1.Name = Class521.smethod_0(64805);
			this.textBox_1.Size = new Size(287, 90);
			this.textBox_1.TabIndex = 3;
			this.label_2.AutoSize = true;
			this.label_2.Location = new Point(84, 110);
			this.label_2.Name = Class521.smethod_0(5849);
			this.label_2.Size = new Size(52, 15);
			this.label_2.TabIndex = 0;
			this.label_2.Text = Class521.smethod_0(64826);
			this.button_0.Location = new Point(243, 257);
			this.button_0.Name = Class521.smethod_0(7442);
			this.button_0.Size = new Size(111, 30);
			this.button_0.TabIndex = 4;
			this.button_0.Text = Class521.smethod_0(5801);
			this.button_0.UseVisualStyleBackColor = true;
			this.button_0.Click += this.button_0_Click;
			this.button_1.DialogResult = DialogResult.Cancel;
			this.button_1.Location = new Point(368, 257);
			this.button_1.Name = Class521.smethod_0(7421);
			this.button_1.Size = new Size(111, 30);
			this.button_1.TabIndex = 5;
			this.button_1.Text = Class521.smethod_0(5783);
			this.button_1.UseVisualStyleBackColor = true;
			this.label_3.AutoSize = true;
			this.label_3.Location = new Point(54, 213);
			this.label_3.Name = Class521.smethod_0(7019);
			this.label_3.Size = new Size(82, 15);
			this.label_3.TabIndex = 6;
			this.label_3.Text = Class521.smethod_0(64839);
			this.checkBox_0.AutoSize = true;
			this.checkBox_0.Location = new Point(147, 212);
			this.checkBox_0.Name = Class521.smethod_0(64860);
			this.checkBox_0.Size = new Size(345, 19);
			this.checkBox_0.TabIndex = 7;
			this.checkBox_0.Text = Class521.smethod_0(64885);
			this.checkBox_0.UseVisualStyleBackColor = true;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = SystemColors.Control;
			base.CancelButton = this.button_1;
			base.ClientSize = new Size(512, 297);
			base.Controls.Add(this.checkBox_0);
			base.Controls.Add(this.label_3);
			base.Controls.Add(this.button_1);
			base.Controls.Add(this.button_0);
			base.Controls.Add(this.label_2);
			base.Controls.Add(this.textBox_1);
			base.Controls.Add(this.numericUpDown_0);
			base.Controls.Add(this.textBox_0);
			base.Controls.Add(this.label_1);
			base.Controls.Add(this.label_0);
			this.DoubleBuffered = true;
			base.FormBorderStyle = FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = Class521.smethod_0(64970);
			base.ShowInTaskbar = false;
			base.SizeGripStyle = SizeGripStyle.Hide;
			base.StartPosition = FormStartPosition.CenterParent;
			this.Text = Class521.smethod_0(64991);
			base.Load += this.Form20_Load;
			((ISupportInitialize)this.numericUpDown_0).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000C5A RID: 3162
		[CompilerGenerated]
		private Delegate17 delegate17_0;

		// Token: 0x04000C5B RID: 3163
		private IContainer icontainer_0;

		// Token: 0x04000C5C RID: 3164
		private Label label_0;

		// Token: 0x04000C5D RID: 3165
		private Label label_1;

		// Token: 0x04000C5E RID: 3166
		private TextBox textBox_0;

		// Token: 0x04000C5F RID: 3167
		private NumericUpDown numericUpDown_0;

		// Token: 0x04000C60 RID: 3168
		private TextBox textBox_1;

		// Token: 0x04000C61 RID: 3169
		private Label label_2;

		// Token: 0x04000C62 RID: 3170
		private Button button_0;

		// Token: 0x04000C63 RID: 3171
		private Button button_1;

		// Token: 0x04000C64 RID: 3172
		private Label label_3;

		// Token: 0x04000C65 RID: 3173
		private CheckBox checkBox_0;

		// Token: 0x02000247 RID: 583
		[CompilerGenerated]
		private sealed class Class314
		{
			// Token: 0x060018CF RID: 6351 RVA: 0x000AAF80 File Offset: 0x000A9180
			internal bool method_0(Account account_0)
			{
				bool result;
				if (account_0.UserName == TApp.UserName)
				{
					result = (account_0.AcctName == this.string_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000C66 RID: 3174
			public string string_0;
		}
	}
}
