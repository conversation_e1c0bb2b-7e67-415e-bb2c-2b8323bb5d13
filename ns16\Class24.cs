﻿using System;
using ns18;

namespace ns16
{
	// Token: 0x02000040 RID: 64
	internal sealed class Class24
	{
		// Token: 0x17000085 RID: 133
		// (get) Token: 0x060001E7 RID: 487 RVA: 0x00018B84 File Offset: 0x00016D84
		public static string AppLoggingIn
		{
			get
			{
				return Class521.smethod_0(3681);
			}
		}

		// Token: 0x17000086 RID: 134
		// (get) Token: 0x060001E8 RID: 488 RVA: 0x00018BA0 File Offset: 0x00016DA0
		public static string AppLoggedIn
		{
			get
			{
				return Class521.smethod_0(3702);
			}
		}

		// Token: 0x17000087 RID: 135
		// (get) Token: 0x060001E9 RID: 489 RVA: 0x00018BBC File Offset: 0x00016DBC
		public static string AppLoading
		{
			get
			{
				return Class521.smethod_0(3723);
			}
		}

		// Token: 0x17000088 RID: 136
		// (get) Token: 0x060001EA RID: 490 RVA: 0x00018BD8 File Offset: 0x00016DD8
		public static string AppStarted
		{
			get
			{
				return Class521.smethod_0(3744);
			}
		}

		// Token: 0x17000089 RID: 137
		// (get) Token: 0x060001EB RID: 491 RVA: 0x00018BF4 File Offset: 0x00016DF4
		public static string AppExited
		{
			get
			{
				return Class521.smethod_0(3761);
			}
		}

		// Token: 0x1700008A RID: 138
		// (get) Token: 0x060001EC RID: 492 RVA: 0x00018C10 File Offset: 0x00016E10
		public static string AutoPlayStarted
		{
			get
			{
				return Class521.smethod_0(3778);
			}
		}

		// Token: 0x1700008B RID: 139
		// (get) Token: 0x060001ED RID: 493 RVA: 0x00018C2C File Offset: 0x00016E2C
		public static string AutoPlayStopped
		{
			get
			{
				return Class521.smethod_0(3803);
			}
		}

		// Token: 0x1700008C RID: 140
		// (get) Token: 0x060001EE RID: 494 RVA: 0x00018C48 File Offset: 0x00016E48
		public static string PeriodResetting
		{
			get
			{
				return Class521.smethod_0(3828);
			}
		}

		// Token: 0x1700008D RID: 141
		// (get) Token: 0x060001EF RID: 495 RVA: 0x00018C64 File Offset: 0x00016E64
		public static string PeriodReset
		{
			get
			{
				return Class521.smethod_0(3853);
			}
		}

		// Token: 0x1700008E RID: 142
		// (get) Token: 0x060001F0 RID: 496 RVA: 0x00018C80 File Offset: 0x00016E80
		public static string OrderPlaced
		{
			get
			{
				return Class521.smethod_0(3870);
			}
		}

		// Token: 0x1700008F RID: 143
		// (get) Token: 0x060001F1 RID: 497 RVA: 0x00018C9C File Offset: 0x00016E9C
		public static string PageChanging
		{
			get
			{
				return Class521.smethod_0(3887);
			}
		}

		// Token: 0x17000090 RID: 144
		// (get) Token: 0x060001F2 RID: 498 RVA: 0x00018CB8 File Offset: 0x00016EB8
		public static string PageChanged
		{
			get
			{
				return Class521.smethod_0(3908);
			}
		}

		// Token: 0x17000091 RID: 145
		// (get) Token: 0x060001F3 RID: 499 RVA: 0x00018CD4 File Offset: 0x00016ED4
		public static string SymblChanging
		{
			get
			{
				return Class521.smethod_0(3925);
			}
		}

		// Token: 0x17000092 RID: 146
		// (get) Token: 0x060001F4 RID: 500 RVA: 0x00018CF0 File Offset: 0x00016EF0
		public static string SymblChanged
		{
			get
			{
				return Class521.smethod_0(3946);
			}
		}

		// Token: 0x17000093 RID: 147
		// (get) Token: 0x060001F5 RID: 501 RVA: 0x00018D0C File Offset: 0x00016F0C
		public static string AcctChanging
		{
			get
			{
				return Class521.smethod_0(3967);
			}
		}

		// Token: 0x17000094 RID: 148
		// (get) Token: 0x060001F6 RID: 502 RVA: 0x00018D28 File Offset: 0x00016F28
		public static string AcctChanged
		{
			get
			{
				return Class521.smethod_0(3988);
			}
		}

		// Token: 0x17000095 RID: 149
		// (get) Token: 0x060001F7 RID: 503 RVA: 0x00018D44 File Offset: 0x00016F44
		public static string BlindTestEntering
		{
			get
			{
				return Class521.smethod_0(4005);
			}
		}

		// Token: 0x17000096 RID: 150
		// (get) Token: 0x060001F8 RID: 504 RVA: 0x00018D60 File Offset: 0x00016F60
		public static string BlindTestEntered
		{
			get
			{
				return Class521.smethod_0(4030);
			}
		}

		// Token: 0x17000097 RID: 151
		// (get) Token: 0x060001F9 RID: 505 RVA: 0x00018D7C File Offset: 0x00016F7C
		public static string BlindTestExiting
		{
			get
			{
				return Class521.smethod_0(4055);
			}
		}

		// Token: 0x17000098 RID: 152
		// (get) Token: 0x060001FA RID: 506 RVA: 0x00018D98 File Offset: 0x00016F98
		public static string BlindTestExited
		{
			get
			{
				return Class521.smethod_0(4080);
			}
		}

		// Token: 0x17000099 RID: 153
		// (get) Token: 0x060001FB RID: 507 RVA: 0x00018DB4 File Offset: 0x00016FB4
		public static string SpanMoveNext
		{
			get
			{
				return Class521.smethod_0(4105);
			}
		}

		// Token: 0x1700009A RID: 154
		// (get) Token: 0x060001FC RID: 508 RVA: 0x00018DD0 File Offset: 0x00016FD0
		public static string SpanMovePrev
		{
			get
			{
				return Class521.smethod_0(4126);
			}
		}

		// Token: 0x1700009B RID: 155
		// (get) Token: 0x060001FD RID: 509 RVA: 0x00018DEC File Offset: 0x00016FEC
		public static string ExceptionOccurred
		{
			get
			{
				return Class521.smethod_0(4147);
			}
		}

		// Token: 0x1700009C RID: 156
		// (get) Token: 0x060001FE RID: 510 RVA: 0x00018E08 File Offset: 0x00017008
		public static string ErrorOccurred
		{
			get
			{
				return Class521.smethod_0(4172);
			}
		}

		// Token: 0x1700009D RID: 157
		// (get) Token: 0x060001FF RID: 511 RVA: 0x00018E24 File Offset: 0x00017024
		public static string AppBackgroudUpdating
		{
			get
			{
				return Class521.smethod_0(4193);
			}
		}

		// Token: 0x1700009E RID: 158
		// (get) Token: 0x06000200 RID: 512 RVA: 0x00018E40 File Offset: 0x00017040
		public static string AppBackgroudUpdated
		{
			get
			{
				return Class521.smethod_0(4226);
			}
		}
	}
}
