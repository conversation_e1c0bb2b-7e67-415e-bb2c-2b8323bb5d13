﻿using System;
using System.Collections.Generic;

namespace TEx.ImportTrans
{
	// Token: 0x02000357 RID: 855
	public sealed class CfmmcAcct : IStoreElement
	{
		// Token: 0x060023B9 RID: 9145 RVA: 0x0000E02D File Offset: 0x0000C22D
		public CfmmcAcct()
		{
			this.list_0 = new List<BindingAcct>();
		}

		// Token: 0x060023BA RID: 9146 RVA: 0x0000E042 File Offset: 0x0000C242
		public CfmmcAcct(string id, string password) : this()
		{
			this.string_0 = id;
			this.string_1 = password;
		}

		// Token: 0x060023BB RID: 9147 RVA: 0x0000E05A File Offset: 0x0000C25A
		public CfmmcAcct(string id, string password, DateTime? begDT, DateTime? endDT) : this(id, password)
		{
			this.nullable_0 = begDT;
			this.nullable_1 = endDT;
		}

		// Token: 0x060023BC RID: 9148 RVA: 0x0000E075 File Offset: 0x0000C275
		public CfmmcAcct(string id, string password, DateTime? begDT, DateTime? endDT, DateTime? lastDnldTime, string note) : this(id, password, begDT, endDT)
		{
			this.nullable_2 = lastDnldTime;
			this.string_2 = note;
		}

		// Token: 0x17000624 RID: 1572
		// (get) Token: 0x060023BD RID: 9149 RVA: 0x000F9D54 File Offset: 0x000F7F54
		// (set) Token: 0x060023BE RID: 9150 RVA: 0x0000E094 File Offset: 0x0000C294
		public string ID
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x17000625 RID: 1573
		// (get) Token: 0x060023BF RID: 9151 RVA: 0x000F9D6C File Offset: 0x000F7F6C
		// (set) Token: 0x060023C0 RID: 9152 RVA: 0x0000E09F File Offset: 0x0000C29F
		public string Password
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x17000626 RID: 1574
		// (get) Token: 0x060023C1 RID: 9153 RVA: 0x000F9D84 File Offset: 0x000F7F84
		// (set) Token: 0x060023C2 RID: 9154 RVA: 0x0000E0AA File Offset: 0x0000C2AA
		public DateTime? BeginDate
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x17000627 RID: 1575
		// (get) Token: 0x060023C3 RID: 9155 RVA: 0x000F9D9C File Offset: 0x000F7F9C
		// (set) Token: 0x060023C4 RID: 9156 RVA: 0x0000E0B5 File Offset: 0x0000C2B5
		public DateTime? EndDate
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x17000628 RID: 1576
		// (get) Token: 0x060023C5 RID: 9157 RVA: 0x000F9DB4 File Offset: 0x000F7FB4
		// (set) Token: 0x060023C6 RID: 9158 RVA: 0x0000E0C0 File Offset: 0x0000C2C0
		public DateTime? LastDownloadTime
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x17000629 RID: 1577
		// (get) Token: 0x060023C7 RID: 9159 RVA: 0x000F9DCC File Offset: 0x000F7FCC
		// (set) Token: 0x060023C8 RID: 9160 RVA: 0x0000E0CB File Offset: 0x0000C2CB
		public List<BindingAcct> BindingAccts
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x1700062A RID: 1578
		// (get) Token: 0x060023C9 RID: 9161 RVA: 0x000F9DE4 File Offset: 0x000F7FE4
		// (set) Token: 0x060023CA RID: 9162 RVA: 0x0000E0D6 File Offset: 0x0000C2D6
		public string Note
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x0400113E RID: 4414
		private string string_0;

		// Token: 0x0400113F RID: 4415
		public string string_1;

		// Token: 0x04001140 RID: 4416
		public DateTime? nullable_0;

		// Token: 0x04001141 RID: 4417
		public DateTime? nullable_1;

		// Token: 0x04001142 RID: 4418
		private DateTime? nullable_2;

		// Token: 0x04001143 RID: 4419
		private List<BindingAcct> list_0;

		// Token: 0x04001144 RID: 4420
		private string string_2;
	}
}
