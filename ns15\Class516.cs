﻿using System;
using System.Runtime.CompilerServices;

namespace ns15
{
	// Token: 0x020003CD RID: 973
	internal abstract class Class516 : IDisposable
	{
		// Token: 0x060026F2 RID: 9970 RVA: 0x0000EFBA File Offset: 0x0000D1BA
		public Class516(UIntPtr uintptr_1)
		{
			this.HKey = uintptr_1;
		}

		// Token: 0x170006C0 RID: 1728
		// (get) Token: 0x060026F3 RID: 9971 RVA: 0x0000EFC9 File Offset: 0x0000D1C9
		// (set) Token: 0x060026F4 RID: 9972 RVA: 0x0000EFD1 File Offset: 0x0000D1D1
		public UIntPtr HKey { get; set; }

		// Token: 0x060026F5 RID: 9973
		public abstract object vmethod_0(string string_0);

		// Token: 0x060026F6 RID: 9974
		public abstract bool vmethod_1(string string_0, out object object_0);

		// Token: 0x060026F7 RID: 9975
		public abstract void Dispose();

		// Token: 0x040012DE RID: 4830
		[CompilerGenerated]
		private UIntPtr uintptr_0;
	}
}
