﻿using System;
using TEx.Chart;

namespace TEx
{
	// Token: 0x020002A8 RID: 680
	internal sealed class IndCurve
	{
		// Token: 0x06001E20 RID: 7712 RVA: 0x0000C9EB File Offset: 0x0000ABEB
		public IndCurve(string desc, CurveItem curve, bool isVisible)
		{
			this._Desc = desc;
			this._Curve = curve;
			this._IsVisible = isVisible;
		}

		// Token: 0x170004C6 RID: 1222
		// (get) Token: 0x06001E21 RID: 7713 RVA: 0x000D4004 File Offset: 0x000D2204
		// (set) Token: 0x06001E22 RID: 7714 RVA: 0x0000CA0A File Offset: 0x0000AC0A
		public string Desc
		{
			get
			{
				return this._Desc;
			}
			private set
			{
				this._Desc = value;
			}
		}

		// Token: 0x170004C7 RID: 1223
		// (get) Token: 0x06001E23 RID: 7715 RVA: 0x000D401C File Offset: 0x000D221C
		// (set) Token: 0x06001E24 RID: 7716 RVA: 0x0000CA15 File Offset: 0x0000AC15
		public CurveItem Curve
		{
			get
			{
				return this._Curve;
			}
			private set
			{
				this._Curve = value;
			}
		}

		// Token: 0x170004C8 RID: 1224
		// (get) Token: 0x06001E25 RID: 7717 RVA: 0x000D4034 File Offset: 0x000D2234
		public bool IsVisible
		{
			get
			{
				return this._IsVisible;
			}
		}

		// Token: 0x04000ED6 RID: 3798
		protected string _Desc;

		// Token: 0x04000ED7 RID: 3799
		protected CurveItem _Curve;

		// Token: 0x04000ED8 RID: 3800
		protected bool _IsVisible;
	}
}
