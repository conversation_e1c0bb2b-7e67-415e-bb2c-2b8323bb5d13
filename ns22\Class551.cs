﻿using System;
using Microsoft.Win32;
using ns18;

namespace ns22
{
	// Token: 0x0200041A RID: 1050
	internal sealed class Class551
	{
		// Token: 0x0600286C RID: 10348 RVA: 0x0010FB14 File Offset: 0x0010DD14
		public static string smethod_0(string string_1)
		{
			string result;
			try
			{
				RegistryKey registryKey = Registry.LocalMachine.OpenSubKey(Class521.smethod_0(121539));
				if (registryKey == null)
				{
					result = string.Empty;
				}
				else
				{
					string text = (string)registryKey.GetValue(string_1, string.Empty);
					registryKey.Close();
					result = text;
				}
			}
			catch
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x0600286D RID: 10349 RVA: 0x0010FB78 File Offset: 0x0010DD78
		public static void smethod_1(string string_1, string string_2)
		{
			try
			{
				RegistryKey registryKey = Registry.LocalMachine.OpenSubKey(Class521.smethod_0(121539), true);
				if (registryKey == null)
				{
					registryKey = Registry.LocalMachine.CreateSubKey(Class521.smethod_0(121539));
				}
				registryKey.SetValue(string_1, string_2);
				registryKey.Close();
			}
			catch
			{
			}
		}

		// Token: 0x04001443 RID: 5187
		private const string string_0 = "SOFTWARE\\RedGate\\SmartAssembly";
	}
}
