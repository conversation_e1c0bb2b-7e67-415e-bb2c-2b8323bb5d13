﻿using System;
using System.Drawing;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns22
{
	// Token: 0x020002EF RID: 751
	internal class Class390 : ShapeCurve
	{
		// Token: 0x06002117 RID: 8471 RVA: 0x000EB0D8 File Offset: 0x000E92D8
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (zedGraphControl_0.GraphPane != null)
			{
				if (this.curveItem_0 != null)
				{
					zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
				}
				LineItem lineItem = zedGraphControl_0.GraphPane.AddCurve(base.IndData.Name, base.DataView, color_0, SymbolType.None);
				if (lineItem != null)
				{
					base.method_3(string_0, lineItem);
				}
			}
		}

		// Token: 0x06002118 RID: 8472 RVA: 0x0000D610 File Offset: 0x0000B810
		public Class390(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
