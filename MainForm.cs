﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Media;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Timers;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Metro;
using DevComponents.DotNetBar.Metro.ColorTables;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Common;
using NAppUpdate.Framework.Sources;
using ns1;
using ns10;
using ns11;
using ns12;
using ns13;
using ns14;
using ns15;
using ns16;
using ns17;
using ns18;
using ns19;
using ns2;
using ns20;
using ns21;
using ns22;
using ns23;
using ns24;
using ns25;
using ns26;
using ns27;
using ns29;
using ns3;
using ns30;
using ns31;
using ns4;
using ns5;
using ns7;
using ns9;
using TEx.Comn;
using TEx.ImportTrans;
using TEx.SIndicator;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200027E RID: 638
	internal sealed partial class MainForm : MetroForm
	{
		// Token: 0x06001BA0 RID: 7072 RVA: 0x000BFFFC File Offset: 0x000BE1FC
		public MainForm()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_9();
			this.method_210();
			this.dotNetBarManager_0.ShowCustomizeContextMenu = false;
			this.bar_1.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.bar_2.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.bar_4.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.bar_3.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.bar_6.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.method_10();
			this.method_3();
			base.Icon = Class375.TExIcoBlue;
			base.ShowIcon = false;
			if (TApp.IsFirstRun)
			{
				PageSelWnd pageSelWnd = new PageSelWnd();
				pageSelWnd.PageSelected += this.method_7;
				pageSelWnd.ShowDialog();
			}
			this.bar_0.Enabled = false;
			this.bar_0.Visible = false;
			this.class304_1.InterceptMouseWheel = Class304.Enum21.const_1;
			this.class304_0.InterceptMouseWheel = Class304.Enum21.const_1;
			base.KeyPreview = true;
			base.Activated += this.MainForm_Activated;
			base.Deactivate += this.MainForm_Deactivate;
			base.FormClosing += this.MainForm_FormClosing;
			base.Load += this.MainForm_Load;
			base.Shown += this.MainForm_Shown;
			base.KeyDown += this.MainForm_KeyDown;
			base.KeyPress += this.MainForm_KeyPress;
			base.ResizeBegin += this.MainForm_ResizeBegin;
			base.ResizeEnd += this.MainForm_ResizeEnd;
			base.Resize += this.MainForm_Resize;
			base.MouseWheel += this.MainForm_MouseWheel;
			this.bar_2.ThemeAware = false;
			this.buttonItem_4.PopupOpen += this.buttonItem_4_PopupOpen;
			this.buttonItem_5.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_5_PopupOpen);
			this.buttonItem_7.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_7_PopupOpen);
			this.buttonItem_34.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_34_PopupOpen);
			this.buttonItem_37.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_37_PopupOpen);
			this.buttonItem_38.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_38_PopupOpen);
			this.buttonItem_0.Click += this.buttonItem_0_Click;
			this.buttonItem_1.Click += this.buttonItem_1_Click;
			this.buttonItem_2.Click += this.buttonItem_2_Click;
			this.buttonItem_3.Click += this.buttonItem_3_Click;
			this.buttonItem_8.Click += this.buttonItem_8_Click;
			this.buttonItem_11.Click += this.buttonItem_11_Click;
			this.buttonItem_12.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_12_PopupOpen);
			this.buttonItem_14.Click += this.buttonItem_14_Click;
			this.buttonItem_15.Click += this.buttonItem_15_Click;
			this.buttonItem_16.Click += this.buttonItem_16_Click;
			this.buttonItem_17.Click += this.buttonItem_17_Click;
			this.buttonItem_19.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.buttonItem_19_PopupOpen);
			this.buttonItem_20.Click += this.buttonItem_20_Click;
			this.buttonItem_21.Click += this.buttonItem_21_Click;
			this.buttonItem_47.Click += this.buttonItem_47_Click;
			this.buttonItem_50.Click += this.buttonItem_50_Click;
			this.buttonItem_51.Click += this.buttonItem_51_Click;
			this.buttonItem_52.Click += this.buttonItem_52_Click;
			this.buttonItem_4.Click += this.buttonItem_4_Click;
			this.buttonItem_22.Click += this.buttonItem_22_Click;
			this.buttonItem_23.Click += this.buttonItem_23_Click;
			this.buttonItem_24.Click += this.buttonItem_24_Click;
			this.buttonItem_25.Click += this.buttonItem_25_Click;
			this.buttonItem_26.Click += this.buttonItem_26_Click;
			this.buttonItem_27.Click += this.buttonItem_27_Click;
			this.buttonItem_28.Click += this.buttonItem_28_Click;
			this.buttonItem_29.Click += this.buttonItem_29_Click;
			this.buttonItem_30.Click += this.buttonItem_30_Click;
			this.buttonItem_49.Click += this.buttonItem_49_Click;
			this.buttonItem_31.Click += this.buttonItem_31_Click;
			this.buttonItem_32.Click += this.buttonItem_32_Click;
			this.buttonItem_33.Click += this.buttonItem_33_Click;
			this.buttonItem_35.Click += this.buttonItem_35_Click;
			this.timer_1.Tick += this.timer_1_Tick;
			this.controlContainerItem_0.MouseEnter += this.controlContainerItem_0_MouseEnter;
			this.controlContainerItem_0.MouseLeave += this.controlContainerItem_0_MouseLeave;
			this.class304_1.ValueChanged += this.class304_1_ValueChanged;
			this.class304_1.BeforeValueDecrement += this.method_156;
			this.class304_1.Click += this.class304_1_Click;
			this.buttonItem_53.Click += this.buttonItem_53_Click;
			this.buttonItem_54.Click += this.buttonItem_54_Click;
			this.buttonItem_55.Click += this.buttonItem_55_Click;
			this.buttonItem_0.Tooltip = Class521.smethod_0(3636);
			this.buttonItem_0.ToolTipVisibleChanged += this.buttonItem_0_ToolTipVisibleChanged;
			this.buttonItem_2.Tooltip = Class521.smethod_0(3636);
			this.buttonItem_2.ToolTipVisibleChanged += this.buttonItem_2_ToolTipVisibleChanged;
			this.buttonItem_1.Tooltip = Class521.smethod_0(3636);
			this.buttonItem_1.ToolTipVisibleChanged += this.buttonItem_1_ToolTipVisibleChanged;
			this.buttonItem_3.Tooltip = Class521.smethod_0(3636);
			this.buttonItem_3.ToolTipVisibleChanged += this.buttonItem_3_ToolTipVisibleChanged;
		}

		// Token: 0x06001BA1 RID: 7073 RVA: 0x0000B72D File Offset: 0x0000992D
		private void MainForm_Load(object sender, EventArgs e)
		{
			this.bool_2 = false;
			this.method_0();
		}

		// Token: 0x06001BA2 RID: 7074 RVA: 0x000C06E4 File Offset: 0x000BE8E4
		private void method_0()
		{
			this.method_1();
			base.Activate();
			Base.UI.smethod_178();
			this.timer_2 = new System.Timers.Timer();
			this.timer_2.Interval = 15000.0;
			this.timer_2.Elapsed += this.timer_2_Elapsed;
			this.timer_2.Start();
		}

		// Token: 0x06001BA3 RID: 7075 RVA: 0x000C0748 File Offset: 0x000BE948
		private void method_1()
		{
			Base.Data.CurrSymblChanged += this.method_164;
			Base.Acct.AccountChanging += this.method_92;
			Base.Acct.AccountChanged += this.method_93;
			Base.Data.AppendingData += this.method_166;
			Base.Data.HisDataAppended += this.method_167;
			Base.Data.HDPS_1hChanged += this.method_168;
			Base.Data.GenHisDataSetStarted += this.method_169;
			Base.Data.GenHisDataSetCompleted += this.method_170;
			Base.Data.GenHisDataSetCanceled += this.method_171;
			Base.Data.NoAvailableDataDetected += this.method_172;
			Base.Data.DownloadDataError += this.method_173;
			Base.Data.RetrievingData += this.method_174;
			Base.Data.RetrieveDataCompleted += this.method_175;
			Base.Data.CurrHisDataSetUdpatedWithNewStartEndDate += this.method_179;
			Base.Data.DateSelectionChanged += this.method_161;
			Base.Acct.smethod_43();
			Base.UI.smethod_176(Class521.smethod_0(73007));
			Base.UI.MainForm = this;
			Base.UI.ChartPageChanged += this.method_111;
			Base.UI.CurrPageSaved += this.method_112;
			Base.UI.SelectedChtCtrlChanged += this.method_113;
			Base.UI.NewSwitchChtCtrlAdded += this.method_39;
			Base.UI.NewSwitchAcctTabAdded += this.method_41;
			Base.UI.ViewSwitched += this.method_40;
			Base.UI.Form.StartedSetOn += this.method_114;
			Base.UI.Form.StartedSetOff += this.method_115;
			Base.UI.Form.StockShortSettingChanged += this.method_116;
			Base.UI.Form.AutoPlayUnitChanged += this.method_117;
			Base.UI.Form.IfShowAcctInfoOnTransTabHeaderChanged += this.method_118;
			Base.UI.Form.IfShowDayOfWeekChanged += this.method_119;
			Base.UI.Form.BlindTestModeOn += this.method_121;
			Base.UI.Form.BlindTestModeOff += this.method_120;
			Base.UI.DrawObjStartMoving += this.method_42;
			Base.UI.TransTabUpdated += this.method_43;
			Base.UI.SpanMovePrevReachedBegDT += this.method_38;
			Base.UI.CurrTradingSymbChanged += this.method_108;
			Base.UI.ZiXuanSymbAdded += this.method_109;
			Base.UI.ChartThemeChanged += this.method_8;
			this.method_2();
			Base.UI.smethod_176(Class521.smethod_0(73040));
			this.method_24(null);
			Base.Trading.TransCreated += this.method_136;
			Base.Trading.ShownHisTransAdded += new Delegate22(this.method_137);
			Base.Trading.OrderCreated += this.method_138;
			Base.Trading.OrderExcFailed += this.method_139;
			Base.Trading.OrderStatusUpdated += this.method_140;
			Base.Trading.CondOrderStatusUpdated += this.method_141;
			Base.Trading.CondOrderCreated += this.method_147;
			Base.Trading.AutoStopChanged += this.method_127;
			Base.Trading.AutoLimitChanged += this.method_131;
			Base.Trading.StSpltTransExecuting += this.method_148;
			Base.Trading.StSpltTransExecuted += this.method_149;
			Base.Trading.FreeMarginNotEnoughWhenChkPlaceMktOdr += this.method_150;
			Base.Trading.ExceedClosableTransWhenChkPlaceMktOdr += this.method_151;
			this.method_16();
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet_ in Base.Data.SymbDataSets)
				{
					this.method_157(symbDataSet_);
				}
			}
			Base.Data.SymbDataSetAdded += this.method_159;
			Base.Data.SymbDataSetRemoved += this.method_160;
			this.method_199();
		}

		// Token: 0x06001BA4 RID: 7076 RVA: 0x000C0B6C File Offset: 0x000BED6C
		private void MainForm_Shown(object sender, EventArgs e)
		{
			if (TApp.SrvParams.LogonNoticeInfo != null && !string.IsNullOrEmpty(TApp.SrvParams.LogonNoticeInfo.HTMLUrl))
			{
				if (Base.UI.Form.LastDisplayedLogonNoticeDT != null && !Base.UI.Form.IfShowSameLogonNoticeNextTime && !(Base.UI.Form.LastDisplayedLogonNoticeDT.Value != TApp.SrvParams.LogonNoticeInfo.DateTime))
				{
					this.method_6();
				}
				else
				{
					new Form16(TApp.SrvParams.LogonNoticeInfo)
					{
						Owner = this,
						StartPosition = FormStartPosition.CenterParent
					}.ShowDialog();
				}
			}
			else
			{
				this.method_6();
			}
			TApp.EnteredMainForm = true;
			Base.UI.smethod_164();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_48();
			}
			int id = Base.UI.CurrTradingSymbol.ID;
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet.HasValidDataSet)
			{
				this.method_66(symbDataSet.CurrHisData);
			}
			else
			{
				Base.UI.CurrTradingSymbol = Base.UI.CurrSymbol;
				Class184.smethod_0(new Exception(Class521.smethod_0(73073) + id + Class521.smethod_0(5046)));
			}
			if (!TApp.IsTrialUser)
			{
				DateTime? endDate = TApp.SrvParams.UsrStkMetaList.First<UsrStkMeta>().EndDate;
				TimeSpan? timeSpan = DateTime.Now - endDate;
				TimeSpan t = new TimeSpan(1, 0, 0, 0);
				if (timeSpan > t && MessageBox.Show(Class521.smethod_0(73150) + endDate.Value.ToShortDateString() + Class521.smethod_0(73211), Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
				{
					try
					{
						Process.Start(new ProcessStartInfo(Class521.smethod_0(73296)));
					}
					catch
					{
					}
				}
			}
			InfoMineMgr.smethod_5(null);
			this.method_11(false);
			CfmmcRecImporter.StartDownloadRecs += this.method_99;
			CfmmcRecImporter.RecImportSuccess += this.method_98;
			CfmmcRecImporter.RecImportFailed += this.method_97;
			CfmmcRecImporter.NoRecNeedToDnldDetected += this.method_96;
			CfmmcRecImporter.NotifyDnRecIndex += this.method_95;
			CfmmcRecImporter.smethod_12();
			CfmmcRecImporter.IEMajor = Class483.smethod_2();
			if (Base.UI.Form.CfmmcAutoDnldConfig != null && Base.UI.Form.CfmmcAutoDnldConfig.AutoDownOnStartup)
			{
				CfmmcRecImporter.smethod_37();
			}
			Class48.smethod_3(Class24.AppStarted, string.Concat(new string[]
			{
				Class521.smethod_0(73353),
				TApp.Ver,
				Class521.smethod_0(73366),
				(TApp.SrvParams.TExPkg != null) ? TApp.SrvParams.TExPkg.Value.ToString() : Class521.smethod_0(45877),
				Class521.smethod_0(73383),
				Base.Acct.CurrAccount.AcctName,
				Class521.smethod_0(5036),
				Base.Data.smethod_127(),
				Class521.smethod_0(73400),
				Base.UI.Form.CurrentPageName,
				Class521.smethod_0(5036),
				Base.UI.smethod_102()
			}));
		}

		// Token: 0x06001BA5 RID: 7077 RVA: 0x000C0EF4 File Offset: 0x000BF0F4
		private void method_2()
		{
			base.Invalidate();
			this.method_5();
			this.method_90();
			this.method_186(this.buttonItem_34);
			this.method_187(this.buttonItem_37, false);
			this.method_76(this.buttonItem_38);
			this.method_69();
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = 30;
			this.timer_0.Tick += this.timer_0_Tick;
			this.buttonItem_34.Text = Base.UI.Form.CurrentPageName;
			this.buttonItem_37.Text = Base.Acct.CurrAccount.AcctName;
			this.buttonItem_37.Tooltip = Base.Acct.CurrAccount.Notes;
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (stkSymbol == null)
			{
				stkSymbol = Base.UI.CurrSymbol;
			}
			this.buttonItem_38.Text = stkSymbol.Desc;
			this.buttonItem_38.Tooltip = stkSymbol.CNName;
			this.buttonItem_56.ExpandChange += this.buttonItem_56_ExpandChange;
			this.buttonItem_57.Click += this.buttonItem_57_Click;
			this.buttonItem_58.Click += this.buttonItem_58_Click;
			this.buttonItem_59.Click += this.buttonItem_59_Click;
			this.textBoxItem_0.InputTextChanged += this.textBoxItem_0_InputTextChanged;
			this.textBoxItem_1.InputTextChanged += this.textBoxItem_1_InputTextChanged;
			this.textBoxItem_2.InputTextChanged += this.textBoxItem_2_InputTextChanged;
			if (!TApp.IsTrialUser)
			{
				this.buttonItem_51.Visible = false;
			}
			Base.UI.smethod_46(this);
			if (Base.UI.Form.TimerSpeed >= 1)
			{
				this.sliderItem_0.Value = Base.UI.Form.TimerSpeed;
			}
			else
			{
				this.sliderItem_0.Value = 90;
			}
			int value = Base.UI.smethod_180();
			this.class304_0.Value = value;
			this.bar_6.Visible = false;
			if (Base.UI.Form.DotNetBarLayoutString != null)
			{
				this.dotNetBarManager_0.LayoutDefinition = Base.UI.Form.DotNetBarLayoutString;
			}
			if (!this.bar_1.Visible)
			{
				this.bar_1.Visible = true;
			}
			if (!this.bar_2.Visible)
			{
				this.bar_2.Visible = true;
			}
			if (!this.bar_3.Visible)
			{
				this.bar_3.Visible = true;
			}
			if (!this.bar_4.Visible)
			{
				this.bar_4.Visible = true;
			}
			if (!this.bar_5.Visible)
			{
				this.bar_5.Visible = true;
			}
			if (Base.UI.Chart.IsSingleTransTabCtrl)
			{
				Base.UI.Form.IsInBlindTestMode = false;
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_154();
				this.buttonItem_11.Enabled = false;
			}
			else
			{
				this.method_155();
			}
			this.method_89();
			Base.UI.Form.IsSpanMoveNext = false;
			Base.UI.Form.IsSpanMovePrev = false;
			if (Base.UI.Form.AutoPlayPeriodType == null)
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
				Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
			}
			this.method_71();
			this.method_68();
			this.method_67();
			this.buttonItem_22.Text = Class521.smethod_0(1993);
			this.buttonItem_22.Tooltip = Class521.smethod_0(64362);
			this.buttonItem_11.Tooltip = Class521.smethod_0(73417) + Class210.smethod_5(Enum3.const_20).ShortCutKeyString;
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.buttonItem_11.Tooltip = Class521.smethod_0(73438);
			}
			this.buttonItem_56.Tooltip = Class521.smethod_0(73491);
			this.buttonItem_56.ToolTipVisibleChanged += this.buttonItem_56_ToolTipVisibleChanged;
			this.buttonItem_5.Tooltip = Class521.smethod_0(73520);
			this.bar_0.EnabledChanged += this.bar_0_EnabledChanged;
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.labelItem_13.Text = Class521.smethod_0(73537);
			}
			if (!TApp.IsStIncluded)
			{
				this.buttonItem_63.Visible = false;
				this.buttonItem_64.Visible = false;
			}
			this.buttonItem_71.Visible = false;
			if (Utility.GetMajorVerFromTExVer(TApp.Ver) >= 4.2 && !TApp.IsTrialUser && !Base.UI.Form.BackupSyncDisabled && Base.UI.Form.BackupSyncPeriodically)
			{
				BkupSyncMgr.smethod_2();
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.class304_0.BackColor = Class181.color_1;
				this.class304_1.BackColor = Class181.color_1;
			}
			else
			{
				this.class304_0.BackColor = Color.White;
				this.class304_1.BackColor = Color.White;
			}
		}

		// Token: 0x06001BA6 RID: 7078 RVA: 0x000C13CC File Offset: 0x000BF5CC
		private void method_3()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font(Class521.smethod_0(6998), num);
			}
			foreach (object obj in this.dockSite_6.Controls)
			{
				Bar bar = (Bar)obj;
				if (bar.Font.Size != num)
				{
					bar.Font = new Font(Class521.smethod_0(6998), num);
				}
			}
			if (this.bar_5.Font.Size != num)
			{
				this.bar_5.Font = new Font(Class521.smethod_0(6998), num);
			}
			num = TApp.smethod_4(8.4f, false);
			if (this.bar_0.Font.Size != num)
			{
				this.bar_0.Font = new Font(Class521.smethod_0(24023), num);
			}
			float num2 = TApp.smethod_4(8f, false);
			if (this.class304_0.Font.Size != num2)
			{
				this.class304_0.Font = new Font(Class521.smethod_0(6998), num2);
				this.class304_1.Font = new Font(Class521.smethod_0(6998), num2);
			}
		}

		// Token: 0x06001BA7 RID: 7079 RVA: 0x000C153C File Offset: 0x000BF73C
		private Point? method_4()
		{
			Point? result = null;
			if (base.Visible && base.WindowState != FormWindowState.Minimized)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2 + 10, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x06001BA8 RID: 7080 RVA: 0x000C15A4 File Offset: 0x000BF7A4
		private void method_5()
		{
			string text = Base.UI.smethod_114() + Class521.smethod_0(73562) + Base.Acct.CurrAccount.AcctName.Trim() + Class521.smethod_0(5046);
			if (Base.UI.Form.IsInBlindTestMode)
			{
				text += Class521.smethod_0(73571);
			}
			this.Text = text;
		}

		// Token: 0x06001BA9 RID: 7081
		[DllImport("user32.dll")]
		private static extern bool ShowWindow(IntPtr intptr_0, int int_11);

		// Token: 0x06001BAA RID: 7082 RVA: 0x0000B73E File Offset: 0x0000993E
		private void MainForm_ResizeBegin(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.method_191();
			this.method_192();
		}

		// Token: 0x06001BAB RID: 7083 RVA: 0x0000B754 File Offset: 0x00009954
		private void MainForm_ResizeEnd(object sender, EventArgs e)
		{
			this.method_193();
			this.method_194();
			base.ResumeLayout();
		}

		// Token: 0x06001BAC RID: 7084 RVA: 0x000C1608 File Offset: 0x000BF808
		private void MainForm_Resize(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.smethod_0();
			this.method_194();
			base.ResumeLayout();
			this.smethod_1();
			if (this.bool_4 && base.WindowState == FormWindowState.Maximized)
			{
				this.bool_4 = false;
				this.method_28(true);
			}
			else if (this.bool_5 && (base.WindowState == FormWindowState.Normal || base.WindowState == FormWindowState.Maximized))
			{
				this.bool_5 = false;
				this.method_28(true);
			}
			else
			{
				this.method_28(true);
			}
		}

		// Token: 0x06001BAD RID: 7085 RVA: 0x0000B76A File Offset: 0x0000996A
		private void MainForm_MouseWheel(object sender, MouseEventArgs e)
		{
			if (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsScrollableCtrlFocused)
			{
				if (e.Delta > 0)
				{
					this.method_50();
				}
				else
				{
					this.method_51();
				}
			}
		}

		// Token: 0x06001BAE RID: 7086 RVA: 0x000C1688 File Offset: 0x000BF888
		protected void WndProc(ref Message m)
		{
			int num = 125;
			int num2 = m.WParam.ToInt32() & 65520;
			if (m.Msg == 274 && (num2 == 61488 || num2 == 61472 || num2 == 61728))
			{
				this.method_29();
				this.method_28(false);
				if (num2 == 61488 || num2 == 61728)
				{
					this.method_191();
					num = this.bar_0.DockedSite.Height;
					if (num2 == 61488)
					{
						this.bool_4 = true;
					}
					else
					{
						this.bool_5 = true;
					}
				}
			}
			else
			{
				int num3 = m.WParam.ToInt32();
				if ((num3 == 513 || num3 == 516) && this.class43_0 != null)
				{
					this.class43_0.method_2();
				}
			}
			base.WndProc(ref m);
			if (m.Msg == 274 && (num2 == 61488 || num2 == 61728))
			{
				this.method_193();
				this.method_30();
				if (!this.method_198() && this.bar_0.Docked && this.bar_0.DockedSite.Height != num)
				{
					this.bar_0.DockedSite.Height = num;
				}
			}
		}

		// Token: 0x06001BAF RID: 7087 RVA: 0x000C17C0 File Offset: 0x000BF9C0
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			Keys keys = (Keys)msg.WParam.ToInt32();
			if (msg.Msg == 257 && (keys == Keys.Left || keys == Keys.Up))
			{
				this.int_0 = 0;
			}
			bool result;
			if ((this.class304_1.Focused || this.class304_0.Focused) && ((keyData >= Keys.D0 && keyData <= Keys.D9) || keyData == Keys.OemPeriod))
			{
				result = base.ProcessCmdKey(ref msg, keyData);
			}
			else
			{
				if (keys == Keys.Escape)
				{
					Base.UI.smethod_156();
					if (Base.UI.Chart.IsSingleFixedContent && !Base.UI.IsInCreateNewPageState && !Base.UI.IsInDrawObjMode && !Base.UI.IsInDrawOdrMode && Base.UI.SelectedChtCtrl != null)
					{
						if (Base.UI.SelectedChtCtrl is ChtCtrl_Tick)
						{
							Base.UI.smethod_153(Base.UI.SelectedChtCtrl);
						}
						else if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
						{
							Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
						}
					}
				}
				if (keys == Keys.Space && Base.UI.TransTabs != null && Base.UI.TransTabs.IsInVideoPage)
				{
					Base.UI.TransTabs.method_133();
					result = base.ProcessCmdKey(ref msg, keyData);
				}
				else if (keyData == Class210.smethod_3(Enum3.const_0))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_OpenLong, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_1))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_CloseLong, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_2))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_OpenShort, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_3))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_CloseShort, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_4))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_OpenLong);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_5))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_CloseLong);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_6))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_OpenShort);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_7))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_CloseShort);
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_8))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						Base.Trading.smethod_55();
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_9))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						Base.Trading.smethod_74();
					}
					result = true;
				}
				else if (keyData == Class210.smethod_3(Enum3.const_10))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_201();
					}
					result = true;
				}
				else
				{
					if (keyData == Class210.smethod_3(Enum3.const_11))
					{
						if (this.sliderItem_0.Value > 1)
						{
							SliderItem sliderItem = this.sliderItem_0;
							int value = sliderItem.Value;
							sliderItem.Value = value - 1;
							return true;
						}
					}
					else if (keyData == Class210.smethod_3(Enum3.const_12))
					{
						if (this.sliderItem_0.Value < this.sliderItem_0.Maximum)
						{
							SliderItem sliderItem2 = this.sliderItem_0;
							int value = sliderItem2.Value;
							sliderItem2.Value = value + 1;
							return true;
						}
					}
					else
					{
						if (keyData == Class210.smethod_3(Enum3.const_13))
						{
							if (!Base.UI.IsInCreateNewPageState)
							{
								this.method_54();
							}
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_14))
						{
							Base.UI.smethod_117();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_15))
						{
							Base.UI.smethod_120();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_16))
						{
							this.method_122(Enum7.const_0);
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_17))
						{
							this.method_122(Enum7.const_1);
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_18))
						{
							this.method_55();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_19))
						{
							Base.UI.smethod_115();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_20))
						{
							this.method_81();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_21))
						{
							Base.UI.smethod_116();
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_22))
						{
							Base.UI.smethod_179(null);
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_23))
						{
							ChtCtrl chtCtrl = Base.UI.SelectedChtCtrl;
							if (chtCtrl == null)
							{
								chtCtrl = Base.UI.smethod_37();
							}
							if (chtCtrl != null)
							{
								BaoDianMgr.smethod_6(chtCtrl, null);
							}
							return true;
						}
						if (keyData == Class210.smethod_3(Enum3.const_24))
						{
							Base.UI.smethod_81();
							return true;
						}
						if (keyData <= Keys.Delete)
						{
							switch (keyData)
							{
							case Keys.Left:
								this.method_52();
								break;
							case Keys.Up:
								this.method_50();
								break;
							case Keys.Right:
								this.method_53();
								break;
							case Keys.Down:
								this.method_51();
								break;
							default:
								if (keyData == Keys.Delete)
								{
									this.method_56();
								}
								break;
							}
						}
						else if (keyData != (Keys.LButton | Keys.MButton | Keys.Space | Keys.Shift))
						{
							if (keyData == (Keys.LButton | Keys.RButton | Keys.MButton | Keys.Space | Keys.Shift))
							{
								this.method_49();
							}
						}
						else
						{
							this.method_48();
						}
					}
					result = base.ProcessCmdKey(ref msg, keyData);
				}
			}
			return result;
		}

		// Token: 0x06001BB0 RID: 7088 RVA: 0x0000B798 File Offset: 0x00009998
		private void MainForm_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.class43_0 != null)
			{
				this.class43_0.method_2();
			}
		}

		// Token: 0x06001BB1 RID: 7089 RVA: 0x000C1CB8 File Offset: 0x000BFEB8
		private void MainForm_KeyPress(object sender, KeyPressEventArgs e)
		{
			MainForm.Class331 @class = new MainForm.Class331();
			if (((!this.class304_1.Focused && !this.class304_0.Focused) || (!char.IsDigit(e.KeyChar) && e.KeyChar != '.')) && !Base.UI.IsInCreateNewPageState && (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsDateTimePickersFocused) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control && Control.ModifierKeys != Keys.Alt && (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsInInputState))
			{
				List<QuickWndItem> quickWndItemList = Base.UI.QuickWndItemList;
				string text = e.KeyChar.ToString();
				@class.string_0 = text.ToLower();
				IEnumerable<QuickWndItem> enumerable = quickWndItemList.Where(new Func<QuickWndItem, bool>(@class.method_0));
				if (enumerable.Any<QuickWndItem>())
				{
					if (this.form18_0 == null)
					{
						try
						{
							this.form18_0 = new Form18(quickWndItemList, enumerable, text);
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
							goto IL_197;
						}
						this.form18_0.StartPosition = FormStartPosition.Manual;
						this.form18_0.QuickWndItemSelected += this.method_207;
						this.form18_0.LostFocus += this.form18_0_LostFocus;
					}
					else
					{
						this.form18_0.method_3(quickWndItemList, enumerable, text);
						if (this.form18_0.Visible)
						{
							this.form18_0.Visible = false;
						}
					}
					this.form18_0.Location = this.method_206();
					this.form18_0.Owner = this;
					try
					{
						this.form18_0.ShowDialog();
					}
					catch (Exception exception_2)
					{
						Class184.smethod_0(exception_2);
					}
				}
				IL_197:;
			}
		}

		// Token: 0x06001BB2 RID: 7090 RVA: 0x0000B7AF File Offset: 0x000099AF
		private void MainForm_Deactivate(object sender, EventArgs e)
		{
			if (this.timer_0 != null)
			{
				this.timer_0.Stop();
				this.timer_0.Enabled = false;
			}
			if (this.class43_0 != null)
			{
				this.class43_0.Enable = false;
			}
		}

		// Token: 0x06001BB3 RID: 7091 RVA: 0x000C1E7C File Offset: 0x000C007C
		private void MainForm_Activated(object sender, EventArgs e)
		{
			if (base.WindowState == FormWindowState.Minimized)
			{
				this.method_28(true);
			}
			if (this.timer_0 != null)
			{
				this.timer_0.Enabled = true;
				this.timer_0.Start();
			}
			if (this.class43_0 != null)
			{
				this.class43_0.Enable = true;
			}
		}

		// Token: 0x06001BB4 RID: 7092 RVA: 0x000C1ED0 File Offset: 0x000C00D0
		private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			Base.UI.smethod_156();
			if (e.CloseReason == CloseReason.UserClosing)
			{
				if (Base.UI.Form.IfConfirmQuit && MessageBox.Show(Class521.smethod_0(11947), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
				{
					e.Cancel = true;
				}
				else
				{
					try
					{
						CfmmcRecImporter.smethod_43();
					}
					catch (Exception exception_)
					{
						Class184.smethod_0(exception_);
					}
					this.timer_1.Stop();
					if (this.class43_0 != null)
					{
						this.class43_0.method_3();
					}
					Base.UI.Form.IsStarted = false;
					base.Hide();
					Base.UI.smethod_177(Class521.smethod_0(73600), this.method_4());
					BaoDianMgr.smethod_8();
					if (Base.UI.TransTabs != null)
					{
						Base.UI.TransTabs.method_131();
					}
					Base.UI.smethod_177(Class521.smethod_0(73637), this.method_4());
					Base.Data.smethod_61();
					Base.Acct.smethod_13();
					if (Base.UI.Form.IfSaveSpeedOnQuit)
					{
						try
						{
							Base.UI.Form.TimerSpeed = this.sliderItem_0.Value;
						}
						catch
						{
							Base.UI.Form.TimerSpeed = 10;
						}
					}
					try
					{
						Base.UI.Form.TradingUnits = Convert.ToInt32(this.class304_0.Value);
					}
					catch
					{
						Base.UI.Form.TradingUnits = 1;
					}
					if (base.WindowState != FormWindowState.Minimized)
					{
						if (Base.UI.Form.IfAutoSavePageOnExit)
						{
							if (this.bar_0.Enabled)
							{
								if (!Base.UI.Form.IsTransTabBarMaximized)
								{
									Base.UI.smethod_81();
								}
							}
							else if (!Base.UI.Form.IsTransTabMaximized)
							{
								Base.UI.smethod_81();
							}
						}
						if (Base.UI.Form.IfSaveWindowOnQuit)
						{
							Base.UI.Form.DotNetBarLayoutString = this.dotNetBarManager_0.LayoutDefinition;
							Base.UI.smethod_45(this);
						}
					}
					Base.UI.smethod_154();
					Base.UI.smethod_47();
					if (Utility.GetMajorVerFromTExVer(TApp.Ver) >= 4.2 && !TApp.IsTrialUser && !Base.UI.Form.BackupSyncDisabled && !Base.UI.Form.BackupSyncNotOnExit)
					{
						BkupSyncMgr.smethod_4(Base.UI.SplashScreen);
					}
					UpdateManager instance = UpdateManager.Instance;
					if (instance.State == UpdateManager.UpdateProcessState.Prepared)
					{
						Base.UI.smethod_177(Class521.smethod_0(73674), this.method_4());
						Class48.smethod_3(Class24.AppBackgroudUpdating, Class521.smethod_0(73353) + TApp.Ver);
						Base.UI.Form.LastAppUpdDateTime = new DateTime?(TApp.SrvParams.AppUpdInfo.DateTime);
						Base.UI.smethod_47();
						try
						{
							instance.ApplyUpdates(false);
							Class48.smethod_2(Class24.AppBackgroudUpdated);
						}
						catch (Exception exception_2)
						{
							Class48.smethod_4(exception_2, true, null);
							try
							{
								instance.CleanUp();
							}
							catch
							{
							}
						}
					}
					Class48.smethod_5(80);
					Class48.smethod_2(Class24.AppExited);
					Base.UI.smethod_178();
				}
			}
		}

		// Token: 0x06001BB5 RID: 7093 RVA: 0x000C21A8 File Offset: 0x000C03A8
		private void method_6()
		{
			string infoHTML = TApp.SrvParams.InfoHTML;
			if (!string.IsNullOrEmpty(infoHTML))
			{
				new Form16(infoHTML)
				{
					Owner = this,
					StartPosition = FormStartPosition.CenterParent
				}.ShowDialog();
			}
		}

		// Token: 0x06001BB6 RID: 7094 RVA: 0x0000B7E6 File Offset: 0x000099E6
		private void method_7(object sender, MsgEventArgs e)
		{
			Base.UI.Form.CurrentPageName = e.Msg;
		}

		// Token: 0x06001BB7 RID: 7095 RVA: 0x0000B7FA File Offset: 0x000099FA
		private void method_8(object sender, EventArgs e)
		{
			this.smethod_0();
			base.SuspendLayout();
			this.method_9();
			this.method_10();
			Base.UI.smethod_33();
			base.ResumeLayout();
			this.smethod_1();
			base.TitleText = base.TitleText;
		}

		// Token: 0x06001BB8 RID: 7096 RVA: 0x000C21E4 File Offset: 0x000C03E4
		private void method_9()
		{
			StyleManager.Style = eStyle.Metro;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class181.color_1, Class181.color_1);
				ColorScheme colorScheme = this.GetColorScheme();
				colorScheme.BarCaptionText = Class181.color_10;
				colorScheme.BarCaptionInactiveText = Class181.color_8;
				colorScheme.CustomizeText = Class181.color_10;
				colorScheme.ItemText = Class181.color_10;
				colorScheme.ItemCheckedBorder = Class181.color_7;
				colorScheme.BarCaptionBackground = Color.FromArgb(32, 32, 32);
			}
			else
			{
				if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
				{
					StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class181.color_9, Class181.color_9);
				}
				else
				{
					StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class181.color_15, Class181.color_15);
				}
				ColorScheme colorScheme2 = this.GetColorScheme();
				colorScheme2.BarCaptionText = Class181.color_1;
				colorScheme2.BarCaptionInactiveText = Class181.color_4;
				colorScheme2.CustomizeText = Class181.color_1;
				colorScheme2.ItemText = Class181.color_1;
				colorScheme2.ItemCheckedBorder = Class181.color_7;
				colorScheme2.BarCaptionBackground = Color.FromArgb(235, 235, 235);
			}
			base.TitleText = base.TitleText;
		}

		// Token: 0x06001BB9 RID: 7097 RVA: 0x000C2308 File Offset: 0x000C0508
		protected Color GetTitleColor(ItemPaintArgs p)
		{
			Color result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				if (base.NonClientActive)
				{
					result = Class181.color_8;
				}
				else
				{
					result = Class181.color_7;
				}
			}
			else if (base.NonClientActive)
			{
				result = Class181.color_1;
			}
			else
			{
				result = Class181.color_4;
			}
			return result;
		}

		// Token: 0x06001BBA RID: 7098 RVA: 0x000C2354 File Offset: 0x000C0554
		private void method_10()
		{
			this.method_88();
			this.method_44();
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.panel_0.BackColor = Class181.color_1;
				this.labelItem_0.ForeColor = Class181.color_8;
				this.labelItem_1.ForeColor = Class181.color_8;
				this.labelItem_2.ForeColor = Class181.color_8;
				this.buttonItem_4.ForeColor = Class181.color_10;
				this.buttonItem_4.SubItemTriangleColor = Class181.color_9;
				this.buttonItem_11.ForeColor = Class181.color_10;
				this.sliderItem_0.TextColor = Class181.color_8;
				this.buttonItem_35.ForeColor = Class181.color_10;
				this.buttonItem_5.ForeColor = Class181.color_10;
				this.buttonItem_5.SubItemTriangleColor = Class181.color_9;
				this.buttonItem_56.ForeColor = Class181.color_9;
				this.buttonItem_56.SubItemTriangleColor = Class181.color_8;
				this.textBoxItem_0.TextBox.ForeColor = Class181.color_8;
				this.textBoxItem_1.TextBox.ForeColor = Class181.color_8;
				this.textBoxItem_2.TextBox.ForeColor = Class181.color_8;
				this.buttonItem_23.ForeColor = Class181.color_9;
				this.buttonItem_24.ForeColor = Class181.color_9;
				this.buttonItem_25.ForeColor = Class181.color_9;
				this.buttonItem_26.ForeColor = Class181.color_9;
				this.buttonItem_27.ForeColor = Class181.color_9;
				this.buttonItem_28.ForeColor = Class181.color_9;
				this.buttonItem_29.ForeColor = Class181.color_9;
				this.buttonItem_30.ForeColor = Class181.color_9;
				this.buttonItem_49.ForeColor = Class181.color_9;
				this.buttonItem_31.ForeColor = Class181.color_9;
				this.buttonItem_32.ForeColor = Class181.color_9;
				this.buttonItem_33.ForeColor = Class181.color_9;
				this.buttonItem_23.HotForeColor = Class181.color_1;
				this.buttonItem_24.HotForeColor = Class181.color_1;
				this.buttonItem_25.HotForeColor = Class181.color_1;
				this.buttonItem_26.HotForeColor = Class181.color_1;
				this.buttonItem_27.HotForeColor = Class181.color_1;
				this.buttonItem_28.HotForeColor = Class181.color_1;
				this.buttonItem_29.HotForeColor = Class181.color_1;
				this.buttonItem_30.HotForeColor = Class181.color_1;
				this.buttonItem_49.HotForeColor = Class181.color_1;
				this.buttonItem_31.HotForeColor = Class181.color_1;
				this.buttonItem_32.HotForeColor = Class181.color_1;
				this.buttonItem_33.HotForeColor = Class181.color_1;
				this.buttonItem_0.HotForeColor = Color.OrangeRed;
				this.buttonItem_0.HotFontBold = true;
				this.buttonItem_1.HotForeColor = Color.LightGreen;
				this.buttonItem_1.HotFontBold = true;
				this.buttonItem_2.HotForeColor = Color.LightGreen;
				this.buttonItem_2.HotFontBold = true;
				this.buttonItem_3.HotForeColor = Color.OrangeRed;
				this.buttonItem_3.HotFontBold = true;
				this.labelItem_0.TextAlignment = StringAlignment.Far;
				this.labelItem_1.TextAlignment = StringAlignment.Far;
				this.class304_0.BackColor = Class181.color_1;
				this.class304_1.BackColor = Class181.color_1;
				this.labelItem_9.ForeColor = Class181.color_8;
				this.labelItem_3.ForeColor = Class181.color_8;
				this.labelItem_10.ForeColor = Class181.color_8;
				this.labelItem_11.ForeColor = Class181.color_8;
				this.labelItem_6.ForeColor = Class181.color_8;
				this.buttonItem_34.ForeColor = Class181.color_10;
				this.buttonItem_37.ForeColor = Class181.color_10;
				this.buttonItem_38.ForeColor = Class181.color_10;
			}
			else
			{
				if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
				{
					this.panel_0.BackColor = Class181.color_9;
				}
				else
				{
					this.panel_0.BackColor = Class181.color_14;
				}
				this.labelItem_0.ForeColor = Class181.color_1;
				this.labelItem_1.ForeColor = Class181.color_1;
				this.labelItem_2.ForeColor = Class181.color_1;
				this.buttonItem_4.ForeColor = Class181.color_1;
				this.buttonItem_4.SubItemTriangleColor = Class181.color_1;
				this.buttonItem_11.ForeColor = Class181.color_1;
				this.sliderItem_0.TextColor = Class181.color_1;
				this.buttonItem_35.ForeColor = Class181.color_1;
				this.buttonItem_5.ForeColor = Class181.color_1;
				this.buttonItem_5.SubItemTriangleColor = Class181.color_1;
				this.buttonItem_56.ForeColor = Class181.color_1;
				this.buttonItem_56.SubItemTriangleColor = Class181.color_1;
				this.textBoxItem_0.TextBox.ForeColor = Class181.color_1;
				this.textBoxItem_1.TextBox.ForeColor = Class181.color_1;
				this.textBoxItem_2.TextBox.ForeColor = Class181.color_1;
				this.buttonItem_23.ForeColor = Class181.color_1;
				this.buttonItem_24.ForeColor = Class181.color_1;
				this.buttonItem_25.ForeColor = Class181.color_1;
				this.buttonItem_26.ForeColor = Class181.color_1;
				this.buttonItem_27.ForeColor = Class181.color_1;
				this.buttonItem_28.ForeColor = Class181.color_1;
				this.buttonItem_29.ForeColor = Class181.color_1;
				this.buttonItem_30.ForeColor = Class181.color_1;
				this.buttonItem_49.ForeColor = Class181.color_1;
				this.buttonItem_31.ForeColor = Class181.color_1;
				this.buttonItem_32.ForeColor = Class181.color_1;
				this.buttonItem_33.ForeColor = Class181.color_1;
				this.buttonItem_23.HotForeColor = Class181.color_1;
				this.buttonItem_24.HotForeColor = Class181.color_1;
				this.buttonItem_25.HotForeColor = Class181.color_1;
				this.buttonItem_26.HotForeColor = Class181.color_1;
				this.buttonItem_27.HotForeColor = Class181.color_1;
				this.buttonItem_28.HotForeColor = Class181.color_1;
				this.buttonItem_29.HotForeColor = Class181.color_1;
				this.buttonItem_30.HotForeColor = Class181.color_1;
				this.buttonItem_49.HotForeColor = Class181.color_1;
				this.buttonItem_31.HotForeColor = Class181.color_1;
				this.buttonItem_32.HotForeColor = Class181.color_1;
				this.buttonItem_33.HotForeColor = Class181.color_1;
				this.buttonItem_0.HotForeColor = Color.OrangeRed;
				this.buttonItem_0.HotFontBold = true;
				this.buttonItem_1.HotForeColor = Color.LightGreen;
				this.buttonItem_1.HotFontBold = true;
				this.buttonItem_2.HotForeColor = Color.LightGreen;
				this.buttonItem_2.HotFontBold = true;
				this.buttonItem_3.HotForeColor = Color.OrangeRed;
				this.buttonItem_3.HotFontBold = true;
				this.labelItem_0.TextAlignment = StringAlignment.Far;
				this.labelItem_1.TextAlignment = StringAlignment.Far;
				this.class304_0.BackColor = Color.White;
				this.class304_1.BackColor = Color.White;
				this.labelItem_9.ForeColor = Class181.color_1;
				this.labelItem_3.ForeColor = Class181.color_1;
				this.labelItem_10.ForeColor = Class181.color_1;
				this.labelItem_11.ForeColor = Class181.color_1;
				this.labelItem_6.ForeColor = Class181.color_1;
				this.buttonItem_34.ForeColor = Class181.color_1;
				this.buttonItem_37.ForeColor = Class181.color_1;
				this.buttonItem_38.ForeColor = Class181.color_1;
			}
		}

		// Token: 0x06001BBB RID: 7099 RVA: 0x00006851 File Offset: 0x00004A51
		private void buttonItem_20_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Classic);
		}

		// Token: 0x06001BBC RID: 7100 RVA: 0x00006865 File Offset: 0x00004A65
		private void buttonItem_21_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Modern);
		}

		// Token: 0x06001BBD RID: 7101 RVA: 0x0000685B File Offset: 0x00004A5B
		private void buttonItem_39_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Yellow);
		}

		// Token: 0x06001BBE RID: 7102 RVA: 0x000C2B24 File Offset: 0x000C0D24
		private void buttonItem_19_PopupOpen(object sender, EventArgs e)
		{
			foreach (object obj in this.buttonItem_19.SubItems)
			{
				((ButtonItem)obj).Checked = false;
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.buttonItem_20.Checked = true;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Modern)
			{
				this.buttonItem_21.Checked = true;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				this.buttonItem_39.Checked = true;
			}
		}

		// Token: 0x06001BBF RID: 7103 RVA: 0x000C2BD4 File Offset: 0x000C0DD4
		private void method_11(bool bool_8)
		{
			AppUpdInfo appUpdInfo = TApp.SrvParams.AppUpdInfo;
			if (appUpdInfo != null && !string.IsNullOrEmpty(appUpdInfo.Feed))
			{
				if (bool_8)
				{
					Version texVersion = TApp.TExVersion;
					Version v = new Version(TApp.SrvParams.AppUpdInfo.Ver);
					if (texVersion <= v)
					{
						this.method_12(false, false);
					}
				}
				else
				{
					this.method_12(false, false);
				}
			}
		}

		// Token: 0x06001BC0 RID: 7104 RVA: 0x000C2C38 File Offset: 0x000C0E38
		private int method_12(bool bool_8, bool bool_9)
		{
			if (TApp.SrvParams.AppUpdInfo != null && !string.IsNullOrEmpty(TApp.SrvParams.AppUpdInfo.Feed))
			{
				if (bool_9)
				{
					Base.UI.smethod_177(Class521.smethod_0(73711), this.method_4());
				}
				string feed = TApp.SrvParams.AppUpdInfo.Feed;
				UpdateManager instance = UpdateManager.Instance;
				instance.UpdateSource = new SimpleWebSource();
				instance.Config.TempFolder = Path.Combine(TApp.string_10, Class521.smethod_0(29061));
				instance.ReinstateIfRestarted();
				IUpdateSource iupdateSource_ = new MemorySource(feed);
				int num = 0;
				int result;
				try
				{
					num = this.method_13(iupdateSource_);
					goto IL_EC;
				}
				catch (Exception ex)
				{
					Base.UI.smethod_178();
					if (ex is InvalidOperationException && ex.Message.Contains(Class521.smethod_0(73748)))
					{
						MessageBox.Show(Class521.smethod_0(73785), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					result = 0;
				}
				return result;
				IL_EC:
				if (bool_9)
				{
					Base.UI.smethod_178();
				}
				if (num > 0)
				{
					bool flag = true;
					if (bool_8)
					{
						flag = false;
						if (MessageBox.Show(string.Concat(new string[]
						{
							Class521.smethod_0(73850),
							TApp.SrvParams.AppUpdInfo.Ver,
							Class521.smethod_0(73887),
							Environment.NewLine,
							Environment.NewLine,
							Class521.smethod_0(73920)
						}), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							flag = true;
						}
						else
						{
							instance.CleanUp();
						}
					}
					if (flag)
					{
						if (bool_9)
						{
							Base.UI.smethod_177(Class521.smethod_0(74114), this.method_4());
						}
						if (feed.Contains(Class521.smethod_0(74143)) || feed.Contains(Class521.smethod_0(74152)) || feed.Contains(Class521.smethod_0(74161)))
						{
							this.bool_3 = true;
						}
						this.labelItem_8.Text = Class521.smethod_0(74174) + num.ToString() + Class521.smethod_0(74183);
						instance.Config.UpdateProcessName = Class521.smethod_0(74248);
						instance.Config.UpdateExecutableName = Class521.smethod_0(74248);
						instance.BeginPrepareUpdates(new AsyncCallback(this.method_14), null);
						this.string_0 = instance.Config.TempFolder;
						if (bool_9)
						{
							Base.UI.smethod_178();
						}
						return num;
					}
				}
				else
				{
					if (bool_8)
					{
						MessageBox.Show(Class521.smethod_0(74265), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					this.labelItem_8.Text = Class521.smethod_0(74346);
				}
			}
			return 0;
		}

		// Token: 0x06001BC1 RID: 7105 RVA: 0x000C2EEC File Offset: 0x000C10EC
		private int method_13(IUpdateSource iupdateSource_0)
		{
			UpdateManager instance = UpdateManager.Instance;
			int result = 0;
			try
			{
				instance.CheckForUpdates(iupdateSource_0);
				result = instance.UpdatesAvailable;
			}
			catch (Exception ex)
			{
				NAppUpdateException ex2 = ex as NAppUpdateException;
				throw;
			}
			return result;
		}

		// Token: 0x06001BC2 RID: 7106 RVA: 0x000C2F30 File Offset: 0x000C1130
		private void method_14(IAsyncResult iasyncResult_0)
		{
			MainForm.Class332 @class = new MainForm.Class332();
			@class.mainForm_0 = this;
			try
			{
				((UpdateProcessAsyncResult)iasyncResult_0).EndInvoke();
			}
			catch (Exception ex)
			{
				MainForm.Class333 class2 = new MainForm.Class333();
				class2.class332_0 = @class;
				Exception exception_ = ex;
				class2.exception_0 = exception_;
				try
				{
					this.labelItem_8.Invoke(new Action(class2.method_0));
					this.method_15();
					return;
				}
				catch
				{
					return;
				}
			}
			@class.string_0 = Class521.smethod_0(74411);
			if (this.bool_3)
			{
				@class.string_0 += Class521.smethod_0(74452);
			}
			else
			{
				@class.string_0 += Class521.smethod_0(74497);
			}
			this.labelItem_8.Invoke(new Action(@class.method_0));
			if (!string.IsNullOrEmpty(this.string_0) && Directory.Exists(this.string_0))
			{
				string text = this.string_0 + Class521.smethod_0(74502);
				if (!Utility.FileExists(text))
				{
					string[] contents = new string[]
					{
						Class521.smethod_0(74527),
						Class521.smethod_0(74556),
						Class521.smethod_0(74577),
						Class521.smethod_0(74646),
						Class521.smethod_0(74695),
						Class521.smethod_0(74752),
						Class521.smethod_0(74769)
					};
					File.WriteAllLines(text, contents);
				}
			}
		}

		// Token: 0x06001BC3 RID: 7107 RVA: 0x000C30C4 File Offset: 0x000C12C4
		private void method_15()
		{
			try
			{
				UpdateManager.Instance.CleanUp();
			}
			catch
			{
			}
		}

		// Token: 0x06001BC4 RID: 7108 RVA: 0x000C30F4 File Offset: 0x000C12F4
		private void method_16()
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.TUnitsInputChanged -= this.method_143;
				Base.UI.TransTabs.TPriceInputChanged -= this.method_144;
				Base.UI.TransTabs.ChangeToHisTransDTRequested -= this.method_145;
				Base.UI.TransTabs.MsgNotifyNeeded -= this.method_146;
				Base.UI.TransTabs.Dispose();
				Base.UI.TransTabs = null;
			}
			Base.UI.smethod_112();
			if (Base.UI.TransTabs != null)
			{
				if (Base.UI.TransTabCtrl != null)
				{
					Base.UI.TransTabCtrl.MaxButtonVisible = !Base.UI.Chart.IsSingleTransTabCtrl;
					this.panel_0.ResumeLayout();
				}
				if (this.bar_0.Visible)
				{
					this.bar_0.Hide();
				}
				if (this.bar_0.Enabled)
				{
					this.bar_0.Enabled = false;
				}
			}
			else
			{
				if (!this.bar_0.Enabled)
				{
					this.bar_0.Enabled = true;
				}
				TransTabs transTabs = new TransTabs(null);
				Base.UI.TransTabs = transTabs;
				this.panelDockContainer_0.Controls.Add(transTabs);
				if (this.bool_2 && this.buttonItem_34.Text == Class521.smethod_0(1984))
				{
					Base.UI.Form.IfShowTransTabsBar = true;
				}
				if (Base.UI.Form.IfShowTransTabsBar)
				{
					if (!this.bar_0.Visible)
					{
						this.bar_0.DockSide = Base.UI.Form.AcctTransBar_DockSide;
						if (!this.bar_0.AutoHide)
						{
							this.bar_0.Show();
							this.panelDockContainer_0.Visible = true;
						}
						Base.UI.IsTransTabsVisible = true;
					}
					else if (!this.panelDockContainer_0.Visible)
					{
						this.panelDockContainer_0.Visible = true;
					}
				}
				else if (this.bar_0.Visible)
				{
					this.bar_0.Hide();
					Base.UI.IsTransTabsVisible = false;
				}
			}
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.IfAutoOpenClose = Base.UI.Form.IfAutoOpenCloseInTradingTab;
				Base.UI.TransTabs.IfFollowPrice = Base.UI.Form.IfFollowPrcInTradingTab;
				Base.UI.TransTabs.TUnitsInputChanged += this.method_143;
				Base.UI.TransTabs.TPriceInputChanged += this.method_144;
				Base.UI.TransTabs.ChangeToHisTransDTRequested += this.method_145;
				Base.UI.TransTabs.MsgNotifyNeeded += this.method_146;
			}
			Base.UI.smethod_134(true);
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			if (Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.TransTabs.method_63();
			}
			this.method_132();
			this.method_17();
			this.method_45();
		}

		// Token: 0x06001BC5 RID: 7109 RVA: 0x0000B833 File Offset: 0x00009A33
		private void bar_0_EnabledChanged(object sender, EventArgs e)
		{
			if (this.bar_0.Enabled)
			{
				this.method_45();
			}
		}

		// Token: 0x06001BC6 RID: 7110 RVA: 0x000C33B0 File Offset: 0x000C15B0
		private void method_17()
		{
			this.buttonItem_63.Click += this.buttonItem_63_Click;
			this.buttonItem_64.Click += this.buttonItem_64_Click;
			this.buttonItem_71.Click += this.buttonItem_71_Click;
			this.method_19();
			this.method_22();
		}

		// Token: 0x06001BC7 RID: 7111 RVA: 0x0000B84A File Offset: 0x00009A4A
		private void buttonItem_64_Click(object sender, EventArgs e)
		{
			this.method_18(Class521.smethod_0(74794));
		}

		// Token: 0x06001BC8 RID: 7112 RVA: 0x0000B85E File Offset: 0x00009A5E
		private void buttonItem_63_Click(object sender, EventArgs e)
		{
			this.method_18(Class521.smethod_0(74803));
		}

		// Token: 0x06001BC9 RID: 7113 RVA: 0x0000B872 File Offset: 0x00009A72
		private void buttonItem_71_Click(object sender, EventArgs e)
		{
			this.method_18(Class521.smethod_0(74812));
		}

		// Token: 0x06001BCA RID: 7114 RVA: 0x0000B886 File Offset: 0x00009A86
		private void method_18(string string_6)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136(string_6);
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BCB RID: 7115 RVA: 0x000C3410 File Offset: 0x000C1610
		private void method_19()
		{
			if (Base.UI.TransTabs != null)
			{
				this.buttonItem_61.SubItems.Clear();
				foreach (object obj in Base.UI.TransTabs.MarketTabsCollection)
				{
					TabItem tabItem = (TabItem)obj;
					ButtonItem buttonItem = new ButtonItem();
					buttonItem.Text = tabItem.Text;
					buttonItem.Click += this.method_20;
					this.buttonItem_61.SubItems.Add(buttonItem);
				}
			}
		}

		// Token: 0x06001BCC RID: 7116 RVA: 0x000C34BC File Offset: 0x000C16BC
		private void method_20(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.SelectedTabIndex = 0;
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
				Base.UI.TransTabs.MktSelectedTab = Base.UI.TransTabs.MarketTabsCollection[buttonItem.Text];
			}
		}

		// Token: 0x06001BCD RID: 7117 RVA: 0x000C3518 File Offset: 0x000C1718
		private void method_21(string string_6 = null)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136(Class521.smethod_0(71429));
				if (!string.IsNullOrEmpty(string_6))
				{
					Base.UI.TransTabs.method_138(string_6);
				}
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BCE RID: 7118 RVA: 0x000C3568 File Offset: 0x000C1768
		private void method_22()
		{
			foreach (object obj in this.buttonItem_62.SubItems)
			{
				((ButtonItem)obj).Click += this.method_23;
			}
		}

		// Token: 0x06001BCF RID: 7119 RVA: 0x000C35D4 File Offset: 0x000C17D4
		private void method_23(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136(Class521.smethod_0(68360));
				Base.UI.TransTabs.method_139(buttonItem.Text);
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BD0 RID: 7120 RVA: 0x000C3628 File Offset: 0x000C1828
		private void method_24(DateTime? nullable_1)
		{
			if (base.WindowState == FormWindowState.Minimized)
			{
				MainForm.ShowWindow(base.Handle, 9);
			}
			this.panel_0.SuspendLayout();
			this.panel_0.Controls.Clear();
			Base.UI.smethod_106(this.panel_0);
			Base.UI.smethod_107(nullable_1);
			Base.UI.ChtCtrlsRestored += this.method_25;
		}

		// Token: 0x06001BD1 RID: 7121 RVA: 0x000C368C File Offset: 0x000C188C
		private void method_25(object sender, EventArgs e)
		{
			foreach (ChtCtrl chtCtrl_ in Base.UI.ChtCtrlList)
			{
				this.method_26(chtCtrl_);
			}
			this.panel_0.ResumeLayout();
			if (!this.bool_2)
			{
				this.method_35();
			}
			else
			{
				Base.UI.smethod_21();
				Base.UI.smethod_133();
			}
			if (Base.UI.ChtCtrlList.Count == 1)
			{
				Base.UI.ChtCtrlList[0].IsSelected = true;
			}
		}

		// Token: 0x06001BD2 RID: 7122 RVA: 0x000C3724 File Offset: 0x000C1924
		private void method_26(ChtCtrl chtCtrl_0)
		{
			chtCtrl_0.Enter += this.method_36;
			chtCtrl_0.Leave += this.method_37;
			if (chtCtrl_0 is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)chtCtrl_0;
				chtCtrl_KLine.PeriodChanged += this.method_32;
				chtCtrl_KLine.AfterEnteringRetroMode += this.method_31;
			}
		}

		// Token: 0x06001BD3 RID: 7123 RVA: 0x000C3788 File Offset: 0x000C1988
		private void method_27(DateTime dateTime_0)
		{
			foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
			{
				chtCtrl.SymbDataSet = Base.Data.smethod_49(chtCtrl.SymbDataSet.SymblID, false);
				BackgroundWorker backgroundWorker = new BackgroundWorker();
				backgroundWorker.DoWork += this.method_33;
				backgroundWorker.RunWorkerCompleted += this.method_34;
				backgroundWorker.RunWorkerAsync(new Class180
				{
					chtCtrl_0 = chtCtrl,
					dateTime_0 = dateTime_0
				});
			}
		}

		// Token: 0x06001BD4 RID: 7124 RVA: 0x000C3830 File Offset: 0x000C1A30
		private void method_28(bool bool_8)
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					chtCtrl.Visible = bool_8;
				}
			}
		}

		// Token: 0x06001BD5 RID: 7125 RVA: 0x000C388C File Offset: 0x000C1A8C
		private void method_29()
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					if (chtCtrl.IsSelected)
					{
						chtCtrl.method_28();
					}
				}
			}
		}

		// Token: 0x06001BD6 RID: 7126 RVA: 0x000C38F0 File Offset: 0x000C1AF0
		private void method_30()
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					if (chtCtrl.IsSelected)
					{
						chtCtrl.method_27();
					}
				}
			}
		}

		// Token: 0x06001BD7 RID: 7127 RVA: 0x0000B8AE File Offset: 0x00009AAE
		private void method_31(EventArgs eventArgs_0)
		{
			this.IsInRetroMode = true;
		}

		// Token: 0x06001BD8 RID: 7128 RVA: 0x000C3954 File Offset: 0x000C1B54
		private void method_32(object sender, EventArgs e)
		{
			ChtCtrl_KLine chtCtrl_KLine = sender as ChtCtrl_KLine;
			if (chtCtrl_KLine.IsSelected)
			{
				this.method_62(chtCtrl_KLine);
			}
			if (Base.UI.Form.AutoPlayPeriodType != null && Base.UI.Form.IsJustSpanMoved && Base.UI.Form.SpanMoveChtCtrl != null && Base.UI.Form.SpanMoveChtCtrl == chtCtrl_KLine)
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(chtCtrl_KLine.PeriodType);
				Base.UI.Form.AutoPlayPeriodUnits = chtCtrl_KLine.PeriodUnits;
			}
			else if (!Base.UI.ChtCtrl_KLineList.Exists(new Predicate<ChtCtrl_KLine>(MainForm.<>c.<>9.method_0)))
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
				Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
			}
		}

		// Token: 0x06001BD9 RID: 7129 RVA: 0x000C3A24 File Offset: 0x000C1C24
		private void method_33(object sender, DoWorkEventArgs e)
		{
			Class180 @class = e.Argument as Class180;
			ChtCtrl chtCtrl_ = @class.chtCtrl_0;
			PeriodType periodType = chtCtrl_.HisDataPeriodSet.PeriodType;
			int? periodUnits = chtCtrl_.HisDataPeriodSet.PeriodUnits;
			int id;
			if (@class.symbDataSet_0 != null && @class.symbDataSet_0.CurrSymbol != null)
			{
				id = @class.symbDataSet_0.CurrSymbol.ID;
			}
			else
			{
				id = chtCtrl_.Symbol.ID;
			}
			SymbDataSet symbDataSet = Base.Data.smethod_49(id, true);
			HisDataPeriodSet hisDataPeriodSet_ = null;
			if (symbDataSet.HasValidDataSet)
			{
				hisDataPeriodSet_ = symbDataSet.method_58(periodType, periodUnits);
			}
			@class.chtCtrl_0 = chtCtrl_;
			@class.hisDataPeriodSet_0 = hisDataPeriodSet_;
			e.Result = @class;
		}

		// Token: 0x06001BDA RID: 7130 RVA: 0x000C3ACC File Offset: 0x000C1CCC
		private void method_34(object sender, RunWorkerCompletedEventArgs e)
		{
			Class180 @class = e.Result as Class180;
			HisDataPeriodSet hisDataPeriodSet_ = @class.hisDataPeriodSet_0;
			if (hisDataPeriodSet_ != null)
			{
				MainForm.Class334 class2 = new MainForm.Class334();
				ChtCtrl chtCtrl_ = @class.chtCtrl_0;
				chtCtrl_.HisDataPeriodSet = hisDataPeriodSet_;
				if (chtCtrl_ is ChtCtrl_KLine && chtCtrl_.HisDataPeriodSet != null)
				{
					((ChtCtrl_KLine)chtCtrl_).method_115();
				}
				DateTime dateTime_ = @class.dateTime_0;
				chtCtrl_.method_18(dateTime_);
				class2.symbDataSet_0 = chtCtrl_.SymbDataSet;
				if ((class2.symbDataSet_0.CurrHisDataSet != null && !class2.symbDataSet_0.IsCurrDateNotListedYet) || Base.UI.CurrTradingSymbol.ID != class2.symbDataSet_0.SymblID)
				{
					return;
				}
				try
				{
					Base.UI.CurrTradingSymbol = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(class2.method_0)).First<SymbDataSet>().CurrSymbol;
					return;
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
					return;
				}
			}
			Class184.smethod_0(new ArgumentNullException(Class521.smethod_0(74821)));
		}

		// Token: 0x06001BDB RID: 7131 RVA: 0x000C3BC4 File Offset: 0x000C1DC4
		private void method_35()
		{
			if (Base.Acct.CurrAccount.LastSymbIdAndDtList != null)
			{
				Base.UI.smethod_22(Base.Acct.CurrAccount.LastSymbDT);
				Base.UI.smethod_133();
			}
			else if (Base.Acct.CurrAccount.LastSymbDT != null)
			{
				this.method_46(Base.Acct.CurrAccount.LastSymbDT.Value);
			}
			else
			{
				Base.UI.smethod_20(Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(MainForm.<>c.<>9.method_1)).Max(new Func<SymbDataSet, DateTime>(MainForm.<>c.<>9.method_2)));
				Base.UI.smethod_133();
			}
		}

		// Token: 0x06001BDC RID: 7132 RVA: 0x000C3C80 File Offset: 0x000C1E80
		private void method_36(object sender, EventArgs e)
		{
			ChtCtrl chtCtrl = sender as ChtCtrl;
			this.method_62(chtCtrl);
			Base.UI.SelectedChtCtrl = chtCtrl;
		}

		// Token: 0x06001BDD RID: 7133 RVA: 0x0000B8B9 File Offset: 0x00009AB9
		private void method_37(object sender, EventArgs e)
		{
			this.method_62(null);
			Base.UI.SelectedChtCtrl = null;
		}

		// Token: 0x06001BDE RID: 7134 RVA: 0x0000B8CA File Offset: 0x00009ACA
		private void method_38(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				Base.UI.smethod_178();
				if (MessageBox.Show(Class521.smethod_0(74858), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_82();
				}
			}
		}

		// Token: 0x06001BDF RID: 7135 RVA: 0x0000B8FF File Offset: 0x00009AFF
		private void method_39(EventArgs18 eventArgs18_0)
		{
			this.method_26(eventArgs18_0.ChtCtrl);
		}

		// Token: 0x06001BE0 RID: 7136 RVA: 0x000C3CA4 File Offset: 0x000C1EA4
		private void method_40(EventArgs20 eventArgs20_0)
		{
			if (eventArgs20_0.Trigger is TransTabs)
			{
				this.bar_0.Enabled = true;
				Base.UI.Form.IfShowTransTabsBar = false;
				Base.UI.IsTransTabsVisible = false;
				Base.UI.SwitchedBehindTransTabs = (eventArgs20_0.Trigger as TransTabs);
				Base.UI.TransTabs = null;
				eventArgs20_0.Trigger = null;
			}
			if (eventArgs20_0.InfrontCtrl is ChtCtrl)
			{
				((ChtCtrl)eventArgs20_0.InfrontCtrl).Focus();
			}
			else if (eventArgs20_0.InfrontCtrl is TransTabs)
			{
				((TransTabs)eventArgs20_0.InfrontCtrl).ParentTransTabCtrl.Focus();
			}
			else if (eventArgs20_0.InfrontCtrl is TransTabCtrl)
			{
				((TransTabCtrl)eventArgs20_0.InfrontCtrl).Focus();
			}
		}

		// Token: 0x06001BE1 RID: 7137 RVA: 0x000C3D60 File Offset: 0x000C1F60
		private void method_41(object sender, EventArgs e)
		{
			if (this.panelDockContainer_0.Controls.Count > 0)
			{
				this.panelDockContainer_0.Controls.Clear();
			}
			this.bar_0.Enabled = false;
			this.bar_0.Hide();
			if (Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.TransTabs.method_63();
			}
			this.method_45();
		}

		// Token: 0x06001BE2 RID: 7138 RVA: 0x0000B90F File Offset: 0x00009B0F
		private void method_42(object sender, EventArgs e)
		{
			this.method_204();
		}

		// Token: 0x06001BE3 RID: 7139 RVA: 0x0000B919 File Offset: 0x00009B19
		private void method_43(object sender, EventArgs e)
		{
			this.method_44();
		}

		// Token: 0x06001BE4 RID: 7140 RVA: 0x000C3DD0 File Offset: 0x000C1FD0
		private void method_44()
		{
			decimal d = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(MainForm.<>c.<>9.method_3)).Sum(new Func<ShownOpenTrans, decimal>(MainForm.<>c.<>9.method_4));
			decimal d2 = Math.Round(d);
			Color foreColor;
			if (d2 < 0m)
			{
				foreColor = Class181.color_24;
			}
			else if (d2 > 0m)
			{
				foreColor = Color.Red;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				foreColor = Class181.color_9;
			}
			else
			{
				foreColor = Class181.color_1;
			}
			this.labelItem_12.ForeColor = foreColor;
			this.labelItem_12.Text = d.ToString(Class521.smethod_0(74951));
		}

		// Token: 0x06001BE5 RID: 7141 RVA: 0x000C3EA0 File Offset: 0x000C20A0
		private void method_45()
		{
			if (!Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader && (Base.UI.TransTabCtrl != null || this.bar_0.Enabled))
			{
				Base.Trading.smethod_151();
				string text = (Base.Acct.smethod_19() / 1.000000000000000000000m).ToString(Class521.smethod_0(74951));
				decimal? num = Base.Acct.smethod_23();
				string text2;
				if (num != null)
				{
					text2 = (Math.Round(num.Value, 0) / 1.00000000000000000000000m).ToString() + Class521.smethod_0(5356);
				}
				else
				{
					text2 = Class521.smethod_0(18686);
				}
				decimal d = Base.Acct.smethod_42();
				decimal d2 = Base.Trading.smethod_178();
				decimal num2 = d + d2;
				string str = string.Concat(new string[]
				{
					Class521.smethod_0(74956),
					text,
					Class521.smethod_0(74989),
					text2,
					Class521.smethod_0(75006),
					num2.ToString(Class521.smethod_0(74951))
				});
				if (Base.UI.TransTabCtrl != null)
				{
					Base.UI.TransTabCtrl.PanelHeaderText = Class521.smethod_0(67422) + str;
				}
				if (this.bar_0.Enabled)
				{
					this.dockContainerItem_0.Text = Class521.smethod_0(64375) + str;
					this.dockContainerItem_0.Visible = true;
				}
			}
			else if (Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader)
			{
				if (Base.UI.TransTabCtrl != null && Base.UI.TransTabCtrl.PanelHeaderText != Class521.smethod_0(67422))
				{
					Base.UI.TransTabCtrl.PanelHeaderText = Class521.smethod_0(67422);
				}
				if (this.bar_0.Enabled && this.dockContainerItem_0.Text != Class521.smethod_0(64375))
				{
					this.dockContainerItem_0.Text = Class521.smethod_0(64375);
				}
			}
		}

		// Token: 0x06001BE6 RID: 7142 RVA: 0x0000B923 File Offset: 0x00009B23
		private void method_46(DateTime dateTime_0)
		{
			Base.UI.smethod_20(dateTime_0);
			Base.UI.smethod_133();
		}

		// Token: 0x06001BE7 RID: 7143 RVA: 0x0000B932 File Offset: 0x00009B32
		private void method_47()
		{
			Base.UI.smethod_18();
			Base.UI.smethod_21();
			Base.UI.smethod_133();
		}

		// Token: 0x06001BE8 RID: 7144 RVA: 0x0000B945 File Offset: 0x00009B45
		private void method_48()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				chtCtrl_KLine.method_120(chtCtrl_KLine.HalfNbOfSticks);
			}
		}

		// Token: 0x06001BE9 RID: 7145 RVA: 0x0000B971 File Offset: 0x00009B71
		private void method_49()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				chtCtrl_KLine.method_122(chtCtrl_KLine.HalfNbOfSticks);
			}
		}

		// Token: 0x06001BEA RID: 7146 RVA: 0x0000B99D File Offset: 0x00009B9D
		private void method_50()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				((ChtCtrl_KLine)Base.UI.SelectedChtCtrl).method_117(false);
			}
		}

		// Token: 0x06001BEB RID: 7147 RVA: 0x0000B9C4 File Offset: 0x00009BC4
		private void method_51()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				((ChtCtrl_KLine)Base.UI.SelectedChtCtrl).method_117(true);
			}
		}

		// Token: 0x06001BEC RID: 7148 RVA: 0x000C40B0 File Offset: 0x000C22B0
		private void method_52()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				selectedChtCtrl.IsInCrossReviewMode = true;
				if (selectedChtCtrl.RevCrossXVal != null)
				{
					double num = Math.Round(selectedChtCtrl.RevCrossXVal.Value);
					if (num > 1.0)
					{
						if (selectedChtCtrl is ChtCtrl_KLine)
						{
							if (num > (double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1))
							{
								selectedChtCtrl.RevCrossXVal = new double?((double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1));
							}
							else
							{
								selectedChtCtrl.RevCrossXVal = new double?(num - 1.0);
							}
						}
						else
						{
							ChtCtrl_Tick chtCtrl_Tick = selectedChtCtrl as ChtCtrl_Tick;
							if (num > (double)chtCtrl_Tick.ChtHDList.Count)
							{
								selectedChtCtrl.RevCrossXVal = new double?((double)chtCtrl_Tick.ChtHDList.Count);
							}
							else
							{
								selectedChtCtrl.RevCrossXVal = new double?(num - 1.0);
							}
						}
					}
					else if (selectedChtCtrl is ChtCtrl_KLine)
					{
						(selectedChtCtrl as ChtCtrl_KLine).method_120(1);
					}
					else
					{
						selectedChtCtrl.RevCrossXVal = new double?((double)1f);
					}
				}
				else if (selectedChtCtrl is ChtCtrl_KLine)
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1));
				}
				else
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count);
				}
				selectedChtCtrl.method_25();
				selectedChtCtrl.method_15();
			}
		}

		// Token: 0x06001BED RID: 7149 RVA: 0x000C420C File Offset: 0x000C240C
		private void method_53()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				selectedChtCtrl.IsInCrossReviewMode = true;
				if (selectedChtCtrl.RevCrossXVal != null)
				{
					double num = Math.Round(selectedChtCtrl.RevCrossXVal.Value);
					if (selectedChtCtrl is ChtCtrl_KLine)
					{
						if (num < (double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1))
						{
							selectedChtCtrl.RevCrossXVal = new double?(Math.Floor(selectedChtCtrl.RevCrossXVal.Value) + 1.0);
						}
						else
						{
							((ChtCtrl_KLine)selectedChtCtrl).method_123();
						}
					}
					else if (num < (double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count)
					{
						selectedChtCtrl.RevCrossXVal = new double?(Math.Floor(selectedChtCtrl.RevCrossXVal.Value) + 1.0);
					}
				}
				else if (selectedChtCtrl is ChtCtrl_KLine)
				{
					((ChtCtrl_KLine)selectedChtCtrl).method_123();
				}
				else
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count);
				}
				selectedChtCtrl.method_25();
				selectedChtCtrl.method_15();
			}
		}

		// Token: 0x06001BEE RID: 7150 RVA: 0x0000B9EB File Offset: 0x00009BEB
		private void method_54()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
				{
					Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
				}
				else
				{
					Base.UI.smethod_151(Base.UI.SelectedChtCtrl);
				}
			}
		}

		// Token: 0x06001BEF RID: 7151 RVA: 0x000C4324 File Offset: 0x000C2524
		private void method_55()
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			if (selectedChtCtrl != null)
			{
				selectedChtCtrl.IsReverse = !selectedChtCtrl.IsReverse;
			}
		}

		// Token: 0x06001BF0 RID: 7152 RVA: 0x000C434C File Offset: 0x000C254C
		private void method_56()
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			if (selectedChtCtrl != null && selectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = selectedChtCtrl as ChtCtrl_KLine;
				if (chtCtrl_KLine.SelectedInd != null && chtCtrl_KLine.SelectedInd.IsMainChartInd)
				{
					Indicator selectedInd = chtCtrl_KLine.SelectedInd;
					selectedInd.IsSelected = false;
					selectedInd.RemoveFromChart();
				}
				else if (chtCtrl_KLine.SelectedDrawObj != null)
				{
					chtCtrl_KLine.SelectedDrawObj.method_30();
				}
			}
		}

		// Token: 0x06001BF1 RID: 7153 RVA: 0x0000BA1A File Offset: 0x00009C1A
		private void buttonItem_23_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(1));
		}

		// Token: 0x06001BF2 RID: 7154 RVA: 0x0000BA2B File Offset: 0x00009C2B
		private void method_57(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(2));
		}

		// Token: 0x06001BF3 RID: 7155 RVA: 0x0000BA3C File Offset: 0x00009C3C
		private void buttonItem_24_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(3));
		}

		// Token: 0x06001BF4 RID: 7156 RVA: 0x0000BA4D File Offset: 0x00009C4D
		private void buttonItem_25_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(5));
		}

		// Token: 0x06001BF5 RID: 7157 RVA: 0x0000BA5E File Offset: 0x00009C5E
		private void buttonItem_26_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(10));
		}

		// Token: 0x06001BF6 RID: 7158 RVA: 0x0000BA70 File Offset: 0x00009C70
		private void buttonItem_27_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(15));
		}

		// Token: 0x06001BF7 RID: 7159 RVA: 0x0000BA82 File Offset: 0x00009C82
		private void buttonItem_28_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(30));
		}

		// Token: 0x06001BF8 RID: 7160 RVA: 0x0000BA94 File Offset: 0x00009C94
		private void buttonItem_29_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(60));
		}

		// Token: 0x06001BF9 RID: 7161 RVA: 0x0000BAA6 File Offset: 0x00009CA6
		private void buttonItem_30_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(120));
		}

		// Token: 0x06001BFA RID: 7162 RVA: 0x0000BAB8 File Offset: 0x00009CB8
		private void buttonItem_49_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(240));
		}

		// Token: 0x06001BFB RID: 7163 RVA: 0x000C43B0 File Offset: 0x000C25B0
		private void buttonItem_31_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByDay, null);
		}

		// Token: 0x06001BFC RID: 7164 RVA: 0x000C43D0 File Offset: 0x000C25D0
		private void buttonItem_32_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByWeek, null);
		}

		// Token: 0x06001BFD RID: 7165 RVA: 0x000C43F0 File Offset: 0x000C25F0
		private void buttonItem_33_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMonth, null);
		}

		// Token: 0x06001BFE RID: 7166 RVA: 0x000C4410 File Offset: 0x000C2610
		private void method_58(PeriodType periodType_0, int? nullable_1)
		{
			MainForm.Class335 @class = new MainForm.Class335();
			@class.periodType_0 = periodType_0;
			@class.nullable_0 = nullable_1;
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_Tick)
				{
					Base.UI.SelectedChtCtrl = Base.UI.smethod_151(Base.UI.SelectedChtCtrl);
				}
				this.method_59(@class.periodType_0, @class.nullable_0);
				Base.UI.SelectedChtCtrl.Focus();
			}
			else
			{
				List<ChtCtrl> visibleChtCtrlList = Base.UI.VisibleChtCtrlList;
				if (visibleChtCtrlList != null && visibleChtCtrlList.Count > 0)
				{
					List<ChtCtrl> list = visibleChtCtrlList.Where(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_5)).ToList<ChtCtrl>();
					if (list.Count > 0)
					{
						ChtCtrl chtCtrl = list.FirstOrDefault(new Func<ChtCtrl, bool>(@class.method_0));
						if (chtCtrl == null)
						{
							chtCtrl = list[0];
						}
						Base.UI.SelectedChtCtrl = chtCtrl;
					}
					else
					{
						ChtCtrl chtCtrl = visibleChtCtrlList[0];
						Base.UI.SelectedChtCtrl = Base.UI.smethod_151(chtCtrl);
					}
					this.method_59(@class.periodType_0, @class.nullable_0);
					Base.UI.SelectedChtCtrl.Focus();
				}
				else if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabs != null)
				{
					Base.UI.SelectedChtCtrl = Base.UI.TransTabs.method_91();
					if (Base.UI.SelectedChtCtrl != null)
					{
						this.method_59(@class.periodType_0, @class.nullable_0);
						Base.UI.SelectedChtCtrl.Focus();
					}
					else
					{
						Class184.smethod_0(new Exception(Class521.smethod_0(75031)));
					}
				}
			}
		}

		// Token: 0x06001BFF RID: 7167 RVA: 0x000C457C File Offset: 0x000C277C
		private void method_59(PeriodType periodType_0, int? nullable_1)
		{
			if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				if (chtCtrl_KLine.SymbDataSet.CurrHisDataSet != null && !chtCtrl_KLine.SymbDataSet.IsCurrDateNotListedYet)
				{
					string text = Class521.smethod_0(75064) + periodType_0.ToString() + Class521.smethod_0(75073) + ((nullable_1 != null) ? nullable_1.Value.ToString() : Class521.smethod_0(75086));
					Class48.smethod_3(Class24.PeriodResetting, text);
					if (periodType_0 == PeriodType.ByMins && nullable_1 != null && nullable_1.Value < 60)
					{
						chtCtrl_KLine.SymbDataSet.method_42();
					}
					chtCtrl_KLine.method_12(periodType_0, nullable_1);
					Class48.smethod_3(Class24.PeriodReset, text);
				}
			}
		}

		// Token: 0x06001C00 RID: 7168 RVA: 0x0000BACD File Offset: 0x00009CCD
		private void buttonItem_22_Click(object sender, EventArgs e)
		{
			this.method_60();
		}

		// Token: 0x06001C01 RID: 7169 RVA: 0x000C4654 File Offset: 0x000C2854
		private void method_60()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
				{
					Base.UI.SelectedChtCtrl = Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
				}
				Base.UI.SelectedChtCtrl.Focus();
			}
			else
			{
				List<ChtCtrl> visibleChtCtrlList = Base.UI.VisibleChtCtrlList;
				if (visibleChtCtrlList != null && visibleChtCtrlList.Count > 0)
				{
					ChtCtrl chtCtrl = visibleChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_6));
					if (chtCtrl == null)
					{
						chtCtrl = visibleChtCtrlList[0];
						Base.UI.SelectedChtCtrl = Base.UI.smethod_149(chtCtrl);
					}
					else
					{
						Base.UI.SelectedChtCtrl = chtCtrl;
					}
					Base.UI.SelectedChtCtrl.Focus();
				}
				else if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabs != null)
				{
					ChtCtrl_Tick chtCtrl_Tick = Base.UI.TransTabs.method_88();
					if (chtCtrl_Tick != null)
					{
						Base.UI.SelectedChtCtrl = chtCtrl_Tick;
						Base.UI.SelectedChtCtrl.Focus();
					}
				}
			}
		}

		// Token: 0x06001C02 RID: 7170 RVA: 0x000C4730 File Offset: 0x000C2930
		private void method_61(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (!buttonItem.Checked)
			{
				if (buttonItem.Tag != null && !(buttonItem.Tag.ToString() == string.Empty))
				{
					HisDataPeriodSet hisDataPeriodSet = buttonItem.Tag as HisDataPeriodSet;
					if (hisDataPeriodSet != null)
					{
						Base.UI.Form.AutoPlayPeriodType = new PeriodType?(hisDataPeriodSet.PeriodType);
						Base.UI.Form.AutoPlayPeriodUnits = hisDataPeriodSet.PeriodUnits;
					}
				}
				else
				{
					Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
					Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
				}
			}
		}

		// Token: 0x06001C03 RID: 7171 RVA: 0x000C47C8 File Offset: 0x000C29C8
		private void method_62(ChtCtrl chtCtrl_0 = null)
		{
			Bar bar_ = this.bar_4;
			if (chtCtrl_0 != null)
			{
				if (chtCtrl_0 is ChtCtrl_KLine)
				{
					if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
					{
						int? periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 1 & periodUnits != null)
						{
							this.method_63(bar_, 2);
						}
						else
						{
							periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
							if (periodUnits.GetValueOrDefault() == 3 & periodUnits != null)
							{
								this.method_63(bar_, 3);
							}
							else
							{
								periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
								if (periodUnits.GetValueOrDefault() == 5 & periodUnits != null)
								{
									this.method_63(bar_, 4);
								}
								else
								{
									periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
									if (periodUnits.GetValueOrDefault() == 10 & periodUnits != null)
									{
										this.method_63(bar_, 5);
									}
									else
									{
										periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
										if (periodUnits.GetValueOrDefault() == 15 & periodUnits != null)
										{
											this.method_63(bar_, 6);
										}
										else
										{
											periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
											if (periodUnits.GetValueOrDefault() == 30 & periodUnits != null)
											{
												this.method_63(bar_, 7);
											}
											else
											{
												periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
												if (periodUnits.GetValueOrDefault() == 60 & periodUnits != null)
												{
													this.method_63(bar_, 8);
												}
												else
												{
													periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
													if (periodUnits.GetValueOrDefault() == 120 & periodUnits != null)
													{
														this.method_63(bar_, 9);
													}
													else
													{
														periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
														if (periodUnits.GetValueOrDefault() == 240 & periodUnits != null)
														{
															this.method_63(bar_, 10);
														}
														else
														{
															this.method_63(bar_, 1);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByDay)
					{
						if (chtCtrl_0.HisDataPeriodSet.PeriodUnits != null)
						{
							if (chtCtrl_0.HisDataPeriodSet.PeriodUnits.Value != 1)
							{
								this.method_63(bar_, 1);
								return;
							}
						}
						this.method_63(bar_, 11);
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByWeek)
					{
						this.method_63(bar_, 12);
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByMonth)
					{
						this.method_63(bar_, 13);
					}
					else
					{
						this.method_63(bar_, 1);
					}
				}
				else
				{
					this.method_63(bar_, 0);
				}
			}
			else
			{
				Base.UI.smethod_158(bar_);
			}
		}

		// Token: 0x06001C04 RID: 7172 RVA: 0x0000BAD7 File Offset: 0x00009CD7
		private void method_63(Bar bar_7, int int_11)
		{
			Base.UI.smethod_158(bar_7);
			((ButtonItem)bar_7.Items[int_11]).Checked = true;
		}

		// Token: 0x06001C05 RID: 7173 RVA: 0x000C4A64 File Offset: 0x000C2C64
		private void method_64(Enum4 enum4_0)
		{
			int num = 10;
			if (enum4_0 == Enum4.const_0)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					num = Base.UI.Form.NMinsPeriodUnits.Value;
				}
			}
			else if (enum4_0 == Enum4.const_1)
			{
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					num = Base.UI.Form.NHoursPeriodUnits.Value;
				}
			}
			else if (Base.UI.Form.NDaysPeriodUnits != null)
			{
				num = Base.UI.Form.NDaysPeriodUnits.Value;
			}
			this.method_204();
			if (this.form18_0 != null)
			{
				this.form18_0.Hide();
			}
			base.BringToFront();
			Form6 form = new Form6(enum4_0, num);
			form.Owner = this;
			form.NPeriodSet += this.method_65;
			form.ShowDialog();
		}

		// Token: 0x06001C06 RID: 7174 RVA: 0x000C4B3C File Offset: 0x000C2D3C
		private void method_65(EventArgs4 eventArgs4_0)
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			switch (eventArgs4_0.NPeriodType)
			{
			case Enum4.const_0:
				this.method_58(PeriodType.ByMins, new int?(eventArgs4_0.Period));
				Base.UI.Form.NMinsPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			case Enum4.const_1:
				this.method_58(PeriodType.ByMins, new int?(eventArgs4_0.Period * 60));
				Base.UI.Form.NHoursPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			case Enum4.const_2:
				this.method_58(PeriodType.ByDay, new int?(eventArgs4_0.Period));
				Base.UI.Form.NDaysPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			}
		}

		// Token: 0x06001C07 RID: 7175 RVA: 0x000C4BE8 File Offset: 0x000C2DE8
		private void buttonItem_56_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			if (this.buttonItem_56.ToolTipControl.Visible)
			{
				if (this.buttonItem_56.Checked)
				{
					this.buttonItem_56.ToolTipControl.Text = Class521.smethod_0(75095) + Base.UI.SelectedChtCtrl.HisDataPeriodSet.PeriodDesc + Class521.smethod_0(5046);
				}
				else
				{
					this.buttonItem_56.ToolTipControl.Text = Class521.smethod_0(9134);
				}
			}
		}

		// Token: 0x06001C08 RID: 7176 RVA: 0x000C4C6C File Offset: 0x000C2E6C
		private void buttonItem_56_ExpandChange(object sender, EventArgs e)
		{
			if (this.buttonItem_56.Expanded)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					this.textBoxItem_0.Text = Base.UI.Form.NMinsPeriodUnits.Value.ToString();
				}
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					this.textBoxItem_1.Text = Base.UI.Form.NHoursPeriodUnits.Value.ToString();
				}
				if (Base.UI.Form.NDaysPeriodUnits != null)
				{
					this.textBoxItem_2.Text = Base.UI.Form.NDaysPeriodUnits.Value.ToString();
				}
				this.textBoxItem_0.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.textBoxItem_1.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.textBoxItem_2.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.textBoxItem_0.TextAlign = HorizontalAlignment.Right;
				this.textBoxItem_1.TextAlign = HorizontalAlignment.Right;
				this.textBoxItem_2.TextAlign = HorizontalAlignment.Right;
				this.textBoxItem_0.KeyDown += this.textBoxItem_0_KeyDown;
				this.textBoxItem_1.KeyDown += this.textBoxItem_1_KeyDown;
				this.textBoxItem_2.KeyDown += this.textBoxItem_2_KeyDown;
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				if (selectedChtCtrl is ChtCtrl_KLine)
				{
					if (selectedChtCtrl.PeriodType == PeriodType.ByMins && selectedChtCtrl.PeriodUnits != null)
					{
						try
						{
							int num = Convert.ToInt32(this.textBoxItem_0.Text);
							int num2 = Convert.ToInt32(this.textBoxItem_1.Text);
							if (Utility.CanExactDiv(selectedChtCtrl.PeriodUnits.Value, 60) && num2 == selectedChtCtrl.PeriodUnits.Value / 60)
							{
								this.buttonItem_58.Checked = true;
							}
							else if (selectedChtCtrl.PeriodUnits.Value == num)
							{
								this.buttonItem_57.Checked = true;
							}
							return;
						}
						catch (Exception exception_)
						{
							Class184.smethod_0(exception_);
							return;
						}
					}
					if (selectedChtCtrl.PeriodType == PeriodType.ByDay && selectedChtCtrl.PeriodUnits != null)
					{
						try
						{
							int num3 = Convert.ToInt32(this.textBoxItem_2.Text);
							if (selectedChtCtrl.PeriodUnits.Value == num3)
							{
								this.buttonItem_59.Checked = true;
							}
						}
						catch (Exception exception_2)
						{
							Class184.smethod_0(exception_2);
						}
					}
				}
			}
		}

		// Token: 0x06001C09 RID: 7177 RVA: 0x000C4EF4 File Offset: 0x000C30F4
		private void buttonItem_57_Click(object sender, EventArgs e)
		{
			string text = this.textBoxItem_0.Text;
			if (text.Length > 0)
			{
				try
				{
					int value = Convert.ToInt32(text);
					this.method_58(PeriodType.ByMins, new int?(value));
					Base.UI.Form.NMinsPeriodUnits = new int?(value);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show(Class521.smethod_0(75120), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0A RID: 7178 RVA: 0x000C4F70 File Offset: 0x000C3170
		private void buttonItem_58_Click(object sender, EventArgs e)
		{
			string text = this.textBoxItem_1.Text;
			if (text.Length > 0)
			{
				try
				{
					int num = Convert.ToInt32(text);
					this.method_58(PeriodType.ByMins, new int?(num * 60));
					Base.UI.Form.NHoursPeriodUnits = new int?(num);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show(Class521.smethod_0(75173), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0B RID: 7179 RVA: 0x000C4FF0 File Offset: 0x000C31F0
		private void buttonItem_59_Click(object sender, EventArgs e)
		{
			string text = this.textBoxItem_2.Text;
			if (text.Length > 0)
			{
				try
				{
					int value = Convert.ToInt32(text);
					this.method_58(PeriodType.ByDay, new int?(value));
					Base.UI.Form.NDaysPeriodUnits = new int?(value);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show(Class521.smethod_0(75226), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0C RID: 7180 RVA: 0x0000BAF8 File Offset: 0x00009CF8
		private void textBoxItem_0_InputTextChanged(object object_0)
		{
			this.buttonItem_57.Checked = false;
		}

		// Token: 0x06001C0D RID: 7181 RVA: 0x0000BB08 File Offset: 0x00009D08
		private void textBoxItem_1_InputTextChanged(object object_0)
		{
			this.buttonItem_58.Checked = false;
		}

		// Token: 0x06001C0E RID: 7182 RVA: 0x0000BB18 File Offset: 0x00009D18
		private void textBoxItem_2_InputTextChanged(object object_0)
		{
			this.buttonItem_59.Checked = false;
		}

		// Token: 0x06001C0F RID: 7183 RVA: 0x0000BB28 File Offset: 0x00009D28
		private void textBoxItem_0_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.buttonItem_57.Checked)
			{
				this.buttonItem_57_Click(this.buttonItem_57, null);
				this.buttonItem_56.Expanded = false;
			}
		}

		// Token: 0x06001C10 RID: 7184 RVA: 0x0000BB5C File Offset: 0x00009D5C
		private void textBoxItem_1_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.buttonItem_58.Checked)
			{
				this.buttonItem_58_Click(this.buttonItem_58, null);
				this.buttonItem_56.Expanded = false;
			}
		}

		// Token: 0x06001C11 RID: 7185 RVA: 0x0000BB90 File Offset: 0x00009D90
		private void textBoxItem_2_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.buttonItem_59.Checked)
			{
				this.buttonItem_59_Click(this.buttonItem_59, null);
				this.buttonItem_56.Expanded = false;
			}
		}

		// Token: 0x06001C12 RID: 7186 RVA: 0x000C506C File Offset: 0x000C326C
		private bool method_66(HisData hisData_0)
		{
			bool result;
			try
			{
				this.class304_1.Value = Convert.ToDecimal(hisData_0.Close) / 1.000000000000000000000000000m;
				result = true;
				goto IL_41;
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			return false;
			IL_41:
			return result;
		}

		// Token: 0x06001C13 RID: 7187 RVA: 0x000C50D0 File Offset: 0x000C32D0
		private void method_67()
		{
			if (Base.Data.smethod_124(Base.UI.CurrTradingSymbol))
			{
				this.buttonItem_2.Enabled = true;
				this.buttonItem_3.Enabled = true;
			}
			else
			{
				this.buttonItem_2.Enabled = false;
				this.buttonItem_3.Enabled = false;
			}
		}

		// Token: 0x06001C14 RID: 7188 RVA: 0x000C5120 File Offset: 0x000C3320
		private void method_68()
		{
			if (Base.UI.CurrTradingSymbol != null && Base.UI.CurrTradingSymbol.IsStock)
			{
				this.buttonItem_0.Text = Class521.smethod_0(24991);
				this.buttonItem_1.Text = Class521.smethod_0(25000);
				this.buttonItem_2.Text = Class521.smethod_0(25000);
				this.buttonItem_3.Text = Class521.smethod_0(24991);
			}
			else
			{
				this.buttonItem_0.Text = Class521.smethod_0(26376);
				this.buttonItem_1.Text = Class521.smethod_0(26410);
				this.buttonItem_2.Text = Class521.smethod_0(26444);
				this.buttonItem_3.Text = Class521.smethod_0(26478);
			}
		}

		// Token: 0x06001C15 RID: 7189 RVA: 0x000C51EC File Offset: 0x000C33EC
		private void method_69()
		{
			if (Base.UI.CurrTradingSymbol != null)
			{
				if (this.class304_1.Increment != Base.UI.CurrTradingSymbol.LeastPriceVar.Value)
				{
					this.class304_1.Increment = Base.UI.CurrTradingSymbol.LeastPriceVar.Value;
				}
				if (this.class304_1.Value == 0m)
				{
					this.class304_1.DecimalPlaces = 0;
				}
				else
				{
					this.class304_1.DecimalPlaces = Base.UI.CurrTradingSymbol.DigitNb;
				}
			}
		}

		// Token: 0x06001C16 RID: 7190 RVA: 0x000C5280 File Offset: 0x000C3480
		private void buttonItem_0_Click(object sender, EventArgs e)
		{
			decimal value = this.class304_1.Value;
			this.method_135(OrderType.Order_OpenLong, value, true);
		}

		// Token: 0x06001C17 RID: 7191 RVA: 0x000C52A4 File Offset: 0x000C34A4
		private void buttonItem_1_Click(object sender, EventArgs e)
		{
			decimal value = this.class304_1.Value;
			this.method_135(OrderType.Order_CloseLong, value, true);
		}

		// Token: 0x06001C18 RID: 7192 RVA: 0x000C52C8 File Offset: 0x000C34C8
		private void buttonItem_2_Click(object sender, EventArgs e)
		{
			decimal value = this.class304_1.Value;
			this.method_135(OrderType.Order_OpenShort, value, true);
		}

		// Token: 0x06001C19 RID: 7193 RVA: 0x000C52EC File Offset: 0x000C34EC
		private void buttonItem_3_Click(object sender, EventArgs e)
		{
			decimal value = this.class304_1.Value;
			this.method_135(OrderType.Order_CloseShort, value, true);
		}

		// Token: 0x06001C1A RID: 7194 RVA: 0x0000BBC4 File Offset: 0x00009DC4
		private void buttonItem_0_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1B RID: 7195 RVA: 0x0000BBC4 File Offset: 0x00009DC4
		private void buttonItem_2_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1C RID: 7196 RVA: 0x0000BBC4 File Offset: 0x00009DC4
		private void buttonItem_1_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1D RID: 7197 RVA: 0x0000BBC4 File Offset: 0x00009DC4
		private void buttonItem_3_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1E RID: 7198 RVA: 0x000C5310 File Offset: 0x000C3510
		private void method_70(ButtonItem buttonItem_72)
		{
			if (buttonItem_72.Enabled && (!Base.UI.Form.IsInBlindTestMode || Base.UI.Form.IsSingleBlindTest))
			{
				buttonItem_72.Tooltip = string.Concat(new string[]
				{
					buttonItem_72.Text,
					Base.UI.CurrTradingSymbol.CNName,
					Class521.smethod_0(24872),
					Base.UI.CurrTradingSymbol.Code,
					Class521.smethod_0(5046)
				});
			}
			else
			{
				buttonItem_72.Tooltip = buttonItem_72.Text;
			}
		}

		// Token: 0x06001C1F RID: 7199 RVA: 0x000C539C File Offset: 0x000C359C
		private void method_71()
		{
			string str = Base.UI.smethod_174(Base.UI.Form.AutoPlayPeriodType.Value, Base.UI.Form.AutoPlayPeriodUnits);
			this.buttonItem_4.Tooltip = Class521.smethod_0(75275) + str + Class521.smethod_0(5046);
		}

		// Token: 0x06001C20 RID: 7200 RVA: 0x000C53F4 File Offset: 0x000C35F4
		private void buttonItem_4_PopupOpen(object sender, PopupOpenEventArgs e)
		{
			if (Base.UI.ChtCtrlList != null)
			{
				this.buttonItem_4.SubItems.Clear();
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = Class521.smethod_0(75312);
				buttonItem.Click += this.method_61;
				PeriodType? autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
				if (autoPlayPeriodType.GetValueOrDefault() == PeriodType.ByMins & autoPlayPeriodType != null)
				{
					int? num = Base.UI.Form.AutoPlayPeriodUnits;
					if (num.GetValueOrDefault() == 1 & num != null)
					{
						buttonItem.Checked = true;
					}
				}
				this.buttonItem_4.SubItems.Add(buttonItem);
				List<HisDataPeriodSet> list = new List<HisDataPeriodSet>();
				using (IEnumerator<ChtCtrl> enumerator = Base.UI.ChtCtrlList.Where(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_7)).OrderBy(new Func<ChtCtrl, PeriodType>(MainForm.<>c.<>9.method_8)).ThenBy(new Func<ChtCtrl, int?>(MainForm.<>c.<>9.method_9)).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						MainForm.Class336 @class = new MainForm.Class336();
						@class.chtCtrl_0 = enumerator.Current;
						if (!@class.chtCtrl_0.HisDataPeriodSet.IsPeriod1m && !list.Exists(new Predicate<HisDataPeriodSet>(@class.method_0)))
						{
							ButtonItem buttonItem2 = new ButtonItem();
							buttonItem2.Text = Class521.smethod_0(75345) + @class.chtCtrl_0.HisDataPeriodSet.PeriodDesc;
							buttonItem2.Tag = @class.chtCtrl_0.HisDataPeriodSet;
							buttonItem2.Click += this.method_61;
							PeriodType periodType = @class.chtCtrl_0.PeriodType;
							autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
							if (periodType == autoPlayPeriodType.GetValueOrDefault() & autoPlayPeriodType != null)
							{
								int? num = @class.chtCtrl_0.PeriodUnits;
								int? autoPlayPeriodUnits = Base.UI.Form.AutoPlayPeriodUnits;
								if (num.GetValueOrDefault() == autoPlayPeriodUnits.GetValueOrDefault() & num != null == (autoPlayPeriodUnits != null))
								{
									buttonItem2.Checked = true;
								}
							}
							list.Add(@class.chtCtrl_0.HisDataPeriodSet);
							this.buttonItem_4.SubItems.Add(buttonItem2);
						}
					}
				}
			}
		}

		// Token: 0x06001C21 RID: 7201 RVA: 0x000C568C File Offset: 0x000C388C
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.int_0++;
			if (this.int_0 > 25)
			{
				if (Utility.GetKeyDown(Keys.Left) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control)
				{
					this.method_52();
				}
				else if (Utility.GetKeyDown(Keys.Right) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control)
				{
					this.method_53();
				}
				else
				{
					this.int_0 = 0;
				}
			}
		}

		// Token: 0x06001C22 RID: 7202 RVA: 0x000C570C File Offset: 0x000C390C
		private void sliderItem_0_ValueChanged(object sender, EventArgs e)
		{
			this.sliderItem_0.Text = this.sliderItem_0.Value.ToString();
			this.method_205();
		}

		// Token: 0x06001C23 RID: 7203 RVA: 0x000C5740 File Offset: 0x000C3940
		private void controlContainerItem_0_MouseEnter(object sender, EventArgs e)
		{
			long num = Base.Trading.smethod_207(Base.UI.CurrTradingSymbol);
			if (num < 0L)
			{
				num = 0L;
			}
			this.toolTip_0.SetToolTip(this.class304_0, Class521.smethod_0(75366) + num.ToString());
		}

		// Token: 0x06001C24 RID: 7204 RVA: 0x0000BBD4 File Offset: 0x00009DD4
		private void controlContainerItem_0_MouseLeave(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.class304_0, null);
		}

		// Token: 0x06001C25 RID: 7205 RVA: 0x0000BBEA File Offset: 0x00009DEA
		private void buttonItem_53_Click(object sender, EventArgs e)
		{
			new Form14
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C26 RID: 7206 RVA: 0x00006678 File Offset: 0x00004878
		private void buttonItem_54_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_116();
		}

		// Token: 0x06001C27 RID: 7207 RVA: 0x0000BC07 File Offset: 0x00009E07
		private void buttonItem_55_Click(object sender, EventArgs e)
		{
			new FormIndMgr().ShowDialog();
		}

		// Token: 0x06001C28 RID: 7208 RVA: 0x0000BC16 File Offset: 0x00009E16
		private void buttonItem_38_PopupOpen(object sender, EventArgs e)
		{
			this.method_76(this.buttonItem_38);
		}

		// Token: 0x06001C29 RID: 7209 RVA: 0x0000BC26 File Offset: 0x00009E26
		private void buttonItem_40_Click(object sender, EventArgs e)
		{
			new Form2
			{
				Owner = this
			}.ShowDialog();
		}

		// Token: 0x06001C2A RID: 7210 RVA: 0x0000BC3C File Offset: 0x00009E3C
		private void buttonItem_41_Click(object sender, EventArgs e)
		{
			new Form13
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C2B RID: 7211 RVA: 0x000C5798 File Offset: 0x000C3998
		private void buttonItem_42_Click(object sender, EventArgs e)
		{
			try
			{
				Process.Start(Class521.smethod_0(75379));
			}
			catch
			{
				MessageBox.Show(Class521.smethod_0(75400), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06001C2C RID: 7212 RVA: 0x000C57EC File Offset: 0x000C39EC
		private void buttonItem_43_Click(object sender, EventArgs e)
		{
			try
			{
				string str = Class521.smethod_0(75457);
				Process.Start(new ProcessStartInfo(Class521.smethod_0(75462) + str));
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001C2D RID: 7213 RVA: 0x000C583C File Offset: 0x000C3A3C
		private void buttonItem_44_Click(object sender, EventArgs e)
		{
			try
			{
				string str = Class521.smethod_0(1449);
				Process.Start(new ProcessStartInfo(Class521.smethod_0(75515) + str));
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
		}

		// Token: 0x06001C2E RID: 7214 RVA: 0x0000BC59 File Offset: 0x00009E59
		private void buttonItem_45_Click(object sender, EventArgs e)
		{
			this.method_12(true, true);
		}

		// Token: 0x06001C2F RID: 7215 RVA: 0x0000BC66 File Offset: 0x00009E66
		private void buttonItem_46_Click(object sender, EventArgs e)
		{
			this.method_204();
			SettingsForm settingsForm = new SettingsForm();
			settingsForm.UISettingsConfirmed += this.method_110;
			settingsForm.Owner = this;
			settingsForm.ShowInTaskbar = false;
			settingsForm.StartPosition = FormStartPosition.CenterParent;
			settingsForm.ShowDialog();
		}

		// Token: 0x06001C30 RID: 7216 RVA: 0x0000BCA2 File Offset: 0x00009EA2
		private void buttonItem_36_Click(object sender, EventArgs e)
		{
			this.method_72();
		}

		// Token: 0x06001C31 RID: 7217 RVA: 0x0000BCAC File Offset: 0x00009EAC
		private void method_72()
		{
			if (this.dockSite_6.Visible)
			{
				this.dockSite_6.Visible = false;
			}
			else
			{
				this.dockSite_6.Visible = true;
			}
		}

		// Token: 0x06001C32 RID: 7218 RVA: 0x0000BCD7 File Offset: 0x00009ED7
		private void buttonItem_6_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfShowTransTabsBar)
			{
				this.method_73();
			}
			else
			{
				this.method_74();
			}
		}

		// Token: 0x06001C33 RID: 7219 RVA: 0x0000BCF5 File Offset: 0x00009EF5
		private void method_73()
		{
			this.buttonItem_6.Checked = false;
			this.bar_0.Hide();
			Base.UI.IsTransTabsVisible = false;
			Base.UI.Form.IfShowTransTabsBar = false;
		}

		// Token: 0x06001C34 RID: 7220 RVA: 0x000C588C File Offset: 0x000C3A8C
		private void method_74()
		{
			if (this.bar_0.Enabled)
			{
				this.buttonItem_6.Checked = true;
				if (this.bar_0.DockSide != Base.UI.Form.AcctTransBar_DockSide)
				{
					this.bar_0.DockSide = Base.UI.Form.AcctTransBar_DockSide;
				}
				if (this.panelDockContainer_0.Controls.Count < 1)
				{
					Base.UI.TransTabs = new TransTabs(null);
					this.panelDockContainer_0.Controls.Add(Base.UI.TransTabs);
				}
				this.bar_0.Show();
				this.panelDockContainer_0.Visible = true;
				Base.UI.IsTransTabsVisible = true;
				Base.UI.Form.IfShowTransTabsBar = true;
			}
		}

		// Token: 0x06001C35 RID: 7221 RVA: 0x000C5940 File Offset: 0x000C3B40
		private void buttonItem_7_PopupOpen(object sender, EventArgs e)
		{
			ButtonItem buttonItem = this.buttonItem_7;
			buttonItem.SubItems.Clear();
			buttonItem.SubItems.Add(this.buttonItem_8);
			buttonItem.SubItems.Add(this.buttonItem_9);
			buttonItem.SubItems.Add(this.buttonItem_10);
			this.method_187(this.buttonItem_9, true);
			this.buttonItem_10.SubItems.Clear();
			foreach (Account account in Base.Acct.CurrAccounts)
			{
				ButtonItem buttonItem2 = new ButtonItem();
				buttonItem2.Text = account.AcctName;
				buttonItem2.Tag = account.ID;
				buttonItem2.Tooltip = account.Notes;
				buttonItem2.Click += this.method_75;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					ButtonItem buttonItem3 = buttonItem2;
					buttonItem3.Text += Class521.smethod_0(9970);
					buttonItem2.Enabled = false;
				}
				this.buttonItem_10.SubItems.Add(buttonItem2);
			}
		}

		// Token: 0x06001C36 RID: 7222 RVA: 0x000C5A80 File Offset: 0x000C3C80
		private void method_75(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (MessageBox.Show(string.Concat(new string[]
			{
				Class521.smethod_0(75552),
				buttonItem.Text,
				Class521.smethod_0(75581),
				Environment.NewLine,
				Class521.smethod_0(75594)
			}), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes && Base.Acct.smethod_8(Convert.ToInt32(buttonItem.Tag)))
			{
				MessageBox.Show(Class521.smethod_0(75744) + buttonItem.Text + Class521.smethod_0(75757), Class521.smethod_0(7730), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x06001C37 RID: 7223 RVA: 0x000C5B34 File Offset: 0x000C3D34
		private void buttonItem_5_PopupOpen(object sender, EventArgs e)
		{
			if (this.dockSite_6.Visible)
			{
				this.buttonItem_36.Checked = true;
			}
			else
			{
				this.buttonItem_36.Checked = false;
			}
			if (this.bar_0.Enabled)
			{
				this.buttonItem_6.Enabled = true;
				if (this.bar_0.Visible)
				{
					this.buttonItem_6.Checked = true;
				}
				else
				{
					this.buttonItem_6.Checked = false;
				}
			}
			else
			{
				this.buttonItem_6.Checked = true;
				this.buttonItem_6.Enabled = false;
			}
			if (Base.UI.DrawToolWnd != null)
			{
				this.buttonItem_47.Checked = true;
			}
			else
			{
				this.buttonItem_47.Checked = false;
			}
			if (Base.UI.DrawOdrWnd != null)
			{
				this.buttonItem_54.Checked = true;
			}
			else
			{
				this.buttonItem_54.Checked = false;
			}
		}

		// Token: 0x06001C38 RID: 7224 RVA: 0x000C5C08 File Offset: 0x000C3E08
		private void method_76(ButtonItem buttonItem_72)
		{
			buttonItem_72.SubItems.Clear();
			if (Base.Data.CurrExchangeList.Count > 1)
			{
				if (TApp.IsStIncluded)
				{
					List<StkSymbol> source = Base.Data.smethod_98();
					List<StkSymbol> source2 = Base.Data.smethod_100(false);
					List<StkSymbol> source3 = Base.Data.smethod_101(false);
					ExchgHouse exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(MainForm.<>c.<>9.method_10));
					if (exchgHouse != null)
					{
						ButtonItem buttonItem = new ButtonItem();
						buttonItem.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable = source3.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_11));
						if (enumerable.Any<StkSymbol>())
						{
							ButtonItem buttonItem2 = new ButtonItem();
							buttonItem2.Text = Class521.smethod_0(23215);
							this.method_79(buttonItem2, enumerable);
							buttonItem.SubItems.Add(buttonItem2);
						}
						IEnumerable<StkSymbol> enumerable2 = source.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_12));
						if (enumerable2.Any<StkSymbol>())
						{
							ButtonItem buttonItem3 = new ButtonItem();
							buttonItem3.Text = Class521.smethod_0(23271);
							this.method_79(buttonItem3, enumerable2);
							buttonItem.SubItems.Add(buttonItem3);
						}
						IEnumerable<StkSymbol> enumerable3 = source2.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_13));
						if (enumerable3.Any<StkSymbol>())
						{
							ButtonItem buttonItem4 = new ButtonItem();
							buttonItem4.Text = Class521.smethod_0(23245);
							this.method_79(buttonItem4, enumerable3);
							buttonItem.SubItems.Add(buttonItem4);
						}
						List<StkSymbol> list = Base.Data.smethod_106(false);
						if (list.Any<StkSymbol>())
						{
							ButtonItem buttonItem5 = new ButtonItem();
							buttonItem5.Text = Class521.smethod_0(23093);
							this.method_79(buttonItem5, list);
							buttonItem.SubItems.Add(buttonItem5);
						}
						List<StkSymbol> list2 = Base.Data.smethod_107(new int?(5), false);
						if (list2.Any<StkSymbol>())
						{
							ButtonItem buttonItem6 = new ButtonItem();
							buttonItem6.Text = Class521.smethod_0(23063);
							this.method_79(buttonItem6, list2);
							buttonItem.SubItems.Add(buttonItem6);
						}
						buttonItem_72.SubItems.Add(buttonItem);
					}
					exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(MainForm.<>c.<>9.method_14));
					if (exchgHouse != null)
					{
						ButtonItem buttonItem7 = new ButtonItem();
						buttonItem7.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable4 = source3.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_15));
						if (enumerable4.Any<StkSymbol>())
						{
							ButtonItem buttonItem8 = new ButtonItem();
							buttonItem8.Text = Class521.smethod_0(23215);
							this.method_79(buttonItem8, enumerable4);
							buttonItem7.SubItems.Add(buttonItem8);
						}
						List<StkSymbol> list3 = Base.Data.smethod_104(false);
						if (list3.Any<StkSymbol>())
						{
							ButtonItem buttonItem9 = new ButtonItem();
							buttonItem9.Text = Class521.smethod_0(23135);
							this.method_79(buttonItem9, list3);
							buttonItem7.SubItems.Add(buttonItem9);
						}
						List<StkSymbol> list4 = Base.Data.smethod_105(false);
						if (list4.Any<StkSymbol>())
						{
							ButtonItem buttonItem10 = new ButtonItem();
							buttonItem10.Text = Class521.smethod_0(23177);
							this.method_79(buttonItem10, list4);
							buttonItem7.SubItems.Add(buttonItem10);
						}
						IEnumerable<StkSymbol> enumerable5 = source.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_16));
						if (enumerable5.Any<StkSymbol>())
						{
							ButtonItem buttonItem11 = new ButtonItem();
							buttonItem11.Text = Class521.smethod_0(23271);
							this.method_79(buttonItem11, enumerable5);
							buttonItem7.SubItems.Add(buttonItem11);
						}
						IEnumerable<StkSymbol> enumerable6 = source2.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_17));
						if (enumerable6.Any<StkSymbol>())
						{
							ButtonItem buttonItem12 = new ButtonItem();
							buttonItem12.Text = Class521.smethod_0(23245);
							this.method_79(buttonItem12, enumerable6);
							buttonItem7.SubItems.Add(buttonItem12);
						}
						List<StkSymbol> list5 = Base.Data.smethod_107(new int?(6), false);
						if (list5.Any<StkSymbol>())
						{
							ButtonItem buttonItem13 = new ButtonItem();
							buttonItem13.Text = Class521.smethod_0(23063);
							this.method_79(buttonItem13, list5);
							buttonItem7.SubItems.Add(buttonItem13);
						}
						buttonItem_72.SubItems.Add(buttonItem7);
					}
				}
				if (TApp.IsFtIncluded)
				{
					bool bool_ = buttonItem_72.SubItems.Count > 0;
					this.method_77(1, buttonItem_72, bool_);
					this.method_77(2, buttonItem_72, false);
					this.method_77(4, buttonItem_72, false);
					this.method_77(3, buttonItem_72, false);
					this.method_77(0, buttonItem_72, false);
					this.method_77(-1, buttonItem_72, false);
				}
			}
			else
			{
				this.method_79(buttonItem_72, Base.Data.UsrStkSymbols.Values);
			}
			if (Base.UI.TransTabs != null)
			{
				List<StkSymbol> zixuanStkSymbList = Base.UI.TransTabs.ZixuanStkSymbList;
				if (zixuanStkSymbList != null)
				{
					ButtonItem buttonItem14 = new ButtonItem();
					buttonItem14.Text = Class521.smethod_0(22944);
					buttonItem14.BeginGroup = true;
					this.method_79(buttonItem14, zixuanStkSymbList);
					buttonItem_72.SubItems.Add(buttonItem14);
				}
			}
		}

		// Token: 0x06001C39 RID: 7225 RVA: 0x000C6140 File Offset: 0x000C4340
		private void method_77(int int_11, ButtonItem buttonItem_72, bool bool_8 = false)
		{
			MainForm.Class337 @class = new MainForm.Class337();
			@class.int_0 = int_11;
			@class.exchgHouse_0 = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(@class.method_0));
			if (@class.exchgHouse_0 != null)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = @class.exchgHouse_0.AbbrName_CN;
				IEnumerable<StkSymbol> enumerable = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1));
				if (enumerable != null && enumerable.Any<StkSymbol>())
				{
					this.method_78(buttonItem, enumerable);
					buttonItem_72.SubItems.Add(buttonItem);
					if (bool_8)
					{
						buttonItem.BeginGroup = true;
					}
				}
			}
		}

		// Token: 0x06001C3A RID: 7226 RVA: 0x000C61E0 File Offset: 0x000C43E0
		private void method_78(ButtonItem buttonItem_72, IEnumerable<StkSymbol> ienumerable_0)
		{
			foreach (string text in ienumerable_0.Select(new Func<StkSymbol, string>(MainForm.<>c.<>9.method_18)).Distinct<string>())
			{
				MainForm.Class338 @class = new MainForm.Class338();
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = text;
				@class.string_0 = text.Substring(0, text.IndexOf(Class521.smethod_0(24872)));
				IEnumerable<StkSymbol> enumerable = ienumerable_0.Where(new Func<StkSymbol, bool>(@class.method_0));
				if (enumerable.Any<StkSymbol>())
				{
					this.method_79(buttonItem, enumerable);
					buttonItem_72.SubItems.Add(buttonItem);
				}
			}
		}

		// Token: 0x06001C3B RID: 7227 RVA: 0x000C62B0 File Offset: 0x000C44B0
		private void method_79(ButtonItem buttonItem_72, IEnumerable<StkSymbol> ienumerable_0)
		{
			buttonItem_72.SubItems.Clear();
			foreach (StkSymbol stkSymbol in ienumerable_0)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = stkSymbol.CNName + Class521.smethod_0(24872) + stkSymbol.Code + Class521.smethod_0(5046);
				buttonItem.Click += this.method_80;
				buttonItem.Tag = stkSymbol.ID;
				buttonItem.Tooltip = stkSymbol.CNName;
				if (stkSymbol.Code == Base.Data.CurrSelectedSymbol.Code)
				{
					buttonItem.Checked = true;
				}
				buttonItem_72.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001C3C RID: 7228 RVA: 0x000C6390 File Offset: 0x000C4590
		private void method_80(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (!buttonItem.Checked)
			{
				StkSymbol stkSymbol_ = SymbMgr.smethod_3(Convert.ToInt32(buttonItem.Tag));
				Base.UI.smethod_177(Class521.smethod_0(4654), this.method_4());
				Base.UI.smethod_175(stkSymbol_);
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C3D RID: 7229 RVA: 0x0000BD21 File Offset: 0x00009F21
		private void buttonItem_11_Click(object sender, EventArgs e)
		{
			this.method_81();
		}

		// Token: 0x06001C3E RID: 7230 RVA: 0x000C63E0 File Offset: 0x000C45E0
		public void method_81()
		{
			if (!Base.UI.Form.IsInBlindTestMode && !Base.UI.IsInCreateNewPageState)
			{
				if (this.timer_1.Enabled)
				{
					this.timer_1.Enabled = false;
					Base.UI.Form.IsStarted = false;
				}
				this.method_82();
			}
		}

		// Token: 0x06001C3F RID: 7231 RVA: 0x000C642C File Offset: 0x000C462C
		public void method_82()
		{
			try
			{
				if (this.buttonItem_11 != null)
				{
					this.buttonItem_11.Checked = true;
				}
			}
			catch (Exception exception_)
			{
				Class184.smethod_0(exception_);
			}
			DateSelectForm dateSelectForm = new DateSelectForm();
			dateSelectForm.Owner = this;
			dateSelectForm.FormClosed += this.method_163;
			dateSelectForm.ShowInTaskbar = false;
			dateSelectForm.ShowDialog();
		}

		// Token: 0x06001C40 RID: 7232 RVA: 0x000C6494 File Offset: 0x000C4694
		private void buttonItem_48_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_85();
			}
			else
			{
				BlindTestForm blindTestForm = new BlindTestForm();
				blindTestForm.Owner = this;
				blindTestForm.SelectionChanged += this.method_83;
				blindTestForm.ShowInTaskbar = false;
				blindTestForm.ShowDialog();
			}
		}

		// Token: 0x06001C41 RID: 7233 RVA: 0x000C64E4 File Offset: 0x000C46E4
		private void method_83(EventArgs1 eventArgs1_0)
		{
			this.list_0 = eventArgs1_0.SelectionSymbList;
			this.method_84(eventArgs1_0.NewSymbID, eventArgs1_0.NewSymbLastDT.Value, true);
			if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabCtrl != null && !Base.UI.TransTabCtrl.IsSwitchedBehind && Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.SelectedChtCtrl = Base.UI.TransTabs.method_88();
				if (Base.UI.SelectedChtCtrl != null)
				{
					Base.UI.SelectedChtCtrl.Focus();
					this.method_62(Base.UI.SelectedChtCtrl);
				}
			}
		}

		// Token: 0x06001C42 RID: 7234 RVA: 0x000C657C File Offset: 0x000C477C
		private bool method_84(int int_11, DateTime dateTime_0, bool bool_8)
		{
			if (int_11 != Base.Data.CurrSelectedSymbol.ID)
			{
				if (!Base.Data.smethod_66(int_11, dateTime_0, false, false))
				{
					if (bool_8)
					{
						Base.UI.Form.IsInBlindTestMode = false;
					}
					return false;
				}
				Base.UI.Form.IsSpanMoveNext = false;
				Base.UI.Form.IsSpanMovePrev = false;
				Base.UI.Form.LastSpanMoveDT = null;
			}
			else
			{
				SymbDataSet symbDataSet = Base.Data.CurrSymbDataSet;
				if (symbDataSet == null)
				{
					symbDataSet = Base.Data.smethod_49(int_11, true);
				}
				else if (!symbDataSet.HasValidDataSet)
				{
					symbDataSet.method_19(symbDataSet.SymblID, new DateTime?(dateTime_0));
				}
				if (symbDataSet.CurrHisDataSet == null || !symbDataSet.CurrHisDataSet.method_7(dateTime_0.Date, new DateTime?(symbDataSet.CurrStkMeta.EndDate.Value)))
				{
					if (bool_8)
					{
						Base.UI.Form.IsInBlindTestMode = false;
					}
					return false;
				}
				DateTime dateTime = dateTime_0;
				if (bool_8)
				{
					dateTime = symbDataSet.method_70(dateTime_0);
					if (dateTime != dateTime_0)
					{
						symbDataSet.CurrHisDataSet.method_15(dateTime);
					}
				}
				this.method_27(dateTime);
				Base.UI.smethod_133();
				Base.Acct.CurrAccount.LastSymbDT = new DateTime?(dateTime);
			}
			if (bool_8)
			{
				this.method_154();
				this.buttonItem_11.Enabled = false;
			}
			this.method_89();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_82();
				Base.UI.TransTabs.method_69();
				Base.UI.TransTabs.Refresh();
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				Class48.smethod_3(Class24.BlindTestEntered, Class521.smethod_0(22542) + Base.Data.CurrSelectedSymbol.Code + Class521.smethod_0(75790) + dateTime_0.ToString());
			}
			return true;
		}

		// Token: 0x06001C43 RID: 7235 RVA: 0x0000BD2B File Offset: 0x00009F2B
		private void labelItem_13_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_85();
			}
			else
			{
				this.buttonItem_48_Click(null, new EventArgs());
			}
		}

		// Token: 0x06001C44 RID: 7236 RVA: 0x000C6720 File Offset: 0x000C4920
		private void method_85()
		{
			if (MessageBox.Show(Class521.smethod_0(75803), Class521.smethod_0(7730), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				Class48.smethod_3(Class24.BlindTestExiting, Class521.smethod_0(22542) + Base.Data.CurrSelectedSymbol.Code);
				Base.UI.Form.IsInBlindTestMode = false;
				if (MessageBox.Show(Class521.smethod_0(75844), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					int value = Base.UI.Form.LastSymbIDPriorToBlindTest.Value;
					DateTime value2 = Base.UI.Form.LastSymbDTPriorToBlindTest.Value;
					if (Base.Data.UsrStkSymbols.ContainsKey(value))
					{
						SymbMgr.smethod_3(value);
						UsrStkMeta usrStkMeta = Base.Data.smethod_90(value);
						if (usrStkMeta == null)
						{
							throw new Exception(Class521.smethod_0(76050) + value.ToString() + Class521.smethod_0(76103));
						}
						if (usrStkMeta.BeginDate <= value2 && value2 <= usrStkMeta.EndDate)
						{
							if (value != Base.Data.CurrSelectedSymbol.ID)
							{
								Base.Data.smethod_75(Base.Data.CurrSymbDataSet);
								Base.Data.smethod_66(Base.UI.Form.LastSymbIDPriorToBlindTest.Value, Base.UI.Form.LastSymbDTPriorToBlindTest.Value, false, false);
							}
							else
							{
								SymbDataSet symbDataSet = Base.Data.smethod_49(value, false);
								if (symbDataSet.CurrHisDataSet.method_7(value2, new DateTime?(symbDataSet.CurrStkMeta.EndDate.Value)))
								{
									this.method_27(symbDataSet.CurrHisDataSet.CurrHisData.Date);
									Base.UI.smethod_133();
									Base.Acct.CurrAccount.LastSymbDT = new DateTime?(symbDataSet.CurrHisDataSet.CurrHisData.Date);
								}
							}
						}
						else
						{
							this.method_86(value);
						}
					}
					else
					{
						this.method_86(value);
					}
				}
				else
				{
					foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
					{
						if (chtCtrl.IfNoSync)
						{
							chtCtrl.IfNoSync = false;
							chtCtrl.LinkedSymblId = null;
						}
					}
					if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.HasValidDataSet)
					{
						this.method_46(Base.Data.CurrSymbDataSet.CurrHisData.Date);
					}
				}
				this.method_155();
				this.method_89();
				this.buttonItem_11.Enabled = true;
				if (Base.UI.TransTabs != null)
				{
					Base.UI.TransTabs.method_69();
					Base.UI.TransTabs.method_82();
					Base.UI.TransTabs.Refresh();
				}
				Class48.smethod_3(Class24.BlindTestExited, Class521.smethod_0(22542) + Base.Data.CurrSelectedSymbol.Code);
			}
		}

		// Token: 0x06001C45 RID: 7237 RVA: 0x000C6A24 File Offset: 0x000C4C24
		private void method_86(int int_11)
		{
			SymbDataSet symbDataSet = Base.Data.smethod_49(int_11, false);
			if (symbDataSet != null)
			{
				DateTime dateTime_;
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					dateTime_ = symbDataSet.CurrHisDataSet.CurrHisData.Date;
				}
				else
				{
					dateTime_ = symbDataSet.method_108();
				}
				this.method_46(dateTime_);
			}
			else
			{
				Class184.smethod_0(new Exception(Class521.smethod_0(76108)));
			}
		}

		// Token: 0x06001C46 RID: 7238 RVA: 0x0000BD4F File Offset: 0x00009F4F
		private void buttonItem_8_Click(object sender, EventArgs e)
		{
			this.method_204();
			Form20 form = new Form20();
			form.Owner = this;
			form.ShowInTaskbar = false;
			form.NewAcctCreated += this.method_87;
			form.ShowDialog();
		}

		// Token: 0x06001C47 RID: 7239 RVA: 0x000C6A80 File Offset: 0x000C4C80
		private void method_87(object sender, EventArgs13 e)
		{
			if (e.IfSwitchToNewAcct)
			{
				Base.UI.smethod_177(Class521.smethod_0(76125), this.method_4());
				try
				{
					Base.Acct.smethod_47(e.NewAcctID);
				}
				catch
				{
					Base.UI.smethod_178();
					throw;
				}
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C48 RID: 7240 RVA: 0x00006731 File Offset: 0x00004931
		private void buttonItem_47_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_115();
		}

		// Token: 0x06001C49 RID: 7241 RVA: 0x0000BD84 File Offset: 0x00009F84
		private void buttonItem_50_Click(object sender, EventArgs e)
		{
			this.method_204();
			new DataMgmtForm
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C4A RID: 7242 RVA: 0x000C6AD8 File Offset: 0x000C4CD8
		private void buttonItem_51_Click(object sender, EventArgs e)
		{
			try
			{
				string str = Class521.smethod_0(1449);
				Process.Start(new ProcessStartInfo(Class521.smethod_0(76154) + str + Class521.smethod_0(76195)));
			}
			catch
			{
			}
		}

		// Token: 0x06001C4B RID: 7243 RVA: 0x000C6B2C File Offset: 0x000C4D2C
		private void buttonItem_52_Click(object sender, EventArgs e)
		{
			try
			{
				string str = Class521.smethod_0(1449);
				Process.Start(new ProcessStartInfo(Class521.smethod_0(76154) + str + Class521.smethod_0(76200)));
			}
			catch
			{
			}
		}

		// Token: 0x06001C4C RID: 7244 RVA: 0x000C6B80 File Offset: 0x000C4D80
		private void method_88()
		{
			decimal d = Base.Trading.smethod_176();
			this.labelItem_7.Text = d.ToString(Class521.smethod_0(74951));
			decimal d2 = Math.Round(d);
			if (d2 > 0m)
			{
				this.labelItem_7.ForeColor = Color.Red;
			}
			else if (d2 < 0m)
			{
				this.labelItem_7.ForeColor = Class181.color_24;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.labelItem_7.ForeColor = Class181.color_9;
			}
			else
			{
				this.labelItem_7.ForeColor = Class181.color_1;
			}
		}

		// Token: 0x06001C4D RID: 7245 RVA: 0x000C6C24 File Offset: 0x000C4E24
		private void method_89()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				if (!Base.UI.Form.IsSingleBlindTest)
				{
					this.buttonItem_38.Text = Class521.smethod_0(24382);
					this.buttonItem_38.Tooltip = Class521.smethod_0(76213);
					this.buttonItem_38.Enabled = false;
				}
				else
				{
					if (Base.UI.CurrTradingSymbol != null)
					{
						this.buttonItem_38.Text = Base.UI.CurrTradingSymbol.Desc;
						this.buttonItem_38.Tooltip = Base.UI.CurrTradingSymbol.CNName;
					}
					this.buttonItem_38.Enabled = false;
				}
			}
			else
			{
				if (Base.UI.CurrTradingSymbol != null)
				{
					this.buttonItem_38.Text = Base.UI.CurrTradingSymbol.Desc;
					this.buttonItem_38.Tooltip = Base.UI.CurrTradingSymbol.CNName;
				}
				this.buttonItem_38.Enabled = true;
			}
		}

		// Token: 0x06001C4E RID: 7246 RVA: 0x0000BDA7 File Offset: 0x00009FA7
		private void method_90()
		{
			this.labelItem_8.Text = Class521.smethod_0(1449);
			this.labelItem_7.Text = Class521.smethod_0(2841);
		}

		// Token: 0x06001C4F RID: 7247 RVA: 0x000C6D04 File Offset: 0x000C4F04
		private void method_91(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (!buttonItem.Checked)
			{
				int num = Convert.ToInt32(buttonItem.Tag);
				Base.UI.smethod_177(Class521.smethod_0(76125), this.method_4());
				try
				{
					Base.Acct.smethod_47(num);
				}
				catch
				{
					Base.UI.smethod_178();
					throw;
				}
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C50 RID: 7248 RVA: 0x0000BDD5 File Offset: 0x00009FD5
		private void method_92(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, Class521.smethod_0(76125));
		}

		// Token: 0x06001C51 RID: 7249 RVA: 0x000C6D68 File Offset: 0x000C4F68
		private void method_93(object sender, EventArgs e)
		{
			this.buttonItem_37.Text = Base.Acct.CurrAccount.AcctName;
			this.buttonItem_37.Tooltip = Base.Acct.CurrAccount.Notes;
			this.labelItem_7.Text = Class521.smethod_0(2841);
			if (this.labelItem_7.ForeColor != Color.Black)
			{
				this.labelItem_7.ForeColor = Color.Black;
			}
			this.method_45();
			this.method_100(Enum13.const_0, Class521.smethod_0(76254));
		}

		// Token: 0x06001C52 RID: 7250 RVA: 0x000C6DF4 File Offset: 0x000C4FF4
		private void method_94()
		{
			if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_128();
			}
			else
			{
				this.method_129();
			}
			if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_152();
			}
			else
			{
				this.method_153();
			}
		}

		// Token: 0x06001C53 RID: 7251 RVA: 0x000C6E44 File Offset: 0x000C5044
		private void timer_2_Elapsed(object sender, ElapsedEventArgs e)
		{
			string text = this.labelItem_8.Text;
			if (!string.IsNullOrEmpty(text) && !text.EndsWith(Class521.smethod_0(19311)))
			{
				this.method_101(Class521.smethod_0(1449));
			}
		}

		// Token: 0x06001C54 RID: 7252 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_95(object sender, EventArgs25 e)
		{
		}

		// Token: 0x06001C55 RID: 7253 RVA: 0x0000BDEA File Offset: 0x00009FEA
		private void method_96(object sender, EventArgs e)
		{
			this.method_101(Class521.smethod_0(76283));
		}

		// Token: 0x06001C56 RID: 7254 RVA: 0x0000BDFE File Offset: 0x00009FFE
		private void method_97(object sender, EventArgs25 e)
		{
			this.method_101(Class521.smethod_0(76376));
		}

		// Token: 0x06001C57 RID: 7255 RVA: 0x0000BE12 File Offset: 0x0000A012
		private void method_98(object sender, EventArgs25 e)
		{
			this.method_101(Class521.smethod_0(76445));
		}

		// Token: 0x06001C58 RID: 7256 RVA: 0x0000BE26 File Offset: 0x0000A026
		private void method_99(object sender, EventArgs25 e)
		{
			this.method_101(Class521.smethod_0(76510));
		}

		// Token: 0x06001C59 RID: 7257 RVA: 0x000C6E8C File Offset: 0x000C508C
		private void method_100(Enum13 enum13_0, string string_6)
		{
			string str = Class521.smethod_0(1449);
			if (enum13_0 != Enum13.const_0)
			{
				if (enum13_0 == Enum13.const_1)
				{
					str = Class521.smethod_0(76592);
				}
			}
			else
			{
				str = Class521.smethod_0(76579);
			}
			this.method_101(str + string_6);
		}

		// Token: 0x06001C5A RID: 7258 RVA: 0x0000BE3A File Offset: 0x0000A03A
		private void method_101(string string_6)
		{
			this.labelItem_8.Text = string_6;
			if (this.timer_2 != null)
			{
				this.timer_2.Stop();
				this.timer_2.Start();
			}
		}

		// Token: 0x06001C5B RID: 7259 RVA: 0x000C6ED4 File Offset: 0x000C50D4
		private void method_102()
		{
			Image image_;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				image_ = Class375.line_chart_lightgray;
			}
			else
			{
				image_ = Class375.line_chart_darkblack;
			}
			Image image = TApp.smethod_7(image_, new Size(20, 20));
			this.buttonItem_22.Image = image;
		}

		// Token: 0x06001C5C RID: 7260 RVA: 0x000C6F1C File Offset: 0x000C511C
		private int? method_103()
		{
			return new int?(this.sliderItem_0.Value);
		}

		// Token: 0x06001C5D RID: 7261 RVA: 0x000C6F40 File Offset: 0x000C5140
		private int? method_104()
		{
			int? result = null;
			try
			{
				result = new int?(Convert.ToInt32(this.class304_0.Value));
			}
			catch
			{
				MessageBox.Show(Class521.smethod_0(76605) + (Base.UI.CurrTradingSymbol.IsFutures ? Class521.smethod_0(44962) : Class521.smethod_0(76630)) + Class521.smethod_0(76639), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			return result;
		}

		// Token: 0x06001C5E RID: 7262 RVA: 0x000C6FD4 File Offset: 0x000C51D4
		private decimal method_105()
		{
			return this.class304_1.Value;
		}

		// Token: 0x06001C5F RID: 7263 RVA: 0x000C6FF0 File Offset: 0x000C51F0
		private int? method_106()
		{
			return new int?(Base.UI.Form.TradingUnits);
		}

		// Token: 0x06001C60 RID: 7264 RVA: 0x000C7010 File Offset: 0x000C5210
		private decimal? method_107()
		{
			return new decimal?(Base.UI.Form.TradingPrice);
		}

		// Token: 0x06001C61 RID: 7265 RVA: 0x0000BE68 File Offset: 0x0000A068
		private void method_108(object sender, EventArgs e)
		{
			this.method_89();
			this.method_68();
			this.method_67();
		}

		// Token: 0x06001C62 RID: 7266 RVA: 0x000C7030 File Offset: 0x000C5230
		private void method_109(object sender, EventArgs e)
		{
			string string_ = Class521.smethod_0(76660) + (sender as StkSymbol).CNName + Class521.smethod_0(76673);
			if (Base.UI.Form.IsInBlindTestMode)
			{
				string_ = Class521.smethod_0(76710);
			}
			this.method_100(Enum13.const_0, string_);
		}

		// Token: 0x06001C63 RID: 7267 RVA: 0x0000BE7E File Offset: 0x0000A07E
		private void method_110(object sender, EventArgs e)
		{
			SettingsForm settingsForm = sender as SettingsForm;
			if (settingsForm.ChartThemeChanged)
			{
				Base.UI.smethod_32(Base.UI.Form.ChartTheme);
			}
			if (settingsForm.KLineTypeChanged)
			{
				Base.UI.smethod_19();
			}
			else
			{
				Base.UI.smethod_31();
			}
		}

		// Token: 0x06001C64 RID: 7268 RVA: 0x000C7084 File Offset: 0x000C5284
		private void method_111(object sender, EventArgs e)
		{
			this.bool_2 = true;
			if (Base.Data.CurrDate == default(DateTime) && Base.Data.CurrSymbDataSet.CurrStkMeta.EndDate != null)
			{
				Base.Data.CurrDate = Base.Data.CurrSymbDataSet.CurrStkMeta.EndDate.Value;
			}
			Base.Acct.CurrAccount.LastSymbDT = new DateTime?(Base.Data.CurrDate);
			this.method_24(Base.Acct.CurrAccount.LastSymbDT);
			this.method_16();
			InfoMineMgr.smethod_5(null);
			this.buttonItem_34.Text = Base.UI.Form.CurrentPageName;
			Class48.smethod_3(Class24.PageChanged, Class521.smethod_0(76751) + Base.UI.Form.CurrentPageName + Class521.smethod_0(5036) + Base.UI.smethod_102());
		}

		// Token: 0x06001C65 RID: 7269 RVA: 0x0000BEB2 File Offset: 0x0000A0B2
		private void method_112(object sender, EventArgs e)
		{
			if (!this.bool_6)
			{
				this.method_100(Enum13.const_0, Class521.smethod_0(76764));
			}
		}

		// Token: 0x06001C66 RID: 7270 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_113(object sender, EventArgs e)
		{
		}

		// Token: 0x06001C67 RID: 7271 RVA: 0x0000BECF File Offset: 0x0000A0CF
		private void method_114(object sender, EventArgs e)
		{
			if (!this.timer_1.Enabled)
			{
				this.method_203();
				this.timer_1.Enabled = true;
			}
			this.IsInRetroMode = false;
		}

		// Token: 0x06001C68 RID: 7272 RVA: 0x0000BEF9 File Offset: 0x0000A0F9
		private void method_115(object sender, EventArgs e)
		{
			if (this.timer_1.Enabled)
			{
				this.timer_1.Enabled = false;
			}
		}

		// Token: 0x06001C69 RID: 7273 RVA: 0x0000BF16 File Offset: 0x0000A116
		private void method_116(object sender, EventArgs e)
		{
			this.method_67();
		}

		// Token: 0x06001C6A RID: 7274 RVA: 0x0000BF20 File Offset: 0x0000A120
		private void method_117(object sender, EventArgs e)
		{
			this.method_71();
		}

		// Token: 0x06001C6B RID: 7275 RVA: 0x0000BF2A File Offset: 0x0000A12A
		private void method_118(object sender, EventArgs e)
		{
			this.method_45();
		}

		// Token: 0x06001C6C RID: 7276 RVA: 0x0000BF34 File Offset: 0x0000A134
		private void method_119(object sender, EventArgs e)
		{
			Base.UI.smethod_31();
		}

		// Token: 0x06001C6D RID: 7277 RVA: 0x000C715C File Offset: 0x000C535C
		private void method_120(object sender, EventArgs e)
		{
			if (this.Text.EndsWith(Class521.smethod_0(73571)))
			{
				this.Text = this.Text.Replace(Class521.smethod_0(73571), string.Empty);
			}
			this.labelItem_13.Text = Class521.smethod_0(76797);
		}

		// Token: 0x06001C6E RID: 7278 RVA: 0x000C71B8 File Offset: 0x000C53B8
		private void method_121(object sender, EventArgs e)
		{
			if (!this.Text.EndsWith(Class521.smethod_0(73571)))
			{
				this.Text += Class521.smethod_0(73571);
			}
			this.labelItem_13.Text = Class521.smethod_0(73537);
		}

		// Token: 0x06001C6F RID: 7279 RVA: 0x000C7210 File Offset: 0x000C5410
		private void method_122(Enum7 enum7_0)
		{
			if (Base.Data.UsrStandAloneStkSymbList.Count > 1)
			{
				if (Base.UI.ChtCtrlList.Exists(new Predicate<ChtCtrl>(MainForm.<>c.<>9.method_19)))
				{
					if (Base.UI.Form.IsInBlindTestMode)
					{
						if (this.list_0 == null)
						{
							this.list_0 = Base.Data.smethod_84();
						}
						if (this.list_0.Count > 1)
						{
							StkSymbol stkSymbol = this.method_123();
							Base.UI.smethod_177(Class521.smethod_0(4654), this.method_4());
							int i = 0;
							while (i < this.list_0.Count)
							{
								i++;
								UsrStkMeta usrStkMeta = Base.Data.smethod_90(stkSymbol.ID);
								if (stkSymbol != Base.Data.CurrSelectedSymbol && usrStkMeta != null && (usrStkMeta.EndDate.Value.Date - usrStkMeta.BeginDate.Value.Date).TotalDays >= (double)SymbMgr.MinBlindTestDays)
								{
									break;
								}
								stkSymbol = this.method_123();
							}
							if (stkSymbol != Base.Data.CurrSelectedSymbol)
							{
								if (Base.UI.Form.IsSingleBlindTest)
								{
									Base.UI.Form.IsSingleBlindTest = false;
								}
								Base.UI.smethod_177(Class521.smethod_0(4654), this.method_4());
								Base.Data.smethod_68(stkSymbol, false, false);
								Base.UI.smethod_178();
							}
						}
					}
					else
					{
						Base.UI.smethod_177(Class521.smethod_0(4654), this.method_4());
						Base.Data.smethod_83(enum7_0, null);
						Base.UI.smethod_178();
					}
				}
			}
		}

		// Token: 0x06001C70 RID: 7280 RVA: 0x000C738C File Offset: 0x000C558C
		private StkSymbol method_123()
		{
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (this.list_0 != null && this.list_0.Count > 1)
			{
				while (stkSymbol == Base.Data.CurrSelectedSymbol)
				{
					int num = new Random(Utility.GetRandomSeed()).Next(0, this.list_0.Count);
					if (num == this.list_0.Count)
					{
						num = this.list_0.Count - 1;
					}
					stkSymbol = this.list_0[num];
				}
			}
			return stkSymbol;
		}

		// Token: 0x06001C71 RID: 7281 RVA: 0x0000BF3D File Offset: 0x0000A13D
		private void method_124(bool bool_8, bool bool_9)
		{
			Form13 form = new Form13(new List<StkSymbol>
			{
				Base.UI.CurrTradingSymbol
			}, bool_8, bool_9, true);
			form.SymbParamsUpdated += this.method_125;
			form.Owner = this;
			form.ShowDialog();
		}

		// Token: 0x06001C72 RID: 7282 RVA: 0x000C740C File Offset: 0x000C560C
		private void method_125(object sender, EventArgs21 e)
		{
			if (e.IfTurnOnAutoStop && Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_126(true);
			}
			else if (e.IfTurnOnAutoLimit && Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_130(true);
			}
		}

		// Token: 0x06001C73 RID: 7283 RVA: 0x000C7460 File Offset: 0x000C5660
		private void method_126(bool bool_8)
		{
			if (Base.UI.CurrTradingSymbol.AutoStopLossPoints == null)
			{
				if (MessageBox.Show(Class521.smethod_0(76822), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_124(true, false);
				}
			}
			else
			{
				Base.Trading.smethod_109(Base.UI.CurrTradingSymbol.ID, true);
				if (bool_8)
				{
					this.method_128();
				}
				else
				{
					this.method_129();
				}
			}
		}

		// Token: 0x06001C74 RID: 7284 RVA: 0x0000BF78 File Offset: 0x0000A178
		private void method_127(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_128();
			}
			else
			{
				this.method_129();
			}
		}

		// Token: 0x06001C75 RID: 7285 RVA: 0x000C74D0 File Offset: 0x000C56D0
		private void method_128()
		{
			if (this.labelItem_4.Image.Tag == null || this.labelItem_4.Image.Tag.ToString() != Class521.smethod_0(76956))
			{
				this.labelItem_4.Image = Class375.autostop;
				this.labelItem_4.Image.Tag = Class521.smethod_0(76956);
				this.labelItem_4.Tooltip = Class521.smethod_0(76969);
			}
		}

		// Token: 0x06001C76 RID: 7286 RVA: 0x000C7558 File Offset: 0x000C5758
		private void method_129()
		{
			if (this.labelItem_4.Image.Tag == null || this.labelItem_4.Image.Tag.ToString() != Class521.smethod_0(77026))
			{
				this.labelItem_4.Image = Class375.autostop_gray;
				this.labelItem_4.Image.Tag = Class521.smethod_0(77026);
				this.labelItem_4.Tooltip = Class521.smethod_0(77039);
			}
		}

		// Token: 0x06001C77 RID: 7287 RVA: 0x000C75E0 File Offset: 0x000C57E0
		private void method_130(bool bool_8)
		{
			if (Base.UI.CurrTradingSymbol.AutoLimitTakePoints == null)
			{
				if (MessageBox.Show(Class521.smethod_0(77096), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_124(false, true);
				}
			}
			else
			{
				Base.Trading.smethod_110(Base.UI.CurrTradingSymbol.ID, bool_8);
				if (bool_8)
				{
					this.method_152();
				}
				else
				{
					this.method_153();
				}
			}
		}

		// Token: 0x06001C78 RID: 7288 RVA: 0x0000BF9B File Offset: 0x0000A19B
		private void method_131(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_152();
			}
			else
			{
				this.method_153();
			}
		}

		// Token: 0x06001C79 RID: 7289 RVA: 0x000C7650 File Offset: 0x000C5850
		private void labelItem_4_Click(object sender, EventArgs e)
		{
			if (this.labelItem_4.Image.Tag != null && this.labelItem_4.Image.Tag.ToString() == Class521.smethod_0(76956))
			{
				if (MessageBox.Show(Class521.smethod_0(77230), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_126(false);
				}
			}
			else if (MessageBox.Show(Class521.smethod_0(77263), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_126(true);
			}
		}

		// Token: 0x06001C7A RID: 7290 RVA: 0x000C76E8 File Offset: 0x000C58E8
		private void labelItem_5_Click(object sender, EventArgs e)
		{
			if (this.labelItem_5.Image.Tag.ToString() == Class521.smethod_0(76956))
			{
				if (MessageBox.Show(Class521.smethod_0(77296), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_130(false);
				}
			}
			else if (MessageBox.Show(Class521.smethod_0(77329), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_130(true);
			}
		}

		// Token: 0x06001C7B RID: 7291 RVA: 0x000C776C File Offset: 0x000C596C
		private void method_132()
		{
			if (Base.UI.CurrTradingSymbol.AutoStopLossPoints != null)
			{
				if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
				{
					this.method_128();
				}
				else
				{
					this.method_129();
				}
			}
			else
			{
				Base.Trading.smethod_109(Base.UI.CurrTradingSymbol.ID, false);
				this.method_129();
			}
			if (Base.UI.CurrTradingSymbol.AutoLimitTakePoints != null)
			{
				if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
				{
					this.method_152();
				}
				else
				{
					this.method_153();
				}
			}
			else
			{
				Base.Trading.smethod_110(Base.UI.CurrTradingSymbol.ID, false);
				this.method_153();
			}
		}

		// Token: 0x06001C7C RID: 7292 RVA: 0x0000BFBE File Offset: 0x0000A1BE
		private void method_133(OrderType orderType_0)
		{
			this.method_134(orderType_0, false, false);
		}

		// Token: 0x06001C7D RID: 7293 RVA: 0x000C7814 File Offset: 0x000C5A14
		private void method_134(OrderType orderType_0, bool bool_8, bool bool_9)
		{
			decimal decimal_;
			if (bool_8)
			{
				decimal_ = this.method_105();
			}
			else
			{
				decimal_ = this.method_107().Value;
			}
			this.method_135(orderType_0, decimal_, bool_9);
		}

		// Token: 0x06001C7E RID: 7294 RVA: 0x000C7848 File Offset: 0x000C5A48
		private void method_135(OrderType orderType_0, decimal decimal_1, bool bool_8)
		{
			int num;
			if (bool_8)
			{
				num = this.method_104().Value;
			}
			else
			{
				num = Base.UI.smethod_180();
			}
			Base.Trading.smethod_197(Base.UI.CurrTradingSymbol.ID, orderType_0, (long)num, decimal_1);
			Class48.smethod_3(Class24.OrderPlaced, string.Concat(new string[]
			{
				Class521.smethod_0(77362),
				orderType_0.ToString(),
				Class521.smethod_0(77399),
				num.ToString(),
				Class521.smethod_0(77412),
				decimal_1.ToString()
			}));
		}

		// Token: 0x06001C7F RID: 7295 RVA: 0x0000BF2A File Offset: 0x0000A12A
		private void method_136(EventArgs17 eventArgs17_0)
		{
			this.method_45();
		}

		// Token: 0x06001C80 RID: 7296 RVA: 0x0000BFCB File Offset: 0x0000A1CB
		private void method_137(EventArgs eventArgs_0)
		{
			this.method_88();
		}

		// Token: 0x06001C81 RID: 7297 RVA: 0x000C78E8 File Offset: 0x000C5AE8
		private void method_138(EventArgs14 eventArgs14_0)
		{
			ShownOrder shownOrder = eventArgs14_0.ShownOrder;
			if (shownOrder.OrderStatus == 2)
			{
				this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + Class521.smethod_0(77425));
				this.method_142(shownOrder);
			}
			else if (shownOrder.OrderStatus == 0)
			{
				this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + Class521.smethod_0(77446));
			}
			this.method_44();
			this.method_45();
		}

		// Token: 0x06001C82 RID: 7298 RVA: 0x0000BFD5 File Offset: 0x0000A1D5
		private void method_139(EventArgs14 eventArgs14_0)
		{
			this.method_100(Enum13.const_1, eventArgs14_0.OrderDesc + Class521.smethod_0(77463) + eventArgs14_0.ExcResultNotes);
		}

		// Token: 0x06001C83 RID: 7299 RVA: 0x000C795C File Offset: 0x000C5B5C
		private void method_140(EventArgs14 eventArgs14_0)
		{
			ShownOrder shownOrder = eventArgs14_0.ShownOrder;
			string str = Class521.smethod_0(1449);
			if (shownOrder.OrderStatus == 2)
			{
				this.method_142(shownOrder);
				str = Class521.smethod_0(77425);
			}
			else if (shownOrder.OrderStatus == 1)
			{
				str = Class521.smethod_0(77484);
			}
			this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + str);
			this.method_44();
			this.method_45();
		}

		// Token: 0x06001C84 RID: 7300 RVA: 0x000C79D0 File Offset: 0x000C5BD0
		private void method_141(EventArgs15 eventArgs15_0)
		{
			CondOrder shownCondOrder = eventArgs15_0.ShownCondOrder;
			string string_ = Class521.smethod_0(1449);
			if (shownCondOrder.OrderStatus == OrderStatus.Executed)
			{
				string_ = Class521.smethod_0(77501);
			}
			else if (shownCondOrder.OrderStatus == OrderStatus.Canceled)
			{
				string_ = Class521.smethod_0(77530);
			}
			this.method_100(Enum13.const_0, string_);
		}

		// Token: 0x06001C85 RID: 7301 RVA: 0x000C7A24 File Offset: 0x000C5C24
		private void method_142(ShownOrder shownOrder_0)
		{
			if (!Base.UI.Form.IfDisableOpenCloseSound)
			{
				string text = Class521.smethod_0(77559);
				if (shownOrder_0.OrderType == 0 || shownOrder_0.OrderType == 2)
				{
					text = Class521.smethod_0(77572);
				}
				text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, text);
				try
				{
					new SoundPlayer(text).Play();
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001C86 RID: 7302 RVA: 0x000C7A9C File Offset: 0x000C5C9C
		private void method_143(object sender, EventArgs e)
		{
			TransTabs transTabs = sender as TransTabs;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && transTabs != null && this.class304_0.Value != transTabs.InputTradingUnits)
			{
				this.class304_0.Value = transTabs.InputTradingUnits;
			}
		}

		// Token: 0x06001C87 RID: 7303 RVA: 0x000C7AEC File Offset: 0x000C5CEC
		private void method_144(object sender, EventArgs e)
		{
			TransTabs transTabs = sender as TransTabs;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && this.class304_1.Value != transTabs.InputTradingPrice)
			{
				this.class304_1.Value = transTabs.InputTradingPrice;
			}
		}

		// Token: 0x06001C88 RID: 7304 RVA: 0x000C7B38 File Offset: 0x000C5D38
		private void method_145(EventArgs16 eventArgs16_0)
		{
			ShownHisTrans shownHisTrans = eventArgs16_0.ShownHisTrans;
			if (!Base.UI.smethod_122(shownHisTrans.CreateTime, Class521.smethod_0(77585)))
			{
				if (Base.UI.smethod_123())
				{
					this.method_84(shownHisTrans.SymbolID, shownHisTrans.CreateTime, false);
				}
			}
		}

		// Token: 0x06001C89 RID: 7305 RVA: 0x0000BFFB File Offset: 0x0000A1FB
		private void method_146(object sender, MsgEventArgs e)
		{
			this.method_100(Enum13.const_0, e.Msg);
		}

		// Token: 0x06001C8A RID: 7306 RVA: 0x0000C00C File Offset: 0x0000A20C
		private void method_147(EventArgs15 eventArgs15_0)
		{
			this.method_100(Enum13.const_0, Class521.smethod_0(77727));
		}

		// Token: 0x06001C8B RID: 7307 RVA: 0x000C7B84 File Offset: 0x000C5D84
		private void method_148(EventArgs22 eventArgs22_0)
		{
			if (eventArgs22_0.TransType == Enum17.const_7)
			{
				if (Base.UI.Form.RationedShareTreatmt == null)
				{
					Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Prompt);
				}
				if (Base.UI.Form.RationedShareTreatmt.Value == RationedShareTreatmt.Prompt)
				{
					decimal? rationedSharePrice = eventArgs22_0.StSplit.RationedSharePrice;
					decimal d = 0m;
					if (rationedSharePrice.GetValueOrDefault() > d & rationedSharePrice != null)
					{
						bool enabled;
						if (enabled = this.timer_1.Enabled)
						{
							this.method_204();
						}
						if (MessageBox.Show(string.Concat(new object[]
						{
							Class521.smethod_0(77760),
							eventArgs22_0.StSplit.RationedShares.Value * 10m / 1.00000000000000000m,
							Class521.smethod_0(77801),
							eventArgs22_0.StSplit.RationedSharePrice.Value,
							Class521.smethod_0(77818)
						}), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							new Form8(eventArgs22_0)
							{
								Owner = this
							}.ShowDialog();
						}
						else
						{
							eventArgs22_0.Cancel = true;
						}
						if (enabled)
						{
							this.method_202();
						}
					}
				}
			}
		}

		// Token: 0x06001C8C RID: 7308 RVA: 0x000C7CE4 File Offset: 0x000C5EE4
		private void method_149(EventArgs22 eventArgs22_0)
		{
			string text = string.Empty;
			if (eventArgs22_0.TransType == Enum17.const_0)
			{
				if (eventArgs22_0.TranStock.Units > 0L)
				{
					object[] array = new object[4];
					array[0] = Class521.smethod_0(77851);
					array[1] = eventArgs22_0.TranStock.Units;
					array[2] = Class521.smethod_0(11734);
					int num = 3;
					decimal? profit = eventArgs22_0.TranStock.Profit;
					decimal d = 0m;
					array[num] = ((profit.GetValueOrDefault() > d & profit != null) ? (Class521.smethod_0(77880) + eventArgs22_0.TranStock.Profit + Class521.smethod_0(77893)) : Class521.smethod_0(74497));
					text = string.Concat(array);
				}
				else
				{
					decimal? profit = eventArgs22_0.TranStock.Profit;
					decimal d = 0m;
					if (profit.GetValueOrDefault() > d & profit != null)
					{
						text = Class521.smethod_0(77902) + eventArgs22_0.TranStock.Profit + Class521.smethod_0(77893);
					}
				}
			}
			if (!string.IsNullOrEmpty(text))
			{
				this.method_100(Enum13.const_0, text);
			}
		}

		// Token: 0x06001C8D RID: 7309 RVA: 0x0000C021 File Offset: 0x0000A221
		private void method_150(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_1, Class521.smethod_0(77931));
		}

		// Token: 0x06001C8E RID: 7310 RVA: 0x0000C036 File Offset: 0x0000A236
		private void method_151(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_1, Class521.smethod_0(78000));
		}

		// Token: 0x06001C8F RID: 7311 RVA: 0x000C7E20 File Offset: 0x000C6020
		private void method_152()
		{
			if (this.labelItem_5.Image.Tag == null || this.labelItem_5.Image.Tag.ToString() != Class521.smethod_0(76956))
			{
				this.labelItem_5.Image = Class375.autolimit;
				this.labelItem_5.Image.Tag = Class521.smethod_0(76956);
				this.labelItem_5.Tooltip = Class521.smethod_0(78077);
			}
		}

		// Token: 0x06001C90 RID: 7312 RVA: 0x000C7EA8 File Offset: 0x000C60A8
		private void method_153()
		{
			if (this.labelItem_5.Image.Tag == null || this.labelItem_5.Image.Tag.ToString() != Class521.smethod_0(77026))
			{
				this.labelItem_5.Image = Class375.autolimit_gray;
				this.labelItem_5.Image.Tag = Class521.smethod_0(77026);
				this.labelItem_5.Tooltip = Class521.smethod_0(78134);
			}
		}

		// Token: 0x06001C91 RID: 7313 RVA: 0x000C7F30 File Offset: 0x000C6130
		private void method_154()
		{
			if (this.labelItem_13.Image.Tag == null || this.labelItem_13.Image.Tag.ToString() != Class521.smethod_0(76956))
			{
				this.labelItem_13.Image = Class375.GlassFace_18;
				this.labelItem_13.Image.Tag = Class521.smethod_0(76956);
				this.buttonItem_11.Tooltip = Class521.smethod_0(73438);
				this.labelItem_13.Tooltip = Class521.smethod_0(78191);
			}
		}

		// Token: 0x06001C92 RID: 7314 RVA: 0x000C7FCC File Offset: 0x000C61CC
		private void method_155()
		{
			if (this.labelItem_13.Image.Tag == null || this.labelItem_13.Image.Tag.ToString() != Class521.smethod_0(77026))
			{
				this.labelItem_13.Image = Class375.GlassFace_18_BW;
				this.labelItem_13.Image.Tag = Class521.smethod_0(77026);
				this.buttonItem_11.Tooltip = Class521.smethod_0(73417) + Class210.smethod_5(Enum3.const_20).ShortCutKeyString;
				this.labelItem_13.Tooltip = Class521.smethod_0(78256);
			}
		}

		// Token: 0x06001C93 RID: 7315 RVA: 0x000C807C File Offset: 0x000C627C
		private void class304_0_ValueChanged(object sender, EventArgs e)
		{
			if (Base.UI.Form.TradingUnits != this.class304_0.Value)
			{
				Base.UI.Form.TradingUnits = Convert.ToInt32(this.class304_0.Value);
			}
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.TransTabs != null && Base.UI.TransTabs.InputTradingUnits != this.class304_0.Value)
			{
				Base.UI.TransTabs.InputTradingUnits = this.class304_0.Value;
			}
		}

		// Token: 0x06001C94 RID: 7316 RVA: 0x000C810C File Offset: 0x000C630C
		private void class304_1_ValueChanged(object sender, EventArgs e)
		{
			this.method_69();
			decimal num = 0m;
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet != null && symbDataSet.LastHisData != null)
			{
				num = Convert.ToDecimal(symbDataSet.LastHisData.Close);
			}
			if (this.decimal_0 == 0m && this.class304_1.Value != num && this.class304_1.Value == this.class304_1.Increment)
			{
				this.class304_1.Value = num;
			}
			this.decimal_0 = this.class304_1.Value;
			Base.UI.Form.TradingPrice = this.decimal_0;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.TransTabs != null && Base.UI.TransTabs.InputTradingPrice != this.class304_1.Value)
			{
				Base.UI.TransTabs.InputTradingPrice = this.class304_1.Value;
			}
		}

		// Token: 0x06001C95 RID: 7317 RVA: 0x000C820C File Offset: 0x000C640C
		private void method_156(object sender, CancelEventArgs e)
		{
			if (this.class304_1.Value == 0m)
			{
				decimal d = Convert.ToDecimal(Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false).LastHisData.Close);
				this.class304_1.Value = d - this.class304_1.Increment;
			}
		}

		// Token: 0x06001C96 RID: 7318 RVA: 0x0000C04B File Offset: 0x0000A24B
		private void class304_1_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfFollowPrcInTradingTab)
			{
				this.timer_1.Stop();
			}
		}

		// Token: 0x06001C97 RID: 7319 RVA: 0x000C8270 File Offset: 0x000C6470
		private void method_157(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged += this.method_165;
			symbDataSet_0.SpltDayEnter += this.method_176;
			symbDataSet_0.SpltDayLeave += this.method_177;
			symbDataSet_0.SpltDaySpan += this.method_178;
		}

		// Token: 0x06001C98 RID: 7320 RVA: 0x000C82C8 File Offset: 0x000C64C8
		private void method_158(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged -= this.method_165;
			symbDataSet_0.SpltDayEnter -= this.method_176;
			symbDataSet_0.SpltDayLeave -= this.method_177;
			symbDataSet_0.SpltDaySpan -= this.method_178;
		}

		// Token: 0x06001C99 RID: 7321 RVA: 0x000C8320 File Offset: 0x000C6520
		private void method_159(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_157(symbDataSet_);
		}

		// Token: 0x06001C9A RID: 7322 RVA: 0x000C8340 File Offset: 0x000C6540
		private void method_160(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_158(symbDataSet_);
		}

		// Token: 0x06001C9B RID: 7323 RVA: 0x0000C066 File Offset: 0x0000A266
		private void method_161(object sender, EventArgs e)
		{
			this.method_162();
		}

		// Token: 0x06001C9C RID: 7324 RVA: 0x000C8360 File Offset: 0x000C6560
		private void method_162()
		{
			this.timer_1.Enabled = false;
			Base.UI.Form.IsStarted = false;
			Base.UI.Form.IsSpanMoveNext = false;
			Base.UI.Form.IsSpanMovePrev = false;
			if (this.buttonItem_4.Checked)
			{
				this.buttonItem_4.Checked = false;
			}
			this.method_27(Base.Data.CurrDate);
			Base.UI.smethod_133();
			Base.Acct.CurrAccount.LastSymbDT = new DateTime?(Base.Data.CurrDate);
			List<ChtCtrl_KLine> chtCtrl_KLineList = Base.UI.ChtCtrl_KLineList;
			if (chtCtrl_KLineList != null)
			{
				foreach (ChtCtrl_KLine chtCtrl_KLine in chtCtrl_KLineList)
				{
					if (chtCtrl_KLine.Symbol.IsStock && chtCtrl_KLine.SymbDataSet.CurrSymbStSpltList != null && chtCtrl_KLine.SymbDataSet.CurrSymbStSpltList.Any<StSplit>())
					{
						chtCtrl_KLine.Chart_CS.method_217();
					}
				}
			}
		}

		// Token: 0x06001C9D RID: 7325 RVA: 0x0000C070 File Offset: 0x0000A270
		private void method_163(object sender, FormClosedEventArgs e)
		{
			this.buttonItem_11.Checked = false;
		}

		// Token: 0x06001C9E RID: 7326 RVA: 0x000C8454 File Offset: 0x000C6654
		private void method_164(EventArgs1 eventArgs1_0)
		{
			DateTime? dateTime = eventArgs1_0.NewSymbLastDT;
			if (dateTime == null)
			{
				dateTime = Base.Acct.CurrAccount.LastSymbDT;
			}
			if (dateTime != null)
			{
				if (eventArgs1_0.IfOnlyNonSyncSelCht)
				{
					ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
					if (selectedChtCtrl.SymbDataSet.CurrHisDataSet != null && selectedChtCtrl.SymbDataSet.method_107(dateTime.Value))
					{
						selectedChtCtrl.method_13(dateTime.Value);
					}
					else
					{
						selectedChtCtrl.HisDataPeriodSet = HisDataPeriodSet.smethod_3(selectedChtCtrl.SymbDataSet.SymblID, dateTime.Value, selectedChtCtrl.PeriodType, selectedChtCtrl.PeriodUnits);
						selectedChtCtrl.method_18(dateTime.Value);
					}
				}
				else
				{
					foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
					{
						if (chtCtrl.SymbDataSet.CurrHisDataSet != null && !chtCtrl.SymbDataSet.IsCurrDateNotListedYet)
						{
							chtCtrl.vmethod_10(chtCtrl.PeriodType, chtCtrl.PeriodUnits, dateTime.Value);
						}
						else
						{
							chtCtrl.HisDataPeriodSet = HisDataPeriodSet.smethod_3(chtCtrl.SymbDataSet.SymblID, dateTime.Value, chtCtrl.PeriodType, chtCtrl.PeriodUnits);
							chtCtrl.method_18(dateTime.Value);
						}
					}
				}
				Base.Data.CurrDate = dateTime.Value;
				Base.Acct.smethod_36();
				Base.Acct.CurrAccount.LastSymbID = new int?(Base.Data.CurrSelectedSymbol.ID);
				if (!eventArgs1_0.IfOnlyNonSyncSelCht || eventArgs1_0.NewSymbID == Base.Data.CurrSelectedSymbol.ID)
				{
					Base.Acct.CurrAccount.LastSymbDT = new DateTime?(dateTime.Value);
				}
				this.method_5();
				this.method_94();
				Class48.smethod_3(Class24.SymblChanged, Class521.smethod_0(78321) + SymbMgr.smethod_3(eventArgs1_0.NewSymbID).Code + Class521.smethod_0(11876) + string.Format(Class521.smethod_0(78334), Base.Data.CurrDate));
			}
			this.method_89();
			if (Base.UI.CurrTradingSymbol.DefaultUnits != null)
			{
				this.class304_0.Value = Base.UI.CurrTradingSymbol.DefaultUnits.Value;
			}
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet != null && symbDataSet.HasValidDataSet && this.method_66(symbDataSet.CurrHisData))
			{
				this.method_69();
			}
			this.method_68();
			this.method_67();
		}

		// Token: 0x06001C9F RID: 7327 RVA: 0x000C86EC File Offset: 0x000C68EC
		private void method_165(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.Form.IfFollowPrcInTradingTab)
			{
				SymbDataSet symbDataSet = sender as SymbDataSet;
				if (symbDataSet.CurrSymbol == Base.UI.CurrTradingSymbol)
				{
					this.method_66(symbDataSet.CurrHisDataSet.CurrHisData);
				}
			}
			this.method_45();
		}

		// Token: 0x06001CA0 RID: 7328 RVA: 0x0000C080 File Offset: 0x0000A280
		private void method_166(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, Class521.smethod_0(4654));
		}

		// Token: 0x06001CA1 RID: 7329 RVA: 0x0000C095 File Offset: 0x0000A295
		private void method_167(object sender, EventArgs e)
		{
			this.method_101(string.Empty);
		}

		// Token: 0x06001CA2 RID: 7330 RVA: 0x0000C0A4 File Offset: 0x0000A2A4
		private void method_168(object sender, EventArgs e)
		{
			Base.Data.smethod_60();
		}

		// Token: 0x06001CA3 RID: 7331 RVA: 0x0000C080 File Offset: 0x0000A280
		private void method_169(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, Class521.smethod_0(4654));
		}

		// Token: 0x06001CA4 RID: 7332 RVA: 0x0000C095 File Offset: 0x0000A295
		private void method_170(object sender, EventArgs e)
		{
			this.method_101(string.Empty);
		}

		// Token: 0x06001CA5 RID: 7333 RVA: 0x0000C0AD File Offset: 0x0000A2AD
		private void method_171(object sender, EventArgs e)
		{
			if (!this.labelItem_8.Text.Contains(Class521.smethod_0(78363)))
			{
				this.method_101(string.Empty);
			}
		}

		// Token: 0x06001CA6 RID: 7334 RVA: 0x0000C0D8 File Offset: 0x0000A2D8
		private void method_172(object sender, EventArgs e)
		{
			Base.UI.smethod_178();
			MessageBox.Show(Class521.smethod_0(78388), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06001CA7 RID: 7335 RVA: 0x0000C0FE File Offset: 0x0000A2FE
		private void method_173(object sender, EventArgs e)
		{
			this.method_101(Class521.smethod_0(78449));
		}

		// Token: 0x06001CA8 RID: 7336 RVA: 0x000C8740 File Offset: 0x000C6940
		private void method_174(EventArgs5 eventArgs5_0)
		{
			if (!string.IsNullOrEmpty(eventArgs5_0.Msg))
			{
				try
				{
					Base.UI.smethod_177(eventArgs5_0.Msg, this.method_4());
					base.Activate();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06001CA9 RID: 7337 RVA: 0x000C8788 File Offset: 0x000C6988
		private void method_175(object sender, EventArgs e)
		{
			try
			{
				Base.UI.smethod_178();
			}
			catch
			{
			}
		}

		// Token: 0x06001CAA RID: 7338 RVA: 0x0000C112 File Offset: 0x0000A312
		private void method_176(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAB RID: 7339 RVA: 0x000C87B4 File Offset: 0x000C69B4
		private void method_177(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone && Base.UI.Form.IsJustSpanMoved && Base.UI.Form.LastSpanMoveDT != null)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAC RID: 7340 RVA: 0x0000C112 File Offset: 0x0000A312
		private void method_178(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAD RID: 7341 RVA: 0x000041B9 File Offset: 0x000023B9
		private void method_179(object sender, EventArgs e)
		{
		}

		// Token: 0x06001CAE RID: 7342 RVA: 0x000C8800 File Offset: 0x000C6A00
		private void buttonItem_12_PopupOpen(object sender, EventArgs e)
		{
			ButtonItem buttonItem = this.buttonItem_12;
			buttonItem.SubItems.Clear();
			buttonItem.SubItems.Add(this.buttonItem_14);
			buttonItem.SubItems.Add(this.buttonItem_15);
			buttonItem.SubItems.Add(this.buttonItem_16);
			buttonItem.SubItems.Add(this.buttonItem_13);
			buttonItem.SubItems.Add(this.buttonItem_18);
			buttonItem.SubItems.Add(this.buttonItem_17);
			this.buttonItem_13.SubItems.Clear();
			foreach (ChartPage chartPage in Base.UI.ChartPageList)
			{
				ButtonItem buttonItem2 = new ButtonItem();
				buttonItem2.Text = chartPage.Name;
				buttonItem2.Click += this.method_182;
				this.buttonItem_13.SubItems.Add(buttonItem2);
				if (chartPage.Name == Base.UI.Form.CurrentPageName)
				{
					buttonItem2.Checked = true;
				}
			}
			this.buttonItem_18.SubItems.Clear();
			foreach (ChartPage chartPage2 in Base.UI.ChartPageList)
			{
				ButtonItem buttonItem3 = new ButtonItem();
				buttonItem3.Text = chartPage2.Name;
				buttonItem3.Click += this.method_184;
				this.buttonItem_18.SubItems.Add(buttonItem3);
				if (chartPage2.Name == Base.UI.Form.CurrentPageName)
				{
					ButtonItem buttonItem4 = buttonItem3;
					buttonItem4.Text += Class521.smethod_0(78526);
					buttonItem3.Enabled = false;
				}
			}
			this.method_180();
		}

		// Token: 0x06001CAF RID: 7343 RVA: 0x0000C132 File Offset: 0x0000A332
		private void method_180()
		{
			if (Base.UI.Form.IfAutoSavePageOnExit)
			{
				this.buttonItem_17.Checked = true;
			}
			else
			{
				this.buttonItem_17.Checked = false;
			}
		}

		// Token: 0x06001CB0 RID: 7344 RVA: 0x00006930 File Offset: 0x00004B30
		private void buttonItem_15_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_81();
		}

		// Token: 0x06001CB1 RID: 7345 RVA: 0x0000C15C File Offset: 0x0000A35C
		private void buttonItem_16_Click(object sender, EventArgs e)
		{
			Form17 form = new Form17();
			form.FormClosed += new FormClosedEventHandler(this.method_181);
			form.Show();
		}

		// Token: 0x06001CB2 RID: 7346 RVA: 0x0000C17C File Offset: 0x0000A37C
		private void buttonItem_17_Click(object sender, EventArgs e)
		{
			if (this.buttonItem_17.Checked)
			{
				Base.UI.Form.IfAutoSavePageOnExit = false;
			}
			else
			{
				Base.UI.Form.IfAutoSavePageOnExit = true;
			}
		}

		// Token: 0x06001CB3 RID: 7347 RVA: 0x0000C1A5 File Offset: 0x0000A3A5
		private void method_181(object sender, EventArgs e)
		{
			this.buttonItem_34.Text = Base.UI.Form.CurrentPageName;
		}

		// Token: 0x06001CB4 RID: 7348 RVA: 0x000C89F8 File Offset: 0x000C6BF8
		private void method_182(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text != Base.UI.Form.CurrentPageName)
			{
				this.method_183(buttonItem.Text);
			}
		}

		// Token: 0x06001CB5 RID: 7349 RVA: 0x000C8A34 File Offset: 0x000C6C34
		private void method_183(string string_6)
		{
			Class48.smethod_3(Class24.PageChanging, Class521.smethod_0(78547) + string_6);
			Base.UI.smethod_177(Class521.smethod_0(78560), this.method_4());
			this.method_100(Enum13.const_0, Class521.smethod_0(78560));
			this.bool_6 = true;
			if (this.bar_0.Enabled)
			{
				if (this.bar_0.Visible && Base.UI.Form.IsTransTabBarMaximized)
				{
					this.method_197(!Base.UI.Form.IsTransTabBarMaximized, true);
				}
				else if (Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else if (Base.UI.Form.IsTransTabMaximized)
			{
				Base.UI.TransTabCtrl.SetTransTabsMaximization();
			}
			else if (Base.UI.Form.IfAutoSavePageOnExit)
			{
				Base.UI.smethod_81();
			}
			Base.Acct.smethod_36();
			this.bar_0.Enabled = false;
			this.bar_0.Hide();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.smethod_154();
				Base.UI.TransTabs.Dispose();
				Base.UI.TransTabs = null;
			}
			Base.UI.smethod_92(string_6);
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			this.bool_6 = false;
			Base.UI.smethod_178();
			this.method_100(Enum13.const_0, Class521.smethod_0(78589));
		}

		// Token: 0x06001CB6 RID: 7350 RVA: 0x000C8B70 File Offset: 0x000C6D70
		private void method_184(object sender, EventArgs e)
		{
			this.buttonItem_5.ShowSubItems = false;
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text == Base.UI.Form.CurrentPageName)
			{
				MessageBox.Show(Class521.smethod_0(78618), Class521.smethod_0(10032), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else if (MessageBox.Show(Class521.smethod_0(78683) + buttonItem.Text + Class521.smethod_0(78716), Class521.smethod_0(7730), MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				Base.UI.smethod_93(buttonItem.Text);
			}
			this.buttonItem_5.ShowSubItems = true;
		}

		// Token: 0x06001CB7 RID: 7351 RVA: 0x000C8C14 File Offset: 0x000C6E14
		private void method_185(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text != Base.UI.Form.CurrentPageName)
			{
				this.method_183(buttonItem.Text);
				this.buttonItem_34.Text = buttonItem.Text;
			}
		}

		// Token: 0x06001CB8 RID: 7352 RVA: 0x0000C1BE File Offset: 0x0000A3BE
		private void buttonItem_34_PopupOpen(object sender, EventArgs e)
		{
			this.method_186(this.buttonItem_34);
		}

		// Token: 0x06001CB9 RID: 7353 RVA: 0x000C8C60 File Offset: 0x000C6E60
		private void method_186(ButtonItem buttonItem_72)
		{
			buttonItem_72.SubItems.Clear();
			buttonItem_72.SubItems.Add(this.buttonItem_14);
			buttonItem_72.SubItems.Add(this.buttonItem_15);
			buttonItem_72.SubItems.Add(this.buttonItem_16);
			buttonItem_72.SubItems.Add(this.buttonItem_17);
			this.method_180();
			for (int i = 0; i < Base.UI.ChartPageList.Count; i++)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = Base.UI.ChartPageList[i].Name;
				buttonItem.Click += this.method_185;
				if (buttonItem.Text == Base.UI.Form.CurrentPageName)
				{
					buttonItem.Checked = true;
				}
				else
				{
					buttonItem.Checked = false;
				}
				if (i == 0)
				{
					buttonItem.BeginGroup = true;
				}
				buttonItem_72.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001CBA RID: 7354 RVA: 0x0000C1CE File Offset: 0x0000A3CE
		private void buttonItem_37_PopupOpen(object sender, EventArgs e)
		{
			this.method_187(this.buttonItem_37, false);
		}

		// Token: 0x06001CBB RID: 7355 RVA: 0x000C8D4C File Offset: 0x000C6F4C
		private void method_187(ButtonItem buttonItem_72, bool bool_8)
		{
			buttonItem_72.SubItems.Clear();
			if (!bool_8)
			{
				buttonItem_72.SubItems.Add(this.buttonItem_8);
			}
			for (int i = 0; i < Base.Acct.CurrAccounts.Count; i++)
			{
				Account account = Base.Acct.CurrAccounts[i];
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = account.AcctName;
				buttonItem.Click += this.method_91;
				buttonItem.Tag = account.ID;
				buttonItem.Tooltip = account.Notes;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					buttonItem.Checked = true;
				}
				if (!bool_8 && i == 0)
				{
					buttonItem.BeginGroup = true;
				}
				buttonItem_72.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001CBC RID: 7356 RVA: 0x000C8E18 File Offset: 0x000C7018
		private void buttonItem_14_Click(object sender, EventArgs e)
		{
			this.bar_1.Visible = false;
			this.bar_2.Visible = false;
			this.bar_4.Visible = false;
			this.bar_3.Visible = false;
			this.buttonItem_34.Enabled = false;
			this.bar_6.Location = new Point(base.Width - 60, 0);
			this.bar_6.Dock = DockStyle.Right;
			this.bar_6.Visible = true;
			if (this.bar_0.Visible)
			{
				this.bar_0.Hide();
			}
			Class310 @class = new Class310();
			this.panel_0.Controls.Add(@class);
			@class.BringToFront();
			@class.Disposed += this.method_188;
			Base.UI.IsInCreateNewPageState = true;
		}

		// Token: 0x06001CBD RID: 7357 RVA: 0x000C8EE4 File Offset: 0x000C70E4
		private void method_188(object sender, EventArgs e)
		{
			this.bar_1.Visible = true;
			this.bar_2.Visible = true;
			this.bar_3.Visible = true;
			this.bar_4.Visible = true;
			this.buttonItem_34.Enabled = true;
			this.bar_6.Visible = false;
			if (Base.UI.Form.IfShowTransTabsBar && this.bar_0.Enabled)
			{
				this.bar_0.Visible = true;
			}
		}

		// Token: 0x06001CBE RID: 7358 RVA: 0x000C8F60 File Offset: 0x000C7160
		private void buttonItem_35_Click(object sender, EventArgs e)
		{
			foreach (object obj in this.panel_0.Controls)
			{
				Control control = (Control)obj;
				if (control.GetType() == typeof(Class310))
				{
					this.class310_0 = (Class310)control;
					if (this.class310_0.IfChanged)
					{
						DialogResult dialogResult = MessageBox.Show(Class521.smethod_0(78729), Class521.smethod_0(7730), MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
						if (dialogResult == DialogResult.Yes)
						{
							if (this.class310_0.IfBlankPanelExist)
							{
								MessageBox.Show(Class521.smethod_0(78766), Class521.smethod_0(7587), MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							}
							else
							{
								Form17 form = new Form17(new ChartUISettings?(this.class310_0.ChartUI), false);
								form.PageSaved += this.method_189;
								form.ShowDialog();
							}
						}
						else if (dialogResult == DialogResult.No)
						{
							this.method_190();
						}
					}
					else
					{
						this.method_190();
					}
				}
			}
		}

		// Token: 0x06001CBF RID: 7359 RVA: 0x0000C1DF File Offset: 0x0000A3DF
		private void method_189(object sender, EventArgs e)
		{
			this.method_190();
		}

		// Token: 0x06001CC0 RID: 7360 RVA: 0x0000C1E9 File Offset: 0x0000A3E9
		private void method_190()
		{
			this.class310_0.Dispose();
			this.panel_0.Controls.Remove(this.class310_0);
			this.class310_0 = null;
			Base.UI.IsInCreateNewPageState = false;
		}

		// Token: 0x06001CC1 RID: 7361 RVA: 0x000C9080 File Offset: 0x000C7280
		private void method_191()
		{
			if (this.bar_1.BarState == eBarState.Docked)
			{
				this.struct6_0.point_0 = this.bar_1.Location;
			}
			if (this.bar_2.BarState == eBarState.Docked)
			{
				this.struct6_0.point_1 = this.bar_2.Location;
			}
			if (this.bar_4.BarState == eBarState.Docked)
			{
				this.struct6_0.point_3 = this.bar_4.Location;
			}
			if (this.bar_3.BarState == eBarState.Docked)
			{
				this.struct6_0.point_2 = this.bar_3.Location;
			}
		}

		// Token: 0x06001CC2 RID: 7362 RVA: 0x0000C21B File Offset: 0x0000A41B
		private void method_192()
		{
			if (this.bar_0.Enabled)
			{
				this.nullable_0 = new int?(this.bar_0.DockedSite.Height);
			}
		}

		// Token: 0x06001CC3 RID: 7363 RVA: 0x000C9120 File Offset: 0x000C7320
		private void method_193()
		{
			if (this.bar_1.BarState == eBarState.Docked)
			{
				this.bar_1.Location = this.struct6_0.point_0;
			}
			if (this.bar_2.BarState == eBarState.Docked)
			{
				this.bar_2.Location = this.struct6_0.point_1;
			}
			if (this.bar_4.BarState == eBarState.Docked)
			{
				this.bar_4.Location = this.struct6_0.point_3;
			}
			if (this.bar_3.BarState == eBarState.Docked)
			{
				this.bar_3.Location = this.struct6_0.point_2;
			}
		}

		// Token: 0x06001CC4 RID: 7364 RVA: 0x000C91C0 File Offset: 0x000C73C0
		private void method_194()
		{
			if (this.bar_0.Enabled && !this.bar_0.AutoHide && this.nullable_0 != null)
			{
				this.bar_0.DockedSite.Height = this.nullable_0.Value;
			}
		}

		// Token: 0x06001CC5 RID: 7365 RVA: 0x0000C247 File Offset: 0x0000A447
		private void bar_0_BarDock(object sender, EventArgs e)
		{
			Base.UI.Form.AcctTransBar_DockSide = this.bar_0.DockSide;
		}

		// Token: 0x06001CC6 RID: 7366 RVA: 0x0000C260 File Offset: 0x0000A460
		private void bar_0_AutoHideDisplay(object sender, AutoHideDisplayEventArgs e)
		{
			if (this.bar_0.Items.Count > 0)
			{
				this.bar_0.Items[0].Visible = true;
			}
		}

		// Token: 0x06001CC7 RID: 7367 RVA: 0x000C9214 File Offset: 0x000C7414
		private void bar_0_SizeChanged(object sender, EventArgs e)
		{
			DockSite dockSite;
			int num;
			if (this.bar_0.DockSide == eDockSide.Bottom)
			{
				dockSite = this.dockSite_0;
				num = Base.UI.Form.OriDockSiteBelowHeight;
			}
			else
			{
				dockSite = this.dockSite_3;
				num = Base.UI.Form.OriDockSiteTopHeight;
			}
			if (dockSite.Height > base.Height - 70)
			{
				if (num < base.Height - 70)
				{
					dockSite.Height = num;
				}
				else
				{
					dockSite.Height = base.Height - 70;
				}
			}
		}

		// Token: 0x06001CC8 RID: 7368 RVA: 0x0000C28E File Offset: 0x0000A48E
		private void method_195(object sender, EventArgs e)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.Focus();
			}
			this.method_196();
		}

		// Token: 0x06001CC9 RID: 7369 RVA: 0x0000C2AA File Offset: 0x0000A4AA
		private void method_196()
		{
			this.method_197(!Base.UI.Form.IsTransTabBarMaximized, false);
		}

		// Token: 0x06001CCA RID: 7370 RVA: 0x000C928C File Offset: 0x000C748C
		private void method_197(bool bool_8, bool bool_9 = false)
		{
			if (bool_8)
			{
				if (!Base.UI.Form.IsTransTabBarMaximized)
				{
					if (this.bar_0.DockSide == eDockSide.Bottom)
					{
						Base.UI.Form.OriDockSiteBelowHeight = this.dockSite_0.Height;
					}
					else
					{
						Base.UI.Form.OriDockSiteTopHeight = this.dockSite_3.Height;
					}
				}
				if (bool_9 && Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else if (this.bar_0.DockSide == eDockSide.Bottom)
			{
				this.dockSite_0.Height = Base.UI.Form.OriDockSiteBelowHeight;
			}
			else
			{
				this.dockSite_3.Height = Base.UI.Form.OriDockSiteTopHeight;
			}
			Base.UI.Form.IsTransTabBarMaximized = bool_8;
			this.method_198();
		}

		// Token: 0x06001CCB RID: 7371 RVA: 0x000C9348 File Offset: 0x000C7548
		private bool method_198()
		{
			bool result;
			if (Base.UI.Form.IsTransTabBarMaximized)
			{
				if (this.bar_0.DockSide == eDockSide.Bottom)
				{
					this.dockSite_0.Height = Base.UI.Form.OriDockSiteBelowHeight + this.panel_0.Height;
				}
				else if (this.bar_0.DockSide == eDockSide.Top)
				{
					this.dockSite_3.Height = Base.UI.Form.OriDockSiteTopHeight + this.panel_0.Height;
				}
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001CCC RID: 7372 RVA: 0x000C93CC File Offset: 0x000C75CC
		private void bar_2_BarDock(object sender, EventArgs e)
		{
			if (this.bar_2.DockSide != eDockSide.Left)
			{
				if (this.bar_2.DockSide != eDockSide.Right)
				{
					if (this.sliderItem_0.SliderOrientation != eOrientation.Horizontal)
					{
						this.sliderItem_0.SliderOrientation = eOrientation.Horizontal;
						return;
					}
					return;
				}
			}
			if (this.sliderItem_0.SliderOrientation != eOrientation.Vertical)
			{
				this.sliderItem_0.SliderOrientation = eOrientation.Vertical;
			}
		}

		// Token: 0x06001CCD RID: 7373 RVA: 0x0000C2C2 File Offset: 0x0000A4C2
		private void bar_2_BarUndock(object sender, EventArgs e)
		{
			if (this.sliderItem_0.SliderOrientation != eOrientation.Horizontal)
			{
				this.sliderItem_0.SliderOrientation = eOrientation.Horizontal;
			}
		}

		// Token: 0x06001CCE RID: 7374 RVA: 0x0000C2DF File Offset: 0x0000A4DF
		private void bar_0_Closing(object sender, BarClosingEventArgs e)
		{
			if (Base.UI.Form.IfShowTransTabsBar)
			{
				Base.UI.Form.IfShowTransTabsBar = false;
			}
		}

		// Token: 0x06001CCF RID: 7375 RVA: 0x000C9430 File Offset: 0x000C7630
		private void method_199()
		{
			if (TApp.IsTrialUser && TApp.smethod_6().Count <= 1)
			{
				this.class43_0 = new Class43(20000, 10000);
				this.class43_0.BeforeTooltipDisplay += this.method_200;
				eTooltipColor color = eTooltipColor.Gray;
				if (TApp.IsTrialUser)
				{
					string str = Class521.smethod_0(76200);
					this.class43_0.method_1(Class521.smethod_0(78827), Base.UI.TransTabs, Enum0.const_2, new SuperTooltipInfo(Class521.smethod_0(78840), Class521.smethod_0(78861), Class521.smethod_0(78926) + str + Class521.smethod_0(79043), null, null, color));
				}
				this.class43_0.method_1(Class521.smethod_0(79076), this.buttonItem_4, Enum0.const_1, new SuperTooltipInfo(Class521.smethod_0(79089), Class521.smethod_0(79146), Class521.smethod_0(79215), Class375.play, null, color));
				this.class43_0.method_1(Class521.smethod_0(79373), this.buttonItem_11, Enum0.const_1, new SuperTooltipInfo(Class521.smethod_0(79386), Class521.smethod_0(79443), Class521.smethod_0(79508), Class375.calendar_date, null, color));
				this.class43_0.method_1(Class521.smethod_0(79690), this.labelItem_10, Enum0.const_0, new SuperTooltipInfo(Class521.smethod_0(79707), Class521.smethod_0(79756), Class521.smethod_0(79825), null, null, color));
				this.class43_0.method_1(Class521.smethod_0(80031), this.labelItem_9, Enum0.const_0, new SuperTooltipInfo(Class521.smethod_0(80048), Class521.smethod_0(80097), Class521.smethod_0(80166), null, null, color));
				this.class43_0.method_1(Class521.smethod_0(80320), this.labelItem_13, Enum0.const_0, new SuperTooltipInfo(Class521.smethod_0(80337), Class521.smethod_0(80386), Class521.smethod_0(80459), Class375.BlindTest_48x48, null, color));
			}
		}

		// Token: 0x06001CD0 RID: 7376 RVA: 0x000C9644 File Offset: 0x000C7844
		private void method_200(object sender, SuperTooltipEventArgs e)
		{
			if (Form.ActiveForm == this && !Base.UI.IsInCreateNewPageState)
			{
				Class46 @class = sender as Class46;
				if (@class.Name == Class521.smethod_0(78827))
				{
					if (Base.UI.TransTabs == null || Base.UI.TransTabs.SelectedTabIndex != 0)
					{
						e.Cancel = true;
					}
				}
				else if (@class.Name == Class521.smethod_0(79076))
				{
					if (this.buttonItem_4.Text != Class521.smethod_0(22771))
					{
						e.Cancel = true;
					}
				}
				else if (@class.Name == Class521.smethod_0(80621))
				{
					if (this.labelItem_13.Image.Tag != null && !(this.labelItem_13.Image.Tag.ToString() != Class521.smethod_0(76956)))
					{
						e.TooltipInfo.HeaderText = Class521.smethod_0(80638);
					}
					else
					{
						e.TooltipInfo.HeaderText = Class521.smethod_0(80337);
					}
				}
			}
			else
			{
				e.Cancel = true;
			}
		}

		// Token: 0x06001CD1 RID: 7377 RVA: 0x0000C2FA File Offset: 0x0000A4FA
		private void buttonItem_4_Click(object sender, EventArgs e)
		{
			this.method_201();
		}

		// Token: 0x06001CD2 RID: 7378 RVA: 0x0000C304 File Offset: 0x0000A504
		private void method_201()
		{
			if (!this.timer_1.Enabled)
			{
				this.method_202();
			}
			else
			{
				this.method_204();
			}
		}

		// Token: 0x06001CD3 RID: 7379 RVA: 0x000C9770 File Offset: 0x000C7970
		public void method_202()
		{
			if (!this.timer_1.Enabled)
			{
				this.IsInRetroMode = false;
				this.method_203();
				this.timer_1.Start();
				Class48.smethod_3(Class24.AutoPlayStarted, Class521.smethod_0(75064) + ((Base.UI.Form.AutoPlayPeriodType != null) ? Base.UI.Form.AutoPlayPeriodType.Value.ToString() : Class521.smethod_0(75086)) + Class521.smethod_0(80687) + ((Base.UI.Form.AutoPlayPeriodUnits != null) ? Base.UI.Form.AutoPlayPeriodUnits.Value.ToString() : Class521.smethod_0(4933)));
			}
			this.buttonItem_4.Text = Class521.smethod_0(80700);
			this.buttonItem_4.Image = Class375.pause;
			Base.UI.IsStartJustClicked = true;
			Base.UI.Form.IsStarted = true;
		}

		// Token: 0x06001CD4 RID: 7380 RVA: 0x000C987C File Offset: 0x000C7A7C
		private void method_203()
		{
			int? num = this.method_103();
			if (num != null)
			{
				this.timer_1.Interval = (101 - num.Value) * 100;
			}
			else
			{
				this.timer_1.Interval = 1100;
			}
		}

		// Token: 0x06001CD5 RID: 7381 RVA: 0x000C98C8 File Offset: 0x000C7AC8
		public void method_204()
		{
			if (this.timer_1.Enabled)
			{
				this.timer_1.Stop();
				Class48.smethod_3(Class24.AutoPlayStopped, Class521.smethod_0(75064) + ((Base.UI.Form.AutoPlayPeriodType != null) ? Base.UI.Form.AutoPlayPeriodType.Value.ToString() : Class521.smethod_0(75086)) + Class521.smethod_0(75073) + ((Base.UI.Form.AutoPlayPeriodUnits != null) ? Base.UI.Form.AutoPlayPeriodUnits.Value.ToString() : Class521.smethod_0(4933)));
			}
			if (this.buttonItem_4.Text != Class521.smethod_0(22771))
			{
				this.buttonItem_4.Text = Class521.smethod_0(22771);
			}
			if (this.buttonItem_4.Image != Class375.play)
			{
				this.buttonItem_4.Image = Class375.play;
			}
			if (Base.UI.Form.IsStarted)
			{
				Base.UI.Form.IsStarted = false;
			}
		}

		// Token: 0x06001CD6 RID: 7382 RVA: 0x0000C323 File Offset: 0x0000A523
		public void method_205()
		{
			if (Base.UI.Form.IsStarted)
			{
				this.method_204();
				this.method_202();
			}
		}

		// Token: 0x06001CD7 RID: 7383 RVA: 0x000C99FC File Offset: 0x000C7BFC
		private void timer_1_Tick(object sender, EventArgs e)
		{
			if (Base.UI.ChtCtrlList == null)
			{
				this.method_204();
			}
			else
			{
				PeriodType? autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
				if (autoPlayPeriodType.GetValueOrDefault() == PeriodType.ByMins & autoPlayPeriodType != null)
				{
					int? autoPlayPeriodUnits = Base.UI.Form.AutoPlayPeriodUnits;
					if (autoPlayPeriodUnits.GetValueOrDefault() == 1 & autoPlayPeriodUnits != null)
					{
						if (Base.UI.smethod_126())
						{
							this.method_44();
							return;
						}
						goto IL_C1;
					}
				}
				ChtCtrl chtCtrl = Base.UI.ChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_20));
				if (chtCtrl != null)
				{
					Base.UI.smethod_118(chtCtrl);
				}
				else
				{
					this.method_204();
					Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
					Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
				}
				IL_C1:;
			}
		}

		// Token: 0x06001CD8 RID: 7384 RVA: 0x000C9ACC File Offset: 0x000C7CCC
		private Point method_206()
		{
			return new Point(base.Location.X + (base.Width - this.form18_0.Width) - 10, base.Location.Y + (base.Height - this.form18_0.Height) - this.bar_5.Height - 10);
		}

		// Token: 0x06001CD9 RID: 7385 RVA: 0x000C9B38 File Offset: 0x000C7D38
		private void method_207(object sender, EventArgs10 e)
		{
			base.BringToFront();
			QuickWndItem quickWndItem = e.QuickWndItem;
			if (quickWndItem != null && quickWndItem.LinkObj != null)
			{
				if (quickWndItem.LinkObj is Class266)
				{
					switch ((quickWndItem.LinkObj as Class266).Id)
					{
					case 250:
						this.method_58(PeriodType.ByMins, new int?(1));
						break;
					case 251:
						this.method_58(PeriodType.ByMins, new int?(3));
						break;
					case 252:
						this.method_58(PeriodType.ByMins, new int?(5));
						break;
					case 253:
						this.method_58(PeriodType.ByMins, new int?(15));
						break;
					case 254:
						this.method_58(PeriodType.ByMins, new int?(30));
						break;
					case 255:
						this.method_58(PeriodType.ByMins, new int?(60));
						break;
					case 256:
						this.method_58(PeriodType.ByMins, new int?(120));
						break;
					case 257:
						this.method_58(PeriodType.ByMins, new int?(240));
						break;
					case 258:
						this.method_58(PeriodType.ByDay, null);
						break;
					case 259:
						this.method_58(PeriodType.ByWeek, null);
						break;
					case 260:
						this.method_58(PeriodType.ByMonth, null);
						break;
					case 261:
						this.method_58(PeriodType.ByMins, new int?(10));
						break;
					case 270:
						this.method_64(Enum4.const_0);
						break;
					case 271:
						this.method_64(Enum4.const_1);
						break;
					case 272:
						this.method_64(Enum4.const_2);
						break;
					}
				}
				else if (quickWndItem.LinkObj.GetType() == typeof(UserDefineIndScript))
				{
					if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
					{
						object linkObj = quickWndItem.LinkObj;
						ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
						UserDefineIndScript userDefineIndScript = quickWndItem.LinkObj as UserDefineIndScript;
						if (userDefineIndScript != null)
						{
							chtCtrl_KLine.method_94((chtCtrl_KLine.MainChart as ChartKLine).DP, userDefineIndScript);
							chtCtrl_KLine.method_114(userDefineIndScript);
						}
					}
				}
				else if (quickWndItem.LinkObj is StkSymbol)
				{
					StkSymbol stkSymbol_ = quickWndItem.LinkObj as StkSymbol;
					Base.UI.smethod_177(Class521.smethod_0(4654), this.method_4());
					Base.UI.smethod_175(stkSymbol_);
					Base.UI.smethod_178();
				}
			}
		}

		// Token: 0x06001CDA RID: 7386 RVA: 0x0000C33F File Offset: 0x0000A53F
		private void form18_0_LostFocus(object sender, EventArgs e)
		{
			base.BringToFront();
		}

		// Token: 0x1700048F RID: 1167
		// (get) Token: 0x06001CDB RID: 7387 RVA: 0x000C9DB0 File Offset: 0x000C7FB0
		// (set) Token: 0x06001CDC RID: 7388 RVA: 0x0000C349 File Offset: 0x0000A549
		public bool IsInRetroMode
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
				if (this.bool_0)
				{
					this.method_204();
					if (this.buttonItem_4.Checked)
					{
						this.buttonItem_4.Checked = false;
					}
				}
			}
		}

		// Token: 0x17000490 RID: 1168
		// (get) Token: 0x06001CDD RID: 7389 RVA: 0x000C9DC8 File Offset: 0x000C7FC8
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				if (this.bool_7)
				{
					createParams.ExStyle |= 33554432;
				}
				return createParams;
			}
		}

		// Token: 0x17000491 RID: 1169
		// (get) Token: 0x06001CDE RID: 7390 RVA: 0x000C9DFC File Offset: 0x000C7FFC
		// (set) Token: 0x06001CDF RID: 7391 RVA: 0x0000C37B File Offset: 0x0000A57B
		public bool EnableFormLevelDoubleBuffering
		{
			get
			{
				return this.bool_7;
			}
			set
			{
				this.bool_7 = value;
			}
		}

		// Token: 0x17000492 RID: 1170
		// (get) Token: 0x06001CE0 RID: 7392 RVA: 0x000C9E14 File Offset: 0x000C8014
		public bool TransTabBarEnabled
		{
			get
			{
				return this.bar_0.Enabled;
			}
		}

		// Token: 0x06001CE1 RID: 7393 RVA: 0x0000C386 File Offset: 0x0000A586
		private void method_208()
		{
			new MainForm.Delegate31(Base.Trading.smethod_59).BeginInvoke(null, null);
		}

		// Token: 0x06001CE2 RID: 7394 RVA: 0x0000C39E File Offset: 0x0000A59E
		private void method_209(OrderType orderType_0)
		{
			new MainForm.Delegate32(this.method_133).BeginInvoke(orderType_0, null, null);
		}

		// Token: 0x06001CE3 RID: 7395 RVA: 0x0000C3B7 File Offset: 0x0000A5B7
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001CE4 RID: 7396 RVA: 0x000C9E30 File Offset: 0x000C8030
		private void method_210()
		{
			this.icontainer_0 = new Container();
			this.dotNetBarManager_0 = new DotNetBarManager(this.icontainer_0);
			this.dockSite_0 = new DockSite();
			this.bar_0 = new Bar();
			this.panelDockContainer_0 = new PanelDockContainer();
			this.dockContainerItem_0 = new DockContainerItem();
			this.dockSite_1 = new DockSite();
			this.dockSite_2 = new DockSite();
			this.dockSite_7 = new DockSite();
			this.bar_5 = new Bar();
			this.labelItem_9 = new LabelItem();
			this.buttonItem_34 = new ButtonItem();
			this.labelItem_3 = new LabelItem();
			this.buttonItem_37 = new ButtonItem();
			this.labelItem_10 = new LabelItem();
			this.buttonItem_38 = new ButtonItem();
			this.labelItem_4 = new LabelItem();
			this.labelItem_5 = new LabelItem();
			this.labelItem_13 = new LabelItem();
			this.labelItem_11 = new LabelItem();
			this.labelItem_7 = new LabelItem();
			this.labelItem_6 = new LabelItem();
			this.labelItem_12 = new LabelItem();
			this.labelItem_8 = new LabelItem();
			this.dockSite_4 = new DockSite();
			this.dockSite_5 = new DockSite();
			this.dockSite_6 = new DockSite();
			this.bar_1 = new Bar();
			this.class304_0 = new Class304();
			this.class304_1 = new Class304();
			this.buttonItem_0 = new ButtonItem();
			this.buttonItem_1 = new ButtonItem();
			this.buttonItem_2 = new ButtonItem();
			this.buttonItem_3 = new ButtonItem();
			this.labelItem_0 = new LabelItem();
			this.controlContainerItem_0 = new ControlContainerItem();
			this.labelItem_1 = new LabelItem();
			this.controlContainerItem_1 = new ControlContainerItem();
			this.bar_2 = new Bar();
			this.buttonItem_4 = new ButtonItem();
			this.buttonItem_60 = new ButtonItem();
			this.labelItem_2 = new LabelItem();
			this.sliderItem_0 = new SliderItem();
			this.bar_6 = new Bar();
			this.buttonItem_35 = new ButtonItem();
			this.bar_3 = new Bar();
			this.buttonItem_11 = new ButtonItem();
			this.buttonItem_5 = new ButtonItem();
			this.buttonItem_36 = new ButtonItem();
			this.buttonItem_6 = new ButtonItem();
			this.buttonItem_61 = new ButtonItem();
			this.buttonItem_63 = new ButtonItem();
			this.buttonItem_64 = new ButtonItem();
			this.buttonItem_48 = new ButtonItem();
			this.buttonItem_54 = new ButtonItem();
			this.buttonItem_7 = new ButtonItem();
			this.buttonItem_8 = new ButtonItem();
			this.buttonItem_9 = new ButtonItem();
			this.buttonItem_10 = new ButtonItem();
			this.buttonItem_41 = new ButtonItem();
			this.buttonItem_62 = new ButtonItem();
			this.buttonItem_65 = new ButtonItem();
			this.buttonItem_66 = new ButtonItem();
			this.buttonItem_67 = new ButtonItem();
			this.buttonItem_68 = new ButtonItem();
			this.buttonItem_69 = new ButtonItem();
			this.buttonItem_70 = new ButtonItem();
			this.buttonItem_71 = new ButtonItem();
			this.buttonItem_47 = new ButtonItem();
			this.buttonItem_55 = new ButtonItem();
			this.buttonItem_46 = new ButtonItem();
			this.buttonItem_50 = new ButtonItem();
			this.buttonItem_53 = new ButtonItem();
			this.buttonItem_12 = new ButtonItem();
			this.buttonItem_13 = new ButtonItem();
			this.buttonItem_14 = new ButtonItem();
			this.buttonItem_15 = new ButtonItem();
			this.buttonItem_16 = new ButtonItem();
			this.buttonItem_18 = new ButtonItem();
			this.buttonItem_17 = new ButtonItem();
			this.buttonItem_19 = new ButtonItem();
			this.buttonItem_20 = new ButtonItem();
			this.buttonItem_39 = new ButtonItem();
			this.buttonItem_21 = new ButtonItem();
			this.buttonItem_42 = new ButtonItem();
			this.buttonItem_43 = new ButtonItem();
			this.buttonItem_44 = new ButtonItem();
			this.buttonItem_45 = new ButtonItem();
			this.buttonItem_51 = new ButtonItem();
			this.buttonItem_52 = new ButtonItem();
			this.buttonItem_40 = new ButtonItem();
			this.bar_4 = new Bar();
			this.buttonItem_56 = new ButtonItem();
			this.itemContainer_0 = new ItemContainer();
			this.textBoxItem_0 = new TextBoxItem();
			this.buttonItem_57 = new ButtonItem();
			this.itemContainer_1 = new ItemContainer();
			this.textBoxItem_1 = new TextBoxItem();
			this.buttonItem_58 = new ButtonItem();
			this.itemContainer_2 = new ItemContainer();
			this.textBoxItem_2 = new TextBoxItem();
			this.buttonItem_59 = new ButtonItem();
			this.buttonItem_22 = new ButtonItem();
			this.buttonItem_23 = new ButtonItem();
			this.buttonItem_24 = new ButtonItem();
			this.buttonItem_25 = new ButtonItem();
			this.buttonItem_26 = new ButtonItem();
			this.buttonItem_27 = new ButtonItem();
			this.buttonItem_28 = new ButtonItem();
			this.buttonItem_29 = new ButtonItem();
			this.buttonItem_30 = new ButtonItem();
			this.buttonItem_49 = new ButtonItem();
			this.buttonItem_31 = new ButtonItem();
			this.buttonItem_32 = new ButtonItem();
			this.buttonItem_33 = new ButtonItem();
			this.dockSite_3 = new DockSite();
			this.panel_0 = new Panel();
			this.toolTip_0 = new System.Windows.Forms.ToolTip(this.icontainer_0);
			this.dockSite_0.SuspendLayout();
			((ISupportInitialize)this.bar_0).BeginInit();
			this.bar_0.SuspendLayout();
			this.dockSite_7.SuspendLayout();
			((ISupportInitialize)this.bar_5).BeginInit();
			this.dockSite_6.SuspendLayout();
			((ISupportInitialize)this.bar_1).BeginInit();
			this.bar_1.SuspendLayout();
			((ISupportInitialize)this.class304_0).BeginInit();
			((ISupportInitialize)this.class304_1).BeginInit();
			((ISupportInitialize)this.bar_2).BeginInit();
			((ISupportInitialize)this.bar_6).BeginInit();
			((ISupportInitialize)this.bar_3).BeginInit();
			((ISupportInitialize)this.bar_4).BeginInit();
			base.SuspendLayout();
			this.dotNetBarManager_0.AllowUserBarCustomize = false;
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.F1);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlC);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlA);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlV);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlX);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlZ);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.CtrlY);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.Del);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(eShortcut.Ins);
			this.dotNetBarManager_0.BottomDockSite = this.dockSite_0;
			this.dotNetBarManager_0.EnableFullSizeDock = false;
			this.dotNetBarManager_0.LeftDockSite = this.dockSite_1;
			this.dotNetBarManager_0.ParentForm = this;
			this.dotNetBarManager_0.RightDockSite = this.dockSite_2;
			this.dotNetBarManager_0.Style = eDotNetBarStyle.Metro;
			this.dotNetBarManager_0.ToolbarBottomDockSite = this.dockSite_7;
			this.dotNetBarManager_0.ToolbarLeftDockSite = this.dockSite_4;
			this.dotNetBarManager_0.ToolbarRightDockSite = this.dockSite_5;
			this.dotNetBarManager_0.ToolbarTopDockSite = this.dockSite_6;
			this.dotNetBarManager_0.TopDockSite = this.dockSite_3;
			this.dockSite_0.AccessibleRole = AccessibleRole.Window;
			this.dockSite_0.Controls.Add(this.bar_0);
			this.dockSite_0.Dock = DockStyle.Bottom;
			this.dockSite_0.DocumentDockContainer = new DocumentDockContainer(new DocumentBaseContainer[]
			{
				new DocumentBarContainer(this.bar_0, 1274, 275)
			}, eOrientation.Vertical);
			this.dockSite_0.Location = new Point(0, 425);
			this.dockSite_0.Margin = new System.Windows.Forms.Padding(0);
			this.dockSite_0.Name = Class521.smethod_0(80709);
			this.dockSite_0.Size = new Size(1274, 278);
			this.dockSite_0.TabIndex = 6;
			this.dockSite_0.TabStop = false;
			this.bar_0.AccessibleDescription = Class521.smethod_0(80730);
			this.bar_0.AccessibleName = Class521.smethod_0(80771);
			this.bar_0.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_0.AntiAlias = true;
			this.bar_0.AutoSyncBarCaption = true;
			this.bar_0.BackColor = SystemColors.Control;
			this.bar_0.CanCustomize = false;
			this.bar_0.CanDockDocument = true;
			this.bar_0.CanDockLeft = false;
			this.bar_0.CanDockRight = false;
			this.bar_0.CanDockTab = false;
			this.bar_0.CanHide = true;
			this.bar_0.CanReorderTabs = false;
			this.bar_0.CloseSingleTab = true;
			this.bar_0.ColorScheme.BarCaptionBackground = Color.FromArgb(251, 250, 247);
			this.bar_0.ColorScheme.BarCaptionText = Color.FromArgb(0, 0, 0);
			this.bar_0.ColorScheme.ItemHotBorder = Color.FromArgb(0, 255, 255, 255);
			this.bar_0.Controls.Add(this.panelDockContainer_0);
			this.bar_0.DisabledImagesGrayScale = false;
			this.bar_0.DisplayMoreItemsOnMenu = true;
			this.bar_0.Font = new Font(Class521.smethod_0(24023), 8.4f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.bar_0.GrabHandleStyle = eGrabHandleStyle.Caption;
			this.bar_0.Items.AddRange(new BaseItem[]
			{
				this.dockContainerItem_0
			});
			this.bar_0.LayoutType = eLayoutType.DockContainer;
			this.bar_0.Location = new Point(0, 3);
			this.bar_0.Margin = new System.Windows.Forms.Padding(0);
			this.bar_0.Name = Class521.smethod_0(80792);
			this.bar_0.Size = new Size(1274, 275);
			this.bar_0.Stretch = true;
			this.bar_0.Style = eDotNetBarStyle.Metro;
			this.bar_0.TabIndex = 0;
			this.bar_0.TabStop = false;
			this.bar_0.Text = Class521.smethod_0(64375);
			this.bar_0.BarDock += this.bar_0_BarDock;
			this.bar_0.Closing += this.bar_0_Closing;
			this.bar_0.AutoHideDisplay += this.bar_0_AutoHideDisplay;
			this.bar_0.SizeChanged += this.bar_0_SizeChanged;
			this.panelDockContainer_0.ColorSchemeStyle = eDotNetBarStyle.Metro;
			this.panelDockContainer_0.Location = new Point(3, 23);
			this.panelDockContainer_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panelDockContainer_0.Name = Class521.smethod_0(80813);
			this.panelDockContainer_0.Size = new Size(1268, 249);
			this.panelDockContainer_0.Style.Alignment = StringAlignment.Center;
			this.panelDockContainer_0.Style.BackColor1.ColorSchemePart = eColorSchemePart.BarBackground;
			this.panelDockContainer_0.Style.ForeColor.ColorSchemePart = eColorSchemePart.ItemText;
			this.panelDockContainer_0.Style.GradientAngle = 90;
			this.panelDockContainer_0.TabIndex = 0;
			this.dockContainerItem_0.Control = this.panelDockContainer_0;
			this.dockContainerItem_0.DefaultFloatingSize = new Size(550, 200);
			this.dockContainerItem_0.Name = Class521.smethod_0(80854);
			this.dockContainerItem_0.Text = Class521.smethod_0(64375);
			this.dockSite_1.AccessibleRole = AccessibleRole.Window;
			this.dockSite_1.Dock = DockStyle.Left;
			this.dockSite_1.DocumentDockContainer = new DocumentDockContainer();
			this.dockSite_1.Location = new Point(0, 31);
			this.dockSite_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_1.Name = Class521.smethod_0(80887);
			this.dockSite_1.Size = new Size(0, 672);
			this.dockSite_1.TabIndex = 3;
			this.dockSite_1.TabStop = false;
			this.dockSite_2.AccessibleRole = AccessibleRole.Window;
			this.dockSite_2.Dock = DockStyle.Right;
			this.dockSite_2.DocumentDockContainer = new DocumentDockContainer();
			this.dockSite_2.Location = new Point(1274, 31);
			this.dockSite_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_2.Name = Class521.smethod_0(80908);
			this.dockSite_2.Size = new Size(0, 672);
			this.dockSite_2.TabIndex = 4;
			this.dockSite_2.TabStop = false;
			this.dockSite_7.AccessibleRole = AccessibleRole.Window;
			this.dockSite_7.Controls.Add(this.bar_5);
			this.dockSite_7.Dock = DockStyle.Bottom;
			this.dockSite_7.Location = new Point(0, 703);
			this.dockSite_7.Margin = new System.Windows.Forms.Padding(0);
			this.dockSite_7.Name = Class521.smethod_0(80929);
			this.dockSite_7.Size = new Size(1274, 32);
			this.dockSite_7.TabIndex = 10;
			this.dockSite_7.TabStop = false;
			this.bar_5.AccessibleDescription = Class521.smethod_0(80950);
			this.bar_5.AccessibleName = Class521.smethod_0(80771);
			this.bar_5.AccessibleRole = AccessibleRole.StatusBar;
			this.bar_5.AntiAlias = true;
			this.bar_5.BackColor = SystemColors.Control;
			this.bar_5.BarType = eBarType.StatusBar;
			this.bar_5.CanCustomize = false;
			this.bar_5.CanDockLeft = false;
			this.bar_5.CanDockRight = false;
			this.bar_5.CanDockTab = false;
			this.bar_5.CanDockTop = false;
			this.bar_5.CanReorderTabs = false;
			this.bar_5.CanUndock = false;
			this.bar_5.Dock = DockStyle.Bottom;
			this.bar_5.DockSide = eDockSide.Bottom;
			this.bar_5.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_5.GrabHandleStyle = eGrabHandleStyle.ResizeHandle;
			this.bar_5.Items.AddRange(new BaseItem[]
			{
				this.labelItem_9,
				this.buttonItem_34,
				this.labelItem_3,
				this.buttonItem_37,
				this.labelItem_10,
				this.buttonItem_38,
				this.labelItem_13,
				this.labelItem_4,
				this.labelItem_5,
				this.labelItem_11,
				this.labelItem_7,
				this.labelItem_6,
				this.labelItem_12,
				this.labelItem_8
			});
			this.bar_5.Location = new Point(0, 1);
			this.bar_5.Margin = new System.Windows.Forms.Padding(3, 0, 3, 2);
			this.bar_5.Name = Class521.smethod_0(80987);
			this.bar_5.PaddingTop = 0;
			this.bar_5.Size = new Size(1274, 31);
			this.bar_5.Stretch = true;
			this.bar_5.Style = eDotNetBarStyle.Metro;
			this.bar_5.TabIndex = 12;
			this.bar_5.TabStop = false;
			this.labelItem_9.Name = Class521.smethod_0(81000);
			this.labelItem_9.Text = Class521.smethod_0(81025);
			this.buttonItem_34.AutoExpandOnClick = true;
			this.buttonItem_34.FixedSize = new Size(0, 20);
			this.buttonItem_34.Name = Class521.smethod_0(81038);
			this.buttonItem_34.SubItemTriangleColor = Color.Empty;
			this.buttonItem_34.Text = Class521.smethod_0(81079);
			this.labelItem_3.BeginGroup = false;
			this.labelItem_3.Height = 20;
			this.labelItem_3.Name = Class521.smethod_0(81096);
			this.labelItem_3.Text = Class521.smethod_0(81121);
			this.buttonItem_37.AutoExpandOnClick = true;
			this.buttonItem_37.Name = Class521.smethod_0(81134);
			this.buttonItem_37.SubItemTriangleColor = Color.Empty;
			this.buttonItem_37.Text = Class521.smethod_0(81175);
			this.labelItem_10.BeginGroup = false;
			this.labelItem_10.Name = Class521.smethod_0(81192);
			this.labelItem_10.Text = Class521.smethod_0(4587);
			this.buttonItem_38.AutoExpandOnClick = true;
			this.buttonItem_38.Name = Class521.smethod_0(81217);
			this.buttonItem_38.SubItemTriangleColor = Color.Empty;
			this.buttonItem_38.Text = Class521.smethod_0(47576);
			this.labelItem_4.BeginGroup = false;
			this.labelItem_4.Height = 20;
			this.labelItem_4.Image = Class375.autostop;
			this.labelItem_4.Name = Class521.smethod_0(81258);
			this.labelItem_4.Tooltip = Class521.smethod_0(42739);
			this.labelItem_4.Click += this.labelItem_4_Click;
			this.labelItem_5.Height = 20;
			this.labelItem_5.Image = Class375.autolimit;
			this.labelItem_5.Name = Class521.smethod_0(81307);
			this.labelItem_5.Tooltip = Class521.smethod_0(42701);
			this.labelItem_5.Click += this.labelItem_5_Click;
			this.labelItem_13.BeginGroup = false;
			this.labelItem_13.Image = Class375.GlassFace_18;
			this.labelItem_13.ItemAlignment = eItemAlignment.Center;
			this.labelItem_13.Text = Class521.smethod_0(76797);
			this.labelItem_13.Name = Class521.smethod_0(81356);
			this.labelItem_13.Tooltip = Class521.smethod_0(23399);
			this.labelItem_13.Click += this.labelItem_13_Click;
			this.labelItem_11.BeginGroup = false;
			this.labelItem_11.Height = 20;
			this.labelItem_11.Name = Class521.smethod_0(81397);
			this.labelItem_11.Text = Class521.smethod_0(81430);
			this.labelItem_7.Height = 20;
			this.labelItem_7.Name = Class521.smethod_0(81451);
			this.labelItem_7.Text = Class521.smethod_0(2841);
			this.labelItem_6.BeginGroup = false;
			this.labelItem_6.Height = 20;
			this.labelItem_6.Name = Class521.smethod_0(81492);
			this.labelItem_6.Text = Class521.smethod_0(81525);
			this.labelItem_12.Height = 20;
			this.labelItem_12.Name = Class521.smethod_0(81546);
			this.labelItem_12.Text = Class521.smethod_0(2841);
			this.labelItem_8.Height = 20;
			this.labelItem_8.ItemAlignment = eItemAlignment.Far;
			this.labelItem_8.Name = Class521.smethod_0(81591);
			this.labelItem_8.Text = Class521.smethod_0(76579);
			this.dockSite_4.AccessibleRole = AccessibleRole.Window;
			this.dockSite_4.Dock = DockStyle.Left;
			this.dockSite_4.Location = new Point(0, 31);
			this.dockSite_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_4.Name = Class521.smethod_0(81628);
			this.dockSite_4.Size = new Size(0, 672);
			this.dockSite_4.TabIndex = 7;
			this.dockSite_4.TabStop = false;
			this.dockSite_5.AccessibleRole = AccessibleRole.Window;
			this.dockSite_5.Dock = DockStyle.Right;
			this.dockSite_5.Location = new Point(1274, 31);
			this.dockSite_5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_5.Name = Class521.smethod_0(81657);
			this.dockSite_5.Size = new Size(0, 672);
			this.dockSite_5.TabIndex = 8;
			this.dockSite_5.TabStop = false;
			this.dockSite_6.AccessibleRole = AccessibleRole.Window;
			this.dockSite_6.BackColor = SystemColors.Control;
			this.dockSite_6.CausesValidation = false;
			this.dockSite_6.Controls.Add(this.bar_1);
			this.dockSite_6.Controls.Add(this.bar_2);
			this.dockSite_6.Controls.Add(this.bar_6);
			this.dockSite_6.Controls.Add(this.bar_3);
			this.dockSite_6.Controls.Add(this.bar_4);
			this.dockSite_6.Dock = DockStyle.Top;
			this.dockSite_6.Location = new Point(0, 0);
			this.dockSite_6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_6.Name = Class521.smethod_0(81686);
			this.dockSite_6.OptimizeLayoutRedraw = false;
			this.dockSite_6.Size = new Size(1274, 31);
			this.dockSite_6.TabIndex = 9;
			this.dockSite_6.TabStop = false;
			this.bar_1.AccessibleDescription = Class521.smethod_0(81711);
			this.bar_1.AccessibleName = Class521.smethod_0(80771);
			this.bar_1.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_1.AntiAlias = true;
			this.bar_1.BackColor = SystemColors.Control;
			this.bar_1.CanAutoHide = false;
			this.bar_1.CanHide = true;
			this.bar_1.Controls.Add(this.class304_0);
			this.bar_1.Controls.Add(this.class304_1);
			this.bar_1.DockSide = eDockSide.Top;
			this.bar_1.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_1.GrabHandleStyle = eGrabHandleStyle.None;
			this.bar_1.Items.AddRange(new BaseItem[]
			{
				this.buttonItem_0,
				this.buttonItem_1,
				this.buttonItem_2,
				this.buttonItem_3,
				this.labelItem_0,
				this.controlContainerItem_0,
				this.labelItem_1,
				this.controlContainerItem_1
			});
			this.bar_1.Location = new Point(0, 0);
			this.bar_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.bar_1.Name = Class521.smethod_0(81756);
			this.bar_1.RoundCorners = false;
			this.bar_1.Size = new Size(470, 31);
			this.bar_1.Style = eDotNetBarStyle.Metro;
			this.bar_1.TabIndex = 8;
			this.bar_1.TabStop = false;
			this.bar_1.Text = Class521.smethod_0(81781);
			this.class304_0.Font = new Font(Class521.smethod_0(6998), 8f);
			this.class304_0.Location = new Point(238, 3);
			this.class304_0.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			NumericUpDown numericUpDown = this.class304_0;
			int[] array = new int[4];
			array[0] = 9999999;
			numericUpDown.Maximum = new decimal(array);
			NumericUpDown numericUpDown2 = this.class304_0;
			int[] array2 = new int[4];
			array2[0] = 1;
			numericUpDown2.Minimum = new decimal(array2);
			this.class304_0.Name = Class521.smethod_0(16172);
			this.class304_0.Size = new Size(75, 25);
			this.class304_0.TabIndex = 8;
			NumericUpDown numericUpDown3 = this.class304_0;
			int[] array3 = new int[4];
			array3[0] = 1;
			numericUpDown3.Value = new decimal(array3);
			this.class304_0.ValueChanged += this.class304_0_ValueChanged;
			this.class304_1.Font = new Font(Class521.smethod_0(6998), 8f);
			this.class304_1.Location = new Point(376, 3);
			this.class304_1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			NumericUpDown numericUpDown4 = this.class304_1;
			int[] array4 = new int[4];
			array4[0] = 9999999;
			numericUpDown4.Maximum = new decimal(array4);
			this.class304_1.Name = Class521.smethod_0(24456);
			this.class304_1.Size = new Size(90, 25);
			this.class304_1.TabIndex = 1;
			this.buttonItem_0.FontBold = true;
			this.buttonItem_0.ForeColor = Color.Red;
			this.buttonItem_0.Name = Class521.smethod_0(81794);
			this.buttonItem_0.SubItemTriangleColor = Color.Empty;
			this.buttonItem_0.Text = Class521.smethod_0(26376);
			this.buttonItem_1.FontBold = true;
			this.buttonItem_1.ForeColor = Color.FromArgb(0, 192, 0);
			this.buttonItem_1.Name = Class521.smethod_0(81819);
			this.buttonItem_1.SubItemTriangleColor = Color.Empty;
			this.buttonItem_1.Text = Class521.smethod_0(26410);
			this.buttonItem_2.BeginGroup = false;
			this.buttonItem_2.FontBold = true;
			this.buttonItem_2.ForeColor = Color.FromArgb(0, 192, 0);
			this.buttonItem_2.Name = Class521.smethod_0(81848);
			this.buttonItem_2.SubItemTriangleColor = Color.Empty;
			this.buttonItem_2.Text = Class521.smethod_0(26444);
			this.buttonItem_3.FontBold = true;
			this.buttonItem_3.ForeColor = Color.Red;
			this.buttonItem_3.Name = Class521.smethod_0(81877);
			this.buttonItem_3.SubItemTriangleColor = Color.Empty;
			this.buttonItem_3.Text = Class521.smethod_0(26478);
			this.labelItem_0.BeginGroup = false;
			this.labelItem_0.Height = 18;
			this.labelItem_0.Name = Class521.smethod_0(81910);
			this.labelItem_0.Text = Class521.smethod_0(81931);
			this.controlContainerItem_0.AllowItemResize = false;
			this.controlContainerItem_0.Control = this.class304_0;
			this.controlContainerItem_0.MenuVisibility = eMenuVisibility.VisibleAlways;
			this.controlContainerItem_0.Name = Class521.smethod_0(81944);
			this.controlContainerItem_0.Text = Class521.smethod_0(3636);
			this.labelItem_1.Height = 18;
			this.labelItem_1.Name = Class521.smethod_0(81981);
			this.labelItem_1.Text = Class521.smethod_0(24430);
			this.controlContainerItem_1.AllowItemResize = false;
			this.controlContainerItem_1.Control = this.class304_1;
			this.controlContainerItem_1.MenuVisibility = eMenuVisibility.VisibleAlways;
			this.controlContainerItem_1.Name = Class521.smethod_0(82002);
			this.controlContainerItem_1.Text = Class521.smethod_0(3636);
			this.bar_2.AccessibleDescription = Class521.smethod_0(82039);
			this.bar_2.AccessibleName = Class521.smethod_0(80771);
			this.bar_2.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_2.AntiAlias = true;
			this.bar_2.BackColor = SystemColors.Control;
			this.bar_2.CanAutoHide = false;
			this.bar_2.CanDockTab = false;
			this.bar_2.DockOffset = 379;
			this.bar_2.DockSide = eDockSide.Top;
			this.bar_2.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_2.GrabHandleStyle = eGrabHandleStyle.None;
			this.bar_2.Items.AddRange(new BaseItem[]
			{
				this.buttonItem_4,
				this.labelItem_2,
				this.sliderItem_0
			});
			this.bar_2.Location = new Point(472, 0);
			this.bar_2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.bar_2.Name = Class521.smethod_0(82084);
			this.bar_2.Size = new Size(251, 31);
			this.bar_2.Style = eDotNetBarStyle.Metro;
			this.bar_2.TabIndex = 9;
			this.bar_2.TabStop = false;
			this.bar_2.Text = Class521.smethod_0(82105);
			this.bar_2.BarDock += this.bar_2_BarDock;
			this.bar_2.BarUndock += this.bar_2_BarUndock;
			this.buttonItem_4.ButtonStyle = eButtonStyle.ImageAndText;
			this.buttonItem_4.FixedSize = new Size(0, 18);
			this.buttonItem_4.Image = Class375.play;
			this.buttonItem_4.ImageFixedSize = new Size(18, 18);
			this.buttonItem_4.Name = Class521.smethod_0(82126);
			this.buttonItem_4.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_60
			});
			this.buttonItem_4.SubItemTriangleColor = Color.Empty;
			this.buttonItem_4.Text = Class521.smethod_0(22771);
			this.buttonItem_60.Name = Class521.smethod_0(82159);
			this.buttonItem_60.SubItemTriangleColor = Color.Empty;
			this.buttonItem_60.Text = Class521.smethod_0(75312);
			this.labelItem_2.BeginGroup = false;
			this.labelItem_2.Name = Class521.smethod_0(82200);
			this.labelItem_2.Text = Class521.smethod_0(82221);
			this.sliderItem_0.LabelWidth = 42;
			this.sliderItem_0.Minimum = 1;
			this.sliderItem_0.Name = Class521.smethod_0(82234);
			this.sliderItem_0.Text = Class521.smethod_0(82251);
			this.sliderItem_0.Value = 100;
			this.sliderItem_0.Width = 86;
			this.sliderItem_0.ValueChanged += this.sliderItem_0_ValueChanged;
			this.bar_6.AccessibleDescription = Class521.smethod_0(82256);
			this.bar_6.AccessibleName = Class521.smethod_0(80771);
			this.bar_6.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_6.AntiAlias = true;
			this.bar_6.BackColor = SystemColors.Control;
			this.bar_6.CanCustomize = false;
			this.bar_6.CanDockBottom = false;
			this.bar_6.CanDockLeft = false;
			this.bar_6.CanDockRight = false;
			this.bar_6.CanDockTab = false;
			this.bar_6.CanReorderTabs = false;
			this.bar_6.DockOffset = 440;
			this.bar_6.DockSide = eDockSide.Top;
			this.bar_6.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_6.GrabHandleStyle = eGrabHandleStyle.None;
			this.bar_6.Items.AddRange(new BaseItem[]
			{
				this.buttonItem_35
			});
			this.bar_6.Location = new Point(725, 0);
			this.bar_6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.bar_6.Name = Class521.smethod_0(82313);
			this.bar_6.Size = new Size(72, 31);
			this.bar_6.Style = eDotNetBarStyle.Metro;
			this.bar_6.TabIndex = 12;
			this.bar_6.TabStop = false;
			this.bar_6.Text = Class521.smethod_0(82350);
			this.buttonItem_35.ButtonStyle = eButtonStyle.ImageAndText;
			this.buttonItem_35.Image = Class375.door_in;
			this.buttonItem_35.ImageFixedSize = new Size(18, 18);
			this.buttonItem_35.Name = Class521.smethod_0(82363);
			this.buttonItem_35.SubItemTriangleColor = Color.Empty;
			this.buttonItem_35.Text = Class521.smethod_0(82404);
			this.bar_3.AccessibleDescription = Class521.smethod_0(82413);
			this.bar_3.AccessibleName = Class521.smethod_0(80771);
			this.bar_3.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_3.AntiAlias = true;
			this.bar_3.BackColor = SystemColors.Control;
			this.bar_3.CanAutoHide = false;
			this.bar_3.CanDockBottom = false;
			this.bar_3.CanHide = true;
			this.bar_3.DockOffset = 711;
			this.bar_3.DockSide = eDockSide.Top;
			this.bar_3.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_3.GrabHandleStyle = eGrabHandleStyle.None;
			this.bar_3.Items.AddRange(new BaseItem[]
			{
				this.buttonItem_11,
				this.buttonItem_5
			});
			this.bar_3.Location = new Point(799, 0);
			this.bar_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.bar_3.Name = Class521.smethod_0(82458);
			this.bar_3.RoundCorners = false;
			this.bar_3.Size = new Size(146, 31);
			this.bar_3.Style = eDotNetBarStyle.Metro;
			this.bar_3.TabIndex = 10;
			this.bar_3.TabStop = false;
			this.bar_3.Text = Class521.smethod_0(82483);
			this.buttonItem_11.ButtonStyle = eButtonStyle.ImageAndText;
			this.buttonItem_11.Image = Class375.calendar_date;
			this.buttonItem_11.ImageFixedSize = new Size(16, 16);
			this.buttonItem_11.Name = Class521.smethod_0(82496);
			this.buttonItem_11.SubItemTriangleColor = Color.Empty;
			this.buttonItem_11.Text = Class521.smethod_0(82533);
			this.buttonItem_5.AutoExpandOnClick = true;
			this.buttonItem_5.ButtonStyle = eButtonStyle.ImageAndText;
			this.buttonItem_5.Image = Class375.chart;
			this.buttonItem_5.ImageFixedSize = new Size(18, 18);
			this.buttonItem_5.Name = Class521.smethod_0(82542);
			this.buttonItem_5.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_36,
				this.buttonItem_6,
				this.buttonItem_61,
				this.buttonItem_63,
				this.buttonItem_64,
				this.buttonItem_48,
				this.buttonItem_54,
				this.buttonItem_62,
				this.buttonItem_71,
				this.buttonItem_47,
				this.buttonItem_46,
				this.buttonItem_19,
				this.buttonItem_41,
				this.buttonItem_55,
				this.buttonItem_50,
				this.buttonItem_53,
				this.buttonItem_12,
				this.buttonItem_7,
				this.buttonItem_42,
				this.buttonItem_43,
				this.buttonItem_44,
				this.buttonItem_45,
				this.buttonItem_51,
				this.buttonItem_52,
				this.buttonItem_40
			});
			this.buttonItem_5.SubItemTriangleColor = Color.Empty;
			this.buttonItem_5.Text = Class521.smethod_0(82587);
			this.buttonItem_36.Name = Class521.smethod_0(82596);
			this.buttonItem_36.Shortcuts.Add(eShortcut.F8);
			this.buttonItem_36.SubItemTriangleColor = Color.Empty;
			this.buttonItem_36.Text = Class521.smethod_0(82633);
			this.buttonItem_36.Click += this.buttonItem_36_Click;
			this.buttonItem_6.Name = Class521.smethod_0(82646);
			this.buttonItem_6.Shortcuts.Add(eShortcut.F12);
			this.buttonItem_6.SubItemTriangleColor = Color.Empty;
			this.buttonItem_6.Text = Class521.smethod_0(64375);
			this.buttonItem_6.Click += this.buttonItem_6_Click;
			this.buttonItem_61.BeginGroup = true;
			this.buttonItem_61.Name = Class521.smethod_0(82683);
			this.buttonItem_61.SubItemTriangleColor = Color.Empty;
			this.buttonItem_61.Text = Class521.smethod_0(82712);
			this.buttonItem_63.Name = Class521.smethod_0(82729);
			this.buttonItem_63.SubItemTriangleColor = Color.Empty;
			this.buttonItem_63.Text = Class521.smethod_0(71717);
			this.buttonItem_64.Name = Class521.smethod_0(82758);
			this.buttonItem_64.SubItemTriangleColor = Color.Empty;
			this.buttonItem_64.Text = Class521.smethod_0(71587);
			this.buttonItem_48.Image = Class375.Glass_Face_48;
			this.buttonItem_48.ImageFixedSize = new Size(16, 16);
			this.buttonItem_48.Name = Class521.smethod_0(82787);
			this.buttonItem_48.SubItemTriangleColor = Color.Empty;
			this.buttonItem_48.Text = Class521.smethod_0(23399);
			this.buttonItem_48.Click += this.buttonItem_48_Click;
			this.buttonItem_54.Name = Class521.smethod_0(82816);
			this.buttonItem_54.SubItemTriangleColor = Color.Empty;
			this.buttonItem_54.Text = Class521.smethod_0(23658);
			this.buttonItem_7.Image = Class375.folder_accept;
			this.buttonItem_7.ImageFixedSize = new Size(16, 16);
			this.buttonItem_7.Name = Class521.smethod_0(82845);
			this.buttonItem_7.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_8,
				this.buttonItem_9,
				this.buttonItem_10
			});
			this.buttonItem_7.SubItemTriangleColor = Color.Empty;
			this.buttonItem_7.Text = Class521.smethod_0(82874);
			this.buttonItem_8.Image = Class375.folder_add;
			this.buttonItem_8.ImageFixedSize = new Size(18, 18);
			this.buttonItem_8.Name = Class521.smethod_0(82891);
			this.buttonItem_8.SubItemTriangleColor = Color.Empty;
			this.buttonItem_8.Text = Class521.smethod_0(82932);
			this.buttonItem_9.Name = Class521.smethod_0(82953);
			this.buttonItem_9.SubItemTriangleColor = Color.Empty;
			this.buttonItem_9.Text = Class521.smethod_0(82994);
			this.buttonItem_10.Name = Class521.smethod_0(83011);
			this.buttonItem_10.SubItemTriangleColor = Color.Empty;
			this.buttonItem_10.Text = Class521.smethod_0(83052);
			this.buttonItem_41.Name = Class521.smethod_0(83069);
			this.buttonItem_41.SubItemTriangleColor = Color.Empty;
			this.buttonItem_41.Text = Class521.smethod_0(11175);
			this.buttonItem_41.Click += this.buttonItem_41_Click;
			this.buttonItem_62.Name = Class521.smethod_0(83098);
			this.buttonItem_62.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_65,
				this.buttonItem_66,
				this.buttonItem_67,
				this.buttonItem_68,
				this.buttonItem_69,
				this.buttonItem_70
			});
			this.buttonItem_62.SubItemTriangleColor = Color.Empty;
			this.buttonItem_62.Text = Class521.smethod_0(68360);
			this.buttonItem_65.Name = Class521.smethod_0(83127);
			this.buttonItem_65.SubItemTriangleColor = Color.Empty;
			this.buttonItem_65.Text = Class521.smethod_0(83172);
			this.buttonItem_66.Name = Class521.smethod_0(83189);
			this.buttonItem_66.SubItemTriangleColor = Color.Empty;
			this.buttonItem_66.Text = Class521.smethod_0(83234);
			this.buttonItem_67.Name = Class521.smethod_0(83251);
			this.buttonItem_67.SubItemTriangleColor = Color.Empty;
			this.buttonItem_67.Text = Class521.smethod_0(83296);
			this.buttonItem_68.Name = Class521.smethod_0(83313);
			this.buttonItem_68.SubItemTriangleColor = Color.Empty;
			this.buttonItem_68.Text = Class521.smethod_0(83358);
			this.buttonItem_69.Name = Class521.smethod_0(83375);
			this.buttonItem_69.SubItemTriangleColor = Color.Empty;
			this.buttonItem_69.Text = Class521.smethod_0(83420);
			this.buttonItem_70.Name = Class521.smethod_0(83437);
			this.buttonItem_70.SubItemTriangleColor = Color.Empty;
			this.buttonItem_70.Text = Class521.smethod_0(83482);
			this.buttonItem_71.Name = Class521.smethod_0(83499);
			this.buttonItem_71.SubItemTriangleColor = Color.Empty;
			this.buttonItem_71.Text = Class521.smethod_0(71633);
			this.buttonItem_47.ImageFixedSize = new Size(16, 16);
			this.buttonItem_47.Name = Class521.smethod_0(83528);
			this.buttonItem_47.SubItemTriangleColor = Color.Empty;
			this.buttonItem_47.Text = Class521.smethod_0(48462);
			this.buttonItem_55.Name = Class521.smethod_0(83557);
			this.buttonItem_55.SubItemTriangleColor = Color.Empty;
			this.buttonItem_55.Text = Class521.smethod_0(47752);
			this.buttonItem_46.BeginGroup = true;
			this.buttonItem_46.Image = Class375.processes;
			this.buttonItem_46.ImageFixedSize = new Size(16, 16);
			this.buttonItem_46.Name = Class521.smethod_0(83586);
			this.buttonItem_46.SubItemTriangleColor = Color.Empty;
			this.buttonItem_46.Text = Class521.smethod_0(11158);
			this.buttonItem_46.Click += this.buttonItem_46_Click;
			this.buttonItem_50.Image = Class375.database_down;
			this.buttonItem_50.ImageFixedSize = new Size(16, 16);
			this.buttonItem_50.Name = Class521.smethod_0(83615);
			this.buttonItem_50.SubItemTriangleColor = Color.Empty;
			this.buttonItem_50.Text = Class521.smethod_0(22293);
			this.buttonItem_53.Image = Class375.shortcuts24x24;
			this.buttonItem_53.ImageFixedSize = new Size(16, 16);
			this.buttonItem_53.Name = Class521.smethod_0(83644);
			this.buttonItem_53.SubItemTriangleColor = Color.Empty;
			this.buttonItem_53.Text = Class521.smethod_0(83673);
			this.buttonItem_12.Image = Class375.application_split;
			this.buttonItem_12.ImageFixedSize = new Size(16, 16);
			this.buttonItem_12.Name = Class521.smethod_0(83690);
			this.buttonItem_12.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_13,
				this.buttonItem_14,
				this.buttonItem_15,
				this.buttonItem_16,
				this.buttonItem_18,
				this.buttonItem_17
			});
			this.buttonItem_12.SubItemTriangleColor = Color.Empty;
			this.buttonItem_12.Text = Class521.smethod_0(8651);
			this.buttonItem_13.Name = Class521.smethod_0(83719);
			this.buttonItem_13.SubItemTriangleColor = Color.Empty;
			this.buttonItem_13.Text = Class521.smethod_0(83760);
			this.buttonItem_14.BeginGroup = true;
			this.buttonItem_14.Image = Class375.application_split;
			this.buttonItem_14.ImageFixedSize = new Size(16, 16);
			this.buttonItem_14.Name = Class521.smethod_0(83777);
			this.buttonItem_14.SubItemTriangleColor = Color.Empty;
			this.buttonItem_14.Text = Class521.smethod_0(83818);
			this.buttonItem_15.Image = Class375.floppy_disc;
			this.buttonItem_15.ImageFixedSize = new Size(16, 16);
			this.buttonItem_15.Name = Class521.smethod_0(83839);
			this.buttonItem_15.SubItemTriangleColor = Color.Empty;
			this.buttonItem_15.Text = Class521.smethod_0(27304);
			this.buttonItem_16.Name = Class521.smethod_0(83880);
			this.buttonItem_16.SubItemTriangleColor = Color.Empty;
			this.buttonItem_16.Text = Class521.smethod_0(83921);
			this.buttonItem_18.Name = Class521.smethod_0(83942);
			this.buttonItem_18.SubItemTriangleColor = Color.Empty;
			this.buttonItem_18.Text = Class521.smethod_0(83983);
			this.buttonItem_17.Name = Class521.smethod_0(84000);
			this.buttonItem_17.SubItemTriangleColor = Color.Empty;
			this.buttonItem_17.Text = Class521.smethod_0(84041);
			this.buttonItem_19.ImageFixedSize = new Size(16, 16);
			this.buttonItem_19.Name = Class521.smethod_0(84058);
			this.buttonItem_19.SubItems.AddRange(new BaseItem[]
			{
				this.buttonItem_20,
				this.buttonItem_39,
				this.buttonItem_21
			});
			this.buttonItem_19.SubItemTriangleColor = Color.Empty;
			this.buttonItem_19.Text = Class521.smethod_0(72044);
			this.buttonItem_20.Name = Class521.smethod_0(84087);
			this.buttonItem_20.SubItemTriangleColor = Color.Empty;
			this.buttonItem_20.Text = Class521.smethod_0(39117);
			this.buttonItem_39.Name = Class521.smethod_0(84128);
			this.buttonItem_39.SubItemTriangleColor = Color.Empty;
			this.buttonItem_39.Text = Class521.smethod_0(39134);
			this.buttonItem_39.Click += this.buttonItem_39_Click;
			this.buttonItem_21.Name = Class521.smethod_0(84169);
			this.buttonItem_21.SubItemTriangleColor = Color.Empty;
			this.buttonItem_21.Text = Class521.smethod_0(39151);
			this.buttonItem_42.BeginGroup = true;
			this.buttonItem_42.Image = Class375.quesmark;
			this.buttonItem_42.ImageFixedSize = new Size(16, 16);
			this.buttonItem_42.Name = Class521.smethod_0(84210);
			this.buttonItem_42.SubItemTriangleColor = Color.Empty;
			this.buttonItem_42.Text = Class521.smethod_0(84227);
			this.buttonItem_42.Click += this.buttonItem_42_Click;
			this.buttonItem_43.Name = Class521.smethod_0(84244);
			this.buttonItem_43.SubItemTriangleColor = Color.Empty;
			this.buttonItem_43.Text = Class521.smethod_0(84269);
			this.buttonItem_43.Click += this.buttonItem_43_Click;
			this.buttonItem_44.Name = Class521.smethod_0(84286);
			this.buttonItem_44.SubItemTriangleColor = Color.Empty;
			this.buttonItem_44.Text = Class521.smethod_0(84307);
			this.buttonItem_44.Click += this.buttonItem_44_Click;
			this.buttonItem_45.Name = Class521.smethod_0(84324);
			this.buttonItem_45.SubItemTriangleColor = Color.Empty;
			this.buttonItem_45.Text = Class521.smethod_0(84345);
			this.buttonItem_45.Click += this.buttonItem_45_Click;
			this.buttonItem_51.Name = Class521.smethod_0(84362);
			this.buttonItem_51.SubItemTriangleColor = Color.Empty;
			this.buttonItem_51.Text = Class521.smethod_0(84399);
			this.buttonItem_52.Name = Class521.smethod_0(84416);
			this.buttonItem_52.SubItemTriangleColor = Color.Empty;
			this.buttonItem_52.Text = Class521.smethod_0(84453);
			this.buttonItem_40.Name = Class521.smethod_0(84470);
			this.buttonItem_40.SubItemTriangleColor = Color.Empty;
			this.buttonItem_40.Text = Class521.smethod_0(84503);
			this.buttonItem_40.Click += this.buttonItem_40_Click;
			this.bar_4.AccessibleDescription = Class521.smethod_0(84516);
			this.bar_4.AccessibleName = Class521.smethod_0(80771);
			this.bar_4.AccessibleRole = AccessibleRole.ToolBar;
			this.bar_4.AntiAlias = true;
			this.bar_4.BackColor = SystemColors.Control;
			this.bar_4.CanDockBottom = false;
			this.bar_4.CanDockTab = false;
			this.bar_4.CanHide = true;
			this.bar_4.CanReorderTabs = false;
			this.bar_4.DockOffset = 792;
			this.bar_4.DockSide = eDockSide.Top;
			this.bar_4.Font = new Font(Class521.smethod_0(6998), 9f);
			this.bar_4.GrabHandleStyle = eGrabHandleStyle.None;
			this.bar_4.Items.AddRange(new BaseItem[]
			{
				this.buttonItem_22,
				this.buttonItem_56,
				this.buttonItem_23,
				this.buttonItem_24,
				this.buttonItem_25,
				this.buttonItem_26,
				this.buttonItem_27,
				this.buttonItem_28,
				this.buttonItem_29,
				this.buttonItem_30,
				this.buttonItem_49,
				this.buttonItem_31,
				this.buttonItem_32,
				this.buttonItem_33
			});
			this.bar_4.Location = new Point(947, 0);
			this.bar_4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.bar_4.Name = Class521.smethod_0(84561);
			this.bar_4.Size = new Size(327, 31);
			this.bar_4.Style = eDotNetBarStyle.Metro;
			this.bar_4.TabIndex = 11;
			this.bar_4.TabStop = false;
			this.bar_4.Text = Class521.smethod_0(84586);
			this.buttonItem_56.AutoExpandOnClick = true;
			this.buttonItem_56.Name = Class521.smethod_0(84599);
			this.buttonItem_56.SubItems.AddRange(new BaseItem[]
			{
				this.itemContainer_0,
				this.itemContainer_1,
				this.itemContainer_2
			});
			this.buttonItem_56.SubItemTriangleColor = Color.Empty;
			this.buttonItem_56.Text = Class521.smethod_0(84628);
			this.itemContainer_0.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemContainer_0.Name = Class521.smethod_0(84633);
			this.itemContainer_0.SubItems.AddRange(new BaseItem[]
			{
				this.textBoxItem_0,
				this.buttonItem_57
			});
			this.itemContainer_0.TitleStyle.CornerType = eCornerType.Square;
			this.textBoxItem_0.AlwaysShowCaption = true;
			this.textBoxItem_0.Caption = Class521.smethod_0(17790);
			this.textBoxItem_0.Name = Class521.smethod_0(84662);
			this.textBoxItem_0.Text = Class521.smethod_0(84687);
			this.textBoxItem_0.TextBoxWidth = 18;
			this.textBoxItem_0.WatermarkColor = SystemColors.GrayText;
			this.textBoxItem_0.HideBorder = true;
			this.buttonItem_57.Image = Class375.flash_16x16;
			this.buttonItem_57.Name = Class521.smethod_0(84692);
			this.buttonItem_57.SubItemTriangleColor = Color.Empty;
			this.buttonItem_57.Text = Class521.smethod_0(26062);
			this.itemContainer_1.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemContainer_1.Name = Class521.smethod_0(84725);
			this.itemContainer_1.SubItems.AddRange(new BaseItem[]
			{
				this.textBoxItem_1,
				this.buttonItem_58
			});
			this.itemContainer_1.TitleStyle.CornerType = eCornerType.Square;
			this.textBoxItem_1.AlwaysShowCaption = true;
			this.textBoxItem_1.Caption = Class521.smethod_0(84754);
			this.textBoxItem_1.Name = Class521.smethod_0(84763);
			this.textBoxItem_1.Text = Class521.smethod_0(84687);
			this.textBoxItem_1.TextBoxWidth = 18;
			this.textBoxItem_1.WatermarkColor = SystemColors.GrayText;
			this.textBoxItem_1.HideBorder = true;
			this.buttonItem_58.Image = Class375.flash_16x16;
			this.buttonItem_58.Name = Class521.smethod_0(84788);
			this.buttonItem_58.SubItemTriangleColor = Color.Empty;
			this.buttonItem_58.Text = Class521.smethod_0(26062);
			this.itemContainer_2.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemContainer_2.Name = Class521.smethod_0(84821);
			this.itemContainer_2.SubItems.AddRange(new BaseItem[]
			{
				this.textBoxItem_2,
				this.buttonItem_59
			});
			this.itemContainer_2.TitleStyle.CornerType = eCornerType.Square;
			this.textBoxItem_2.AlwaysShowCaption = true;
			this.textBoxItem_2.Caption = Class521.smethod_0(84850);
			this.textBoxItem_2.Name = Class521.smethod_0(84859);
			this.textBoxItem_2.Text = Class521.smethod_0(84687);
			this.textBoxItem_2.TextBoxWidth = 18;
			this.textBoxItem_2.WatermarkColor = SystemColors.GrayText;
			this.textBoxItem_2.HideBorder = true;
			this.buttonItem_59.Image = Class375.flash_16x16;
			this.buttonItem_59.Name = Class521.smethod_0(84884);
			this.buttonItem_59.SubItemTriangleColor = Color.Empty;
			this.buttonItem_59.Text = Class521.smethod_0(26062);
			this.buttonItem_22.Name = Class521.smethod_0(84917);
			this.buttonItem_22.SubItemTriangleColor = Color.Empty;
			this.buttonItem_22.Text = Class521.smethod_0(1993);
			this.buttonItem_23.Name = Class521.smethod_0(84942);
			this.buttonItem_23.SubItemTriangleColor = Color.Empty;
			this.buttonItem_23.Text = Class521.smethod_0(11989);
			this.buttonItem_23.Tooltip = Class521.smethod_0(16325);
			this.buttonItem_24.Name = Class521.smethod_0(84967);
			this.buttonItem_24.SubItemTriangleColor = Color.Empty;
			this.buttonItem_24.Text = Class521.smethod_0(12011);
			this.buttonItem_24.Tooltip = Class521.smethod_0(16338);
			this.buttonItem_25.Name = Class521.smethod_0(84992);
			this.buttonItem_25.SubItemTriangleColor = Color.Empty;
			this.buttonItem_25.Text = Class521.smethod_0(12038);
			this.buttonItem_25.Tooltip = Class521.smethod_0(16351);
			this.buttonItem_26.Name = Class521.smethod_0(85017);
			this.buttonItem_26.SubItemTriangleColor = Color.Empty;
			this.buttonItem_26.Text = Class521.smethod_0(12261);
			this.buttonItem_26.Tooltip = Class521.smethod_0(48169);
			this.buttonItem_27.Name = Class521.smethod_0(85042);
			this.buttonItem_27.SubItemTriangleColor = Color.Empty;
			this.buttonItem_27.Text = Class521.smethod_0(12065);
			this.buttonItem_27.Tooltip = Class521.smethod_0(16364);
			this.buttonItem_28.Name = Class521.smethod_0(85067);
			this.buttonItem_28.SubItemTriangleColor = Color.Empty;
			this.buttonItem_28.Text = Class521.smethod_0(12096);
			this.buttonItem_28.Tooltip = Class521.smethod_0(16377);
			this.buttonItem_29.Name = Class521.smethod_0(85092);
			this.buttonItem_29.SubItemTriangleColor = Color.Empty;
			this.buttonItem_29.Text = Class521.smethod_0(85117);
			this.buttonItem_29.Tooltip = Class521.smethod_0(37487);
			this.buttonItem_30.Name = Class521.smethod_0(85122);
			this.buttonItem_30.SubItemTriangleColor = Color.Empty;
			this.buttonItem_30.Text = Class521.smethod_0(12150);
			this.buttonItem_30.Tooltip = Class521.smethod_0(37500);
			this.buttonItem_49.Name = Class521.smethod_0(85147);
			this.buttonItem_49.SubItemTriangleColor = Color.Empty;
			this.buttonItem_49.Text = Class521.smethod_0(12177);
			this.buttonItem_49.Tooltip = Class521.smethod_0(48182);
			this.buttonItem_31.Name = Class521.smethod_0(85172);
			this.buttonItem_31.SubItemTriangleColor = Color.Empty;
			this.buttonItem_31.Text = Class521.smethod_0(21727);
			this.buttonItem_31.Tooltip = Class521.smethod_0(12209);
			this.buttonItem_32.Name = Class521.smethod_0(85197);
			this.buttonItem_32.SubItemTriangleColor = Color.Empty;
			this.buttonItem_32.Text = Class521.smethod_0(48195);
			this.buttonItem_32.Tooltip = Class521.smethod_0(12228);
			this.buttonItem_33.Name = Class521.smethod_0(85222);
			this.buttonItem_33.SubItemTriangleColor = Color.Empty;
			this.buttonItem_33.Text = Class521.smethod_0(48200);
			this.buttonItem_33.Tooltip = Class521.smethod_0(12247);
			this.dockSite_3.AccessibleRole = AccessibleRole.Window;
			this.dockSite_3.Dock = DockStyle.Top;
			this.dockSite_3.DocumentDockContainer = new DocumentDockContainer();
			this.dockSite_3.Location = new Point(0, 31);
			this.dockSite_3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_3.Name = Class521.smethod_0(85247);
			this.dockSite_3.Size = new Size(1274, 0);
			this.dockSite_3.TabIndex = 5;
			this.dockSite_3.TabStop = false;
			this.panel_0.Dock = DockStyle.Fill;
			this.panel_0.Location = new Point(0, 31);
			this.panel_0.Margin = new System.Windows.Forms.Padding(0);
			this.panel_0.Name = Class521.smethod_0(85264);
			this.panel_0.Size = new Size(1274, 394);
			this.panel_0.TabIndex = 11;
			this.toolTip_0.AutomaticDelay = 200;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.ClientSize = new Size(1274, 735);
			base.Controls.Add(this.panel_0);
			base.Controls.Add(this.dockSite_0);
			base.Controls.Add(this.dockSite_2);
			base.Controls.Add(this.dockSite_1);
			base.Controls.Add(this.dockSite_3);
			base.Controls.Add(this.dockSite_4);
			base.Controls.Add(this.dockSite_5);
			base.Controls.Add(this.dockSite_6);
			base.Controls.Add(this.dockSite_7);
			this.DoubleBuffered = true;
			base.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			base.Name = Class521.smethod_0(85281);
			this.dockSite_0.ResumeLayout(false);
			((ISupportInitialize)this.bar_0).EndInit();
			this.bar_0.ResumeLayout(false);
			this.dockSite_7.ResumeLayout(false);
			((ISupportInitialize)this.bar_5).EndInit();
			this.dockSite_6.ResumeLayout(false);
			((ISupportInitialize)this.bar_1).EndInit();
			this.bar_1.ResumeLayout(false);
			((ISupportInitialize)this.class304_0).EndInit();
			((ISupportInitialize)this.class304_1).EndInit();
			((ISupportInitialize)this.bar_2).EndInit();
			((ISupportInitialize)this.bar_6).EndInit();
			((ISupportInitialize)this.bar_3).EndInit();
			((ISupportInitialize)this.bar_4).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000DAB RID: 3499
		private List<StkSymbol> list_0;

		// Token: 0x04000DAC RID: 3500
		private MainForm.Struct6 struct6_0;

		// Token: 0x04000DAD RID: 3501
		private int? nullable_0;

		// Token: 0x04000DAE RID: 3502
		private bool bool_0;

		// Token: 0x04000DAF RID: 3503
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x04000DB0 RID: 3504
		private System.Windows.Forms.Timer timer_1 = new System.Windows.Forms.Timer();

		// Token: 0x04000DB1 RID: 3505
		private System.Timers.Timer timer_2;

		// Token: 0x04000DB2 RID: 3506
		private bool bool_1;

		// Token: 0x04000DB3 RID: 3507
		private int int_0;

		// Token: 0x04000DB4 RID: 3508
		private decimal decimal_0;

		// Token: 0x04000DB5 RID: 3509
		private bool bool_2;

		// Token: 0x04000DB6 RID: 3510
		private bool bool_3;

		// Token: 0x04000DB7 RID: 3511
		private bool bool_4;

		// Token: 0x04000DB8 RID: 3512
		private bool bool_5;

		// Token: 0x04000DB9 RID: 3513
		private string string_0;

		// Token: 0x04000DBA RID: 3514
		private Class43 class43_0;

		// Token: 0x04000DBB RID: 3515
		private Form18 form18_0;

		// Token: 0x04000DBC RID: 3516
		private Class310 class310_0;

		// Token: 0x04000DBD RID: 3517
		private const string string_1 = "正在提取数据...";

		// Token: 0x04000DBE RID: 3518
		private const string string_2 = "正在切换账户...";

		// Token: 0x04000DBF RID: 3519
		private const string string_3 = "交易练习者更新程序";

		// Token: 0x04000DC0 RID: 3520
		private const string string_4 = "TExUpd.exe";

		// Token: 0x04000DC1 RID: 3521
		private const string string_5 = " - 双盲测试模式";

		// Token: 0x04000DC2 RID: 3522
		private const int int_1 = 274;

		// Token: 0x04000DC3 RID: 3523
		private const int int_2 = 257;

		// Token: 0x04000DC4 RID: 3524
		private const int int_3 = 514;

		// Token: 0x04000DC5 RID: 3525
		private const int int_4 = 517;

		// Token: 0x04000DC6 RID: 3526
		private const int int_5 = 513;

		// Token: 0x04000DC7 RID: 3527
		private const int int_6 = 516;

		// Token: 0x04000DC8 RID: 3528
		private const int int_7 = 61536;

		// Token: 0x04000DC9 RID: 3529
		private const int int_8 = 61472;

		// Token: 0x04000DCA RID: 3530
		private const int int_9 = 61488;

		// Token: 0x04000DCB RID: 3531
		private const int int_10 = 61728;

		// Token: 0x04000DCC RID: 3532
		private bool bool_6;

		// Token: 0x04000DCD RID: 3533
		private bool bool_7;

		// Token: 0x04000DCE RID: 3534
		private IContainer icontainer_0;

		// Token: 0x04000DCF RID: 3535
		private DotNetBarManager dotNetBarManager_0;

		// Token: 0x04000DD0 RID: 3536
		private DockSite dockSite_0;

		// Token: 0x04000DD1 RID: 3537
		private DockSite dockSite_1;

		// Token: 0x04000DD2 RID: 3538
		private DockSite dockSite_2;

		// Token: 0x04000DD3 RID: 3539
		private DockSite dockSite_3;

		// Token: 0x04000DD4 RID: 3540
		private DockSite dockSite_4;

		// Token: 0x04000DD5 RID: 3541
		private DockSite dockSite_5;

		// Token: 0x04000DD6 RID: 3542
		private DockSite dockSite_6;

		// Token: 0x04000DD7 RID: 3543
		private DockSite dockSite_7;

		// Token: 0x04000DD8 RID: 3544
		private Bar bar_0;

		// Token: 0x04000DD9 RID: 3545
		private PanelDockContainer panelDockContainer_0;

		// Token: 0x04000DDA RID: 3546
		private DockContainerItem dockContainerItem_0;

		// Token: 0x04000DDB RID: 3547
		private Bar bar_1;

		// Token: 0x04000DDC RID: 3548
		private Class304 class304_0;

		// Token: 0x04000DDD RID: 3549
		private Class304 class304_1;

		// Token: 0x04000DDE RID: 3550
		private ButtonItem buttonItem_0;

		// Token: 0x04000DDF RID: 3551
		private ButtonItem buttonItem_1;

		// Token: 0x04000DE0 RID: 3552
		private ButtonItem buttonItem_2;

		// Token: 0x04000DE1 RID: 3553
		private ButtonItem buttonItem_3;

		// Token: 0x04000DE2 RID: 3554
		private LabelItem labelItem_0;

		// Token: 0x04000DE3 RID: 3555
		private ControlContainerItem controlContainerItem_0;

		// Token: 0x04000DE4 RID: 3556
		private LabelItem labelItem_1;

		// Token: 0x04000DE5 RID: 3557
		private ControlContainerItem controlContainerItem_1;

		// Token: 0x04000DE6 RID: 3558
		private Bar bar_2;

		// Token: 0x04000DE7 RID: 3559
		private ButtonItem buttonItem_4;

		// Token: 0x04000DE8 RID: 3560
		private LabelItem labelItem_2;

		// Token: 0x04000DE9 RID: 3561
		private SliderItem sliderItem_0;

		// Token: 0x04000DEA RID: 3562
		private Bar bar_3;

		// Token: 0x04000DEB RID: 3563
		private ButtonItem buttonItem_5;

		// Token: 0x04000DEC RID: 3564
		private ButtonItem buttonItem_6;

		// Token: 0x04000DED RID: 3565
		private ButtonItem buttonItem_7;

		// Token: 0x04000DEE RID: 3566
		private ButtonItem buttonItem_8;

		// Token: 0x04000DEF RID: 3567
		private ButtonItem buttonItem_9;

		// Token: 0x04000DF0 RID: 3568
		private ButtonItem buttonItem_10;

		// Token: 0x04000DF1 RID: 3569
		private ButtonItem buttonItem_11;

		// Token: 0x04000DF2 RID: 3570
		private ButtonItem buttonItem_12;

		// Token: 0x04000DF3 RID: 3571
		private ButtonItem buttonItem_13;

		// Token: 0x04000DF4 RID: 3572
		private ButtonItem buttonItem_14;

		// Token: 0x04000DF5 RID: 3573
		private ButtonItem buttonItem_15;

		// Token: 0x04000DF6 RID: 3574
		private ButtonItem buttonItem_16;

		// Token: 0x04000DF7 RID: 3575
		private ButtonItem buttonItem_17;

		// Token: 0x04000DF8 RID: 3576
		private ButtonItem buttonItem_18;

		// Token: 0x04000DF9 RID: 3577
		private ButtonItem buttonItem_19;

		// Token: 0x04000DFA RID: 3578
		private ButtonItem buttonItem_20;

		// Token: 0x04000DFB RID: 3579
		private ButtonItem buttonItem_21;

		// Token: 0x04000DFC RID: 3580
		private Bar bar_4;

		// Token: 0x04000DFD RID: 3581
		private ButtonItem buttonItem_22;

		// Token: 0x04000DFE RID: 3582
		private ButtonItem buttonItem_23;

		// Token: 0x04000DFF RID: 3583
		private ButtonItem buttonItem_24;

		// Token: 0x04000E00 RID: 3584
		private ButtonItem buttonItem_25;

		// Token: 0x04000E01 RID: 3585
		private ButtonItem buttonItem_26;

		// Token: 0x04000E02 RID: 3586
		private ButtonItem buttonItem_27;

		// Token: 0x04000E03 RID: 3587
		private ButtonItem buttonItem_28;

		// Token: 0x04000E04 RID: 3588
		private ButtonItem buttonItem_29;

		// Token: 0x04000E05 RID: 3589
		private ButtonItem buttonItem_30;

		// Token: 0x04000E06 RID: 3590
		private ButtonItem buttonItem_31;

		// Token: 0x04000E07 RID: 3591
		private ButtonItem buttonItem_32;

		// Token: 0x04000E08 RID: 3592
		private ButtonItem buttonItem_33;

		// Token: 0x04000E09 RID: 3593
		private Panel panel_0;

		// Token: 0x04000E0A RID: 3594
		private Bar bar_5;

		// Token: 0x04000E0B RID: 3595
		private ButtonItem buttonItem_34;

		// Token: 0x04000E0C RID: 3596
		private LabelItem labelItem_3;

		// Token: 0x04000E0D RID: 3597
		private LabelItem labelItem_4;

		// Token: 0x04000E0E RID: 3598
		private LabelItem labelItem_5;

		// Token: 0x04000E0F RID: 3599
		private LabelItem labelItem_6;

		// Token: 0x04000E10 RID: 3600
		private LabelItem labelItem_7;

		// Token: 0x04000E11 RID: 3601
		private LabelItem labelItem_8;

		// Token: 0x04000E12 RID: 3602
		private Bar bar_6;

		// Token: 0x04000E13 RID: 3603
		private ButtonItem buttonItem_35;

		// Token: 0x04000E14 RID: 3604
		private ButtonItem buttonItem_36;

		// Token: 0x04000E15 RID: 3605
		private System.Windows.Forms.ToolTip toolTip_0;

		// Token: 0x04000E16 RID: 3606
		private LabelItem labelItem_9;

		// Token: 0x04000E17 RID: 3607
		private ButtonItem buttonItem_37;

		// Token: 0x04000E18 RID: 3608
		private LabelItem labelItem_10;

		// Token: 0x04000E19 RID: 3609
		private ButtonItem buttonItem_38;

		// Token: 0x04000E1A RID: 3610
		private ButtonItem buttonItem_39;

		// Token: 0x04000E1B RID: 3611
		private ButtonItem buttonItem_40;

		// Token: 0x04000E1C RID: 3612
		private ButtonItem buttonItem_41;

		// Token: 0x04000E1D RID: 3613
		private ButtonItem buttonItem_42;

		// Token: 0x04000E1E RID: 3614
		private ButtonItem buttonItem_43;

		// Token: 0x04000E1F RID: 3615
		private ButtonItem buttonItem_44;

		// Token: 0x04000E20 RID: 3616
		private ButtonItem buttonItem_45;

		// Token: 0x04000E21 RID: 3617
		private ButtonItem buttonItem_46;

		// Token: 0x04000E22 RID: 3618
		private ButtonItem buttonItem_47;

		// Token: 0x04000E23 RID: 3619
		private LabelItem labelItem_11;

		// Token: 0x04000E24 RID: 3620
		private LabelItem labelItem_12;

		// Token: 0x04000E25 RID: 3621
		private ButtonItem buttonItem_48;

		// Token: 0x04000E26 RID: 3622
		private LabelItem labelItem_13;

		// Token: 0x04000E27 RID: 3623
		private ButtonItem buttonItem_49;

		// Token: 0x04000E28 RID: 3624
		private ButtonItem buttonItem_50;

		// Token: 0x04000E29 RID: 3625
		private ButtonItem buttonItem_51;

		// Token: 0x04000E2A RID: 3626
		private ButtonItem buttonItem_52;

		// Token: 0x04000E2B RID: 3627
		private ButtonItem buttonItem_53;

		// Token: 0x04000E2C RID: 3628
		private ButtonItem buttonItem_54;

		// Token: 0x04000E2D RID: 3629
		private ButtonItem buttonItem_55;

		// Token: 0x04000E2E RID: 3630
		private ButtonItem buttonItem_56;

		// Token: 0x04000E2F RID: 3631
		private ItemContainer itemContainer_0;

		// Token: 0x04000E30 RID: 3632
		private TextBoxItem textBoxItem_0;

		// Token: 0x04000E31 RID: 3633
		private ButtonItem buttonItem_57;

		// Token: 0x04000E32 RID: 3634
		private ItemContainer itemContainer_1;

		// Token: 0x04000E33 RID: 3635
		private TextBoxItem textBoxItem_1;

		// Token: 0x04000E34 RID: 3636
		private ButtonItem buttonItem_58;

		// Token: 0x04000E35 RID: 3637
		private ItemContainer itemContainer_2;

		// Token: 0x04000E36 RID: 3638
		private TextBoxItem textBoxItem_2;

		// Token: 0x04000E37 RID: 3639
		private ButtonItem buttonItem_59;

		// Token: 0x04000E38 RID: 3640
		private ButtonItem buttonItem_60;

		// Token: 0x04000E39 RID: 3641
		private ButtonItem buttonItem_61;

		// Token: 0x04000E3A RID: 3642
		private ButtonItem buttonItem_62;

		// Token: 0x04000E3B RID: 3643
		private ButtonItem buttonItem_63;

		// Token: 0x04000E3C RID: 3644
		private ButtonItem buttonItem_64;

		// Token: 0x04000E3D RID: 3645
		private ButtonItem buttonItem_65;

		// Token: 0x04000E3E RID: 3646
		private ButtonItem buttonItem_66;

		// Token: 0x04000E3F RID: 3647
		private ButtonItem buttonItem_67;

		// Token: 0x04000E40 RID: 3648
		private ButtonItem buttonItem_68;

		// Token: 0x04000E41 RID: 3649
		private ButtonItem buttonItem_69;

		// Token: 0x04000E42 RID: 3650
		private ButtonItem buttonItem_70;

		// Token: 0x04000E43 RID: 3651
		private ButtonItem buttonItem_71;

		// Token: 0x0200027F RID: 639
		private struct Struct6
		{
			// Token: 0x04000E44 RID: 3652
			public Point point_0;

			// Token: 0x04000E45 RID: 3653
			public Point point_1;

			// Token: 0x04000E46 RID: 3654
			public Point point_2;

			// Token: 0x04000E47 RID: 3655
			public Point point_3;
		}

		// Token: 0x02000280 RID: 640
		// (Invoke) Token: 0x06001CE6 RID: 7398
		private delegate bool Delegate31();

		// Token: 0x02000281 RID: 641
		// (Invoke) Token: 0x06001CEA RID: 7402
		public delegate void Delegate32(OrderType orderType);

		// Token: 0x02000282 RID: 642
		[CompilerGenerated]
		private sealed class Class331
		{
			// Token: 0x06001CEE RID: 7406 RVA: 0x000CDC48 File Offset: 0x000CBE48
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000E48 RID: 3656
			public string string_0;
		}

		// Token: 0x02000283 RID: 643
		[CompilerGenerated]
		private sealed class Class332
		{
			// Token: 0x06001CF0 RID: 7408 RVA: 0x0000C3D8 File Offset: 0x0000A5D8
			internal void method_0()
			{
				this.mainForm_0.labelItem_8.Text = this.string_0;
			}

			// Token: 0x04000E49 RID: 3657
			public MainForm mainForm_0;

			// Token: 0x04000E4A RID: 3658
			public string string_0;
		}

		// Token: 0x02000284 RID: 644
		[CompilerGenerated]
		private sealed class Class333
		{
			// Token: 0x06001CF2 RID: 7410 RVA: 0x000CDC98 File Offset: 0x000CBE98
			internal void method_0()
			{
				this.class332_0.mainForm_0.labelItem_8.Text = Class521.smethod_0(115877) + this.exception_0.InnerException.Message + Class521.smethod_0(115926);
			}

			// Token: 0x04000E4B RID: 3659
			public Exception exception_0;

			// Token: 0x04000E4C RID: 3660
			public MainForm.Class332 class332_0;
		}

		// Token: 0x02000286 RID: 646
		[CompilerGenerated]
		private sealed class Class334
		{
			// Token: 0x06001D0B RID: 7435 RVA: 0x000CDDB8 File Offset: 0x000CBFB8
			internal bool method_0(SymbDataSet symbDataSet_1)
			{
				return symbDataSet_1.SymblID != this.symbDataSet_0.SymblID;
			}

			// Token: 0x04000E63 RID: 3683
			public SymbDataSet symbDataSet_0;
		}

		// Token: 0x02000287 RID: 647
		[CompilerGenerated]
		private sealed class Class335
		{
			// Token: 0x06001D0D RID: 7437 RVA: 0x000CDDE0 File Offset: 0x000CBFE0
			internal bool method_0(ChtCtrl chtCtrl_0)
			{
				return chtCtrl_0.method_34(new PeriodType?(this.periodType_0), this.nullable_0);
			}

			// Token: 0x04000E64 RID: 3684
			public PeriodType periodType_0;

			// Token: 0x04000E65 RID: 3685
			public int? nullable_0;
		}

		// Token: 0x02000288 RID: 648
		[CompilerGenerated]
		private sealed class Class336
		{
			// Token: 0x06001D0F RID: 7439 RVA: 0x000CDE08 File Offset: 0x000CC008
			internal bool method_0(HisDataPeriodSet hisDataPeriodSet_0)
			{
				return hisDataPeriodSet_0.method_45(this.chtCtrl_0.HisDataPeriodSet);
			}

			// Token: 0x04000E66 RID: 3686
			public ChtCtrl chtCtrl_0;
		}

		// Token: 0x02000289 RID: 649
		[CompilerGenerated]
		private sealed class Class337
		{
			// Token: 0x06001D11 RID: 7441 RVA: 0x000CDE2C File Offset: 0x000CC02C
			internal bool method_0(ExchgHouse exchgHouse_1)
			{
				return exchgHouse_1.ID == this.int_0;
			}

			// Token: 0x06001D12 RID: 7442 RVA: 0x000CDE4C File Offset: 0x000CC04C
			internal bool method_1(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x04000E67 RID: 3687
			public int int_0;

			// Token: 0x04000E68 RID: 3688
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x0200028A RID: 650
		[CompilerGenerated]
		private sealed class Class338
		{
			// Token: 0x06001D14 RID: 7444 RVA: 0x000CDE70 File Offset: 0x000CC070
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.MstSymbol.CNName == this.string_0;
			}

			// Token: 0x04000E69 RID: 3689
			public string string_0;
		}
	}
}
