﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns1;
using ns18;
using ns26;
using TEx;
using TEx.Util;

namespace ns21
{
	// Token: 0x02000228 RID: 552
	internal sealed class Class302
	{
		// Token: 0x170003B9 RID: 953
		// (get) Token: 0x060016EA RID: 5866 RVA: 0x0009FF1C File Offset: 0x0009E11C
		// (set) Token: 0x060016EB RID: 5867 RVA: 0x00009660 File Offset: 0x00007860
		public string CurrGrpName { get; set; }

		// Token: 0x170003BA RID: 954
		// (get) Token: 0x060016EC RID: 5868 RVA: 0x0009FF34 File Offset: 0x0009E134
		// (set) Token: 0x060016ED RID: 5869 RVA: 0x0000966B File Offset: 0x0000786B
		public List<Class303> CondGroups { get; set; }

		// Token: 0x170003BB RID: 955
		// (get) Token: 0x060016EE RID: 5870 RVA: 0x0009FF4C File Offset: 0x0009E14C
		// (set) Token: 0x060016EF RID: 5871 RVA: 0x00009676 File Offset: 0x00007876
		public List<FilterCond> LastConds { get; set; }

		// Token: 0x170003BC RID: 956
		// (get) Token: 0x060016F0 RID: 5872 RVA: 0x0009FF64 File Offset: 0x0009E164
		// (set) Token: 0x060016F1 RID: 5873 RVA: 0x00009681 File Offset: 0x00007881
		public string LastRsltCsv { get; set; }

		// Token: 0x060016F2 RID: 5874 RVA: 0x0009FF7C File Offset: 0x0009E17C
		public List<FilterCond> method_0()
		{
			List<FilterCond> result = null;
			if (this.CondGroups != null && !string.IsNullOrEmpty(this.CurrGrpName))
			{
				Class303 @class = this.CondGroups.SingleOrDefault(new Func<Class303, bool>(this.method_2));
				if (@class != null)
				{
					result = @class.FilterConds;
				}
			}
			return result;
		}

		// Token: 0x060016F3 RID: 5875 RVA: 0x0009FFC8 File Offset: 0x0009E1C8
		public bool method_1()
		{
			bool result = true;
			string filePath = Class302.smethod_0();
			try
			{
				string content = JsonConvert.SerializeObject(this, Formatting.Indented);
				Utility.SaveFile(filePath, content, null);
			}
			catch (Exception exception_)
			{
				result = false;
				Class184.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x060016F4 RID: 5876 RVA: 0x000A0010 File Offset: 0x0009E210
		public static string smethod_0()
		{
			return Base.UI.smethod_44(Class521.smethod_0(9393));
		}

		// Token: 0x060016F5 RID: 5877 RVA: 0x000A0030 File Offset: 0x0009E230
		public static Class302 smethod_1()
		{
			Class302 result = null;
			string text = Class302.smethod_0();
			if (new FileInfo(text).Exists)
			{
				try
				{
					result = JsonConvert.DeserializeObject<Class302>(File.ReadAllText(text));
				}
				catch (Exception exception_)
				{
					Class184.smethod_0(exception_);
				}
			}
			return result;
		}

		// Token: 0x060016F7 RID: 5879 RVA: 0x000A007C File Offset: 0x0009E27C
		[CompilerGenerated]
		private bool method_2(Class303 class303_0)
		{
			return class303_0.Name.Equals(this.CurrGrpName, StringComparison.InvariantCultureIgnoreCase);
		}

		// Token: 0x04000BB1 RID: 2993
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000BB2 RID: 2994
		[CompilerGenerated]
		private List<Class303> list_0;

		// Token: 0x04000BB3 RID: 2995
		[CompilerGenerated]
		private List<FilterCond> list_1;

		// Token: 0x04000BB4 RID: 2996
		[CompilerGenerated]
		private string string_1;
	}
}
