﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns11;
using ns18;
using ns23;
using ns7;
using TEx.Inds;
using TEx.SIndicator;

namespace ns16
{
	// Token: 0x02000324 RID: 804
	internal sealed class Class440
	{
		// Token: 0x170005DE RID: 1502
		// (get) Token: 0x0600223B RID: 8763 RVA: 0x000F2890 File Offset: 0x000F0A90
		// (set) Token: 0x0600223C RID: 8764 RVA: 0x0000DA36 File Offset: 0x0000BC36
		public ParserEnvironment Envi { get; private set; }

		// Token: 0x170005DF RID: 1503
		// (get) Token: 0x0600223D RID: 8765 RVA: 0x000F28A8 File Offset: 0x000F0AA8
		// (set) Token: 0x0600223E RID: 8766 RVA: 0x0000DA41 File Offset: 0x0000BC41
		public string WrongMsg { get; private set; }

		// Token: 0x0600223F RID: 8767 RVA: 0x0000DA4C File Offset: 0x0000BC4C
		public Class440(ParserEnvironment parserEnvironment_1)
		{
			this.Envi = parserEnvironment_1;
		}

		// Token: 0x06002240 RID: 8768 RVA: 0x000F28C0 File Offset: 0x000F0AC0
		private Class443 method_0(int int_0, int int_1, string string_1)
		{
			return new Class443(int_0, int_1, string_1);
		}

		// Token: 0x06002241 RID: 8769 RVA: 0x000F28DC File Offset: 0x000F0ADC
		private static string smethod_0(ref int int_0, ref int int_1, string string_1)
		{
			string text = Class521.smethod_0(1449);
			bool flag = false;
			int i = int_0;
			while (i < string_1.Count<char>())
			{
				if (char.IsDigit(string_1[i]))
				{
					text += string_1[i].ToString();
					int_1++;
					if (i == string_1.Count<char>() - 1)
					{
						int_0 = i;
						break;
					}
				}
				else
				{
					if (string_1[i] != '.')
					{
						int_0 = i - 1;
						int_1--;
						break;
					}
					if (flag)
					{
						throw new Exception(string.Format(Class521.smethod_0(102686), int_0, int_1, Class521.smethod_0(1733)));
					}
					flag = true;
					text += string_1[i].ToString();
					int_1++;
				}
				i++;
				continue;
				IL_C1:
				return text;
			}
			goto IL_C1;
		}

		// Token: 0x06002242 RID: 8770 RVA: 0x000F29B4 File Offset: 0x000F0BB4
		private static string smethod_1(ref int int_0, ref int int_1, string string_1)
		{
			string text = Class521.smethod_0(1449);
			if (int_0 >= string_1.Length - 1)
			{
				throw new Exception(Class521.smethod_0(102727));
			}
			for (int i = int_0 + 1; i < string_1.Count<char>(); i++)
			{
				if (string_1[i] == '\'')
				{
					int_0 = i;
					int_1++;
					return text;
				}
				text += string_1[i].ToString();
				int_1++;
			}
			throw new Exception(Class521.smethod_0(102727));
		}

		// Token: 0x06002243 RID: 8771 RVA: 0x000F2A44 File Offset: 0x000F0C44
		private static string smethod_2(ref int int_0, ref int int_1, string string_1, Class440.Delegate36 delegate36_0)
		{
			string text = Class521.smethod_0(1449);
			for (int i = int_0; i < string_1.Count<char>(); i++)
			{
				if (!delegate36_0(string_1[i]))
				{
					int_0 = i - 1;
					int_1--;
					return text;
				}
				text += string_1[i].ToString();
				int_1++;
			}
			int_0 = string_1.Count<char>() - 1;
			return text;
		}

		// Token: 0x06002244 RID: 8772 RVA: 0x000F2AB8 File Offset: 0x000F0CB8
		public Tokenes method_1(string string_1)
		{
			string text = string_1.ToUpper();
			List<HToken> list = new List<HToken>();
			int num = 1;
			int num2 = 0;
			for (int i = 0; i < text.Count<char>(); i++)
			{
				num2++;
				char c = text[i];
				if (c != ' ' && c != '\t')
				{
					if (c == '/' && Class440.smethod_3(i, '/', text))
					{
						for (int j = i + 2; j < text.Count<char>(); j++)
						{
							char c2 = text[j];
							if (c2 == '\r')
							{
								if (Class440.smethod_3(j, '\n', text))
								{
									j++;
								}
								num++;
								num2 = 0;
								i = j;
								break;
							}
							if (c2 == '\n')
							{
								num++;
								num2 = 0;
								i = j;
								break;
							}
							if (j == text.Count<char>() - 1)
							{
								return new Tokenes(list);
							}
						}
					}
					else if (c == '\r')
					{
						if (Class440.smethod_3(i, '\n', text))
						{
							i++;
						}
						num++;
						num2 = 0;
					}
					else if (c == '\n')
					{
						num++;
						num2 = 0;
					}
					else if (char.IsLetter(c))
					{
						Class440.Class441 @class = new Class440.Class441();
						@class.string_0 = Class521.smethod_0(1449);
						Enum26 enum26_ = Enum26.const_4;
						int col = num2;
						@class.string_0 += Class440.smethod_2(ref i, ref num2, text, new Class440.Delegate36(this.method_2));
						if (this.Envi.Functions.Any(new Func<MethodInfo, bool>(@class.method_0)))
						{
							enum26_ = Enum26.const_1;
						}
						else if (@class.string_0 == Class521.smethod_0(102768))
						{
							enum26_ = Enum26.const_17;
						}
						else if (@class.string_0 == Class521.smethod_0(102773))
						{
							enum26_ = Enum26.const_18;
						}
						else if (this.Envi.UserDefineParams.Any(new Func<UserDefineParam, bool>(@class.method_1)))
						{
							enum26_ = Enum26.const_2;
						}
						else if (this.Envi.Properties.Any(new Func<PropertyInfo, bool>(@class.method_2)))
						{
							enum26_ = Enum26.const_0;
						}
						else if (ParserEnvironment.smethod_4(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_34;
						}
						else if (ParserEnvironment.smethod_8(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_35;
						}
						else if (ParserEnvironment.smethod_9(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_36;
						}
						else if (ParserEnvironment.smethod_10(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_37;
						}
						HToken item = new HToken(col, num, new Class442(enum26_, @class.string_0));
						list.Add(item);
					}
					else if (char.IsDigit(c))
					{
						string text2 = Class521.smethod_0(1449);
						int col2 = num2;
						text2 += Class440.smethod_0(ref i, ref num2, text);
						HToken item2 = new HToken(col2, num, new Class442(Enum26.const_5, text2));
						list.Add(item2);
					}
					else
					{
						HToken item3;
						if (c == '+')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_6, Class521.smethod_0(49784)));
						}
						else if (c == '\'')
						{
							string text3 = Class440.smethod_1(ref i, ref num2, text);
							if (!(text3 != Class521.smethod_0(1449)) || i >= text.Count<char>() - 1)
							{
								throw new Exception(new HToken(num2, num, new Class442(Enum26.const_40, text3)).method_0(Class521.smethod_0(102778)));
							}
							item3 = new HToken(num2, num, new Class442(Enum26.const_38, text3));
						}
						else if (c == '-')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_7, Class521.smethod_0(3210)));
						}
						else if (c == '*')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_8, Class521.smethod_0(102823)));
						}
						else if (c == '/')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_9, Class521.smethod_0(24570)));
						}
						else if (c == ':')
						{
							if (Class440.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_11, Class521.smethod_0(99492)));
							}
							else
							{
								item3 = new HToken(num2, num, new Class442(Enum26.const_10, Class521.smethod_0(50733)));
							}
						}
						else if (c == '>')
						{
							if (Class440.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_14, Class521.smethod_0(24882)));
							}
							else
							{
								item3 = new HToken(num2, num, new Class442(Enum26.const_12, Class521.smethod_0(24877)));
							}
						}
						else if (c == '&')
						{
							if (Class440.smethod_3(i, '&', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_17, Class521.smethod_0(102828)));
							}
							else
							{
								item3 = new HToken(num2, num, new Class442(Enum26.const_17, Class521.smethod_0(102833)));
							}
						}
						else if (c == '|')
						{
							if (Class440.smethod_3(i, '|', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_18, Class521.smethod_0(102838)));
							}
							else
							{
								item3 = new HToken(num2, num, new Class442(Enum26.const_18, Class521.smethod_0(102843)));
							}
						}
						else if (c == '<')
						{
							if (Class440.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_16, Class521.smethod_0(24892)));
							}
							else if (Class440.smethod_3(i, '>', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class442(Enum26.const_13, Class521.smethod_0(102848)));
							}
							else
							{
								item3 = new HToken(num2, num, new Class442(Enum26.const_15, Class521.smethod_0(24887)));
							}
						}
						else if (c == '!')
						{
							if (!Class440.smethod_3(i, '=', text))
							{
								throw new Exception(new HToken(num2, num, new Class442(Enum26.const_40, c.ToString())).method_0(Class521.smethod_0(102858)));
							}
							i++;
							item3 = new HToken(num2, num, new Class442(Enum26.const_13, Class521.smethod_0(102853)));
						}
						else if (c == '=')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_20, Class521.smethod_0(59685)));
						}
						else if (c == ',')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_21, Class521.smethod_0(4736)));
						}
						else if (c == '.')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_19, Class521.smethod_0(1733)));
						}
						else if (c == ';')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_22, Class521.smethod_0(51510)));
						}
						else if (c == '(')
						{
							item3 = new HToken(num2, num, new Class442(Enum26.const_23, Class521.smethod_0(24872)));
						}
						else
						{
							if (c != ')')
							{
								throw new Exception(new HToken(num2, num, new Class442(Enum26.const_40, c.ToString())).method_0(Class521.smethod_0(102911)));
							}
							item3 = new HToken(num2, num, new Class442(Enum26.const_24, Class521.smethod_0(5046)));
						}
						list.Add(item3);
					}
				}
			}
			if (list.Count > 0 && list.Last<HToken>().Symbol.HSymbolType != Enum26.const_22)
			{
				HToken htoken = list.Last<HToken>();
				HToken item4 = new HToken(htoken.Col + 1, htoken.Line, new Class442(Enum26.const_22, Class521.smethod_0(51510)));
				list.Add(item4);
			}
			return new Tokenes(list);
		}

		// Token: 0x06002245 RID: 8773 RVA: 0x000F3264 File Offset: 0x000F1464
		private bool method_2(char char_0)
		{
			if (!char.IsLetterOrDigit(char_0))
			{
				if (char_0 != '_')
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06002246 RID: 8774 RVA: 0x000F328C File Offset: 0x000F148C
		private static bool smethod_3(int int_0, char char_0, string string_1)
		{
			bool result;
			if (int_0 + 1 < string_1.Count<char>() && string_1[int_0 + 1] == char_0)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0400108A RID: 4234
		private List<HToken> list_0 = new List<HToken>();

		// Token: 0x0400108B RID: 4235
		[CompilerGenerated]
		private ParserEnvironment parserEnvironment_0;

		// Token: 0x0400108C RID: 4236
		[CompilerGenerated]
		private string string_0;

		// Token: 0x02000325 RID: 805
		// (Invoke) Token: 0x06002248 RID: 8776
		private delegate bool Delegate36(char achar);

		// Token: 0x02000326 RID: 806
		[CompilerGenerated]
		private sealed class Class441
		{
			// Token: 0x0600224C RID: 8780 RVA: 0x000F32BC File Offset: 0x000F14BC
			internal bool method_0(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.string_0;
			}

			// Token: 0x0600224D RID: 8781 RVA: 0x000F32E0 File Offset: 0x000F14E0
			internal bool method_1(UserDefineParam userDefineParam_0)
			{
				return userDefineParam_0.Name == this.string_0;
			}

			// Token: 0x0600224E RID: 8782 RVA: 0x000F32BC File Offset: 0x000F14BC
			internal bool method_2(PropertyInfo propertyInfo_0)
			{
				return propertyInfo_0.Name == this.string_0;
			}

			// Token: 0x0400108D RID: 4237
			public string string_0;
		}
	}
}
